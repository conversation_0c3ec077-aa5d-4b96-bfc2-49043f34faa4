/**
 * Or32462:有機体:印刷設定モーダル（画面/特殊コンポーネント）
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR>
 */
export interface Or32462StateType {
  /**
   * 開閉フラグ
   */
  isOpen?: boolean
  /**
   * パラメータ
   */
  param: Or32462Param
}

/**
 * パラメータ構造
 */
export interface Or32462Param {
  /**
   * セクション名
   */
  sectionName: string
  /**
   * 職員ID
   */
  shokuinId: string
  /**
   * 施設ID
   */
  shisetuId: string
  /**
   * 事業所ID
   */
  svJigyoId: string
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 担当者ID
   */
  tantoId: string
  /**
   * 基準日
   */
  kijunbi: string
  /**
   * 履歴ID
   */
  historyId: string
  /**
   * フォーカス設定用イニシャル
   */
  focusSettingInitial: string[]
  /**
   * 初期選択状態の担当者カウンタ値
   */
  selectedUserCounter: string,
}

/**
 * メッセージのボタンの値の型
 */
export type Or32462MsgBtnType = 'yes' | 'no' | 'cancel'
