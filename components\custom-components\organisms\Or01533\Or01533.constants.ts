import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * Or10826:有機体:（日課表パターン）ダイアログ
 * GUI00990_日課表パターン（設定）
 *
 * @description
 * 静的データ
 *
 * <AUTHOR>
 */
export namespace Or01533Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or01533', seq)
  /**
   * 操作区分0:初期化
   */
  export const OPERA_FLG_0 = '0'
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false
    /**
     * CANCEL
     */
    export const DIALOG_RESULT_CANCEL = 'cancel'
    /**
     *YES
     */
    export const DIALOG_RESULT_YES = 'yes'
    /**
     * NO
     */
    export const DIALOG_RESULT_NO = 'no'
  }

  /**
   * 区分1:日常
   */
  export const DATA_KBN_DAILY_LIFE = '1'
  /**
   * 区分2:処遇
   */
  export const DATA_KBN_TREATMENT_RELATED_TO_SELF_RELIANCE_SUPPORT = '2'
  /**
   * 区分3:介護
   */
  export const DATA_KBN_NURSING_PREVENTIVE_CARE_SERVICES = '3'

  /**
   * 区分1:日常
   */
  export const DATA_KBN_NUMBER_DAILY_LIFE = 1
  /**
   * 区分2:処遇
   */
  export const DATA_KBN_NUMBER_TREATMENT_RELATED_TO_SELF_RELIANCE_SUPPORT = 2
  /**
   * 区分3:介護
   */
  export const DATA_KBN_NUMBER_NURSING_PREVENTIVE_CARE_SERVICES = 3
  /**
   * 文字サイズ
   */
  export const FONT_SIZE = '9'

  /**
   * 文字位置
   */
  export const ALIGNMENT = '0'

  /**
   * 文字色
   */
  export const FONT_COLOR = '#C0C0C0'

  /**
   * 背景カラー
   */
  export const BACK_COLOR = '#000000'

  /**
   * 処理区分 初期化
   */
  export const PROCESS_FLG_INIT = '0'

  /**
   * 処理区分 タイトルプルダウン
   */
  export const PROCESS_FLG_GROUP = '1'

  /**
   * 表の頭
   */
  export const HEADER_SLOTS = [
    { headerName: '日常生活活動等', index: 1, width: 130 },
    { headerName: '自立支援に関する処遇', index: 2, width: 150 },
    { headerName: '提供担当者(職種)', index: 3, width: 130 },
    { headerName: '介護(介護予防)サービス', index: 4, width: 130 },
    { headerName: '提供担当者(職種)_受託居宅サービス', index: 5, width: 130 },
  ]

  /**
   * 刻度 目盛
   */
  export const FIRST_SLOTS = [
    {
      columnName: '0:00',
      time: '00:00',
      borderBottom: '1px solid #ddd',
      index: 1,
      labelShowFlg: true,
    },
    {
      columnName: '0:15',
      time: '00:15',
      borderBottom: '1px solid #ddd',
      index: 2,
      labelShowFlg: true,
    },
    {
      columnName: '0:30',
      time: '00:30',
      borderBottom: '1px solid #ddd',
      index: 3,
      labelShowFlg: true,
    },
    {
      columnName: '0:45',
      time: '00:45',
      borderBottom: '1px solid #ddd',
      index: 4,
      labelShowFlg: true,
    },
    {
      columnName: '1:00',
      time: '01:00',
      borderBottom: '1px solid #ddd',
      index: 5,
      labelShowFlg: true,
    },
    {
      columnName: '1:15',
      time: '01:15',
      borderBottom: '1px solid #ddd',
      index: 6,
      labelShowFlg: true,
    },
    {
      columnName: '1:30',
      time: '01:30',
      borderBottom: '1px solid #ddd',
      index: 7,
      labelShowFlg: true,
    },
    {
      columnName: '1:45',
      time: '01:45',
      borderBottom: '1px solid #ddd',
      index: 8,
      labelShowFlg: true,
    },
    {
      columnName: '2:00',
      time: '02:00',
      borderBottom: '1px solid #ddd',
      index: 9,
      labelShowFlg: true,
    },
    {
      columnName: '2:15',
      time: '02:15',
      borderBottom: '1px solid #ddd',
      index: 10,
      labelShowFlg: true,
    },
    {
      columnName: '2:30',
      time: '02:30',
      borderBottom: '1px solid #ddd',
      index: 11,
      labelShowFlg: true,
    },
    {
      columnName: '2:45',
      time: '02:45',
      borderBottom: '1px solid #ddd',
      index: 12,
      labelShowFlg: true,
    },
    {
      columnName: '3:00',
      time: '03:00',
      borderBottom: '1px solid #ddd',
      index: 13,
      labelShowFlg: true,
    },
    {
      columnName: '3:15',
      time: '03:15',
      borderBottom: '1px solid #ddd',
      index: 14,
      labelShowFlg: true,
    },
    {
      columnName: '3:30',
      time: '03:30',
      borderBottom: '1px solid #ddd',
      index: 15,
      labelShowFlg: true,
    },
    {
      columnName: '3:45',
      time: '03:45',
      borderBottom: '1px solid #ddd',
      index: 16,
      labelShowFlg: true,
    },
    {
      columnName: '4:00',
      time: '04:00',
      borderBottom: '1px solid #ddd',
      index: 17,
      labelShowFlg: true,
    },
    {
      columnName: '4:15',
      time: '04:15',
      borderBottom: '1px solid #ddd',
      index: 18,
      labelShowFlg: true,
    },
    {
      columnName: '4:30',
      time: '04:30',
      borderBottom: '1px solid #ddd',
      index: 19,
      labelShowFlg: true,
    },
    {
      columnName: '4:45',
      time: '04:45',
      borderBottom: '1px solid #ddd',
      index: 20,
      labelShowFlg: true,
    },
    {
      columnName: '5:00',
      time: '05:00',
      borderBottom: '1px solid #ddd',
      index: 21,
      labelShowFlg: true,
    },
    {
      columnName: '5:15',
      time: '05:15',
      borderBottom: '1px solid #ddd',
      index: 22,
      labelShowFlg: true,
    },
    {
      columnName: '5:30',
      time: '05:30',
      borderBottom: '1px solid #ddd',
      index: 23,
      labelShowFlg: true,
    },
    {
      columnName: '5:45',
      time: '05:45',
      borderBottom: '1px solid #ddd',
      index: 24,
      labelShowFlg: true,
    },
    {
      columnName: '6:00',
      time: '06:00',
      borderBottom: '1px solid #ddd',
      index: 25,
      labelShowFlg: true,
    },
    {
      columnName: '6:15',
      time: '06:15',
      borderBottom: '1px solid #ddd',
      index: 26,
      labelShowFlg: true,
    },
    {
      columnName: '6:30',
      time: '06:30',
      borderBottom: '1px solid #ddd',
      index: 27,
      labelShowFlg: true,
    },
    {
      columnName: '6:45',
      time: '06:45',
      borderBottom: '1px solid #ddd',
      index: 28,
      labelShowFlg: true,
    },
    {
      columnName: '7:00',
      time: '07:00',
      borderBottom: '1px solid #ddd',
      index: 29,
      labelShowFlg: true,
    },
    {
      columnName: '7:15',
      time: '07:15',
      borderBottom: '1px solid #ddd',
      index: 30,
      labelShowFlg: true,
    },
    {
      columnName: '7:30',
      time: '07:30',
      borderBottom: '1px solid #ddd',
      index: 31,
      labelShowFlg: true,
    },
    {
      columnName: '7:45',
      time: '07:45',
      borderBottom: '1px solid #ddd',
      index: 32,
      labelShowFlg: true,
    },
    {
      columnName: '8:00',
      time: '08:00',
      borderBottom: '1px solid #ddd',
      index: 33,
      labelShowFlg: true,
    },
    {
      columnName: '8:15',
      time: '08:15',
      borderBottom: '1px solid #ddd',
      index: 34,
      labelShowFlg: true,
    },
    {
      columnName: '8:30',
      time: '08:30',
      borderBottom: '1px solid #ddd',
      index: 35,
      labelShowFlg: true,
    },
    {
      columnName: '8:45',
      time: '08:45',
      borderBottom: '1px solid #ddd',
      index: 36,
      labelShowFlg: true,
    },
    {
      columnName: '9:00',
      time: '09:00',
      borderBottom: '1px solid #ddd',
      index: 37,
      labelShowFlg: true,
    },
    {
      columnName: '9:15',
      time: '09:15',
      borderBottom: '1px solid #ddd',
      index: 38,
      labelShowFlg: true,
    },
    {
      columnName: '9:30',
      time: '09:30',
      borderBottom: '1px solid #ddd',
      index: 39,
      labelShowFlg: true,
    },
    {
      columnName: '9:45',
      time: '09:45',
      borderBottom: '1px solid #ddd',
      index: 40,
      labelShowFlg: true,
    },
    {
      columnName: '10:00',
      time: '10:00',
      borderBottom: '1px solid #ddd',
      index: 41,
      labelShowFlg: true,
    },
    {
      columnName: '10:15',
      time: '10:15',
      borderBottom: '1px solid #ddd',
      index: 42,
      labelShowFlg: true,
    },
    {
      columnName: '10:30',
      time: '10:30',
      borderBottom: '1px solid #ddd',
      index: 43,
      labelShowFlg: true,
    },
    {
      columnName: '10:45',
      time: '10:45',
      borderBottom: '1px solid #ddd',
      index: 44,
      labelShowFlg: true,
    },
    {
      columnName: '11:00',
      time: '11:00',
      borderBottom: '1px solid #ddd',
      index: 45,
      labelShowFlg: true,
    },
    {
      columnName: '11:15',
      time: '11:15',
      borderBottom: '1px solid #ddd',
      index: 46,
      labelShowFlg: true,
    },
    {
      columnName: '11:30',
      time: '11:30',
      borderBottom: '1px solid #ddd',
      index: 47,
      labelShowFlg: true,
    },
    {
      columnName: '11:45',
      time: '11:45',
      borderBottom: '1px solid #ddd',
      index: 48,
      labelShowFlg: true,
    },
    {
      columnName: '12:00',
      time: '12:00',
      borderBottom: '1px solid #ddd',
      index: 49,
      labelShowFlg: true,
    },
    {
      columnName: '12:15',
      time: '12:15',
      borderBottom: '1px solid #ddd',
      index: 50,
      labelShowFlg: true,
    },
    {
      columnName: '12:30',
      time: '12:30',
      borderBottom: '1px solid #ddd',
      index: 51,
      labelShowFlg: true,
    },
    {
      columnName: '12:45',
      time: '12:45',
      borderBottom: '1px solid #ddd',
      index: 52,
      labelShowFlg: true,
    },
    {
      columnName: '13:00',
      time: '13:00',
      borderBottom: '1px solid #ddd',
      index: 53,
      labelShowFlg: true,
    },
    {
      columnName: '13:15',
      time: '13:15',
      borderBottom: '1px solid #ddd',
      index: 54,
      labelShowFlg: true,
    },
    {
      columnName: '13:30',
      time: '13:30',
      borderBottom: '1px solid #ddd',
      index: 55,
      labelShowFlg: true,
    },
    {
      columnName: '13:45',
      time: '13:45',
      borderBottom: '1px solid #ddd',
      index: 56,
      labelShowFlg: true,
    },
    {
      columnName: '14:00',
      time: '14:00',
      borderBottom: '1px solid #ddd',
      index: 57,
      labelShowFlg: true,
    },
    {
      columnName: '14:15',
      time: '14:15',
      borderBottom: '1px solid #ddd',
      index: 58,
      labelShowFlg: true,
    },
    {
      columnName: '14:30',
      time: '14:30',
      borderBottom: '1px solid #ddd',
      index: 59,
      labelShowFlg: true,
    },
    {
      columnName: '14:45',
      time: '14:45',
      borderBottom: '1px solid #ddd',
      index: 60,
      labelShowFlg: true,
    },
    {
      columnName: '15:00',
      time: '15:00',
      borderBottom: '1px solid #ddd',
      index: 61,
      labelShowFlg: true,
    },
    {
      columnName: '15:15',
      time: '15:15',
      borderBottom: '1px solid #ddd',
      index: 62,
      labelShowFlg: true,
    },
    {
      columnName: '15:30',
      time: '15:30',
      borderBottom: '1px solid #ddd',
      index: 63,
      labelShowFlg: true,
    },
    {
      columnName: '15:45',
      time: '15:45',
      borderBottom: '1px solid #ddd',
      index: 64,
      labelShowFlg: true,
    },
    {
      columnName: '16:00',
      time: '16:00',
      borderBottom: '1px solid #ddd',
      index: 65,
      labelShowFlg: true,
    },
    {
      columnName: '16:15',
      time: '16:15',
      borderBottom: '1px solid #ddd',
      index: 66,
      labelShowFlg: true,
    },
    {
      columnName: '16:30',
      time: '16:30',
      borderBottom: '1px solid #ddd',
      index: 67,
      labelShowFlg: true,
    },
    {
      columnName: '16:45',
      time: '16:45',
      borderBottom: '1px solid #ddd',
      index: 68,
      labelShowFlg: true,
    },
    {
      columnName: '17:00',
      time: '17:00',
      borderBottom: '1px solid #ddd',
      index: 69,
      labelShowFlg: true,
    },
    {
      columnName: '17:15',
      time: '17:15',
      borderBottom: '1px solid #ddd',
      index: 70,
      labelShowFlg: true,
    },
    {
      columnName: '17:30',
      time: '17:30',
      borderBottom: '1px solid #ddd',
      index: 71,
      labelShowFlg: true,
    },
    {
      columnName: '17:45',
      time: '17:45',
      borderBottom: '1px solid #ddd',
      index: 72,
      labelShowFlg: true,
    },
    {
      columnName: '18:00',
      time: '18:00',
      borderBottom: '1px solid #ddd',
      index: 73,
      labelShowFlg: true,
    },
    {
      columnName: '18:15',
      time: '18:15',
      borderBottom: '1px solid #ddd',
      index: 74,
      labelShowFlg: true,
    },
    {
      columnName: '18:30',
      time: '18:30',
      borderBottom: '1px solid #ddd',
      index: 75,
      labelShowFlg: true,
    },
    {
      columnName: '18:45',
      time: '18:45',
      borderBottom: '1px solid #ddd',
      index: 76,
      labelShowFlg: true,
    },
    {
      columnName: '19:00',
      time: '19:00',
      borderBottom: '1px solid #ddd',
      index: 77,
      labelShowFlg: true,
    },
    {
      columnName: '19:15',
      time: '19:15',
      borderBottom: '1px solid #ddd',
      index: 78,
      labelShowFlg: true,
    },
    {
      columnName: '19:30',
      time: '19:30',
      borderBottom: '1px solid #ddd',
      index: 79,
      labelShowFlg: true,
    },
    {
      columnName: '19:45',
      time: '19:45',
      borderBottom: '1px solid #ddd',
      index: 80,
      labelShowFlg: true,
    },
    {
      columnName: '20:00',
      time: '20:00',
      borderBottom: '1px solid #ddd',
      index: 81,
      labelShowFlg: true,
    },
    {
      columnName: '20:15',
      time: '20:15',
      borderBottom: '1px solid #ddd',
      index: 82,
      labelShowFlg: true,
    },
    {
      columnName: '20:30',
      time: '20:30',
      borderBottom: '1px solid #ddd',
      index: 83,
      labelShowFlg: true,
    },
    {
      columnName: '20:45',
      time: '20:45',
      borderBottom: '1px solid #ddd',
      index: 84,
      labelShowFlg: true,
    },
    {
      columnName: '21:00',
      time: '21:00',
      borderBottom: '1px solid #ddd',
      index: 85,
      labelShowFlg: true,
    },
    {
      columnName: '21:15',
      time: '21:15',
      borderBottom: '1px solid #ddd',
      index: 86,
      labelShowFlg: true,
    },
    {
      columnName: '21:30',
      time: '21:30',
      borderBottom: '1px solid #ddd',
      index: 87,
      labelShowFlg: true,
    },
    {
      columnName: '21:45',
      time: '21:45',
      borderBottom: '1px solid #ddd',
      index: 88,
      labelShowFlg: true,
    },
    {
      columnName: '22:00',
      time: '22:00',
      borderBottom: '1px solid #ddd',
      index: 89,
      labelShowFlg: true,
    },
    {
      columnName: '22:15',
      time: '22:15',
      borderBottom: '1px solid #ddd',
      index: 90,
      labelShowFlg: true,
    },
    {
      columnName: '22:30',
      time: '22:30',
      borderBottom: '1px solid #ddd',
      index: 91,
      labelShowFlg: true,
    },
    {
      columnName: '22:45',
      time: '22:45',
      borderBottom: '1px solid #ddd',
      index: 92,
      labelShowFlg: true,
    },
    {
      columnName: '23:00',
      time: '23:00',
      borderBottom: '1px solid #ddd',
      index: 93,
      labelShowFlg: true,
    },
    {
      columnName: '23:15',
      time: '23:15',
      borderBottom: '1px solid #ddd',
      index: 94,
      labelShowFlg: true,
    },
    {
      columnName: '23:30',
      time: '23:30',
      borderBottom: '1px solid #ddd',
      index: 95,
      labelShowFlg: true,
    },
    {
      columnName: '23:45',
      time: '23:45',
      borderBottom: '1px solid #ddd',
      index: 96,
      labelShowFlg: true,
    },
    {
      columnName: '00:00',
      time: '00:00',
      borderBottom: '1px solid #ddd',
      index: 97,
      labelShowFlg: true,
    },
  ]
  /**
   * タブID
   */
  export namespace TAB {
    /**
     * 日課表イメージタブ
     */
    export const TAB_ID_DAILY_TABLE_IMAGE = 'dailyTableImage'
    /**
     * 日常生活活動等タブ
     */
    export const TAB_ID_DAILY_LIFE_ACTIVITIES = 'dailyLifeActivities'
    /**
     * 自立支援に関する処遇タブ
     */
    export const TAB_ID_TREATMENT_RELATED_TO_SELF_RELIANCE_SUPPORT =
      'treatmentRelatedToSelfRelianceSupport'
    /**
     * 介護(介護予防)サービスタブ
     */
    export const TAB_ID_NURSING_PREVENTIVE_CARE_SERVICES = 'nursingPreventiveCareServices'
    /**
     * 特記事項、サービス例タブ
     */
    export const TAB_ID_SPECIAL_NOTES_SERVICES_EXAMOLES = 'specialNotesServicesExamples'
  }
}
