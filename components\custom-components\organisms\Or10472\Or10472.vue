<script setup lang="ts">
/**
 * Or10472:家族マスタ)ダイアログ
 * GUI00829_［家族マスタ］画面
 *
 * @description
 * 処理ロジック
 *
 * <AUTHOR> NGUYEN NHUT THANH
 */
import { onMounted, reactive, ref, watch, computed, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or50242Const } from '../Or50242/Or50242.constants'
import { Or25627Const } from '../Or25627/Or25627.constants'
import { Or10472Const } from './Or10472.constants'
import type { AsyncFunction, Or10472StateType } from './Or10472.type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Or10472OnewayType } from '~/types/cmn/business/components/Or10472Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type {
  FamilyMasterSelectInEntity,
  FamilyMasterSelectOutEntity,
} from '~/repositories/cmn/entities/FamilyMasterSelectEntity'
import { useScreenOneWayBind, useSetupChildProps, useScreenStore, useScreenUtils } from '#imports'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { UPDATE_KBN } from '~/constants/classification-constants'
import type { Or25627Type } from '~/types/cmn/business/components/Or25627Type'
import type {
  FamilyMasterUpdateInEntity,
  FamilyMasterUpdateOutEntity,
} from '~/repositories/cmn/entities/FamilyMasterUpdateEntity'
import { AuthzRepository } from '~/repositories/business/core/authz/AuthzRepository'
import type {
  ICheckAuthzKinouInEntity,
  ICheckAuthzKinouOutEntity,
} from '~/repositories/business/core/authz/entities/CheckAuthzKinouEntity'

const { getChildCpBinds, setChildCpBinds } = useScreenUtils()
const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or10472OnewayType
  uniqueCpId: string
  parentUniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

const defaultOnewayModelValue: Or10472OnewayType = {
  viewAuthority: 'F',
  saveAuthority: 'F',
  dispTabFlg: 'family',
  sysRyaku: 'SYS',
}

/** 権限チェックAPI用の入力パラメータ */
const input: ICheckAuthzKinouInEntity = {
  keys: [{ path: '/components/custom-components/organisms/Or10472/Or10472' }],
}
/** 権限チェックAPIのレスポンスデータ */
const res: ICheckAuthzKinouOutEntity = await AuthzRepository.checkAuthzKinou(input)

/** 保存権限有無フラグ */
const isPermissionSave = ref<boolean>(
  Array.isArray(res.data?.authzKinou)
    ? res.data.authzKinou
        .filter((authz) => authz.path === '/components/custom-components/organisms/Or10472/Or10472')
        .every((authz) => authz.use9)
    : true
)

//(予定マスタ) 行のアクションボタン
const or50242 = ref({ uniqueCpId: Or50242Const.CP_ID(0) })
// (予定マスタ)アセスメント（包括）マスタリスト
const or25627 = ref({ uniqueCpId: Or25627Const.CP_ID(0) })
//Or21814_有機体:確認ダイアログ
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
// Or21813_有機体:エラーダイアログ
const or21813 = ref({ uniqueCpId: Or21813Const.CP_ID(0) })
const localOneway = reactive({
  or10895: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // 予定マスタダイアログ
  mo00024Oneway: {
    width: '500px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or10472',
      toolbarTitle: t('label.family-master-title'),
      toolbarName: 'Or10472ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  // タブ
  mo00043OneWay: {
    tabItems: [
      { id: Or10472Const.TAB.TAB_ID_CARE_CHECK, title: t('label.care-check') },
      { id: Or10472Const.TAB.TAB_ID_OFFER, title: t('label.offer') },
      { id: Or10472Const.TAB.TAB_ID_FAMILY, title: t('label.family') },
      { id: Or10472Const.TAB.TAB_ID_SCHEDULE, title: t('label.schedule') },
      { id: Or10472Const.TAB.TAB_ID_CARE_OFFER_LOCATION, title: t('label.care-offer-location') },
    ],
  } as Mo00043OnewayType,
  // 閉じる
  mo00611OneWay: {
    btnLabel: t('btn.close'),
    tooltipLocation: 'bottom',
    tooltipText: t('tooltip.screen-close'),
  } as Mo00611OnewayType,
  // 保存
  mo00609OneWay: {
    btnLabel: t('btn.save'),
    tooltipLocation: 'bottom',
    tooltipText: t('tooltip.display-row-save'),
    disabled: false,
  } as Mo00609OnewayType,
})

const local = reactive({
  mo00043: { id: Or10472Const.TAB.TAB_ID_FAMILY } as Mo00043Type,
  or25627: {
    editFlg: false,
    delBtnDisabled: false,
    stringInputAssistList: [],
    saveResultStringInputAssistList: [],
  } as Or25627Type,
})

/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or10472Const.DEFAULT.IS_OPEN,
})
const or25627Ref = ref<{
  tableValidation(): AsyncFunction
  createRow(): AsyncFunction
  copyRow(): AsyncFunction
  deleteRow(): AsyncFunction
  init(): AsyncFunction
}>()

// Or50242 Ref
const or50242Ref = ref<{ delBtnDisable(disabled: boolean): AsyncFunction }>()

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or10472StateType>({
  cpId: Or10472Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or10472Const.DEFAULT.IS_OPEN
    },
  },
})
// ダイアログ表示フラグ
const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  return useScreenStore().isEditNavControl()
})
let isClose = ''
const isNewTab = { newTabFlg: false, newTab: '' }
let noFlg = false

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or50242Const.CP_ID(0)]: or50242.value,
  [Or25627Const.CP_ID(0)]: or25627.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or21813Const.CP_ID(0)]: or21813.value,
})

onMounted(async () => {
  await init()
})

/**
 * 予定マスタ初期処理
 */
async function init() {
  // 閲覧権限がない場合、画面に遷移するためのタブを非表示とする
  // if (localOneway.or10895.viewAuthority === 'F') {
  //   // 画面を閉じる。
  //   setState({ isOpen: false })
  //   return
  // }
  // 区分フラグ
  let kbnFlg: string
  //引継情報. sys略称 が "SYS" の場合、１
  if (localOneway.or10895.sysRyaku === Or10472Const.SYS) {
    kbnFlg = '1'
  } else {
    // 以外の場合、3
    kbnFlg = '3'
  }
  // 予定マスタ情報取得(IN)
  const inputData: FamilyMasterSelectInEntity = {
    kbnFlg: kbnFlg,
    cf1Kbn: '1',
  }
  // 予定マスタ初期情報取得
  const ret: FamilyMasterSelectOutEntity = await ScreenRepository.select(
    'familyMasterSelect',
    inputData
  )
  local.or25627.stringInputAssistList = []
  ret.data.stringInputAssistList.map((item) => {
    return local.or25627.stringInputAssistList.push({
      cf1Id: item.cf1Id,
      cf1Kbn: item.cf1Kbn,
      kbnCd: item.kbnCd,
      textKnj: item.textKnj,
      changeF: item.changeF,
      kbnFlg: item.kbnFlg,
      modifiedCnt: item.modifiedCnt,
      updateKbn: UPDATE_KBN.NONE,
    })
  })
  // 登録権限がない場合、
  if (isPermissionSave.value === false) {
    // 保存ボタンを非活性とする
    localOneway.mo00609OneWay.disabled = true
  } else {
    localOneway.mo00609OneWay.disabled = false
  }
  // 親画面.表示タブフラグが「予定」の場合
  if (localOneway.or10895.dispTabFlg === Or10472Const.TAB.TAB_ID_FAMILY) {
    // 予定タブを選択する
    local.mo00043.id = Or10472Const.TAB.TAB_ID_FAMILY
  }
  or25627Ref.value?.init()
}

/**
 * 保存
 */
async function save() {
  await nextTick()
  // 画面入力データ変更があるかどうかを判定する
  if (isEdit.value) {
    // 変更がある場合、処理継続
    const valid = or25627Ref.value?.tableValidation()
    // 変更ありのデータ行をINPUT情報として、下記の保存APIを実行する
    const dataArray = local.or25627.stringInputAssistList.filter(
      (data) => data.updateKbn !== UPDATE_KBN.DELETE
    )
    //予定一覧.区分番号、予定一覧.内容の何れかがブランクの場合
    const blankArray = dataArray.filter((data) => data.kbnCd === '' || data.textKnj === '')
    if (blankArray.length > 0) {
      showOr21813Msg(t('message.e-cmn-41708'))
      return false
    }
    //予定マスタ一覧に、重複した区分番号が存在する場合
    const nameSet = new Set<string>()
    for (const item of dataArray) {
      if (nameSet.has(item.kbnCd)) {
        showOr21813Msg(t('message.e-cmn-41713', [item.kbnCd]))
        return false
      }
      nameSet.add(item.kbnCd)
    }
    if (!valid) {
      return false
    }
    // 区分フラグ
    let kbnFlg: string
    //引継情報. sys略称 が "SYS" の場合、１
    if (localOneway.or10895.sysRyaku === Or10472Const.SYS) {
      kbnFlg = '1'
    } else {
      // 以外の場合、3
      kbnFlg = '3'
    }
    const param: FamilyMasterUpdateInEntity = {
      kbnFlg: kbnFlg,
      stringInputAssistList: local.or25627.stringInputAssistList
        .filter((item) => item.updateKbn !== UPDATE_KBN.NONE)
        .map((item) => {
          return {
            ...item,
            kbnFlg: item.kbnFlg || kbnFlg,
            cf1Kbn: item.cf1Kbn || '1',
          }
        }),
    }
    // 予定マスタ情報保存
    const ret: FamilyMasterUpdateOutEntity = await ScreenRepository.update(
      'familyMasterUpdate',
      param
    )
    ret.data.stringInputAssistList?.map((item) => {
      return local.or25627.stringInputAssistList.push({
        cf1Id: item.cf1Id,
        cf1Kbn: item.cf1Kbn,
        kbnCd: item.kbnCd,
        textKnj: item.textKnj,
        changeF: item.changeF,
        kbnFlg: item.kbnFlg,
        modifiedCnt: item.modifiedCnt,
        updateKbn: UPDATE_KBN.NONE,
      })
    })
    await nextTick()
    const data = getChildCpBinds(props.uniqueCpId, {
      Or25627: { cpPath: 'Or25627', twoWayFlg: true },
    })
    // 変更がない場合、処理終了。
    setChildCpBinds(props.uniqueCpId, {
      Or25627: {
        twoWayValue: data.Or25627.twoWayBind?.value,
      },
    })
    await init()
    return true
  }
}

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  isClose = 'true'
  if (!isEdit.value) {
    // 画面を閉じる。
    setState({ isOpen: false })
  } else {
    if (false === isPermissionSave.value) {
      // 変更がある場合、確認ダイアログを表示する。(保存権限がない場合)
      showOr21814MsgTwoBtn(t('message.i-cmn-10006'))
    } else {
      // 変更がある場合、確認ダイアログを表示する。(保存権限がある場合)
      showOr21814MsgThreeBtn(t('message.i-cmn-10430'))
    }
  }
}

/**
 * 閉じるボタン押下_保存権限がない場合の開閉
 *
 * @param errormsg - Message
 */
function showOr21814MsgTwoBtn(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: errormsg,
      firstBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.no'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 閉じるボタン押下_保存権限がある場合の開閉
 *
 * @param errormsg - Message
 */
function showOr21814MsgThreeBtn(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト0
      dialogText: errormsg,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 保存ボタン押下_保存権限がある場合の開閉
 *
 * @param errormsg - Message
 */
function showOr21813Msg(errormsg: string) {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト0
      dialogText: errormsg,
      firstBtnType: 'normal1',
      firstBtnLabel: 'OK',
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })
  // エラーダイアログをオープン
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 行追加ボタン
 */
function onAddItem() {
  or25627Ref.value?.createRow()
}

/**
 * 行複写ボタン
 */
function onCloneItem() {
  or25627Ref.value?.copyRow()
}

/**
 * 行削除ボタン
 */
function onDelete() {
  or25627Ref.value?.deleteRow()
}

/**
 * 行削除ボタン活性状態を監視
 *
 * @description
 * 活性状態を監視
 */
watch(
  () => local.or25627.delBtnDisabled,
  (newValue) => {
    or50242Ref.value?.delBtnDisable(!newValue)
  }
)
/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    mo00024.value.isOpen = true
    if (!newValue) {
      close()
    }
  }
)

/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (false === isPermissionSave.value) {
      if ('true' === isClose) {
        if (newValue.secondBtnClickFlg) {
          // そうです：画面を閉じる。
          setState({ isOpen: false })
        } else {
          return
        }
      } else {
        // 処理続き
        if (newValue.secondBtnClickFlg) {
          noFlg = true
          local.mo00043.id = isNewTab.newTab
        }
      }
    } else {
      if ('true' === isClose) {
        if (newValue.firstBtnClickFlg) {
          const res = await save()
          if (res) {
            // そうです：画面を閉じる。
            setState({ isOpen: false })
          }
        } else if (newValue.secondBtnClickFlg) {
          // いいえ：画面を閉じる。
          setState({ isOpen: false })
        } else {
          return
        }
      } else {
        if (newValue.firstBtnClickFlg) {
          const res = await save()
          if (res) {
            noFlg = true
            local.mo00043.id = isNewTab.newTab
          }
          return
        } else if (newValue.secondBtnClickFlg) {
          noFlg = true
          local.mo00043.id = isNewTab.newTab
        } else {
          return
        }
      }
    }
    // 子コンポーネントのflgをリセットする
    Or21814Logic.event.set({
      uniqueCpId: or21814.value.uniqueCpId,
      events: {
        secondBtnClickFlg: false,
        firstBtnClickFlg: false,
      },
    })
  }
)

// メニュー切替
watch(
  () => local.mo00043.id,
  async (newValue) => {
    if (!noFlg) {
      // 変更がない場合、画面データあるかどうかを判定する。
      if (newValue !== Or10472Const.TAB.TAB_ID_FAMILY) {
        isNewTab.newTabFlg = true
        isNewTab.newTab = newValue
        // タブを設定に切り替え
        await nextTick()
        if (isEdit.value) {
          // タブ切替の阻止
          local.mo00043.id = Or10472Const.TAB.TAB_ID_FAMILY
          if (false === isPermissionSave.value) {
            // 変更がある場合、確認ダイアログを表示する。(保存権限がない場合)
            showOr21814MsgTwoBtn(t('message.i-cmn-10006'))
          } else {
            // 変更がある場合、確認ダイアログを表示する。(保存権限がある場合)
            showOr21814MsgThreeBtn(t('message.i-cmn-10430'))
          }
        }
      }
    } else {
      isNewTab.newTabFlg = false
      noFlg = false
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
    style="padding: 8px !important"
  >
    <template #cardItem>
      <c-v-row>
        <c-v-col class="pa-2 table-wrapper">
          <base-mo00043
            v-model="local.mo00043"
            :oneway-model-value="localOneway.mo00043OneWay"
          ></base-mo00043>
          <!-- タブ switch -->
          <c-v-window v-model="local.mo00043.id">
            <c-v-window-item value="careCheck">
              <!-- タブ：ケアチェック -->
            </c-v-window-item>
            <c-v-window-item value="offer">
              <!-- タブ：提供 -->
            </c-v-window-item>
            <c-v-window-item value="family">
              <!-- タブ：家族 -->
              <div class="mb-2"></div>
              <!-- (予定マスタ) 行のアクションボタン -->
              <div class="head-btn-margin">
                <g-custom-or-50242
                  ref="or50242Ref"
                  v-bind="or50242"
                  @on-add-item="onAddItem"
                  @on-clone-item="onCloneItem"
                  @on-delete="onDelete"
                />
              </div>
              <!-- (予定マスタ)アセスメント（包括）マスタリスト -->
              <g-custom-or-25627
                ref="or25627Ref"
                v-bind="or25627"
                v-model="local.or25627"
                :parent-unique-cp-id="props.uniqueCpId"
              >
              </g-custom-or-25627>
            </c-v-window-item>
            <c-v-window-item value="schedule">
              <!-- タブ：予定 -->
            </c-v-window-item>
            <c-v-window-item value="careOfferLocation">
              <!-- タブ：ケアの提供場所 -->
            </c-v-window-item>
          </c-v-window>
        </c-v-col>
      </c-v-row>
    </template>
    <!-- フッター -->
    <template #cardActionRight>
      <!-- 予定コンテンツエリア-フッターアクションバー -->
      <c-v-row
        v-if="local.mo00043.id === Or10472Const.TAB.TAB_ID_FAMILY"
        no-gutters
      >
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          @click="close"
        >
          <!-- ツールチップ表示 -->
          <c-v-tooltip
            v-if="localOneway.mo00611OneWay.tooltipText"
            :text="localOneway.mo00611OneWay.tooltipText"
            :location="localOneway.mo00611OneWay.tooltipLocation"
            activator="parent"
          />
        </base-mo00611>
        <!-- 保存ボタン -->
        <base-mo00609
          v-bind="localOneway.mo00609OneWay"
          class="mx-2"
          :disabled="!isPermissionSave"
          @click="save"
        >
          <!-- ツールチップ表示 -->
          <c-v-tooltip
            v-if="localOneway.mo00609OneWay.tooltipText"
            :text="localOneway.mo00609OneWay.tooltipText"
            :location="localOneway.mo00609OneWay.tooltipLocation"
            activator="parent"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  />
  <!-- Or21813:有機体:エラーダイアログ -->
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  />
</template>

<style scoped lang="scss">
$table-row-height: 32px;

:deep(.table-wrapper table tbody tr td),
:deep(.table-wrapper .row-height) {
  height: $table-row-height !important;
  max-height: $table-row-height !important;
  min-height: $table-row-height !important;
}

.v-row {
  margin: -8px;
}
</style>
