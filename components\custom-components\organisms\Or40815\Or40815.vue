<script setup lang="ts">
/**
 * Or40815：有機体：（基本チェックリストデータ）計画対象期間選択
 * GUI01080_基本チェックリスト
 *
 * @description
 * 計画対象期間選択の処理
 *
 * <AUTHOR> NGUYEN VAN PHONG
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import type { OrT0002OneWayType } from '../OrT0002/OrT0002.type'
import { OrX0115Logic } from '../OrX0115/OrX0115.logic'
import { OrX0115Const } from '../OrX0115/OrX0115.constants'
import { Or40815Const } from './Or40815.constants'
import { Or40815Logic } from './Or40815.logic'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CustomClass } from '~/types/CustomClassType'
import type { OrCpGroupDefinitionInputFormDeleteDialogType } from '~/types/cmn/business/generator-components/OrCpGroupDefinitionInputFormDeleteDialog'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Or40815OnewayType, Or40815Type } from '~/types/cmn/business/components/Or40815Type'
import { useScreenTwoWayBind, useSetupChildProps } from '#imports'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { OrX0115OnewayType } from '~/types/cmn/business/components/OrX0115Type'

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or40815OnewayType
  uniqueCpId: string
}
const props = defineProps<Props>()
console.log('Or40815 Props',props);

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()
const planTargetPeriodDataIndex = ref<number>(-1)

const orX0115 = ref({ uniqueCpId: '' })
const or21814 = ref({ uniqueCpId: '' })
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0115Const.CP_ID(0)]: orX0115.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
})

const cssVars = computed(() => {
  return {
    '--custom-color': localOneway.kikanDataExitFlg ? 'inherit' : 'rgb(217, 2, 20)',
  }
})

const dialogFlg = ref<number>(0)

const localOneway = reactive({
  computed: {
    cssVars() {
      return {
        '--custom-color': 'red',
        '--border-size': '2px',
      }
    },
  },
  or40815Oneway: {} as Or40815OnewayType,
  // 計画対象期間タイトルラベル
  mo00615Oneway: {
    itemLabel: t('label.planning-period'),
    customClass: new CustomClass({ outerClass: 'mr-0', labelClass: 'ma-1' }),
  } as Mo00615OnewayType,
  // 計画対象期間選択アイコンボタン
  mo00009Oneway: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  // 計画対象期間
  mo01338Oneway: {
    value: '',
    valueFontWeight: 'true',
    customClass: new CustomClass({
      itemClass: 'ml-1 font-color-red',
      labelClass: 'ma-1 font-color-red',
    }),
  } as Mo01338OnewayType,
  // 計画対象期間-前へアイコンボタン
  mo00009Twoway: {
    // デフォルト値の設定
    btnIcon: 'chevron_left',
    density: 'compact',
  } as Mo00009OnewayType,
  mo01338Twoway: {
    value: '0 / 0',
    valueFontWeight: 'true',
  } as Mo01338OnewayType,
  // 計画対象期間-次へアイコンボタン
  mo00009Threeway: {
    // デフォルト値の設定
    btnIcon: 'chevron_right',
    density: 'compact',
  } as Mo00009OnewayType,
  moDialogConfirm: {
    mo00024: {
      isOpen: false,
    } as Mo00024Type,
  } as OrCpGroupDefinitionInputFormDeleteDialogType,
  kikanDataExitFlg: false,
  orT0002Oneway: {
    planTargetPeriodDataList: [],
    termid: 0,
  } as OrT0002OneWayType,
  orX0115Oneway: {
    kindId: '',
    sc1Id: '',
  } as OrX0115OnewayType,
})

/**************************************************
 * Pinia
 **************************************************/
useScreenTwoWayBind<Or40815Type>({
  cpId: Or40815Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(() => {
  // 確認ダイアログを初期化
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      dialogTitle: t('label.top-btn-title'),
      dialogText: '',
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      iconName: 'info',
      iconColor: 'rgb(var(--v-theme-blue-700))',
      iconBackgroundColor: 'rgb(var(--v-theme-blue-200))',
    },
  })
})

/**
 * 計画対象期間データ更新の監視
 */
watch(
  () => props.onewayModelValue,
  (newValue) => {
    localOneway.or40815Oneway = {
      ...newValue,
    }
    // 計画対象期間選択一覧設定
    localOneway.orX0115Oneway = newValue.orX0115Oneway
    init()
  },
  { deep: true }
)

/**
 * データ再取得フラグの監視
 */

/**
 * 計画期間情報初期化
 */
function init() {
  if (
    localOneway.or40815Oneway.planTargetPeriodData &&
    localOneway.or40815Oneway.planTargetPeriodData.totalCount > 0
  ) {
    localOneway.mo01338Oneway.value =
      localOneway.or40815Oneway.planTargetPeriodData.planTargetPeriod!
    localOneway.mo01338Twoway.value =
      localOneway.or40815Oneway.planTargetPeriodData.currentIndex +
      ' / ' +
      localOneway.or40815Oneway.planTargetPeriodData.totalCount
    localOneway.mo01338Oneway.valueFontWeight = undefined
    planTargetPeriodDataIndex.value = localOneway.or40815Oneway.planTargetPeriodData.currentIndex
    localOneway.kikanDataExitFlg = true
  } else {
    localOneway.mo01338Oneway.value = t('label.plan-no-data-label')
    localOneway.mo01338Twoway.value =
      localOneway.or40815Oneway.planTargetPeriodData.currentIndex +
      ' / ' +
      localOneway.or40815Oneway.planTargetPeriodData.totalCount
    localOneway.kikanDataExitFlg = false
  }
}
/**
 *  ボタン押下時の処理
 */
function onClickDialog() {
  //計画期間変更区分:0:選択先の期間ID
  Or40815Logic.data.set({
    uniqueCpId: props.uniqueCpId,
    value: {
      planTargetPeriodId: Or40815Logic.data.get(props.uniqueCpId)!.planTargetPeriodId,
      planTargetPeriodUpdateFlg: Or40815Const.UPDATE_CATEGORY_SELECT,
      orX0115UniqueCpId: orX0115.value.uniqueCpId,
    },
  })
}

/**
 * AC013: 「計画対象期間-前へアイコンボタン」押下
 *  計画対象期間-前へアイコンボタンボタン押下時の処理
 */
function onClickMo00009Twoway() {
  // 1件目の計画対象期間データが表示されている状態
  if (planTargetPeriodDataIndex.value === 1) {
    // 確認ダイアログを開く
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: {
        isOpen: true,
        dialogText: t('message.i-cmn-11262'),
      },
    })
    return
  }

  // 1件目の計画対象期間データが表示されている状態
  if (planTargetPeriodDataIndex.value === 1) {
    dialogFlg.value = Or40815Const.MO_00009_TWOWAY
    // 上書確認ダイアログ
    localOneway.moDialogConfirm.mo00024.isOpen = true
    return
  }

  //計画期間変更区分：1:選択している期間IDの前
  Or40815Logic.data.set({
    uniqueCpId: props.uniqueCpId,
    value: {
      planTargetPeriodId: Or40815Logic.data.get(props.uniqueCpId)!.planTargetPeriodId,
      planTargetPeriodUpdateFlg: Or40815Const.UPDATE_CATEGORY_PREVIOUS,
    },
  })
}
/**
 *  計画対象期間-計画対象期間-計画対象期間-前へアイコンボタンボタン押下時の処理
 *
 */
function onClickMo00009Threeway() {
  // 1件目の計画対象期間データが表示されている状態
  if (
    planTargetPeriodDataIndex.value === localOneway.or40815Oneway.planTargetPeriodData.totalCount
  ) {
    // 上書確認ダイアログ
    // 確認ダイアログを開く
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: {
        isOpen: true,
        dialogText: t('message.i-cmn-11262'),
      },
    })
    return
  }
  //計画期間変更区分設定：2:選択している期間IDの次
  Or40815Logic.data.set({
    uniqueCpId: props.uniqueCpId,
    value: {
      planTargetPeriodId: Or40815Logic.data.get(props.uniqueCpId)!.planTargetPeriodId,
      planTargetPeriodUpdateFlg: Or40815Const.UPDATE_CATEGORY_NEXT,
    },
  })
}

// ダイアログ表示フラグ
const showDialogOrX0115 = computed(() => {
  // OrX0115 cks_flg=1 のダイアログ開閉状態
  return OrX0115Logic.state.get(orX0115.value.uniqueCpId)?.isOpen ?? false
})
</script>

<template>
  <c-v-row
    no-gutters
    class="text-center"
  >
    <!--計画対象期間  ラベル-->
    <c-v-col cols="auto">
      <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway" />
    </c-v-col>
    <!--計画対象期間アイコンボタン-->
    <c-v-col
      cols="auto"
      align-self="center"
      style="padding-top: 0px; padding-bottom: 0px; padding-right: 0px; padding-left: 0px"
    >
      <base-mo00009
        :oneway-model-value="localOneway.mo00009Oneway"
        @click="onClickDialog"
      />

      <!-- GU00070_対象期間画面 -->
      <g-custom-or-x-0115
        v-if="showDialogOrX0115"
        v-bind="orX0115"
      />
    </c-v-col>
    <!-- 計画対象期間 -->
    <c-v-col
      id="k1"
      cols="auto"
      class="multiline-text"
      :style="cssVars"
      style="padding-top: 0px; padding-bottom: 0px; padding-right: 0px; padding-left: 0px"
    >
      <base-mo01338 :oneway-model-value="localOneway.mo01338Oneway" />
    </c-v-col>
    <!-- 計画対象期間-前へアイコンボタン -->
    <c-v-col
      cols="auto"
      align-self="center"
      style="padding-top: 0px; padding-bottom: 0px; padding-right: 0px; padding-left: 0px"
    >
      <base-mo00009
        :oneway-model-value="localOneway.mo00009Twoway"
        @click="onClickMo00009Twoway()"
      />
    </c-v-col>
    <!-- 画対象期間-ページング -->
    <c-v-col cols="auto">
      <base-mo01338 :oneway-model-value="localOneway.mo01338Twoway" />
    </c-v-col>
    <!--計画対象期間-次へアイコンボタン-->
    <c-v-col
      cols="auto"
      align-self="center"
    >
      <base-mo00009
        :oneway-model-value="localOneway.mo00009Threeway"
        @click="onClickMo00009Threeway"
      />
    </c-v-col>
  </c-v-row>
  <!-- Or21814 確認ダイアログ -->
  <g-base-or21814 v-bind="or21814" />
</template>

<style scoped lang="scss">
.font-color-red {
  color: rgb(217, 2, 20);
}
.text-center {
  align-items: baseline;
}
.multiline-text {
  white-space: pre-line;
  text-align: left;
}
</style>
