<script setup lang="ts">
/**
 * Or27263:有機体:月間・年間表（画面/特殊コンポーネント）
 * GUI00939_月間・年間表
 *
 * @description
 * GUI00939_月間・年間表の処理
 *
 * <AUTHOR> 王利峰
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { isUndefined } from 'lodash'
import { OrX0007Const } from '../OrX0007/OrX0007.constants'
import { OrX0008Const } from '../OrX0008/OrX0008.constants'
import { OrX0009Const } from '../OrX0009/OrX0009.constants'
import { OrX0010Const } from '../OrX0010/OrX0010.constants'
import { OrX0007Logic } from '../OrX0007/OrX0007.logic'
import { OrX0008Logic } from '../OrX0008/OrX0008.logic'
import { Or15845Const } from '../Or15845/Or15845.constants'
import { Or15846Const } from '../Or15846/Or15846.constants'
import { Or15843Const } from '../Or15843/Or15843.constants'
import { Or15852Const } from '../Or15852/Or15852.constants'
import { Or28630Const } from '../Or28630/Or28630.constants'
import { Or28630Logic } from '../Or28630/Or28630.logic'
import { Or28676Const } from '../Or28676/Or28676.constants'
import { Or28676Logic } from '../Or28676/Or28676.logic'
import { Or15845Logic } from '../Or15845/Or15845.logic'
import { Or15846Logic } from '../Or15846/Or15846.logic'
import { OrX0009Logic } from '../OrX0009/OrX0009.logic'
import { OrX0010Logic } from '../OrX0010/OrX0010.logic'
import { Or10464Logic } from '../Or10464/Or10464.logic'
import { Or10464Const } from '../Or10464/Or10464.constants'
import type { Or27263StateType } from './Or27263.type'
import { Or27263Const } from './Or27263.constants'
import {
  useCommonProps,
  useNuxtApp,
  useScreenInitFlg,
  useScreenStore,
  useScreenUtils,
  useSetupChildProps,
  useSystemCommonsStore,
  useGyoumuCom,
  useJigyoList,
} from '#imports'
import type { DebugLogPluginInterface } from '~/plugins/debug-log'
import { Or00051Const } from '~/components/base-components/organisms/Or00051/Or00051.constants'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import type { PlanCreateDataType } from '~/types/PlanCreateDataType'
import type { OrX0007Type, OrX0007OnewayType } from '~/types/cmn/business/components/OrX0007Type'
import type {
  OrX0008Type,
  OrX0008OnewayType,
  RirekiInfo,
} from '~/types/cmn/business/components/OrX0008Type'
import type { OrX0009Type, OrX0009OnewayType } from '~/types/cmn/business/components/OrX0009Type'
import type { OrX0010Type, OrX0010OnewayType } from '~/types/cmn/business/components/OrX0010Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  MonthlyYearlyTableSelectOutEntity,
  MonthlyYearlyTableSelectOutData,
} from '~/repositories/cmn/entities/MonthlyYearlyTableSelectEntity'
import type { Or15845Type } from '~/types/cmn/business/components/Or15845Type'
import type { Mo00046Type } from '~/types/business/components/Mo00046Type'
import type { Or15843OnewayType, Or15843Type } from '~/types/cmn/business/components/Or15843Type'
import type { Or15852OnewayType, Or15852Type } from '~/types/cmn/business/components/Or15852Type'
import type { Or28630OnewayType, Or28630Type } from '~/types/cmn/business/components/Or28630Type'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type { MonthlyYearlyTableUpdateInEntity } from '~/repositories/cmn/entities/MonthlyYearlyTableUpdateEntity'
import type {
  MonthlyYearlyTablePtnTitleCntSelectInEntity,
  MonthlyYearlyTablePtnTitleCntSelectOutEntity,
} from '~/repositories/cmn/entities/MonthlyYearlyTablePtnTitleCntSelectEntity'
import type {
  MonthlyYearlyTableCopyReturnSelectInEntity,
  MonthlyYearlyTableCopyReturnSelectOutEntity,
} from '~/repositories/cmn/entities/MonthlyYearlyTableCopyReturnSelectEntity'
import { useCmnRouteCom } from '~/utils/useCmnRouteCom'
import type {
  GyoumuComSelectInEntity,
  IKikanComInfo,
} from '~/repositories/cmn/entities/GyoumuComSelectEntity'
import type { GyoumuComUpdateOutEntity } from '~/repositories/cmn/entities/GyoumuComUpdateEntity'
import type {
  Or21814OnewayType,
  Or21814EventType,
} from '~/components/base-components/organisms/Or21814/Or21814.type'
import { useUserListInfo } from '~/utils/useUserListInfo'
import { SPACE_WAVE, UPDATE_KBN } from '~/constants/classification-constants'
import { hasRegistAuth, hasPrintAuth } from '~/utils/useCmnAuthz'
import type { Or28676OnewayType, Or28676Type } from '~/types/cmn/business/components/Or28676Type'
import { Or00094Const } from '~/components/base-components/organisms/Or00094/Or00094.constants'
import { Or00094Logic } from '~/components/base-components/organisms/Or00094/Or00094.logic'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import type { Mo00040Type } from '~/types/business/components/Mo00040Type'
import type { Or10464OnewayType } from '~/types/cmn/business/components/Or10464Type'
import { Or00249Logic } from '~/components/base-components/organisms/Or00249/Or00249.logic'
import { Or00249Const } from '~/components/base-components/organisms/Or00249/Or00249.constants'

const { setChildCpBinds, getChildCpBinds, searchUniqCpId } = useScreenUtils()
const cmnRouteCom = useCmnRouteCom()
const gyoumuCom = useGyoumuCom()
const $log = useNuxtApp().$log as DebugLogPluginInterface
const { t } = useI18n()
/** props */
const props = defineProps(useCommonProps())
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
const { syscomUserSelectWatchFunc } = useUserListInfo()
const { jigyoListWatch } = useJigyoList()
/**************************************************
 * Props
 **************************************************/
/**************************************************
 * 変数定義
 **************************************************/
const defaultModelValue = {
  or27263: {
    /** 操作区分 */
    operaFlg: Or27263Const.OPERA_FLAG_0,
    /**履歴ID*/
    rirekiId: '0',
  } as Or27263StateType,
  strArray: [] as string[],
}

const defaultComponents = {
  // 期間データ
  orX0007: { PlanTargetPeriodUpdateFlg: '' } as OrX0007Type,
  //履歴
  orX0008: {
    createId: '0',
    createUpateFlg: '',
  } as OrX0008Type,
  //作成者
  orX0009: { staffId: '', staffName: '' } as OrX0009Type,
  //作成日
  orX0010: { value: '' } as OrX0010Type,
  //ケース番号 Or15843
  or15843: {
    value: '',
  } as Or15843Type,
  //年度 Or15852
  or15852: {
    year: '',
  } as Or15852Type,
  //月間・年間表_履歴対象 Or15845
  or15845: {
    mm: { value: '' },
    naiyoKnj: { value: '' },
    mokutekiKnj: { value: '' },
    seq: '',
    id: '',
    modifiedCnt: '',
  } as Or15845Type,
  //視点入力支援 Or15846
  or15846: { value: '' } as Mo00046Type,
}

const defaultOneway = reactive({
  //期間データ
  orX0007Oneway: { planTargetPeriodData: { currentIndex: 0, totalCount: 0 } } as OrX0007OnewayType,
  //履歴
  orX0008Oneway: {
    createData: {} as RirekiInfo,
    screenID: 'GUI00939',
  } as OrX0008OnewayType,
  //作成者
  orX0009Oneway: {
    createData: {} as PlanCreateDataType,
    isDisabled: false,
  } as OrX0009OnewayType,
  //作成日
  orX0010Oneway: {} as OrX0010OnewayType,

  //ケース番号 Or15843
  Or15843Oneway: { isRequired: false, showItemLabel: true } as Or15843OnewayType,

  //年度 Or15852
  Or15852Oneway: { year: '' } as Or15852OnewayType,
  //月間・年間表_履歴対象 Or15845

  //視点入力支援 Or15846
})

const local = reactive({
  or27263: {
    ...defaultModelValue.or27263,
    // ...Props.modelValue,
  } as Or27263StateType,
})

const localComponents = reactive({
  orX0007: {
    ...defaultComponents.orX0007,
  } as OrX0007Type,
  orX0008: {
    ...defaultComponents.orX0008,
  } as OrX0008Type,
  orX0009: {
    ...defaultComponents.orX0009,
  } as OrX0009Type,
  orX0010: {
    ...defaultComponents.orX0010,
  } as OrX0010Type,

  //ケース番号 Or15843
  or15843: {
    ...defaultComponents.or15843,
  } as Or15843Type,

  //年度 Or15852
  or15852: {
    ...defaultComponents.or15852,
  } as Or15852Type,
  //月間・年間表_履歴対象 Or15845
  or15845: [
    {
      ...defaultComponents.or15845,
    } as Or15845Type,
  ],
  //視点入力支援 Or15846
  or15846: {
    ...defaultComponents.or15846,
  } as Mo00046Type,

  //月間・年間表パターン（設定）画面ポップアップ
  or28630: {
    monthlyData: [],
    yearly: '',
    emitType: Or28630Const.DEFAULT.EMIT_TYPE_INIT,
  } as Or28630Type,
})

const localOneway = reactive({
  orX0007Oneway: {
    ...defaultOneway.orX0007Oneway,
  } as OrX0007OnewayType,
  orX0008Oneway: {
    ...defaultOneway.orX0008Oneway,
  } as OrX0008OnewayType,
  orX0009Oneway: {
    ...defaultOneway.orX0009Oneway,
  } as OrX0009OnewayType,
  orX0010Oneway: {
    ...defaultOneway.orX0010Oneway,
  } as OrX0010OnewayType,
  or15843Oneway: {
    ...defaultOneway.orX0010Oneway,
  } as Or15843OnewayType,
  or15852Oneway: {
    ...defaultOneway.Or15852Oneway,
  } as Or15852OnewayType,

  //GUI00940_月間・年間表パターン（設定）画面ポップアップ
  or28630Oneway: { monthlyData: [{ monthly: { value: '' } }], yearly: '' } as Or28630OnewayType,
  //GUI00941_月間・年間表複写画面 ポップアップ
  or28676Oneway: {} as Or28676OnewayType,
  //GUI00944_印刷設定画面 ポップアップ
  or10464Oneway: {
    userList: [{}],
    gojuuOnKana: [] as string[],
  } as Or10464OnewayType,
})

const or11871 = ref({ uniqueCpId: '' })
const or00248 = ref({ uniqueCpId: '' })
const orX0007_1 = ref({ uniqueCpId: '' })
const orX0008_1 = ref({ uniqueCpId: '' })
const orX0009_1 = ref({ uniqueCpId: '' })
const orX0010_1 = ref({ uniqueCpId: '' })
const or15843_1 = ref({ uniqueCpId: '' })
const or15845_1 = ref({ uniqueCpId: '' })
const or15846_1 = ref({ uniqueCpId: '' })
const or15852_1 = ref({ uniqueCpId: '' })
const or00051 = ref({ uniqueCpId: '' })
const or21813_1 = ref({ uniqueCpId: '' })
const or21814_1 = ref({ uniqueCpId: '' })
const or28630_1 = ref({ uniqueCpId: '' })
const or28676_1 = ref({ uniqueCpId: '' })
const or10464_1 = ref({ uniqueCpId: '' })
const or41179_1 = ref({ uniqueCpId: '' })
/**************************************************
 * コンポーネント固有処理
 **************************************************/
const isInit = useScreenInitFlg()
onMounted(async () => {
  Or11871Logic.state.set({
    uniqueCpId: or11871.value.uniqueCpId,
    state: {
      disabledSaveBtn: !(await hasRegistAuth(Or27263Const.LINK_AUTH)), //共通処理の保存権限チェックを行う
      disabledPrintBtn: !(await hasPrintAuth(Or27263Const.LINK_AUTH)), //共通処理の印刷権限チェックを行う
    },
  })
  // 利用者を全選択です。
  const uniqueCpId248 = searchUniqCpId(props.uniqueCpId, Or00248Const.CP_ID(1), 0)
  if (uniqueCpId248) {
    const uniqueCpId94 = searchUniqCpId(uniqueCpId248, Or00094Const.CP_ID(0), 0)
    if (uniqueCpId94) {
      Or00094Logic.state.set({
        uniqueCpId: uniqueCpId94,
        state: {
          dispSettingBtnDisplayFlg: true,
          focusSettingFlg: true,
          focusSettingInitial: [Or27263Const.STR_ALL],
        },
      })
    }
  }
  local.or27263.onMountedOpenFlg = Or27263Const.ONMOUNTED_OPEN_0
  // 初期情報取得
  if (isInit) {
    if (systemCommonsStore.getUserSelectSelfId()) {
      Or41179Logic.state.set({
        uniqueCpId: or41179_1.value.uniqueCpId,
        state: {
          searchCriteria: {
            selfId: systemCommonsStore.getUserSelectSelfId(),
          },
        },
      })
    }
    await initOr27263()
  }
  local.or27263.onMountedOpenFlg = Or27263Const.ONMOUNTED_OPEN_1
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or00248Const.CP_ID(1)]: or00248.value,
  [Or11871Const.CP_ID]: or11871.value,
  [Or00051Const.CP_ID]: or00051.value,
  [OrX0007Const.CP_ID(1)]: orX0007_1.value,
  [OrX0008Const.CP_ID(1)]: orX0008_1.value,
  [OrX0009Const.CP_ID(1)]: orX0009_1.value,
  [OrX0010Const.CP_ID(1)]: orX0010_1.value,
  [Or15843Const.CP_ID(1)]: or15843_1.value,
  [Or15845Const.CP_ID(1)]: or15845_1.value,
  [Or15846Const.CP_ID(1)]: or15846_1.value,
  [Or15852Const.CP_ID(1)]: or15852_1.value,
  [Or21813Const.CP_ID(1)]: or21813_1.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or28630Const.CP_ID(1)]: or28630_1.value,
  [Or10464Const.CP_ID(1)]: or10464_1.value,
  [Or28676Const.CP_ID(1)]: or28676_1.value,
  [Or41179Const.CP_ID(1)]: or41179_1.value,
})

const or00249 = ref({ uniqueCpId: '' })
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(or00248.value.uniqueCpId, {
  [Or00249Const.CP_ID(0)]: or00249.value,
})

Or11871Logic.state.set({
  uniqueCpId: or11871.value.uniqueCpId,
  state: {
    screenTitleLabel: t('label.monthly-yearly-table'),
    showFavorite: true,
    showViewSelect: false,
    showCreateBtn: true,
    showCreateMenuCopy: false,
    showPrintBtn: true,
    showOptionMenuBtn: true,
    viewSelectItems: [],
    showMasterBtn: false,
    showOptionMenuDelete: true,
    showSaveBtn: true,
  },
})

/**
 * 事業所選択更新の監視
 */
// ★事業所選択監視関数を実行
jigyoListWatch(or41179_1.value.uniqueCpId, (newJigyoId: string) => {
  void callbackFuncJigyo(newJigyoId)
})

/**
 * 事業所選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newJigyoId - 変更後の事業者ID
 */
async function callbackFuncJigyo(newJigyoId: string) {
  console.log('callbackFuncJigyo 開始:', newJigyoId)
  console.log('callbackFuncJigyo getSvJigyoId<<:', systemCommonsStore.getSvJigyoId)
  const oldJigyoId = systemCommonsStore.getSvJigyoId ?? ''
  await gyoumuCom.doComLogicChangeSvJigyo(
    newJigyoId,
    oldJigyoId,
    local.or27263,
    isEdit.value,
    showConfirmMessgeBox,
    _save,
    getMonthlyYearlyTableData,
    setCommonPlanPeriod,
    setSvJigyoId,
    setSelectSelUserIndex
  )
  console.log('callbackFuncJigyo getSvJigyoId>>:', systemCommonsStore.getSvJigyoId)
  console.log('callbackFuncJigyo 終了:', newJigyoId)
}

/**
 * 事業所設定
 *
 * @param jigyoId - 設定の事業者ID
 */
const setSvJigyoId = (jigyoId: string) => {
  systemCommonsStore.setSvJigyoId(jigyoId)
  Or41179Logic.data.set({
    uniqueCpId: or41179_1.value.uniqueCpId,
    value: {
      modelValue: jigyoId,
    } as Mo00040Type,
  })
}

/**
 * 利用者設定
 *
 * @param index - 利用者index
 */
const setSelectSelUserIndex = (index: number) => {
  //利用者設定
  Or00249Logic.data.set({
    uniqueCpId: or00249.value.uniqueCpId,
    value: {
      selectUserIndex: index,
    },
  })
}

// ★利用者選択監視関数を実行
syscomUserSelectWatchFunc(callbackFuncSub01)

/**
 * 利用者選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newSelfId - 利用者
 */
function callbackFuncSub01(newSelfId: string) {
  if (newSelfId !== '') {
    console.log('利用者選択監視関数を実行:', systemCommonsStore.getUserSelectSelfId())
    // 事業所選択プルダウンの検索条件を更新
    // 検索条件を変更すると、変更を検知して事業所情報の更新が行われる
    // 更新が完了すると、関数「 jigyoListWatch 」に指定したコールバック関数が実行されます
    Or41179Logic.state.set({
      uniqueCpId: or41179_1.value.uniqueCpId,
      state: {
        searchCriteria: {
          selfId: newSelfId,
        },
      },
    })
  }
}

// GUI00944_印刷設定画面 ダイアログ表示フラグ
const showDialogOr10464 = computed(() => {
  // Or28824のダイアログ開閉状態
  return Or28630Logic.state.get(or10464_1.value.uniqueCpId)?.isOpen ?? false
})

// GUI00940_月間・年間表パターン（設定） ダイアログ表示フラグ
const showDialogOr28630 = computed(() => {
  // Or28824のダイアログ開閉状態
  return Or28630Logic.state.get(or28630_1.value.uniqueCpId)?.isOpen ?? false
})

// GUI00941_月間・年間表複写画面 ダイアログ表示フラグ
const showDialogOr28676 = computed(() => {
  // Or28676のダイアログ開閉状態
  return Or28676Logic.state.get(or28676_1.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * Emit
 **************************************************/

/**
 * 計画期間変更の監視
 */
watch(
  () => OrX0007Logic.data.get(orX0007_1.value.uniqueCpId),
  async (newValue) => {
    /**********************************************************************
     * データ再取得
    /*********************************************************************/
    $log.debug('計画期間変更フラグの監視 planTargetPeriodUpdateFlg:', newValue)
    if (isUndefined(newValue)) {
      return
    }

    const planID = newValue.planTargetPeriodId
    const planUpdateFlg = newValue.PlanTargetPeriodUpdateFlg

    if (planUpdateFlg === undefined || planUpdateFlg === '') {
      return
    }

    if (planUpdateFlg === '0' && planID !== '0' && planID !== '') {
      //「期間-選択確認前 アイコンボタン」押下
      setCommonPlanPeriod({ ...local.or27263.kikanObj, sc1Id: String(planID) })
      //共通情報.履歴ＩＤ ＝ 0
      local.or27263.rirekiId = '0'
      //操作区分 ＝ K3:計画対象期間Open
      local.or27263.operaFlg = Or27263Const.OPERA_FLAG_K3
      //データ再取得
      await getMonthlyYearlyTableData()
    } else if (planUpdateFlg === '1') {
      //「期間-前へ アイコンボタン」押下
      local.or27263.operaFlg = Or27263Const.OPERA_FLAG_K1
      //共通情報.履歴ＩＤ ＝ 0
      local.or27263.rirekiId = '0'
      //遷移保存確認区分
      local.or27263.moveSaveConfirmFlg = isEdit.value ? '1' : '0'
      //データ再取得
      await getMonthlyYearlyTableData()
    } else if (planUpdateFlg === '2') {
      //「期間-次へ アイコンボタン」押下
      local.or27263.operaFlg = Or27263Const.OPERA_FLAG_K2
      //共通情報.履歴ＩＤ ＝ 0
      local.or27263.rirekiId = '0'
      //遷移保存確認区分
      local.or27263.moveSaveConfirmFlg = isEdit.value ? '1' : '0'
      //データ再取得
      await getMonthlyYearlyTableData()
    }
  },
  { deep: true }
)

/**
 * 履歴変更の監視
 */
watch(
  () => OrX0008Logic.data.get(orX0008_1.value.uniqueCpId),
  async (newValue) => {
    /**********************************************************************
     * データ再取得
    /*********************************************************************/
    $log.debug('履歴変更の監視 createUpdateFlg:', newValue)
    if (isUndefined(newValue)) {
      return
    }

    const rirekiId = newValue.createId
    const createUpateFlg = newValue.createUpateFlg

    if (createUpateFlg === undefined || createUpateFlg === '') {
      return
    }

    if (createUpateFlg === '0') {
      //「履歴-選択確認後 アイコンボタン」押下
      // 表示用「実施計画①履歴」情報を設定する
      local.or27263.rirekiObj = {
        // ・履歴ＩＤ ＝ 返回値.履歴ＩＤ
        rirekiId: rirekiId,
        // ・ケースNo. ＝ 返回値.ケースNo.
        caseNo: newValue.rirekiObj?.caseNo ?? '',
        // ・作成者ＩＤ ＝ 返回値.作成者ＩＤ
        shokuId: newValue.rirekiObj?.staffId ?? '',
        // ・作成者 ＝ 返回値.作成者
        shokuKnj: newValue.rirekiObj?.staffName ?? '',
        nendoY: newValue.rirekiObj?.nendoY ?? '',
        sogoShitenKnj: newValue.rirekiObj?.sogoShitenKnj ?? '',
        // ・改訂フラグ ＝ 返回値.改訂フラグ
        kaiteiFlg: newValue.rirekiObj?.kaiteiFlg ?? '',
        // ・更新回数 ＝ 返回値.更新回数
        modifiedCnt: newValue.rirekiObj?.modifiedCnt ?? '',
        createYmd: newValue.rirekiObj?.createDate ?? '',
        // 表示用「実施計画①履歴」情報.ページング番号 ＝ 返回値.順番
        pagingNo: String(newValue.rirekiObj?.currentIndex),
        // 表示用「実施計画①履歴」情報.ページング総数 ＝ 返回値.総件数
        pagingCnt: String(newValue.rirekiObj?.totalCount),
      }
      local.or27263.operaFlg = Or27263Const.OPERA_FLAG_R3
      //データ再取得
      await getMonthlyYearlyTableData()
    } else if (createUpateFlg === '1') {
      //「履歴-前へ アイコンボタン」押下
      local.or27263.operaFlg = Or27263Const.OPERA_FLAG_R1
      local.or27263.moveSaveConfirmFlg = isEdit.value ? '1' : '0'
      //データ再取得
      await getMonthlyYearlyTableData()
    } else if (createUpateFlg === '2') {
      //「履歴-次へ アイコンボタン」押下
      local.or27263.operaFlg = Or27263Const.OPERA_FLAG_R2
      local.or27263.moveSaveConfirmFlg = isEdit.value ? '1' : '0'
      //データ再取得
      await getMonthlyYearlyTableData()
    }
  },
  { deep: true }
)

/**
 * パターンデータの更新の監視
 */
watch(
  () => localComponents.or28630,
  (newValue) => {
    console.log('パターンデータの更新の監視:', newValue)
    if (newValue.emitType === Or28630Const.DEFAULT.EMIT_TYPE_COMFIRM) {
      const monthlyList = Or15845Logic.data.get(or15845_1.value.uniqueCpId)!
      for (let i = 0; i < monthlyList.length; i++) {
        monthlyList[i].naiyoKnj.value = newValue.monthlyData![i].content.value
        monthlyList[i].mokutekiKnj.value = newValue.monthlyData![i].objective.value
      }
      //月間データをパターン画面からで設定
      Or15845Logic.data.set({
        uniqueCpId: or15845_1.value.uniqueCpId,
        value: monthlyList,
      })
      //年間データをパターン画面からで設定
      Or15846Logic.data.set({
        uniqueCpId: or15846_1.value.uniqueCpId,
        value: { value: newValue.yearly },
      })
    }
  }
)

/**
 * 保存（削除）フラグ更新の監視
 */
watch(
  () => local.or27263.operaFlg,
  (newValue) => {
    console.log('保存（削除）フラグ更新の監視:', newValue)
    if (local.or27263.operaFlg === Or27263Const.OPERA_FLAG_3) {
      // 「新規」ボタン、「複写」メニュー、「パターン」メニュー、「印刷設定アイコン」ボタン、「削除」メニューを非活性にする
      Or11871Logic.state.set({
        uniqueCpId: or11871.value.uniqueCpId,
        state: {
          disabledCreateBtn: true,
          disabledPrintBtn: true,
          disabledOptionMenuDelete: true,
          disabledCreateMenuCopy: true,
        },
      })
      // 「作成者選択アイコンボタン」を活性にする
      OrX0009Logic.state.set({
        uniqueCpId: orX0009_1.value.uniqueCpId,
        state: {
          isDisabled: true,
        },
      })
      // 「作成日選択アイコンボタン」、「作成日」を非活性にする
      OrX0010Logic.state.set({
        uniqueCpId: orX0010_1.value.uniqueCpId,
        state: {
          isDisabled: true,
        },
      })
    } else {
      // 「新規」ボタン、「複写」メニュー、「パターン」メニュー、「印刷設定アイコン」ボタン、「削除」メニューを活性にする
      Or11871Logic.state.set({
        uniqueCpId: or11871.value.uniqueCpId,
        state: {
          disabledCreateBtn: false,
          disabledPrintBtn: false,
          disabledOptionMenuDelete: false,
          disabledCreateMenuCopy: false,
        },
      })
      // 「作成者選択アイコンボタン」を活性にする
      OrX0009Logic.state.set({
        uniqueCpId: orX0009_1.value.uniqueCpId,
        state: {
          isDisabled: false,
        },
      })
      // 「作成日選択アイコンボタン」、「作成日」を活性にする
      OrX0010Logic.state.set({
        uniqueCpId: orX0010_1.value.uniqueCpId,
        state: {
          isDisabled: false,
        },
      })
    }
  }
)

/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or11871Logic.event.get(or11871.value.uniqueCpId),
  async (newValue) => {
    $log.debug('画面メニューのイベントを監視', newValue)
    if (newValue === undefined) {
      return
    }

    if (newValue.favoriteEventFlg) {
      $log.debug('お気に入り')
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { favoriteEventFlg: false },
      })
    }
    if (newValue.saveEventFlg) {
      $log.debug('保存')
      await _save()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { saveEventFlg: false },
      })
    }
    if (newValue.createEventFlg) {
      $log.debug('新規')
      await _create()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { createEventFlg: false },
      })
    }
    if (newValue.printEventFlg) {
      $log.debug('印刷')
      await _print()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { printEventFlg: false },
      })
    }
    if (newValue.masterEventFlg) {
      $log.debug('マスタ他')
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { masterEventFlg: false },
      })
    }
    if (newValue.deleteEventFlg) {
      $log.debug('削除')
      await _delete()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { deleteEventFlg: false },
      })
    }
    if (newValue.copyEventFlg) {
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { copyEventFlg: false },
      })
    }
  }
)

/**************************************************
 * コンポーネント固有処理
 **************************************************/

/**
 * 共通情報.計画期間を設定
 *
 * @param kikanObj - 計画期間
 */
const setCommonPlanPeriod = (kikanObj: IKikanComInfo) => {
  cmnRouteCom.setPlanPeriod({
    kikanId: kikanObj.sc1Id,
    kikanIndex: kikanObj.pagingNo ?? '',
    startYmd: kikanObj.startYmd ?? '',
    endYmd: kikanObj.endYmd ?? '',
  })
}

/**
 * メッセージを表示する
 *
 * @param messageId - メッセージ
 */
const showMessageBox = async (messageId: string) => {
  await openInfoDialog2({
    // ダイアログタイトル
    dialogTitle: t('label.error'),
    // ダイアログテキスト
    dialogText: t(messageId),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.ok'),
    secondBtnType: 'blank',
    thirdBtnType: 'blank',
  })
}

/**
 * メッセージを表示する
 */
const showConfirmMessgeBox = async () => {
  return await openInfoDialog2({
    // ダイアログタイトル
    dialogTitle: t('label.top-btn-title'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-10430'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'destroy1',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'normal3',
    thirdBtnLabel: t('btn.cancel'),
  })
}

/**
 * 初期化処理
 */
const initOr27263 = async () => {
  //操作区分 ＝ 0:初期化
  local.or27263.operaFlg = Or27263Const.OPERA_FLAG_0
  //データ取得
  if (systemCommonsStore.getUserSelectSelfId() && systemCommonsStore.getSvJigyoId) {
    await getMonthlyYearlyTableData()
  }
}

/**
 * 画面表示のデータを取得
 */
const getMonthlyYearlyTableData = async () => {
  console.log('monthlyYearlyTableData > local.or27263:', local.or27263)

  const inputParam: GyoumuComSelectInEntity = {
    ...gyoumuCom.getGyoumuComSelectInEntity(
      local.or27263.operaFlg,
      local.or27263.moveSaveConfirmFlg,
      local.or27263.rirekiPagingNo,
      cmnRouteCom.getPlanPeriod()?.kikanId
    ),
    rirekiId:
      local.or27263.rirekiId && local.or27263.rirekiId !== '' ? local.or27263.rirekiId : '0',
  }
  const ret: MonthlyYearlyTableSelectOutEntity = await ScreenRepository.select(
    'monthlyYearlyTableSelect',
    inputParam
  )
  //API戻り値の設定前の処理
  if (
    !(await gyoumuCom.doComLogicSelectApi(
      ret,
      showMessageBox,
      showConfirmMessgeBox,
      _save,
      getMonthlyYearlyTableData,
      local.or27263,
      {
        dataList: ret.data.dataList ?? [],
        initMasterObj: ret.data.initMasterObj ?? {},
      } as MonthlyYearlyTableSelectOutData,
      setCommonPlanPeriod
    ))
  ) {
    return
  }
  const retData = ret.data
  console.log('retData:', retData)

  //画面初期化のComponents表示フラグ設定、画面初期化のデータ設定
  setFormData()
  setChildCpBinds(props.uniqueCpId, {
    [OrX0007Const.CP_ID(1)]: {
      twoWayValue: {
        planTargetPeriodId: localComponents.orX0007.planTargetPeriodId,
        PlanTargetPeriodUpdateFlg: localComponents.orX0007.PlanTargetPeriodUpdateFlg,
      },
    },
    [OrX0008Const.CP_ID(1)]: {
      twoWayValue: {
        createId: localComponents.orX0008.createId,
        createUpdateFlg: localComponents.orX0008.createUpateFlg,
      },
    },
    [OrX0009Const.CP_ID(1)]: {
      twoWayValue: {
        staffId: localComponents.orX0009.staffId,
        staffName: localComponents.orX0009.staffName,
      },
    },
    [OrX0010Const.CP_ID(1)]: {
      twoWayValue: {
        value: localComponents.orX0010.value,
        mo01343: {},
      },
    },
    [Or15843Const.CP_ID(1)]: {
      twoWayValue: { value: localComponents.or15843.value },
    },
    [Or15845Const.CP_ID(1)]: {
      twoWayValue: localComponents.or15845,
      oneWayState: { initMasterObj: local.or27263.initMasterObj },
    },
    [Or15846Const.CP_ID(1)]: {
      twoWayValue: { value: localComponents.or15846.value ?? '' },
      oneWayState: { initMasterObj: local.or27263.initMasterObj },
    },
    [Or15852Const.CP_ID(1)]: {
      twoWayValue: {
        year: localComponents.or15852.year,
      },
    },
  })
  //選定の利用者INDEXを一時保存
  local.or27263.searchSelUserIndex =
    Or00249Logic.data.get(or00249.value.uniqueCpId)?.selectUserIndex ?? 0
}

/**
 *
 * 画面コントロール表示設定
 */
function setFormData() {
  console.log('START setFormData()')
  const kikanObj = local.or27263.kikanObj
  const rirekiObj = local.or27263.rirekiObj
  //種別ID
  localOneway.orX0007Oneway.kindId = local.or27263.syubetuId
  if (kikanObj.pagingFlg !== Or27263Const.PLAN_TARGET_PERIOD_0) {
    //orX0007
    localOneway.orX0007Oneway.planTargetPeriodData.currentIndex = kikanObj.pagingNo
      ? Number(kikanObj.pagingNo)
      : 0
    localOneway.orX0007Oneway.planTargetPeriodData.totalCount = kikanObj.pagingCnt
      ? Number(kikanObj.pagingCnt)
      : 0
    if (kikanObj.startYmd && kikanObj.endYmd) {
      //表示用「計画対象期間」情報.開始日 + " ～ " + 表示用「計画対象期間」情報.終了日
      localOneway.orX0007Oneway.planTargetPeriodData.planTargetPeriod = kikanObj.startYmd
        .concat(SPACE_WAVE)
        .concat(kikanObj.endYmd)
    }
    localOneway.orX0007Oneway.planTargetPeriodData.planTargetPeriodId = Number(kikanObj.sc1Id)
    localComponents.orX0007.planTargetPeriodId = kikanObj.sc1Id
  } else {
    // ・計画対象期間-ページングを"0 / 0"で表示にする。
    // ・計画対象期間を固定文字「計画対象期間アイコンボタンから（改行）期間登録を行ってください」で表示※太字、赤色
    // ・履歴、作成者、作成日、入力フームを非表示にする。
    localOneway.orX0007Oneway.planTargetPeriodData.currentIndex = 0
    localOneway.orX0007Oneway.planTargetPeriodData.totalCount = 0
    localComponents.orX0007.planTargetPeriodId = '0'
  }

  //orX0008
  localOneway.orX0008Oneway.sc1Id = kikanObj.sc1Id
  localOneway.orX0008Oneway.officeId = systemCommonsStore.getSvJigyoId ?? ''
  localOneway.orX0008Oneway.useId = systemCommonsStore.getUserSelectSelfId() ?? ''
  localOneway.orX0008Oneway.createData.createDate = rirekiObj.createYmd
  localOneway.orX0008Oneway.createData.createId = rirekiObj.rirekiId
  localOneway.orX0008Oneway.createData.currentIndex = rirekiObj.pagingNo
    ? Number(rirekiObj.pagingNo)
    : 0
  localOneway.orX0008Oneway.createData.staffId = rirekiObj.shokuId
  localOneway.orX0008Oneway.createData.staffName = rirekiObj.shokuKnj
  localOneway.orX0008Oneway.createData.totalCount = rirekiObj.pagingCnt
    ? Number(rirekiObj.pagingCnt)
    : 0
  localComponents.orX0008.createId = rirekiObj.rirekiId

  //orX0009
  localOneway.orX0009Oneway.createData!.createDate = rirekiObj.createYmd
  localOneway.orX0009Oneway.createData!.createId = Number(rirekiObj.rirekiId)
  localOneway.orX0009Oneway.createData!.currentIndex = Number(rirekiObj.pagingNo)
  localOneway.orX0009Oneway.createData!.staffId = Number(rirekiObj.shokuId)
  localOneway.orX0009Oneway.createData!.staffName = rirekiObj.shokuKnj
  localOneway.orX0009Oneway.createData!.totalCount = Number(rirekiObj.pagingCnt)

  localComponents.orX0009.staffId = rirekiObj.shokuId
  localComponents.orX0009.staffName = rirekiObj.shokuKnj
  //orX0010 計画書_履歴リストデータが存在の場合、計画書_履歴リスト1件目.作成日
  localComponents.orX0010.value = rirekiObj.createYmd
  //共通情報を設定する
  setCommonPlanPeriod(kikanObj)
  //or15843 ケース番号設定
  localComponents.or15843.value = rirekiObj.caseNo
  //or15852 年度設定
  localComponents.or15852.year = rirekiObj.nendoY
  localOneway.or15852Oneway.year = rirekiObj.nendoY
  local.or27263.rirekiId = rirekiObj.rirekiId
  //or15845 月間データ設定
  if (local.or27263.dataList !== undefined && local.or27263.dataList.length > 0) {
    localComponents.or15845 = local.or27263.dataList.map((item) => ({
      mm: { value: item.mm, unit: Or27263Const.UNIT_MONTH },
      naiyoKnj: { value: item.naiyoKnj },
      mokutekiKnj: { value: item.mokutekiKnj },
      seq: item.seq,
      /** ＩＤ */
      id: item.id,
      /** 更新回数 */
      modifiedCnt: item.modifiedCnt,
    }))
  }
  //or15846 年間データ設定
  localComponents.or15846.value = rirekiObj.sogoShitenKnj
  //or30583 の情報設定

  console.log('END setFormData()')
}

/**
 * 新規作成時処理
 */
async function _create() {
  console.log(' START _create')
  //操作区分が3:削除の場合
  if (local.or27263.operaFlg === Or27263Const.OPERA_FLAG_3) {
    return
  }
  //二回目新規ボタン押下する場合、メッセージを表示i.cmn.11265
  // 新規作成した{0}が保存されていません。保存を行った後に新規作成してください。
  if (local.or27263.operaFlg === Or27263Const.OPERA_FLAG_1) {
    await openInfoDialog(Or27263Const.INFO_MESSAGE_TYPE_11265)
    return
  }

  //期間管理フラグが「1:管理する」、かつ、計画対象期間リストが0件、メッセージを表示e.cmn.40980(i.cmn.11300)
  if (
    local.or27263.kikanFlg === Or27263Const.PLAN_TARGET_PERIOD_1 &&
    local.or27263.kikanObj.pagingFlg === '0'
  ) {
    await openErrorDialog()
    // 「対象期間」画面をポップアップで起動する。
    return
  }

  //画面入力データに変更がある場合、メッセージを表示i.cmn.10430
  if (isEdit.value) {
    const dialogResult = await openInfoDialog(Or27263Const.INFO_MESSAGE_TYPE_10430)
    switch (dialogResult) {
      case Or27263Const.DIALOG_RESULT_YES:
        await _save()
        break
      case Or27263Const.DIALOG_RESULT_NO:
        // いいえ選択時は編集内容を破棄するので何もしない
        break
      case Or27263Const.DIALOG_RESULT_CANCEL:
        // キャンセル選択時は複写データの作成を行わずに終了する
        return
    }
  }
  //共通情報.履歴ＩＤ ＝ 0
  local.or27263.rirekiId = '0'
  //操作区分 ＝ 1:新規
  local.or27263.operaFlg = Or27263Const.OPERA_FLAG_1
  //データ再取得
  await getMonthlyYearlyTableData()
  console.log('END _create')
}

/**
 * 保存時処理
 */
async function _save() {
  //画面入力データに変更がある場合
  console.log('_save 操作を開始')
  await gyoumuCom.doComLogicSave(
    local.or27263,
    isEdit.value,
    getMonthlyYearlyTableData,
    saveApiData
  )
  console.log('_save 操作を終了')
}

/**
 * 保存処理を行う。
 */
async function saveApiData() {
  const orX0009Data = OrX0009Logic.data.get(orX0009_1.value.uniqueCpId)
  const screenData = getChildCpBinds(props.uniqueCpId, {
    or15843: { cpPath: Or15843Const.CP_ID(1) },
    or15852: { cpPath: Or15852Const.CP_ID(1) },
    or15845: { cpPath: Or15845Const.CP_ID(1) },
    or15846: { cpPath: Or15846Const.CP_ID(1) },
  })
  const or15843Data = screenData.or15843.twoWayBind?.value as Or15843Type
  const or15852Data = screenData.or15852.twoWayBind?.value as Or15852Type
  const or15845Data = screenData.or15845.twoWayBind?.value as Or15845Type[]
  const or15846Data = screenData.or15846.twoWayBind?.value as Mo00046Type
  const inputParam: MonthlyYearlyTableUpdateInEntity = {
    ...gyoumuCom.getGyoumuComUpdateInEntity(
      local.or27263.operaFlg,
      local.or27263.kikanFlg,
      local.or27263.syubetuId,
      cmnRouteCom.getPlanPeriod()?.kikanId
    ),
    rirekiId: local.or27263.rirekiId,
    kaiteiFlg: local.or27263.rirekiObj.kaiteiFlg,
    //履歴データ
    rirekiObj: {
      /** currentIndex */
      rirekiId: local.or27263.rirekiId,
      /** 作成者ID */
      shokuId: orX0009Data?.staffId ?? '',
      /** 作成者 */
      shokuKnj: orX0009Data?.staffName ?? '',
      /** 作成日 */
      createYmd: OrX0010Logic.data.get(orX0010_1.value.uniqueCpId)?.value ?? '',
      /** ケース番号 */
      caseNo: or15843Data.value,
      /** 年度 */
      nendoY: or15852Data.year,
      /** 視点入力支援 */
      sogoShitenKnj: or15846Data.value ?? '',
      /** 改訂フラグ */
      kaiteiFlg: local.or27263.rirekiObj.kaiteiFlg,
      /** 更新回数 */
      modifiedCnt: local.or27263.rirekiObj.modifiedCnt,
      /** 更新区分 */
      updateKbn:
        local.or27263.operaFlg === Or27263Const.OPERA_FLAG_3
          ? UPDATE_KBN.DELETE
          : local.or27263.operaFlg === Or27263Const.OPERA_FLAG_1
            ? UPDATE_KBN.CREATE
            : UPDATE_KBN.UPDATE,
    },
    dataList: or15845Data.map((x) => {
      return {
        mm: x.mm.value,
        naiyoKnj: x.naiyoKnj.value,
        mokutekiKnj: x.mokutekiKnj.value,
        seq: x.seq,
        id: x.id,
        modifiedCnt: x.modifiedCnt,
        updateKbn:
          local.or27263.operaFlg === Or27263Const.OPERA_FLAG_3
            ? UPDATE_KBN.DELETE
            : x.id
              ? UPDATE_KBN.UPDATE
              : UPDATE_KBN.CREATE,
      }
    }),
  }
  //期間管理フラグが0:期間管理しない場合
  if (local.or27263.kikanFlg === '0') {
    const kikanObj = local.or27263.kikanObj
    //表示用「計画対象期間」情報.計画期間ＩＤが空白の場合
    if (kikanObj.sc1Id === '') {
      inputParam.kikanObj = {
        ...kikanObj,
        updateKbn: UPDATE_KBN.CREATE,
      }
    }
  }
  console.log('input:', inputParam)
  //画面情報の保存処理を行う。
  const ret: GyoumuComUpdateOutEntity = await ScreenRepository.update(
    'monthlyYearlyTableUpdate',
    inputParam
  )
  return ret
}

/**
 * 「印刷設定アイコンボタン」押下の処理
 */
async function _print() {
  console.log('_print 操作を開始')
  // i.cmn.10430
  // はい：AC003の保存処理を行って、処理続き。
  // いいえ：処理続き。
  // キャンセル：処理終了。
  if (isEdit.value) {
    const dialogResult = await openInfoDialog(Or27263Const.INFO_MESSAGE_TYPE_10430)
    switch (dialogResult) {
      case Or27263Const.DIALOG_RESULT_YES:
        // はい選択時は入力内容を保存する
        await _save()
        break
      case Or27263Const.DIALOG_RESULT_NO:
        // いいえ選択時は編集内容を破棄するので何もしない
        break
      case Or27263Const.DIALOG_RESULT_CANCEL:
        // キャンセル選択時は複写データの作成を行わずに終了する
        return
    }
  }
  // 親画面.利用者リスト
  //なし
  // 親画面.利用者ID
  localOneway.or10464Oneway.userId = systemCommonsStore.getUserSelectSelfId() ?? ''
  // 親画面.セクション名
  localOneway.or10464Oneway.sectionName = ''
  // 親画面.初期選択セクション
  localOneway.or10464Oneway.choIndex = '1'
  // 親画面.計画期間管理フラグ
  localOneway.or10464Oneway.kikanFlg = local.or27263.kikanFlg
  // 親画面.50音行番号
  localOneway.or10464Oneway.gojuuOnRowNo = ''
  // 親画面.50音母音
    const workGetUserSelectFilterInitials =
    systemCommonsStore.getUserSelectFilterInitials() as string[]
  localOneway.or10464Oneway.gojuuOnKana =
    workGetUserSelectFilterInitials ?? defaultModelValue.strArray
  // 親画面.履歴ID
  localOneway.or10464Oneway.historyId = local.or27263.rirekiId
  // 親画面.システムコード (共通情報)
  // 親画面.システム略称 (共通情報)
  // 親画面.法人ID (共通情報)
  // 親画面.施設ID (共通情報)
  // 親画面.事業者ID (共通情報)
  // 親画面.職員ID (共通情報)
  // 親画面.担当者ID
  // 親画面.担当開始日 (共通情報)
  // 親画面.選択帳票番号
  //cpnFlg
  localOneway.or10464Oneway.cpnFlg = local.or27263.initMasterObj.cpnFlg
  localOneway.or10464Oneway.cpnFlg = ''
  //書式
  localOneway.or10464Oneway.shosikiFlg = ''
  Or10464Logic.state.set({
    uniqueCpId: or10464_1.value.uniqueCpId,
    state: { isOpen: true },
  })
  console.log('_print 操作を終了')
}

/**
 *
 * 削除処理
 *
 */
async function _delete() {
  console.log('_delete 操作を開始')
  if (
    local.or27263.operaFlg === Or27263Const.OPERA_FLAG_3 ||
    local.or27263.kikanObj.pagingFlg === '0'
  ) {
    return
  }
  //共通処理 機能の使用権限をチェックする。

  //削除確認（）
  const dialogResult = await openInfoDialog(Or27263Const.INFO_MESSAGE_TYPE_11326)
  switch (dialogResult) {
    case Or27263Const.DIALOG_RESULT_YES:
      // はい：処理継続
      break
    case Or27263Const.DIALOG_RESULT_NO:
      // いいえ：処理終了
      return
  }
  //月間・年間表のデータの更新区分を「D:削除」にする。
  local.or27263.operaFlg = Or27263Const.OPERA_FLAG_3
  // 以下の項目を非活性にする

  //以下の項目を非表示にする。
  // 「作成者タイトルラベル」、「作成者選択アイコンボタン」、「作成者名」、「ケース番号」、「年度前へ」、「年度次へ」、月間・年間表_履歴対象、視点入力支援

  console.log('_delete 操作を終了')
}

/**
 *
 * 「パターン」押下の処理
 *
 */
async function _pattern() {
  console.log('_pattern Start')
  const inputParam: MonthlyYearlyTablePtnTitleCntSelectInEntity = {
    masterKbn: Or27263Const.MASTER_KBN_YEAR_MONTH_PATTERN,
  }
  //APIで月間・年間表詳細情報を取得する。
  const ret: MonthlyYearlyTablePtnTitleCntSelectOutEntity = await ScreenRepository.select(
    'monthlyYearlyTablePtnTitleCntSelect',
    inputParam
  )
  //OUTPUT情報：データ件数
  const retCount = ret.data.ptnTitleCnt
  if (retCount !== '0') {
    console.log(
      'データ件数が0より大きい場合、「GUI00940_月間・年間表パターン（設定）」画面をポップアップで起動する'
    )
    //OUTPUT情報．データ件数が0より大きい場合、「GUI00940_月間・年間表パターン（設定）」画面をポップアップで起動する
    const monthlyList = ref<Or15845Type[]>(Or15845Logic.data.get(or15845_1.value.uniqueCpId)!)
    for (const item of monthlyList.value) {
      localComponents.or28630.monthlyData!.push({
        monthly: { value: item.mm.value },
        content: { value: item.naiyoKnj.value },
        objective: { value: item.mokutekiKnj.value },
      })
    }

    localComponents.or28630.yearly = Or15846Logic.data.get(or15846_1.value.uniqueCpId)?.value
    localComponents.or28630.emitType = Or28630Const.DEFAULT.EMIT_TYPE_INIT
    localOneway.or28630Oneway.userId = systemCommonsStore.getUserSelectSelfId() ?? ''
    console.log('localComponents.or28630.monthlyData:', localComponents.or28630.monthlyData)
    console.log('localComponents.or28630.yearly:', localComponents.or28630.yearly)
    // Or28630のダイアログ開閉状態を更新する
    Or28630Logic.state.set({
      uniqueCpId: or28630_1.value.uniqueCpId,
      state: { isOpen: true },
    })
  } else {
    //OUTPUT情報．データ件数が0の場合、パターン（タイトル）画面をポップアップで起動する
    console.log('データ件数が0の場合、パターン（タイトル）画面をポップアップで起動する')
  }

  console.log('_pattern End')
}

/**
 * 編集破棄ダイアログ表示
 *
 * @param msgType - message TYPE
 *
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
async function openInfoDialog(msgType: string): Promise<string> {
  if (msgType === Or27263Const.INFO_MESSAGE_TYPE_10430) {
    // データ変更確認ダイアログを初期化(i.cmn.10430)
    Or21814Logic.state.set({
      uniqueCpId: or21814_1.value.uniqueCpId,
      state: {
        // ダイアログタイトル
        dialogTitle: t('label.top-btn-title'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-10430'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'destroy1',
        secondBtnLabel: t('btn.no'),
        thirdBtnType: 'normal3',
        thirdBtnLabel: t('btn.cancel'),
      },
    })
  } else if (msgType === Or27263Const.INFO_MESSAGE_TYPE_11326) {
    // 削除確認ダイアログを初期化(i.cmn.11326)
    Or21814Logic.state.set({
      uniqueCpId: or21814_1.value.uniqueCpId,
      state: {
        // ダイアログタイトル
        dialogTitle: t('label.top-btn-title'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-11326', [
          localComponents.orX0010.value,
          t('label.monthly-yearly-table'),
        ]),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'destroy1',
        secondBtnLabel: t('btn.no'),
        thirdBtnType: 'blank',
      },
    })
  } else if (msgType === Or27263Const.INFO_MESSAGE_TYPE_11265) {
    // 二回目新規確認ダイアログを初期化(i.cmn.11265)
    Or21814Logic.state.set({
      uniqueCpId: or21814_1.value.uniqueCpId,
      state: {
        // ダイアログタイトル
        dialogTitle: t('label.top-btn-title'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-11265', [t('label.monthly-yearly-table')]),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      },
    })
  }

  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)

        let result = Or27263Const.DIALOG_RESULT_CANCEL

        if (event?.firstBtnClickFlg) {
          result = Or27263Const.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or27263Const.DIALOG_RESULT_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = Or27263Const.DIALOG_RESULT_CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * エラーダイアログをオープンする
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
function openInfoDialog2(state: Or21814OnewayType): Promise<Or21814EventType | undefined> {
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      ...state,

      isOpen: true,
    },
  })
  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(event)
      },
      { once: true }
    )
  })
}

/**
 * エラーダイアログ表示（e.cmn.40980）
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
async function openErrorDialog(): Promise<string> {
  // 計画対象期間リストが0件のエラーダイアログを初期化(i.cmn.40980)
  Or21813Logic.state.set({
    uniqueCpId: or21813_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト
      dialogText: t('message.e-cmn-40980'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })

  // 確認ダイアログを開く
  Or21813Logic.state.set({
    uniqueCpId: or21813_1.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813_1.value.uniqueCpId)

        let result = Or27263Const.DIALOG_RESULT_CANCEL

        if (event?.firstBtnClickFlg) {
          result = Or27263Const.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or27263Const.DIALOG_RESULT_NO
        }

        // 確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 「複写ボタン」押下
 */
function onCopyClick() {
  //操作区分が3:削除の場合
  //表示用「計画対象期間」情報.ページング区分が0:なしの場合
  if (
    local.or27263.operaFlg === Or27263Const.OPERA_FLAG_3 ||
    local.or27263.kikanObj.pagingFlg === '0'
  ) {
    return
  }
  //共通情報.施設ＩＤ
  localOneway.or28676Oneway.shisetuId = systemCommonsStore.getShisetuId ?? ''
  //共通情報.適用事業所ＩＤ（※リスト）
  localOneway.or28676Oneway.svJigyoIdList = systemCommonsStore.getSvJigyoIdList as string[]
  //共通情報.利用者ＩＤ
  localOneway.or28676Oneway.userId = systemCommonsStore.getUserSelectSelfId() ?? ''
  //種別ＩＤ
  localOneway.or28676Oneway.syubetuId = local.or27263.syubetuId
  //期間管理フラグ
  localOneway.or28676Oneway.kikanFlg = local.or27263.kikanFlg
  // Or00100のダイアログ開閉状態を更新する
  Or28676Logic.state.set({
    uniqueCpId: or28676_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 履歴ＩＤを親画面に返却する。
 *
 * @param newValue - 履歴ＩＤ
 */
async function handleReturn(newValue: Or28676Type) {
  if (!newValue.rirekiId) {
    return
  }
  //GUI00941_月間・年間表複写画面をポップアップで起動する TODO
  const ret: MonthlyYearlyTableCopyReturnSelectOutEntity = await ScreenRepository.select(
    'monthlyYearlyTableCopyReturnSelect',
    { rirekiId: newValue.rirekiId } as MonthlyYearlyTableCopyReturnSelectInEntity
  )

  //複写用情報を本画面に設定する。
  const rirekiObj = ret.data.rirekiObj
  localComponents.or15845 = ret.data.dataList.map((item) => ({
    mm: { value: item.mm, unit: Or27263Const.UNIT_MONTH },
    naiyoKnj: { value: item.naiyoKnj },
    mokutekiKnj: { value: item.mokutekiKnj },
    seq: item.seq,
    /** ＩＤ */
    id: '0',
    /** 更新回数 */
    modifiedCnt: '0',
  }))
  //月間・年間表_詳細リスト
  Or15845Logic.data.set({ uniqueCpId: or15845_1.value.uniqueCpId, value: localComponents.or15845 })
  //総合支援視点
  Or15846Logic.data.set({
    uniqueCpId: or15846_1.value.uniqueCpId,
    value: { value: rirekiObj.sogoShitenKnj },
  })
  //改訂フラグ
  local.or27263.rirekiObj.kaiteiFlg = rirekiObj.kaiteiFlg
}

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  return useScreenStore().isEditNavControl()
})
</script>

<template>
  <c-v-sheet class="view">
    <!-- Or11871：有機体：画面メニューエリア -->
    <g-base-or11871 v-bind="or11871">
      <template #createItems>
        <!--複写ボタン-->
        <c-v-list-item
          :title="t('btn.copy')"
          @click="onCopyClick"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.care-plan2-copy-btn')"
          />
        </c-v-list-item>
      </template>
      <template #optionMenuItems>
        <c-v-list-item
          :title="t('label.pattern')"
          prepend-icon="open_in_browser"
          @click="_pattern()"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.pattern')"
          />
        </c-v-list-item>
      </template>
    </g-base-or11871>
    <c-v-row
      no-gutters
      class="content-area"
    >
      <c-v-col
        cols="2"
        class="hidden-scroll h-100"
      >
        <!-- （利用者基本）利用者選択の表示 -->
        <g-base-or00248 v-bind="or00248"></g-base-or00248>
      </c-v-col>
      <c-v-col class="hidden-scroll h-100 ml-4">
        <!-- コンテンツエリア -->
        <c-v-container class="d-flex flex-column h-100 pr-0">
          <c-v-row>
            <c-v-col
              style="padding-top: 8px; padding-bottom: 0px; padding-right: 8px; padding-left: 8px"
            >
              <!-- 事業所 -->
              <g-base-or-41179 v-bind="or41179_1" />
            </c-v-col>
          </c-v-row>
          <c-v-row class="second-row">
            <c-v-col
              v-show="local.or27263.kikanFlg === '1'"
              cols="auto"
              style="padding-top: 8px; padding-bottom: 0px; padding-right: 8px; padding-left: 8px"
            >
              <!-- 計画対象期間 -->
              <g-custom-orX0007
                v-bind="orX0007_1"
                :oneway-model-value="localOneway.orX0007Oneway"
                :unique-cp-id="orX0007_1.uniqueCpId"
                :parent-method="_save"
              />
            </c-v-col>
            <c-v-col
              v-show="local.or27263.kikanObj?.pagingCnt !== '0'"
              cols="auto"
              style="padding-left: 8px; padding-top: 8px; padding-bottom: 0px; padding-right: 8px"
            >
              <!-- 履歴 -->
              <g-custom-orX0008
                v-bind="orX0008_1"
                :oneway-model-value="localOneway.orX0008Oneway"
                :unique-cp-id="orX0008_1.uniqueCpId"
                :parent-method="_save"
              />
            </c-v-col>
            <c-v-col
              v-show="local.or27263.kikanObj?.pagingCnt !== '0'"
              cols="auto"
              :style="{ width: '220px' }"
              style="padding-top: 8px; padding-bottom: 0px; padding-right: 8px; padding-left: 0px"
            >
              <!-- 作成者 -->
              <g-custom-orX0009
                v-bind="orX0009_1"
                :oneway-model-value="localOneway.orX0009Oneway"
                :unique-cp-id="orX0009_1.uniqueCpId"
              />
            </c-v-col>
            <c-v-col
              v-show="local.or27263.kikanObj?.pagingCnt !== '0'"
              cols="auto"
              style="padding-top: 8px; padding-bottom: 0px; padding-right: 8px; padding-left: 0px"
            >
              <!-- 作成日 -->
              <g-custom-orX0010
                v-bind="orX0010_1"
                :oneway-model-value="localOneway.orX0010Oneway"
                :unique-cp-id="orX0010_1.uniqueCpId"
              />
            </c-v-col>
          </c-v-row>
          <c-v-row
            v-show="
              local.or27263.kikanObj?.pagingCnt !== '0' &&
              local.or27263.operaFlg !== Or27263Const.OPERA_FLAG_3
            "
            class="three-row"
          >
            <c-v-col
              style="padding-top: 8px; padding-bottom: 0px; padding-right: 8px; padding-left: 8px"
            >
              <!-- ケース番号 -->
              <g-custom-or15843
                v-bind="or15843_1"
                :oneway-model-value="localOneway.or15843Oneway"
                :unique-cp-id="or15843_1.uniqueCpId"
              />
            </c-v-col>
            <c-v-col
              style="padding-top: 8px; padding-bottom: 0px; padding-right: 8px; padding-left: 0px"
            >
              <!-- 年度 -->
              <g-custom-or15852
                v-bind="or15852_1"
                :oneway-model-value="localOneway.or15852Oneway"
                :unique-cp-id="or15852_1.uniqueCpId"
              />
            </c-v-col>
          </c-v-row>
          <c-v-row>
            <c-v-divider class="divider-class" />
          </c-v-row>
          <c-v-row
            class="flex-1-1 h-100"
            style="min-height: 0"
          >
            <c-v-col
              v-if="
                local.or27263.kikanObj?.pagingCnt !== '0' &&
                local.or27263.operaFlg !== Or27263Const.OPERA_FLAG_3
              "
              cols="auto"
              class="py-0"
            >
              <!-- 月間行事 -->
              <g-custom-or-15845
                v-bind="or15845_1"
                :unique-cp-id="or15845_1.uniqueCpId"
              ></g-custom-or-15845>
            </c-v-col>
            <c-v-col
              v-if="
                local.or27263.kikanObj?.pagingCnt !== '0' &&
                local.or27263.operaFlg !== Or27263Const.OPERA_FLAG_3
              "
              cols="auto"
              class="py-0"
            >
              <!-- 年間行事 -->
              <g-custom-or-15846
                v-bind="or15846_1"
                :unique-cp-id="or15846_1.uniqueCpId"
              ></g-custom-or-15846>
            </c-v-col>
          </c-v-row>
        </c-v-container>
      </c-v-col>
    </c-v-row>

    <!-- Or00051：フッターエリア -->
    <g-base-or00051 v-bind="or00051" />
  </c-v-sheet>

  <!-- エラー確認ダイアログ -->
  <g-base-or21813 v-bind="or21813_1" />
  <!-- INFO 確認ダイアログ -->
  <g-base-or21814 v-bind="or21814_1" />

  <!-- パターンダイアログ画面 -->
  <g-custom-or-28630
    v-if="showDialogOr28630"
    v-bind="or28630_1"
    v-model="localComponents.or28630"
    :oneway-model-value="localOneway.or28630Oneway"
    :unique-cp-id="or28630_1.uniqueCpId"
  />
  <!--GUI00941_月間・年間表複写-->
  <g-custom-or-28676
    v-if="showDialogOr28676"
    v-bind="or28676_1"
    :oneway-model-value="localOneway.or28676Oneway"
    @update:model-value="handleReturn"
  />

  <!--GUI00944_印刷設定-->
  <g-custom-or-10464
    v-if="showDialogOr10464"
    v-bind="or10464_1"
    :oneway-model-value="localOneway.or10464Oneway"
  />
</template>

<style scoped lang="scss">
.divider-class {
  border-width: thin;
  margin: 16px 0 16px 8px;
}

.view {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: transparent;
}

.content-area {
  display: flex;
  flex-grow: 1;
  overflow: hidden;
  height: 100%;
}

.content {
  padding: 5px 0px;
  overflow-x: auto;
}
.second-row {
  margin-top: 0px;
  align-items: baseline;
  :deep(.v-sheet) {
    background-color: transparent !important;
  }
}
.three-row {
  margin-top: 0px;
  align-items: baseline;
  justify-content: flex-end;
  :deep(.v-sheet) {
    background-color: transparent !important;
  }
}
</style>
