<script setup lang="ts">
/**
 * GUI00902:有機体:印刷設定
 * GUI00902_印刷設定
 *
 * @description
 * 印刷設定
 *
 * <AUTHOR>
 */
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
  dateUtils
} from '#imports'
import { Or32462Const } from '~/components/custom-components/organisms/Or32462/Or32462.constants'
import { Or32462Logic } from '~/components/custom-components/organisms/Or32462/Or32462.logic'
import type { Or32462Param } from '~/components/custom-components/organisms/Or32462/Or32462.type'
const { convertDateToSeireki } = dateUtils()

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI00902'
// ルーティング
const routing = 'GUI00902/pinia'
// 画面物理名
const screenName = 'GUI00902'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const Or32462 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00902' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 子コンポーネントのユニークIDを設定する
// Or32462.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI00902',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or32462Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or32462Const.CP_ID(1)]: Or32462.value,
})

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  Or32462Logic.initialize(Or32462.value.uniqueCpId)
}

// ダイアログ表示フラグ
const showDialogOr32462 = computed(() => {
  // Or32462のダイアログ開閉状態
  return Or32462Logic.state.get(Or32462.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(Or32462)--期間管理フラグが「管理する」の場合
 *
 */
function onClickOr32462() {
  // Or32462のダイアログ開閉状態を更新する
  Or32462Logic.state.set({
    uniqueCpId: Or32462.value.uniqueCpId,
    state: {
      isOpen: true,
      param: {
        sectionName: 'アセスメントフェースシート',
        shokuinId: '1',
        svJigyoId: '1',
        userId: '41',
        tantoId: '1',
        kijunbi: convertDateToSeireki(undefined),
        historyId: '1',
        focusSettingInitial: ['や', 'ゆ', 'よ'],
        selectedUserCounter: '2'
      } as Or32462Param,
    },
  })
}
/**************************************************
 * コンポーネント固有処理
 **************************************************/
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr32462"
        >GUI00902_印刷設定</v-btn
      >
      <g-custom-or-32462
        v-if="showDialogOr32462"
        v-bind="Or32462"
      />
    </c-v-col>
  </c-v-row>
</template>
