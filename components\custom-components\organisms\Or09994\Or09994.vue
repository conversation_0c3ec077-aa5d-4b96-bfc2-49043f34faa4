<!-- eslint-disable @typescript-eslint/prefer-for-of -->
<script setup lang="ts">
/**
 * Or09994:有機体:モーダル（画面/特殊コンポーネント）
 * GUI00790_［表示順変更アセスメント］画面
 *
 * <AUTHOR>
 */
import {
  onMounted,
  reactive,
  watch,
  ref,
  computed,
  nextTick,
  type ComponentPublicInstance,
  onUnmounted,
} from 'vue'
import { useI18n } from 'vue-i18n'
import type { SortableEvent } from 'vue-draggable-plus'
import { Or09994Const } from './Or09994.constants'

import type { Or09994StateType, TableData } from './Or09994.type'
import { useScreenOneWayBind, useSetupChildProps, useValidation } from '#imports'
import type {
  Title,
  Or09994Type,
  Or09994OnewayType,
} from '~/types/cmn/business/components/Or09994Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'

import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import type { Mo01278Type } from '~/types/business/components/Mo01278Type'
import type { Mo01336OnewayType } from '~/types/business/components/Mo01336Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { CustomClass } from '~/types/CustomClassType'
import type {
  Mo01354Headers,
  Mo01354OnewayType,
  Mo01354Type,
} from '~/components/base-components/molecules/Mo01354/Mo01354Type'

const { t } = useI18n()
/**
 * 検証
 */
const validation = useValidation()

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or09994Type
  onewayModelValue: Or09994OnewayType
  uniqueCpId: string
}
/**
 * 引継情報を取得する
 */
const props = defineProps<Props>()

const localOneway = reactive({
  or09994: {
    ...props.onewayModelValue,
  },
  /**
   * 情報ダイアログ
   */
  mo00024Oneway: {
    width: '900px',
    height: '380px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or09994',
      toolbarTitle: t('label.display-order-modified-assessment'),
      toolbarName: 'Or09994ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
      cardTextClass: 'or09994Content',
    },
  } as Mo00024OnewayType,
  /**
   * 表用数値専用テキストフィールド
   */
  mo01278Oneway: {
    maxLength: '3',
    rules: [validation.integer],
    min: 1,
    max: 999,
  },
  mo01265OnewayModelValue: {
    btnLabel: t('btn.delete-display-order'),
    customClass: {
      outerClass: 'pl-2',
    },
    prependIcon: 'delete',
    disabled: false,
  } as Mo01265OnewayType,
  mo00043OneWay: {
    tabItems: [
      {
        id: 'change',
        title: t('label.display-order-modified'),
        tooltipText: t('label.display-order-modified'),
        tooltipLocation: 'bottom',
      },
      {
        id: 'move',
        title: t('label.display-order-move'),
        tooltipText: t('label.display-order-move'),
        tooltipLocation: 'bottom',
      },
    ],
  } as Mo00043OnewayType,
  mo00611OneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  mo00609OneWay: {
    btnLabel: t('btn.confirm'),
  } as Mo00609OnewayType,
})

const local = reactive({
  mo00043: { id: 'change' } as Mo00043Type,
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * 変数定義
 **************************************************/

const or21814 = ref({ uniqueCpId: '' }) // 確認ダイアログ

const mo00024 = ref<Mo00024Type>({
  isOpen: Or09994Const.DEFAULT.IS_OPEN,
})

/**
 * 表用双方向データ
 */
const or1354Data = ref<Mo01354Type>({
  values: {
    selectedRowId: '1',
    selectedRowIds: [],
    items: [],
    scrollToId: '',
  },
})

/**
 * 分子：表
 * 単方向バインドモデルのローカル変数
 */
const mo01354Oneway = ref<Mo01354OnewayType>({
  /** 初期値：ヘッダー情報 */
  headers: [
    {
      title: t('label.display-order'),
      key: 'sort',
      sortable: false,
      minWidth: '80px',
      required: false,
    } as Mo01354Headers,
    {
      title: t('label.diagnosis-name'),
      key: 'diagnosisName',
      sortable: false,
      required: false,
    } as Mo01354Headers,
    {
      title: t('label.disease-code'),
      key: 'diseaseCode',
      sortable: false,
      minWidth: '128px',
      required: false,
    } as Mo01354Headers,
    {
      title: t('label.ICD-CM-code'),
      key: 'icdcmCode',
      sortable: false,
      minWidth: '158px',
      required: false,
    } as Mo01354Headers,
  ],
  height: '170px',
  // Mo01354 表コンポーネントが提供する、デフォルトのヘッダーを利用しない（カスタマイズ可能になる）
  useDefaultHeader: false,
  // 行入替用のアイコンを表示
  showDragIndicatorFlg: false,
})
/**
 * 現在ドラッグ中のDOM
 */
const eventTarget = ref<SortableEvent>()
/**
 * 表示順のDOMデータ
 */
const itemRefs = new Map<string, HTMLElement>()

/**
 * 表示順変更テーブル情報
 */
const tableData = ref<TableData[]>([])

/**
 * ダイアログ表示フラグ
 */
const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 初期化
 */
const isInit = ref(false)
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or09994StateType>({
  cpId: Or09994Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or09994Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(1)]: or21814.value, // 確認ダイアログ
})

onMounted(async () => {
  // 確認ダイアログを初期化
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.no'),
    },
  })
  await init()
})

onUnmounted(() => {
  // DOM要素のクリア
  itemRefs.clear()
})

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      close()
    }
  }
)

/**
 * AC001_初期情報取得
 */
async function init() {
  await nextTick()
  // 「表示順変更」タブ初期表示
  // 取得した疾患一覧データを疾患一覧に設定する。
  tableData.value = []
  if (props.onewayModelValue.sortList && props.onewayModelValue.sortList.length > 0) {
    for (let i = 0; i < props.onewayModelValue.sortList.length; i++) {
      const item = {
        id: String(i),
        sort: {
          value: String(props.onewayModelValue.sortList[i].sort),
        } as Mo01278Type,
        diagnosisName: {
          value: props.onewayModelValue.sortList[i].diagnosisName,
          unit: '',
        } as Mo01337OnewayType,
        diseaseCode: {
          value: props.onewayModelValue.sortList[i].diseaseCode,
          unit: '',
          customClass: {
            outerClass: '',
            outerStyle: 'background: transparent;',
            labelClass: '',
            labelStyle: '',
            itemClass: 'icdcmCodeClass',
            itemStyle: '',
          } as CustomClass,
        } as Mo01336OnewayType,
        icdcmCode: {
          value:
            props.onewayModelValue.sortList[i].icdcmCode1 +
            ' . ' +
            props.onewayModelValue.sortList[i].icdcmCode2,
          unit: '',
          customClass: {
            outerClass: '',
            outerStyle: 'background: transparent;',
            labelClass: '',
            labelStyle: '',
            itemClass: 'icdcmCodeClass',
            itemStyle: '',
          } as CustomClass
        } as Mo01337OnewayType,
        copy: String(props.onewayModelValue.sortList[i].sort),
      } as TableData
      tableData.value.push(item)
    }
  }

  or1354Data.value.values.items = tableData.value
  // 現在のタブが変更画面でない場合、最初の行の内容を選択する
  if (local.mo00043.id !== 'change') {
    or1354Data.value.values.selectedRowId = or1354Data.value.values.items[0].id
  }

  // 初期化が完了したら、選択された行の位置にビューポートを移動する
  if (isInit.value) {
    await nextTick()
    or1354Data.value.values.scrollToId = or1354Data.value.values.selectedRowId
  }
}

/**
 * AC005_「表示順位削除」押下
 */
function sortDeleteClick() {
  // 表示順リスト表示順リスト内の順序列の内容をクリアする
  // リストのレコードを取得し、ループを使用してレコードの順序に基づいてソートする
  for (const item of tableData.value) {
    if (item.sort && 'value' in item.sort) {
      item.sort = {
        value: '',
      } as Mo01278Type
    }
  }
}

/**
 * AC002_「×ボタン」押下
 * AC007_「閉じボタン」押下
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  setState({ isOpen: false })
}

/**
 * AC008_「確定ボタン」押下
 * 「確定」ボタン押下
 */
function confirm() {
  // 表示されているタブ画面の表示順欄の値で疾患データ情報の内容をソートする
  // ※表示順欄に値のない行は順次に最後に移動する
  const respData: Or09994Type = {
    sortList: [],
  }

  if (tableData.value && tableData.value.length > 0) {
    const tempList = new Array<Title>()
    const noSortList = new Array<Title>()
    for (const item of tableData.value) {
      if (item.sort.value && parseInt(item.sort.value)) {
        const data: Title = {
          sort: parseInt(item.sort.value),
          diagnosisName: item.diagnosisName.value,
          diseaseCode: item.diseaseCode.value ?? undefined,
          icdcmCode1: item.icdcmCode.value ? item.icdcmCode.value.split(' . ')[0] : '',
          icdcmCode2: item.icdcmCode.value ? item.icdcmCode.value.split(' . ')[1] : '',
        }
        tempList.push(data)
      } else {
        const data: Title = {
          sort: 0,
          diagnosisName: item.diagnosisName.value,
          diseaseCode: item.diseaseCode.value ?? undefined,
          icdcmCode1: item.icdcmCode.value ? item.icdcmCode.value.split(' . ')[0] : '',
          icdcmCode2: item.icdcmCode.value ? item.icdcmCode.value.split(' . ')[1] : '',
        }
        noSortList.push(data)
      }
    }
    tempList.sort((a, b) => a.sort - b.sort)
    tempList.push(...noSortList)
    respData.sortList = tempList
  }
  // 返却情報.疾患データ情報 = ソート後の疾患データ情報の内容
  emit('update:modelValue', respData)
  // 本画面を閉じ、親画面に返却する。
  close()
}

/**
 * タブ切り替え--表示順な回復を示
 */
async function tabsClick() {
  if ('move' === local.mo00043.id) {
    mo01354Oneway.value.showDragIndicatorFlg = true
  } else {
    mo01354Oneway.value.showDragIndicatorFlg = false
  }
  for (const item of or1354Data.value.values.items) {
    if (item && 'move' === local.mo00043.id) {
      mo01354Oneway.value.showDragIndicatorFlg = true
      item.sort = {
        value: item.copy as string,
      } as Mo01278Type
    } else if (item && 'change' === local.mo00043.id) {
      mo01354Oneway.value.showDragIndicatorFlg = false
      const sortData = item.sort as Mo01278Type
      item.copy = sortData.value
    }
  }

  await init()
}

// DOM要素をMapに設定する
const setItemRef = (el: Element | ComponentPublicInstance | null, id: string) => {
  const elHtml: HTMLElement = el as HTMLElement
  if (el) {
    itemRefs.set(id, elHtml)
  }
}

/**
 * 「表示順テキストフィールド」クリック
 *
 * @param item - テーブルデータ
 */
function clickSort(item: TableData) {
  or1354Data.value.values.selectedRowId = item.id
  if (item.sort.value === '') {
    const notNullItems = tableData.value.filter((item) => item.sort.value !== '')
    let maxValue = 0
    // リストが空でないなら最大値を取得
    if (notNullItems.length > 0) {
      maxValue = Math.max(...notNullItems.map((i) => parseInt(i.sort.value)))
    }
    item.sort.value = maxValue + 1 + ''
  }
}

/**
 * ドラッグ終了後にダイアログを開いて移動するかどうかを確認する
 *
 * @param event - event
 */
const end = async (event: SortableEvent) => {
  if (event.newIndex === event.oldIndex) {
    return
  }
  eventTarget.value = event
  // 確認ダイアログを開く
  const dialogResult = await openConfirmDialog(
    t('message.i-cmn-10678', [String(event.oldIndex! + 1), String(event.newIndex! + 1)])
  )
  if (dialogResult === 'yes') {
    // 「はい」をクリックして移動を確認する
    drop(eventTarget.value)
    return
  }
  // キャンセルをクリックした後、位置をリセットする
  reset(eventTarget.value)
}

/**
 * ドラッグ終了時に表示順序の位置を変更する
 *
 * @param event - event
 */
const drop = (event: SortableEvent | undefined) => {
  if (!event) {
    return
  }
  let small
  let big
  // 下にドラッグする場合
  if (event.oldIndex! < event.newIndex!) {
    small = event.oldIndex!
    big = event.newIndex!
  }
  // 上にドラッグする場合
  else {
    small = event.newIndex!
    big = event.oldIndex!
  }
  // 古い位置のデータをバックアップする
  const oldData = tableData.value[event.oldIndex!]
  // 古い位置のデータを削除する。
  tableData.value.splice(event.oldIndex!, 1)
  // 新しい座標にデータを挿入する。
  tableData.value.splice(event.newIndex!, 0, oldData)
  // 古い座標と新しい座標の間の表示を小さい順に並べ替える。
  for (let index: number = small; index <= big; index++) {
    // それぞれの表示を+1する。
    tableData.value[index].sort.value = index + 1 + ''
  }
}

/**
 * ドラッグのリセット
 *
 * @param event - event
 */
const reset = (event: SortableEvent | undefined) => {
  if (!event) {
    return
  }
  // キャンセルをクリックした後、位置をリセットする
  const newData = or1354Data.value.values.items[event.newIndex!]
  or1354Data.value.values.items.splice(event.newIndex!, 1)
  or1354Data.value.values.items.splice(event.oldIndex!, 0, newData)
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no
 */
async function openConfirmDialog(paramDialogText: string): Promise<'yes' | 'no'> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = 'no' as 'yes' | 'no'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <base-mo00043
        v-model="local.mo00043"
        style="padding-left: 0 !important"
        :oneway-model-value="localOneway.mo00043OneWay"
        @click="tabsClick"
      >
      </base-mo00043>
      <!-- タブ switch -->
      <c-v-window v-model="local.mo00043.id">
        <c-v-window-item value="change">
          <!-- タブ：表示順変更 -->
          <c-v-card>
            <template #title>
              <base-mo01265
                :oneway-model-value="localOneway.mo01265OnewayModelValue"
                @click="sortDeleteClick"
              >
                <c-v-tooltip
                  activator="parent"
                  location="bottom"
                  :max-width="300"
                  :text="t('tooltip.display-order-delete-success')"
                  open-delay="200"
                />
              </base-mo01265>
            </template>
            <c-v-card-text class="w-auto flex-0-0 table-header">
              <!-- 分子：表 -->
              <base-mo-01354
                v-model="or1354Data"
                :oneway-model-value="mo01354Oneway"
                class="list-wrapper w-100"
                @end="end"
              >
                <!-- 表示順 -->
                <template #[`item.sort`]="{ item }">
                  <div
                    v-if="local.mo00043.id === 'change'"
                    :ref="(el) => setItemRef(el, item.id)"
                    class="h-32"
                  >
                    <base-mo01278
                      v-model="item.sort"
                      :oneway-model-value="localOneway.mo01278Oneway"
                      style="width: 80px;"
                      @click.stop="clickSort(item)"
                    />
                  </div>
                  <div
                    v-else
                    :ref="(el) => setItemRef(el, item.id)"
                    class="d-flex justify-end align-center number h-32 bold"
                  >
                    {{ item.sort.value }}
                  </div>
                </template>
                <!-- 診断名 -->
                <template #[`item.diagnosisName`]="{ item }">
                  <div class="tdClass">
                    <span class="overflowText">{{ item.diagnosisName.value }}</span>
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :max-width="String(500)"
                      :text="String(item.diagnosisName.value)"
                      open-delay="200"
                    />
                  </div>
                </template>
                <!-- 疾患コード -->
                <template #[`item.diseaseCode`]="{ item }">
                  <div class="tdClass">
                    <base-mo01336 :oneway-model-value="item.diseaseCode"></base-mo01336>
                  </div>
                </template>
                <!-- ICD-CMコード -->
                <template #[`item.icdcmCode`]="{ item }">
                  <div class="tdClass">
                    <base-mo01337 :oneway-model-value="item.icdcmCode"></base-mo01337>
                  </div>
                </template>
                <!-- ページングを非表示 -->
                <template #bottom />
              </base-mo-01354>
            </c-v-card-text>
          </c-v-card>
        </c-v-window-item>
        <c-v-window-item
          value="move"
          class="w-auto flex-0-0"
        >
          <!-- タブ：表示順移動 -->
          <c-v-card>
            <template #title>
              <base-mo01265
                :oneway-model-value="localOneway.mo01265OnewayModelValue"
                style="visibility: hidden"
                @click="sortDeleteClick"
              >
                <c-v-tooltip
                  activator="parent"
                  location="bottom"
                  :max-width="300"
                  :text="t('tooltip.display-order-delete-success')"
                  open-delay="200"
                />
              </base-mo01265>
            </template>
            <c-v-card-text class="w-auto flex-0-0 table-header">
              <!-- 分子：表 -->
              <base-mo-01354
                v-model="or1354Data"
                :oneway-model-value="mo01354Oneway"
                class="list-wrapper w-100"
                @end="end"
              >
                <!-- 表示順 -->
                <template #[`item.sort`]="{ item }">
                  <div
                    v-if="local.mo00043.id === 'change'"
                    :ref="(el) => setItemRef(el, item.id)"
                    class="h-32"
                  >
                    <base-mo01278
                      v-model="item.sort"
                      :oneway-model-value="localOneway.mo01278Oneway"
                      @click.stop="clickSort(item)"
                    />
                  </div>
                  <div
                    v-else
                    :ref="(el) => setItemRef(el, item.id)"
                    class="d-flex justify-end align-center number h-32 bold tdClass"
                  >
                    {{ item.sort.value }}
                  </div>
                </template>
                <!-- 診断名 -->
                <template #[`item.diagnosisName`]="{ item }">
                  <div class="tdClass">
                    <span class="overflowText">{{ item.diagnosisName.value }}</span>
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :max-width="String(500)"
                      :text="String(item.diagnosisName.value)"
                      open-delay="200"
                    />
                  </div>
                </template>
                <!-- 疾患コード -->
                <template #[`item.diseaseCode`]="{ item }">
                  <div class="tdClass">
                    <base-mo01336 :oneway-model-value="item.diseaseCode"></base-mo01336>
                  </div>
                </template>
                <!-- ICD-CMコード -->
                <template #[`item.icdcmCode`]="{ item }">
                  <div class="tdClass">
                    <base-mo01337 :oneway-model-value="item.icdcmCode"></base-mo01337>
                  </div>
                </template>
                <!-- ページングを非表示 -->
                <template #bottom />
              </base-mo-01354>
            </c-v-card-text>
          </c-v-card>
        </c-v-window-item>
      </c-v-window>
    </template>
    <!-- フッター -->
    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          class="mx-2"
          @click="close"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="300"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <base-mo00609
          v-bind="localOneway.mo00609OneWay"
          @click="confirm"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="300"
            :text="t('tooltip.confirm-btn')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
    <!-- Or21814:有機体:確認ダイアログ -->
    <g-base-or21814
      v-if="showDialogOr21814"
      v-bind="or21814"
    />
  </base-mo00024>
</template>

<style lang="scss">
.or09994Content {
  padding-left: 8px !important;
  padding-top: 0px !important;
  padding-right: 8px !important;
  padding-bottom: 8px !important;

  .icdcmCodeClass > .v-col {
    text-align: left !important;
  }
}
</style>
<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table.scss';

.bold {
  font-weight: bold;
}

:deep(th),
.d-flex {
  padding: 8px 16px !important;
  white-space: pre;
}

.h-32 {
  height: 32px;
}

.tdClass {
  padding-left: 16px;
  padding-right: 16px;
}

:deep(.v-card-item) {
  padding: 8px 0 !important;
}

:deep(.v-card-text) {
  padding: 0;
}

:deep(.v-row > .v-col) {
  width: 56px;
  text-align: center;
}

:deep(.full-width-field) {
  padding: 0 16px !important;
  text-align: right;
  font-weight: bold !important;
  width: 100%;
}

.overflowText {
  text-wrap: auto;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  line-clamp: 1;
  -webkit-line-clamp: 1;
}

:deep(.v-btn__content) {
  .v-icon--size-small {
    display: none;
  }
}

:deep(.full-cell) {
  padding: 1px 0 0 1px !important;
}
</style>
