<script setup lang="ts">
import { computed, nextTick, onMounted, onUnmounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'
import { Or54587Const } from '../Or54587/Or54587.constants'
import { Or54588Const } from '../Or54588/Or54588.constants'
import { Or10659Logic } from '../Or10659/Or10659.logic'
import { Or54591Const } from './Or54591.constants'
import type { Or54591StateType } from './Or54591.type'
import { Or54591Logic } from './Or54591.logic'
import { useScreenOneWayBind, useScreenStore, useScreenUtils, useSetupChildProps } from '#imports'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  MonitoringConfigureMasterInitSelectInEntity,
  MonitoringConfigureMasterInitSelectOutEntity,
  MonitoringConfigureMasterUpdateInEntity,
  MonitoringConfigureMasterUpdateOutEntity,
} from '~/repositories/cmn/entities/MonitoringConfigureMasterInitEntity'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Mo01282OnewayType } from '~/types/business/components/Mo01282Type'
import type {
  ItemInfo,
  Or54587OnewayType,
  Or54587Type,
} from '~/types/cmn/business/components/Or54587Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00040OnewayType, Mo00040Type } from '~/types/business/components/Mo00040Type'
import type { Mo01379OnewayType } from '~/types/business/components/Mo01379Type'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { Or54591OnewayType } from '~/types/cmn/business/components/Or54591Type'
import { ResBodyStatusCode } from '~/constants/api-constants'
import type { Or54588Type } from '~/types/cmn/business/components/Or54588Type'
import { UPDATE_KBN } from '~/constants/classification-constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import type { OrX0118OnewayType } from '~/types/cmn/business/components/OrX0118Type'

/**
 * Or54591:有機体:モニタリング設定マスタ タブ
 * GUI01220_モニタリング設定マスタ
 *
 * @description
 * モニタリング設定マスタ
 *
 * <AUTHOR>
 */

/************************************************
 * Props
 ************************************************/
interface Props {
  uniqueCpId: string
  parentUniqueCpId: string
  onewayModelValue: Or54591OnewayType
}
// 引継情報を取得する
const props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  return useScreenStore().isEditNavControl()
})
const { getChildCpBinds } = useScreenUtils()

// 帳票名プルダウンの一時value
const ledgerOldValue = {
  modelValue: '',
}

/** ローカルOneway */
const localOneway = reactive({
  /** 帳票名ラベル */
  mo01338LedgerLabelOneway: {
    // '帳票名'
    value: t('label.ledge-name'),
    valueFontWeight: 'bold',
  } as Mo01338OnewayType,
  /** セレクトフィールドoneway */
  mo00040Oneway: {
    showItemLabel: false,
    isRequired: false,
    items: [],
    width: '600px',
    hideDetails: true,
    itemTitle: 'titleKnj',
    itemValue: 'free1Id',
    customClass: new CustomClass({ labelClass: 'padding-bottom-none' }),
    itemLabelFontWeight: 'bold',
  } as Mo00040OnewayType,
  /** ラベル */
  mo01379Oneway: {
    value: t('label.all-common'),
  } as Mo01379OnewayType,
  // フォーカスのインデックス
  focusIdx: '',
  // モニタリング（上段）表oneway
  or54587: {
    // '■モニタリング'
    titleLable: {
      value: t('label.monitoring-block-1'),
      valueFontWeight: 'bold',
      customClass: new CustomClass({
        itemClass: 'table-height pt-2',
      }),
    },
    nameKnjDisabledFlg: true,
    inputKbnDisabledFlg: true,
    rendouKbnDisabledFlg: true,
    inputKbnOneway: { items: [], itemTitle: 'label', itemValue: 'value' } as Mo01282OnewayType,
    rendouKbnOneway: { items: [], itemTitle: 'label', itemValue: 'value' } as Mo01282OnewayType,
    focusIndex: '',
    hasError: false,
    currentFree1Id: '',
  } as Or54587OnewayType,
  // 総括（下段）表oneway
  or54587_1: {
    // '■総括'
    titleLable: {
      value: t('label.summary-block-2'),
      valueFontWeight: 'bold',
      customClass: new CustomClass({
        itemClass: 'table-height pt-2',
      }),
    },
    nameKnjDisabledFlg: true,
    inputKbnDisabledFlg: true,
    rendouKbnDisabledFlg: true,
    inputKbnOneway: { items: [], itemTitle: 'label', itemValue: 'value' } as Mo01282OnewayType,
    rendouKbnOneway: { items: [], itemTitle: 'label', itemValue: 'value' } as Mo01282OnewayType,
    focusIndex: '',
    hasError: false,
    currentFree1Id: '',
  } as Or54587OnewayType,
  or54591: {
    ...props.onewayModelValue,
  } as Or54591OnewayType,
  mo01338PreviewLabelOneway: {
    // '■プレビュー'
    value: t('label.issues-planning-block-2'),
    valueFontWeight: 'bold',
    customClass: new CustomClass({
      itemClass: 'preview-height pt-2',
    }),
  } as Mo01338OnewayType,
  // 帳票マスタ情報
  ledgerMasterInfo: {
    /**
     * 現在のマスタヘッダID
     */
    currentFree1Id: '',
    /**
     * 現在の帳票タイトル
     */
    currentTitleKnj: '',
    /**
     * 印刷文字サイズ
     */
    fontSize: '',
    /**
     * 固定様式区分
     */
    koteiKbn: '',
    /**
     * 行の分割数（上）
     */
    columnCount: '',
    /**
     * 行の分割数（下）
     */
    columnCount2: '',
    /**
     * モニタリング使用フラグ
     */
    monitoringUseFlg1: '',
    /**
     * 総括使用フラグ
     */
    summaryUseFlg2: '',
    /**
     * 入力値変更判定フラグ
     */
    inputChangeFlag: '',
  },
})

// 画面元データ
const originData = ref<Or54587Type>()
// 画面元データ
const originData_1 = ref<Or54587Type>()

/** ローカルTwoway */
const local = reactive({
  // モニタリング（上段）表
  or54587: {
    itemInfoList: [],
  } as Or54587Type,
  // 総括（下段）表
  or54587_1: {
    itemInfoList: [],
  } as Or54587Type,
  // プレビュー(上段)一覧
  or54588: {
    itemInfoList: [],
    rowCount: 0,
    fontSize: '',
  } as Or54588Type,
  // プレビュー(下段)一覧
  or54588_1: {
    itemInfoList: [],
    rowCount: 0,
    fontSize: '',
  } as Or54588Type,
  /** セレクトフィールドmodel */
  mo00040Type: {
    modelValue: '',
  } as Mo00040Type,
  // アイコンボタン
  mo00009Oneway: {
    btnIcon: 'edit_square',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
  } as Mo00009OnewayType,
  mo01338Fourway: {
    value: t('label.category-number'),
  } as Mo01338OnewayType,
})

// モニタリング（上段）表
const or54587 = ref({ uniqueCpId: '' })
// モニタリング（上段）表
const or54587_1 = ref({ uniqueCpId: '' })
// プレビュー(上段)一覧
const or54588 = ref({ uniqueCpId: '' })
// プレビュー(下段)一覧
const or54588_1 = ref({ uniqueCpId: '' })
// Or21813_有機体:エラーダイアログ
const or21813 = ref({ uniqueCpId: '' })
//Or21814_有機体:確認ダイアログ
const or21814 = ref({ uniqueCpId: '' })
// 開閉フラグ
const isClose = ref(false)

// エラーダイアログ表示フラグ
const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})
// 確認ダイアログ表示フラグ
const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  // 汎用コードマスタ取得
  await initCodes()
})
onUnmounted(() => {
  // 画面変更フラグ再設定
  resetEdit()
})

/**************************************************
 * Pinia
 **************************************************/
useSetupChildProps(props.uniqueCpId, {
  [Or21813Const.CP_ID(0)]: or21813.value,
  [Or54587Const.CP_ID(0)]: or54587.value,
  [Or54587Const.CP_ID(1)]: or54587_1.value,
  [Or54588Const.CP_ID(0)]: or54588.value,
  [Or54588Const.CP_ID(1)]: or54588_1.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
})

useScreenOneWayBind<Or54591StateType>({
  cpId: Or54591Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    param: (value) => {
      switch (value?.executeFlag) {
        // 保存
        case 'save':
          void save()
          break
        // データ再取得
        case 'getData':
          void getInitData()
          break
        default:
          break
      }
    },
  },
})

/**************************************************
 * 関数
 **************************************************/
/**
 * モニタリング（上段）表とプレビュー(上段)の更新
 *
 * @param val -評価表設定ダイヤログから戻るデータ
 */
function updateUpperWidth(val: OrX0118OnewayType) {
  val.list?.forEach((item, index) => {
    if (local.or54587.itemInfoList[index]) {
      local.or54587.itemInfoList[index].widthCnt.value = item.widthCnt
    }
    if (local.or54588.itemInfoList[index]) {
      local.or54588.itemInfoList[index].widthCnt = item.widthCnt.toString()
    }
  })
}

/**
 * モニタリング（下段）表とプレビュー(下段)の更新
 *
 * @param val -評価表設定ダイヤログから戻るデータ
 */
function updateLowerWidth(val: OrX0118OnewayType) {
  val.list?.forEach((item, index) => {
    if (local.or54587_1.itemInfoList[index]) {
      local.or54587_1.itemInfoList[index].widthCnt.value = item.widthCnt
    }
    if (local.or54588_1.itemInfoList[index]) {
      local.or54588_1.itemInfoList[index].widthCnt = item.widthCnt.toString()
    }
  })
}

/**
 * プレビュー(上段)タイトルの更新
 *
 * @param items -タイトルデータ
 */
function updateUpperTitle(item: { value: string; id: string }) {
  local.or54588.itemInfoList.forEach((itemInfo) => {
    if (itemInfo.id === item.id) {
      itemInfo.nameKnj.value = item.value
    }
  })
}

/**
 * プレビュー(下段)タイトルの更新
 *
 * @param items -タイトルデータ
 */
function updateLowerTitle(item: { value: string; id: string }) {
  local.or54588_1.itemInfoList.forEach((itemInfo) => {
    if (itemInfo.id === item.id) {
      itemInfo.nameKnj.value = item.value
    }
  })
}

/**
 * エラー行のフォーカスをリセット
 */
function resetFocus() {
  localOneway.focusIdx = ''
  localOneway.or54587.focusIndex = ''
  localOneway.or54587_1.focusIndex = ''
}

/**
 * 閉じるボタン押下_保存権限がない場合の開閉
 *
 * @param errormsg - Message
 */
function showOr21814MsgTwoBtn(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: errormsg,
      firstBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.no'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 閉じるボタン押下_保存権限がある場合の開閉
 *
 * @param errormsg - Message
 */
function showOr21814MsgThreeBtn(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト0
      dialogText: errormsg,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 確認ダイヤログを呼び出し
 */
const show21814Dialog = async () => {
  // 該当画面にデータが変更ありの場合
  if (isEdit.value) {
    if (!localOneway.or54591.editFlg) {
      // 変更がある場合、確認ダイアログを表示する。(保存権限がない場合)
      // メッセージ内容：情報を保存する権限がありません。入力内容は破棄されますが、処理を続けますか？
      showOr21814MsgTwoBtn(t('message.i-cmn-10006'))
    } else {
      // 変更がある場合、確認ダイアログを表示する。(保存権限がある場合)
      // 他の項目を選択すると、変更内容は失われます。「改行」変更を保存しますか？
      showOr21814MsgThreeBtn(t('message.i-cmn-10430'))
    }
    return new Promise(() => {
      watch(
        () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
        async () => {
          const event = Or21814Logic.event.get(or21814.value.uniqueCpId)
          // 編集権限なし
          if (!localOneway.or54591.editFlg) {
            // 'はい'
            if (event?.secondBtnClickFlg) {
              await getInitData()
            } else {
              // 帳票名プルダウンのmodelValue復元
              local.mo00040Type.modelValue = ledgerOldValue.modelValue
              return
            }
          } else {
            // 編集権限あり
            // 'はい'
            if (event?.firstBtnClickFlg) {
              // 保存
              await save()
            }
            // 'いいえ'
            else if (event?.secondBtnClickFlg) {
              await getInitData()
            }
            // '×'または'キャンセル'
            else {
              // 帳票名プルダウンのmodelValue復元
              local.mo00040Type.modelValue = ledgerOldValue.modelValue
              return
            }
          }
          Or21814Logic.event.set({
            uniqueCpId: or21814.value.uniqueCpId,
            events: {
              firstBtnClickFlg: false,
              secondBtnClickFlg: false,
              thirdBtnClickFlg: false,
            },
          })
        },
        { once: true }
      )
    })
  } else {
    await getInitData()
  }
}

/**
 * 保存ボタン押下_保存権限がある場合の開閉
 *
 * @param errormsg - Message
 */
function showOr21813Msg(errormsg: string) {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト0
      dialogText: errormsg,
      firstBtnType: 'normal1',
      firstBtnLabel: 'OK',
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })
  // エラーダイアログをオープン
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ITEM_INPUT },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_LINKAGE_ITEM },
  ]
  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  const inputKbnOnewayCode = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_ITEM_INPUT)
  const rendouKbnOnewayCode = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_LINKAGE_ITEM)
  // 項目入力値
  localOneway.or54587.inputKbnOneway.items = inputKbnOnewayCode
  // 連動項目
  localOneway.or54587.rendouKbnOneway.items = rendouKbnOnewayCode
  // 項目入力値
  localOneway.or54587_1.inputKbnOneway.items = inputKbnOnewayCode
  // 連動項目
  localOneway.or54587_1.rendouKbnOneway.items = rendouKbnOnewayCode.filter(
    (item) =>
      item.value === Or54587Const.DEFAULT.SELECT_CD_KBN_RENDOU_NONE ||
      item.value === Or54587Const.DEFAULT.SELECT_CD_KBN_RENDOU_COMPREHENSIVE_POLICY
  )
}

/**
 * 画面変更フラグの再設定
 */
const resetEdit = () => {
  useScreenStore().setCpNavControl({
    cpId: Or54587Const.CP_ID(0),
    uniqueCpId: or54587.value.uniqueCpId,
    editFlg: false,
  })
  useScreenStore().setCpNavControl({
    cpId: Or54587Const.CP_ID(1),
    uniqueCpId: or54587_1.value.uniqueCpId,
    editFlg: false,
  })
}

/**
 * 初期表示のデータ取得
 */
const getInitData = async () => {
  // input情報取得
  const inputData: MonitoringConfigureMasterInitSelectInEntity = {
    moniFilt: localOneway.or54591.moniFilt,
    svJigyoId: localOneway.or54591.svJigyoId,
    free1Id: local.mo00040Type.modelValue ?? '',
    yoshikiKbn: localOneway.or54591.yoshikiKbn,
  }
  // 初期情報取得
  const res: MonitoringConfigureMasterInitSelectOutEntity = await ScreenRepository.select(
    'monitoringConfigureMasterInitSelect',
    inputData
  )
  if (res.statusCode === ResBodyStatusCode.SUCCESS) {
    // 帳票名セレクトリスト取得
    localOneway.mo00040Oneway.items = res.data.ledgerNameSelectList
    // 帳票マスタ情報取得
    localOneway.ledgerMasterInfo = res.data.ledgerMasterInfo
    // 「帳票名セレクトフィールドのモデルを一時設定
    ledgerOldValue.modelValue = localOneway.ledgerMasterInfo.currentFree1Id
    // 「帳票名セレクトフィールドのモデルを設定
    local.mo00040Type.modelValue = localOneway.ledgerMasterInfo.currentFree1Id
    /** モニタリング（上段）表処理  */
    // モニタリング（上段）表リスト
    local.or54587.itemInfoList = []
    // プレビュー(上段)一覧リスト
    local.or54588.itemInfoList = []
    const monitoringItemInfoList = res.data.monitoringItemInfoList
    // 現在のマスタヘッダIDの設定
    localOneway.or54587.currentFree1Id = res.data.ledgerMasterInfo.currentFree1Id
    localOneway.or54587_1.currentFree1Id = res.data.ledgerMasterInfo.currentFree1Id
    for (let index = 0; index < monitoringItemInfoList.length; index++) {
      // 行の分割数（上）が7の場合、項目1～７までのみ表示
      if (Number(localOneway.ledgerMasterInfo.columnCount) === index) {
        break
      }
      local.or54587.itemInfoList.push({
        itemNum: {
          // '項目'+id
          value: t('label.item-1') + monitoringItemInfoList[index].koumokuId,
          unit: '',
          customClass: {
            itemStyle:
              'background: rgb(var(--v-theme-black-50));padding-left: 17px;height:32px;align-content:center',
            itemClass: 'item-num-class',
          } as CustomClass,
        },
        nameKnj: { value: monitoringItemInfoList[index].nameKnj },
        inputKbn: { modelValue: monitoringItemInfoList[index].inputKbn },
        rendouKbn: { modelValue: monitoringItemInfoList[index].rendouKbn },
        widthCnt: { value: Number(monitoringItemInfoList[index].widthCnt), unit: '' },
        updateKbn: '',
        modifiedCnt: '',
        id: (index + 1).toString(),
      })
      local.or54588.itemInfoList.push({
        nameKnj: {
          value: monitoringItemInfoList[index].nameKnj,
          unit: '',
          valueFontWeight: 'bold',
          customClass: {
            outerClass: 'mr-0',
          } as CustomClass,
        },
        widthCnt: monitoringItemInfoList[index].widthCnt,
        id: monitoringItemInfoList[index].koumokuId,
      })
      local.or54588.rowCount = 3
      local.or54588.fontSize = res.data.ledgerMasterInfo.fontSize
    }
    // モニタリング使用フラグ=「0:未使用」且つ、「帳票マスタ情報.固定様式区分=1or2」以外の場合：活性
    if (
      localOneway.ledgerMasterInfo.monitoringUseFlg1 === '0' &&
      localOneway.ledgerMasterInfo.koteiKbn !== '1' &&
      localOneway.ledgerMasterInfo.koteiKbn !== '2'
    ) {
      localOneway.or54587.nameKnjDisabledFlg = false
    } else {
      // 以外の場合：非活性
      localOneway.or54587.nameKnjDisabledFlg = true
    }
    // 以外の場合、活性
    if (localOneway.ledgerMasterInfo.monitoringUseFlg1 !== '1') {
      localOneway.or54587.inputKbnDisabledFlg = false
      localOneway.or54587.rendouKbnDisabledFlg = false
    } else {
      // モニタリング使用フラグは「1:使用中」の場合、非活性
      localOneway.or54587.inputKbnDisabledFlg = true
      localOneway.or54587.rendouKbnDisabledFlg = true
    }
    // コンポネントID設定
    localOneway.or54587.componentId = Or54587Const.DEFAULT.COMPONENT_ID_UPPER_TABLE
    /** 総括（下段）表処理 */
    // 総括（下段）表リスト
    local.or54587_1.itemInfoList = []
    // プレビュー(下段)一覧リスト
    local.or54588_1.itemInfoList = []
    const summaryItemInfoList = res.data.summaryItemInfoList
    for (let index = 0; index < summaryItemInfoList.length; index++) {
      // 行の分割数（下）が1の場合、項目1までのみ表示され
      if (Number(localOneway.ledgerMasterInfo.columnCount2) === index) {
        break
      }
      local.or54587_1.itemInfoList.push({
        itemNum: {
          // '項目'+id
          value: t('label.item-1') + summaryItemInfoList[index].koumokuId,
          unit: '',
          customClass: {
            itemStyle:
              'background: rgb(var(--v-theme-black-50));padding-left: 17px;height:32px;align-content:center',
            itemClass: 'item-num-class',
          } as CustomClass,
        },
        nameKnj: { value: summaryItemInfoList[index].nameKnj },
        inputKbn: { modelValue: summaryItemInfoList[index].inputKbn },
        rendouKbn: { modelValue: summaryItemInfoList[index].rendouKbn },
        widthCnt: { value: Number(summaryItemInfoList[index].widthCnt), unit: '' },
        updateKbn: '',
        modifiedCnt: '',
        id: summaryItemInfoList[index].koumokuId,
      })
      local.or54588_1.itemInfoList.push({
        nameKnj: {
          value: summaryItemInfoList[index].nameKnj,
          unit: '',
          valueFontWeight: 'bold',
          customClass: {
            outerClass: 'mr-0',
          } as CustomClass,
        },
        widthCnt: summaryItemInfoList[index].widthCnt,
        id: summaryItemInfoList[index].koumokuId,
      })
      local.or54588_1.rowCount = 2
      local.or54588_1.fontSize = res.data.ledgerMasterInfo.fontSize
    }
    // 総合使用フラグ=「0:未使用」且つ、「帳票マスタ情報.固定様式区分=1or2」以外の場合：活性
    if (
      localOneway.ledgerMasterInfo.summaryUseFlg2 === '0' &&
      localOneway.ledgerMasterInfo.koteiKbn !== '1' &&
      localOneway.ledgerMasterInfo.koteiKbn !== '2'
    ) {
      localOneway.or54587_1.nameKnjDisabledFlg = false
    } else {
      // 以外の場合：非活性
      localOneway.or54587_1.nameKnjDisabledFlg = true
    }
    // 非活性
    localOneway.or54587_1.inputKbnDisabledFlg = true
    // 以外の場合、活性
    if (localOneway.ledgerMasterInfo.summaryUseFlg2 !== '1') {
      localOneway.or54587_1.rendouKbnDisabledFlg = false
    } else {
      // モニタリング使用フラグは「1:使用中」の場合、非活性
      localOneway.or54587_1.rendouKbnDisabledFlg = true
    }
    // コンポネントID設定
    localOneway.or54587_1.componentId = Or54587Const.DEFAULT.COMPONENT_ID_LOWER_TABLE
    // 元データの一時保存
    originData.value = cloneDeep(local.or54587)
    originData_1.value = cloneDeep(local.or54587_1)
    await nextTick()
    // 画面変更フラグ再設定
    resetEdit()
  }
}

/**
 * エラーチェック
 *
 * @param itemInfos - 項目情報リスト
 *
 * @returns - エラー箇所のインデックス
 */
const checkError = (itemInfos: ItemInfo[]) => {
  let focusIdx = ''
  for (let index = 0; index < itemInfos.length; index++) {
    if (itemInfos[index].nameKnj.value === '') {
      focusIdx = index + ''
      showOr21813Msg(t('message.e-cmn-41723'))
      break
    }
  }
  return focusIdx
}

/**
 * 保存処理
 */
const save = async () => {
  localOneway.focusIdx = ''
  let linkageErrIdx = 0
  const childCpBindsData = getChildCpBinds(props.uniqueCpId, {
    // モニタリング（上段）表
    [Or54587Const.CP_ID(0)]: { cpPath: Or54587Const.CP_ID(0), twoWayFlg: true },
    // モニタリング（下段）表
    [Or54587Const.CP_ID(1)]: { cpPath: Or54587Const.CP_ID(1), twoWayFlg: true },
  })

  const or54587Data = childCpBindsData[Or54587Const.CP_ID(0)].twoWayBind?.value as Or54587Type
  const or54587Data_1 = childCpBindsData[Or54587Const.CP_ID(1)].twoWayBind?.value as Or54587Type
  // 「項目名（非固定項目）」空白チェック
  localOneway.focusIdx = checkError(or54587Data.itemInfoList)
  if (localOneway.focusIdx) {
    localOneway.or54587.hasError = true
    return
  }
  // 「項目名（非固定項目）」空白チェック
  localOneway.focusIdx = checkError(or54587Data_1.itemInfoList)
  if (localOneway.focusIdx) {
    localOneway.or54587_1.hasError = true
    return
  }
  // ※モニタリング（上段）表の連動項目チェック
  // 入力値が「マスタ」或は「マスタ＋文章」
  // かつ 連動項目が設定されていない 或は 連動の表示コード<300 或は 連動の表示コード>399 の場合
  linkageErrIdx = or54587Data.itemInfoList.findIndex(
    (item) =>
      (item.inputKbn.modelValue === '3' || item.inputKbn.modelValue === '4') &&
      (!item.rendouKbn.modelValue ||
        Number(item.rendouKbn.modelValue) < 300 ||
        Number(item.rendouKbn.modelValue) > 399)
  )
  if (linkageErrIdx > 0) {
    showOr21813Msg(t('message.e-cmn-40145'))
    return
  }
  // ※総括（下段）表の連動項目は固定値ですから、連動項目をチェックする必要がない

  // 入力値変更判定フラグ
  let inputChangeFlag = false
  // モニタリング（上段）表処理
  const monitoringItemInfoList = [] as {
    nameKnj: string
    inputKbn: string
    rendouKbn: string
    widthCnt: string
    updateKbn: string
    modifiedCnt: string
  }[]
  or54587Data.itemInfoList.forEach((item, index) => {
    let monitoringItemInfo = {
      nameKnj: '',
      inputKbn: '',
      rendouKbn: '',
      widthCnt: '',
      updateKbn: '',
      modifiedCnt: '',
    }
    monitoringItemInfo = {
      nameKnj: item.nameKnj.value,
      inputKbn: item.inputKbn.modelValue ?? '',
      rendouKbn: item.rendouKbn.modelValue ?? '',
      widthCnt: item.widthCnt.value?.toString() ?? '',
      updateKbn: item.updateKbn,
      modifiedCnt: item.modifiedCnt,
    }
    // 項目名と入力値と文字数変更
    if (
      item.nameKnj.value !== originData.value?.itemInfoList[index].nameKnj.value ||
      item.inputKbn.modelValue !== originData.value?.itemInfoList[index].inputKbn.modelValue ||
      item.widthCnt.value !== originData.value?.itemInfoList[index].widthCnt.value
    ) {
      monitoringItemInfo.updateKbn = UPDATE_KBN.UPDATE
    }
    // 「入力値（非固定項目）」値変更の場合
    if (item.inputKbn.modelValue !== originData.value?.itemInfoList[index].inputKbn.modelValue) {
      inputChangeFlag = true
    }
    // リスト追加
    monitoringItemInfoList.push(monitoringItemInfo)
  })
  // モニタリング（下段）表処理
  const summaryItemInfoList = [] as {
    nameKnj: string
    inputKbn: string
    rendouKbn: string
    widthCnt: string
    updateKbn: string
    modifiedCnt: string
  }[]
  or54587Data_1.itemInfoList.forEach((item, index) => {
    let summaryItemInfo = {
      nameKnj: '',
      inputKbn: '',
      rendouKbn: '',
      widthCnt: '',
      updateKbn: '',
      modifiedCnt: '',
    }
    summaryItemInfo = {
      nameKnj: item.nameKnj.value,
      inputKbn: item.inputKbn.modelValue ?? '',
      rendouKbn: item.rendouKbn.modelValue ?? '',
      widthCnt: item.widthCnt.value?.toString() ?? '',
      updateKbn: item.updateKbn,
      modifiedCnt: item.modifiedCnt,
    }
    // 項目名と入力値と文字数変更
    if (
      item.nameKnj.value !== originData_1.value?.itemInfoList[index].nameKnj.value ||
      item.inputKbn.modelValue !== originData_1.value?.itemInfoList[index].inputKbn.modelValue ||
      item.widthCnt.value !== originData_1.value?.itemInfoList[index].widthCnt.value
    ) {
      // 該当列の更新区分が'U'で設定する。
      summaryItemInfo.updateKbn = UPDATE_KBN.UPDATE
    }
    // 「入力値（非固定項目）」値変更の場合
    if (item.inputKbn.modelValue !== originData_1.value?.itemInfoList[index].inputKbn.modelValue) {
      inputChangeFlag = true
    }
    // リスト追加
    summaryItemInfoList.push(summaryItemInfo)
  })
  if (inputChangeFlag) {
    // 入力値変更判定フラグ=1
    localOneway.ledgerMasterInfo.inputChangeFlag = '1'
  }
  const param: MonitoringConfigureMasterUpdateInEntity = {
    ledgerMasterInfo: {
      inputChangeFlag: localOneway.ledgerMasterInfo.inputChangeFlag,
      currentFree1Id: localOneway.ledgerMasterInfo.currentFree1Id,
      columnCount: localOneway.ledgerMasterInfo.columnCount,
      columnCount2: localOneway.ledgerMasterInfo.columnCount2,
    },
    monitoringItemInfoList: monitoringItemInfoList,
    summaryItemInfoList: summaryItemInfoList,
  }
  // 情報保存
  const res: MonitoringConfigureMasterUpdateOutEntity = await ScreenRepository.update(
    'monitoringConfigureMasterUpdate',
    param
  )
  if (res.statusCode === ResBodyStatusCode.SUCCESS) {
    // 保存APIで返却した情報.更新前のチェックフラグ＝1の場合
    if (res.data.beforeUpdateFlag === '1') {
      // 保存エラーイベントを設定
      Or54591Logic.event.set({
        uniqueCpId: props.uniqueCpId,
        events: {
          saveErrorEventFlg: true,
        },
      })
      // エラー
      showOr21813Msg(t('message.e-cmn-40157'))
      // 帳票名プルダウンのmodelValue復元
      local.mo00040Type.modelValue = ledgerOldValue.modelValue
    } else {
      // 保存エラーイベントを設定
      Or54591Logic.event.set({
        uniqueCpId: props.uniqueCpId,
        events: {
          saveErrorEventFlg: false,
        },
      })
      if (isClose.value) {
        await nextTick()
        // 画面変更フラグ再設定
        resetEdit()
        // ダイヤログを閉じる
        Or10659Logic.state.set({
          uniqueCpId: props.parentUniqueCpId,
          state: {
            isOpen: false,
          },
        })
      } else {
        await getInitData()
      }
    }
  }
}

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * OnewayModelValueを監視
 */
watch(
  () => props.onewayModelValue,
  (newValue) => {
    localOneway.or54591 = {
      ...newValue,
    }
  },
  { immediate: true }
)

/**
 * 帳票名プルダウンの監視
 */
watch(
  () => local.mo00040Type.modelValue,
  async (newValue) => {
    if (!newValue) return
    if (newValue === ledgerOldValue.modelValue) return
    // 事業所選択から返却.事業所ID
    local.mo00040Type.modelValue = newValue
    await show21814Dialog()
  }
)

/**
 * Or21813のイベントを監視
 *
 * @description
 * またOr21813のボタン押下フラグをリセットする。
 */
watch(
  () => Or21813Logic.event.get(or21813.value.uniqueCpId),
  () => {
    if (localOneway.or54587.hasError) {
      localOneway.or54587.focusIndex = localOneway.focusIdx
    }
    if (localOneway.or54587_1.hasError) {
      localOneway.or54587_1.focusIndex = localOneway.focusIdx
    }
  }
)

// 親画面へメソッドを渡す
defineExpose({
  resetEdit,
})
</script>

<template>
  <div class="or54591Wrapper pb-2">
    <div class="d-flex pt-2 pl-2">
      <!-- 帳票名ラベル -->
      <base-mo01338 :oneway-model-value="localOneway.mo01338LedgerLabelOneway" />
      <!-- 帳票名セレクトフィールド -->
      <div class="mr-2 mb-2">
        <base-mo00040
          v-model="local.mo00040Type"
          :oneway-model-value="localOneway.mo00040Oneway"
        />
      </div>
      <div class="ml-2 pt-2">
        <!-- 説明:※全共通  -->
        <base-mo01379 :oneway-model-value="localOneway.mo01379Oneway" />
      </div>
    </div>
    <div class="pl-2"><c-v-divider /></div>
    <!-- モニタリング（上段）表  -->
    <g-custom-or-54587
      v-bind="or54587"
      v-model="local.or54587"
      :oneway-model-value="localOneway.or54587"
      :parent-unique-cp-id="props.uniqueCpId"
      @update:width="updateUpperWidth"
      @update:title="updateUpperTitle"
      @reset-focus-index="resetFocus"
    />
    <div class="pl-2 pt-2"><c-v-divider /></div>
    <!-- 総括（下段）表  -->
    <g-custom-or-54587
      v-bind="or54587_1"
      v-model="local.or54587_1"
      :oneway-model-value="localOneway.or54587_1"
      :parent-unique-cp-id="props.uniqueCpId"
      @update:width="updateLowerWidth"
      @update:title="updateLowerTitle"
      @reset-focus-index="resetFocus"
    />
    <div class="pl-2 pt-2"><c-v-divider /></div>
    <!-- 分子：ラベル -->
    <base-mo01338
      :oneway-model-value="localOneway.mo01338PreviewLabelOneway"
      class="pl-2 pb-2"
    />
    <div class="preview">
      <!-- プレビュー(上段)一覧 -->
      <g-custom-or-54588
        v-bind="or54588"
        v-model="local.or54588"
      />
      <!-- プレビュー(下段)一覧 -->
      <g-custom-or-54588
        v-bind="or54588_1"
        v-model="local.or54588_1"
      />
    </div>
  </div>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  />
  <!-- Or21813:有機体:エラーダイアログ -->
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  />
</template>

<style scoped lang="scss">
//@use '@/styles/cmn/mo-data-table-list.scss';
.or54591Wrapper {
  height: 720px;
  overflow: auto;
}
// テーブルタイトルのmargin
:deep(.ml-4) {
  margin-left: 0px !important;
}
.preview {
  width: 100%;
  overflow: auto;
}
:deep(.padding-bottom-none .v-col) {
  padding-bottom: 0px !important;
}
:deep(.preview-height .v-col) {
  height: 18px !important;
}
</style>
