import { Or05657Const } from './Or05657.constants'
import type { Or05657StateType } from './Or05657.type'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'

/**
 * Or05657:(見通し)注釈ダイアログ
 * GUI00916_見通し
 *
 * @description
 * 処理ロジック
 *
 * <AUTHOR> NGUYEN MANH HUNG
 */

export namespace Or05657Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or05657Const.CP_ID(0),
      uniqueCpId,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [],
    })
    // 子コンポーネントのセットアップ
    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }
  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or05657StateType>(Or05657Const.CP_ID(0))
}
