<script setup lang="ts">
/**
 * Or28169：有機体：(基本チェックリストヘッダ)履歴選択
 * GUI01080_基本チェックリスト
 *
 * @description
 * (基本チェックリストヘッダ)履歴選択
 *
 * <AUTHOR> NGUYEN VAN PHONG
 */
import { computed, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or26385Logic } from '../Or26385/Or26385.logic'
import { Or28169Const } from './Or28169.constants'
import { Or28169Logic } from './Or28169.logic'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CustomClass } from '~/types/CustomClassType'
import { useScreenTwoWayBind, useSetupChildProps } from '#imports'
import type { Or28169OnewayType, Or28169Type } from '~/types/cmn/business/components/Or28169Type'
import type { Or26385OnewayType, Or26385Type } from '~/types/cmn/business/components/Or26385Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or28169OnewayType
  uniqueCpId: string
}
const props = defineProps<Props>()
console.log('or28169or28169or28169', props)

/**************************************************
 * 変数定義
 **************************************************/
const createDataDataIndex = ref<number>(-1)
const { t } = useI18n()

const localOneway = reactive({
  Or28169Oneway: {
    ...props.onewayModelValue,
  } as Or28169OnewayType,
  // 履歴タイトルラベル
  mo00615Oneway: {
    itemLabel: t('label.history'),
    customClass: new CustomClass({ outerClass: 'mr-0', labelClass: 'ma-1' }),
  } as Mo00615OnewayType,
  // 履歴選択アイコンボタン
  mo00009Oneway: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  // 履歴-前へアイコンボタン
  mo00009Twoway: {
    // デフォルト値の設定
    btnIcon: 'chevron_left',
    density: 'compact',
  } as Mo00009OnewayType,
  // 履歴-ページング
  mo01338Oneway: {
    value: '1 / 1',
    valueFontWeight: 'true',
  } as Mo01338OnewayType,
  // 履歴-次へアイコンボタン
  mo00009Threeway: {
    // デフォルト値の設定
    btnIcon: 'chevron_right',
    density: 'compact',
  } as Mo00009OnewayType,
})

const or26385 = ref({ uniqueCpId: '' })
const or21814 = ref({ uniqueCpId: '' })
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(1)]: or21814.value,
})

/**************************************************
 * Pinia
 **************************************************/
useScreenTwoWayBind<Or28169Type>({
  cpId: Or28169Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
const or26385Data: Or26385OnewayType = {
  /**
   * 計画期間ID
   */
  sc1Id: '',
  /**
   * 委託先事業者ID
   */
  itkJigyoId: '',
  /**
   * 利用者ID
   */
  userid: '',
  /**
   * 履歴ID
   */
  rirekiId: '',
}

/**
 * 初期化の計画書_履歴データ更新の監視
 */
watch(
  () => props.onewayModelValue,
  (newValue) => {
    localOneway.Or28169Oneway = {
      ...newValue,
    }
    Or28169Logic.data.set({
      uniqueCpId: props.uniqueCpId,
      value: {
        historyId: newValue.rirekiId,
        historyUpateFlg: '',
      },
    })

    or26385Data.sc1Id = localOneway.Or28169Oneway.sc1Id
    or26385Data.itkJigyoId = localOneway.Or28169Oneway.itkJigyoId
    or26385Data.userid = localOneway.Or28169Oneway.userid
    or26385Data.rirekiId = localOneway.Or28169Oneway.rirekiId

    init()
  },
  { deep: true }
)

/**
 * 初期情報取得
 */
function init() {
  createDataDataIndex.value = Number(localOneway.Or28169Oneway.krirekiNo)
  localOneway.mo01338Oneway.value =
    localOneway.Or28169Oneway.krirekiNo + ' / ' + localOneway.Or28169Oneway.krirekiTotalCnt
}

/**
 *  履歴-前へアイコンボタンボタン押下時の処理
 */
function onClickMo00009Twoway() {
  if (createDataDataIndex.value === 1) {
    console.log('minnnn index')
    // 確認ダイアログを開く
    // 確認ダイアログを初期化
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: {
        dialogTitle: t('message.i-cmn-11263'),
        dialogText: '',
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        iconName: 'info',
        iconColor: 'rgb(var(--v-theme-blue-700))',
        iconBackgroundColor: 'rgb(var(--v-theme-blue-200))',
      },
    })
    return
  }

  //履歴変更区分1:選択している計画書IDの前
  Or28169Logic.data.set({
    uniqueCpId: props.uniqueCpId,
    value: {
      historyId: Or28169Logic.data.get(props.uniqueCpId)!.historyId,
      historyUpateFlg: Or28169Const.UPDATE_CATEGORY_PREVIOUS,
    },
  })
}

/**
 *  計画対象期間-計画対象期間-計画対象期間-前へアイコンボタンボタン押下時の処理
 */
function onClickMo00009Threeway() {
  if (createDataDataIndex.value >= Number(localOneway.Or28169Oneway.krirekiTotalCnt)) {
    console.log('maxxxxx index')
    // Or21814Logic.state.set({
    //   uniqueCpId: or21814.value.uniqueCpId,
    //   state: {
    //     isOpen: true,
    //     dialogText: t('message.i-cmn-11262'),
    //   },
    // })
    return
  }
  Or28169Logic.data.set({
    uniqueCpId: props.uniqueCpId,
    value: {
      historyId: Or28169Logic.data.get(props.uniqueCpId)!.historyId,
      historyUpateFlg: Or28169Const.UPDATE_CATEGORY_NEXT,
    },
  })
}

/**
 * ダイアログ表示フラグ
 */
const showDialogOr26385 = computed(() => {
  // Or26385のダイアログ開閉状態
  return Or26385Logic.state.get(or26385.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
const or26385Type = ref<Or26385Type>({})

/**
 *  ボタン押下時の処理
 */
function onClickOr26385() {
  Or26385Logic.state.set({
    uniqueCpId: or26385.value.uniqueCpId,
    state: { isOpen: true },
  })
}

watch(
  () => or26385Type.value,
  () => {
    //計画期間変更区分:0:選択先の期間ID
    Or28169Logic.data.set({
      uniqueCpId: props.uniqueCpId,
      value: {
        historyId: or26385Type.value.baseCheckListHistorySelected!.rirekiId,
        historyUpateFlg: Or28169Const.UPDATE_CATEGORY_SELECT,
      },
    })
  }
)
</script>

<template>
  <c-v-row
    no-gutters
    class="text-center"
  >
    <!--履歴タイトルラベル-->
    <c-v-col cols="auto">
      <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway" />
    </c-v-col>
    <!--履歴選択アイコンボタン-->
    <c-v-col
      cols="auto"
      align-self="center"
    >
      <base-mo00009
        :oneway-model-value="localOneway.mo00009Oneway"
        @click="onClickOr26385"
      />
      <g-custom-or-26385
        v-if="showDialogOr26385"
        v-bind="or26385"
        v-model="or26385Type"
        :oneway-model-value="or26385Data"
        :unique-cp-id="or26385.uniqueCpId"
      />
    </c-v-col>
    <!--履歴-前へアイコンボタン-->
    <c-v-col
      cols="auto"
      align-self="center"
    >
      <base-mo00009
        :oneway-model-value="localOneway.mo00009Twoway"
        @click="onClickMo00009Twoway"
      />
    </c-v-col>
    <!--履歴-ページング-->
    <c-v-col cols="auto">
      <base-mo01338 :oneway-model-value="localOneway.mo01338Oneway" />
    </c-v-col>
    <!--履歴-次へアイコンボタン-->
    <c-v-col
      cols="auto"
      align-self="center"
    >
      <base-mo00009
        :oneway-model-value="localOneway.mo00009Threeway"
        @click="onClickMo00009Threeway"
      />
    </c-v-col>
  </c-v-row>
  <!-- Or21814 確認ダイアログ -->
  <g-base-or21814 v-bind="or21814" />
</template>

<style lang="scss">
.text-center {
  align-items: baseline;
}
</style>
