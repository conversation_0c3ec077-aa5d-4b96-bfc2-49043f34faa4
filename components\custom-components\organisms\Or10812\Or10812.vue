<script setup lang="ts">
/**
 * Or10812:有機体:モーダル（画面/特殊コンポーネント）
 * GUI00808_［特記事項選択］画面
 *
 * <AUTHOR>
 */
import { onMounted, reactive, watch, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or10812Const } from './Or10812.constants'

import type { Or10812StateType, DataInfoDetailsType, TableDataType } from './Or10812.type'
import { useScreenOneWayBind, useSetupChildProps } from '#imports'
import type { Or10812Type, Or10812OnewayType } from '~/types/cmn/business/components/Or10812Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type {
  ISpecialNoteMatterSelectInEntity,
  ISpecialNoteMatterSelectOutEntity,
} from '~/repositories/cmn/entities/SpecialNoteMatterSelectEntity'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { Mo01280Type, Mo01280OnewayType } from '@/types/business/components/Mo01280Type'
import type { Mo00018Type, Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import type { Or21815StateType } from '~/components/base-components/organisms/Or21815/Or21815.type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or10812Type
  onewayModelValue: Or10812OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

// 子コンポーネント用変数
const or21815 = ref({ uniqueCpId: '' })

const defaultOnewayModelValue: Or10812OnewayType = {
  // 計画期間ID
  sc1Id: '',
  // アセスメントID
  gdlId: '',
  // 改訂フラグ
  revisionFlag: '',
}

const localOneway = reactive({
  or10812: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // 情報ダイアログ
  mo00024Oneway: {
    width: 'auto',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or10812',
      toolbarTitle: t('label.special-note-matter-select'),
      toolbarName: 'Or10812ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  mo00018OneWay: {
    name: t('label.include-item-name'),
    itemLabel: '',
    isVerticalLabel: false,
    showItemLabel: true,
  } as Mo00018OnewayType,
  includeItemName: t('label.include-item-name'),
  mo00611OneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  mo00609Oneway: {
    btnLabel: t('btn.add'),
  } as Mo00609OnewayType,
  mo00609OneWay: {
    btnLabel: t('btn.overwrite'),
  } as Mo00609OnewayType,
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or10812Const.DEFAULT.IS_OPEN,
})

// 行選択のデータ
const selectedData = ref<string[]>([])

// テーブルヘッダ
const tableHeaders = [
  { title: '', align: 'center', key: 'itemContent', width: '678px', sortable: false },
]

// 特記事項テーブル情報
const tableDatas = ref<TableDataType[]>([])

// 項目名を含める
const mo00018ModelValue = ref<Mo00018Type>({
  modelValue: false,
})

const respData: Or10812Type = {
  overwriteFlag: '',
  specialNoteMatterSelect: '',
}

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or10812StateType>({
  cpId: Or10812Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or10812Const.DEFAULT.IS_OPEN
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21815Const.CP_ID(0)]: or21815.value,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/

onMounted(async () => {
  await init()
})

/**
 * 警告ダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openWarningDialog = (uniqueCpId: string, state: Or21815StateType) => {
  Or21815Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(uniqueCpId)

        let result = Or10812Const.DEFAULT.STR_YES

        if (event?.firstBtnClickFlg) {
          result = Or10812Const.DEFAULT.STR_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or10812Const.DEFAULT.STR_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = Or10812Const.DEFAULT.STR_CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      close()
    }
  }
)

/**
 * AC001_初期情報取得
 */
async function init() {
    // バックエンドAPIから初期情報取得
    const inputData: ISpecialNoteMatterSelectInEntity = {
      sc1Id: localOneway.or10812.sc1Id,
      gdlId: localOneway.or10812.gdlId,
      revisionFlag: localOneway.or10812.revisionFlag,
    }
    const ret: ISpecialNoteMatterSelectOutEntity = await ScreenRepository.select(
      'specialNoteMatterSelect',
      inputData
    )

    const resData: DataInfoDetailsType = {
      ...ret.data,
    }
    if (resData?.operationInfoList) {
      for (const item of resData.operationInfoList) {
        if (item) {
          tableDatas.value?.push({
            id: item.itemId,
            titleModelValue: {
              value: item.itemTitle,
              unit: '',
            } as Mo01337OnewayType,
            itemContent: item.itemContents,
            itemContentModelValue: {
              value: item.itemContents,
            } as Mo01280Type,
            itemContentOnewayModelValue: {
              disabled: true,
              readonly: true,
            } as Mo01280OnewayType,
            checkboxLabel: '',
          })
        }
      }
    }
}

/**
 * AC002_「×ボタン」押下
 * AC007_「閉じボタン」押下
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  setState({ isOpen: false })
}

/**
 * 動的取得パターン
 *
 * @param selectedId - 行選択ID
 */
function getSelectedClass(selectedId: string) {
  if (selectedId) {
    if (selectedData.value.includes(selectedId)) {
      return 'customRow selected'
    } else {
      return 'customRow'
    }
  }
}

/**
 * リターンデータ取得
 *
 * @param type - 上書フラグ「0：追加  1:上書」
 */
function getRespDate(type: string) {
  for (const data of tableDatas.value) {
    for (const item of selectedData.value) {
      if (data.id === item) {
        if (respData.specialNoteMatterSelect) {
          respData.specialNoteMatterSelect = respData.specialNoteMatterSelect + Or10812Const.DEFAULT.STR_NEW_LINE_CHAR
          // ・「項目名を含める」が選択されない場合
          if (mo00018ModelValue.value.modelValue) {
            respData.specialNoteMatterSelect =
              respData.specialNoteMatterSelect +
              Or10812Const.DEFAULT.STR_NEW_LINE_CHAR +
              data.titleModelValue.value +
              Or10812Const.DEFAULT.STR_NEW_LINE_CHAR +
              data.itemContent
          } else {
            //・「項目名を含める」が選択された場合
            respData.specialNoteMatterSelect =
              respData.specialNoteMatterSelect + Or10812Const.DEFAULT.STR_NEW_LINE_CHAR + data.itemContent
          }
        } else {
          // ・「項目名を含める」が選択されない場合
          if (mo00018ModelValue.value.modelValue) {
            respData.specialNoteMatterSelect = data.titleModelValue.value + Or10812Const.DEFAULT.STR_NEW_LINE_CHAR + data.itemContent
          } else {
            //・「項目名を含める」が選択された場合
            respData.specialNoteMatterSelect = data.itemContent
          }
        }
      }
    }
  }

  respData.overwriteFlag = type
}

/**
 * AC008_「追加ボタン」押下
 * 「追加」ボタン押下
 */
async function add() {
  // 選択した特記事項の数量 > 0の場合
  if (selectedData.value.length > 0) {
    // 特記事項リストをループし、返却内容を準備する。
    getRespDate('0')

    emit('update:modelValue', respData)
    // 本画面を閉じ、親画面に返却する。
    close()
  }
  // 選択した特記事項の数量 = 0の場合
  else {
    // メッセージ
    const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.caution'),
      // ダイアログテキスト
      dialogText: t('message.w-cmn-20791'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })

    // はい
    if (Or10812Const.DEFAULT.STR_YES === dialogResult) {
      // 処理終了
      return
    }
  }
}

/**
 * AC009_「上書ボタン」押下
 * 「上書」ボタン押下
 */
async function book() {
  // 選択した特記事項の数量 > 0の場合
  if (selectedData.value.length > 0) {
    // 特記事項リストをループし、返却内容を準備する。
    getRespDate('1')

    emit('update:modelValue', respData)
    // 本画面を閉じ、親画面に返却する。
    close()
  }
  // 選択した特記事項の数量 = 0の場合
  else {
    // メッセージ
    const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.caution'),
      // ダイアログテキスト
      dialogText: t('message.w-cmn-20791'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })

    // はい
    if (Or10812Const.DEFAULT.STR_YES === dialogResult) {
      // 処理終了
      return
    }
  }
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row no-gutters>
        <c-v-col class="w-auto flex-0-0">
          <c-v-data-table
            v-model="selectedData"
            class="list-wrapper"
            fixed-header
            height="517px"
            :headers="tableHeaders"
            :items="tableDatas"
            show-select
            hide-default-footer
            :items-per-page="-1"
          >
            <template #[`item.itemContent`]="{ item }">
              <c-v-row
                no-gutter
                :class="getSelectedClass(item.id)"
              >
                <base-mo01337
                  class="pl-2 fontSize"
                  :oneway-model-value="item.titleModelValue"
                ></base-mo01337>
              </c-v-row>
              <c-v-row class="overflowTd">
                <div>
                  <base-mo01280
                    class="itemContent"
                    :model-value="item.itemContentModelValue"
                    :oneway-model-value="item.itemContentOnewayModelValue"
                  >
                  </base-mo01280>
                  <c-v-tooltip
                    activator="parent"
                    location="bottom"
                    :max-width="600"
                    :text="item.itemContent"
                    open-delay="200"
                  ></c-v-tooltip>
                </div>
              </c-v-row>
            </template>
          </c-v-data-table>
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- 項目名を含める -->
      <div class="itemNameInclude">
        <base-mo00018
          v-model="mo00018ModelValue"
          :oneway-model-value="localOneway.mo00018OneWay"
        >
        </base-mo00018>
        <base-at-label
          :value="localOneway.includeItemName"
          class="itemNameIncludeLabel"
        ></base-at-label>
      </div>

      <!-- 閉じるボタン -->
      <base-mo00611
        v-bind="localOneway.mo00611OneWay"
        class="footer-btn"
        @click="close"
      >
        <c-v-tooltip
          :text="t('tooltip.screen-close')"
          activator="parent"
          location="top"
        ></c-v-tooltip>
      </base-mo00611>

      <!-- 上書ボタン -->
      <base-mo00609
        :oneway-model-value="localOneway.mo00609OneWay"
        class="footer-btn"
        @click="book()"
      >
        <c-v-tooltip
          :text="t('tooltip.overwrite')"
          activator="parent"
          location="top"
        ></c-v-tooltip>
      </base-mo00609>

      <!-- 追加ボタン -->
      <base-mo00609
        :oneway-model-value="localOneway.mo00609Oneway"
        @click="add()"
      >
        <c-v-tooltip
          :text="t('tooltip.add')"
          activator="parent"
          location="top"
        ></c-v-tooltip>
      </base-mo00609>
    </template>
  </base-mo00024>
  <!-- メッセージ 警告 -->
  <g-base-or-21815 v-bind="or21815" />
</template>
<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';

.list-wrapper :deep(.v-table__wrapper) {
  border-top: thin solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
}

.list-wrapper :deep(.v-table__wrapper th) {
  font-weight: bold;
  font-size: 14px;
  border-top: none !important;
}

.list-wrapper :deep(.v-table__wrapper th:first-child) {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
}

.list-wrapper :deep(.v-table__wrapper th) {
  background-color: rgb(var(--v-theme-black-100)) !important;
}

.list-wrapper :deep(.v-table__wrapper td) {
  border-right: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
  padding: 0;
  font-size: 14px;
}

.list-wrapper :deep(.v-table__wrapper td:first-child) {
  border-left: 1px rgb(var(--v-theme-black-200)) solid !important;
}

// START--AC003_「表頭列チェックボックス」、AC004_行選択イベント--START
:deep(.v-data-table__tr > td:has(.selected)) {
  background-color: rgb(var(--v-theme-blue-100)) !important;

  .selected {
    background-color: rgb(var(--v-theme-blue-100)) !important;
  }
}

:deep(.v-data-table__tr:has(.selected)) {
  background-color: rgb(var(--v-theme-blue-100)) !important;
}
// END--AC003_「表頭列チェックボックス」、AC004_行選択イベント--END

:deep(.full-width-field) {
  height: 86px !important;
  font-size: 14px;
  border-top: thin solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
}

:deep(.v-selection-control__wrapper) {
  width: 100%;
}

:deep(.v-input__details) {
  display: none;
}

:deep(.fill) {
  color: rgb(var(--v-theme-key));
}

.footer-btn {
  margin-right: 8px;
}

.overflowTd {
  margin: 0 auto;
  width: 100%;
  padding: 0 !important;

  div {
    width: 100%;
    padding: 8px 0;
    height: 86px;
    box-sizing: border-box;
    position: relative;

    .itemContent {
      width: 100%;
      padding: 0 8px 0 16px;
      display: -webkit-box;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-box-orient: vertical;
      line-clamp: 4;
      -webkit-line-clamp: 4;
      position: absolute;
      top: 50%;
      transform: translate(0, -50%);
    }
  }
}

.itemNameInclude {
  margin-right: 72px;
  display: inline-flex;

  .itemNameIncludeLabel {
    line-height: 36px;
  }
}

.fontSize {
  font-size: 14px;
}

.customRow {
  align-content: center;
  background: #f2f2f2;
  height: 32px;
  padding: 8px 0px;
  margin: 0 auto;
}
</style>
