<script setup lang="ts">
/**
 * GUI01109_入力支援【目標とする生活（１年）】
 * Or10883: （目標とする生活（１年））入力支援ダイアログ
 *
 * @description
 * （目標とする生活（１年））入力支援ダイアログ
 *
 * <AUTHOR> LE TIEN HOAN
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import type { inputSupportItem, TableData } from '../Or10883/Or10883.type'
import { Or00232Logic } from '../Or00232/Or00232.logic'
import { Or00232Const } from '../Or00232/Or00232.constants'
import { Or26663Const } from '../Or26663/Or26663.constants'
import { Or10883Const } from './Or10883.constants'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type {
  Or10883OnewayType,
  Or10883TwowayType,
} from '~/types/cmn/business/components/Or10883Type'
import { useScreenOneWayBind, useSetupChildProps, useSystemCommonsStore } from '#imports'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  TargetLifeSelectInEntity,
  TargetLifeSelectOutEntity,
} from '~/repositories/cmn/entities/TargetLifeSelectEntity'
import type {
  TargetLifeSearchSelectInEntity,
  TargetLifeSearchSelectOutEntity,
} from '~/repositories/cmn/entities/TargetLifeSearchSelectEntity'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo00040OnewayType } from '~/types/business/components/Mo00040Type'
import type {
  RegainTargetLifeSelectInEntity,
  RegainTargetLifeSelectOutEntity,
} from '~/repositories/cmn/entities/RegainTargetLifeSelectEntity'
import type {
  OverwriteFlagUpdateInEntity,
  OverwriteFlagUpdateOutEntity,
} from '~/repositories/cmn/entities/OverwriteFlagUpdateEntity'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import type { Or00232OnewayType } from '~/types/cmn/business/components/Or00232Type'
import { DIALOG_BTN } from '~/constants/classification-constants'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import { Or26663Logic } from '~/components/custom-components/organisms/Or26663/Or26663.logic'
import type { Or26663OnewayType } from '~/types/cmn/business/components/Or26663Type'
import type {
  InputSupportGoalAndLife1YearScreenRedisplaySelectInEntity,
  InputSupportGoalAndLife1YearScreenRedisplaySelectOutEntity,
} from '~/repositories/cmn/entities/InputSupportGoalAndLife1YearScreenRedisplaySelectEntity'

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or10883TwowayType
  onewayModelValue: Or10883OnewayType
  uniqueCpId: string
}

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

// 引継情報を取得する
const props = defineProps<Props>()

const columnMinWidth = ref<number[]>([520])

/**
 * システム共有情報ストア
 */
const systemCommonsStore = useSystemCommonsStore()

// 選択した行のindex
const selectedItemIndex = ref<number>(-1)

// 変数.古処理モード
const oldSyoriMode = ref('')

const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(1) })
const or21815 = ref({ uniqueCpId: Or21815Const.CP_ID(1) })
const or00232 = ref({ uniqueCpId: Or00232Const.CP_ID(1) })
const or10883 = ref({ uniqueCpId: '', editCpNo: '' })
const or26663 = ref({ uniqueCpId: Or26663Const.CP_ID(1) })

// 【画面情報】.全て検索モード
const allsearchMode = ref('')

const detailNaiyo = ref('')

const mo00039 = ref('')

const Or00232OnewayModel: Or00232OnewayType = {
  t1Cd: '0',
  t2Cd: '0',
  t3Cd: '0',
  inputContents: '',
}
const Or26663OnewayModel: Or26663OnewayType = {
  bunruiId: '1',
  mode: '1',
  houshiki: '1',
}

const localOneway = reactive({
  Or10883: {
    ...props.onewayModelValue,
  },
  mo00024Oneway: {
    width: '550px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or10883',
      toolbarTitle: `${t('label.input-support')}【${props.onewayModelValue.title}】`,
      toolbarName: 'Or10883ToolBar',
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.close-btn'),
  } as Mo00611OnewayType,
  mo00609ConfirmOneway: {
    btnLabel: t('btn.confirm'),
    tooltipLocation: 'bottom',
    tooltipText: t('tooltip.confirm-btn'),
  } as Mo00609OnewayType,
  mo00043OneWay: {
    tabItems: [
      {
        id: Or10883Const.TAB_ID.CARE_MANAGER_PREVENTION,
        title: t('label.care-manager-prevention'),
      },
      { id: Or10883Const.TAB_ID.PAST_HISTORY, title: t('label.past-history') },
      { id: Or10883Const.TAB_ID.COMMON, title: t('label.common') },
    ],
  } as Mo00043OnewayType,
  mo00043OneWayCommon: {
    tabItems: [
      {
        id: Or10883Const.TAB_ID_COMMON.PERSON_ONLY,
        title: t('label.person-only'),
      },
      { id: Or10883Const.TAB_ID_COMMON.ALL, title: t('label.all-tab') },
    ],
  } as Mo00043OnewayType,
  mo00045Oneway: {
    /** 項目ラベルを表示するかどうか */
    showItemLabel: false,
    /** ラベルを縦に表示するかどうか */
    isVerticalLabel: false,
    /** 必須項目かどうか */
    isRequired: false,
    /** 詳細を非表示にするかどうか */
    hideDetails: true,
    /** 入力フィールドの幅 */
    width: '260',
    customClass: new CustomClass({}),
  } as Mo00045OnewayType,
  btnSearchOneway: {
    /** 検索ボタンのラベル */
    btnLabel: t('btn.search'),
    prependIcon: 'search',
  },
  mo00039Oneway: {
    name: t('label.delimiter'),
    itemLabel: '',
    showItemLabel: false,
    inline: true,
    customClass: {
      outerClass: 'radio-item1',
    } as CustomClass,
  } as Mo00039OnewayType,
  mo00009Database: {
    density: 'compact',
    rounded: 'sm',
    size: 'medium',
    btnIcon: 'database',
    tooltipLocation: 'bottom',
    tooltipText: t('tooltip.others-function'),
  },
  mo00615Oneway: {
    itemLabel: t('label.display-hierarchy'),
    showItemLabel: true,
    showRequiredLabel: false,
    itemLabelFontWeight: 'normal',
    customClass: new CustomClass({
      outerClass: '',
      itemClass: '',
    }),
  } as Mo00615OnewayType,
  mo00040Oneway: {
    itemTitle: 'bunruiName',
    itemValue: 'bunruiId',
    showItemLabel: false,
    itemLabelFontWeight: 'bold',
    width: '160px',
    hideDetails: true,
    items: [],
  } as Mo00040OnewayType,
  mo00009OnewayConsulationRouteIcon: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,
})

const local = reactive({
  mo00043: { id: Or10883Const.TAB_ID.CARE_MANAGER_PREVENTION } as Mo00043Type,
  mo00043Common: { id: Or10883Const.TAB_ID_COMMON.PERSON_ONLY } as Mo00043Type,
  mo000040: {
    modelValue: '',
  },
})

const tableData = ref<TableData>({
  inputSupportCareList: [],
  inputSupportRirekiList: [],
  inputSupportComList: [],
  bunruiInfoList: [],
})

const mo00045 = ref<Mo00045Type>({ value: '' })

const mo00024 = ref<Mo00024Type>({
  isOpen: Or10883Const.DEFAULT.IS_OPEN,
})

const radioItems = ref([] as CodeType[])

const headersSupportCare = [
  {
    title: t('label.care-manager-prevention-sentence-master-contents'),
    key: 'naiyo',
    sortable: false,
    width: '350',
  },
]

const headersRirek = [
  {
    title: t('label.past-history-display'),
    key: 'naiyo',
    sortable: false,
    width: '350',
  },
]

const headersCom = [
  {
    title: t('label.sentence-master-common-contents'),
    key: 'naiyo',
    sortable: false,
    width: '350',
  },
]

const headersDetailDisplay = [
  {
    title: t('label.detail-display'),
    key: 'naiyo',
    sortable: false,
    width: '350',
  },
]

const isShowDialogOr00232 = computed(() => {
  return Or00232Logic.state.get(or00232.value.uniqueCpId)?.isOpen ?? false
})
const isShowDialogOr26663 = computed(() => {
  return Or26663Logic.state.get(or26663.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * Pinia
 **************************************************/
/**
 * Piniaストアの設定
 * - ダイアログの開閉状態を管理
 * - uniqueCpIdを使用して一意の状態を識別
 */
const { setState } = useScreenOneWayBind({
  cpId: Or10883Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value?: boolean) => {
      mo00024.value.isOpen = value ?? Or10883Const.DEFAULT.IS_OPEN
    },
  },
})

onMounted(async () => {
  await initCodes()
  await getTargetLifeList()
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(1)]: or21814.value,
  [Or21815Const.CP_ID(1)]: or21815.value,
  [Or00232Const.CP_ID(1)]: or00232.value,
})

function handleOpenConsultationRouteModal() {
  // AC024
  // GUI01109 入力支援【相談経路】画面をポップアップで起動する。
  // Or10883OnewayModel.inputContents = local.value.data.consulationRouteOthers.value
  // or10883.value.editCpNo = 'consulationRouteOthers'
  Or26663Logic.state.set({
    uniqueCpId: or26663.value.uniqueCpId,
    state: { isOpen: true },
  })
}
/**
 * 初期情報取得
 */
async function getTargetLifeList() {
  const inputData: TargetLifeSelectInEntity = {
    // 職員ID
    shokuinId: systemCommonsStore.getStaffId!,
    // 大分類ＣＤ
    t1Cd: localOneway.Or10883.t1Cd,
    // 中分類ＣＤ
    t2Cd: localOneway.Or10883.t2Cd,
    // 小分類ＣＤ
    t3Cd: localOneway.Or10883.t3Cd,
    // 過去履歴用テーブル名
    tableName: localOneway.Or10883.historyTableName,
    // 項目名
    columnName: localOneway.Or10883.historyColumnName,
    // 利用者ID
    userId: localOneway.Or10883.userId,
    // システムコード
    sysCd: systemCommonsStore.getSystemCode!,
    // システム区分
    sysCdKbn: systemCommonsStore.getSystemCategory!,
    // 検索区分
    searchKbn: '',
    // 上書きフラグ
    uwagakiFlg: null,
    // 分類ID
    bunruiIdCom: '',
    // 検索内容
    selNaiyo: '',
    // 古処理モード
    oldSyoriMode: '',
    // 新処理モード
    newSyoriMode: '',
    // タブインデックス
    tabIndex: '',
    // パラメータ
    param: '',
  }

  // バックエンドAPIから初期情報取得
  const ret: TargetLifeSelectOutEntity = await ScreenRepository.select(
    'inputSupportGoalAndLife1YearScreenAcquireSelect',
    inputData
  )
  tableData.value.inputSupportCareList = ret.data.inputSupportCareList

  if (tableData.value.inputSupportCareList.length > 0)
    selectRow(0, tableData.value.inputSupportCareList[0])
}

/**
 * 再表示
 */
async function getRegainTargetLifeList() {
  // バックエンドAPIから初期情報取得
  const ret: RegainTargetLifeSelectOutEntity = await ScreenRepository.select(
    'inputSupportGoalAndLife1YearScreenAcquireRedisplaySelect',
    {}
  )

  //TODO:
  // allsearchMode.value = ret.data.allsearchMode

  // // 【画面情報】.検索区分 = 1:全ての場合
  // // 上記取得したOUTPUT情報.全て検索モード = 1の場合、
  // if (
  //   local.mo00043Common.id === Or10883Const.TAB_ID_COMMON.ALL &&
  //   ret.data.allsearchMode === Or10883Const.DEFAULT.allsearchMode
  // ) {
  //   const dialogResult = await showOr21814Msg(t('message.i-cmn-11425'))
  //   switch (dialogResult) {
  //     case DIALOG_BTN.YES: {
  //       break
  //     }
  //   }
  //   return
  // }

  // tableData.value.inputSupportCareList = ret.data.inputSupportCareList

  // if (tableData.value.inputSupportCareList.length > 0)
  //   selectRow(0, tableData.value.inputSupportCareList[0])

  // tableData.value.inputSupportRirekiList = ret.data.inputSupportRirekiList
  // tableData.value.inputSupportComList = ret.data.inputSupportComList
  // localOneway.mo00040Oneway.items = ret.data.bunruiComInfoList
}

/**
 * メッセージの開閉
 *
 * @param text - 表示メッセージ
 *
 * @returns ダイアログの選択結果（yes)
 */
async function showOr21814Msg(text: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: text,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = DIALOG_BTN.YES

        if (event?.firstBtnClickFlg) {
          result = DIALOG_BTN.YES
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 警告ダイアログの開閉
 *
 * @param text - 表示メッセージ
 *
 *  @returns ダイアログの選択結果（yes)
 */
async function showOr21815Msg(text: string) {
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      dialogTitle: t('label.warning'),
      // ダイアログテキスト
      dialogText: text,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  // 確認ダイアログをオープン
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815.value.uniqueCpId)

        let result = DIALOG_BTN.YES

        if (event?.firstBtnClickFlg) {
          result = DIALOG_BTN.YES
        }

        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: or21815.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 値が変更されたら更新する
 */
async function updateModelValueMo00040() {
  await getRegainTargetLifeList()
}

/**
 * 汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 回数区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_OVERWRITE_FLAG },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  // 回数区分
  radioItems.value = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_OVERWRITE_FLAG)

  mo00039.value = Or10883Const.RADIO.OVERWRITE
}

/**
 * 検索ボタンの押下処理
 */
async function onClickSearchBtn() {
  // 画面情報】.検索区分 = 1:全て AND 【画面情報】.全て検索モード = 1の場合、
  // 【画面情報】.検索内容入力しない場合、メッセージを表示する
  if (
    local.mo00043Common.id === Or10883Const.TAB_ID_COMMON.ALL &&
    allsearchMode.value === '1' &&
    !mo00045.value.value
  ) {
    const dialogResult = await showOr21814Msg(t('message.i-cmn-11424'))
    switch (dialogResult) {
      case DIALOG_BTN.YES: {
        break
      }
    }
    return
  }

  const inputData: TargetLifeSearchSelectInEntity = {
    // テーブル名
    tableNm: localOneway.Or10883.tableName ?? '',
    // 項目名
    columnNm: localOneway.Or10883.columnName ?? '',
  }

  // バックエンドAPIから初期情報取得
  const ret: TargetLifeSearchSelectOutEntity = await ScreenRepository.select(
    'inputSupportGoalAndLife1YearContentSelect',
    inputData
  )

  // 【画面情報】.検索区分 = 1:全て AND 【画面情報】.全て検索モード = 1の場合、
  // 【画面情報】.検索内容ある場合、
  // 上記取得したOUTPUT情報.検索内容リスト件数 > 10000件の場合
  if (
    local.mo00043Common.id === Or10883Const.TAB_ID_COMMON.ALL &&
    ret.data.columnNmList.length > Or10883Const.DEFAULT.LENGTH_10000
  ) {
    const dialogResult = await showOr21815Msg(t('message.w-cmn-20836'))
    switch (dialogResult) {
      case DIALOG_BTN.YES: {
        break
      }
    }
    return
  }

  selectRow(-1, undefined)

  // TODO:  chưa biết xử lý AC011-4 yêu cầu gì
}

/**
 * テーブルをダブルクリック
 *
 * @param index - 選択された行のインデックス
 *
 * @param item - 入力支援情報
 */
function handleDoubleClick(index: number, item: inputSupportItem) {
  selectedItemIndex.value = index
  detailNaiyo.value = item.naiyo
  confirm()
}

/**
 * 行を選択したときの処理
 *
 * @param index - 選択された行のインデックス
 *
 * @param item - 入力支援情報
 */
function selectRow(index: number, item?: inputSupportItem) {
  selectedItemIndex.value = index
  detailNaiyo.value = item?.naiyo ?? ''
}

/**
 * ［その他の機能］画面が表示される。
 */
async function clickOpenMaster() {
  if (
    local.mo00043.id === Or10883Const.TAB_ID.CARE_MANAGER_PREVENTION ||
    local.mo00043.id === Or10883Const.TAB_ID.PAST_HISTORY
  ) {
    // 【画面情報】.処理モード = 1 OR 2
    // GUI01087_「ケアマネ(予防)文章マスタ」画面を表示する。
    Or00232OnewayModel.inputContents = localOneway.Or10883.inputContents
    Or00232OnewayModel.t1Cd = localOneway.Or10883.t1Cd
    Or00232OnewayModel.t2Cd = localOneway.Or10883.t2Cd
    Or00232OnewayModel.t3Cd = localOneway.Or10883.t3Cd

    Or00232Logic.state.set({
      uniqueCpId: or00232.value.uniqueCpId,
      state: { isOpen: true },
    })
  } else {
    // 【画面情報】.処理モード = 3
    // TODO GUI00122_「文章入力補助」画面を表示する
    // 文章内容 : 親画面.文章内容
  }
  // マスタ画面閉じる、画面情報を再取得のため、下記のAPIを呼び出す。
  const inputData: InputSupportGoalAndLife1YearScreenRedisplaySelectInEntity = {
    shokuinId: systemCommonsStore.getStaffId!,
    sysCd: systemCommonsStore.getSystemCode!,
    sysCdKbn: '',
    userId: '',
    uwagakiFlg: mo00039.value,
    t1Cd: '',
    t2Cd: '',
    t3Cd: '',
    oldSyoriMode: '',
    newSyoriMode: '',
    tabIndex: '',
    searchKbn: '',
    tableName: '',
    columnName: '',
    bunruiIdCom: '',
    selNaiyo: '',
  }

  // バックエンドAPIから初期情報取得
  const ret: InputSupportGoalAndLife1YearScreenRedisplaySelectOutEntity =
    await ScreenRepository.select('inputSupportGoalAndLife1YearScreenRedisplaySelect', inputData)

    //TODO: chưa biết xử lý gì sau khi gọi API này, action AC005
}

/**
 * 閉じる
 */
async function close() {
  const inputData: OverwriteFlagUpdateInEntity = {
    // 利用者ID
    shokuinId: systemCommonsStore.getStaffId!,
    // システムコード
    sysCd: systemCommonsStore.getSystemCode!,
    // 上書きフラグ
    uwagakiFlg: mo00039.value,
    // 分類ID
    bunruiId: local.mo000040.modelValue,
    // 画面.処理モード
    shoriMode: local.mo00043.id,
  }

  // バックエンドAPIから初期情報取得
  await ScreenRepository.update('inputSupportGoalAndLife1YearScreenUpdate', inputData)
  setState({ isOpen: false })
}

/**
 * 確定時処理
 */
function confirm() {
  const respData: Or10883TwowayType = {
    confirmation: mo00039.value,
    naiyo: '',
  }
  if (mo00039.value === Or10883Const.RADIO.ADD) {
    // 返却情報.内容 = 【引継情報】.文章内容 + 【画面情報】.入力支援情報セクションの選択行.一覧_内容の内容
    respData.naiyo = localOneway.Or10883.inputContents + detailNaiyo.value
    localOneway.Or10883.inputContents = localOneway.Or10883.inputContents + detailNaiyo.value
  } else {
    // 返却情報.内容 = 【画面情報】.入力支援情報セクションの選択行.一覧_内容の内容
    respData.naiyo = detailNaiyo.value
    localOneway.Or10883.inputContents = detailNaiyo.value
  }
  emit('update:modelValue', respData)
}

/**
 * （ダイアログ）の開閉状態を監視
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    if (!newValue) {
      await close()
    }
  }
)

watch(
  () => Or26663Logic.state.get(or26663.value.uniqueCpId)?.isOpen,
  async (newValue, oldValue) => {
    // マスタ画面閉じる、画面情報を再取得のため、下記のAPIを呼び出す。
    console.log(newValue)
    console.log(oldValue)
    await getRegainTargetLifeList()
  }
)

/**
 * メニュー切替
 */
watch(
  () => local.mo00043.id,
  async (newValue, oldValue) => {
    oldSyoriMode.value = oldValue

    // 【画面情報】.検索内容をクリアする
    local.mo000040.modelValue = ''

    await getRegainTargetLifeList()

    selectRow(-1, undefined)

    if (
      local.mo00043.id === Or10883Const.TAB_ID.CARE_MANAGER_PREVENTION &&
      tableData.value.inputSupportCareList.length > 0
    ) {
      selectRow(0, tableData.value.inputSupportCareList[0])
    }

    if (local.mo00043.id === Or10883Const.TAB_ID.PAST_HISTORY) {
      mo00045.value.value = ''
      if (tableData.value.inputSupportRirekiList.length > 0) {
        selectRow(0, tableData.value.inputSupportRirekiList[0])
      }
    }

    if (
      local.mo00043.id === Or10883Const.TAB_ID.COMMON &&
      tableData.value.inputSupportComList.length > 0
    ) {
      selectRow(0, tableData.value.inputSupportComList[0])
    }
  }
)

// 検索区分切替、画面情報を再取得する。
watch(
  () => local.mo00043Common.id,
  async () => {
    // 【画面情報】.検索内容をクリアする
    local.mo000040.modelValue = ''

    // 処理モード変更する、画面情報を再取得のため、下記のAPIを呼び出す。
    await getRegainTargetLifeList()

    selectRow(-1, undefined)

    if (
      local.mo00043.id === Or10883Const.TAB_ID.CARE_MANAGER_PREVENTION &&
      tableData.value.inputSupportCareList.length > 0
    ) {
      selectRow(0, tableData.value.inputSupportCareList[0])
    }

    if (local.mo00043.id === Or10883Const.TAB_ID.PAST_HISTORY) {
      mo00045.value.value = ''
      if (tableData.value.inputSupportRirekiList.length > 0) {
        selectRow(0, tableData.value.inputSupportRirekiList[0])
      }
    }

    if (
      local.mo00043.id === Or10883Const.TAB_ID.COMMON &&
      tableData.value.inputSupportComList.length > 0
    ) {
      selectRow(0, tableData.value.inputSupportComList[0])
    }
  }
)

watch(
  () => Or00232Logic.state.get(or00232.value.uniqueCpId)?.isOpen,
  async () => {
    // マスタ画面閉じる、画面情報を再取得のため、下記のAPIを呼び出す。
    await getRegainTargetLifeList()
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row class="d-flex align-items-center">
        <div v-if="local.mo00043.id === Or10883Const.TAB_ID.COMMON">
          <base-mo-00615 :oneway-model-value="localOneway.mo00615Oneway" />
        </div>
        <base-mo00009
          v-if="local.mo00043.id === Or10883Const.TAB_ID.COMMON"
          :oneway-model-value="localOneway.mo00009OnewayConsulationRouteIcon"
          @click="handleOpenConsultationRouteModal"
        ></base-mo00009>
        <div v-if="local.mo00043.id === Or10883Const.TAB_ID.COMMON">
          <base-mo00040
            v-model="local.mo000040"
            :oneway-model-value="localOneway.mo00040Oneway"
            @update:model-value="updateModelValueMo00040()"
          />
        </div>
        <c-v-col
          cols="auto"
          class="d-flex ml-auto"
        >
          <base-mo00009
            :oneway-model-value="localOneway.mo00009Database"
            @click="clickOpenMaster()"
          />
        </c-v-col>
      </c-v-row>
      <hr class="v-divider v-theme--mainTheme" />
      <div class="d-flex border-tab mb-2">
        <base-mo00043
          v-model="local.mo00043"
          :oneway-model-value="localOneway.mo00043OneWay"
        />
        <!-- ③共通モードの場合、表示 -->
        <base-mo00043
          v-if="local.mo00043.id === Or10883Const.TAB_ID.PAST_HISTORY"
          v-model="local.mo00043Common"
          :oneway-model-value="localOneway.mo00043OneWayCommon"
          class="ml-auto"
        />
      </div>

      <c-v-row>
        <c-v-col
          cols="12"
          class="d-flex align-items-center search-class"
        >
          <!-- 検索内容テキストボックス -->
          <base-mo00045
            v-model="mo00045"
            :oneway-model-value="localOneway.mo00045Oneway"
            @keyup.enter="onClickSearchBtn"
          />
          <!-- 検索ボタン -->
          <base-mo00611
            :oneway-model-value="localOneway.btnSearchOneway"
            @click.stop="onClickSearchBtn"
          >
          </base-mo00611>
        </c-v-col>
        <c-v-col
          cols="12"
          class="d-flex align-items-center radio-class"
        >
          <base-mo00039
            v-model="mo00039"
            :oneway-model-value="localOneway.mo00039Oneway"
          >
            <base-at-radio
              v-for="item in radioItems"
              :key="item.value"
              :name="item.label"
              :radio-label="item.label"
              :value="item.value"
            />
          </base-mo00039>
        </c-v-col>
      </c-v-row>
      <hr class="v-divider v-theme--mainTheme" />
      <c-v-window v-model="local.mo00043.id">
        <!-- ケアマネ（予防） -->
        <c-v-window-item :value="Or10883Const.TAB_ID.CARE_MANAGER_PREVENTION">
          <c-v-data-table
            v-resizable-grid="{ columnWidths: columnMinWidth }"
            :headers="headersSupportCare"
            class="table-header table-wrapper"
            hide-default-footer
            fixed-header
            :items-per-page="-1"
            :items="tableData.inputSupportCareList"
          >
            <template #item="{ item, index }">
              <tr
                :class="{ 'select-row': selectedItemIndex === index }"
                @click="selectRow(index, item)"
                @dblclick="handleDoubleClick(index, item)"
              >
                <td>
                  {{ item.naiyo }}
                </td>
              </tr>
            </template>
          </c-v-data-table>
        </c-v-window-item>
        <!-- 過去履歴 -->
        <c-v-window-item :value="Or10883Const.TAB_ID.PAST_HISTORY">
          <c-v-data-table
            v-resizable-grid="{ columnWidths: columnMinWidth }"
            :headers="headersRirek"
            class="table-header table-wrapper"
            hide-default-footer
            fixed-header
            :items-per-page="-1"
            :items="tableData.inputSupportRirekiList"
          >
            <template #item="{ item, index }">
              <tr
                :class="{ 'select-row': selectedItemIndex === index }"
                @click="selectRow(index, item)"
                @dblclick="handleDoubleClick(index, item)"
              >
                <td>
                  {{ item.naiyo }}
                </td>
              </tr>
            </template>
          </c-v-data-table>
        </c-v-window-item>
        <!-- 共通 -->
        <c-v-window-item :value="Or10883Const.TAB_ID.COMMON">
          <c-v-data-table
            v-resizable-grid="{ columnWidths: columnMinWidth }"
            :headers="headersCom"
            class="table-header table-wrapper"
            hide-default-footer
            fixed-header
            :items-per-page="-1"
            :items="tableData.inputSupportComList"
          >
            <template #item="{ item, index }">
              <tr
                :class="{ 'select-row': selectedItemIndex === index }"
                @click="selectRow(index, item)"
                @dblclick="handleDoubleClick(index, item)"
              >
                <td>
                  {{ item.naiyo }}
                </td>
              </tr>
            </template>
          </c-v-data-table>
        </c-v-window-item>
      </c-v-window>
      <c-v-data-table
        v-resizable-grid="{ columnWidths: columnMinWidth }"
        :headers="headersDetailDisplay"
        class="table-header table-wrapper list-wrapper mt-2"
        hide-default-footer
        fixed-header
        :items-per-page="-1"
        :items="[{}]"
      >
        <template #item="{}">
          <tr class="height-100">
            <td class="px-4">
              {{ detailNaiyo }}
            </td>
          </tr>
        </template>
      </c-v-data-table>
      <g-custom-or-00232
        v-if="isShowDialogOr00232"
        v-bind="or00232"
        :oneway-model-value="Or00232OnewayModel"
      />

      <g-custom-or-26663
        v-if="isShowDialogOr26663"
        v-bind="or26663"
        :oneway-model-value="Or26663OnewayModel"
      />
      <g-base-or21814 v-bind="or21814"> </g-base-or21814>
      <g-base-or21815 v-bind="or21815"> </g-base-or21815>
    </template>

    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close()"
        >
        </base-mo00611>
        <base-mo00609
          :oneway-model-value="localOneway.mo00609ConfirmOneway"
          class="mx-2"
          @click="confirm()"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
</template>

<style scoped lang="scss">
@use '@/styles/base.scss';
@use '@/styles/cmn/dialog-data-table-list.scss';
@use '@/styles/cmn/dialog-base.scss';

.align-items-center {
  align-items: center;
}

.height-100 {
  height: 100px;

  .px-4 {
    vertical-align: top;
  }
}

.search-class,
.radio-class {
  padding: 0px !important;
}

.border-tab {
  border-bottom: thin solid rgb(var(--v-theme-form));

  :deep(.tabs) {
    border-bottom: none;
  }
}
</style>
