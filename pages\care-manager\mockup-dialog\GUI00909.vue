<script setup lang="ts">
/**
 * GUI00909:有機体:印刷設定
 * GUI00909_印刷設定
 *
 * @description
 * 印刷設定
 *
 * <AUTHOR>
 */
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or52881Const } from '~/components/custom-components/organisms/Or52881/Or52881.constants'
import { Or52881Logic } from '~/components/custom-components/organisms/Or52881/Or52881.logic'
import type { Or52881Param } from '~/components/custom-components/organisms/Or52881/Or52881.type'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI00909'
// ルーティング
const routing = 'GUI00909/pinia'
// 画面物理名
const screenName = 'GUI00909'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or52881 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00909' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 子コンポーネントのユニークIDを設定する
// or52881.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI00909',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or52881Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or52881Const.CP_ID(1)]: or52881.value,
})

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  Or52881Logic.initialize(or52881.value.uniqueCpId)
}

// ダイアログ表示フラグ
const showDialogOr52881 = computed(() => {
  // Or52881のダイアログ開閉状態
  return Or52881Logic.state.get(or52881.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(Or52881)--期間管理フラグが「管理する」の場合
 *
 */
function onClickOr52881_1() {
  // Or52881のダイアログ開閉状態を更新する
  Or52881Logic.state.set({
    uniqueCpId: or52881.value.uniqueCpId,
    state: {
      isOpen: true,
      param: {
        prtNo: '1',
        svJigyoId: '1',
        shisetuId: '1',
        tantoId: '1',
        syubetsuId: '2',
        sectionName: 'インターライ方式ケアアセスメント表',
        userId: '2',
        assessmentId: '4',
        svJigyoKnj: '1',
        processYmd: '2025/07/02',
        parentUserIdSelectDataFlag: false,
        focusSettingInitial: ['や', 'ゆ', 'よ'],
        selectedUserCounter: '2'
      } as Or52881Param,
    },
  })
}

/**************************************************
 * コンポーネント固有処理
 **************************************************/
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr52881_1"
        >GUI00909_印刷設定</v-btn
      >
      <g-custom-or-52881
        v-if="showDialogOr52881"
        v-bind="or52881"
      />
    </c-v-col>
  </c-v-row>
</template>
