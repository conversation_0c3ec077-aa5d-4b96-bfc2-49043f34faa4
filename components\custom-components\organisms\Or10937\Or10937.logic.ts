import { Or10937Const } from './Or10937.constants'
import type { Or10937StateType } from './Or10937.type'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'

/**
 * Or10937:［履歴選択］画面 ﾌｪｰｽｼｰﾄ
 * GUI00900_［履歴選択］画面 ﾌｪｰｽｼｰﾄ
 *
 * @description
 * GUI00900_［履歴選択］画面 ﾌｪｰｽｼｰﾄ
 *
 * <AUTHOR>
 */

export namespace Or10937Logic {
  /**
   * initialize
   *
   * @description
   * コンポーネントの初期化処理。
   * 共通関数を呼びpiniaの領域を初期化する。
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId } = useInitialize({
      cpId: Or10937Const.CP_ID(0),
      uniqueCpId,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [],
    })

    return {
      cpId,
      uniqueCpId: uniqCpId,
    }
  }
  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or10937StateType>(Or10937Const.CP_ID(0))
}
