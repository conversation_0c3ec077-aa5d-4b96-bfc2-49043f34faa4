import { getSequencedCpId } from '#imports'
/**
 * Or08207_基本情報画面入力フォーム
 * GUI01067_基本情報
 *
 * @description
 * 静的データ
 *
 * <AUTHOR> HOANG SY TOAN
 */

export namespace Or08207Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or08207', seq)

  export namespace DEFAULTS {
    /**
     * EMPTY_OPTION
     */
    export const EMPTY_OPTION = { label: '', value: '0' }
    /**
     * 経過的要介護
     */
    export const NURSING_CARE_VALUE = '2'

    /**
     *真実
     */
    export const TRUE = '1'
  }
}
