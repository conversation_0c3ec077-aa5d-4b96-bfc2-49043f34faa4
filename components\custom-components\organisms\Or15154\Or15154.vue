<script setup lang="ts">
/**
 * Or15154：有機体：（確定版）入院基本情報 ７．身体・生活機能の状況／療養生活上の課題についてセクション
 *
 * <AUTHOR>
 */
import { computed, reactive, ref, watch, type Ref } from 'vue'
import { useI18n } from 'vue-i18n'
import type { CodeType } from '../Or28326/Or28326.type'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import { Or51775Const } from '../Or51775/Or51775.constants'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { Or51813Logic } from '../Or51813/Or51813.logic'
import type { UserManagementInfo, AttendingPhysicianInfo } from '../Or51813/Or51813.type'
import { TeX0012Logic } from '../../template/TeX0012/TeX0012.logic'
import { Or15154Const } from './Or15154.constants'
import type { Or15154OneWayType, Or15154ValuesType } from './Or15154.type'
import {
  useCommonProps,
  useScreenOneWayBind,
  useScreenTwoWayBind,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo00046OnewayType } from '~/types/business/components/Mo00046Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo00030OnewayType, Mo00030Type } from '~/types/business/components/Mo00030Type'
import type { Mo00038OnewayType } from '~/types/business/components/Mo00038Type'
import type { Mo00020OnewayType } from '~/types/business/components/Mo00020Type'
import type { Mo01343OnewayType, Mo01343Type } from '~/types/business/components/Mo01343Type'
import type { Mo00614OnewayType } from '~/types/business/components/Mo00614Type'
import type { Mo01408OnewayType, Mo01408Type } from '~/types/business/components/Mo01408Type'
import type { Or51775OnewayType, Or51775Type } from '~/types/cmn/business/components/Or51775Type'
import type { Or51813OneWayType } from '~/types/cmn/business/components/Or51813Type'
import type { Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { TeX0012Type } from '~/types/cmn/business/components/TeX0012Type'
/**************************************************
 * Props
 **************************************************/
const systemCommonsStore = useSystemCommonsStore()
interface Props {
  /** uniqueCpId */
  uniqueCpId: string
  parentUniqueCpId: string
}
const props = defineProps<Props>()
/**************************************************
 * Pinia
 **************************************************/
const { t } = useI18n()

const { refValue } = useScreenTwoWayBind<Or15154ValuesType>({
  cpId: Or15154Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
}) as { refValue: Ref<Or15154ValuesType> }
useScreenOneWayBind<Or15154OneWayType>({
  cpId: Or15154Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    codeList: (value) => {
      localOneway.codeListOneway = value as Record<string, CodeType[]>
    },
  },
})
/**************************************************
 * 変数定義
 **************************************************/
const or51813OneWayType = ref<Or51813OneWayType>({
  functionName: '',
  assessmentStyleNumber: '',
  userId: '',
  houjinId: '',
  shisetuId: '',
  svJigyoId: '',
})

const localOneway = reactive({
  codeListOneway: {} as Record<string, CodeType[]>,
  or51775Oneway: {
    title: 'タイトルテスト',
    screenId: '',
    bunruiId: '2',
    t1Cd: '',
    t2Cd: '',
    t3Cd: '',
    tableName: '',
    columnName: '',
    assessmentMethod: '',
    inputContents: '',
    userId: TeX0012Logic.data.get(props.parentUniqueCpId)?.userId ?? systemCommonsStore.getUserId,
    mode: '',
  } as Or51775OnewayType,
  mo00045Oneway: {
    maxlength: '50',
    width: '610',
    isVerticalLabel: false,
    showItemLabel: true,
    itemLabel: t('label.name'),
  } as Mo00045OnewayType,
  mo00045Oneway2: {
    maxlength: '41',
    width: '328px',
    isVerticalLabel: false,
    showItemLabel: false,
  } as Mo00045OnewayType,
  mo00045Oneway3: {
    maxlength: '24',
    width: '320px',
    isVerticalLabel: false,
    showItemLabel: false,
  } as Mo00045OnewayType,
  mo00045Oneway4: {
    maxlength: '24',
    width: '320px',
    isVerticalLabel: false,
    showItemLabel: false,
  } as Mo00045OnewayType,
  mo00045Oneway5: {
    maxlength: '20',
    width: '305px',
    isVerticalLabel: false,
    showItemLabel: false,
  } as Mo00045OnewayType,
  mo00045Oneway6: {
    maxlength: '40',
    width: '328px',
    isVerticalLabel: false,
    showItemLabel: false,
  } as Mo00045OnewayType,
  mo00045Oneway7: {
    maxlength: '20',
    width: '200px',
    isVerticalLabel: false,
    showItemLabel: false,
  } as Mo00045OnewayType,
  mo00045Oneway8: {
    maxlength: '10',
    width: '168px',
    isVerticalLabel: false,
    showItemLabel: false,
  } as Mo00045OnewayType,
  mo00046Oneway: {
    autoGrow: false,
    rows: '2',
    maxRows: '2',
    width: '90% ',
    maxlength: '4000',
    noResize: true,
  } as Mo00046OnewayType,
  mo00009Oneway: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  mo01343Oneway: {
    selectMode: '1',
  } as Mo01343OnewayType,
  mo00030Oneway: {
    mo00045Oneway: {
      showItemLabel: true,
      itemLabel: t('label.name-furigana'),
      isVerticalLabel: false,
      maxLength: '4',
      width: '150px',
      customClass: new CustomClass({
        labelClass: 'd-flex align-center mr-2',
        labelStyle: 'padding:0 !important',
      }),
    } as Mo00045OnewayType,
    mode: '1',
    disalbeSpinBtnDefaultProcessing: false,
  } as Mo00030OnewayType,
  mo00030OnewaySmoking: {
    mo00045Oneway: {
      showItemLabel: false,
      appendLabel: t('label.smoking_quantity_per_day_label'),
      isVerticalLabel: false,
      maxLength: '14',
      width: '200px',
      customClass: new CustomClass({
        labelClass: 'd-flex align-center mr-2',
        labelStyle: 'padding:0 !important',
      }),
    } as Mo00045OnewayType,
    mode: '1',
    disalbeSpinBtnDefaultProcessing: false,
  } as Mo00030OnewayType,
  mo00030OnewayAlcohol: {
    mo00045Oneway: {
      showItemLabel: false,
      appendLabel: t('label.alcohol_consumption_quantity_per_day_label'),
      isVerticalLabel: false,
      maxLength: '28',
      width: '250px',
      customClass: new CustomClass({
        labelClass: 'd-flex align-center mr-2',
        labelStyle: 'padding:0 !important',
      }),
    } as Mo00045OnewayType,
    mode: '1',
    disalbeSpinBtnDefaultProcessing: false,
  } as Mo00030OnewayType,
  mo00038Oneway: {
    mo00045Oneway: {
      appendLabel: t('label.times_per_day'),
      showItemLabel: true,
      isVerticalLabel: false,
      maxLength: '1',
      width: '100px',
      customClass: new CustomClass({
        labelClass: 'd-flex align-center mr-2',
        labelStyle: 'padding:0 !important',
      }),
    } as Mo00045OnewayType,
    min: 0,
    max: 99,
    disalbeSpinBtnDefaultProcessing: false,
  } as Mo00038OnewayType,
  mo00614Oneway1: {
    showItemLabel: true,
    itemLabel: t('label.meal_frequency_breakfast'),
    appendLabel: t('label.meal_time_label'),
  } as Mo00614OnewayType,
  mo00614Oneway2: {
    showItemLabel: true,
    itemLabel: t('label.meal_frequency_lunch'),
    appendLabel: t('label.meal_time_label'),
  } as Mo00614OnewayType,
  mo00614Oneway3: {
    showItemLabel: true,
    itemLabel: t('label.meal_frequency_dinner'),
    appendLabel: t('label.meal_time_label'),
  } as Mo00614OnewayType,
  mo00039Oneway: {
    showItemLabel: false,
    inline: true,
    customClass: { outerClass: 'd-flex align-center' } as CustomClass,
  } as Mo00039OnewayType,
  //７．身体・生活機能の状況／療養生活上の課題についてセクション
  physicalAndLifestyleStatusTitle: {
    valueFontWeight: 'blod',
    value: t('label.physical_and_lifestyle_status_title'),
    customClass: {
      itemStyle: 'font-size:18px; !import',
    } as CustomClass,
  } as Mo01338OnewayType,
  udfMealFormCategoryLabel: {
    itemLabelFontWeight: '400',
    itemLabel: t('label.udf_meal_form_category_label'),
    customClass: { outerClass: ' background-transparent ml-2' } as CustomClass,
  } as Mo01338OnewayType,
  mo00045Oneway1: {
    maxlength: '80',
    width: '80%',
    itemLabel: t('label.name'),
    isVerticalLabel: false,
    showItemLabel: true,
    customClass: { outerClass: 'mt-2' } as CustomClass,
  } as Mo00045OnewayType,
  mo00020Oneway: {
    isRequired: false,
    isVerticalLabel: false,
    showItemLabel: false,
    showSelectArrow: false,
  } as Mo00020OnewayType,
  mo00020Oneway1: {
    isRequired: false,
    isVerticalLabel: false,
    showItemLabel: false,
    showSelectArrow: false,
    maxlength: '10',
    width: '168px',
  } as Mo00020OnewayType,
  mo00020Oneway2: {
    isRequired: false,
    isVerticalLabel: false,
    showItemLabel: false,
    showSelectArrow: false,
    maxlength: '10',
    width: '168px',
    customClass: { outerClass: 'mx-2' } as CustomClass,
  } as Mo00020OnewayType,
  mo01408Oneway: {
    showItemLabel: true,
    isVerticalLabel: false,
    itemLabel: t('label.meal_frequency_breakfast'),
    appendLabel: t('label.meal_time_label'),
    items: [],
    multiple: false,
    width: '150px',
  } as Mo01408OnewayType,
  mo01408Oneway2: {
    showItemLabel: true,
    isVerticalLabel: false,
    itemLabel: t('label.meal_frequency_lunch'),
    appendLabel: t('label.meal_time_label'),
    items: [],
    multiple: false,
    width: '150px',
  } as Mo01408OnewayType,
  mo01408Oneway3: {
    showItemLabel: true,
    isVerticalLabel: false,
    itemLabel: t('label.meal_frequency_dinner'),
    appendLabel: t('label.meal_time_label'),
    items: [],
    multiple: false,
    width: '150px',
  } as Mo01408OnewayType,
  // GUI00937 共通入力支援画面
  or51775: {
    screenId: 'GUI01300',
    bunruiId: '-', // 分類ID TBD
    t2Cd: '',
    t3Cd: '',
    tableName: 'cpn_tuc_hosp_info_teikyou_data',
    assessmentMethod: '共通情報.アセスメント方式', // アセスメント方式 TBD
    userId: systemCommonsStore.getUserId ?? '',
  } as Or51775OnewayType,
  adlIdouList: [
    {
      label: t('label.adl_independent'),
      key: 'adlIdou1',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_supervised'),
      key: 'adlIdou2',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_partial_assistance'),
      key: 'adlIdou3',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_full_assistance'),
      key: 'adlIdou4',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
  adlIdouShudanList: [
    {
      label: t('label.adl_cane'),
      key: 'adlIdouShudan1',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_walker'),
      key: 'adlIdouShudan2',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_wheelchair'),
      key: 'adlIdouShudan3',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_other'),
      key: 'adlIdouShudan4',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
  adlIdouShitsunaiList: [
    {
      label: t('label.adl_cane'),
      key: 'adlIdouShitsunai1',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_walker'),
      key: 'adlIdouShitsunai2',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_wheelchair'),
      key: 'adlIdouShitsunai3',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_other'),
      key: 'adlIdouShitsunai4',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
  adlKouiList: [
    {
      label: t('label.adl_independent'),
      key: 'adlKoui1',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_supervised'),
      key: 'adlKoui2',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_partial_assistance'),
      key: 'adlKoui3',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_full_assistance'),
      key: 'adlKoui4',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
  adlIjyouList: [
    {
      label: t('label.adl_independent'),
      key: 'adlIjyou1',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_supervised'),
      key: 'adlIjyou2',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_partial_assistance'),
      key: 'adlIjyou3',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_full_assistance'),
      key: 'adlIjyou4',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
  adlSeiyouList: [
    {
      label: t('label.adl_independent'),
      key: 'adlSeiyou1',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_supervised'),
      key: 'adlSeiyou2',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_partial_assistance'),
      key: 'adlSeiyou3',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_full_assistance'),
      key: 'adlSeiyou4',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
  adlNyuuyokuList: [
    {
      label: t('label.adl_independent'),
      key: 'adlNyuuyoku1',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_supervised'),
      key: 'adlNyuuyoku2',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_partial_assistance'),
      key: 'adlNyuuyoku3',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_full_assistance'),
      key: 'adlNyuuyoku4',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
  adlShokujiList: [
    {
      label: t('label.adl_independent'),
      key: 'adlShokuji1',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_supervised'),
      key: 'adlShokuji2',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_partial_assistance'),
      key: 'adlShokuji3',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_full_assistance'),
      key: 'adlShokuji4',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
  adlKikyoList: [
    {
      label: t('label.adl_independent'),
      key: 'adlKikyo1',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_supervised'),
      key: 'adlKikyo2',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_partial_assistance'),
      key: 'adlKikyo3',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_full_assistance'),
      key: 'adlKikyo4',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
  shokujiSeigenList: [
    {
      label: t('label.meal_form_regular'),
      key: 'shokujiKeitai1',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.meal_form_chopped'),
      key: 'shokujiKeitai2',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.meal_form_swallowing_disorder'),
      key: 'shokujiKeitai3',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.meal_form_mashed'),
      key: 'shokujiKeitai4',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
  sesshuHouhouList: [
    {
      label: t('label.intake_method_oral'),
      key: 'sesshuHouhou1',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.intake_method_enteral'),
      key: 'sesshuHouhou2',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
  hainyouList: [
    {
      label: t('label.adl_independent'),
      key: 'hainyou1',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_supervised'),
      key: 'hainyou2',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_partial_assistance'),
      key: 'hainyou3',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_full_assistance'),
      key: 'hainyou4',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
  haibenList: [
    {
      label: t('label.adl_independent'),
      key: 'haiben1',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_supervised'),
      key: 'haiben2',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_partial_assistance'),
      key: 'haiben3',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.adl_full_assistance'),
      key: 'haiben4',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
  ryouyouUmuList1: [
    {
      label: t('label.psychiatric_issues_none'),
      key: 'ryouyou1Umu',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
  ryouyouUmuList2: [
    {
      label: t('label.psychiatric_issues_hallucinations'),
      key: 'ryouyou2Umu',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.psychiatric_issues_excitement'),
      key: 'ryouyou3Umu',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.psychiatric_issues_anxiety'),
      key: 'ryouyou4Umu',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.psychiatric_issues_delusions'),
      key: 'ryouyou5Umu',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.psychiatric_issues_violence'),
      key: 'ryouyou6Umu',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.psychiatric_issues_resistance_to_care'),
      key: 'ryouyou7Umu',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.psychiatric_issues_insomnia'),
      key: 'ryouyou8Umu',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
  ryouyouUmuList3: [
    {
      label: t('label.psychiatric_issues_reversed_sleep_pattern'),
      key: 'ryouyou9Umu',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.psychiatric_issues_wandering'),
      key: 'ryouyou10Umu',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.psychiatric_issues_dangerous_behavior'),
      key: 'ryouyou11Umu',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.psychiatric_issues_unhygienic_behavior'),
      key: 'ryouyou12Umu',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
  ryouyouUmuList4: [
    {
      label: t('label.psychiatric_issues_other'),
      key: 'ryouyou13Umu',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
  sickUmuList1: [
    {
      label: t('label.psychiatric_issues_none'),
      key: 'sick1Umu',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
  sickUmuList2: [
    {
      label: t('label.medical_history_malignant_tumor'),
      key: 'sick2Umu',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.medical_history_dementia'),
      key: 'sick3Umu',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.medical_history_acute_respiratory_infection'),
      key: 'sick4Umu',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.medical_history_cerebrovascular_disease'),
      key: 'sick5Umu',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.medical_history_fracture'),
      key: 'sick6Umu',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
  sickUmuList3: [
    {
      label: t('label.psychiatric_issues_other'),
      key: 'sick7Umu',
      showItemLabel: false,
      itemLabel: '',
    },
  ],

  shochiUmuList1: [
    {
      label: t('label.psychiatric_issues_none'),
      key: 'shochi1Umu',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
  shochiUmuList2: [
    {
      label: t('label.medical_procedures_iv_infusion'),
      key: 'shochi2Umu',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.medical_procedures_oxygen_therapy'),
      key: 'shochi3Umu',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.medical_procedures_suction'),
      key: 'shochi4Umu',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.medical_procedures_tracheostomy'),
      key: 'shochi5Umu',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.medical_procedures_gastrostomy'),
      key: 'shochi6Umu',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.medical_procedures_nasogastric_feeding'),
      key: 'shochi7Umu',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.medical_procedures_enteral_feeding'),
      key: 'shochi8Umu',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.medical_procedures_pressure_ulcer_care'),
      key: 'shochi9Umu',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
  shochiUmuList3: [
    {
      label: t('label.medical_procedures_urinary_catheter'),
      key: 'shochi10Umu',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.medical_procedures_urostomy'),
      key: 'shochi11Umu',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.medical_procedures_gastrostomy_stoma'),
      key: 'shochi12Umu',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.medical_procedures_pain_management'),
      key: 'shochi13Umu',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.medical_procedures_bowel_management'),
      key: 'shochi14Umu',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
  shochiUmuList4: [
    {
      label: t('label.medical_procedures_self_injection'),
      key: 'shochi15Umu',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
  shochiUmuList5: [
    {
      label: t('label.psychiatric_issues_other'),
      key: 'shochi16Umu',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
})
//
const fieldsToCheck = [
  { key: 'shokujiCntMorn' },
  { key: 'shokujiCntNoon' },
  { key: 'shokujiCntEven' },
]
const or51775 = ref({ uniqueCpId: '' }) // Or51775：有機体：入力支援［ケアマネ］モーダル
const or51813 = ref({ uniqueCpId: '' })
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or51775Const.CP_ID(1)]: or51775.value,
})
const local = reactive({
  commonInfo: {} as TeX0012Type,
  or51775: { modelValue: '' } as Or51775Type,
  or51775Value: '',
  mo01343: {
    value: '',
    endValue: '',
    mo00024: { isOpen: false },
  },
})
/**
 * mo01343の値変更を監視
 *
 * @param mo01343 - Mo01343Type
 */
const handleMo01343 = (mo01343: Mo01343Type) => {
  refValue.value.or15154Values.nyuuinStartYmd.value = mo01343.value
  refValue.value.or15154Values.nyuuinEndYmd.value = mo01343.endValue
}

/**
 * 共通入力支援画面の確定ボタン押す後の処理
 *
 * @param data - 入力支援情報
 */
const handleOr51775Confirm = (data: Or51775ConfirmType) => {
  const setOrAppendValue = (str: string, data: Or51775ConfirmType) => {
    if (data.type === Or15154Const.DEFAULT.ADD_END_TEXT_IMPORT_TYPE) {
      // 本文末に追加の場合
      return `${str}${data.value}`
    } else if (data.type === Or15154Const.DEFAULT.OVERWRITE_TEXT_IMPORT_TYPE) {
      // 本文上書の場合
      return data.value
    } else {
      return ''
    }
  }
  ;(refValue.value.or15154Values[local.or51775Value] as unknown as Mo00045Type).value =
    setOrAppendValue(
      (refValue.value.or15154Values[local.or51775Value] as unknown as Mo00045Type).value ?? '',
      data
    )
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: false },
  })
}
/**
 * 入力支援アイコンボタンクリック
 *
 * @param column
 *
 * @param title
 *
 * @param t2Cd
 *
 * @param t3Cd
 *
 * @param columnName
 */
const handPropUp = (
  column: string,
  title: string,
  t2Cd: string,
  t3Cd: string,
  columnName: string
) => {
  // GUI00937 共通入力支援画面をポップアップで起動する
  console.log(column, title, t2Cd, t3Cd, columnName)
  local.or51775Value = column
  localOneway.or51775.title = title
  localOneway.or51775.t2Cd = t2Cd
  localOneway.or51775.t3Cd = t3Cd
  localOneway.or51775.columnName = columnName
  localOneway.or51775.inputContents = title
  local.or51775.modelValue =
    (refValue.value.or15154Values[local.or51775Value] as unknown as Mo00045Type).value ?? ''
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

watch(
  () => localOneway.codeListOneway,
  (newVal) => {
    local.mo01343.value = refValue.value.or15154Values.nyuuinStartYmd.value
    local.mo01343.endValue = refValue.value.or15154Values.nyuuinEndYmd.value
    localOneway.mo01408Oneway.items = newVal.MEAL_FREQUENCY.map((item) => {
      return {
        ...item,
        title: item.label,
      }
    })
    localOneway.mo01408Oneway2.items = localOneway.mo01408Oneway.items
    localOneway.mo01408Oneway3.items = localOneway.mo01408Oneway.items
    fieldsToCheck.forEach((field) => {
      if (
        !newVal.MEAL_FREQUENCY.find(
          (item) =>
            item.value === (refValue.value.or15154Values[field.key] as unknown as Mo01408Type).value
        )
      ) {
        ;(refValue.value.or15154Values[field.key] as unknown as Mo01408Type).value = ''
      }
    })
  }
)

const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr51813 = computed(() => {
  // Or51813のダイアログ開閉状態
  return Or51813Logic.state.get(or51813.value.uniqueCpId)?.isOpen ?? false
})
/**
 *  ボタン押下時の処理
 *
 * @param functionName - 機能名
 *
 * @param assessmentStyleNumber - アセスメント画面の様式版数
 */
function onClickOr51813(functionName: string, assessmentStyleNumber: string) {
  or51813OneWayType.value.functionName = functionName
  or51813OneWayType.value.assessmentStyleNumber = assessmentStyleNumber
  Or51813Logic.state.set({
    uniqueCpId: or51813.value.uniqueCpId,
    state: { isOpen: true },
  })
}

function getUserManagementInfo(data: UserManagementInfo) {
  console.log(data, '===>UserManagementInfo')
  refValue.value.or15154Values.sickMemoKnj.value = data.sickKnj
  if (data.nyutuKbn && data.nyutuKbn === '1') {
    refValue.value.or15154Values.nyuuinUmu = '1'
    refValue.value.or15154Values.nyuuinStartYmd.value = data.startYmd
    refValue.value.or15154Values.nyuuinEndYmd.value = data.endYmd
  }
}
function getAttendingPhysicianInfo(data: AttendingPhysicianInfo) {
  console.log(data, '===>AttendingPhysicianInfo')
}

// smoking
function handSmoking(smoking: Mo00030Type) {
  if (smoking.mo00045 && smoking.mo00045.value !== '0') {
    refValue.value.or15154Values.smokingUmu = '1'
  }
}
// alcohol
function handAlcohol(alcohol: Mo00030Type) {
  if (alcohol.mo00045 && alcohol.mo00045.value !== '0') {
    refValue.value.or15154Values.alcoholUmu = '1'
  }
}
function handleUmu(umu: string) {
  //gishiKbn
  if (umu === '0') {
    refValue.value.or15154Values.gishiKbn = ''
  }
}
function handlekbn(kbn: string) {
  //gishiUmu
  if (kbn != null) {
    refValue.value.or15154Values.gishiUmu = '1'
  }
}
function handRyouyouUmu1(ryouyouUmu1: Mo00018Type) {
  if (ryouyouUmu1.modelValue) {
    ;[
      ...localOneway.ryouyouUmuList2,
      ...localOneway.ryouyouUmuList3,
      ...localOneway.ryouyouUmuList4,
    ].forEach((item) => {
      ;(refValue.value.or15154Values[item.key] as unknown as Mo00018Type).modelValue = false
    })
    refValue.value.or15154Values.ryouyouMemoKnj.value = ''
  }
}
function handRyouyouUmu() {
  ;[
    ...localOneway.ryouyouUmuList2,
    ...localOneway.ryouyouUmuList3,
    ...localOneway.ryouyouUmuList4,
  ].forEach((item) => {
    if ((refValue.value.or15154Values[item.key] as unknown as Mo00018Type).modelValue) {
      refValue.value.or15154Values.ryouyou1Umu.modelValue = false
    }
  })
}

function handRyouyouMemoKnj(ryouyouMemoKnj: Mo00045Type) {
  if (ryouyouMemoKnj.value) {
    refValue.value.or15154Values.ryouyou1Umu.modelValue = false
  }
}

function handSickUmuList1(ryouyouUmu1: Mo00018Type) {
  if (ryouyouUmu1.modelValue) {
    ;[...localOneway.sickUmuList2, ...localOneway.sickUmuList3].forEach((item) => {
      ;(refValue.value.or15154Values[item.key] as unknown as Mo00018Type).modelValue = false
    })
    refValue.value.or15154Values.sickMemoKnj.value = ''
  }
}
function handSickUmuList() {
  ;[...localOneway.sickUmuList2, ...localOneway.sickUmuList3].forEach((item) => {
    if ((refValue.value.or15154Values[item.key] as unknown as Mo00018Type).modelValue) {
      refValue.value.or15154Values.sick1Umu.modelValue = false
    }
  })
}

function handSickMemoKnj(sickMemoKnj: Mo00045Type) {
  if (sickMemoKnj.value) {
    refValue.value.or15154Values.sick1Umu.modelValue = false
  }
}

function handShochiUmuList1(ryouyouUmu1: Mo00018Type) {
  if (ryouyouUmu1.modelValue) {
    ;[
      ...localOneway.shochiUmuList2,
      ...localOneway.shochiUmuList3,
      ...localOneway.shochiUmuList4,
    ].forEach((item) => {
      ;(refValue.value.or15154Values[item.key] as unknown as Mo00018Type).modelValue = false
    })
    refValue.value.or15154Values.shochi15MemoKnj.value = ''
    refValue.value.or15154Values.shochi16MemoKnj.value = ''
  }
}
function handShochiUmuList() {
  ;[
    ...localOneway.shochiUmuList2,
    ...localOneway.shochiUmuList3,
    ...localOneway.shochiUmuList4,
  ].forEach((item) => {
    if ((refValue.value.or15154Values[item.key] as unknown as Mo00018Type).modelValue) {
      refValue.value.or15154Values.shochi1Umu.modelValue = false
    }
  })
}

function handShochi16MemoKnj(shochiMemoKnj: Mo00045Type) {
  if (shochiMemoKnj.value) {
    refValue.value.or15154Values.shochi1Umu.modelValue = false
  }
}
</script>

<template>
  <div v-if="refValue.or15154Values">
    <c-v-row class="title">
      <c-v-col>
        <base-mo01338
          :oneway-model-value="localOneway.physicalAndLifestyleStatusTitle"
        ></base-mo01338
      ></c-v-col>
    </c-v-row>

    <!-- 麻痺の状況  -->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.paralysis_status') }}
      </c-v-col>
      <c-v-col
        cols="4"
        class="data-cell border-bottom-0"
      >
        <base-mo00039
          v-model="refValue.or15154Values.mahiKbn"
          :oneway-model-value="localOneway.mo00039Oneway"
        >
          <base-at-radio
            v-for="(item, index) in localOneway.codeListOneway.CONFIRMATION_INFO_PARALYSIS_STATUS"
            :key="'radio' + '_' + index"
            :name="item.label"
            :radio-label="item.label"
            :value="item.value"
          />
        </base-mo00039>
      </c-v-col>
      <!-- 褥瘡の有無 -->
      <c-v-col
        cols="1"
        class="header-cell"
      >
        {{ t('label.bed_sore_presence') }}
      </c-v-col>
      <c-v-col
        cols="5"
        class="data-cell border-bottom-0"
      >
        <div class="d-flex">
          <base-mo00039
            v-model="refValue.or15154Values.jyokusouUmu"
            :oneway-model-value="localOneway.mo00039Oneway"
          >
            <base-at-radio
              v-for="(item, index) in localOneway.codeListOneway.M_CD_KBN_ID_OMITTED"
              :key="'radio' + '_' + index"
              :name="item.label"
              :radio-label="item.label"
              :value="item.value"
            />
          </base-mo00039>

          <div class="d-flex align-center">
            <c-v-divider
              vertical
              inset
            />
            <base-mo00009
              :oneway-model-value="localOneway.mo00009Oneway"
              @click="
                handPropUp(
                  'jyokusouMemoKnj',
                  t('label.bed_sore_presence'),
                  Or15154Const.DEFAULT.T2_CD_2,
                  Or15154Const.DEFAULT.T3_CD_17,
                  'jyokusou_memo_knj'
                )
              "
            />
            <!-- Mo00045 -->
            <base-mo00045
              v-model="refValue.or15154Values.jyokusouMemoKnj"
              :oneway-model-value="localOneway.mo00045Oneway3"
            />
          </div>
        </div>
      </c-v-col>
    </c-v-row>

    <!-- ＡＤＬ -->
    <c-v-row
      class="row"
      style="border-right: 1px gainsboro solid"
    >
      <c-v-col
        cols="1"
        class="header-cell"
      >
        {{ t('label.adl_label') }}
      </c-v-col>
      <c-v-col cols="11">
        <c-v-row>
          <c-v-col class="header-title-cell adl-one">
            <!--ADL移動 confirmationInfo.adlIdou1 ; confirmationInfo.adlIdou2; confirmationInfo.adlIdou3; confirmationInfo.adlIdou4 -->
            {{ t('label.mobility_label') }}
          </c-v-col>
          <c-v-col class="data-cell d-flex flex-warp adl-two">
            <base-mo00018
              v-for="(item, index) in localOneway.adlIdouList"
              :key="index"
              v-model="refValue.or15154Values[item.key]"
              :oneway-model-value="{
                name: item.label,
                hideDetails: true,
                showItemLabel: item.showItemLabel,
                itemLabel: item.itemLabel,
                checkboxLabel: item.label,
                isVerticalLabel: false,
                customClass: { outerClass: 'requiredText' } as CustomClass,
              }"
            />
          </c-v-col>
          <c-v-col class="header-title-cell adl-three">
            <!--ADL移動（室内） confirmationInfo.adlIdouShitsunai1 ; confirmationInfo.adlIdouShitsunai2; confirmationInfo.adlIdouShitsunai3; confirmationInfo.adlIdouShitsunai4 -->
            {{ t('label.indoor_mobility_label') }}
          </c-v-col>
          <c-v-col class="data-cell d-flex flex-warp border-right-0">
            <base-mo00018
              v-for="(item, index) in localOneway.adlIdouShitsunaiList"
              :key="index"
              v-model="refValue.or15154Values[item.key]"
              class="widthFix"
              :oneway-model-value="{
                name: item.label,
                hideDetails: true,
                showItemLabel: item.showItemLabel,
                itemLabel: item.itemLabel,
                checkboxLabel: item.label,
                isVerticalLabel: false,
                customClass: { outerClass: 'requiredText' } as CustomClass,
              }"
            />
          </c-v-col>
        </c-v-row>

        <c-v-row>
          <c-v-col class="header-title-cell adl-one">
            <!--ADL移乗 -->
            {{ t('label.transfer_label') }}
          </c-v-col>
          <c-v-col class="data-cell d-flex flex-warp adl-two">
            <base-mo00018
              v-for="(item, index) in localOneway.adlIjyouList"
              :key="index"
              v-model="refValue.or15154Values[item.key]"
              :oneway-model-value="{
                name: item.label,
                hideDetails: true,
                showItemLabel: item.showItemLabel,
                itemLabel: item.itemLabel,
                checkboxLabel: item.label,
                isVerticalLabel: false,
                customClass: { outerClass: 'requiredText' } as CustomClass,
              }"
            />
          </c-v-col>
          <c-v-col class="header-title-cell adl-three">
            <!--ADL移動（） confirmationInfo.adlIdouShitsunai1 ; confirmationInfo.adlIdouShitsunai2; confirmationInfo.adlIdouShitsunai3; confirmationInfo.adlIdouShitsunai4 -->
            {{ t('label.outdoor_mobility_label') }}
          </c-v-col>
          <c-v-col class="data-cell d-flex flex-warp border-right-0">
            <base-mo00018
              v-for="(item, index) in localOneway.adlIdouShudanList"
              :key="index"
              v-model="refValue.or15154Values[item.key]"
              class="widthFix"
              :oneway-model-value="{
                name: item.label,
                hideDetails: true,
                showItemLabel: item.showItemLabel,
                itemLabel: item.itemLabel,
                checkboxLabel: item.label,
                isVerticalLabel: false,
                customClass: { outerClass: 'requiredText' } as CustomClass,
              }"
            />
          </c-v-col>
        </c-v-row>

        <c-v-row>
          <c-v-col class="header-title-cell adl-one">
            <!--ADL更衣  -->
            {{ t('label.dressing_label') }}
          </c-v-col>
          <c-v-col class="data-cell d-flex flex-warp adl-two">
            <base-mo00018
              v-for="(item, index) in localOneway.adlKouiList"
              :key="index"
              v-model="refValue.or15154Values[item.key]"
              :oneway-model-value="{
                name: item.label,
                hideDetails: true,
                showItemLabel: item.showItemLabel,
                itemLabel: item.itemLabel,
                checkboxLabel: item.label,
                isVerticalLabel: false,
                customClass: { outerClass: 'requiredText' } as CustomClass,
              }"
            />
          </c-v-col>
          <c-v-col class="header-title-cell adl-three">
            <!--ADL移動（） -->
            {{ t('label.activities_of_daily_living_label') }}
          </c-v-col>
          <c-v-col class="data-cell d-flex flex-warp border-right-0">
            <base-mo00018
              v-for="(item, index) in localOneway.adlKikyoList"
              :key="index"
              v-model="refValue.or15154Values[item.key]"
              class="widthFix"
              :oneway-model-value="{
                name: item.label,
                hideDetails: true,
                showItemLabel: item.showItemLabel,
                itemLabel: item.itemLabel,
                checkboxLabel: item.label,
                isVerticalLabel: false,
                customClass: { outerClass: 'requiredText' } as CustomClass,
              }"
            />
          </c-v-col>
        </c-v-row>

        <c-v-row>
          <c-v-col class="header-title-cell adl-one">
            <!--ADL整容 confirmationInfo.adlSeiyou1 ; confirmationInfo.adlSeiyou2; confirmationInfo.adlSeiyou3; confirmationInfo.adlSeiyou4 -->
            {{ t('label.grooming_label') }}
          </c-v-col>
          <c-v-col class="data-cell d-flex flex-warp adl-two">
            <base-mo00018
              v-for="(item, index) in localOneway.adlSeiyouList"
              :key="index"
              v-model="refValue.or15154Values[item.key]"
              :oneway-model-value="{
                name: item.label,
                hideDetails: true,
                showItemLabel: item.showItemLabel,
                itemLabel: item.itemLabel,
                checkboxLabel: item.label,
                isVerticalLabel: false,
                customClass: { outerClass: 'requiredText' } as CustomClass,
              }"
            />
          </c-v-col>
        </c-v-row>

        <c-v-row>
          <c-v-col class="header-title-cell adl-one">
            <!--ADL入浴 confirmationInfo.adlNyuuyoku1 ; confirmationInfo.adlNyuuyoku2; confirmationInfo.adlNyuuyoku3; confirmationInfo.adlNyuuyoku4 -->
            {{ t('label.bathing_label') }}
          </c-v-col>
          <c-v-col class="data-cell d-flex flex-warp adl-two">
            <base-mo00018
              v-for="(item, index) in localOneway.adlNyuuyokuList"
              :key="index"
              v-model="refValue.or15154Values[item.key]"
              :oneway-model-value="{
                name: item.label,
                hideDetails: true,
                showItemLabel: item.showItemLabel,
                itemLabel: item.itemLabel,
                checkboxLabel: item.label,
                isVerticalLabel: false,
                customClass: { outerClass: 'requiredText' } as CustomClass,
              }"
            />
          </c-v-col>
        </c-v-row>

        <c-v-row>
          <c-v-col class="header-title-cell adl-one border-bottom-0">
            <!--ADL食事 confirmationInfo.adlShokuji1 ; confirmationInfo.adlShokuji2; confirmationInfo.adlShokuji3; confirmationInfo.adlShokuji4 -->
            {{ t('label.eating_label') }}
          </c-v-col>
          <c-v-col class="data-cell d-flex flex-warp adl-two border-bottom-0">
            <base-mo00018
              v-for="(item, index) in localOneway.adlShokujiList"
              :key="index"
              v-model="refValue.or15154Values[item.key]"
              :oneway-model-value="{
                name: item.label,
                hideDetails: true,
                showItemLabel: item.showItemLabel,
                itemLabel: item.itemLabel,
                checkboxLabel: item.label,
                isVerticalLabel: false,
                customClass: { outerClass: 'requiredText' } as CustomClass,
              }"
            />
          </c-v-col>
        </c-v-row>
      </c-v-col>
    </c-v-row>

    <!-- 食事内容 -->
    <c-v-row class="row">
      <c-v-col
        cols="1"
        class="header-cell"
      >
        {{ t('label.meal_content_label') }}
      </c-v-col>
      <c-v-col>
        <!-- 食事回数 -->
        <c-v-row>
          <c-v-col class="header-title-cell adl-one">
            {{ t('label.meal_frequency') }}
          </c-v-col>
          <c-v-col class="data-cell d-flex">
            <!-- meal_frequency_breakfast_time_label -->
            <!-- Mo00038 -->
            <base-mo00038
              v-model="refValue.or15154Values.shokujiCnt"
              :oneway-model-value="localOneway.mo00038Oneway"
            ></base-mo00038>

            <div class="ml-5">
              <base-mo01408
                v-model="refValue.or15154Values.shokujiCntMorn"
                class="cus-select"
                :oneway-model-value="localOneway.mo01408Oneway"
              />
            </div>

            <div class="ml-5">
              <base-mo01408
                v-model="refValue.or15154Values.shokujiCntNoon"
                class="cus-select"
                :oneway-model-value="localOneway.mo01408Oneway2"
              />
            </div>

            <div class="ml-5">
              <base-mo01408
                v-model="refValue.or15154Values.shokujiCntEven"
                class="cus-select"
                :oneway-model-value="localOneway.mo01408Oneway3"
              />
            </div>
          </c-v-col>
        </c-v-row>

        <!-- 食事制限 -->
        <c-v-row>
          <c-v-col class="header-title-cell adl-one">
            {{ t('label.dietary_restrictions') }}
          </c-v-col>

          <c-v-col class="data-cell">
            <div class="d-flex">
              <base-mo00039
                v-model="refValue.or15154Values.jyokusouUmu"
                :oneway-model-value="localOneway.mo00039Oneway"
              >
                <base-at-radio
                  v-for="(item, index) in localOneway.codeListOneway.MEAL_WATER_RESTRICTION"
                  :key="'radio' + '_' + index"
                  :name="item.label"
                  :radio-label="item.label"
                  :value="item.value"
                />
              </base-mo00039>

              <div class="d-flex align-center ml-5">
                <c-v-divider
                  vertical
                  inset
                />
                <base-mo00009
                  :oneway-model-value="localOneway.mo00009Oneway"
                  @click="
                    handPropUp(
                      'shokujiSeigenMemoKnj',
                      t('label.dietary_restrictions'),
                      Or15154Const.DEFAULT.T2_CD_2,
                      Or15154Const.DEFAULT.T3_CD_2,
                      'shokuji_seigen_memo_knj'
                    )
                  "
                />
                <!-- Mo00045 -->
                <base-mo00045
                  v-model="refValue.or15154Values.shokujiSeigenMemoKnj"
                  :oneway-model-value="localOneway.mo00045Oneway8"
                />
              </div>
            </div>
          </c-v-col>
        </c-v-row>

        <!-- 食事形態 -->
        <c-v-row>
          <c-v-col class="header-title-cell adl-one">
            {{ t('label.meal_form') }}
          </c-v-col>

          <c-v-col class="data-cell">
            <div class="d-flex">
              <base-mo00018
                v-for="(item, index) in localOneway.shokujiSeigenList"
                :key="index"
                v-model="refValue.or15154Values[item.key]"
                :oneway-model-value="{
                  name: item.label,
                  hideDetails: true,
                  showItemLabel: item.showItemLabel,
                  itemLabel: item.itemLabel,
                  checkboxLabel: item.label,
                  isVerticalLabel: false,
                  customClass: { outerClass: 'requiredText' } as CustomClass,
                }"
              />
              <div class="d-flex align-center ml-5">
                {{ t('label.udf_meal_form_category_label') }}
              </div>
              <div class="d-flex align-center">
                <c-v-divider
                  vertical
                  inset
                />
                <base-mo00009
                  :oneway-model-value="localOneway.mo00009Oneway"
                  @click="
                    handPropUp(
                      'udfShokujiKeitaiKbn',
                      t('label.udf_meal_form_category_label'),
                      Or15154Const.DEFAULT.T2_CD_2,
                      Or15154Const.DEFAULT.T3_CD_4,
                      'udf_shokuji_keitai_kbn'
                    )
                  "
                />
                <!-- Mo00045 -->
                <base-mo00045
                  v-model="refValue.or15154Values.udfShokujiKeitaiKbn"
                  :oneway-model-value="localOneway.mo00045Oneway8"
                />
              </div>
            </div>
          </c-v-col>
        </c-v-row>

        <!-- 摂取方法 -->
        <c-v-row>
          <c-v-col class="header-title-cell adl-one">
            {{ t('label.intake_method_label') }}
          </c-v-col>
          <c-v-col class="d-flex align-center data-cell">
            <base-mo00018
              v-for="(item, index) in localOneway.sesshuHouhouList"
              :key="index"
              v-model="refValue.or15154Values[item.key]"
              :oneway-model-value="{
                name: item.label,
                hideDetails: true,
                showItemLabel: item.showItemLabel,
                itemLabel: item.itemLabel,
                checkboxLabel: item.label,
                isVerticalLabel: false,
                customClass: { outerClass: 'requiredText' } as CustomClass,
              }"
            />
          </c-v-col>
          <c-v-col
            cols="2"
            class="header-cell"
            style="border-bottom: 1px gainsboro solid"
          >
            {{ t('label.fluid_thickener_label') }}
          </c-v-col>
          <c-v-col
            cols="6"
            class="data-cell"
          >
            <base-mo00039
              v-model="refValue.or15154Values.suibunToromi"
              :oneway-model-value="localOneway.mo00039Oneway"
            >
              <base-at-radio
                v-for="(item, index) in localOneway.codeListOneway.M_CD_KBN_ID_OMITTED"
                :key="'radio' + '_' + index"
                :name="item.label"
                :radio-label="item.label"
                :value="item.value"
              />
            </base-mo00039>
          </c-v-col>
        </c-v-row>

        <!-- 水分制限 -->
        <c-v-row>
          <c-v-col class="header-title-cell adl-one border-bottom-0">
            {{ t('label.fluid_restriction') }}
          </c-v-col>

          <c-v-col class="data-cell border-bottom-0">
            <div class="d-flex">
              <base-mo00039
                v-model="refValue.or15154Values.suibunSeigen"
                :oneway-model-value="localOneway.mo00039Oneway"
              >
                <base-at-radio
                  v-for="(item, index) in localOneway.codeListOneway.MEAL_WATER_RESTRICTION"
                  :key="'radio' + '_' + index"
                  :name="item.label"
                  :radio-label="item.label"
                  :value="item.value"
                />
              </base-mo00039>

              <div class="d-flex align-center ml-5">
                <c-v-divider
                  vertical
                  inset
                />
                <base-mo00009
                  :oneway-model-value="localOneway.mo00009Oneway"
                  @click="
                    handPropUp(
                      'suibunSeigenMemoKnj',
                      t('label.fluid_restriction'),
                      Or15154Const.DEFAULT.T2_CD_2,
                      Or15154Const.DEFAULT.T3_CD_3,
                      'suibun_seigen_memo_knj'
                    )
                  "
                />
                <!-- Mo00045 -->
                <base-mo00045
                  v-model="refValue.or15154Values.suibunSeigenMemoKnj"
                  :oneway-model-value="localOneway.mo00045Oneway8"
                />
              </div>
            </div>
          </c-v-col>
        </c-v-row>
      </c-v-col>
    </c-v-row>

    <!-- 口腔 -->
    <c-v-row class="row">
      <c-v-col
        cols="1"
        class="header-cell"
      >
        {{ t('label.oral_care_label') }}
      </c-v-col>
      <c-v-col>
        <!-- 嚥下機能 -->
        <c-v-row>
          <!-- Mo00039 -->
          <c-v-col class="header-title-cell adl-one">
            {{ t('label.swallowing_function') }}
          </c-v-col>
          <c-v-col class="data-cell">
            <base-mo00039
              v-model="refValue.or15154Values.engeUmu"
              :oneway-model-value="localOneway.mo00039Oneway"
            >
              <base-at-radio
                v-for="(item, index) in localOneway.codeListOneway
                  .CONFIRMATION_INFO_SWALLOWING_FUNCTION"
                :key="'radio' + '_' + index"
                class="widthFix2"
                :name="item.label"
                :radio-label="item.label"
                :value="item.value"
              />
            </base-mo00039>
          </c-v-col>
          <!-- Mo00039 -->
          <c-v-col
            cols="1"
            class="header-title-cell"
            style="border-right: 0; border-bottom: 1px gainsboro solid"
          >
            {{ t('label.denture') }}
          </c-v-col>
          <c-v-col
            cols="5"
            class="data-cell d-flex align-center"
          >
            <base-mo00039
              v-model="refValue.or15154Values.gishiUmu"
              :oneway-model-value="localOneway.mo00039Oneway"
              @update:model-value="handleUmu"
            >
              <base-at-radio
                v-for="(item, index) in localOneway.codeListOneway.M_CD_KBN_ID_OMITTED"
                :key="'radio' + '_' + index"
                :name="item.label"
                :radio-label="item.label"
                :value="item.value"
              />
            </base-mo00039>
            （
            <base-mo00039
              v-model="refValue.or15154Values.gishiKbn"
              :oneway-model-value="localOneway.mo00039Oneway"
              @update:model-value="handlekbn"
            >
              <base-at-radio
                v-for="(item, index) in localOneway.codeListOneway
                  .CONFIRMATION_INFO_ARTIFICIAL_TOOTH_CATEGORY"
                :key="'radio' + '_' + index"
                :name="item.label"
                :radio-label="item.label"
                :value="item.value"
              />
            </base-mo00039>
            ）
          </c-v-col>
        </c-v-row>
        <!-- 口腔清潔 -->
        <c-v-row>
          <!-- Mo00039 -->
          <c-v-col class="header-title-cell adl-one border-bottom-0">
            {{ t('label.oral_hygiene') }}
          </c-v-col>
          <c-v-col class="data-cell border-bottom-0">
            <!-- Mo00039 -->
            <base-mo00039
              v-model="refValue.or15154Values.koukuuCare"
              :oneway-model-value="localOneway.mo00039Oneway"
            >
              <base-at-radio
                v-for="(item, index) in localOneway.codeListOneway
                  .CONFIRMATION_INFO_ORAL_CAVITY_CLEANLINESS"
                :key="'radio' + '_' + index"
                class="widthFix2"
                :name="item.label"
                :radio-label="item.label"
                :value="item.value"
              />
            </base-mo00039>
          </c-v-col>

          <c-v-col
            cols="1"
            class="header-title-cell"
            style="border-right: 0; border-bottom: 0"
          >
            {{ t('label.bad_breath') }}
          </c-v-col>
          <c-v-col
            cols="5"
            class="data-cell border-bottom-0"
          >
            <base-mo00039
              v-model="refValue.or15154Values.koushuuUmu"
              :oneway-model-value="localOneway.mo00039Oneway"
            >
              <base-at-radio
                v-for="(item, index) in localOneway.codeListOneway.M_CD_KBN_ID_OMITTED"
                :key="'radio' + '_' + index"
                :name="item.label"
                :radio-label="item.label"
                :value="item.value"
              />
            </base-mo00039>
          </c-v-col>
        </c-v-row>
      </c-v-col>
    </c-v-row>

    <!-- 排泄 -->
    <c-v-row class="row">
      <c-v-col
        cols="1"
        class="header-cell"
      >
        {{ t('label.elimination_label') }}
      </c-v-col>
      <c-v-col>
        <!-- Mo00018 -->
        <c-v-row>
          <c-v-col class="header-title-cell adl-one">
            {{ t('label.urination_label') }}
          </c-v-col>
          <c-v-col class="data-cell d-flex align-center">
            <base-mo00018
              v-for="(item, index) in localOneway.hainyouList"
              :key="index"
              v-model="refValue.or15154Values[item.key]"
              :oneway-model-value="{
                name: item.label,
                hideDetails: true,
                showItemLabel: item.showItemLabel,
                itemLabel: item.itemLabel,
                checkboxLabel: item.label,
                isVerticalLabel: false,
                customClass: { outerClass: 'requiredText' } as CustomClass,
              }"
            />
          </c-v-col>
        </c-v-row>

        <c-v-row>
          <c-v-col class="header-title-cell adl-one">
            {{ t('label.defecation_label') }}
          </c-v-col>
          <c-v-col class="data-cell d-flex align-center">
            <base-mo00018
              v-for="(item, index) in localOneway.haibenList"
              :key="index"
              v-model="refValue.or15154Values[item.key]"
              :oneway-model-value="{
                name: item.label,
                hideDetails: true,
                showItemLabel: item.showItemLabel,
                itemLabel: item.itemLabel,
                checkboxLabel: item.label,
                isVerticalLabel: false,
                customClass: { outerClass: 'requiredText' } as CustomClass,
              }"
            />
          </c-v-col>
        </c-v-row>

        <c-v-row>
          <c-v-col class="header-title-cell adl-one">
            {{ t('label.portable_toilet_label_1') }}<br />
            {{ t('label.portable_toilet_label_2') }}
          </c-v-col>
          <c-v-col class="data-cell">
            <!--  -->
            <base-mo00039
              v-model="refValue.or15154Values.portableToiletUmu"
              :oneway-model-value="localOneway.mo00039Oneway"
            >
              <base-at-radio
                v-for="(item, index) in localOneway.codeListOneway
                  .CONFIRMATION_INFO_PORTABLE_TOILETS"
                :key="'radio' + '_' + index"
                :name="item.label"
                :radio-label="item.label"
                :value="item.value"
              />
            </base-mo00039>
          </c-v-col>
        </c-v-row>

        <c-v-row>
          <c-v-col class="header-title-cell adl-one border-bottom-0">
            {{ t('label.diaper_pad') }}
          </c-v-col>
          <c-v-col class="data-cell border-bottom-0">
            <!-- CONFIRMATION_INFO_PORTABLE_TOILETS -->
            <base-mo00039
              v-model="refValue.or15154Values.omutsuPadUmu"
              :oneway-model-value="localOneway.mo00039Oneway"
            >
              <base-at-radio
                v-for="(item, index) in localOneway.codeListOneway
                  .CONFIRMATION_INFO_PORTABLE_TOILETS"
                :key="'radio' + '_' + index"
                :name="item.label"
                :radio-label="item.label"
                :value="item.value"
              />
            </base-mo00039>
          </c-v-col>
        </c-v-row>
      </c-v-col>
    </c-v-row>

    <!-- 睡眠の状態 -->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        <!--  <base-mo01338> -->
        {{ t('label.sleep_status') }}
      </c-v-col>
      <c-v-col
        cols="10"
        class="data-cell border-bottom-0"
      >
        <div class="d-flex">
          <base-mo00039
            v-model="refValue.or15154Values.sleepJyoutai"
            :oneway-model-value="localOneway.mo00039Oneway"
          >
            <base-at-radio
              v-for="(item, index) in localOneway.codeListOneway.SLEEPING_STATE"
              :key="'radio' + '_' + index"
              class="sleepRadio"
              :name="item.label"
              :radio-label="item.label"
              :value="item.value"
            />
          </base-mo00039>

          <div class="d-flex align-center ml-5">
            <c-v-divider
              vertical
              inset
            />
            <base-mo00009
              :oneway-model-value="localOneway.mo00009Oneway"
              @click="
                handPropUp(
                  'sleepMemoKnj',
                  t('label.sleep_status'),
                  Or15154Const.DEFAULT.T2_CD_2,
                  Or15154Const.DEFAULT.T3_CD_5,
                  'sleep_memo_knj'
                )
              "
            />
            <!-- Mo00045 -->
            <base-mo00045
              v-model="refValue.or15154Values.sleepMemoKnj"
              :oneway-model-value="localOneway.mo00045Oneway4"
            />
          </div>
        </div>
      </c-v-col>
    </c-v-row>

    <!-- 眠剤の使用 -->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        <!--  <base-mo01338> -->
        {{ t('label.sleep_aid_usage') }}
      </c-v-col>
      <c-v-col
        cols="10"
        class="data-cell border-bottom-0"
      >
        <base-mo00039
          v-model="refValue.or15154Values.sleepDrugUmu"
          :oneway-model-value="localOneway.mo00039Oneway"
        >
          <base-at-radio
            v-for="(item, index) in localOneway.codeListOneway.M_CD_KBN_ID_OMITTED"
            :key="'radio' + '_' + index"
            :name="item.label"
            :radio-label="item.label"
            :value="item.value"
          />
        </base-mo00039>
      </c-v-col>
    </c-v-row>

    <!-- 喫煙 -->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        <!--  <base-mo01338> -->
        {{ t('label.smoking_label') }}
      </c-v-col>
      <c-v-col
        cols="10"
        class="data-cell border-bottom-0"
      >
        <div class="d-flex">
          <base-mo00039
            v-model="refValue.or15154Values.smokingUmu"
            :oneway-model-value="localOneway.mo00039Oneway"
          >
            <base-at-radio
              v-for="(item, index) in localOneway.codeListOneway.M_CD_KBN_ID_OMITTED"
              :key="'radio' + '_' + index"
              :name="item.label"
              :radio-label="item.label"
              :value="item.value"
            />
          </base-mo00039>
          <!-- Mo00030-->
          <base-mo00030
            v-model="refValue.or15154Values.smoking"
            class="ml-4"
            :oneway-model-value="localOneway.mo00030OnewaySmoking"
            @update:model-value="handSmoking"
          />
        </div>
      </c-v-col>
    </c-v-row>

    <!-- 飲酒 -->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        <!--  <base-mo01338> -->
        {{ t('label.alcohol_consumption_label') }}
      </c-v-col>
      <c-v-col
        cols="10"
        class="data-cell border-bottom-0"
      >
        <div class="d-flex">
          <base-mo00039
            v-model="refValue.or15154Values.alcoholUmu"
            :oneway-model-value="localOneway.mo00039Oneway"
          >
            <base-at-radio
              v-for="(item, index) in localOneway.codeListOneway.M_CD_KBN_ID_OMITTED"
              :key="'radio' + '_' + index"
              :name="item.label"
              :radio-label="item.label"
              :value="item.value"
            />
          </base-mo00039>

          <div class="d-flex align-center">
            <!-- Mo00030 -->
            <base-mo00030
              v-model="refValue.or15154Values.alcohol"
              class="ml-4"
              :oneway-model-value="localOneway.mo00030OnewayAlcohol"
              @update:model-value="handAlcohol"
            />
          </div>
        </div>
      </c-v-col>
    </c-v-row>

    <!-- コミュニケーション能力 -->
    <c-v-row class="row">
      <c-v-col
        cols="1"
        style="padding-left: 6px !important"
        class="header-cell"
      >
        {{ t('label.communication_skills_label_1') }}<br />
        {{ t('label.communication_skills_label_2') }}
      </c-v-col>

      <c-v-col>
        <c-v-row>
          <c-v-col class="header-title-cell adl-one">
            {{ t('label.vision') }}
          </c-v-col>
          <c-v-col class="data-cell">
            <!--  Mo00039 -->
            <base-mo00039
              v-model="refValue.or15154Values.eyesightKbn"
              :oneway-model-value="localOneway.mo00039Oneway"
            >
              <base-at-radio
                v-for="(item, index) in localOneway.codeListOneway.CONFIRMATION_INFO_CODE"
                :key="'radio' + '_' + index"
                :name="item.label"
                :radio-label="item.label"
                :value="item.value"
              />
            </base-mo00039>
          </c-v-col>

          <c-v-col
            cols="2"
            class="header-title-cell border-right-0"
            style="border-bottom: 1px gainsboro solid"
          >
            {{ t('label.consultation-label-28') }}
          </c-v-col>
          <c-v-col
            cols="5"
            class="data-cell"
          >
            <div>
              <base-mo00039
                v-model="refValue.or15154Values.glassesUmu"
                :oneway-model-value="localOneway.mo00039Oneway"
              >
                <div
                  v-for="(item, index) in localOneway.codeListOneway.M_CD_KBN_ID_OMITTED"
                  :key="'radio' + '_' + index"
                  class="d-flex align-center"
                >
                  <base-at-radio
                    :name="item.label"
                    :radio-label="item.label"
                    :value="item.value"
                  />
                  <div
                    v-if="index === 0"
                    class="d-flex align-center"
                  >
                    <c-v-divider
                      vertical
                      inset
                    />
                    <base-mo00009
                      :oneway-model-value="localOneway.mo00009Oneway"
                      @click="
                        handPropUp(
                          'glassesMemoKnj',
                          t('label.consultation-label-28'),
                          Or15154Const.DEFAULT.T2_CD_2,
                          Or15154Const.DEFAULT.T3_CD_6,
                          'glasses_memo_knj'
                        )
                      "
                    />
                    <!-- Mo00045 -->
                    <base-mo00045
                      v-model="refValue.or15154Values.glassesMemoKnj"
                      :oneway-model-value="localOneway.mo00045Oneway5"
                    />
                  </div>
                </div>
              </base-mo00039>
            </div>
          </c-v-col>
        </c-v-row>

        <c-v-row>
          <c-v-col class="header-title-cell adl-one">
            {{ t('label.hearing_power') }}
          </c-v-col>
          <c-v-col class="data-cell">
            <base-mo00039
              v-model="refValue.or15154Values.hearingKbn"
              :oneway-model-value="localOneway.mo00039Oneway"
            >
              <base-at-radio
                v-for="(item, index) in localOneway.codeListOneway.CONFIRMATION_INFO_CODE"
                :key="'radio' + '_' + index"
                :name="item.label"
                :radio-label="item.label"
                :value="item.value"
              />
            </base-mo00039>
          </c-v-col>

          <c-v-col
            cols="2"
            class="header-title-cell border-right-0"
            style="border-bottom: 1px gainsboro solid"
          >
            {{ t('label.consultation-label-27') }}
          </c-v-col>
          <c-v-col
            cols="5"
            class="data-cell"
          >
            <base-mo00039
              v-model="refValue.or15154Values.hearingAidUmu"
              :oneway-model-value="localOneway.mo00039Oneway"
            >
              <base-at-radio
                v-for="(item, index) in localOneway.codeListOneway.M_CD_KBN_ID_OMITTED"
                :key="'radio' + '_' + index"
                :name="item.label"
                :radio-label="item.label"
                :value="item.value"
              />
            </base-mo00039>
          </c-v-col>
        </c-v-row>

        <c-v-row>
          <c-v-col cols="5">
            <c-v-row>
              <c-v-col class="header-title-cell adl-four">
                {{ t('label.language') }}
              </c-v-col>
              <c-v-col class="data-cell">
                <base-mo00039
                  v-model="refValue.or15154Values.languageAbility"
                  :oneway-model-value="localOneway.mo00039Oneway"
                >
                  <base-at-radio
                    v-for="(item, index) in localOneway.codeListOneway.CONFIRMATION_INFO_CODE"
                    :key="'radio' + '_' + index"
                    :name="item.label"
                    :radio-label="item.label"
                    :value="item.value"
                  />
                </base-mo00039>
              </c-v-col>
            </c-v-row>

            <c-v-row>
              <c-v-col class="header-title-cell adl-four border-bottom-0">
                {{ t('label.communication') }}
              </c-v-col>
              <c-v-col class="data-cell border-bottom-0">
                <base-mo00039
                  v-model="refValue.or15154Values.comnKbn"
                  :oneway-model-value="localOneway.mo00039Oneway"
                >
                  <base-at-radio
                    v-for="(item, index) in localOneway.codeListOneway.CONFIRMATION_INFO_CODE"
                    :key="'radio' + '_' + index"
                    :name="item.label"
                    :radio-label="item.label"
                    :value="item.value"
                  />
                </base-mo00039>
              </c-v-col>
            </c-v-row>
          </c-v-col>

          <c-v-col
            cols="2"
            class="header-title-cell border-right-0"
          >
            {{ t('label.special_communication_notes_1') }}<br />
            {{ t('label.special_communication_notes_2') }}<br />
            <div class="d-flex justify-space-between mt-f1">
              {{ t('label.special_communication_notes_3') }}
              <div class="d-flex">
                <c-v-divider
                  vertical
                  inset
                />
                <base-mo00009
                  :oneway-model-value="localOneway.mo00009Oneway"
                  @click="
                    handPropUp(
                      'comnTokkiKnj',
                      t('label.special_communication_notes_label'),
                      Or15154Const.DEFAULT.T2_CD_2,
                      Or15154Const.DEFAULT.T3_CD_7,
                      'comn_tokki_knj'
                    )
                  "
                />
              </div>
            </div>
          </c-v-col>

          <c-v-col
            cols="5"
            class="data-cell border-bottom-0"
          >
            <base-mo00046
              v-model="refValue.or15154Values.comnTokkiKnj"
              :oneway-model-value="localOneway.mo00046Oneway"
            />
          </c-v-col>
        </c-v-row>
      </c-v-col>
    </c-v-row>

    <!-- 精神面における療養上の問題 -->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.psychiatric_issues_label1') }}<br />
        {{ t('label.psychiatric_issues_label2') }}
      </c-v-col>
      <c-v-col
        cols="10"
        class="data-cell2 d-flex flex-column border-bottom-0"
      >
        <div class="d-flex">
          <!-- Mo00018 -->
          <base-mo00018
            v-for="(item, index) in localOneway.ryouyouUmuList1"
            :key="item.key"
            v-model="refValue.or15154Values[item.key]"
            :oneway-model-value="{
              name: item.label,
              hideDetails: true,
              showItemLabel: item.showItemLabel,
              itemLabel: item.itemLabel,
              checkboxLabel: item.label,
              isVerticalLabel: false,
              customClass: { outerClass: 'requiredText' } as CustomClass,
            }"
            @update:model-value="handRyouyouUmu1"
          />
        </div>

        <div class="d-flex ml-2">
          <!-- Mo00018 -->
          <base-mo00018
            v-for="(item, index) in localOneway.ryouyouUmuList2"
            :key="item.key"
            v-model="refValue.or15154Values[item.key]"
            class="mx-2"
            :oneway-model-value="{
              name: item.label,
              hideDetails: true,
              showItemLabel: item.showItemLabel,
              itemLabel: item.itemLabel,
              checkboxLabel: item.label,
              isVerticalLabel: false,
              customClass: { outerClass: 'requiredText' } as CustomClass,
            }"
            @change="handRyouyouUmu"
          />
        </div>

        <div class="d-flex ml-2">
          <base-mo00018
            v-for="(item, index) in localOneway.ryouyouUmuList3"
            :key="item.key"
            v-model="refValue.or15154Values[item.key]"
            class="mx-2"
            :oneway-model-value="{
              name: item.label,
              hideDetails: true,
              showItemLabel: item.showItemLabel,
              itemLabel: item.itemLabel,
              checkboxLabel: item.label,
              isVerticalLabel: false,
              customClass: { outerClass: 'requiredText' } as CustomClass,
            }"
            @change="handRyouyouUmu"
          />
        </div>

        <div class="d-flex ml-2">
          <base-mo00018
            v-for="(item, index) in localOneway.ryouyouUmuList4"
            :key="item.key"
            v-model="refValue.or15154Values[item.key]"
            class="mx-2"
            :oneway-model-value="{
              name: item.label,
              hideDetails: true,
              showItemLabel: item.showItemLabel,
              itemLabel: item.itemLabel,
              checkboxLabel: item.label,
              isVerticalLabel: false,
              customClass: { outerClass: 'requiredText' } as CustomClass,
            }"
            @change="handRyouyouUmu"
          />
          <!-- Mo00045 -->
          <div class="d-flex align-center">
            <c-v-divider
              vertical
              inset
            />
            <base-mo00009
              :oneway-model-value="localOneway.mo00009Oneway"
              @click="
                handPropUp(
                  'ryouyouMemoKnj',
                  t('label.psychiatric_issues_label'),
                  Or15154Const.DEFAULT.T2_CD_2,
                  Or15154Const.DEFAULT.T3_CD_8,
                  'ryouyou_memo_knj'
                )
              "
            />

            <!-- Mo00045 -->
            <base-mo00045
              v-model="refValue.or15154Values.ryouyouMemoKnj"
              :oneway-model-value="localOneway.mo00045Oneway6"
              @update:model-value="handRyouyouMemoKnj"
            />
          </div>
        </div>
      </c-v-col>
    </c-v-row>

    <!-- 疾患歴 -->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.medical_history_label') }}
        <div class="d-flex align-center">
          <c-v-divider
            vertical
            inset
          />
          <base-mo00009
            v-if="local.commonInfo.pastMedicalHistoryAuthorityFlag === Or15154Const.DEFAULT.VALUE_0"
            :oneway-model-value="localOneway.mo00009Oneway"
            @click="
              onClickOr51813(
                Or15154Const.DEFAULT.FUNCTION_NAME_ASSESSMENT,
                Or15154Const.DEFAULT.STYLE_NUMBER_BEFORE_REVISION
              )
            "
          />
          <g-custom-or-51813
            v-if="showDialogOr51813"
            v-bind="or51813"
            :oneway-model-value="or51813OneWayType"
            @user-management-confirm="getUserManagementInfo"
            @attending-physician-confirm="getAttendingPhysicianInfo"
          />
        </div>
      </c-v-col>
      <c-v-col
        cols="10"
        class="data-cell2 d-flex flex-column border-bottom-0"
      >
        <div class="d-flex">
          <!-- Mo00018 -->
          <base-mo00018
            v-for="(item, index) in localOneway.sickUmuList1"
            :key="item.key"
            v-model="refValue.or15154Values[item.key]"
            :oneway-model-value="{
              name: item.label,
              hideDetails: true,
              showItemLabel: item.showItemLabel,
              itemLabel: item.itemLabel,
              checkboxLabel: item.label,
              isVerticalLabel: false,
              customClass: { outerClass: 'requiredText' } as CustomClass,
            }"
            @update:model-value="handSickUmuList1"
          />
        </div>

        <div class="d-flex ml-2">
          <!-- Mo00018 -->
          <base-mo00018
            v-for="(item, index) in localOneway.sickUmuList2"
            :key="item.key"
            v-model="refValue.or15154Values[item.key]"
            class="mx-2"
            :oneway-model-value="{
              name: item.label,
              hideDetails: true,
              showItemLabel: item.showItemLabel,
              itemLabel: item.itemLabel,
              checkboxLabel: item.label,
              isVerticalLabel: false,
              customClass: { outerClass: 'requiredText' } as CustomClass,
            }"
            @change="handSickUmuList"
          />
        </div>
        <div class="d-flex ml-2">
          <!-- Mo00018 -->
          <base-mo00018
            v-for="(item, index) in localOneway.sickUmuList3"
            :key="item.key"
            v-model="refValue.or15154Values[item.key]"
            class="mx-2"
            :oneway-model-value="{
              name: item.label,
              hideDetails: true,
              showItemLabel: item.showItemLabel,
              itemLabel: item.itemLabel,
              checkboxLabel: item.label,
              isVerticalLabel: false,
              customClass: { outerClass: 'requiredText' } as CustomClass,
            }"
            @change="handSickUmuList"
          />
          <!-- Mo00045 -->
          <div class="d-flex align-center">
            <c-v-divider
              vertical
              inset
            />
            <base-mo00009
              :oneway-model-value="localOneway.mo00009Oneway"
              @click="
                handPropUp(
                  'sickMemoKnj',
                  t('label.medical_history_label'),
                  Or15154Const.DEFAULT.T2_CD_2,
                  Or15154Const.DEFAULT.T3_CD_9,
                  'sick_memo_knj'
                )
              "
            />
            <!-- Mo00045 -->
            <base-mo00045
              v-model="refValue.or15154Values.sickMemoKnj"
              :oneway-model-value="localOneway.mo00045Oneway6"
              @update:model-value="handSickMemoKnj"
            />
          </div>
        </div>
      </c-v-col>
    </c-v-row>

    <!-- 入院歴 -->
    <c-v-row class="row">
      <c-v-col
        cols="1"
        class="header-cell d-flex"
      >
        {{ t('label.hospitalization_history_label') }}
        <c-v-divider
          class="right"
          vertical
          inset
        />
        <base-mo00009
          v-if="local.commonInfo.pastMedicalHistoryAuthorityFlag === Or15154Const.DEFAULT.VALUE_0"
          :oneway-model-value="localOneway.mo00009Oneway"
          @click="
            onClickOr51813(
              Or15154Const.DEFAULT.FUNCTION_NAME_ASSESSMENT,
              Or15154Const.DEFAULT.STYLE_NUMBER_R34
            )
          "
        />
      </c-v-col>
      <c-v-col cols="11">
        <c-v-row>
          <c-v-col class="header-title-cell adl-one">
            {{ t('label.recent_hospitalization_1') }}<br />
            {{ t('label.recent_hospitalization_2') }}
          </c-v-col>
          <c-v-col class="data-cell d-flex">
            <div>
              <base-mo00039
                v-model="refValue.or15154Values.nyuuinUmu"
                :oneway-model-value="localOneway.mo00039Oneway"
              >
                <div
                  v-for="(item, index) in localOneway.codeListOneway.MEAL_WATER_RESTRICTION"
                  :key="'radio' + '_' + index"
                  class="d-flex align-center"
                >
                  <base-at-radio
                    class="mr-9"
                    :name="item.label"
                    :radio-label="item.label"
                    :value="item.value"
                  />
                  <div
                    v-if="index === 1"
                    class="d-flex"
                  >
                    <div class="d-flex align-center">
                      {{ t('label.reason_label') }}
                      <c-v-divider
                        vertical
                        inset
                      />
                      <base-mo00009
                        :oneway-model-value="localOneway.mo00009Oneway"
                        @click="
                          handPropUp(
                            'nyuuinRiyu',
                            t('label.reason_label'),
                            Or15154Const.DEFAULT.T2_CD_2,
                            Or15154Const.DEFAULT.T3_CD_18,
                            'nyuuin_riyu'
                          )
                        "
                      />

                      <!-- Mo00045 -->
                      <base-mo00045
                        v-model="refValue.or15154Values.nyuuinRiyu"
                        :oneway-model-value="localOneway.mo00045Oneway7"
                      />
                    </div>
                    <div class="d-flex align-center ml-10">
                      {{ t('label.duration_label') }}
                      <div class="d-flex ml-2">
                        <c-v-divider
                          vertical
                          inset
                        />
                        <base-mo00009
                          :oneway-model-value="localOneway.mo00009Oneway"
                          @click="local.mo01343.mo00024.isOpen = true"
                        >
                          <base-mo01343
                            v-model="local.mo01343"
                            :oneway-model-value="localOneway.mo01343Oneway"
                            @update:model-value="handleMo01343"
                          />
                        </base-mo00009>
                      </div>
                      <base-mo00020
                        v-model="refValue.or15154Values.nyuuinStartYmd"
                        :oneway-model-value="localOneway.mo00020Oneway1"
                      />
                      <p class="mx-2">～</p>
                      <base-mo00020
                        v-model="refValue.or15154Values.nyuuinEndYmd"
                        :oneway-model-value="localOneway.mo00020Oneway2"
                      />
                    </div>
                  </div>
                </div>
              </base-mo00039>
            </div>
          </c-v-col>
        </c-v-row>

        <c-v-row>
          <c-v-col class="header-title-cell adl-one">
            {{ t('label.hospitalization_frequency_label') }}
          </c-v-col>
          <c-v-col class="data-cell border-bottom-0">
            <base-mo00039
              v-model="refValue.or15154Values.nyuuinHindo"
              :oneway-model-value="localOneway.mo00039Oneway"
            >
              <base-at-radio
                v-for="(item, index) in localOneway.codeListOneway.HOSPITALIZATION_FREQUENCY"
                :key="'radio' + '_' + index"
                :name="item.label"
                :radio-label="item.label"
                :value="item.value"
              />
            </base-mo00039>
          </c-v-col>
        </c-v-row>
      </c-v-col>
    </c-v-row>

    <!-- 入院前に実施している医療処置 -->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.medical_procedures_performed_before_hospital_admission') }}
      </c-v-col>
      <c-v-col
        cols="10"
        class="data-cell2 d-flex flex-column border-bottom-0"
      >
        <div class="d-flex">
          <!-- Mo00018 -->
          <base-mo00018
            v-for="(item, index) in localOneway.shochiUmuList1"
            :key="item.key"
            v-model="refValue.or15154Values[item.key]"
            :oneway-model-value="{
              name: item.label,
              hideDetails: true,
              showItemLabel: item.showItemLabel,
              itemLabel: item.itemLabel,
              checkboxLabel: item.label,
              isVerticalLabel: false,
              customClass: { outerClass: 'requiredText' } as CustomClass,
            }"
            @update:model-value="handShochiUmuList1"
          />
        </div>

        <div class="d-flex ml-2">
          <!-- Mo00018 -->
          <base-mo00018
            v-for="(item, index) in localOneway.shochiUmuList2"
            :key="item.key"
            v-model="refValue.or15154Values[item.key]"
            class="mx-2"
            :oneway-model-value="{
              name: item.label,
              hideDetails: true,
              showItemLabel: item.showItemLabel,
              itemLabel: item.itemLabel,
              checkboxLabel: item.label,
              isVerticalLabel: false,
              customClass: { outerClass: 'requiredText' } as CustomClass,
            }"
            @change="handShochiUmuList"
          />
        </div>

        <div class="d-flex ml-2">
          <!-- Mo00018 -->
          <base-mo00018
            v-for="(item, index) in localOneway.shochiUmuList3"
            :key="item.key"
            v-model="refValue.or15154Values[item.key]"
            class="mx-2"
            :oneway-model-value="{
              name: item.label,
              hideDetails: true,
              showItemLabel: item.showItemLabel,
              itemLabel: item.itemLabel,
              checkboxLabel: item.label,
              isVerticalLabel: false,
              customClass: { outerClass: 'requiredText' } as CustomClass,
            }"
            @change="handShochiUmuList"
          />
        </div>

        <div class="d-flex ml-2">
          <!-- Mo00018 -->
          <base-mo00018
            v-for="(item, index) in localOneway.shochiUmuList4"
            :key="item.key"
            v-model="refValue.or15154Values[item.key]"
            class="ml-2 mx-1"
            :oneway-model-value="{
              name: item.label,
              hideDetails: true,
              showItemLabel: item.showItemLabel,
              itemLabel: item.itemLabel,
              checkboxLabel: item.label,
              isVerticalLabel: false,
              customClass: { outerClass: 'requiredText' } as CustomClass,
            }"
            @change="handShochiUmuList"
          />
          <!-- Mo00045 -->
          <div class="d-flex align-center">
            <c-v-divider
              vertical
              inset
            />
            <base-mo00009
              :oneway-model-value="localOneway.mo00009Oneway"
              @click="
                handPropUp(
                  'shochi15MemoKnj',
                  t('label.injection_memo'),
                  Or15154Const.DEFAULT.T2_CD_2,
                  Or15154Const.DEFAULT.T3_CD_10,
                  'shochi15_memo_knj'
                )
              "
            />

            <!-- Mo00045 -->
            <base-mo00045
              v-model="refValue.or15154Values.shochi15MemoKnj"
              :oneway-model-value="localOneway.mo00045Oneway7"
              @update:model-value="handShochi16MemoKnj"
            />
          </div>

          <base-mo00018
            v-for="(item, index) in localOneway.shochiUmuList5"
            :key="index"
            v-model="refValue.or15154Values[item.key]"
            class="mx-1"
            :oneway-model-value="{
              name: item.label,
              hideDetails: true,
              showItemLabel: item.showItemLabel,
              itemLabel: item.itemLabel,
              checkboxLabel: item.label,
              isVerticalLabel: false,
              customClass: { outerClass: 'requiredText' } as CustomClass,
            }"
          />
          <!-- Mo00045 -->
          <div class="d-flex align-center">
            <c-v-divider
              vertical
              inset
            />
            <base-mo00009
              :oneway-model-value="localOneway.mo00009Oneway"
              @click="
                handPropUp(
                  'shochi16MemoKnj',
                  t('label.psychiatric_issues_other'),
                  Or15154Const.DEFAULT.T2_CD_2,
                  Or15154Const.DEFAULT.T3_CD_11,
                  'shochi16_memo_knj'
                )
              "
            />
            <!-- Mo00045 -->
            <base-mo00045
              v-model="refValue.or15154Values.shochi16MemoKnj"
              :oneway-model-value="localOneway.mo00045Oneway6"
              @update:model-value="handShochi16MemoKnj"
            />
          </div>
        </div>
      </c-v-col>
    </c-v-row>
    <!-- Or51775: 有機体: GUI00937 入力支援［ケアマネ］をポップアップで起動する。 -->
    <g-custom-or51775
      v-if="showDialogOr51775"
      v-bind="or51775"
      v-model="local.or51775"
      :oneway-model-value="localOneway.or51775"
      @confirm="handleOr51775Confirm"
    />
  </div>
</template>

<style scoped lang="scss">
.row {
  display: flex;
  align-items: center;
  border-bottom: 1px gainsboro solid;
  border-left: 1px gainsboro solid;
  // border-right: 1px gainsboro solid;
  min-height: 62px;
}

.header-cell {
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 12px !important;
}

.header-title-cell {
  background-color: transparent;
  border-right: 1px gainsboro solid;
  display: grid;
  align-items: center;
}

.data-cell {
  border-left: 1px gainsboro solid;
  border-bottom: 1px gainsboro solid;
  border-right: 1px gainsboro solid;
  background: #fff;
  padding: 10px 12px;
  width: 100%;
  min-height: 62px;
  display: grid;
  align-items: center;
}

.data-cell2 {
  border-left: 1px gainsboro solid;
  border-bottom: 1px gainsboro solid;
  border-right: 1px gainsboro solid;
  background: #fff;
  padding: 10px 12px;
  width: 100%;
  min-height: 62px;
  display: grid;
}
.border-right-0 {
  border-right: 0;
}
.border-bottom-0 {
  border-bottom: 0 !important;
}
:deep(.v-input__control) {
  background-color: rgb(var(--v-theme-surface));
}
:deep(.v-selection-control-group--inline) {
  align-items: center;
}
.flex-20 {
  flex: 0 0 20%;
  max-width: 20%;
}
.flex-80 {
  flex: 0 0 80%;
  max-width: 80%;
}
.flex-7 {
  flex: 0 0 7.333333%;
  max-width: 7.3333333333%;
}
.flex-56 {
  flex: 0 0 56%;
  max-width: 56%;
}
.mlr-10 {
  margin: 0 10%;
}
.left {
  margin-left: 5%;
}
.left2 {
  margin-left: 2.1%;
}
.title_font {
  font-size: 18px !important;
}
.requiredText {
  :deep(.item-label) {
    margin-left: 10px;
    font-size: 18px !important;
    color: red !important;
  }
}
.widthFix {
  width: 100px;
}
.widthFix2 {
  width: 120px;
}
.mt-f1 {
  margin-top: -15%;
}
.title {
  margin-top: 12px;
  background-color: #fff;
  border: 1px gainsboro solid;
}
.adl-one {
  flex: 0 0 9.1% !important;
  max-width: 9.1% !important;
  border-left: 1px gainsboro solid;
  border-bottom: 1px gainsboro solid;
  border-right: 0 !important;
}
.adl-two {
  flex: 0 0 36.4% !important;
  max-width: 36.4% !important;
}
.adl-three {
  flex: 0 0 9.1% !important;
  max-width: 9.1% !important;
  border-left: 0 !important;
  border-bottom: 1px gainsboro solid;
  border-right: 0 !important;
}
.adl-four {
  border-left: 1px gainsboro solid;
  border-bottom: 1px gainsboro solid;
  border-right: 0 !important  ;
  flex: 0 0 21.9% !important;
  max-width: 21.9% !important;
}
.right {
  margin-right: -5px;
}
.sleepRadio {
  margin-right: 18px !important;
}
</style>
