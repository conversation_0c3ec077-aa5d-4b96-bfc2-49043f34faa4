<script setup lang="ts">
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'
import { Or26678Const } from '../Or26678/Or26678.constants'
import type { CodeType } from '../Or28326/Or28326.type'
import { Or51734Logic } from '../Or51734/Or51734.logic'
import type { Or26678StateType } from './Or26678.type'
import type {
  ImplementationMonitoringMasterSelectInEntity,
  ImplementationMonitoringMasterSelectOutEntity,
  ImplementationMonitoringMasterUpdateInEntity,
  ImplementationMonitoringMasterUpdateOutEntity,
} from '~/repositories/cmn/entities/ImplementationMonitoringMasterEntity.ts'
import {
  useJigyoList,
  useScreenOneWayBind,
  useScreenStore,
  useScreenTwoWayBind,
  useSetupChildProps,
} from '#imports'
import type {
  MonitoringItem,
  Or26678Type,
  Or26678OnewayType,
} from '~/types/cmn/business/components/Or26678Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { Mo00039Items, Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import type { Mo00040Type } from '~/types/business/components/Mo00040Type'
import { ResBodyStatusCode } from '~/constants/api-constants'
import { CmnMCdKbnId } from '~/constants/cmn/system-code-constants'

/**
 * or26678:有機体:実施モニタリングマスタ
 * GUI01249_実施モニタリングマスタ
 *
 * @description
 * 実施モニタリングマスタ
 *
 * <AUTHOR>
 */

/************************************************
 * Props
 ************************************************/
interface Props {
  uniqueCpId: string
  parentUniqueCpId: string
  onewayModelValue: Or26678OnewayType
}
// 引継情報を取得する
const props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()
// 保存Type
const saveType = ref('')
// 事業所のmodelValueの一時変数を定義する
const tmpOffice = ref<string | undefined>()
// コード一時変数
const oldCodeData = {
  svJigyoId: '',
  shisetuId: '',
}
/** 事業所情報 */
const { jigyoListWatch } = useJigyoList()
/** 事業所情報 */
const jigyoInfo = ref({
  houjinId: '',
  shisetuId: '',
  svJigyoId: '',
  svJigyoCd: '',
  jigyoRyakuKnj: '',
})

const local = reactive({
  or26678: {
    implementationMonitoringMasterList: [],
    numberItem: { raidoValue: '' } as MonitoringItem,
    planImportItem: { raidoValue: '' } as MonitoringItem,
    managerItem: { raidoValue: '' } as MonitoringItem,
    frequencyItem: { raidoValue: '' } as MonitoringItem,
    contentOmittedItem: [
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        onewayModelValue: {
          checkboxLabel: t('label.content-blank-omitted-category'),
          showItemLabel: false,
        } as Mo00018OnewayType,
        updateKbn: '',
      },
      {
        checkboxValue: { modelValue: false } as Mo00018Type,
        onewayModelValue: {
          checkboxLabel: t('label.content-same-omitted-category'),
          showItemLabel: false,
        } as Mo00018OnewayType,
        updateKbn: '',
      },
    ],
    inFrameItem: { raidoValue: '' } as MonitoringItem,
  } as Or26678Type,
})

const localOneway = reactive({
  or26678: {
    ...props.onewayModelValue,
  },
})
// 初期化フラグ
let initFlg = false
// 事業所
const or41179 = ref({ uniqueCpId: '' })
// Or21813_有機体:エラーダイアログ
const or21813 = ref({ uniqueCpId: '' })
//Or21814_有機体:確認ダイアログ
const or21814 = ref({ uniqueCpId: '' })

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  return useScreenStore().isEditByUniqueCpId(props.uniqueCpId)
})

/**************************************************
 * Pinia
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or41179Const.CP_ID(0)]: or41179.value,
  [Or21813Const.CP_ID(0)]: or21813.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
})

const { refValue } = useScreenTwoWayBind<Or26678Type>({
  cpId: Or26678Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

// エラーダイアログ表示フラグ
const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})
// 確認ダイアログ表示フラグ
const showDialogOr21814 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

useScreenOneWayBind<Or26678StateType>({
  cpId: Or26678Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    param: (value) => {
      switch (value?.executeFlag) {
        // 保存
        case 'save':
          saveType.value = value?.saveType ?? ''
          // 保存実行
          void save(localOneway.or26678.svJigyoId)
          break
        // データ再取得
        case 'getData':
          void init()
          break
        default:
          break
      }
    },
  },
})

onMounted(async () => {
  // コード項目初期化
  await initCodes()
  // 事業所コード設定
  Or41179Logic.state.set({
    uniqueCpId: or41179.value.uniqueCpId,
    state: {
      searchCriteria: {
        selfId: '0000000123',
      },
    },
  })
  // 事業所選択値を設定
  Or41179Logic.data.set({
    uniqueCpId: or41179.value.uniqueCpId,
    value: {
      modelValue: localOneway.or26678.svJigyoId,
    } as Mo00040Type,
  })
  // 値変更ワッチャーを起動
  setupDataWacth()
})

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * 事業者情報の監視
 */
watch(
  () => jigyoInfo.value,
  async (newValue, oldValue) => {
    if (!oldValue.svJigyoId || !newValue.svJigyoId) return
    // 事業所選択から返却.事業所ID
    localOneway.or26678.svJigyoId = newValue.svJigyoId
    if (newValue.svJigyoId === oldCodeData.svJigyoId) {
      return
    } else {
      await nextTick()
      await showDialog()
    }
  },
  { deep: true }
)

/**
 * 事業者情報プルダウンmodelValueの監視
 */
watch(
  () => Or41179Logic.data.get(or41179.value.uniqueCpId)?.modelValue,
  (_newVal, oldVal) => {
    // oldValueを一時変数に保存できます
    tmpOffice.value = oldVal
  }
)

/**************************************************
 * 関数
 **************************************************/
/**
 * 事業所選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 *  @param newJigyoId - 変更後の事業者ID
 */
function callbackFuncJigyo(newJigyoId: string) {
  if (newJigyoId) {
    const jigyoInfoList = Or41179Logic.state.get(or41179.value.uniqueCpId)?.jigyoInfoList
    const jigyoInfoData = jigyoInfoList?.find((jigyoInfo) => jigyoInfo.svJigyoId === newJigyoId)
    if (jigyoInfoData) {
      jigyoInfo.value = {
        houjinId: jigyoInfoData.houjinId,
        shisetuId: jigyoInfoData.shisetuId,
        svJigyoId: jigyoInfoData.svJigyoId,
        svJigyoCd: jigyoInfoData.svJigyoCd,
        jigyoRyakuKnj: jigyoInfoData.jigyoRyakuKnj,
      }
    }
  }
}
// ★事業所選択監視関数を実行
jigyoListWatch(or41179.value.uniqueCpId, callbackFuncJigyo)

/**
 *  コード処理
 *
 * @param codeList - コードリスト
 *
 * @param cpnFlg - ケアプラン方式
 *
 * @param type - 区分
 */
function setCodes(codeList: CodeType[], cpnFlg: string, type: string) {
  if (codeList) {
    const item = {
      itemType: '',
      raidoValue: '',
      radioOneway: {
        name: '',
        items: [] as Mo00039Items[],
        showItemLabel: false,
        checkOff: false,
      } as Mo00039OnewayType,
      updateKbn: '',
    } as MonitoringItem
    if (codeList.length === 4 && cpnFlg === '5') {
      // 表示項目は（3:長期目標 4:短期目標）を除外する
      codeList.splice(1, 2)
      item.radioOneway.items = codeList
    } else {
      item.radioOneway.items = codeList
    }
    item.itemType = type
    if (type === '1') {
      local.or26678.numberItem = item
    } else if (type === '2') {
      local.or26678.planImportItem = item
    } else if (type === '4') {
      local.or26678.managerItem = item
    } else if (type === '7') {
      local.or26678.frequencyItem = item
    } else if (type === '8') {
      local.or26678.inFrameItem = item
    }
  }
}

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const cmnSystemCodeInEntity = {
    selectCodeKbnList: [
      // コード区分: 80
      { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_IMPLEMENTATION_MONITORING_MASTER_DISPLAY_CATEGORY },
      // コード区分: 81
      { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_IMPLEMENTATION_MONITORING_MASTER_NUMBER_CATEGORY },
      // コード区分: 82
      {
        mCdKbnId:
          CmnMCdKbnId.M_CD_KBN_ID_IMPLEMENTATION_MONITORING_MASTER_IN_FRAME_LINKS_OPERATION_CATEGOR,
      },
    ],
  }

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes(cmnSystemCodeInEntity)

  const code80List = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_IMPLEMENTATION_MONITORING_MASTER_DISPLAY_CATEGORY
  )
  // コード処理
  setCodes(code80List, localOneway.or26678.cpnFlg, '1')
  // コード処理
  setCodes(
    CmnSystemCodeRepository.filter(
      CmnMCdKbnId.M_CD_KBN_ID_IMPLEMENTATION_MONITORING_MASTER_NUMBER_CATEGORY
    ),
    localOneway.or26678.cpnFlg,
    '2'
  )
  // コード処理
  setCodes(code80List, localOneway.or26678.cpnFlg, '4')
  if (localOneway.or26678.cpnFlg !== '5') {
    // コード処理
    setCodes(code80List, localOneway.or26678.cpnFlg, '7')
  }

  // コード処理
  setCodes(
    CmnSystemCodeRepository.filter(
      CmnMCdKbnId.M_CD_KBN_ID_IMPLEMENTATION_MONITORING_MASTER_IN_FRAME_LINKS_OPERATION_CATEGOR
    ),
    localOneway.or26678.cpnFlg,
    '8'
  )
}

/**
 * 初期情報取得
 */
const init = async () => {
  // 実施モニタリングマスタ情報取得(IN)
  const inputData: ImplementationMonitoringMasterSelectInEntity = {
    /**
     * 施設ID
     */
    shisetuId: localOneway.or26678.shisetuId,
    /**
     * 事業者ID
     */
    svJigyoId: localOneway.or26678.svJigyoId,
    /**
     * ケアプラン方式
     */
    cpnFlg: localOneway.or26678.cpnFlg,
  }
  // 確認方法区分マスタ初期情報取得
  const res: ImplementationMonitoringMasterSelectOutEntity = await ScreenRepository.select(
    'implementationMonitoringMasterSelect',
    inputData
  )
  // コードデータ一時保存
  oldCodeData.svJigyoId = localOneway.or26678.svJigyoId
  oldCodeData.shisetuId = localOneway.or26678.shisetuId
  if (res.statusCode === ResBodyStatusCode.SUCCESS) {
    // リストをクリア
    local.or26678.implementationMonitoringMasterList = []
    res.data.implementationMonitoringMasterList.forEach((item) => {
      local.or26678.implementationMonitoringMasterList.push(item)
      //（実施モニタリングマスタ情報.整数分類3 ＝ 1のレコード）
      if (item.bunrui3Id === '1') {
        local.or26678.numberItem.raidoValue = item.intValue
      } else if (item.bunrui3Id === '2') {
        local.or26678.planImportItem.raidoValue = item.intValue
      } else if (item.bunrui3Id === '4') {
        local.or26678.managerItem.raidoValue = item.intValue
      } else if (item.bunrui3Id === '7' && localOneway.or26678.cpnFlg !== '5') {
        local.or26678.frequencyItem.raidoValue = item.intValue
      } else if (item.bunrui3Id === '8') {
        local.or26678.inFrameItem.raidoValue = item.intValue
      } else if (item.bunrui3Id === '5') {
        local.or26678.contentOmittedItem[0].checkboxValue.modelValue =
          item.intValue === '1' ? true : false
      } else if (item.bunrui3Id === '6') {
        local.or26678.contentOmittedItem[1].checkboxValue.modelValue =
          item.intValue === '1' ? true : false
      }
    })
    refValue.value = cloneDeep(local.or26678)
    // RefValueを設定
    setRefValue(refValue.value)
  }
  initFlg = true
}

/**
 *  RefValueを設定
 *
 * @param data - 初期取得された情報
 */
function setRefValue(data: Or26678Type) {
  // APIから取得されたデータでRefValueを更新する
  useScreenStore().setCpTwoWay({
    cpId: Or26678Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: data,
    isInit: true,
  })
}

/**
 *  保存処理
 *
 * @param svJigyoId - 事務所ID
 */
async function save(svJigyoId: string) {
  local.or26678.implementationMonitoringMasterList.forEach((item) => {
    item.svJigyoId = svJigyoId
    if (item.bunrui3Id === '1') {
      item.intValue = local.or26678.numberItem.raidoValue
      item.updateKbn = local.or26678.numberItem.updateKbn
    } else if (item.bunrui3Id === '2') {
      item.intValue = local.or26678.planImportItem.raidoValue
      item.updateKbn = local.or26678.planImportItem.updateKbn
    } else if (item.bunrui3Id === '4') {
      item.intValue = local.or26678.managerItem.raidoValue
      item.updateKbn = local.or26678.managerItem.updateKbn
    } else if (item.bunrui3Id === '7' && localOneway.or26678.cpnFlg !== '5') {
      item.intValue = local.or26678.frequencyItem.raidoValue
      item.updateKbn = local.or26678.frequencyItem.updateKbn
    } else if (item.bunrui3Id === '8') {
      item.intValue = local.or26678.inFrameItem.raidoValue
      item.updateKbn = local.or26678.inFrameItem.updateKbn
    } else if (item.bunrui3Id === '5') {
      item.intValue = local.or26678.contentOmittedItem[0].checkboxValue.modelValue ? '1' : '0'
      item.updateKbn = local.or26678.contentOmittedItem[0].updateKbn
    } else if (item.bunrui3Id === '6') {
      item.intValue = local.or26678.contentOmittedItem[1].checkboxValue.modelValue ? '1' : '0'
      item.updateKbn = local.or26678.contentOmittedItem[1].updateKbn
    }
  })
  // 更新データ作成
  const inputData: ImplementationMonitoringMasterUpdateInEntity = {
    implementationMonitoringMasterList: local.or26678.implementationMonitoringMasterList,
  }
  const resData: ImplementationMonitoringMasterUpdateOutEntity = await ScreenRepository.update(
    'implementationMonitoringMasterUpdate',
    inputData
  )

  // 保存成功の場合
  if (resData.statusCode === ResBodyStatusCode.SUCCESS) {
    // ダイヤログを閉じる
    if (saveType.value === Or26678Const.DEFAULT.CLOSEANDSAVE) {
      Or51734Logic.state.set({
        uniqueCpId: props.parentUniqueCpId,
        state: {
          isOpen: false,
        },
      })
    }
    // タブ変更場合
    else if (saveType.value === Or26678Const.DEFAULT.CHANGETABSAVE) {
      Or51734Logic.event.set({
        uniqueCpId: props.parentUniqueCpId,
        state: {
          isSave: true,
        },
      })
    } else {
      // 画面情報再取得
      await init()
    }
  }
}

/**
 *  値変更ワッチャー
 */
function setupDataWacth() {
  // 番号
  watch(
    () => local.or26678,
    (newValue) => {
      if (!refValue.value) return
      refValue.value = cloneDeep(newValue)
    },
    { deep: true }
  )
}

/**
 * 共通処理の編集権限チェックを行う。
 */
const showDialog = async () => {
  // 該当画面にデータが変更ありの場合
  if (isEdit.value) {
    if (!localOneway.or26678.editFlg) {
      // 変更がある場合、確認ダイアログを表示する。(保存権限がない場合)
      // メッセージ内容：情報を保存する権限がありません。入力内容は破棄されますが、処理を続けますか？
      showOr21814MsgTwoBtn(t('message.i-cmn-10006'))
    } else {
      // 変更がある場合、確認ダイアログを表示する。(保存権限がある場合)
      // 他の項目を選択すると、変更内容は失われます。「改行」変更を保存しますか？
      showOr21814MsgThreeBtn(t('message.i-cmn-10430'))
    }
    return new Promise(() => {
      watch(
        () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
        async () => {
          const event = Or21814Logic.event.get(or21814.value.uniqueCpId)
          // 編集権限なし
          if (!localOneway.or26678.editFlg) {
            // 'はい'
            if (event?.secondBtnClickFlg) {
              await init()
            } else {
              // 事業所プルダウンの事業所IDの復元
              Or41179Logic.data.set({
                uniqueCpId: or41179.value.uniqueCpId,
                value: {
                  modelValue: tmpOffice.value,
                } as Mo00040Type,
              })
              // oneway事業所ID復元
              localOneway.or26678.svJigyoId = oldCodeData.svJigyoId
              return
            }
          } else {
            // 編集権限あり
            // 'はい'
            if (event?.firstBtnClickFlg) {
              // 保存
              await save(oldCodeData.svJigyoId)
            }
            // 'いいえ'
            else if (event?.secondBtnClickFlg) {
              await init()
            }
            // '×'または'キャンセル'
            else {
              // 事業所選択から返却.事業所ID復元
              Or41179Logic.data.set({
                uniqueCpId: or41179.value.uniqueCpId,
                value: {
                  modelValue: tmpOffice.value,
                } as Mo00040Type,
              })
              // oneway事業所ID復元
              localOneway.or26678.svJigyoId = oldCodeData.svJigyoId
              return
            }
          }
          Or21814Logic.event.set({
            uniqueCpId: or21814.value.uniqueCpId,
            events: {
              firstBtnClickFlg: false,
              secondBtnClickFlg: false,
              thirdBtnClickFlg: false,
            },
          })
        },
        { once: true }
      )
    })
  } else {
    if (initFlg) {
      await init()
    }
  }
}

/**
 * 閉じるボタン押下_保存権限がない場合の開閉
 *
 * @param errormsg - Message
 */
function showOr21814MsgTwoBtn(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: errormsg,
      firstBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.no'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 閉じるボタン押下_保存権限がある場合の開閉
 *
 * @param errormsg - Message
 */
function showOr21814MsgThreeBtn(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト0
      dialogText: errormsg,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 番号を変更すると、更新区分設定
 *
 */
function numberItemChange() {
  // 更新区分を設定
  local.or26678.numberItem.updateKbn = Or26678Const.DEFAULT.UPDATE_KBN_U
}
/**
 * 計画取込を変更すると、更新区分設定
 *
 */
function planImportItemChange() {
  local.or26678.planImportItem.updateKbn = Or26678Const.DEFAULT.UPDATE_KBN_U
}

/**
 * 担当者を変更すると、更新区分設定
 *
 */
function managerItemChange() {
  local.or26678.managerItem.updateKbn = Or26678Const.DEFAULT.UPDATE_KBN_U
}

/**
 * 頻度を変更すると、更新区分設定
 *
 */
function frequencyItemChange() {
  local.or26678.frequencyItem.updateKbn = Or26678Const.DEFAULT.UPDATE_KBN_U
}

/**
 * 内容の省略を変更すると、更新区分設定
 *
 */
function contentOmittedItemClick01() {
  local.or26678.contentOmittedItem[0].updateKbn = Or26678Const.DEFAULT.UPDATE_KBN_U
}
/**
 * 内容の省略を変更すると、更新区分設定
 *
 */
function contentOmittedItemClick02() {
  local.or26678.contentOmittedItem[1].updateKbn = Or26678Const.DEFAULT.UPDATE_KBN_U
}

/**
 * 枠内クリックの動作を変更すると、更新区分設定
 *
 */
function inFrameItemChange() {
  local.or26678.inFrameItem.updateKbn = Or26678Const.DEFAULT.UPDATE_KBN_U
}
</script>

<template>
  <c-v-row
    no-gutters
    class="border-bottom"
  >
    <div class="pt-2 pb-2 pl-2">
      <!-- 事業所 -->
      <g-base-or-41179 v-bind="or41179"></g-base-or-41179>
    </div>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col class="pa-2">
      <!-- 番号 -->
      <c-v-row
        class="boder-right"
        no-gutters
      >
        <c-v-col
          cols="3"
          class="d-flex align-center border-left-bottom border-top background-color title"
          ><div class="pl-4">{{ t('label.number') }}</div></c-v-col
        >
        <c-v-col
          cols="9"
          class="d-flex align-center border-left-bottom border-top"
        >
          <div class="pl-2">
            <base-mo00039
              v-model="local.or26678.numberItem.raidoValue"
              :oneway-model-value="local.or26678.numberItem.radioOneway"
              @change="numberItemChange"
            />
          </div>
        </c-v-col>
      </c-v-row>
      <!-- 計画取込 -->
      <c-v-row
        class="boder-right"
        no-gutters
      >
        <c-v-col
          cols="3"
          class="d-flex align-center border-left-bottom background-color title"
          ><div class="pl-4">{{ t('label.plan-import') }}</div></c-v-col
        >
        <c-v-col
          cols="9"
          class="d-flex align-center border-left-bottom"
        >
          <div class="pl-2">
            <base-mo00039
              v-model="local.or26678.planImportItem.raidoValue"
              :oneway-model-value="local.or26678.planImportItem.radioOneway"
              @change="planImportItemChange"
            />
          </div>
        </c-v-col>
      </c-v-row>
      <!-- 担当者 -->
      <c-v-row
        class="boder-right"
        no-gutters
      >
        <c-v-col
          cols="3"
          class="d-flex align-center border-left-bottom background-color title"
          ><div class="pl-4">{{ t('label.using-manager') }}</div></c-v-col
        >
        <c-v-col
          cols="9"
          class="d-flex align-center border-left-bottom"
        >
          <div class="pl-2">
            <base-mo00039
              v-model="local.or26678.managerItem.raidoValue"
              :oneway-model-value="local.or26678.managerItem.radioOneway"
              @change="managerItemChange"
            />
          </div>
        </c-v-col>
      </c-v-row>
      <!-- 頻度 -->
      <c-v-row
        v-if="localOneway.or26678.cpnFlg !== '5'"
        class="boder-right"
        no-gutters
      >
        <c-v-col
          cols="3"
          class="d-flex align-center border-left-bottom background-color title"
          ><div class="pl-4">{{ t('label.frequency') }}</div></c-v-col
        >
        <c-v-col
          cols="9"
          class="d-flex align-center border-left-bottom"
        >
          <div class="pl-2">
            <base-mo00039
              v-model="local.or26678.frequencyItem.raidoValue"
              :oneway-model-value="local.or26678.frequencyItem.radioOneway"
              @change="frequencyItemChange"
            />
          </div>
        </c-v-col>
      </c-v-row>
      <!-- 内容の省略 -->
      <c-v-row
        class="boder-right"
        no-gutters
      >
        <c-v-col
          cols="3"
          class="d-flex align-center border-left-bottom background-color title"
          ><div class="pl-4">{{ t('label.content-omitted') }}</div></c-v-col
        >
        <c-v-col
          cols="9"
          class="d-flex align-center border-left-bottom"
        >
          <div class="d-flex pl-2">
            <base-mo00018
              v-model="local.or26678.contentOmittedItem[0].checkboxValue"
              :oneway-model-value="local.or26678.contentOmittedItem[0].onewayModelValue"
              @click="contentOmittedItemClick01()"
            />
            <base-mo00018
              v-model="local.or26678.contentOmittedItem[1].checkboxValue"
              :oneway-model-value="local.or26678.contentOmittedItem[1].onewayModelValue"
              @click="contentOmittedItemClick02()"
            />
          </div>
        </c-v-col>
      </c-v-row>
      <!-- 枠内クリックの動作 -->
      <c-v-row
        class="boder-right"
        no-gutters
      >
        <c-v-col
          cols="3"
          class="d-flex align-center border-left-bottom background-color title"
          ><div class="pl-4">{{ t('label.in-frame-click-action') }}</div></c-v-col
        >
        <c-v-col
          cols="9"
          class="d-flex align-center border-left-bottom"
        >
          <div class="pl-2">
            <base-mo00039
              v-model="local.or26678.inFrameItem.raidoValue"
              :oneway-model-value="local.or26678.inFrameItem.radioOneway"
              @change="inFrameItemChange"
            />
          </div>
        </c-v-col>
      </c-v-row>
    </c-v-col>
  </c-v-row>
  <!-- 説明:※事業所単位で保存  -->
  <div class="d-flex detail-label">{{ t('label.hoshi-save-by-office-unit') }}</div>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  />
  <!-- Or21813:有機体:エラーダイアログ -->
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  />
</template>

<style scoped lang="scss">
.border-bottom {
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid;
}

.border-top {
  border-top: 1px rgb(var(--v-theme-black-200)) solid;
}
.border-left-bottom {
  border-left: 1px rgb(var(--v-theme-black-200)) solid;
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid;
}
.boder-right {
  border-right: 1px rgb(var(--v-theme-black-200)) solid;
}
.title {
  height: 50px;
  font-weight: bold;
  padding-left: 8px;
}
.detail-label {
  padding-left: 8px;
  padding-top: 132px;
}
</style>
