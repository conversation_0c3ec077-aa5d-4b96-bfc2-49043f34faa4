<script setup lang="ts">
/**
 * Or32309:選定表(アセスメント(インターライ))
 * GUI00883: 選定表(アセスメント(インターライ))
 *
 * @description
 *選定表(アセスメント(インターライ))
 *
 * <AUTHOR> 李博
 */

import { onMounted, reactive, ref, watch, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or51928Logic } from '../../organisms/Or51928/Or51928.logic'
import { Or51928Const } from '../../organisms/Or51928/Or51928.constants'
// import { Or32309Const } from './Or32309.constants'
import type { CountEntity } from './Or32309.type'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { useUsersProfileView, useSetupChildProps, useJigyoList} from '#imports'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or05899Const } from '~/components/base-components/organisms/Or05899/Or05899.constants'
import { Or05899Logic } from '~/components/base-components/organisms/Or05899/Or05899.logic'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import type { Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo01343Type } from '~/types/business/components/Mo01343Type'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { CustomClass } from '~/types/CustomClassType'
import type {
  Or32309OnewayType,
  HistoryInfo,
  PlanPeriodInfo,
} from '~/types/cmn/business/components/Or32309Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Or10894OnewayType } from '~/types/cmn/business/components/Or10894Type'
import type { Mo00038OnewayType } from '~/types/business/components/Mo00038Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Or10320OnewayType, Or10320Type } from '~/types/cmn/business/components/Or10320Type'
import { Or10320Const } from '~/components/custom-components/organisms/Or10320/Or10320.constants'
import type { OrX0001Type, OrX0001OnewayType } from '~/types/cmn/business/components/OrX0001Type'
import { OrX0001Const } from '~/components/custom-components/organisms/OrX0001/OrX0001.constants'
import type { Mo01344OnewayType } from '@/types/business/components/Mo01344Type'
import { OrHeadLineConst } from '~/components/base-components/organisms/OrHeadLine/OrHeadLine.constants'
import { Or00249Const } from '~/components/base-components/organisms/Or00249/Or00249.constants'
import { OrX0006Const } from '~/components/custom-components/organisms/OrX0006/OrX0006.constants'
import type { OrX0006OnewayType } from '~/types/cmn/business/components/OrX0006Type'
import type { OrX0007OnewayType } from '~/types/cmn/business/components/OrX0007Type'
import { OrX0007Const } from '~/components/custom-components/organisms/OrX0007/OrX0007.constants'
import type { PlanCreateDataType } from '~/types/PlanCreateDataType'
import { OrX0008Const } from '~/components/custom-components/organisms/OrX0008/OrX0008.constants'
import type { OrX0009OnewayType } from '~/types/cmn/business/components/OrX0009Type'
import { OrX0009Const } from '~/components/custom-components/organisms/OrX0009/OrX0009.constants'
import type { Or10279OneWayType } from '~/types/cmn/business/components/Or10279Type'
import { Or10894Logic } from '~/components/custom-components/organisms/Or10894/Or10894.logic'
import { Or10894Const } from '~/components/custom-components/organisms/Or10894/Or10894.constants'
import type {
  weeklyTableHistoryInfoSelectEntity,
  weeklyTableHistoryInfoSelectOutEntity,
} from '~/repositories/cmn/entities/weeklyTableHistoryInfoSelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { useUserListInfo } from '~/utils/useUserListInfo'
import type {
  Mo01334Headers,
  Mo01334OnewayType,
  Mo01334Type,
  Mo01334Items,
} from '~/types/business/components/Mo01334Type'

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or32309OnewayType
  uniqueCpId: string
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

// システム共有情報取得
const systemCommonsStore = useSystemCommonsStore()

// 利用者一覧変更監視
const { syscomUserSelectWatchFunc } = useUserListInfo()

// 事業所変更監視
const { jigyoListWatch } = useJigyoList()

const or05899 = ref({ uniqueCpId: '' })
const or00248 = ref({ uniqueCpId: '' })
const orHeadLine = ref({ uniqueCpId: '' })
const or00249 = ref({ uniqueCpId: '' })
const or10320 = ref({ uniqueCpId: Or10320Const.CP_ID(1) })
const orX0001 = ref({ uniqueCpId: OrX0001Const.CP_ID(1) })
const orX0006 = ref({ uniqueCpId: '' })
const orX0007 = ref({ uniqueCpId: '' })
const orX0008 = ref({ uniqueCpId: '' })
const or41179 = ref({ uniqueCpId: '' })
const orX0009 = ref({ uniqueCpId: '' })
const or10279 = ref({ uniqueCpId: '' })
const or51928 = ref({ uniqueCpId: '' })
const or10894 = ref({ uniqueCpId: Or10894Const.CP_ID(1) })

// お気に入りに該当機能
const favorite = ref<boolean>(false)

// 削除フラグ
const deleteFlag = ref<boolean>(false)

// ローカルTwoway
const local = reactive({
  // 作成日
  createDate: {
    value: systemCommonsStore.getSystemDate,
    mo01343: {
      value: systemCommonsStore.getSystemDate,
      mo00024: {
        isOpen: false,
      } as Mo00024Type,
    } as Mo01343Type,
  } as Mo00020Type,
  // 該当数リスト
  countItem: {
    cap1CorrespondingNumber: '0',
    cap2CorrespondingNumber: '0',
    cap3CorrespondingNumber: '0',
    cap4CorrespondingNumber: '0',
    cap5CorrespondingNumber: '0',
    cap6CorrespondingNumber: '0',
    cap7CorrespondingNumber: '0',
    cap8CorrespondingNumber: '0',
    cap9CorrespondingNumber: '0',
    cap10CorrespondingNumber: '0',
    cap11CorrespondingNumber: '0',
    cap12CorrespondingNumber: '0',
    cap13CorrespondingNumber: '0',
    cap14CorrespondingNumber: '0',
    cap15CorrespondingNumber: '0',
    cap16CorrespondingNumber: '0',
    cap17CorrespondingNumber: '0',
    cap18CorrespondingNumber: '0',
    cap19CorrespondingNumber: '0',
    cap20CorrespondingNumber: '0',
    cap21CorrespondingNumber: '0',
    cap22CorrespondingNumber: '0',
    cap23CorrespondingNumber: '0',
    cap24CorrespondingNumber: '0',
    cap25CorrespondingNumber: '0',
    cap26CorrespondingNumber: '0',
    cap27CorrespondingNumber: '0',

    cap1CorrespondingRate: '0',
    cap2CorrespondingRate: '0',
    cap3CorrespondingRate: '0',
    cap4CorrespondingRate: '0',
    cap5CorrespondingRate: '0',
    cap6CorrespondingRate: '0',
    cap7CorrespondingRate: '0',
    cap8CorrespondingRate: '0',
    cap9CorrespondingRate: '0',
    cap10CorrespondingRate: '0',
    cap11CorrespondingRate: '0',
    cap12CorrespondingRate: '0',
    cap13CorrespondingRate: '0',
    cap14CorrespondingRate: '0',
    cap15CorrespondingRate: '0',
    cap16CorrespondingRate: '0',
    cap17CorrespondingRate: '0',
    cap18CorrespondingRate: '0',
    cap19CorrespondingRate: '0',
    cap20CorrespondingRate: '0',
    cap21CorrespondingRate: '0',
    cap22CorrespondingRate: '0',
    cap23CorrespondingRate: '0',
    cap24CorrespondingRate: '0',
    cap25CorrespondingRate: '0',
    cap26CorrespondingRate: '0',
    cap27CorrespondingRate: '0',
  } as CountEntity,
  // フラグ
  flag: {
    // 期間管理
    periodManage: '1',
    // 計画期間が登録されていない
    periodManageRegistration: false,
  },
  // 二回目新規ボタン押下State
  addBtnState: false,
  // 事業所ID
  officeId: '1',
  // 計画対象期間情報ID
  planPeriodId: 1,
  // 計画対象期間情報
  planPeriod: {} as PlanPeriodInfo,
  // 履歴情報ID
  historyId: 1,
  // 履歴情報
  history: {} as HistoryInfo,
  // 計画期間総件数
  totalCount: 0,
  // 認知能力
  degreeAbility: 5,
  // 法人ID
  houjinId: '',
  // 施設ID
  shisetuId: '',
  // 利用者ID
  userId: '',
  // 事業者ID
  svJigyoId: '',
  // アセスメントID
  raiId: '',
  // 基準日
  kijunbiYmd: '',
  // 作成者ID
  sakuseiId: '',
  historyModifiedCnt: '',
})

// ローカルOneway
const localOneway = reactive({
  //事業所
  orX0006Oneway: {} as OrX0006OnewayType,
  orX0007Oneway: { planTargetPeriodData: { currentIndex: 0, totalCount: 0 } } as OrX0007OnewayType,
  //作成者
  orX0009Oneway: {
    createData: {} as PlanCreateDataType,
  } as OrX0009OnewayType,
  // or10320単方向バインド
  or10320Oneway: {
    facilityId: '',
    jigyoId: '',
    tekiyouOfficeIdList: [],
    tekiyouOfficeId: '',
    jigyoGrouptekiyouId: '',
  } as Or10320OnewayType,
  Or10894Model: {
    cpnFlg: '',
    rssmtId: '',
    capType: '',
  } as Or10894OnewayType,
  Or10279Model: {
    staffId: systemCommonsStore.getStaffId,
    baseDate: systemCommonsStore.getSystemDate,
    officeId: systemCommonsStore.getStaffId,
  } as Or10279OneWayType,
  mo013380title: {
    value: '该当数',
    valueFontWeight: '',
    customClass: { outerClass: 'background-transparent pa-2' } as CustomClass,
  } as Mo01338OnewayType,
  mo01338tag: {
    min: 0,
    max: 99,
    showItemLabel: false,
    isVerticalLabel: false,
    mo00045Oneway: {
      width: '40px',
      customClass: { outerClass: 'background-Gray mr-2' } as CustomClass,
      isVerticalLabel: false,
      showItemLabel: false,
      maxLength: '1',
    } as Mo00045OnewayType,
  } as Mo00038OnewayType,
})

/**
 * 分子：表
 * 単方向バインドモデルのローカル変数
 */
const mo01334Oneway = reactive<Mo01334OnewayType>({
  /** 初期値：ヘッダー情報 */
  headers: [
    {
      title: t('選定日'),
      key: 'capDateYmd',
      sortable: false,
      width: '54',
    },
    {
      title: t('種別'),
      key: 'capType',
      sortable: false,
      width: '54',
    },
    {
      title: t('尺度'),
      key: 'scale',
      sortable: false,
      width: '54',
      children: [
        { title: 'BMI', key: 'scaleBmi', sortable: false, width: '45' },
        { title: 'DRS', key: 'scaleDrs', sortable: false, width: '45' },
        { title: 'PS', key: 'scalePs', sortable: false, width: '45' },
        { title: 'CPS', key: 'scaleCps', sortable: false, width: '45' },
        { title: 'ADL-H', key: 'scaleAdlh', sortable: false, width: '45' },
      ],
    },
    {
      title: t('领域'),
      key: 'field',
      sortable: false,
      width: '810',
      children: [
        {
          title: '械能面',
          key: 'kikaiEnerugī',
          sortable: false,
          width: '180',
          children: [
            { title: '1', key: 'cap1DisplayContents', sortable: false, width: '30' },
            { title: '2', key: 'cap2DisplayContents', sortable: false, width: '30' },
            { title: '3', key: 'cap3DisplayContents', sortable: false, width: '30' },
            { title: '4', key: 'cap4DisplayContents', sortable: false, width: '30' },
            { title: '5', key: 'cap5DisplayContents', sortable: false, width: '30' },
            { title: '6', key: 'cap6DisplayContents', sortable: false, width: '30' },
          ],
        },
        {
          title: '精神面',
          key: 'Spirit',
          sortable: false,
          width: '180',
          children: [
            { title: '7', key: 'cap7DisplayContents', sortable: false, width: '30' },
            { title: '8', key: 'cap8DisplayContents', sortable: false, width: '30' },
            { title: '9', key: 'cap9DisplayContents', sortable: false, width: '30' },
            { title: '10', key: 'cap10DisplayContents', sortable: false, width: '30' },
            { title: '11', key: 'cap11DisplayContents', sortable: false, width: '30' },
            { title: '12', key: 'cap12DisplayContents', sortable: false, width: '30' },
          ],
        },
        {
          title: '社会面',
          key: 'society',
          sortable: false,
          width: '90',
          children: [
            { title: '13', key: 'cap13DisplayContents', sortable: false, width: '30' },
            { title: '14', key: 'cap14DisplayContents', sortable: false, width: '30' },
            { title: '15', key: 'cap15DisplayContents', sortable: false, width: '30' },
          ],
        },
        {
          title: '床面',
          key: 'bed',
          sortable: false,
          width: '360',
          children: [
            { title: '16', key: 'cap16DisplayContents', sortable: false, width: '30' },
            { title: '17', key: 'cap17DisplayContents', sortable: false, width: '30' },
            { title: '18', key: 'cap18DisplayContents', sortable: false, width: '30' },
            { title: '19', key: 'cap19DisplayContents', sortable: false, width: '30' },
            { title: '20', key: 'cap20DisplayContents', sortable: false, width: '30' },
            { title: '21', key: 'cap21DisplayContents', sortable: false, width: '30' },
            { title: '22', key: 'cap22DisplayContents', sortable: false, width: '30' },
            { title: '23', key: 'cap23DisplayContents', sortable: false, width: '30' },
            { title: '24', key: 'cap24DisplayContents', sortable: false, width: '30' },
            { title: '25', key: 'cap25DisplayContents', sortable: false, width: '30' },
            { title: '26', key: 'cap26DisplayContents', sortable: false, width: '30' },
            { title: '27', key: 'cap27DisplayContents', sortable: false, width: '30' },
          ],
        },
      ],
    },
    {
      title: t('合计'),
      key: 'total',
      sortable: false,
      width: '30',
    },
  ] as unknown as Mo01334Headers[],
  items: Array<Mo01334Items>(),
  showPaginationTopFlg: false,
  showPaginationBottomFlg: false,
  height: '250px',
}) as unknown as Mo01334OnewayType

// Or10320双方向バインド
const or10320Type = ref<Or10320Type>({
  data: '',
})

// 事業所IDバックアップ
const jigyoId = ref('')

// メッセージ「i-cmn-11260」 - 削除確認画面
const orX0001Type = ref<OrX0001Type>({
  emitType: 'clickYes',
  selectValue: '',
})

// OrX0001単方向バインド -  削除確認画面
// メッセージ「i-cmn-11260」
const orX0001Oneway = ref<OrX0001OnewayType>({
  icon: 'help',
  iconColor: 'rgb(var(--v-theme-black-500))',
  iconSize: '50px',
  msg: '',
  items: [],
  btnYesDisplay: true,
  btnNoDisplay: false,
  btnCancelDisplay: true,
  mo00024Oneway: {
    persistent: true,
    showCloseBtn: true,
    class: 'mr-1',
    width: '788px',
    mo01344Oneway: {
      toolbarTitle: t('label.confirm-deletion'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
    } as Mo01344OnewayType,
  } as Mo00024OnewayType,
  mo00609OnewayYes: {
    class: 'mr-1',
    name: 'deleteDialogYes',
    btnLabel: t('btn.confirm'),
    width: '60px',
  } as Mo00609OnewayType,
  mo00611OnewayCancel: {
    class: 'mr-1',
    name: 'deleteDialogCancel',
    btnLabel: t('btn.cancel'),
    width: '80px',
    variant: 'tonal',
    color: 'gray',
  } as Mo00611OnewayType,
})

const tableData = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

// ダイアログ表示フラグ
const showDialogOr51928 = computed(() => {
  // Or51928のダイアログ開閉状態
  return Or51928Logic.state.get(or51928.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * ライフサイクルフック
 **************************************************/

onMounted(() => {
  // コントロール設定
  void init()
})

/**************************************************
 * Pinia
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or05899Const.CP_ID]: or05899.value,
  [Or00248Const.CP_ID(1)]: or00248.value,
  [OrX0006Const.CP_ID(1)]: orX0006.value,
  [OrX0007Const.CP_ID(1)]: orX0007.value,
  [OrX0008Const.CP_ID(1)]: orX0008.value,
  [Or41179Const.CP_ID(0)]: or41179.value,
  [OrX0009Const.CP_ID(1)]: orX0009.value,
  [Or10894Const.CP_ID(1)]: or10894.value,
  [Or51928Const.CP_ID(1)]: or51928.value,
})

useSetupChildProps(or00248.value.uniqueCpId, {
  [OrHeadLineConst.CP_ID]: orHeadLine.value,
  [Or00249Const.CP_ID(1)]: or00249.value,
})

// 利用者選択用の共通処理を呼び出す
useUsersProfileView({
  or00248UniqCpId: or00248.value.uniqueCpId,
  or00249UniqCpId: or00249.value.uniqueCpId,
  orHeadLineUniqCpId: orHeadLine.value.uniqueCpId,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/

// 子コンポーネントに対して初期設定を行う
Or05899Logic.state.set({
  uniqueCpId: or05899.value.uniqueCpId,
  state: {
    screenTitleLabel: t('label.corresponding-table'),
    showFavorite: true,
    showViewSelect: false,
    viewSelectItems: [],
    showSaveBtn: false,
    showReloadBtn: false,
    showCreateBtn: false,
    showCreateMenuCopy: false,
    showPrintBtn: true,
    showMasterBtn: false,
    showOptionMenuBtn: true,
    showOptionMenuDelete: false,
  },
})

systemCommonsStore.setUserSelectZenkaiSvJigyoIdList([''])

// ★利用者選択監視関数を実行
syscomUserSelectWatchFunc(callbackUserChange, props.uniqueCpId)

// ★事業所選択監視関数を実行
jigyoListWatch(or41179.value.uniqueCpId, callbackFuncJigyo)

/**
 * 利用者選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newSelfId - 変更後の利用者番号
 */
function callbackUserChange(newSelfId: string) {
  if (newSelfId !== '') {
    local.userId = newSelfId
  } else {
    return
  }

  // 画面情報再取得
  void getInitDataInfo
}

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or05899Logic.event.get(or05899.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.favoriteEventFlg) {
      // お気に入りボタンが押下された場合、お気に入り処理を実行する
      favoriteIconClick()
      setOr05899Event({ favoriteEventFlg: false })
    }
    if (newValue.printEventFlg) {
      // 実行無効制御
      if (!deleteFlag.value) {
        // 印刷ボタンが押下された場合、印刷設定画面を表示する
        void openPrintDialog()
        setOr05899Event({ printEventFlg: false })
      }
    }
    if (newValue.masterEventFlg) {
      // マスタ他ボタンが押下された場合、マスタ他設定画面を表示する
      setOr05899Event({ masterEventFlg: false })
    }
  }
)

/**************************************************
 * 関数
 **************************************************/

/**
 * メニューエベントを設定
 *
 * @param event - イベント
 */
const setOr05899Event = (event: Record<string, boolean>) => {
  Or05899Logic.event.set({
    uniqueCpId: or05899.value.uniqueCpId,
    events: event,
  })
}

/**
 * 初期表示
 */
const init = async () => {
  // 事業所選択リストを初期化
  Or41179Logic.state.set({
    uniqueCpId: or41179.value.uniqueCpId,
    state: {
      searchCriteria: {
        selfId: systemCommonsStore.getUserSelectSelfId(),
      },
    },
  })
  await getInitDataInfo()
}

/** 初期情報取得 */
const getInitDataInfo = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: weeklyTableHistoryInfoSelectEntity = {
    anHoujinId: systemCommonsStore.getHoujinId ?? '',
    anShisetuId: systemCommonsStore.getShisetuId!,
    anSvJigyoId: local.svJigyoId,
    anUserid: systemCommonsStore.getUserId!,
  }

  const resp: weeklyTableHistoryInfoSelectOutEntity = await ScreenRepository.select(
    'correspondingTableAssessmentInterrai',
    inputData
  )
  const { cpnTucRaiList } = resp.data
  console.log(cpnTucRaiList, 'cpnTucRaiList')

  mo01334Oneway.items = cpnTucRaiList.map((item) => {
    return {
      ...item,
      id: item.raiId,
    }
  })
  cpnTucRaiList.forEach((item) => {
    local.countItem.cap1CorrespondingNumber = item.cap1CorrespondingNumber
    local.countItem.cap1CorrespondingRate = item.cap1CorrespondingRate

    local.countItem.cap2CorrespondingNumber = item.cap2CorrespondingNumber
    local.countItem.cap2CorrespondingRate = item.cap2CorrespondingRate

    local.countItem.cap3CorrespondingNumber = item.cap3CorrespondingNumber
    local.countItem.cap3CorrespondingRate = item.cap3CorrespondingRate

    local.countItem.cap4CorrespondingNumber = item.cap4CorrespondingNumber
    local.countItem.cap4CorrespondingRate = item.cap4CorrespondingRate

    local.countItem.cap5CorrespondingNumber = item.cap5CorrespondingNumber
    local.countItem.cap5CorrespondingRate = item.cap5CorrespondingRate

    local.countItem.cap6CorrespondingNumber = item.cap6CorrespondingNumber
    local.countItem.cap6CorrespondingRate = item.cap6CorrespondingRate

    local.countItem.cap7CorrespondingNumber = item.cap7CorrespondingNumber
    local.countItem.cap7CorrespondingRate = item.cap7CorrespondingRate

    local.countItem.cap8CorrespondingNumber = item.cap8CorrespondingNumber
    local.countItem.cap8CorrespondingRate = item.cap8CorrespondingRate

    local.countItem.cap9CorrespondingNumber = item.cap9CorrespondingNumber
    local.countItem.cap9CorrespondingRate = item.cap9CorrespondingRate

    local.countItem.cap10CorrespondingNumber = item.cap10CorrespondingNumber
    local.countItem.cap10CorrespondingRate = item.cap10CorrespondingRate

    local.countItem.cap11CorrespondingNumber = item.cap11CorrespondingNumber
    local.countItem.cap11CorrespondingRate = item.cap11CorrespondingRate

    local.countItem.cap12CorrespondingNumber = item.cap12CorrespondingNumber
    local.countItem.cap12CorrespondingRate = item.cap12CorrespondingRate

    local.countItem.cap13CorrespondingNumber = item.cap13CorrespondingNumber
    local.countItem.cap13CorrespondingRate = item.cap13CorrespondingRate

    local.countItem.cap14CorrespondingNumber = item.cap14CorrespondingNumber
    local.countItem.cap14CorrespondingRate = item.cap14CorrespondingRate

    local.countItem.cap15CorrespondingNumber = item.cap15CorrespondingNumber
    local.countItem.cap15CorrespondingRate = item.cap15CorrespondingRate

    local.countItem.cap16CorrespondingNumber = item.cap16CorrespondingNumber
    local.countItem.cap16CorrespondingRate = item.cap16CorrespondingRate

    local.countItem.cap17CorrespondingNumber = item.cap17CorrespondingNumber
    local.countItem.cap17CorrespondingRate = item.cap17CorrespondingRate

    local.countItem.cap18CorrespondingNumber = item.cap18CorrespondingNumber
    local.countItem.cap18CorrespondingRate = item.cap18CorrespondingRate

    local.countItem.cap19CorrespondingNumber = item.cap19CorrespondingNumber
    local.countItem.cap19CorrespondingRate = item.cap19CorrespondingRate

    local.countItem.cap20CorrespondingNumber = item.cap20CorrespondingNumber
    local.countItem.cap20CorrespondingRate = item.cap20CorrespondingRate

    local.countItem.cap21CorrespondingNumber = item.cap21CorrespondingNumber
    local.countItem.cap21CorrespondingRate = item.cap21CorrespondingRate

    local.countItem.cap22CorrespondingNumber = item.cap22CorrespondingNumber
    local.countItem.cap22CorrespondingRate = item.cap22CorrespondingRate

    local.countItem.cap23CorrespondingNumber = item.cap23CorrespondingNumber
    local.countItem.cap23CorrespondingRate = item.cap23CorrespondingRate

    local.countItem.cap24CorrespondingNumber = item.cap24CorrespondingNumber
    local.countItem.cap24CorrespondingRate = item.cap24CorrespondingRate

    local.countItem.cap25CorrespondingNumber = item.cap25CorrespondingNumber
    local.countItem.cap25CorrespondingRate = item.cap25CorrespondingRate

    local.countItem.cap26CorrespondingNumber = item.cap26CorrespondingNumber
    local.countItem.cap26CorrespondingRate = item.cap26CorrespondingRate

    local.countItem.cap27CorrespondingNumber = item.cap27CorrespondingNumber
    local.countItem.cap27CorrespondingRate = item.cap27CorrespondingRate
  })
}
/**
 * AC002_「お気に入りアイコンボタン」押下
 */
const favoriteIconClick = () => {
  // TODO お気に入りに該当機能がない場合
  if (favorite.value) {
    // 「アセスメント(インターライ)」をお気に入り機能に追加する。
  }
  // お気に入りに該当機能がある場合
  else {
    // 「アセスメント(インターライ)」をお気に入り機能から削除する。
  }
}

/**
 * AC010_「CSV」押下
 */
const csvClick = () => {
  // GUI00788 ［アセスメント（インターライ）CSV出力］画面をポップアップで起動する

  // Or10894のダイアログ開閉状態を更新する
  Or10894Logic.state.set({
    uniqueCpId: or10894.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 事業所選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newJigyoId - 変更後の事業者ID
 */
function callbackFuncJigyo(newJigyoId: string) {
  if (jigyoId.value === '') {
    jigyoId.value = newJigyoId
    return
  }

  // 事業所変更がない場合、スキップ
  if (newJigyoId === jigyoId.value) {
    return
  }

  jigyoId.value = newJigyoId

  // 画面情報再取得
  void getInitDataInfo
}

/**
 * 印刷画面を開く
 *
 */
function openPrintDialog() {
 // Or51928のダイアログ開閉状態を更新する
  Or51928Logic.state.set({
    uniqueCpId: or51928.value.uniqueCpId,
    state: {
      isOpen: true,
      param: {
        /**
         * 処理年月日
         */
        processYmd: '2024/11/11',
        /**
         * 基準日
         */
        basicDate: '2024/10/11',
        /**
         * セクション名
         */
        sectionName: '',
        /**
         * 施設ID
         */
        shisetuId: '',
        /**
         * 事業者ID
         */
        svJigyoId: '',
        /**
         * 利用者ID
         */
        userId: '2',
        /**
         * 履歴ID
         */
        assId: '1',
        /**
         * 担当者ID
         */
        tantoId: '',
        /**
         * 計画書様式
         */
        cksFlg: '3',
        /**
         * 法人ID
         */
        houjinId: '',
        /**
         * 職員ID
         */
        shokuId: '',
        /**
         * フォーカス設定用イニシャル
         */
        focusSettingInitial: [],
        /**
         * 担当者カウンタ値
         */
        selectedUserCounter: '2',
      },
    },
  })
}
</script>

<template>
  <c-v-sheet class="view">
    <g-base-or05899 v-bind="or05899">
      <template #optionMenuItems>
        <c-v-list-item
          :title="t('label.incentives-corresponding')"
          prepend-icon="open_in_browser"
          @click="csvClick"
        >
        </c-v-list-item>
      </template>
    </g-base-or05899>

    <c-v-row
      no-gutters
      class="main-Content d-flex flex-0-1 h-100 overflow-hidden"
    >
      <!-- ナビゲーションエリア -->
      <c-v-col
        cols="auto"
        class="hidden-scroll h-100 pa-3"
      >
        <!-- 利用者選択一覧 -->
        <g-base-or00248 v-bind="or00248" />
      </c-v-col>
      <!-- コンテンツエリア -->
      <c-v-col class="main-right d-flex flex-column overflow-hidden h-100">
        <!-- 上段 -->
        <c-v-row no-gutters>
          <!-- 事業所 -->
          <c-v-row
            no-gutters
            class="pl-2"
          >
            <!-- 事業所選択画面 -->
            <g-base-or-41179 v-bind="or41179" />
          </c-v-row>
        </c-v-row>
        <!-- 中段 -->
        <c-v-col>
          <div class="table-header">
            <base-mo-01334
              v-model="tableData"
              :oneway-model-value="mo01334Oneway"
              class="list-wrapper"
              hide-default-footer
            >
              <template #[`item.capDateYmd`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.capDateYmd }" />
              </template>
              <template #[`item.capType`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.capType }" />
              </template>
              <template #[`item.scaleBmi`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.scaleBmi }" />
              </template>
              <template #[`item.scaleDrs`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.scaleDrs }" />
              </template>
              <template #[`item.scalePs`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.scalePs }" />
              </template>
              <template #[`item.scaleCps`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.scaleCps }" />
              </template>
              <template #[`item.scaleAdlh`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.scaleAdlh }" />
              </template>
              <template #[`item.cap1DisplayContents`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.cap1DisplayContents }" />
              </template>
              <template #[`item.cap2DisplayContents`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.cap2DisplayContents }" />
              </template>
              <template #[`item.cap3DisplayContents`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.cap3DisplayContents }" />
              </template>
              <template #[`item.cap4DisplayContents`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.cap4DisplayContents }" />
              </template>
              <template #[`item.cap5DisplayContents`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.cap5DisplayContents }" />
              </template>
              <template #[`item.cap6DisplayContents`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.cap6DisplayContents }" />
              </template>
              <template #[`item.cap7DisplayContents`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.cap7DisplayContents }" />
              </template>
              <template #[`item.cap8DisplayContents`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.cap8DisplayContents }" />
              </template>
              <template #[`item.cap9DisplayContents`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.cap9DisplayContents }" />
              </template>
              <template #[`item.cap10DisplayContents`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.cap10DisplayContents }" />
              </template>
              <template #[`item.cap11DisplayContents`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.cap11DisplayContents }" />
              </template>
              <template #[`item.cap12DisplayContents`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.cap12DisplayContents }" />
              </template>
              <template #[`item.cap13DisplayContents`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.cap13DisplayContents }" />
              </template>
              <template #[`item.cap14DisplayContents`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.cap14DisplayContents }" />
              </template>
              <template #[`item.cap15DisplayContents`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.cap15DisplayContents }" />
              </template>
              <template #[`item.cap16DisplayContents`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.cap16DisplayContents }" />
              </template>
              <template #[`item.cap17DisplayContents`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.cap17DisplayContents }" />
              </template>
              <template #[`item.cap18DisplayContents`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.cap18DisplayContents }" />
              </template>
              <template #[`item.cap19DisplayContents`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.cap19DisplayContents }" />
              </template>
              <template #[`item.cap20DisplayContents`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.cap20DisplayContents }" />
              </template>
              <template #[`item.cap21DisplayContents`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.cap21DisplayContents }" />
              </template>
              <template #[`item.cap22DisplayContents`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.cap22DisplayContents }" />
              </template>
              <template #[`item.cap23DisplayContents`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.cap23DisplayContents }" />
              </template>
              <template #[`item.cap24DisplayContents`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.cap24DisplayContents }" />
              </template>
              <template #[`item.cap25DisplayContents`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.cap25DisplayContents }" />
              </template>
              <template #[`item.cap26DisplayContents`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.cap26DisplayContents }" />
              </template>
              <template #[`item.cap27DisplayContents`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.cap27DisplayContents }" />
              </template>
              <template #[`item.total`]="{ item }">
                <base-mo01337 :oneway-model-value="{ value: item.rowTotal }" />
              </template>
            </base-mo-01334>
            <div
              class="backgroundcolor"
              style="width: 100%; overflow-x: auto; margin-top: 400px"
            >
              <!-- 外层横向排列 -->
              <div style="display: flex; height: 65px; min-width: 2046px">
                <!-- 左边区域，两行 -->
                <div
                  class="border-all-1 backgroundcolor"
                  style="width: 435px; background-color: #f0f0f0"
                >
                  <div
                    class="border-all-1"
                    style="width: 435px; height: 32px"
                  >
                    該当数
                  </div>
                  <div
                    class="border-all-1"
                    style="width: 435px; height: 32px"
                  >
                    該当率(%)
                  </div>
                </div>
                <!-- 右边区域，每项两行 -->
                <div
                  class="d-flex"
                  style="width: 1556px"
                >
                  <!-- cap1 -->
                  <div style="width: 58px">
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap1CorrespondingNumber }}
                    </div>
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap1CorrespondingRate }}
                    </div>
                  </div>
                  <!-- cap2 -->
                  <div style="width: 58px">
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap2CorrespondingNumber }}
                    </div>
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap2CorrespondingRate }}
                    </div>
                  </div>

                  <!-- cap3 -->
                  <div style="width: 58px">
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap3CorrespondingNumber }}
                    </div>
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap3CorrespondingRate }}
                    </div>
                  </div>

                  <!-- cap4 -->
                  <div style="width: 58px">
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap4CorrespondingNumber }}
                    </div>
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap4CorrespondingRate }}
                    </div>
                  </div>

                  <!-- cap5 -->
                  <div style="width: 58px">
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap5CorrespondingNumber }}
                    </div>
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap5CorrespondingRate }}
                    </div>
                  </div>

                  <!-- cap6 -->
                  <div style="width: 58px">
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap6CorrespondingNumber }}
                    </div>
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap6CorrespondingRate }}
                    </div>
                  </div>

                  <!-- cap7 -->
                  <div style="width: 58px">
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap7CorrespondingNumber }}
                    </div>
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap7CorrespondingRate }}
                    </div>
                  </div>

                  <!-- cap8 -->
                  <div style="width: 58px">
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap8CorrespondingNumber }}
                    </div>
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap8CorrespondingRate }}
                    </div>
                  </div>

                  <!-- cap9 -->
                  <div style="width: 58px">
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap9CorrespondingNumber }}
                    </div>
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap9CorrespondingRate }}
                    </div>
                  </div>

                  <!-- cap10 -->
                  <div style="width: 58px">
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap10CorrespondingNumber }}
                    </div>
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap10CorrespondingRate }}
                    </div>
                  </div>

                  <!-- cap11 -->
                  <div style="width: 58px">
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap11CorrespondingNumber }}
                    </div>
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap11CorrespondingRate }}
                    </div>
                  </div>

                  <!-- cap12 -->
                  <div style="width: 58px">
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap12CorrespondingNumber }}
                    </div>
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap12CorrespondingRate }}
                    </div>
                  </div>

                  <!-- cap13 -->
                  <div style="width: 58px">
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap13CorrespondingNumber }}
                    </div>
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap13CorrespondingRate }}
                    </div>
                  </div>

                  <!-- cap14 -->
                  <div style="width: 58px">
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap14CorrespondingNumber }}
                    </div>
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap14CorrespondingRate }}
                    </div>
                  </div>

                  <!-- cap15 -->
                  <div style="width: 58px">
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap15CorrespondingNumber }}
                    </div>
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap15CorrespondingRate }}
                    </div>
                  </div>

                  <!-- cap16 -->
                  <div style="width: 58px">
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap16CorrespondingNumber }}
                    </div>
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap16CorrespondingRate }}
                    </div>
                  </div>

                  <!-- cap17 -->
                  <div style="width: 58px">
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap17CorrespondingNumber }}
                    </div>
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap17CorrespondingRate }}
                    </div>
                  </div>

                  <!-- cap18 -->
                  <div style="width: 58px">
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap18CorrespondingNumber }}
                    </div>
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap18CorrespondingRate }}
                    </div>
                  </div>

                  <!-- cap19 -->
                  <div style="width: 58px">
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap19CorrespondingNumber }}
                    </div>
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap19CorrespondingRate }}
                    </div>
                  </div>

                  <!-- cap20 -->
                  <div style="width: 58px">
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap20CorrespondingNumber }}
                    </div>
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap20CorrespondingRate }}
                    </div>
                  </div>

                  <!-- cap21 -->
                  <div style="width: 58px">
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap21CorrespondingNumber }}
                    </div>
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap21CorrespondingRate }}
                    </div>
                  </div>

                  <!-- cap22 -->
                  <div style="width: 58px">
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap22CorrespondingNumber }}
                    </div>
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap22CorrespondingRate }}
                    </div>
                  </div>

                  <!-- cap23 -->
                  <div style="width: 58px">
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap23CorrespondingNumber }}
                    </div>
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap23CorrespondingRate }}
                    </div>
                  </div>

                  <!-- cap24 -->
                  <div style="width: 58px">
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap24CorrespondingNumber }}
                    </div>
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap24CorrespondingRate }}
                    </div>
                  </div>

                  <!-- cap25 -->
                  <div style="width: 58px">
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap25CorrespondingNumber }}
                    </div>
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap25CorrespondingRate }}
                    </div>
                  </div>

                  <!-- cap26 -->
                  <div style="width: 58px">
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap26CorrespondingNumber }}
                    </div>
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap26CorrespondingRate }}
                    </div>
                  </div>

                  <!-- cap27 -->
                  <div style="width: 58px">
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap27CorrespondingNumber }}
                    </div>
                    <div
                      class="border-all-1"
                      style="
                        height: 32px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                      "
                    >
                      {{ local.countItem.cap27CorrespondingRate }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </c-v-col>
        <!-- 下段 -->
        <c-v-row no-gutters> </c-v-row>
      </c-v-col>
    </c-v-row>
    <c-v-row
      no-gutters
      class="footer"
    >
      <c-v-col>
        <g-base-or00051 />
      </c-v-col>
    </c-v-row>
  </c-v-sheet>

  <g-custom-or-10320
    v-bind="or10320"
    v-model="or10320Type"
    :oneway-model-value="localOneway.or10320Oneway"
  />

  <!-- GUI00876 ［誘因項目該当表］画面-->
  <g-custom-or-10894 v-bind="or10894" />

  <!-- GUI00788 ［アセスメント（インターライ）CSV出力］画面-->
  <g-custom-or-10279
    v-bind="or10279"
    :oneway-model-value="localOneway.Or10279Model"
  />

  <g-custom-or-x-0001
    v-bind="orX0001"
    v-model="orX0001Type"
    :oneway-model-value="orX0001Oneway"
  />
  <!-- 印刷画面 -->
  <g-custom-or-51928
    v-if="showDialogOr51928"
    v-bind="or51928"
  />
</template>

<style scoped lang="scss">
:deep(.pl-4) {
  padding-left: 0px !important;
}

.view {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: transparent;
}

.main-Content {
  .main-left {
    max-width: 20%;
  }

  .main-right {
    .middleContent {
      min-height: 0;

      .v-window {
        width: 100%;
      }
    }
  }
}

.createDateOuterClass {
  background: rgb(var(--v-theme-background));
}

.tabItems {
  border-top: thin solid rgb(var(--v-theme-form));
  margin-top: 8px;
}
.no-scrollbar {
  overflow-x: hidden !important;
}
.table-header :deep(.v-table__wrapper th) {
  background-color: rgb(var(--v-theme-black-100)) !important;
  border: 1px rgb(var(--v-theme-black-200)) solid !important;
  font-size: 14px;
  padding: 0 4px;
  width: 40px;
  font-weight: bold;
}

:deep(.table-header td) {
  border: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-color: rgb(var(--v-theme-black-100));
  font-size: 14px;
}
.header {
  :deep(.v-sheet) {
    background-color: transparent !important;
  }
}
.border-all-1 {
  border: 1px solid rgba(var(--v-theme-black-200), 0.7);
  width: 58px;
}
.background-color {
  background-color: rgba(var(--v-theme-green-100));
}
.titelehead {
  height: 26px;
  width: 400px;
}
.titlecontent {
  width: 20px;
  height: 26px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.backgroundcolor {
  background-color: #c6e0b4 !important;
}
</style>
