/**
 * Or60400:有機体:週間計画一括取込
 * GUI00614_週間計画一括取込
 *
 * @description
 * GUI00614_週間計画一括取込
 *
 * <AUTHOR>
 */
import { HttpResponse } from 'msw'
import defaultData from './data/default.json'

import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { weekPlanBundleImportInfoSelectInEntity } from '~/repositories/cmn/entities/weekPlanBundleImportInfoEntity'
type DefaultDataType = {
  res1: object
  res2: object
  res3: object
}

/**
 * GUI00614_週間計画一括取込
 *
 * @description
 * dataName："weekPlanBundleImportInfoSelect"
 */
export function handler(inEntity: weekPlanBundleImportInfoSelectInEntity) {
  const typedDefaultData = defaultData as DefaultDataType
  const key = `res${Math.floor(Math.random() * 3) + 1}` as keyof DefaultDataType
  const result = typedDefaultData[key]

  const responceJson: BaseResponseBody = {
    statusCode: 'success',
    ...result,
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
