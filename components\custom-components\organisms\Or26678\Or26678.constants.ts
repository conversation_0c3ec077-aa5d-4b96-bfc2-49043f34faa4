import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * Or26678:有機体:実施モニタリングマスタ
 *
 * @description
 * 静的データ
 *
 * <AUTHOR>
 */
export namespace Or26678Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or26678', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * タブID
     */
    export const TAB_ID = '1'
    /**
     * コード区分: 80
     */
    export const SELECT_CD_KBN_80 = 80
    /**
     * コード区分: 81
     */
    export const SELECT_CD_KBN_81 = 81
    /**
     * コード区分: 82
     */
    export const SELECT_CD_KBN_82 = 82
    /**
     * 更新区分 更新: 'U'
     */
    export const UPDATE_KBN_U = 'U'
    /**
     * 保存のみ
     */
    export const ONLYSAVE = 'onlySave'
    /**
     * 閉じして保存
     */
    export const CLOSEANDSAVE = 'closeAndSave'
    /**
     * タブ切替で保存
     */
    export const CHANGETABSAVE = 'changetabSave'
  }
}
