<script setup lang="ts">
/**
 * GUI01254:有機体:印刷設定
 * GUI01254_印刷設定
 *
 * @description
 * 印刷設定
 *
 * <AUTHOR>
 */
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or57074Const } from '~/components/custom-components/organisms/Or57074/Or57074.constants'
import { Or57074Logic } from '~/components/custom-components/organisms/Or57074/Or57074.logic'
import type { Or57074Param } from '~/components/custom-components/organisms/Or57074/Or57074.type'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI01254'
// ルーティング
const routing = 'GUI01254/pinia'
// 画面物理名
const screenName = 'GUI01254'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or57074 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01254' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 子コンポーネントのユニークIDを設定する
// or57074.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI01254',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or57074Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or57074Const.CP_ID(1)]: or57074.value,
})

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  Or57074Logic.initialize(or57074.value.uniqueCpId)
}

// ダイアログ表示フラグ
const showDialogOr57074 = computed(() => {
  // Or57074のダイアログ開閉状態
  return Or57074Logic.state.get(or57074.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(Or57074)--期間管理フラグが「管理する」の場合
 *
 */
function onClickOr57074_1() {
  // Or57074のダイアログ開閉状態を更新する
  Or57074Logic.state.set({
    uniqueCpId: or57074.value.uniqueCpId,
    state: {
      isOpen: true,
      param: {
        prtNo: '1',
        houjinId:'1',
        svJigyoId: '1',
        shisetuId: '1',
        tantoId: '1',
        syubetsuId: '2',
        sectionName: 'インターライ方式ケアアセスメント表',
        userId: '1',
        cmoni1Id: '1',
        svJigyoKnj: '1',
        processYmd: '2025/07/02',
        parentUserIdSelectDataFlag: false,
      } as Or57074Param,
    },
  })
}

/**************************************************
 * コンポーネント固有処理
 **************************************************/
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr57074_1"
        >GUI01232_印刷設定</v-btn
      >
      <g-custom-or-57074
        v-if="showDialogOr57074"
        v-bind="or57074"
      />
    </c-v-col>
  </c-v-row>
</template>
