<script setup lang="ts">
/**
 * Or15194：有機体：（確定版）利用者(患者)基本情報について
 *
 * <AUTHOR>
 */
import { computed, reactive, ref, watch, type Ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { Or28287Logic } from '../Or28287/Or28287.logic'
import { Or28287Const } from '../Or28287/Or28287.constants'
import { TeX0012Logic } from '../../template/TeX0012/TeX0012.logic'
import { Or15194Const } from './Or15194.constants'
import type { Or15194OneWayType, Or15194ValuesType } from './Or15194.Type'
import {
  useScreenOneWayBind,
  useScreenTwoWayBind,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import type { Mo00020OnewayType } from '~/types/business/components/Mo00020Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo00038OnewayType } from '~/types/business/components/Mo00038Type'
import type { Mo00040OnewayType } from '~/types/business/components/Mo00040Type'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type { Mo01343OnewayType, Mo01343Type } from '~/types/business/components/Mo01343Type'
import type { Or51775OnewayType, Or51775Type } from '~/types/cmn/business/components/Or51775Type'
import type { Or28287Type } from '~/types/cmn/business/components/Or28287Type'
import type { TeX0012Type } from '~/types/cmn/business/components/TeX0012Type'
/**************************************************
 * Props
 **************************************************/
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
interface Props {
  /** uniqueCpId */
  uniqueCpId: string
  parentUniqueCpId: string
}

const props = defineProps<Props>()
/**************************************************
 * Pinia
 **************************************************/
const { t } = useI18n()

const { refValue } = useScreenTwoWayBind<Or15194ValuesType>({
  cpId: Or15194Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
}) as { refValue: Ref<Or15194ValuesType> }
useScreenOneWayBind<Or15194OneWayType>({
  cpId: Or15194Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    codeList: (value) => {
      localOneway.codeListOneway = value!
      if (refValue.value.or15194Values.nyuuinYokaiKbnKakutei === Or15194Const.DEFAULT.VALUE_0) {
        local.or15194.yokaiKbn1.modelValue = refValue.value.or15194Values.yokaiKbn
        local.or15194.yokaiKbn2.modelValue = ''
      } else if (
        refValue.value.or15194Values.nyuuinYokaiKbnKakutei === Or15194Const.DEFAULT.VALUE_1
      ) {
        local.or15194.yokaiKbn1.modelValue = ''
        local.or15194.yokaiKbn2.modelValue = refValue.value.or15194Values.yokaiKbn
      }
    },
  },
})

const or51775 = ref({ uniqueCpId: '' }) // Or51775：有機体：入力支援［ケアマネ］モーダル
const or28287 = ref({ uniqueCpId: '' })
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or51775Const.CP_ID(1)]: or51775.value,
  [Or28287Const.CP_ID(1)]: or28287.value,
})
/**************************************************
 * 変数定義
 **************************************************/
 const local = reactive({
  commonInfo: {} as TeX0012Type,
  or28287: {
    /** 保険者*/
    hokensya: '',
    /** 被保険者番号*/
    hHokenNo: '',
    /** 認定有効開始日（表示用）*/
    certificationValidityStartDate: '',
    /**  認定有効終了日（表示用）*/
    certificationValidityEndDate: '',
    /** 要介護度*/
    yokaiKnj: '',
    /** 限度額*/
    limit: '',
  },
  or51775: { modelValue: '' } as Or51775Type,
  mo01343: {
    value: '',
    endValue: '',
    mo00024: { isOpen: false },
  },
  or15194: {
    yokaiKbn1: {
      modelValue: '',
    },
    yokaiKbn2: {
      modelValue: '',
    },
  },
})
const localOneway = reactive({
  codeListOneway: {} as Record<string, CodeType[]>,
  or28287Oneway: {
    /** 利用者ID */
    userId: local.commonInfo.userId,
  },
  // GUI00937 共通入力支援画面
  or51775Oneway: {
    screenId: Or15194Const.DEFAULT.GUI,
    bunruiId: '-', // 分類ID TBD
    t2Cd: '',
    t3Cd: '',
    tableName: 'cpn_tuc_hosp_info_teikyou_data',
    assessmentMethod: Or15194Const.DEFAULT.ASSESS_MENT_METHOD, // アセスメント方式 TBD
    userId: systemCommonsStore.getUserId ?? '',
  } as Or51775OnewayType,
  mo01343Oneway: {
    selectMode: Or15194Const.DEFAULT.VALUE_1,
  } as Mo01343OnewayType,
  mo00045Oneway: {
    maxlength: '44',
    width: '625px',
    isVerticalLabel: false,
    showItemLabel: false,
  } as Mo00045OnewayType,
  mo00045Oneway1: {
    maxlength: '10',
    width: '170px',
    isVerticalLabel: false,
    showItemLabel: false,
  } as Mo00045OnewayType,
  mo00020Oneway1: {
    isRequired: false,
    isVerticalLabel: false,
    showItemLabel: false,
    showSelectArrow: false,
    maxlength: '10',
    width: '170px',
  } as Mo00020OnewayType,
  mo00020Oneway2: {
    isRequired: false,
    isVerticalLabel: false,
    showItemLabel: true,
    itemLabel: t('label.application_in_progress_date_label'),
    showSelectArrow: false,
    maxlength: '10',
    width: '170px',
    customClass: { outerClass: 'mx-2' } as CustomClass,
  } as Mo00020OnewayType,
  mo00009Oneway: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  mo01338Oneway: {
    valueFontWeight: 'blod',
    value: t('label.user_basic_information_label'),
    customClass: {
      itemStyle: 'font-size:18px; !import',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00039Oneway: {
    itemLabel: t('label.residence_type_label'),
    showItemLabel: true,
    inline: true,
    customClass: { outerClass: 'd-flex align-center' } as CustomClass,
  } as Mo00039OnewayType,
  mo00039Oneway2: {
    showItemLabel: false,
    inline: true,
    customClass: { outerClass: 'd-flex align-center' } as CustomClass,
  } as Mo00039OnewayType,

  // 文字数入力
  mo00038Oneway1: {
    mo00045Oneway: {
      appendLabel: t('label.building_floors_label'),
      showItemLabel: false,
      maxLength: '999',
      width: '107px',
      customClass: new CustomClass({
        outerClass: 'mx-4',
      }),
    } as Mo00045OnewayType,
    min: 0,
    max: 99,
    disalbeSpinBtnDefaultProcessing: false,
  } as Mo00038OnewayType,
  // 文字数入力
  mo00038Oneway2: {
    mo00045Oneway: {
      itemLabel: t('label.room_label'),
      appendLabel: t('label.floor_label'),
      showItemLabel: true,
      isVerticalLabel: false,
      maxLength: '20',
      width: '80px',
      customClass: new CustomClass({
        labelClass: 'd-flex align-center mr-2',
        labelStyle: 'padding:0 !important',
      }),
    } as Mo00045OnewayType,
    min: 0,
    max: 99,
    disalbeSpinBtnDefaultProcessing: false,
  } as Mo00038OnewayType,
  // 文字数入力
  mo00038Oneway3: {
    mo00045Oneway: {
      appendLabel: t('label.percentage_label'),
      showItemLabel: false,
      maxLength: '20',
      width: '80px',
    } as Mo00045OnewayType,
    min: 0,
    max: 99,
    disalbeSpinBtnDefaultProcessing: false,
  } as Mo00038OnewayType,
  mo00040Oneway1: {
    itemTitle: 'label',
    itemValue: 'value',
    showItemLabel: false,
    itemLabelFontWeight: 'bold',
    width: '80px',
    hideDetails: true,
    items: [],
  } as Mo00040OnewayType,
  mo00040Oneway2: {
    itemTitle: 'label',
    itemValue: 'value',
    showItemLabel: false,
    itemLabelFontWeight: 'bold',
    width: '80px',
    hideDetails: true,
    items: [],
  } as Mo00040OnewayType,
  mo00018Oneway1: {
    name: t('label.application_in_progress_flag'),
    hideDetails: true,
    showItemLabel: false,
    itemLabel: '',
    checkboxLabel: t('label.application_in_progress_flag'),
  } as Mo00018OnewayType,
  mo00018Oneway2: {
    name: t('label.classification_change'),
    hideDetails: true,
    showItemLabel: false,
    itemLabel: '',
    checkboxLabel: t('label.classification_change'),
  } as Mo00018OnewayType,
  mo00018Oneway3: {
    name: t('label.unapplied_flag'),
    hideDetails: true,
    showItemLabel: false,
    itemLabel: '',
    checkboxLabel: t('label.unapplied_flag'),
  } as Mo00018OnewayType,
  mo00018Oneway4: {
    name: t('label.physician_judgment'),
    hideDetails: true,
    showItemLabel: false,
    itemLabel: '',
    checkboxLabel: t('label.physician_judgment'),
  } as Mo00018OnewayType,
  mo00018Oneway5: {
    name: t('label.care_manager_judgment'),
    hideDetails: true,
    showItemLabel: false,
    itemLabel: '',
    checkboxLabel: t('label.care_manager_judgment'),
  } as Mo00018OnewayType,
  pensionDataList: [
    {
      label: t('label.national_pension'),
      key: 'nenkin1Umu',
    },
    {
      label: t('label.employee_pension'),
      key: 'nenkin2Umu',
    },
    {
      label: t('label.disability_pension'),
      key: 'nenkin3Umu',
    },
    {
      label: t('label.welfare_pension'),
      key: 'nenkin4Umu',
    },
    {
      label: t('label.other_pension'),
      key: 'nenkin5Umu',
    },
  ],
  shogaiKbnDataList: [
    {
      label: t('label.physical_disability_recognition'),
      key: 'shintaiShogaiKbn',
    },
    {
      label: t('label.mental_disability_recognition'),
      key: 'chitekiShogaiKbn',
    },
    {
      label: t('label.intellectual_disability_recognition'),
      key: 'seishinShogaiKbn',
    },
  ],
})


/**
 * 入力支援ポップアップの表示状態を返すComputed
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr28287 = computed(() => {
  // Or28287のダイアログ開閉状態
  return Or28287Logic.state.get(or28287.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 共通入力支援画面の確定ボタン押す後の処理
 *
 * @param data - 入力支援情報
 */
const handleOr51775Confirm = (data: Or51775ConfirmType) => {
  const setOrAppendValue = (str: string, data: Or51775ConfirmType) => {
    if (data.type === Or15194Const.DEFAULT.ADD_END_TEXT_IMPORT_TYPE) {
      // 本文末に追加の場合
      return `${str}${data.value}`
    } else if (data.type === Or15194Const.DEFAULT.OVERWRITE_TEXT_IMPORT_TYPE) {
      // 本文上書の場合
      return data.value
    } else {
      return ''
    }
  }

  switch (localOneway.or51775Oneway.title) {
    case t('label.special_notes'):
      refValue.value.or15194Values.houseTokkiKnj.value = setOrAppendValue(
        refValue.value.or15194Values.houseTokkiKnj.value ?? '',
        data
      )
      break
    default:
      break
  }
  console.log(refValue.value.or15194Values.houseTokkiKnj.value)
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: false },
  })
}

/**
 * 入力支援アイコンボタンクリック
 *
 */
const handleHouseTokkiKnj = () => {
  // GUI00937 共通入力支援画面をポップアップで起動する
  localOneway.or51775Oneway.title = t('label.special_notes')
  localOneway.or51775Oneway.t2Cd = Or15194Const.DEFAULT.VALUE_1
  localOneway.or51775Oneway.t3Cd = Or15194Const.DEFAULT.VALUE_3
  localOneway.or51775Oneway.columnName = 'house_tokki_knj'
  localOneway.or51775Oneway.inputContents = t('label.special_notes')
  local.or51775.modelValue = refValue.value.or15194Values.houseTokkiKnj.value ?? ''
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 入力支援アイコンボタンクリック
 *
 */
const handleNenkinMemoKnj = () => {
  // GUI00937 共通入力支援画面をポップアップで起動する
  localOneway.or51775Oneway.title = t('label.pension_type_label')
  localOneway.or51775Oneway.t2Cd = Or15194Const.DEFAULT.VALUE_1
  localOneway.or51775Oneway.t3Cd = Or15194Const.DEFAULT.VALUE_5
  localOneway.or51775Oneway.columnName = 'nenkin_memo_knj'
  localOneway.or51775Oneway.inputContents = t('label.pension_type_label')
  local.or51775.modelValue = refValue.value.or15194Values.nenkinMemoKnj?.value ?? ''
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 入力支援アイコンボタンクリック
 *
 */
const handleCare = () => {
  Or28287Logic.state.set({
    uniqueCpId: or28287.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * or28287の値変更を監視
 *
 * @param or28287 - Or28287Type
 */
const handleOr28287 = (or28287: Or28287Type) => {
  console.log(or28287, 12331)
}

/**
 * mo00039の値変更を監視
 *
 * @param mo00039 - Mo00039Type
 */
const handleMo00039 = (mo00039: string) => {
  if (mo00039 === Or15194Const.DEFAULT.VALUE_0) {
    local.or15194.yokaiKbn2.modelValue = ''
    refValue.value.or15194Values.yokaiKbn = local.or15194.yokaiKbn1.modelValue
  } else if (mo00039 === Or15194Const.DEFAULT.VALUE_1) {
    local.or15194.yokaiKbn1.modelValue = ''
    refValue.value.or15194Values.yokaiKbn = local.or15194.yokaiKbn2.modelValue
  }
}

/**
 * mo00039の値変更を監視
 *
 * @param mo00039 - Mo00039Type
 */
const handleShogaiNintei = (mo00039: string) => {
  if (mo00039 === Or15194Const.DEFAULT.VALUE_0) {
    refValue.value.or15194Values.shintaiShogaiKbn.modelValue = false
    refValue.value.or15194Values.chitekiShogaiKbn.modelValue = false
    refValue.value.or15194Values.seishinShogaiKbn.modelValue = false
  }
}

/**
 * mo00018の値変更を監視
 *
 * @param mo00018 - mo00018
 */
const handleMo00018 = (mo00018: Mo00018Type) => {
  if (mo00018.modelValue) {
    refValue.value.or15194Values.shogaiNintei = Or15194Const.DEFAULT.VALUE_1
  }
}

/**
 * mo01343の値変更を監視
 *
 * @param mo01343 - Mo01343Type
 */
const handleMo01343 = (mo01343: Mo01343Type) => {
  refValue.value.or15194Values.ninteiStartYmd.value = mo01343.value
  refValue.value.or15194Values.ninteiEndYmd.value = mo01343.endValue
}
/**
 * codeListOnewayの値変更を監視
 *
 */
watch(
  () => localOneway.codeListOneway,
  (newVal) => {
    local.mo01343.value = refValue.value.or15194Values.ninteiStartYmd.value
    local.mo01343.endValue = refValue.value.or15194Values.ninteiEndYmd.value

    localOneway.mo00040Oneway1.items = newVal.SUPPORT_LEVEL_VALUE
    localOneway.mo00040Oneway2.items = newVal.CARE_LEVEL_VALUE
  }
)

/**
 * 画面メニューのイベントを監視
 */
watch(
  () => TeX0012Logic.data.get(props.parentUniqueCpId),
  (newValue) => {
    if (newValue?.teikyouId) {
      local.commonInfo = newValue
      localOneway.or51775Oneway.userId = newValue.userId ?? ''
    }
  },
  { deep: true }
)
</script>

<template>
  <div v-if="refValue.or15194Values">
    <c-v-row class="title">
      <c-v-col>
        <base-mo01338 :oneway-model-value="localOneway.mo01338Oneway"></base-mo01338>
      </c-v-col>
    </c-v-row>
    <!-- 住環境 ※可能ならば、「写真」などを添付 -->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.living_environment_label') }}<br />
        {{ t('label.ifPossible-attachPhotos-andOthers') }}<br />
        {{ t('label.living_environment') }}
      </c-v-col>
      <c-v-col cols="10">
        <c-v-row class="border-bottom">
          <c-v-col
            cols="7"
            class="data-cell flex-wrap"
          >
            <div class="d-flex align-items flex-wrap">
              <!-- 住環境 -->
              <base-mo00039
                v-model="refValue.or15194Values.houseKbn"
                :oneway-model-value="localOneway.mo00039Oneway"
              >
                (
                <base-at-radio
                  v-for="(item, index) in localOneway.codeListOneway
                    .CONFIRMATION_INFO_LIVING_ENVIRONMENT"
                  :key="'radio' + '_' + index"
                  :name="item.label"
                  :radio-label="item.label"
                  :value="item.value"
                />
                )
              </base-mo00039>
              <!-- 住居階層 -->
              <base-mo00038
                v-model="refValue.or15194Values.houseFloor"
                :oneway-model-value="localOneway.mo00038Oneway1"
              ></base-mo00038>
              <!-- 居室階 -->
              <base-mo00038
                v-model="refValue.or15194Values.houseRoomFloor"
                :oneway-model-value="localOneway.mo00038Oneway2"
              ></base-mo00038>
            </div>
          </c-v-col>
          <c-v-col
            cols="2"
            class="header-cell"
          >
            {{ t('label.elevator_label') }}
          </c-v-col>
          <!-- エレベーターラベル -->
          <c-v-col
            cols="3"
            class="data-cell"
          >
            <base-mo00039
              v-model="refValue.or15194Values.elevatorUmu"
              :oneway-model-value="localOneway.mo00039Oneway2"
            >
              <base-at-radio
                v-for="(item, index) in localOneway.codeListOneway.M_CD_KBN_ID_OMITTED"
                :key="index"
                :name="'radio' + '-' + index"
                :radio-label="item.label"
                :value="item.value"
              />
            </base-mo00039>
          </c-v-col>
        </c-v-row>
        <!--住まいに関する特記事項-->
        <c-v-row>
          <c-v-col
            cols="12"
            class="data-cell"
          >
            <div class="d-flex align-center">
              <div class="d-flex align-center">
                {{ t('label.special_notes') }}
                <c-v-divider
                  class="ml-2"
                  vertical
                  inset
                />
                <base-mo00009
                  :oneway-model-value="localOneway.mo00009Oneway"
                  @click="handleHouseTokkiKnj"
                />
              </div>
              <base-mo00045
                v-model="refValue.or15194Values.houseTokkiKnj"
                :oneway-model-value="localOneway.mo00045Oneway"
              />
            </div>
          </c-v-col>
        </c-v-row>
      </c-v-col>
    </c-v-row>
    <!--入院時の要介護度-->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.care_level_at_admission_label') }}
        <div class="d-flex">
          <c-v-divider
            vertical
            inset
          />
          <base-mo00009
            v-if="
              local.commonInfo.nursingCareInsuranceAuthorityFlag === Or15194Const.DEFAULT.VALUE_0
            "
            :oneway-model-value="localOneway.mo00009Oneway"
            @click="handleCare"
          >
            <g-custom-or-28287
              v-if="showDialogOr28287"
              v-bind="or28287"
              v-model="local.or28287"
              :oneway-model-value="localOneway.or28287Oneway"
              @update:model-value="handleOr28287"
            />
          </base-mo00009>
        </div>
      </c-v-col>
      <c-v-col
        cols="10"
        class="data-cell"
      >
        <div class="d-flex align-center mb-2">
          <base-mo00039
            v-model="refValue.or15194Values.nyuuinYokaiKbnKakutei"
            :oneway-model-value="localOneway.mo00039Oneway2"
            @update:model-value="handleMo00039"
          >
            <div
              v-for="(item, index) in localOneway.codeListOneway
                .CONFIRMATION_INFO_CARE_LEVEL_AT_ADMISSION_CONFIRMED"
              :key="'radio' + '_' + index"
              class="d-flex mr-2"
            >
              <base-at-radio
                :name="item.label"
                :radio-label="item.label"
                :value="item.value"
              />
              <base-mo00040
                v-if="index === 0"
                v-model="local.or15194.yokaiKbn1"
                class="ml-1"
                :disabled="
                  refValue.or15194Values.nyuuinYokaiKbnKakutei === Or15194Const.DEFAULT.VALUE_1
                "
                :oneway-model-value="localOneway.mo00040Oneway1"
              />
              <base-mo00040
                v-if="index === 1"
                v-model="local.or15194.yokaiKbn2"
                class="ml-1"
                :disabled="
                  refValue.or15194Values.nyuuinYokaiKbnKakutei === Or15194Const.DEFAULT.VALUE_0
                "
                :oneway-model-value="localOneway.mo00040Oneway2"
              />
            </div>
          </base-mo00039>
          <div class="d-flex align-center ml-11">
            {{ t('label.validity_period_label') }}
            <div class="d-flex ml-2">
              <c-v-divider
                vertical
                inset
              />
              <base-mo00009
                :oneway-model-value="localOneway.mo00009Oneway"
                @click="local.mo01343.mo00024.isOpen = true"
              >
                <base-mo01343
                  v-model="local.mo01343"
                  :oneway-model-value="localOneway.mo01343Oneway"
                  @update:model-value="handleMo01343"
                />
              </base-mo00009>
            </div>
            <base-mo00020
              v-model="refValue.or15194Values.ninteiStartYmd"
              :oneway-model-value="localOneway.mo00020Oneway1"
            />
            <p class="mx-2">～</p>
            <base-mo00020
              v-model="refValue.or15194Values.ninteiEndYmd"
              :oneway-model-value="localOneway.mo00020Oneway1"
            />
          </div>
        </div>
        <div class="d-flex">
          <div class="d-flex align-center">
            <base-mo00018
              v-model="refValue.or15194Values.ninteiShinseiFlg"
              :oneway-model-value="localOneway.mo00018Oneway1"
            />(
            <base-mo00020
              v-model="refValue.or15194Values.ninteiShinseiYmd"
              :oneway-model-value="localOneway.mo00020Oneway2"
            />)
          </div>
          <div class="d-flex align-center mlr-10">
            <base-mo00018
              v-model="refValue.or15194Values.ninteiKbnHenkou"
              :oneway-model-value="localOneway.mo00018Oneway2"
            />(
            <base-mo00020
              v-model="refValue.or15194Values.kbnHenkouYmd"
              :oneway-model-value="localOneway.mo00020Oneway2"
            />)
          </div>
          <base-mo00018
            v-model="refValue.or15194Values.ninteiMishinseiFlg"
            :oneway-model-value="localOneway.mo00018Oneway3"
          />
        </div>
      </c-v-col>
    </c-v-row>

    <c-v-row>
      <c-v-col cols="10">
        <c-v-row>
          <!-- 障害高齢者の日常生活自立度 -->
          <c-v-col cols="12">
            <c-v-row class="row">
              <c-v-col class="header-cell flex-20">
                {{ t('label.daily_living_independence_degree') }}
              </c-v-col>
              <c-v-col class="data-cell flex-80">
                <base-mo00039
                  v-model="refValue.or15194Values.shogaiJiritsuCd"
                  :oneway-model-value="localOneway.mo00039Oneway2"
                >
                  <base-at-radio
                    v-for="(item, index) in localOneway.codeListOneway
                      .CONFIRMATION_INFO_DEGREE_INDEP_DAILY_LIVING_ELD_DISAB"
                    :key="index"
                    :name="'radio' + '-' + index"
                    :radio-label="item.label"
                    :value="item.value"
                  />
                </base-mo00039>
              </c-v-col>
            </c-v-row>
          </c-v-col>
          <!-- 認知症高齢者の日常生活自立度 -->
          <c-v-col cols="12">
            <c-v-row class="row">
              <c-v-col class="header-cell flex-20">
                {{ t('label.dementia_daily_living_independence_degree') }}
              </c-v-col>
              <c-v-col class="data-cell flex-80">
                <base-mo00039
                  v-model="refValue.or15194Values.ninchiJiritsuCd"
                  :oneway-model-value="localOneway.mo00039Oneway2"
                >
                  <base-at-radio
                    v-for="(item, index) in localOneway.codeListOneway
                      .CONFIRMATION_INFO_DEGREE_INDEP_DAILY_LIVING_ELD_DEM"
                    :key="index"
                    :name="'radio' + '-' + index"
                    :radio-label="item.label"
                    :value="item.value"
                  />
                </base-mo00039>
              </c-v-col>
            </c-v-row>
          </c-v-col>
        </c-v-row>
      </c-v-col>
      <c-v-col
        cols="2"
        class="data-cell border-bottom"
        style="border-left: 0;"
      >
        <base-mo00018
          v-model="refValue.or15194Values.ishiHandanFlg"
          :oneway-model-value="localOneway.mo00018Oneway4"
        />
        <base-mo00018
          v-model="refValue.or15194Values.careHandanFlg"
          :oneway-model-value="localOneway.mo00018Oneway5"
        />
      </c-v-col>
    </c-v-row>
    <c-v-row class="row">
      <!-- 介護保険の自己負担割合 -->
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.self_payment_ratio_label') }}
      </c-v-col>
      <c-v-col
        cols="3"
        class="data-cell"
      >
        <base-mo00039
          v-model="refValue.or15194Values.futanWariFlg"
          :oneway-model-value="localOneway.mo00039Oneway2"
        >
          <div
            v-for="(item, index) in localOneway.codeListOneway
              .CONFIRMATION_INFO_OWN_BURDEN_RATIO_BEDSORE"
            :key="'radio' + '_' + index"
            class="d-flex"
          >
            <base-at-radio
              :name="item.label"
              :radio-label="item.label"
              :value="item.value"
            />
            <base-mo00038
              v-if="index === 0"
              v-model="refValue.or15194Values.futanWariai"
              :oneway-model-value="localOneway.mo00038Oneway3"
            ></base-mo00038>
          </div>
        </base-mo00039>
      </c-v-col>
      <!-- 障害など認定 -->
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.disability_recognition') }}
      </c-v-col>
      <c-v-col
        cols="5"
        class="data-cell d-flex"
      >
        <base-mo00039
          v-model="refValue.or15194Values.shogaiNintei"
          :oneway-model-value="localOneway.mo00039Oneway2"
          @update:model-value="handleShogaiNintei"
        >
          <base-at-radio
            v-for="(item, index) in localOneway.codeListOneway.M_CD_KBN_ID_OMITTED"
            :key="index"
            :name="'radio' + '-' + index"
            :radio-label="item.label"
            :value="item.value"
          />
        </base-mo00039>
        <div class="d-flex align-center">
          (
          <base-mo00018
            v-for="(item, index) in localOneway.shogaiKbnDataList"
            :key="index"
            v-model="refValue.or15194Values[item.key]"
            :oneway-model-value="
              {
                name: item.label,
                hideDetails: true,
                showItemLabel: false,
                itemLabel: '',
                checkboxLabel: item.label,
              } as Mo00018OnewayType
            "
            @update:model-value="handleMo00018"
          />
          )
        </div>
      </c-v-col>
    </c-v-row>
    <!-- 年金などの種類 -->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.pension_type_label') }}
      </c-v-col>
      <c-v-col
        cols="10"
        class="data-cell"
      >
        <div class="d-flex align-center">
          <base-mo00018
            v-for="(item, index) in localOneway.pensionDataList"
            :key="index"
            v-model="refValue.or15194Values[item.key]"
            :oneway-model-value="{
              name: item.label,
              hideDetails: true,
              showItemLabel: false,
              itemLabel: '',
              checkboxLabel: item.label,
            }"
          />
          <div class="d-flex align-center ml-10">
            <c-v-divider
              class="ml-2"
              vertical
              inset
            />
            <base-mo00009
              :oneway-model-value="localOneway.mo00009Oneway"
              @click="handleNenkinMemoKnj"
            />
          </div>
          <base-mo00045
            v-model="refValue.or15194Values.nenkinMemoKnj"
            :oneway-model-value="localOneway.mo00045Oneway1"
          />
        </div>
      </c-v-col>
    </c-v-row>
    <!-- Or51775: 有機体: GUI00937 入力支援［ケアマネ］をポップアップで起動する。 -->
    <g-custom-or51775
      v-if="showDialogOr51775"
      v-bind="or51775"
      v-model="local.or51775"
      :oneway-model-value="localOneway.or51775Oneway"
      @confirm="handleOr51775Confirm"
    />
  </div>
</template>

<style scoped lang="scss">
.row {
  display: flex;
  align-items: center;
  border-bottom: 1px gainsboro solid;
  border-left: 1px gainsboro solid;
  min-height: 62px;
}

.header-cell {
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 12px !important;
}

.header-title-cell {
  background-color: transparent;
  border-right: 1px gainsboro solid;
  display: grid;
  align-items: center;
}

.data-cell {
  border-left: 1px gainsboro solid;
  border-right: 1px gainsboro solid;
  background: #fff;
  padding: 10px 12px;
  width: 100%;
  min-height: 62px;
  display: grid;
  align-items: center;
}

:deep(.v-input__control) {
  background-color: rgb(var(--v-theme-surface));
}
:deep(.v-selection-control-group--inline) {
  align-items: center;
}
.flex-20 {
  flex: 0 0 20%;
  max-width: 20%;
}
.flex-80 {
  flex: 0 0 80%;
  max-width: 80%;
}
.mlr-10 {
  margin: 0 10%;
}
.border-bottom {
  border-bottom: 1px gainsboro solid;
}
.title {
  margin-top: 12px;
  background-color: #fff;
  border-left: 1px gainsboro solid;
  border-right: 1px gainsboro solid;
  border-bottom: 1px gainsboro solid;
}
</style>
