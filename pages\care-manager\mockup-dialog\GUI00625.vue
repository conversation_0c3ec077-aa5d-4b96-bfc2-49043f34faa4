<script setup lang="ts">
/**
 * Or52612:［表示設定］画面
 * GUI00625_［表示設定］画面
 *
 * @description
 * GUI00625_［表示設定］画面
 *
 * <AUTHOR>
 */

import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or52612Const } from '~/components/custom-components/organisms/Or52612/Or52612.constants'
import { Or52612Logic } from '~/components/custom-components/organisms/Or52612/Or52612.logic'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * 画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI00625'
// ルーティング
const routing = 'GUI00625/pinia'
// 画面物理名
const screenName = 'GUI00625'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

// 有機体or52612ユーニックID
const or52612 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00625' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）

// 子コンポーネントのユニークIDを設定する
or52612.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI00625',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or52612Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or52612Const.CP_ID(1)]: or52612.value,
})

/**************************************************
 * Props
 **************************************************/

// ダイアログ表示フラグ
const showDialogOr52612 = computed(() => {
  // Or52612のダイアログ開閉状態
  return Or52612Logic.state.get(or52612.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
const or52612Data = {
  // 全体の判断
  svJigyoId: '1',
}

/**
 *  ボタン押下時の処理
 *
 * 期間管理フラグ
 */
function or52612OnClick_1() {
  or52612Data.svJigyoId = '0'
  // Or52612のダイアログ開閉状態を更新する
  Or52612Logic.state.set({
    uniqueCpId: or52612.value.uniqueCpId,
    state: { isOpen: true },
  })
}
function or52612OnClick_2() {
  or52612Data.svJigyoId = '1'
  // Or52612のダイアログ開閉状態を更新する
  Or52612Logic.state.set({
    uniqueCpId: or52612.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters><c-v-col>GUI00625_［表示設定］画面確認用ページ</c-v-col></c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >GUI00625_［表示設定］画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>

  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="or52612OnClick_1()"
        >GUI00625_［表示設定］画面 （引継情報.全体の判断が ０の場合）
      </v-btn>
      <g-custom-or52612
        v-if="showDialogOr52612"
        v-bind="or52612"
        :oneway-model-value="or52612Data"
      />
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="or52612OnClick_2()"
        >GUI00625_［表示設定］画面 （引継情報.全体の判断が ０以外、かつ 0件以外の場合）
      </v-btn>
    </c-v-col>
  </c-v-row>
</template>
