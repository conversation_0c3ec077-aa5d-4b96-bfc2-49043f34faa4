<script setup lang="ts">
/**
 * Or27592:支援経過記録一覧印刷設定モーダル
 * GUI01264_印刷設定
 *
 * @description
 * 支援経過記録一覧印刷設定モーダル
 *
 * <AUTHOR> PHAM TIEN THANH
 */
import { computed, onMounted, reactive, ref, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or10016Const } from '../Or10016/Or10016.constants'
import { Or26257Const } from '../Or26257/Or26257.constants'
import { Or26257Logic } from '../Or26257/Or26257.logic'
import { Or10016Logic } from '../Or10016/Or10016.logic'
import { OrX0130Const } from '../OrX0130/OrX0130.constants'
import { OrX0130Logic } from '../OrX0130/OrX0130.logic'

import type { OrX0130TableType } from '../OrX0130/OrX0130.type'
import { OrX0145Const } from '../OrX0145/OrX0145.constants'
import { Or27592Const } from './Or27592.constants'
import type { Or27592TwoWayData } from './Or27592.type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Or27592OnewayType } from '~/types/cmn/business/components/Or27592Type'
import {
  useReportUtils,
  useScreenOneWayBind,
  useScreenStore,
  useScreenTwoWayBind,
  useSetupChildProps,
} from '#imports'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  PrintSettingScreenInitialInfoSelectInEntity,
  PrintSettingScreenInitialInfoSelectOutEntity,
  IChoPrtInfo,
} from '~/repositories/cmn/entities/PrintSettingsScreenInitialInfoSelectGUI1264Entity'
import type {
  LedgerInitializeDataSelectInEntity,
  LedgerInitializeDataSelectOutEntity,
} from '~/repositories/cmn/entities/ledgerInitializeDataSelectGUI01264Entity'
import type { PrintSettingsInfoUpdateInEntity } from '~/repositories/cmn/entities/PrintSettingsInfoUpdateGUI011264Entity'
import type {
  Mo01334Items,
  Mo01334OnewayType,
  Mo01334Type,
} from '~/types/business/components/Mo01334Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { CustomClass } from '~/types/CustomClassType'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import { Or00094Const } from '~/components/base-components/organisms/Or00094/Or00094.constants'
import { Or00094Logic } from '~/components/base-components/organisms/Or00094/Or00094.logic'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import type { Mo00040OnewayType, Mo00040Type } from '~/types/business/components/Mo00040Type'
import type { Mo00610OnewayType } from '~/types/business/components/Mo00610Type'
import type { Or26257Type, Or26257OnewayType } from '~/types/cmn/business/components/Or26257Type'
import type { Or10016OnewayType } from '~/types/cmn/business/components/Or10016Type'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import { reportOutputType } from '~/utils/useReportUtils'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import type {
  PrintSet,
  PrintOption,
  PrintSubjectHistory,
  SpecialistPassageReportInEntity,
} from '~/repositories/cmn/entities/SpecialistPassageReportEntity'
import type {
  SpecialistPassageByDateReportServiceInEntity,
  PrintSet as SimplePrintSet,
} from '~/repositories/cmn/entities/SpecialistPassageByDateReportServiceEntity'
import type {
  PreventCareElapsedRecordReportServiceInEntity,
  PrintSet as PreventionPrintSet,
  PrintOption as PreventionPrintOption,
  PrintSubjectHistory as PreventionPrintSubjectHistory,
  JigyoInfo,
} from '~/repositories/cmn/entities/PreventCareElapsedRecordReportServiceEntity'
import type {
  SpecialistPassageR34ReportServiceInEntity,
  PrintSet as R34PrintSet,
  PrintOption as R34PrintOption,
  PrintSubjectHistory as R34PrintSubjectHistory,
} from '~/repositories/cmn/entities/SpecialistPassageR34ReportServiceEntity'

const { t } = useI18n()

/**
 * システム共有情報ストア
 */
const systemCommonsStore = useSystemCommonsStore()

const { reportOutput } = useReportUtils()

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or27592OnewayType
  uniqueCpId: string
}

/**
 * 引継情報を取得する
 */
const props = defineProps<Props>()

const or00094 = ref({ uniqueCpId: '' })

const or21813 = ref({ uniqueCpId: '' })

const or21815 = ref({ uniqueCpId: '' })

const or10016 = ref({ uniqueCpId: '' })

const or26257 = ref({ uniqueCpId: '' })

const orX0130 = ref({ uniqueCpId: '' })

const or21814 = ref({ uniqueCpId: '' })

const orX0145 = ref({ uniqueCpId: '' })

/**
 * プロファイル
 */
const choPro = ref('')

/**
 * 利用者一覧明細選択
 */
const orX0130Type = ref<string>('')

/**
 * 帳票イニシャライズデータを取得する
 */
const reportInitData = ref({
  // 氏名伏字印刷
  prtName: '',
  // 文書番号印刷
  prtBng: '',
  // 個人情報印刷
  prtKojin: '',
})

const localOneway = reactive({
  Or27592: {
    ...props.onewayModelValue,
  },
  mo00024Oneway: {
    width: '1300px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or27592',
      toolbarTitle: t('label.print-set'),
      toolbarName: 'Or27592ToolBar',
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  Mo00610OneWaySealColumn: {
    btnLabel: t('btn.seal-column'),
    tooltipText: t('tooltip.seal-column-btn'),
  } as Mo00610OnewayType,
  Mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.close-btn'),
  } as Mo00611OnewayType,
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
    tooltipText: t('tooltip.print-btn'),
  } as Mo00609OnewayType,
  mo01338OneWayTitle: {
    value: t('label.print-settings-title'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00039OneWayPrintCategory: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  mo00039OneWayUserSelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  mo00039OneWayPrintType: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  mo00020OneWay: {
    showItemLabel: false,
    width: '100%',
  } as Mo00020OnewayType,
  mo01338OneWayPrinterOption: {
    value: t('label.printer-option'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00018OneWayChangeTitle: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.honorifics-change'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo00045OnewayTitleInput: {
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
  } as Mo00045OnewayType,
  mo00018OneWayPrintTheForm: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-entry-sheet'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo00018OneWayPrintRecorder: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-recorder'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo00018OneWayPrintTime: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-time'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo00018OneWayFilterByRecorder: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.filter-by-recorder'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo00018OneWayTimePeriod: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-time-period'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  mo00018OneWayPrintConfirmationItems: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-confirmation-items'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
  } as Mo00018OnewayType,
  mo00018OneWayPrintCarePrevention: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-care-prevention'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
  } as Mo00018OnewayType,
  mo01338OneWayRequiredCareLevel: {
    value: t('label.print-nursing-care-required'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00040OneWayRegistrationDateCategory: {
    items: [],
    showItemLabel: false,
    isRequired: false,
    width: '100%',
    hideDetails: true,
    itemTitle: 'label',
    itemValue: 'value',
    customClass: {
      itemClass: 'registration-date-dropdown',
    } as CustomClass,
  } as Mo00040OnewayType,
  mo00040OneWayRequiredCareLevel: {
    items: [],
    showItemLabel: false,
    isRequired: false,
    width: '344px',
    hideDetails: true,
    itemTitle: 'label',
    itemValue: 'value',
  } as Mo00040OnewayType,
  mo01338OneWayPrinterUserSelect: {
    value: t('label.printer-user-select'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338OneWayPeriod: {
    value: t('label.period'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338OneWaySelected: {
    value: t('label.selected'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338OneWayAuthor: {
    value: t('label.author'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338OneWayAdministratorFilterByRecorder: {
    value: t('label.administrator-taro'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338OneWayAdministratorAuthor: {
    value: t('label.administrator-taro'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338OneWayRecordingDate: {
    value: t('label.recording-date'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,

  mo00009OnewayFilterByRecorderType: {
    icon: true,
    btnIcon: 'edit_square',
    prependIcon: 'edit_square',
  } as Mo00009OnewayType,
  mo00009OnewayPeriodType: {
    icon: true,
    btnIcon: 'edit_square',
    prependIcon: 'edit_square',
  } as Mo00009OnewayType,
  mo00009OnewayAuthorType: {
    icon: true,
    btnIcon: 'edit_square',
    prependIcon: 'edit_square',
  } as Mo00009OnewayType,
  mo00009OnewayCareManagerInChargeType: {
    icon: true,
    btnIcon: 'edit_square',
    prependIcon: 'edit_square',
  } as Mo00009OnewayType,
  mo00020OneWayStartDate: {
    showItemLabel: false,
    width: '132px',
  } as Mo00020OnewayType,
  mo00020OneWayEndDate: {
    showItemLabel: false,
    width: '132px',
  } as Mo00020OnewayType,
  mo00020OneWayRecordingDate: {
    showItemLabel: false,
    showSelectArrow: true,
    width: '132px',
    mo00009OnewayForward: {},
    mo00009OnewayBack: {},
  } as Mo00020OnewayType,
  orX0130Oneway: {
    selectMode: OrX0130Const.DEFAULT.TANI,
    tableStyle: 'width: 100%',
    focusSettingInitial: [OrX0130Const.DEFAULT.STR_ALL],
  } as OrX0130OnewayType,
  //（予防計画書）印鑑欄設定）ダイアログ
  or10016Oneway: {
    houjinId: systemCommonsStore.getHoujinId!,
    shisetsuId: systemCommonsStore.getShisetuId!,
    shokuinId: systemCommonsStore.getStaffId!,
    systemCode: systemCommonsStore.getSystemCode!,
    jigyoshoId: systemCommonsStore.getSvJigyoId!,
    loginNumber: systemCommonsStore.getLoginInfo?.loginNumber ?? '',
    loginUserType: systemCommonsStore.getLoginInfo?.userType ?? '',
    emrLinkFlag: systemCommonsStore.getElectronicMedicalRecordCooperationFlag ? '1' : '0',
    reportSectionNumber: '1',
    assessment: '',
    conferenceFlag: false,
  } as Or10016OnewayType,
  /**
   * 担当ケアマネプルダウン
   */
  orX0145Oneway: {
    itemLabel: t('label.care-manager-in-charge'),
    isVerticalLabel: true,
    multiple: false,
    selectedUserCounter: systemCommonsStore.getStaffId ?? '1',
    customClass: {
      outerClass: 'd-flex',
      itemStyle: 'width:300px',
    } as CustomClass,
  } as OrX0145OnewayType,
})

const mo00024 = ref<Mo00024Type>({
  isOpen: Or27592Const.DEFAULT.IS_OPEN,
})

/**
 * 担当ケアマネプルダウン選択値
 */
const orX0145Type = ref<OrX0145Type>({
  value: {} as TantoCmnShokuin,
} as OrX0145Type)

const local = reactive({
  textInput: {
    value: '',
  } as Mo00045Type,
  titleInput: {
    value: '',
  } as Mo00045Type,
  mo00039TypePrintCategory: '',
  mo00020Type: {
    value: '',
  } as Mo00020Type,
  mo00020RecordingDate: {
    value: '',
  } as Mo00020Type,
  mo00018TypeChangeTitle: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypePrintTheForm: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypePrintRecorder: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypePrintTime: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypeFilterByRecorder: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypeTimePeriod: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypePrintConfirmationItems: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypePrintCarePrevention: {
    modelValue: false,
  } as Mo00018Type,
  mo00039TypeUserSelectType: '',
  mo00039TypePrintType: '',
  mo00020StartDate: {
    value: '',
  } as Mo00020Type,
  mo00020EndDate: {
    value: '',
  } as Mo00020Type,
  mo00040TypeRequiredCareLevel: {
    modelValue: '',
  } as Mo00040Type,
  mo00040TypeRegistrationDateCategory: {
    modelValue: '',
  } as Mo00040Type,
  mo00040TypeUserSelectType: '',
})

/**
 * 出力帳票名一覧
 */
const mo01334OnewayReport = ref<Mo01334OnewayType>({
  headers: [
    {
      title: t('label.report'),
      key: 'prtTitle',
      sortable: false,
      minWidth: '100',
    },
  ],
  items: [],
  height: 655,
})

/**
 * 出力帳票名一覧
 */
const mo01334TypeReport = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind({
  cpId: Or27592Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value?: boolean) => {
      mo00024.value.isOpen = value ?? Or27592Const.DEFAULT.IS_OPEN
    },
  },
})

/**
 * 帳票選択時のchoIndex更新
 */
watch(
  () => mo01334TypeReport.value.value,
  (newValue) => {
    if (newValue) {
      const selectedReport = mo01334OnewayReport.value.items.find((item) => item.id === newValue)
      if (selectedReport) {
        localOneway.Or27592.choIndex =
          selectedReport.prtNo === Or27592Const.CHOICE_INDEX.NORMAL_MODE ? '1' : '2'
      }
    }
  }
)

const isEdit = computed(() => {
  return useScreenStore().getCpNavControl(props.uniqueCpId)
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or00094Const.CP_ID(0)]: or00094.value,
  [Or21813Const.CP_ID(0)]: or21813.value,
  [Or21815Const.CP_ID(0)]: or21815.value,
  [Or10016Const.CP_ID(0)]: or10016.value,
  [Or26257Const.CP_ID(0)]: or26257.value,
  [OrX0130Const.CP_ID(0)]: orX0130.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [OrX0145Const.CP_ID(0)]: orX0145.value,
})

const { refValue } = useScreenTwoWayBind<Or27592TwoWayData>({
  cpId: Or27592Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

/**
 * 50音行番号と母音から50音文字配列を生成する
 *
 * @description
 * gojuonGyoBangoとgojuonBoinの組み合わせから、OrX0130で使用する50音文字配列を生成する
 *
 * @param gojuonGyoBango - 50音行番号 (1:あ行, 2:か行, 3:さ行, 4:た行, 5:な行, 6:は行, 7:ま行, 8:や行, 9:ら行, 10:わ行)
 *
 * @param gojuonBoin - 50音母音 (1:あ段, 2:い段, 3:う段, 4:え段, 5:お段)
 *
 * @returns 50音文字配列
 */
function convertGojuonToFocusSetting(gojuonGyoBango?: string, gojuonBoin?: string): string[] {
  // デフォルトは「全」を返す
  if (!gojuonGyoBango) {
    return [OrX0130Const.DEFAULT.STR_ALL]
  }

  // 特殊な値の処理
  if (gojuonGyoBango === Or27592Const.GOJUON.ALL || gojuonBoin === Or27592Const.GOJUON.ALL) {
    // 「全」を表す場合
    return [OrX0130Const.DEFAULT.STR_ALL]
  }

  if (gojuonGyoBango === Or27592Const.GOJUON.OTHER || gojuonBoin === Or27592Const.GOJUON.OTHER) {
    // 「他」を表す場合
    return [OrX0130Const.DEFAULT.STR_OTHER]
  }

  // gojuonBoinが未設定の場合、行全体を選択
  if (!gojuonBoin || gojuonBoin === '') {
    // 行番号から行の文字を取得
    const gojuonRowMap: Record<string, string[]> = {
      '1': ['あ', 'い', 'う', 'え', 'お'], // あ行
      '2': ['か', 'き', 'く', 'け', 'こ'], // か行
      '3': ['さ', 'し', 'す', 'せ', 'そ'], // さ行
      '4': ['た', 'ち', 'つ', 'て', 'と'], // た行
      '5': ['な', 'に', 'ぬ', 'ね', 'の'], // な行
      '6': ['は', 'ひ', 'ふ', 'へ', 'ほ'], // は行
      '7': ['ま', 'み', 'む', 'め', 'も'], // ま行
      '8': ['や', 'い', 'ゆ', 'え', 'よ'], // や行
      '9': ['ら', 'り', 'る', 'れ', 'ろ'], // ら行
      '10': ['わ', 'い', 'う', 'え', 'を'], // わ行
    }

    const rowChars = gojuonRowMap[gojuonGyoBango]
    if (rowChars) {
      return rowChars
    }
  }

  // 50音行番号と母音の組み合わせから文字を特定
  const gojuonMap: Record<string, Record<string, string>> = {
    '1': { '1': 'あ', '2': 'い', '3': 'う', '4': 'え', '5': 'お' }, // あ行
    '2': { '1': 'か', '2': 'き', '3': 'く', '4': 'け', '5': 'こ' }, // か行
    '3': { '1': 'さ', '2': 'し', '3': 'す', '4': 'せ', '5': 'そ' }, // さ行
    '4': { '1': 'た', '2': 'ち', '3': 'つ', '4': 'て', '5': 'と' }, // た行
    '5': { '1': 'な', '2': 'に', '3': 'ぬ', '4': 'ね', '5': 'の' }, // な行
    '6': { '1': 'は', '2': 'ひ', '3': 'ふ', '4': 'へ', '5': 'ほ' }, // は行
    '7': { '1': 'ま', '2': 'み', '3': 'む', '4': 'め', '5': 'も' }, // ま行
    '8': { '1': 'や', '2': 'い', '3': 'ゆ', '4': 'え', '5': 'よ' }, // や行
    '9': { '1': 'ら', '2': 'り', '3': 'る', '4': 'れ', '5': 'ろ' }, // ら行
    '10': { '1': 'わ', '2': 'い', '3': 'う', '4': 'え', '5': 'を' }, // わ行
  }

  const targetChar = gojuonMap[gojuonGyoBango]?.[gojuonBoin ?? '']

  if (targetChar) {
    return [targetChar]
  }

  // マッピングが見つからない場合は「全」を返す
  return [OrX0130Const.DEFAULT.STR_ALL]
}

onMounted(async () => {
  local.mo00020Type.value = systemCommonsStore.getSystemDate!
  local.mo00020RecordingDate.value = systemCommonsStore.getSystemDate!
  local.mo00020StartDate.value = systemCommonsStore.getSystemDate!
  local.mo00020EndDate.value = systemCommonsStore.getSystemDate!

  Or00094Logic.state.set({
    uniqueCpId: or00094.value.uniqueCpId,
    state: { dispSettingBtnDisplayFlg: true },
  })

  await getPrintSettingList()
  await initCodes()

  // 帳票選択モードに基づいてデフォルトの帳票を選択
  if (mo01334OnewayReport.value.items.length > 0) {
    mo01334TypeReport.value.value = mo01334OnewayReport.value.items[0].id
  }

  // シンプルモードの場合、一部の機能を無効化
  if (isSupportRecordDailyListMode.value) {
    // 記録日付をデフォルト値に設定
    if (!local.mo00020RecordingDate.value) {
      local.mo00020RecordingDate.value = systemCommonsStore.getSystemDate!
    }
  }

  // gojuonGyoBangoとgojuonBoinからfocusSettingInitialを生成
  const focusSettingInitial = convertGojuonToFocusSetting(
    localOneway.Or27592.gojuonGyoBango,
    localOneway.Or27592.gojuonBoin
  )

  // OrX0130のfocusSettingInitialを設定
  localOneway.orX0130Oneway.focusSettingInitial = focusSettingInitial

  // ⑬⑭⑮⑯ 初期値設定 - ログイン職員情報から設定
  const loginStaffInfo = systemCommonsStore.getLoginInfo
  if (loginStaffInfo) {
    // ⑬記録者id : 親画面.NDS共通初期ﾃﾞｰﾀ.ログイン職員情報.職員ID (共通情報)
    // const recorderId = systemCommonsStore.getStaffId ?? ''

    // ⑭記録者 : 親画面.NDS共通初期ﾃﾞｰﾀ.ログイン職員情報.職員名 (共通情報)
    const recorderName = systemCommonsStore.getCurrentUser.shokuinKnj ?? ''
    localOneway.mo01338OneWayAdministratorFilterByRecorder.value = recorderName

    // ⑮作成者id : 親画面.NDS共通初期ﾃﾞｰﾀ.ログイン職員情報.職員ID (共通情報)
    // const authorId = systemCommonsStore.getStaffId ?? ''

    // ⑯作成者 : 親画面.NDS共通初期ﾃﾞｰﾀ.ログイン職員情報.職員名 (共通情報)
    const authorName = systemCommonsStore.getCurrentUser.shokuinKnj ?? ''
    localOneway.mo01338OneWayAdministratorAuthor.value = authorName
  }

  // 担当ケアマネ設定フラグが0より大きい、かつ、担当者IDが0以外の場合
  // 担当ケアマネに担当者IDを設定する
  const currentManagerId = systemCommonsStore.getManagerId!
  if (
    localOneway.Or27592.careManagerInChargeSettingsFlag !==
      Or27592Const.CARE_MANAGER_FLAG.DISABLED &&
    currentManagerId &&
    currentManagerId !== Or27592Const.MANAGER_ID.DEFAULT
  ) {
    // TODO: 担当者IDから担当ケアマネの名前を取得して設定する
    // 現在は担当者IDをそのまま表示（実際の実装では名前を取得する必要がある）
    // localOneway.mo01338OneWayCareManagerInCharge2.value = `担当者ID: ${currentManagerId}`
    // OrX0145でのデフォルト選択は別途実装が必要
  }
})

/**
 * 汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CREATED_DATE_PRINT_TYPE },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_PRINTED_CARE_LEVEL_NEW },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_REGISTRATION_DATE_TYPE },
  ]

  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  // 回数区分
  localOneway.mo00039OneWayPrintCategory.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )

  localOneway.mo00039OneWayUserSelectType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )

  localOneway.mo00039OneWayPrintType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_CREATED_DATE_PRINT_TYPE
  )

  localOneway.mo00040OneWayRequiredCareLevel.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_PRINTED_CARE_LEVEL_NEW
  )

  localOneway.mo00040OneWayRegistrationDateCategory.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_REGISTRATION_DATE_TYPE
  )

  // 初期値
  local.mo00039TypeUserSelectType = Or27592Const.USER_SELECT_TYPE.TANI

  // 初期値設定 - Mo00039OneWayPrintType
  if (localOneway.mo00039OneWayPrintType.items.length > 0) {
    local.mo00039TypePrintType = (
      localOneway.mo00039OneWayPrintType.items[0] as { value: string }
    ).value
  }

  // 初期値設定
  if (localOneway.mo00040OneWayRequiredCareLevel.items.length > 0) {
    local.mo00040TypeRequiredCareLevel.modelValue = (
      localOneway.mo00040OneWayRequiredCareLevel.items[0] as { value: string }
    ).value
  }

  if (localOneway.mo00040OneWayRegistrationDateCategory.items.length > 0) {
    local.mo00040TypeRegistrationDateCategory.modelValue = (
      localOneway.mo00040OneWayRegistrationDateCategory.items[1] as { value: string }
    ).value
  }
}

/**
 * 印刷設定画面初期情報を取得する
 */
async function getPrintSettingList() {
  const inputData: PrintSettingScreenInitialInfoSelectInEntity = {
    sysCd: systemCommonsStore.getSystemCode ?? '1',
    sysRyaku: '3GK', // systemCommonsStore.getSystemAbbreviation ?? '3GK',
    kinounameKnj: 'PRT',
    kikanFlg: localOneway.Or27592.kikanFlg ?? '1',
    houjinId: systemCommonsStore.getHoujinId ?? '1',
    svJigyoId: '0', // systemCommonsStore.getSvJigyoId ?? '0',
    userId: '31', //localOneway.Or27592.userId ?? '1',
    shokuId: systemCommonsStore.getStaffId ?? '1',
    shisetuId: '19', // systemCommonsStore.getShisetuId ?? '1',
    cpnFlg: localOneway.Or27592.cpnFlg ?? '1',
    shosikiFlg: localOneway.Or27592.shosikiFlg ?? '1',
    sectionName: '[3GK]利用料請求書', // localOneway.Or27592.sectionName ?? '[3GK]利用料請求書',
    choIndex: localOneway.Or27592.choIndex || '1',
    kojinhogoFlg: '1',
    sectionAddNo: '000001',
  }

  const ret: PrintSettingScreenInitialInfoSelectOutEntity = await ScreenRepository.select(
    'printSettingsScreenInitialInfoSelectGUI1264',
    inputData
  )

  const mo01334OnewayList: Mo01334Items[] = []
  for (let index = 0; index < ret.data.choPrtList.length; index++) {
    const item = ret.data.choPrtList[index]
    if (item) {
      mo01334OnewayList.push({
        id: index.toString(),
        mo01337OnewayReport: {
          value: item.prtTitle,
          unit: '',
        } as Mo01337OnewayType,
        ...item,
      } as Mo01334Items)
    }
  }

  mo01334OnewayReport.value.items = mo01334OnewayList

  if (ret.data.choPrtList.length > 0) mo01334TypeReport.value.value = ret.data.choPrtList[0].choPro

  refValue.value = { choPrtList: ret.data.choPrtList }
  useScreenStore().setCpTwoWay({
    cpId: Or27592Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: refValue.value,
    isInit: true,
  })
  // OrX0130コンポーネントはユーザーの選択を処理します

  reportInitData.value = ret.data.iniDataObject
}

/**
 * 帳票イニシャライズデータを取得する。
 */
async function getReportInfoDataList() {
  const inputData: LedgerInitializeDataSelectInEntity = {
    // システムコード : 親画面.システムコード (共通情報)
    sysCd: systemCommonsStore.getSystemCode ?? '0',
    // 機能名 : "PRT"
    kinounameKnj: 'PRT',
    // 職員ID: 親画面.職員ID (共通情報)
    shokuId: systemCommonsStore.getStaffId ?? '0',
    // セクション : 出力帳票一覧明細.選択行.プロファイル
    sectionKnj: choPro.value,
    // 個人情報表示フラグ : ０
    kojinhogoFlg: '0',
    // 個人情報表示値 : ０
    sectionAddNo: '0',
  }

  // バックエンドAPIから初期情報取得
  const ret: LedgerInitializeDataSelectOutEntity = await ScreenRepository.select(
    'ledgerInitializeDataSelectGUI01264',
    inputData
  )
  reportInitData.value = ret.data.iniDataObject
}

/**
 * 印刷設定情報を保存する
 *
 * @param choPrtList - 出力帳票印刷情報リスト
 */
async function savePrintSettingInfo(choPrtList: IChoPrtInfo[]) {
  const inputData: PrintSettingsInfoUpdateInEntity = {
    sysCd: systemCommonsStore.getSystemCode ?? '1',
    sysRyaku: systemCommonsStore.getSystemAbbreviation ?? '1',
    kinounameKnj: 'PRT',
    houjinId: systemCommonsStore.getHoujinId ?? '1',
    shisetuId: systemCommonsStore.getShisetuId ?? '1',
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '1',
    shokuId: systemCommonsStore.getStaffId ?? '1',
    choPro: choPro.value,
    kojinhogoFlg: '0',
    sectionAddNo: '0',
    iniDataObject: { ...reportInitData.value },
    choPrtList: choPrtList.map((item) => ({
      ...item,
      defPrtTitle: item.prtTitle,
    })),
    modifiedCnt: '1',
  }

  // バックエンドAPIから初期情報取得
  await ScreenRepository.update('printSettingsInfoUpdateGUI011264', inputData)
}

/**
 * エラーダイアログの開閉
 *
 * @returns ダイアログの選択結果（yes）
 */
async function showOr21813MsgOneBtn() {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      dialogTitle: t('label.error'),
      dialogText: t('message.e-cmn-40172'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813.value.uniqueCpId)

        let result = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }

        // 確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 警告ダイアログの開閉
 *
 * @returns ダイアログの選択結果（yes）
 */
async function showOr21815MsgOneBtn() {
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.caution'),
      // ダイアログテキスト
      iconName: 'warning',
      dialogText: t('message.w-cmn-20845'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815.value.uniqueCpId)

        let result = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }

        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: or21815.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 帳票タイトルが入力していない場合
 */
async function checkTitleInput() {
  if (local.titleInput.value) return
  const dialogResult = await showOr21815MsgOneBtn()
  switch (dialogResult) {
    case 'yes': {
      let label = ''
      for (const item of mo01334OnewayReport.value.items) {
        if (item) {
          if (mo01334TypeReport.value.value === item.id && item.mo01337OnewayReport) {
            const data = item.mo01337OnewayReport as Mo01337OnewayType
            label = data.value
          }
        }
      }
      local.titleInput.value = label
      break
    }
  }
  return
}

/**
 * 変更データを取得する
 *
 * @returns 出力帳票印刷情報リスト
 */
async function getDataTable() {
  const choPrtList = mo01334OnewayReport.value.items.map(({ id, mo01337OnewayReport, ...rest }) => {
    if (id === mo01334TypeReport.value.value) {
      return {
        ...rest,
        prtTitle: local.titleInput.value,
        prndate: local.mo00039TypePrintCategory,
        // ①敬称を変更する : パラメータ03
        param03: local.mo00018TypeChangeTitle.modelValue ? '1' : '0',
        // ②敬称 : パラメータ04
        param04: getValueOrDefault(local.textInput.value, isFieldDisabled.textInput.value, ''),
        // ⑤記録者を印刷する : パラメータ05
        param05: local.mo00018TypePrintRecorder.modelValue ? '1' : '0',
        // ⑧印刷する要介護度 : パラメータ06
        param06: local.mo00040TypeRequiredCareLevel.modelValue ?? '',
        // ⑪確認項目を印刷する : パラメータ07
        param07: getValueOrDefault(
          local.mo00018TypePrintConfirmationItems.modelValue ? '1' : '0',
          isFieldDisabled.printConfirmationItems.value,
          '0'
        ),
        // ⑩時間を印刷する : パラメータ08
        param08: local.mo00018TypePrintTime.modelValue ? '1' : '0',
        // ④記録者で絞り込む : パラメータ09
        param09: local.mo00018TypeFilterByRecorder.modelValue ? '1' : '0',
        // ③記入用シートを印刷する : パラメータ10
        param10: getValueOrDefault(
          local.mo00018TypePrintTheForm.modelValue ? '1' : '0',
          isFieldDisabled.printTheForm.value,
          '0'
        ),
        // ⑫H27年様式で印刷する : パラメータ16 (正しいparameter)
        param11: getValueOrDefault(
          local.mo00018TypePrintCarePrevention.modelValue ? '1' : '0',
          isFieldDisabled.printCarePrevention.value,
          '0'
        ),
        // ⑬記録者id : 親画面.NDS共通初期ﾃﾞｰﾀ.ログイン職員情報.職員ID (共通情報)
        param12: systemCommonsStore.getStaffId ?? '',
        // ⑦登録日区分 : パラメータ13
        param13: local.mo00040TypeRegistrationDateCategory.modelValue ?? '',
        // ⑥作成年月日印刷区分 : パラメータ14
        param14: local.mo00039TypePrintType ?? '',
        // ⑨期間を印刷する : パラメータ15
        param15: getValueOrDefault(
          local.mo00018TypeTimePeriod.modelValue ? '1' : '0',
          isFieldDisabled.timePeriod.value,
          '0'
        ),
        // ⑫H27年様式で印刷する : パラメータ16 (正しいmapping)
        param16: getValueOrDefault(
          local.mo00018TypePrintCarePrevention.modelValue ? '1' : '0',
          isFieldDisabled.printCarePrevention.value,
          '0'
        ),
      }
    }
    return {
      ...rest,
    }
  }) as IChoPrtInfo[]

  refValue.value = { choPrtList: choPrtList }

  await nextTick()

  return choPrtList
}

/**
 * 閉じる
 */
async function close() {
  await checkTitleInput()

  const choPrtList = await getDataTable()

  if (!isSupportRecordDailyListMode.value && isEdit.value) {
    await savePrintSettingInfo(choPrtList)
  }

  setState({ isOpen: false })
}

/**
 * 印刷
 */
async function pdfDownload() {
  await checkTitleInput()

  // バリデーション実行
  const isValid = await validatePrintTargets()
  if (!isValid) {
    return
  }

  const choPrtList = await getDataTable()

  // シンプルモードでない場合、編集権限がある場合は設定を保存
  if (!isSupportRecordDailyListMode.value && isEdit.value) {
    await savePrintSettingInfo(choPrtList)
  }

  // PDF出力実行
  if (isSupportRecordDailyListMode.value) {
    // 簡単モードの場合、専用のAPI呼び出し
    await executeSimplePdfOutput()
  } else {
    // 通常モードの場合、選択された利用者でPDF出力
    if (local.mo00039TypeUserSelectType === Or27592Const.USER_SELECT_TYPE.TANI) {
      // 単一選択の場合
      if (showPrintCarePrevention.value && local.mo00018TypePrintCarePrevention.modelValue) {
        // 介護予防印刷が選択されている場合
        await executePreventionPdfOutput(selectedUsers.value[0])
      } else {
        // 支援経過記録様式により判断
        if (localOneway.Or27592.shienKeikaKirokuYoshiki === '2') {
          // R3/4改訂版の場合
          await executeR34PdfOutput(selectedUsers.value[0])
        } else {
          // 改訂前（通常）の場合
          await executeNormalPdfOutput(selectedUsers.value[0])
        }
      }
    } else {
      // 複数選択の場合
      for (const user of selectedUsers.value) {
        if (showPrintCarePrevention.value && local.mo00018TypePrintCarePrevention.modelValue) {
          // 介護予防印刷が選択されている場合
          await executePreventionPdfOutput(user)
        } else {
          // 支援経過記録様式により判断
          if (localOneway.Or27592.shienKeikaKirokuYoshiki === '2') {
            // R3/4改訂版の場合
            await executeR34PdfOutput(user)
          } else {
            // 改訂前（通常）の場合
            await executeNormalPdfOutput(user)
          }
        }
      }
    }
  }

  setState({ isOpen: false })
}

/**
 * （ダイアログ）の開閉状態を監視
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    mo00024.value.isOpen = true
    if (!newValue) {
      await close()
    }
  }
)

/**
 * 「出力帳票名」選択
 */
watch(
  () => mo01334TypeReport.value.value,
  async (newValue, oldValue) => {
    if (oldValue) {
      for (const item of mo01334OnewayReport.value.items) {
        if (item) {
          if (oldValue === item.id) {
            // 画面.帳票タイトルが空白以外の場合
            if (local.titleInput.value) {
              item.prtTitle = local.titleInput.value
            }
            // 画面.出力帳票一覧明細に切替前選択される行.日付表示有無 = 画面.日付印刷区分
            item.prndate = local.mo00039TypePrintCategory
            // ①敬称を変更する : パラメータ03
            item.param03 = local.mo00018TypeChangeTitle.modelValue ? '1' : '0'
            // ②敬称 : パラメータ04
            item.param04 = getValueOrDefault(
              local.textInput.value,
              isFieldDisabled.textInput.value,
              ''
            )
            // ⑤記録者を印刷する : パラメータ05
            item.param05 = local.mo00018TypePrintRecorder.modelValue ? '1' : '0'
            // ⑧印刷する要介護度 : パラメータ06
            item.param06 = local.mo00040TypeRequiredCareLevel.modelValue ?? ''
            // ⑪確認項目を印刷する : パラメータ07
            item.param07 = getValueOrDefault(
              local.mo00018TypePrintConfirmationItems.modelValue ? '1' : '0',
              isFieldDisabled.printConfirmationItems.value,
              '0'
            )
            // ⑥時間を印刷する : パラメータ08
            item.param08 = local.mo00018TypePrintTime.modelValue ? '1' : '0'
            // ④記録者で絞り込む : パラメータ09
            item.param09 = local.mo00018TypeFilterByRecorder.modelValue ? '1' : '0'
            // ③記入用シートを印刷する : パラメータ10
            item.param10 = getValueOrDefault(
              local.mo00018TypePrintTheForm.modelValue ? '1' : '0',
              isFieldDisabled.printTheForm.value,
              '0'
            )
            // ⑫H27年様式で印刷する : パラメータ11 (暫定、本来はparam16)
            item.param11 = getValueOrDefault(
              local.mo00018TypePrintCarePrevention.modelValue ? '1' : '0',
              isFieldDisabled.printCarePrevention.value,
              '0'
            )
            // ⑦登録日区分 : パラメータ13
            item.param13 = local.mo00040TypeRegistrationDateCategory.modelValue ?? ''
            // ⑥作成年月日印刷区分 : パラメータ14
            item.param14 = local.mo00039TypePrintType ?? ''
            // ⑨期間を印刷する : パラメータ15
            item.param15 = getValueOrDefault(
              local.mo00018TypeTimePeriod.modelValue ? '1' : '0',
              isFieldDisabled.timePeriod.value,
              '0'
            )
            // ⑫H27年様式で印刷する : パラメータ16 (正しいmapping)
            item.param16 = getValueOrDefault(
              local.mo00018TypePrintCarePrevention.modelValue ? '1' : '0',
              isFieldDisabled.printCarePrevention.value,
              '0'
            )
          }
        }
      }
    }

    for (const item of mo01334OnewayReport.value.items) {
      if (item) {
        if (newValue === item.id) {
          // 画面.帳票タイトル = 画面.出力帳票一覧明細に選択される行.帳票タイトル
          local.titleInput.value = item?.prtTitle as string
          // 画面.出力帳票一覧明細に切替前選択される行.日付表示有無 = 画面.日付印刷区分
          local.mo00039TypePrintCategory = item?.prndate as string
          // ①敬称を変更する = 画面.出力帳票一覧明細に選択される行.パラメータ03
          local.mo00018TypeChangeTitle.modelValue =
            item?.param03 === Or27592Const.PARAMETER.ACTIVE ? true : false
          // ②敬称 = 画面.出力帳票一覧明細に選択される行.パラメータ04
          local.textInput.value = item?.param04 as string
          // ⑤記録者を印刷する = 画面.出力帳票一覧明細に選択される行.パラメータ05
          local.mo00018TypePrintRecorder.modelValue =
            item?.param05 === Or27592Const.PARAMETER.ACTIVE ? true : false
          // ⑧印刷する要介護度 = 画面.出力帳票一覧明細に選択される行.パラメータ06
          if (item?.param06) {
            local.mo00040TypeRequiredCareLevel.modelValue = item.param06 as string
          }
          // ⑪確認項目を印刷する = 画面.出力帳票一覧明細に選択される行.パラメータ07
          local.mo00018TypePrintConfirmationItems.modelValue =
            item?.param07 === Or27592Const.PARAMETER.ACTIVE ? true : false
          // ⑥時間を印刷する = 画面.出力帳票一覧明細に選択される行.パラメータ08
          local.mo00018TypePrintTime.modelValue =
            item?.param08 === Or27592Const.PARAMETER.ACTIVE ? true : false
          // ④記録者で絞り込む = 画面.出力帳票一覧明細に選択される行.パラメータ09
          local.mo00018TypeFilterByRecorder.modelValue =
            item?.param09 === Or27592Const.PARAMETER.ACTIVE ? true : false
          // ③記入用シートを印刷する = 画面.出力帳票一覧明細に選択される行.パラメータ10
          local.mo00018TypePrintTheForm.modelValue =
            item?.param10 === Or27592Const.PARAMETER.ACTIVE ? true : false
          // ⑫H27年様式で印刷する = 画面.出力帳票一覧明細に選択される行.パラメータ11 (暫定、本来はparam16)
          local.mo00018TypePrintCarePrevention.modelValue =
            item?.param11 === Or27592Const.PARAMETER.ACTIVE ? true : false
          // ⑦登録日区分 = 画面.出力帳票一覧明細に選択される行.パラメータ13
          if (item?.param13) {
            local.mo00040TypeRegistrationDateCategory.modelValue = item.param13 as string
          }
          // ⑥作成年月日印刷区分 = 画面.出力帳票一覧明細に選択される行.パラメータ14
          if (item?.param14) {
            local.mo00039TypePrintType = item.param14 as string
          }
          // ⑨期間を印刷する = 画面.出力帳票一覧明細に選択される行.パラメータ15
          local.mo00018TypeTimePeriod.modelValue =
            item?.param15 === Or27592Const.PARAMETER.ACTIVE ? true : false
          // ⑫H27年様式で印刷する = 画面.出力帳票一覧明細に選択される行.パラメータ16 (正しいmapping)
          if (item?.param16) {
            local.mo00018TypePrintCarePrevention.modelValue =
              item.param16 === Or27592Const.PARAMETER.ACTIVE ? true : false
          }

          choPro.value = item?.choPro as string

          await getReportInfoDataList()
        }
      }
    }
  }
)

/**
 * 「利用者選択方」ラジオボタン選択
 */
watch(
  () => local.mo00039TypeUserSelectType,
  () => {
    // OrX0130のselectModeを更新
    if (local.mo00039TypeUserSelectType === Or27592Const.USER_SELECT_TYPE.TANI) {
      localOneway.orX0130Oneway.selectMode = OrX0130Const.DEFAULT.TANI
      localOneway.mo00018OneWayPrintTheForm.disabled = false
    } else {
      localOneway.orX0130Oneway.selectMode = OrX0130Const.DEFAULT.HUKUSUU
      local.mo00018TypePrintTheForm.modelValue = false
      localOneway.mo00018OneWayPrintTheForm.disabled = true
    }
  }
)

/**
 * 「記入用シートを印刷する」チェックボックスの変更を監視
 */
watch(
  () => local.mo00018TypePrintTheForm.modelValue,
  (newValue) => {
    // 「期間を印刷する」チェックボックスの無効状態を計算
    localOneway.mo00018OneWayTimePeriod.disabled = newValue
  }
)

/**
 * ラジオボタンの選択状態を追跡する
 */
watch(
  () => local.mo00039TypePrintCategory,
  async () => {
    await checkTitleInput()
  }
)

/**
 * Mo00040RequiredCareLevelの変更を追跡する
 */
watch(
  () => localOneway.mo00040OneWayRequiredCareLevel.items,
  (newItems) => {
    if (newItems && newItems.length > 0 && !local.mo00040TypeRequiredCareLevel.modelValue) {
      local.mo00040TypeRequiredCareLevel.modelValue = (newItems[0] as { value: string }).value
    }
  }
)

/**
 * Mo00040RegistrationDateCategoryの変更を追跡する
 */
watch(
  () => localOneway.mo00040OneWayRegistrationDateCategory.items,
  (newItems) => {
    if (newItems && newItems.length > 0 && !local.mo00040TypeRegistrationDateCategory.modelValue) {
      local.mo00040TypeRegistrationDateCategory.modelValue = (
        newItems[0] as { value: string }
      ).value
    }
  }
)

/**
 * Mo00039OneWayPrintTypeの変更を追跡する
 */
watch(
  () => localOneway.mo00039OneWayPrintType.items,
  (newItems) => {
    if (newItems && newItems.length > 0 && !local.mo00039TypePrintType) {
      local.mo00039TypePrintType = (newItems[0] as { value: string }).value
    }
  }
)

/**
 *  テーブルの変更を追跡する
 */
watch(
  () => mo01334OnewayReport.value.items,
  () => {
    const choPrtList = [
      ...mo01334OnewayReport.value.items.map(({ id, mo01337OnewayReport, ...rest }) => ({
        ...rest,
      })),
    ] as IChoPrtInfo[]
    refValue.value = { choPrtList: choPrtList }
  },
  { deep: true }
)

/**
 * 介護支援経過日別一覧のモードかどうか
 */
const isSupportRecordDailyListMode = computed(() => {
  return localOneway.Or27592.choIndex === Or27592Const.CHOICE_INDEX.SUPPORT_RECORD_DAILY_LIST_MODE
})

/**
 * 担当ケアマネが無効化されているかどうか
 */
const isCareManagerDisabled = computed(() => {
  return (
    localOneway.Or27592.careManagerInChargeSettingsFlag ===
      Or27592Const.CARE_MANAGER_FLAG.DISABLED &&
    systemCommonsStore.getManagerId !== Or27592Const.MANAGER_ID.DEFAULT
  )
})

/**
 * 確認項目を印刷するチェックボックスを表示するかどうか
 * systemAbbreviation === "CMN" かつ SvJigyoId !== "50010" の場合のみ表示
 */
const showPrintConfirmationItems = computed(() => {
  return (
    systemCommonsStore.getSystemAbbreviation === Or27592Const.SYSTEM_ABBREVIATION.CMN &&
    systemCommonsStore.getSvJigyoId !== Or27592Const.SPECIAL_OFFICE_ID.ID_50010
  )
})

/**
 * 介護予防印刷チェックボックスを表示するかどうか
 * choIndex === '1' かつ svJigyoId === '50010' の場合のみ表示
 */
const showPrintCarePrevention = computed(() => {
  return (
    localOneway.Or27592.choIndex === Or27592Const.CHOICE_INDEX.NORMAL_MODE &&
    systemCommonsStore.getSvJigyoId === Or27592Const.SPECIAL_OFFICE_ID.ID_50010
  )
})

/**
 * 各フィールドがdisabledかどうかを判定するヘルパー関数
 */
const isFieldDisabled = {
  // タイトル入力フィールド
  titleInput: computed(() => !isSupportRecordDailyListMode.value),

  // 敬称テキストボックス
  textInput: computed(() => !local.mo00018TypeChangeTitle.modelValue),

  // 記録者で絞り込むボタン
  filterByRecorderButton: computed(() => !local.mo00018TypeFilterByRecorder.modelValue),

  // 記入用シートを印刷する（複数選択時）
  printTheForm: computed(
    () => local.mo00039TypeUserSelectType !== Or27592Const.USER_SELECT_TYPE.TANI
  ),

  // 期間を印刷する（記入用シートが選択されている時）
  timePeriod: computed(() => local.mo00018TypePrintTheForm.modelValue),

  // 担当ケアマネ
  careManager: computed(() => isCareManagerDisabled.value),

  // 確認項目を印刷する（条件によって非表示になる場合）
  printConfirmationItems: computed(() => !showPrintConfirmationItems.value),

  // 介護予防印刷（条件によって非表示になる場合）
  printCarePrevention: computed(() => !showPrintCarePrevention.value),
}

/**
 * disabledなフィールドの値を安全に取得するヘルパー関数
 *
 * @param value - 元の値
 *
 * @param isDisabled - disabledかどうか
 *
 * @param defaultValue - disabled時のデフォルト値
 *
 * @returns disabled時はデフォルト値、そうでなければ元の値
 */
function getValueOrDefault<T>(value: T, isDisabled: boolean, defaultValue: T): T {
  return isDisabled ? defaultValue : value
}

const or26257Type = ref<Or26257Type>({
  shokuin: {
    chkShokuId: '',
    shokuin1Kana: '',
    shokuin2Kana: '',
    shokuin1Knj: '',
    shokuin2Knj: '',
    sexFlg: '',
    birthdayYmd: '',
    zip: '',
    addressKnj: '',
    tel: '',
    inYmd: '',
    outYmd: '',
    shokushuId: '',
    shokuNumber: '',
    keitaitel: '',
    shokushuId2: '',
    shokushuId3: '',
    shokushuId4: '',
    shokushuId5: '',
    shikakuId1: '',
    shikakuId2: '',
    shikakuId3: '',
    shikakuId4: '',
    shikakuId5: '',
    shokushuKnj1: '',
    shokushuKnj2: '',
    shokushuKnj3: '',
    shokushuKnj4: '',
    shokushuKnj5: '',
    shikakuKnj1: '',
    shikakuKnj2: '',
    shikakuKnj3: '',
    shikakuKnj4: '',
    shikakuKnj5: '',
    koyouState: '',
    stopFlg: '',
    nenrei: '',
    validFlg: '',
    colorFlg: '',
  },
  svJigyoIdList: [],
})

/**
 * Or26257のモーダルのデータを監視する変数
 */
const or26257Data = ref<Or26257OnewayType>({
  // システム略称
  sysCdKbn: '',
  // アカウント設定
  secAccountAllFlg: '0',
  // 適用事業所IDリスト
  svJigyoIdList: [{ svJigyoId: systemCommonsStore.getSvJigyoId! }],
  // 職員ID
  shokuinId: '',
  // システムコード
  gsysCd: '',
  // モード
  selectMode: Or26257Const.DEFAULT.SELECT_MODE_12,
  // 基準日
  kijunYmd: systemCommonsStore.getSystemDate!,
  // 事業所ID
  defSvJigyoId: systemCommonsStore.getSvJigyoId!,
  // フィルターフラグ
  filterDwFlg: '1',
  // 雇用状態
  koyouState: '0',
  // 地域フラグ
  areaFlg: '0',
  // 表示名称リスト
  hyoujiColumnList: [{ hyoujiColumn: 'shokushu_id' }],
  // 未設定フラグ
  misetteiFlg: '1',
  // 他職員参照権限
  otherRead: '',
  // 中止フラグ
  refStopFlg: '0',
  // 処理フラグ
  syoriFlg: '',
  // メニュー1ID
  menu1Id: '',
  // 件数フラグ
  kensuFlg: '0',
  shokuinIdList: [],
})

/**
 * Or26257モーダルの状態を監視する変数
 */
const showDialogOr26257 = computed(() => {
  return Or26257Logic.state.get(or26257.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 職員検索ダイアログを開く
 *
 * @param source - ダイアログの呼び出し元（filterByRecorder: 記録者、author: 作成者）
 */
function openStaffSearchDialog(source: 'filterByRecorder' | 'author') {
  // 結果処理のためにダイアログの呼び出し元を設定
  dialogSource.value = source

  // Or26257ダイアログを開く
  Or26257Logic.state.set({
    uniqueCpId: or26257.value.uniqueCpId,
    state: { isOpen: true },
  })
}

// ダイアログの呼び出し元を保存し、戻り値を処理
const dialogSource = ref<'filterByRecorder' | 'author' | ''>(Or27592Const.DIALOG_SOURCE.EMPTY)

// Or26257モーダルから返されるデータの変更を監視
watch(
  () => or26257Type.value,
  (newValue) => {
    if (dialogSource.value === Or27592Const.DIALOG_SOURCE.FILTER_BY_RECORDER) {
      // 記録者の名前を更新
      localOneway.mo01338OneWayAdministratorFilterByRecorder.value =
        `${newValue.shokuin.shokuin1Knj} ${newValue.shokuin.shokuin2Knj}`.trim()
    } else if (dialogSource.value === Or27592Const.DIALOG_SOURCE.AUTHOR) {
      // 作成者の名前を更新
      localOneway.mo01338OneWayAdministratorAuthor.value =
        `${newValue.shokuin.shokuin1Knj} ${newValue.shokuin.shokuin2Knj}`.trim()
    }
  },
  { deep: true }
)

/**
 * 記録者を選択
 */
function selectFilterByRecorder() {
  openStaffSearchDialog(Or27592Const.DIALOG_SOURCE.FILTER_BY_RECORDER)
}

/**
 * 作成者を選択
 */
function selectAuthor() {
  openStaffSearchDialog(Or27592Const.DIALOG_SOURCE.AUTHOR)
}

/**
 * Or10016モーダルの状態を監視する変数
 */
const showDialogOr10016 = computed(() => {
  return Or10016Logic.state.get(or10016.value.uniqueCpId)?.isOpen ?? false
})

/**
 * Or10016 印鑑欄設定ダイアログを開く
 */
function openSealColumnDialog() {
  localOneway.or10016Oneway.reportSectionNumber = localOneway.Or27592.choIndex

  Or10016Logic.state.set({
    uniqueCpId: or10016.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * choIndexが変更された場合、or10016OnewayのreportSectionNumberを更新します。
 */
watch(
  () => localOneway.Or27592.choIndex,
  (newValue) => {
    localOneway.or10016Oneway.reportSectionNumber = newValue
  }
)

/**
 * props.onewayModelValue の変更を監視
 */
watch(
  () => props.onewayModelValue,
  (newValue) => {
    if (newValue) {
      // Or27592の値を更新
      Object.assign(localOneway.Or27592, newValue)

      // gojuonGyoBangoとgojuonBoinからfocusSettingInitialを生成
      const focusSettingInitial = convertGojuonToFocusSetting(
        localOneway.Or27592.gojuonGyoBango,
        localOneway.Or27592.gojuonBoin
      )

      // OrX0130のfocusSettingInitialを設定
      localOneway.orX0130Oneway.focusSettingInitial = focusSettingInitial
    }
  },
  { deep: true, immediate: true }
)

/**
 * 「利用者選択」の監視 - OrX0130
 */
watch(
  () => OrX0130Logic.event.get(orX0130.value.uniqueCpId),
  (newValue) => {
    if (newValue?.clickFlg && newValue.userList.length > 0) {
      // 選択された利用者情報を取得
      selectedUsers.value = newValue.userList
      // 利用者IDを設定（単一選択の場合）
      if (local.mo00039TypeUserSelectType === Or27592Const.USER_SELECT_TYPE.TANI) {
        localOneway.Or27592.userId = newValue.userList[0].userId
      }
    }
  }
)

/**
 * 選択された利用者リスト
 */
const selectedUsers = ref<OrX0130TableType[]>([])

/**
 * 帳票ID
 */
const reportId = ref('SpecialistPassageReportService')

/**
 * 簡単モード用帳票ID
 */
const simpleReportId = ref('SpecialistPassageByDateReportService')

/**
 * 介護予防用帳票ID
 */
const preventionReportId = ref('PreventCareElapsedRecordReportService')

/**
 * R3/4改訂版用帳票ID
 */
const r34ReportId = ref('SpecialistPassageR34ReportService')

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no)
 */
const openConfirmDialog = async (paramDialogText: string): Promise<'yes' | 'no'> => {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: paramDialogText,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result: 'yes' | 'no' = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })
        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 印刷対象バリデーション
 */
async function validatePrintTargets(): Promise<boolean> {
  // プロファイルチェック
  if (!choPro.value) {
    await showOr21813MsgOneBtn()
    return false
  }

  // シンプルモードの場合、記録日付のチェックのみ
  if (isSupportRecordDailyListMode.value) {
    if (!local.mo00020RecordingDate.value) {
      const dialogResult = await openConfirmDialog(t('message.i-cmn-11455'))
      return dialogResult === Or27592Const.DIALOG_RESULT.NO
    }
    return true
  }

  // 通常モードの場合、利用者選択チェック
  if (local.mo00039TypeUserSelectType === Or27592Const.USER_SELECT_TYPE.TANI) {
    // 単一選択の場合
    if (selectedUsers.value.length === Or27592Const.DEFAULT.ZERO_NUMBER) {
      const dialogResult = await openConfirmDialog(t('message.i-cmn-11393'))
      return dialogResult === Or27592Const.DIALOG_RESULT.NO
    }
  } else {
    // 複数選択の場合
    if (selectedUsers.value.length === Or27592Const.DEFAULT.ZERO_NUMBER) {
      const dialogResult = await openConfirmDialog(t('message.i-cmn-11393'))
      return dialogResult === Or27592Const.DIALOG_RESULT.NO
    }
  }

  return true
}

/**
 * 通常モード用帳票データを作成する
 *
 * @param user - 利用者情報
 *
 * @returns 帳票データ
 */
function createNormalReportData(user: OrX0130TableType): SpecialistPassageReportInEntity {
  return {
    // 法人ID
    houjinId: systemCommonsStore.getHoujinId!,
    // 初期設定マスタ
    cksFlg: localOneway.Or27592.shosikiFlg,
    // 期間を印刷する
    keikaTimeFlg: getValueOrDefault(
      local.mo00018TypeTimePeriod.modelValue ? '1' : '0',
      isFieldDisabled.timePeriod.value,
      '0'
    ),
    // 印刷する要介護度
    yokaiKbn: local.mo00040TypeRequiredCareLevel.modelValue ?? '',
    // 利用者ID
    userId: '1', //user.userId,
    // 指定日
    designationDate: local.mo00020StartDate.value,
    // 施設ID
    shisetuId: systemCommonsStore.getShisetuId!,
    // 帳票セクション番号
    sheetNo: localOneway.Or27592.choIndex || '1',
    // 事業者ID
    svJigyoId: systemCommonsStore.getSvJigyoId!,
    // 事業者名
    svJigyoKnj: localOneway.Or27592.svJigyoKnj,
    // 作成者名
    shokuName: localOneway.mo01338OneWayAdministratorAuthor.value,
    // 作成者の順番
    shokuKubun: local.mo00039TypePrintType ?? '',
    // 曜日表示
    displayDayOfWeek: getValueOrDefault(
      local.mo00018TypeTimePeriod.modelValue ? '1' : '0',
      isFieldDisabled.timePeriod.value,
      '0'
    ),
    // 時間表示
    displayTime: local.mo00018TypePrintTime.modelValue ? '1' : '0',
    // 記録者を印字する
    displayRecorder: local.mo00018TypePrintRecorder.modelValue ? '1' : '0',
    // 時間を印刷する
    printTimeFlag: local.mo00018TypePrintTime.modelValue ? '1' : '0',
    // システムコード
    sysCd: systemCommonsStore.getSystemCode!,
    // システム日付
    asYmd: systemCommonsStore.getSystemDate!,
    // 印刷設定
    printSet: {
      shiTeiKubun: local.mo00039TypePrintCategory,
      shiTeiDate: local.mo00020StartDate.value,
    } as PrintSet,
    // 印刷オプション
    printOption: {
      emptyFlg: getValueOrDefault(
        local.mo00018TypePrintTheForm.modelValue ? '1' : '0',
        isFieldDisabled.printTheForm.value,
        '0'
      ),
      keishoKnj: getValueOrDefault(local.textInput.value, isFieldDisabled.textInput.value, ''),
      keishoFlg: local.mo00018TypeChangeTitle.modelValue ? '1' : '0',
    } as PrintOption,
    // 印刷対象履歴リスト
    printSubjectHistoryList: [
      {
        userId: user.userId,
        userName: `${user.name1Knj} ${user.name2Knj}`.trim(),
        startYmd: local.mo00020StartDate.value,
        endYmd: local.mo00020EndDate.value,
        assType: '',
      },
    ] as PrintSubjectHistory[],
  }
}

/**
 * 簡単モード用帳票データを作成する
 *
 * @returns 帳票データ
 */
function createSimpleReportData(): SpecialistPassageByDateReportServiceInEntity {
  return {
    // 初期設定マスタ
    cksFlg: localOneway.Or27592.shosikiFlg,
    // 利用者IDリスト（簡単モードでは固定値または親画面から取得）
    userIdList: [localOneway.Or27592.userId || '1'],
    // 事業者ID
    svJigyoId: systemCommonsStore.getSvJigyoId!,
    // 事業者名
    svJigyoKnj: localOneway.Or27592.svJigyoKnj,
    // 記録日
    yymmYmd: local.mo00020RecordingDate.value,
    // 印刷設定
    printSet: {
      shiTeiKubun: local.mo00039TypePrintCategory,
      shiTeiDate: local.mo00020RecordingDate.value,
    } as SimplePrintSet,
  }
}

/**
 * R3/4改訂版用帳票データを作成する
 *
 * @param user - 利用者情報
 *
 * @returns 帳票データ
 */
function createR34ReportData(user: OrX0130TableType): SpecialistPassageR34ReportServiceInEntity {
  return {
    // 法人ID
    houjinId: systemCommonsStore.getHoujinId!,
    // 初期設定マスタ
    cksFlg: localOneway.Or27592.shosikiFlg,
    // 期間を印刷する
    keikaTimeFlg: getValueOrDefault(
      local.mo00018TypeTimePeriod.modelValue ? '1' : '0',
      isFieldDisabled.timePeriod.value,
      '0'
    ),
    // 印刷する要介護度
    yokaiKbn: local.mo00040TypeRequiredCareLevel.modelValue ?? '',
    // 利用者ID
    userId: user.userId,
    // 指定日
    designationDate: local.mo00020StartDate.value,
    // 施設ID
    shisetuId: systemCommonsStore.getShisetuId!,
    // 帳票セクション番号
    sheetNo: localOneway.Or27592.choIndex || '1',
    // 事業者ID
    svJigyoId: systemCommonsStore.getSvJigyoId!,
    // 事業者名
    svJigyoKnj: localOneway.Or27592.svJigyoKnj,
    // 作成者名
    shokuName: localOneway.mo01338OneWayAdministratorAuthor.value,
    // 作成者の順番
    shokuKubun: local.mo00039TypePrintType ?? '',
    // 曜日表示
    displayDayOfWeek: getValueOrDefault(
      local.mo00018TypeTimePeriod.modelValue ? '1' : '0',
      isFieldDisabled.timePeriod.value,
      '0'
    ),
    // 時間表示
    displayTime: local.mo00018TypePrintTime.modelValue ? '1' : '0',
    // 記録者を印字する
    displayRecorder: local.mo00018TypePrintRecorder.modelValue ? '1' : '0',
    // 時間を印刷する
    printTimeFlag: local.mo00018TypePrintTime.modelValue ? '1' : '0',
    // システムコード
    sysCd: systemCommonsStore.getSystemCode!,
    // システム日付
    asYmd: systemCommonsStore.getSystemDate!,
    // 印刷設定
    printSet: {
      shiTeiKubun: local.mo00039TypePrintCategory,
      shiTeiDate: local.mo00020StartDate.value,
    } as R34PrintSet,
    // 印刷オプション
    printOption: {
      emptyFlg: getValueOrDefault(
        local.mo00018TypePrintTheForm.modelValue ? '1' : '0',
        isFieldDisabled.printTheForm.value,
        '0'
      ),
      keishoKnj: getValueOrDefault(local.textInput.value, isFieldDisabled.textInput.value, ''),
      keishoFlg: local.mo00018TypeChangeTitle.modelValue ? '1' : '0',
    } as R34PrintOption,
    // 印刷対象履歴リスト
    printSubjectHistoryList: [
      {
        userId: user.userId,
        userName: `${user.name1Knj} ${user.name2Knj}`.trim(),
        startYmd: local.mo00020StartDate.value,
        endYmd: local.mo00020EndDate.value,
        assType: '',
      },
    ] as R34PrintSubjectHistory[],
  }
}

/**
 * 介護予防用帳票データを作成する
 *
 * @param user - 利用者情報
 *
 * @returns 帳票データ
 */
function createPreventionReportData(
  user: OrX0130TableType
): PreventCareElapsedRecordReportServiceInEntity {
  return {
    // レポート区分
    rptKbn: localOneway.Or27592.choIndex || '1',
    // 曜日表示
    displayDayOfWeek: getValueOrDefault(
      local.mo00018TypeTimePeriod.modelValue ? 'true' : 'false',
      isFieldDisabled.timePeriod.value,
      'false'
    ),
    // 記録者を印字する
    displayRecorder: local.mo00018TypePrintRecorder.modelValue ? 'true' : 'false',
    // 時間表示
    displayTime: local.mo00018TypePrintTime.modelValue ? 'true' : 'false',
    // 時間を印刷する
    printTimeFlag: local.mo00018TypePrintTime.modelValue ? 'true' : 'false',
    // 期間を印刷する
    periodPrintFlag: getValueOrDefault(
      local.mo00018TypeTimePeriod.modelValue ? 'true' : 'false',
      isFieldDisabled.timePeriod.value,
      'false'
    ),
    // 期間
    period: `${local.mo00020StartDate.value}～${local.mo00020EndDate.value}`,
    // 指定日
    designationDate: local.mo00020StartDate.value,
    // 帳票セクション番号
    sheetNo: localOneway.Or27592.choIndex || '1',
    // 要介護度を印刷する
    yokaigoFlg: 'true',
    // 印刷する要介護度
    yokaigoKbn: local.mo00040TypeRequiredCareLevel.modelValue ?? '',
    // 事業者情報
    jigyoInfo: {
      houjinId: systemCommonsStore.getHoujinId!,
      shisetuId: systemCommonsStore.getShisetuId!,
      svJigyoId: systemCommonsStore.getSvJigyoId!,
    } as JigyoInfo,
    // 印刷設定
    printSet: {
      shiTeiKubun: local.mo00039TypePrintCategory,
      shiTeiDate: local.mo00020StartDate.value,
    } as PreventionPrintSet,
    // 印刷オプション
    printOption: {
      emptyFlg: getValueOrDefault(
        local.mo00018TypePrintTheForm.modelValue ? 'true' : 'false',
        isFieldDisabled.printTheForm.value,
        'false'
      ),
      keishoKnj: getValueOrDefault(local.textInput.value, isFieldDisabled.textInput.value, ''),
      keishoFlg: local.mo00018TypeChangeTitle.modelValue ? 'true' : 'false',
      jigyoshaKnj: localOneway.Or27592.svJigyoKnj,
      shokuName: orX0145Type.value?.value
        ? `${(orX0145Type.value.value as TantoCmnShokuin).shokuinKnj || ''}`.trim()
        : '',
      inkanPrintFlag: 'true',
    } as PreventionPrintOption,
    // 印刷対象履歴リスト
    printSubjectHistoryList: [
      {
        userId: user.userId,
        userName: `${user.name1Knj} ${user.name2Knj}`.trim(),
        startYmd: local.mo00020StartDate.value,
        endYmd: local.mo00020EndDate.value,
      },
    ] as PreventionPrintSubjectHistory[],
  }
}

/**
 * 通常モードPDF出力実行
 *
 * @param user - 利用者情報
 */
async function executeNormalPdfOutput(user: OrX0130TableType) {
  try {
    // API処理失敗時のシステムエラーダイアログを非表示にする
    systemCommonsStore.setSystemErrorDialogType('hide')

    // 通常モード用帳票データを作成
    const reportData = createNormalReportData(user)

    // 帳票出力
    await reportOutput(reportId.value, reportData, reportOutputType.DOWNLOAD)
  } finally {
    // API処理失敗時のシステムエラーダイアログの表示タイプをデフォルトに戻す
    systemCommonsStore.setSystemErrorDialogType('details')
  }
}

/**
 * 簡単モードPDF出力実行
 */
async function executeSimplePdfOutput() {
  try {
    // API処理失敗時のシステムエラーダイアログを非表示にする
    systemCommonsStore.setSystemErrorDialogType('hide')

    // 簡単モード用帳票データを作成
    const reportData = createSimpleReportData()

    // 帳票出力
    await reportOutput(simpleReportId.value, reportData, reportOutputType.DOWNLOAD)
  } finally {
    // API処理失敗時のシステムエラーダイアログの表示タイプをデフォルトに戻す
    systemCommonsStore.setSystemErrorDialogType('details')
  }
}

/**
 * R3/4改訂版PDF出力実行
 *
 * @param user - 利用者情報
 */
async function executeR34PdfOutput(user: OrX0130TableType) {
  try {
    // API処理失敗時のシステムエラーダイアログを非表示にする
    systemCommonsStore.setSystemErrorDialogType('hide')

    // R3/4改訂版用帳票データを作成
    const reportData = createR34ReportData(user)

    // 帳票出力
    await reportOutput(r34ReportId.value, reportData, reportOutputType.DOWNLOAD)
  } finally {
    // API処理失敗時のシステムエラーダイアログの表示タイプをデフォルトに戻す
    systemCommonsStore.setSystemErrorDialogType('details')
  }
}

/**
 * 介護予防モードPDF出力実行
 *
 * @param user - 利用者情報
 */
async function executePreventionPdfOutput(user: OrX0130TableType) {
  try {
    // API処理失敗時のシステムエラーダイアログを非表示にする
    systemCommonsStore.setSystemErrorDialogType('hide')

    // 介護予防モード用帳票データを作成
    const reportData = createPreventionReportData(user)

    // 帳票出力
    await reportOutput(preventionReportId.value, reportData, reportOutputType.DOWNLOAD)
  } finally {
    // API処理失敗時のシステムエラーダイアログの表示タイプをデフォルトに戻す
    systemCommonsStore.setSystemErrorDialogType('details')
  }
}
</script>
<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        no-gutters
        class="or27592_screen"
      >
        <c-v-col
          cols="12"
          sm="2"
          class="pa-0 pt-2 pl-2 or27592_border_right"
        >
          <base-mo-01334
            v-model="mo01334TypeReport"
            :oneway-model-value="mo01334OnewayReport"
            class="list-wrapper"
          >
            <!-- 帳票 -->
            <template #[`item.prtTitle`]="{ item }">
              <base-mo01337 :oneway-model-value="item.mo01337OnewayReport" />
            </template>
            <template #bottom />
          </base-mo-01334>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="5"
          class="pa-0 pt-2 or27592_border_right content_center"
        >
          <c-v-row
            v-if="!isSupportRecordDailyListMode"
            no-gutters
            class="or27592_row flex-center"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0 py-2 ml-2"
            >
              <base-mo00610
                :oneway-model-value="localOneway.Mo00610OneWaySealColumn"
                @click="openSealColumnDialog"
              >
              </base-mo00610>
            </c-v-col>
          </c-v-row>
          <c-v-divider
            v-if="!isSupportRecordDailyListMode"
            class="my-0"
          ></c-v-divider>
          <c-v-row
            no-gutters
            class="or27592_row flex-center"
          >
            <c-v-col
              cols="12"
              sm="2"
              class="pa-0 ml-2"
            >
              <base-mo01338 :oneway-model-value="localOneway.mo01338OneWayTitle"></base-mo01338>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="9"
              class="pa-0 pl-1"
            >
              <base-mo00045
                v-model="local.titleInput"
                :oneway-model-value="localOneway.mo00045OnewayTitleInput"
                :disabled="!isSupportRecordDailyListMode"
                @keyup.enter="checkTitleInput"
              />
            </c-v-col>
          </c-v-row>
          <c-v-divider class="my-1"></c-v-divider>
          <c-v-row
            no-gutters
            class="customCol or27592_row"
          >
            <c-v-col
              cols="12"
              sm="7"
              class="pa-0"
            >
              <!-- 印刷日付選択ラジオボタングループ -->
              <base-mo00039
                v-model="local.mo00039TypePrintCategory"
                :oneway-model-value="localOneway.mo00039OneWayPrintCategory"
              >
              </base-mo00039>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="5"
              class="pa-0 d-flex justify-end pr-2"
            >
              <!-- 印刷日付ラベル -->
              <base-mo00020
                v-if="local.mo00039TypePrintCategory === Or27592Const.PRINT_CATEGORY.DATE_PRINT"
                v-model="local.mo00020Type"
                :oneway-model-value="localOneway.mo00020OneWay"
                @mousedown="checkTitleInput"
              >
              </base-mo00020>
            </c-v-col>
          </c-v-row>
          <c-v-row
            v-if="!isSupportRecordDailyListMode"
            no-gutters
            class="customCol or27592_row"
          >
            <c-v-col
              cols="12"
              sm="6"
              class="pa-0"
            >
              <!-- 登録日区分ラジオボタングループ -->
              <base-mo00039
                v-model="local.mo00039TypePrintType"
                :oneway-model-value="localOneway.mo00039OneWayPrintType"
              >
              </base-mo00039>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="6"
              class="pa-0 d-flex justify-end"
            >
              <!-- 登録日区分ドロップダウン -->
              <base-mo00040
                v-if="local.mo00039TypePrintType === Or27592Const.PRINT_TYPE.REGISTRATION_DATE"
                v-model="local.mo00040TypeRegistrationDateCategory"
                :oneway-model-value="localOneway.mo00040OneWayRegistrationDateCategory"
                item-title="label"
                item-value="value"
              >
              </base-mo00040>
            </c-v-col>
          </c-v-row>
          <c-v-row
            v-if="!isSupportRecordDailyListMode"
            no-gutters
            class="printerOption customCol or27592_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pt-0 pb-0"
            >
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayPrinterOption"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
          <c-v-row
            v-if="!isSupportRecordDailyListMode"
            no-gutters
            class="customCol or27592_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0 d-flex"
            >
              <!-- 敬称を変更するチェックボックス -->
              <base-mo00018
                v-model="local.mo00018TypeChangeTitle"
                :oneway-model-value="localOneway.mo00018OneWayChangeTitle"
              >
              </base-mo00018>
              <!-- 敬称テキストボックス with parentheses -->
              <div class="input-with-parentheses">
                <span class="parenthesis-left">(</span>
                <base-mo00045
                  v-model="local.textInput"
                  :oneway-model-value="localOneway.mo00045OnewayTitleInput"
                  :disabled="!local.mo00018TypeChangeTitle.modelValue"
                />
                <span class="parenthesis-right">)</span>
              </div>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <!-- 記入用シートを印刷するチェックボックス -->
              <base-mo00018
                v-model="local.mo00018TypePrintTheForm"
                :oneway-model-value="localOneway.mo00018OneWayPrintTheForm"
              >
              </base-mo00018>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="6"
              class="pa-0"
            >
              <!-- 記録者を印刷するチェックボックス -->
              <base-mo00018
                v-model="local.mo00018TypePrintRecorder"
                :oneway-model-value="localOneway.mo00018OneWayPrintRecorder"
              >
              </base-mo00018>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="6"
              class="pa-0"
            >
              <!-- 期間を印刷するチェックボックス -->
              <base-mo00018
                v-model="local.mo00018TypeTimePeriod"
                :oneway-model-value="localOneway.mo00018OneWayTimePeriod"
              >
              </base-mo00018>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="6"
              class="pa-0"
            >
              <!-- 時間を印刷するチェックボックス -->
              <base-mo00018
                v-model="local.mo00018TypePrintTime"
                :oneway-model-value="localOneway.mo00018OneWayPrintTime"
              >
              </base-mo00018>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="6"
              class="pa-0"
            >
              <!-- 確認項目を印刷するチェックボックス -->
              <base-mo00018
                v-if="showPrintConfirmationItems"
                v-model="local.mo00018TypePrintConfirmationItems"
                :oneway-model-value="localOneway.mo00018OneWayPrintConfirmationItems"
              >
              </base-mo00018>
            </c-v-col>
            <c-v-col
              cols="6"
              sm="6"
              class="pa-0 d-flex align-center"
            >
              <!-- 記録者で絞り込むチェックボックス -->
              <base-mo00018
                v-model="local.mo00018TypeFilterByRecorder"
                :oneway-model-value="localOneway.mo00018OneWayFilterByRecorder"
              >
              </base-mo00018>
            </c-v-col>
            <c-v-col
              cols="6"
              sm="5"
              class="pa-0 ml-2 d-flex align-center"
            >
              <!-- 選択 -->
              <base-mo01338 :oneway-model-value="localOneway.mo01338OneWaySelected"> </base-mo01338>
              <!-- レコーダーによるボタンフィルター -->
              <base-mo00009
                :oneway-model-value="localOneway.mo00009OnewayFilterByRecorderType"
                :disabled="!local.mo00018TypeFilterByRecorder.modelValue"
                @click="selectFilterByRecorder"
              >
              </base-mo00009>
              <!-- 管理者 太郎 -->
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayAdministratorFilterByRecorder"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
          <c-v-row
            v-if="!isSupportRecordDailyListMode"
            no-gutters
            class="customCol or27592_row"
          >
            <c-v-col
              cols="12"
              sm="4"
              class="pa-0 ml-2 mt-4"
            >
              <!-- 要介護レベル選択 -->
              <base-mo01338 :oneway-model-value="localOneway.mo01338OneWayRequiredCareLevel">
              </base-mo01338>
              <base-mo00040
                v-model="local.mo00040TypeRequiredCareLevel"
                :oneway-model-value="localOneway.mo00040OneWayRequiredCareLevel"
                item-title="label"
                item-value="value"
                class="mt-2"
              >
              </base-mo00040>
            </c-v-col>
          </c-v-row>
          <c-v-row
            v-if="!isSupportRecordDailyListMode"
            no-gutters
            class="customCol or27592_row"
          >
            <c-v-col
              cols="12"
              sm="6"
              class="pa-0 ml-2"
            >
              <div class="d-flex align-center">
                <!-- 作成者ラベル -->
                <base-mo01338 :oneway-model-value="localOneway.mo01338OneWayAuthor"> </base-mo01338>
                <!-- 作成者ボタン -->
                <base-mo00009
                  :oneway-model-value="localOneway.mo00009OnewayAuthorType"
                  @click="selectAuthor"
                />
                <!-- 作成者情報 -->
                <base-mo01338 :oneway-model-value="localOneway.mo01338OneWayAdministratorAuthor">
                </base-mo01338>
              </div>
            </c-v-col>
          </c-v-row>
          <c-v-row
            v-if="!isSupportRecordDailyListMode"
            no-gutters
            class="customCol or27592_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <!-- 介護予防印刷チェックボックス - choIndex === '1' かつ svJigyoId === '50010' の場合のみ表示 -->
              <base-mo00018
                v-if="showPrintCarePrevention"
                v-model="local.mo00018TypePrintCarePrevention"
                :oneway-model-value="localOneway.mo00018OneWayPrintCarePrevention"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="5"
          class="pa-0"
        >
          <c-v-row
            v-if="!isSupportRecordDailyListMode"
            class="or27592_row"
            no-gutters
          >
            <c-v-col
              cols="12"
              sm="2"
              class="pa-0 d-flex align-center ml-2"
            >
              <!-- 利用者選択ラベル -->
              <base-mo01338 :oneway-model-value="localOneway.mo01338OneWayPrinterUserSelect">
              </base-mo01338>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="4"
              class="pa-0 d-flex align-center"
            >
              <!-- 利用者選択ラジオボタングループ -->
              <base-mo00039
                v-model="local.mo00039TypeUserSelectType"
                :oneway-model-value="localOneway.mo00039OneWayUserSelectType"
              >
              </base-mo00039>
            </c-v-col>
          </c-v-row>
          <c-v-row
            v-if="!isSupportRecordDailyListMode"
            no-gutters
            class="or27592_row"
          >
            <c-v-col
              cols="12"
              class="pa-0 d-flex align-center ml-2"
            >
              <!-- 期間ラベル -->
              <base-mo01338 :oneway-model-value="localOneway.mo01338OneWayPeriod"> </base-mo01338>
              <!-- 期間ボタン -->
              <base-mo00009 :oneway-model-value="localOneway.mo00009OnewayPeriodType" />
              <!-- 期間日付選択 -->
              <div class="d-flex align-center">
                <base-mo00020
                  v-model="local.mo00020StartDate"
                  :oneway-model-value="localOneway.mo00020OneWayStartDate"
                />
                <span class="mx-1">～</span>
                <base-mo00020
                  v-model="local.mo00020EndDate"
                  :oneway-model-value="localOneway.mo00020OneWayEndDate"
                />
              </div>
            </c-v-col>
          </c-v-row>
          <!-- 担当ケアマネ -->
          <c-v-row
            v-if="
              !isSupportRecordDailyListMode &&
              systemCommonsStore.getProcessDate !== Or27592Const.EMPTY_VALUES.EMPTY_STRING
            "
            no-gutters
            class="or27592_row"
          >
            <c-v-col
              cols="12"
              class="pa-0 ml-2"
            >
              <!-- 担当ケアマネプルダウン -->
              <g-custom-or-x-0145
                v-bind="orX0145"
                v-model="orX0145Type"
                :oneway-model-value="localOneway.orX0145Oneway"
                :disabled="isCareManagerDisabled"
              />
            </c-v-col>
          </c-v-row>
          <c-v-row
            v-if="!isSupportRecordDailyListMode"
            class="or27592_row"
            no-gutters
          >
            <c-v-col
              cols="12"
              sm="11"
              class="py-0 pr-0"
            >
              <g-custom-or-x-0130
                v-bind="orX0130"
                v-model="orX0130Type"
                :oneway-model-value="localOneway.orX0130Oneway"
              />
            </c-v-col>
          </c-v-row>
          <!-- 記録日付 -->
          <c-v-row
            v-if="isSupportRecordDailyListMode"
            class="or27592_row mt-4"
            no-gutters
          >
            <c-v-col
              cols="12"
              class="pa-0 pl-4"
            >
              <!-- 記録日付ラベル -->
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayRecordingDate"
                class="ml-8"
              >
              </base-mo01338>
              <!-- 記録日付選択 -->
              <base-mo00020
                v-model="local.mo00020RecordingDate"
                :oneway-model-value="localOneway.mo00020OneWayRecordingDate"
              />
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>

    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.Mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close()"
        ></base-mo00611>
        <!-- 印刷ボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          @click="pdfDownload()"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <g-base-or21813 v-bind="or21813"> </g-base-or21813>
  <g-base-or21815 v-bind="or21815"> </g-base-or21815>
  <g-base-or21814 v-bind="or21814" />
  <g-custom-or-26257
    v-if="showDialogOr26257"
    v-bind="or26257"
    v-model="or26257Type"
    :oneway-model-value="or26257Data"
  />
  <g-custom-or-10016
    v-if="showDialogOr10016"
    v-bind="or10016"
    :oneway-model-value="localOneway.or10016Oneway"
  />
</template>

<style scoped lang="scss">
@use '@/styles/base.scss';
@use '@/styles/cmn/dialog-base.scss';

.or27592_screen {
  margin: -8px !important;
}

.or27592_border_right {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.or27592_row {
  margin: 0px !important;
  margin-top: 8px !important;
}

.content_center {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  padding: 0px !important;

  .label_left {
    padding-right: 0px;
  }

  .label_right {
    padding-left: 0px;
    padding-right: 0px;
  }

  .printerOption {
    background-color: rgba(var(--v-theme-black-100));
  }

  :deep(.label-area-style) {
    display: none;
  }

  .customCol {
    margin-left: 0px;
    margin-right: 0px;
  }
}

.flex-center {
  display: flex;
  align-items: center;
}

.user_class {
  margin-left: -12px;
}

.care-manager-disabled {
  opacity: 0.6;
  pointer-events: none;
}

.d-flex.align-center {
  margin-right: 8px;
  min-height: 48px;

  :deep(.v-btn) {
    align-self: center;
  }

  :deep(.v-input--selection-controls) {
    align-self: center;
  }

  :deep(.v-field) {
    align-self: center;
  }
}

/* 登録日区分ドロップダウンのスタイル */
:deep(.registration-date-dropdown) {
  .v-field {
    min-width: 100%;
  }

  .v-select__menu-inner {
    max-width: none !important;
    min-width: 250px;
    white-space: nowrap;
  }

  .v-list-item {
    padding-inline: 12px;
    min-height: 40px;
  }

  .v-list-item__title {
    white-space: nowrap;
    overflow: visible;
    font-size: 14px;
  }

  .v-overlay__content {
    max-width: none !important;
    position: absolute;
    z-index: 2001;
  }

  .v-select__selection-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.input-with-parentheses {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: rgba(var(--v-theme-on-surface));

  .parenthesis-left {
    margin-right: 8px;
    line-height: 1;
    display: flex;
    align-items: center;
  }

  .parenthesis-right {
    margin-left: 2px;
    line-height: 1;
    display: flex;
    align-items: center;
  }

  :deep(.v-field) {
    flex: 0 0 auto;
  }
}
</style>
