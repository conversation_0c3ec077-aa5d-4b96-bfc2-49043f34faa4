import { HttpResponse } from 'msw'
import defaultData from './data/default.json'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { PlanMonitoringPrintSettingsHistorySelectInEntity } from '~/repositories/cmn/entities/PlanMonitoringPrintSettingsUpdateEntity.ts'
/**
 * GUI01232_印刷設定
 *
 * @description
 * GUI01232_印刷設定対象一覧情報データを返却する。
 * dataName："planMonitoringPrintSettingsHistorySelect"
 */
export function handler(inEntity: PlanMonitoringPrintSettingsHistorySelectInEntity) {
  const responceJson: BaseResponseBody = {
    statusCode: 'success',
    data: {
      ...defaultData,
    },
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
