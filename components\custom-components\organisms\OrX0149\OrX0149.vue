<script setup lang="ts">
/**
 * OrX0149:有機体:印刷設定モーダル
 * GUI04470_［印刷設定］画面
 *
 * @description
 * ［印刷設定］画面
 *
 * <AUTHOR>
 */
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import {
  useScreenOneWayBind,
  useSetupChildProps,
  useScreenStore,
  useScreenTwoWayBind,
} from '#imports'
import type {
  Mo01334Items,
  Mo01334OnewayType,
  Mo01334Type,
} from '~/types/business/components/Mo01334Type'
import type { CustomClass } from '~/types/CustomClassType'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00040Type } from '~/types/business/components/Mo00040Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import type { Mo00039OnewayType } from '@/types/business/components/Mo00039Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00045Type, Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { OrX0149OnewayType, OrX0149Type } from '~/types/cmn/business/components/OrX0149Type'
import type { PrintSettingsInfoComUpdateInEntity } from '~/repositories/cmn/entities/printSettingsInfoComUpdate'
import type {
  OrX0149StateType,
  OrX0149TwoWayData,
  choPrtList,
} from '~/components/custom-components/organisms/OrX0149/OrX0149.type'
import type {
  LedgerInitializeDataComSelectOutEntity,
  LedgerInitializeDataComSelectInEntity,
} from '~/repositories/cmn/entities/ledgerInitializeDataComSelect'
import type {
  printSettingsInitialInfoSelectGUI04470InEntity,
  printSettingsInitialInfoSelectGUI04470OutEntity,
} from '~/repositories/cmn/entities/PrintSettingsInitialInfoSelectGUI04470Entity'

import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { OrX0149Const } from '~/components/custom-components/organisms/OrX0149/OrX0149.constants'
import { Or26315Const } from '~/components/custom-components/organisms/Or26315/Or26315.constants'

const { t } = useI18n()

/************************************************
 * Props
 ************************************************/
interface Props {
  modelValue: OrX0149Type
  onewayModelValue: OrX0149OnewayType
  uniqueCpId: string
  parentCpId: string
}
const props = defineProps<Props>()

// 引継情報を取得する
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()

const or26315 = ref({ uniqueCpId: '' })
const or21813 = ref({ uniqueCpId: '' })
const or21815 = ref({ uniqueCpId: '' })

// プロファイル
const choPro = ref('')

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or26315Const.CP_ID(0)]: or26315.value,
  [Or21813Const.CP_ID(0)]: or21813.value,
  [Or21815Const.CP_ID(0)]: or21815.value,
})

// 帳票イニシャライズデータを取得する
const reportInitData = ref([
  {
    // 氏名伏字印刷
    prtName: '',
    // 文書番号印刷
    prtBng: '',
    // 個人情報印刷
    prtKojin: '',
  },
])

/**************************************************
 * 変数定義
 **************************************************/
const isEdit = computed(() => {
  return useScreenStore().getCpNavControl(props.uniqueCpId)
})

// ローカル双方向bind
const local = reactive({
  OrX0149: {
    ...props.modelValue,
  } as OrX0149Type,
  mo00040: { modelValue: '' } as Mo00040Type,
  mo00039Type: '',
  mo00020Type: {
    value: '',
  } as Mo00020Type,
  statementTitle: {
    value: '',
  } as Mo00045Type,
})

const localOneway = reactive({
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.close-btn'),
  } as Mo00611OnewayType,
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
  } as Mo00609OnewayType,
  mo00039OneWay: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  mo00020OneWay: {
    showItemLabel: true,
    width: '132px',
  } as Mo00020OnewayType,
  OrX0149: {
    ...props.onewayModelValue,
  },
  mo01338OneWayTitle: {
    value: t('label.print-settings-title'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00009OnewayType: {
    icon: true,
    btnIcon: 'edit_square',
    prependIcon: 'edit_square',
  } as Mo00009OnewayType,
  mo00020KijunbiOneWay: {
    showItemLabel: false,
    showSelectArrow: true,
    width: '132px',
    mo00009OnewayBack: {},
    mo00009OnewayForward: {},
  } as Mo00020OnewayType,
  // 「印刷設定」ダイアログ
  mo00024Oneway: {
    width: 'auto',
    height: 'auto',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      toolbarTitle: t('label.print-set'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
      scrollable: false,
    },
  },
  OrX0149Oneway: {
    ...props.onewayModelValue,
  } as OrX0149OnewayType,
  // チェックボックス
  mo00018OnewayType: {
    showItemLabel: false,
    checkboxLabel: '',
    isVerticalLabel: true,
    hideDetails: true,
    outerDivClass: '',
  } as Mo00018OnewayType,
  statementTitleAbbreviation: {
    itemLabel: '',
    showItemLabel: true,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
    width: '420',
    disabled: false,
  } as Mo00045OnewayType,
})

// ダイアログ設置
const mo00024 = ref<Mo00024Type>({
  isOpen: OrX0149Const.DEFAULT.IS_OPEN,
})

/**************************************************
 * Pinia
 **************************************************/

const { setState } = useScreenOneWayBind<OrX0149StateType>({
  cpId: OrX0149Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? OrX0149Const.DEFAULT.IS_OPEN
    },
  },
})

/**
 * 出力帳票名一覧
 */
const mo01334OnewayReport = ref<Mo01334OnewayType>({
  headers: [
    {
      title: t('label.report'),
      key: 'prtTitle',
      sortable: false,
      minWidth: '180',
    },
  ],
  items: [],
  height: 400,
})
/**
 * 出力帳票名一覧
 */
const mo01334TypeReport = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

/**
 * 変更データを取得する
 *
 * @returns 出力帳票印刷情報リスト
 */
async function getDataTable() {
  const choPrtList = mo01334OnewayReport.value.items.map(({ id, mo01337OnewayReport, ...rest }) => {
    if (id === mo01334TypeReport.value.value) {
      return {
        ...rest,
        prtTitle: local.statementTitle.value,
        prndate: local.mo00039Type,
      }
    }
    return {
      ...rest,
    }
  }) as choPrtList[]

  refValue.value = { choPrtList: choPrtList }

  await nextTick()

  return choPrtList
}

const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})
const showDialogOr21815 = computed(() => {
  // Or21815のダイアログ開閉状態
  return Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 警告ダイアログの開閉
 *
 * @returns ダイアログの選択結果（ok）
 */
async function showOr21815MsgOneBtn() {
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.caution'),
      // ダイアログテキスト
      iconName: 'warning',
      dialogText: t('message.w-cmn-20845'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815.value.uniqueCpId)

        let result = 'ok'

        if (event?.firstBtnClickFlg) {
          result = 'ok'
        }

        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: or21815.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * errorダイアログの開閉
 *
 * @returns ダイアログの選択結果（ok）
 */

async function showOr21813MsgOneBtn() {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      dialogTitle: t('label.error'),
      dialogText: t('message.e-cmn-40172'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813.value.uniqueCpId)

        let result = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }

        Or21813Logic.event.set({
          uniqueCpId: or21813.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

const { refValue } = useScreenTwoWayBind<OrX0149TwoWayData>({
  cpId: OrX0149Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
onMounted(async () => {
  //システム日付
  local.mo00020Type.value = systemCommonsStore.getSystemDate!
  await getPrintSettingList()
  await initCodes()
})

/**
 * 汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [{ mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY }]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  // 回数区分
  //日付印刷区分
  localOneway.mo00039OneWay.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )
}

/**
 * 印刷設定画面初期情報を取得する
 */
async function getPrintSettingList() {
  const inputData: printSettingsInitialInfoSelectGUI04470InEntity = {
    sysCd: localOneway.OrX0149.sysCd,
    sysRyaku: localOneway.OrX0149.sysRyaku,
    kinounameKnj: OrX0149Const.DEFAULT.KINOU_NAMEKNJ,
    houjinId: localOneway.OrX0149.houjinId,
    shisetuId: localOneway.OrX0149.shisetuId,
    svJigyoId: localOneway.OrX0149.svJigyoId,
    shokuId: localOneway.OrX0149.shokuId,
    sectionName: localOneway.OrX0149.sectionName,
    choIndex: localOneway.OrX0149.choIndex,
    kojinhogoFlg: '0',
    sectionAddNo: '0',
  }

  // バックエンドAPIから初期情報取得
  const ret: printSettingsInitialInfoSelectGUI04470OutEntity = await ScreenRepository.select(
    'printSettingsInitialInfoSelectGUI04470',
    inputData
  )

  const mo01334OnewayList: Mo01334Items[] = []
  for (const item of ret.data.choPrtList) {
    if (item) {
      mo01334OnewayList.push({
        id: item.prtNo,
        mo01337OnewayReport: {
          value: item.prtTitle,
          unit: '',
        } as Mo01337OnewayType,
        ...item,
      } as Mo01334Items)
    }
  }
  mo01334OnewayReport.value.items = mo01334OnewayList

  if (ret.data.choPrtList.length > 0) {
    mo01334TypeReport.value.value = localOneway.OrX0149.choIndex
  }

  refValue.value = { choPrtList: ret.data.choPrtList }

  useScreenStore().setCpTwoWay({
    cpId: OrX0149Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: refValue.value,
    isInit: true,
  })
  await getReportInfoDataList()
}

/**
 * 帳票イニシャライズデータを取得する。
 */
async function getReportInfoDataList() {
  const inputData: LedgerInitializeDataComSelectInEntity = {
    sysCd: localOneway.OrX0149.sysCd,
    kinounameKnj: OrX0149Const.DEFAULT.KINOU_NAMEKNJ,
    shokuId: localOneway.OrX0149.shokuId,
    sectionKnj: choPro.value,
    kojinhogoFlg: OrX0149Const.DEFAULT.ZERO,
    sectionAddNo: OrX0149Const.DEFAULT.ZERO,
  }
  // バックエンドAPIから初期情報取得
  const ret: LedgerInitializeDataComSelectOutEntity = await ScreenRepository.select(
    'ledgerInitializeDataComSelect',
    inputData
  )
  reportInitData.value = ret.data.iniDataObject
}

/**
 * 出力帳票一覧明細に選択されている行
 */
const statementTitleChange = async () => {
  if (!local.statementTitle.value) {
    await showOr21815MsgOneBtn()
    local.statementTitle.value = mo01334OnewayReport.value.items[0].prtTitle as string
  }
}

/**
 * 帳票選択切替
 */
watch(
  () => mo01334TypeReport.value.value,
  async (newValue, oldValue) => {
    if (oldValue) {
      for (const item of mo01334OnewayReport.value.items) {
        if (item) {
          if (oldValue === item.id) {
            // 画面.出力帳票一覧明細に切替前選択される行.帳票タイトル = 画面.帳票タイトル
            if (local.statementTitle.value) item.prtTitle = local.statementTitle.value
            // 画面.出力帳票一覧明細に切替前選択される行.日付表示有無 = 画面.日付印刷区分
            item.prndate = local.mo00039Type
          }
        }
      }
    }
    for (const item of mo01334OnewayReport.value.items) {
      if (item) {
        if (newValue === item.id) {
          // 画面.帳票タイトル = 画面.出力帳票一覧明細に選択される行.帳票タイトル
          local.statementTitle.value = item?.prtTitle as string
          // 画面.出力帳票一覧明細に切替前選択される行.日付表示有無 = 画面.日付印刷区分
          local.mo00039Type = item?.prndate as string

          choPro.value = item?.choPro as string
        }
      }
    }
    if (!newValue) {
      await showOr21813MsgOneBtn()
    }
  }
)
/**
 * 「閉じるボタン」押下
 */
async function close() {
  const choPrtList = await getDataTable()
  if (isEdit.value) await savePrintSettingInfo(choPrtList)
  setState({ isOpen: false })
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.emitType,
  (newValue) => {
    if (newValue === 'closeBtnClick') {
      // await onClickCloseBtn()
      setState({ isOpen: false })
    }
    mo00024.value.emitType = 'blank'
  }
)

/**
 * 「印刷」ボタン押下
 */
async function print() {
  //印刷設定情報保存
  // if (!local.mo01334.values.length) {
  //   return await showOr21815MsgOneBtn(false)
  // }
  const choPrtList = await getDataTable()

  if (isEdit.value) await savePrintSettingInfo(choPrtList)
  setState({ isOpen: false })
}
/**
 * 印刷設定情報を保存する
 *
 * @param choPrtList - 出力帳票印刷情報リスト
 */
async function savePrintSettingInfo(choPrtList: choPrtList[]) {
  const inputData: PrintSettingsInfoComUpdateInEntity = {
    sysCd: localOneway.OrX0149.sysCd,
    sysRyaku: OrX0149Const.DEFAULT.SYS_RYAKU,
    kinounameKnj: OrX0149Const.DEFAULT.KINOU_NAMEKNJ,
    houjinId: localOneway.OrX0149.houjinId,
    shisetuId: localOneway.OrX0149.shisetuId,
    svJigyoId: localOneway.OrX0149.svJigyoId,
    shokuId: localOneway.OrX0149.shokuId,
    kojinhogoFlg: OrX0149Const.DEFAULT.ZERO,
    sectionAddNo: OrX0149Const.DEFAULT.ZERO,
    iniDataObject: { ...reportInitData.value },
    choPrtList: choPrtList,
    choPro: '',
    sectionName: localOneway.OrX0149.sectionName,
  }

  // バックエンドAPIからsave
  await ScreenRepository.update('printSettingsInfoComUpdate', inputData)
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row class="OrX0149_screen">
        <c-v-col class="pa-0 pt-2 px-2 OrX0149_border_right">
          <!-- 帳票 -->
          <base-mo-01334
            v-model="mo01334TypeReport"
            :oneway-model-value="mo01334OnewayReport"
            class="list-wrapper table-report"
          >
            <!-- 帳票ラベル  -->
            <template #[`item.prtTitle`]="{ item }">
              <base-mo01337 :oneway-model-value="item.mo01337OnewayReport" />
            </template>
            <template #bottom />
          </base-mo-01334>
        </c-v-col>
        <c-v-col class="pa-0 pt-2 content_center">
          <c-v-row
            no-gutter
            class="OrX0149_row flex-center"
          >
            <c-v-col
              cols="12"
              sm="3"
              class="pa-2"
            >
              <!-- タイトル  -->
              <base-mo01338 :oneway-model-value="localOneway.mo01338OneWayTitle"></base-mo01338>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="OrX0149_row title-input"
          >
            <base-Mo00045
              v-model="local.statementTitle"
              :oneway-model-value="localOneway.statementTitleAbbreviation"
              @blur="statementTitleChange"
            />
          </c-v-row>
          <c-v-divider class="my-0"></c-v-divider>
          <c-v-row
            no-gutter
            class="customCol OrX0149_row"
            style="height: 133px"
          >
            <c-v-col
              cols="12"
              sm="7"
              class="pa-2"
            >
              <!-- 印刷日付選択ラジオボタングループ -->
              <base-mo00039
                v-model="local.mo00039Type"
                :oneway-model-value="localOneway.mo00039OneWay"
              >
              </base-mo00039>
            </c-v-col>
            <c-v-col
              id="test"
              cols="12"
              sm="5"
              class="pa-2"
            >
              <!-- 印刷日付ラベル -->
              <base-mo00020
                v-if="local.mo00039Type == '2'"
                v-model="local.mo00020Type"
                :oneway-model-value="localOneway.mo00020OneWay"
              >
              </base-mo00020>
            </c-v-col>
          </c-v-row>
          <!-- <c-v-divider class="my-0"></c-v-divider> -->
        </c-v-col>
      </c-v-row>
    </template>

    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close()"
        ></base-mo00611>
        <!-- 印刷ボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          @click="print()"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  >
  </g-base-or21813>
  <g-base-or21815
    v-if="showDialogOr21815"
    v-bind="or21815"
  >
  </g-base-or21815>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';
.OrX0149_screen {
  margin: -8px !important;
}

.OrX0149_border_right {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));

  :deep(.table-report) {
    width: 180px;
  }
}

.OrX0149_row {
  margin: 0px !important;
}

:deep(.title-input) {
  padding-bottom: 8px;

  .v-col-auto {
    display: none;
  }

  .v-input__control {
    margin-left: 8px;
  }
}

.content_center {
  padding: 0px !important;

  .label_left {
    padding-right: 0px;
  }

  .label_right {
    padding-left: 0px;
    padding-right: 0px;
  }

  .printerOption {
    background-color: rgb(var(--v-theme-black-100));
  }

  :deep(.label-area-style) {
    display: none;
  }

  .customCol {
    margin-left: 0px;
    margin-right: 0px;
  }
}

.my-0 {
  width: calc(100% - 8px);
}

.flex-center {
  display: flex;
  align-items: center;
}

.customCol {
  margin-left: 0px;
  margin-right: 0px;
}
</style>
