<script setup lang="ts">
/**
 * Or06618:有機体:(日課計画マスタ)コンテンツエリアタブ
 * GUI01056_日課計画マスタ
 *
 * @description
 * 日課計画マスタ画面
 *
 * <AUTHOR>
 */

import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or01086Const } from '../Or01086/Or01086.constants'
import { Or01080Const } from '../Or01080/Or01080.constants'
import { Or01083Const } from '../Or01083/Or01083.constants'
import { Or01084Const } from '../Or01084/Or01084.constants'
import { Or01085Const } from '../Or01085/Or01085.constants'
import { Or01082Const } from '../Or01082/Or01082.constants'
import { Or01086Logic } from '../Or01086/Or01086.logic'
import { Or01080Logic } from '../Or01080/Or01080.logic'
import  { Or01083Logic } from '../Or01083/Or01083.logic'
import  { Or01084Logic } from '../Or01084/Or01084.logic'
import  { Or01085Logic } from '../Or01085/Or01085.logic'
import { Or01082Logic } from '../Or01082/Or01082.logic'
import { Or06618Const } from './Or06618.constants'
import type { Or06618Type } from './Or06618.type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { useColorUtils, useScreenStore, useScreenTwoWayBind, useSetupChildProps } from '#build/imports'
import { useScreenUtils } from '~/utils/useScreenUtils'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CustomClass } from '~/types/CustomClassType'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'

import type { Or01082Type } from '~/types/cmn/business/components/Or01082Type'
import type {
  DailyScheduleMasterInitSelectInEntity,
  DailyScheduleMasterInitSelectOutEntity,
} from '~/repositories/cmn/entities/DailyScheduleMasterInitSelectEntity'
import type {
  DailyScheduleMasterBunrui3,
  DailyScheduleMasterInsertInEntity,
} from '~/repositories/cmn/entities/DailyScheduleMasterInsertEntity'
import type { Or01080OneWayType, Or01080Type } from '~/types/cmn/business/components/Or01080Type'
import type { Or01083OneWayType, Or01083Type } from '~/types/cmn/business/components/Or01083Type'
import type { Or01084OneWayType, Or01084Type } from '~/types/cmn/business/components/Or01084Type'
import type { Or01085Type } from '~/types/cmn/business/components/Or01085Type'
import type { Or01086OneWayType, Or01086Type } from '~/types/cmn/business/components/Or01086Type'
import type { Or06618OneWayType } from '~/types/cmn/business/components/Or06618Type'
import type { Mo00040Type, Mo00040OnewayType } from '~/types/business/components/Mo00040Type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'

const { t } = useI18n()
const { convertDecimalToHex ,convertHexToDecimal} = useColorUtils()
const { setChildCpBinds } = useScreenUtils()

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or06618OneWayType
  uniqueCpId: string
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/

const or01080 = ref({ uniqueCpId: '' })
const or01082 = ref({ uniqueCpId: '' })
const or01083 = ref({ uniqueCpId: '' })
const or01084 = ref({ uniqueCpId: '' })
const or01085 = ref({ uniqueCpId: '' })
const or01086 = ref({ uniqueCpId: '' })
const or21814_1 = ref({ uniqueCpId: '' })
// ローカルTwoway
const local = reactive({
  or06618: {} as Or06618Type,
  or01080: { timeFlg: '' } as Or01080Type,
  or01082: { color: '' } as Or01082Type,
  or01083: { textSizeFlg: '' } as Or01083Type,
  or01084: { letterPositionFlg: '' } as Or01084Type,
  or01085: { color: '' } as Or01085Type,
  or01086: { initialImport: '' } as Or01086Type,
  mo00040 : {} as Mo00040Type
})
// ローカルOneway
const localOneway = reactive({
  // 本画面
  or06618Oneway: {
    ...props.onewayModelValue,
  },
  // 予防計画書からの初期取込入力
  or01086Oneway: {} as Or01086OneWayType,
  // 時間
  or01080Oneway: {} as Or01080OneWayType,
  // 文字サイズ選択
  or01083Oneway: {} as Or01083OneWayType,
  // 文字位置入力
  or01084Oneway: {} as Or01084OneWayType,
  mo01338: {
    value: t('label.initial-value'),
    customClass: new CustomClass({ outerClass: 'left-text ', itemClass: 'label-center' }),
  } as Mo01338OnewayType,
  mo01338Oneway: {
    value: t('label.from-prevention-care-plan-import'),
  } as Mo01338OnewayType,
  mo01338Twoway: {
    value: t('label.time'),
  } as Mo01338OnewayType,
  mo01338Threeway: {
    value: t('label.font-size'),
  } as Mo01338OnewayType,
  mo01338Fourway: {
    value: t('label.text-position'),
  } as Mo01338OnewayType,
  mo01338Fiveway: {
    value: t('label.color'),
  } as Mo01338OnewayType,
  mo01338Sixway: {
    value: t('label.background-color'),
  } as Mo01338OnewayType,
  mo00040Oneway: {
    showItemLabel: false,
    itemLabel: t('label.office-selection'),
    itemTitle: 'jigyoKnj',
    itemValue: 'jigyoId',
    width: '180px',
    items: [],
  } as Mo00040OnewayType,
})
/**************************************************
 * Pinia
 **************************************************/
useScreenTwoWayBind<Or06618Type>({
  cpId: Or06618Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or01080Const.CP_ID(0)]: or01080.value,
  [Or01082Const.CP_ID(0)]: or01082.value,
  [Or01083Const.CP_ID(0)]: or01083.value,
  [Or01084Const.CP_ID(0)]: or01084.value,
  [Or01085Const.CP_ID(0)]: or01085.value,
  [Or01086Const.CP_ID(0)]: or01086.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
})

/**************************************************
 * ウォッチャー
 **************************************************/
onMounted(async () => {
  if (localOneway.or06618Oneway.dailyScheduleMasterInData.svJigyoId === '50010') {
    localOneway.mo01338Oneway.value = t('label.from-prevention-care-plan-import')
  } else {
    localOneway.mo01338Oneway.value = t('label.care-plan2-init-value-import')
  }
  await initCodes()
  await fetchInitData()
  // データ変更確認ダイアログを初期化(i.cmn.10430)
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })
})
/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 計画書（２）からの初期取込
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_IMPORT_CARE_PLAN_SELECT_ITEMS },
    // 時間選択
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DAILY_MAST_TIME },
    // 文字サイズ選択
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_PRINT_LETTER_SIZE },
    // 文字位置
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DAILY_LETTER_POSITION },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 計画書（２）からの初期取込
  localOneway.or01086Oneway.radioItemsList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_IMPORT_CARE_PLAN_SELECT_ITEMS
  )
  // 時間
  localOneway.or01080Oneway.radioItemsList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_DAILY_MAST_TIME
  )
  // 文字サイズ選択
  localOneway.or01083Oneway.radioItemsList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_PRINT_LETTER_SIZE
  )
  // 文字位置
  localOneway.or01084Oneway.radioItemsList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_DAILY_LETTER_POSITION
  )
}

/**
 * 日課計画マスタ初期情報取得
 *
 */
async function fetchInitData() {
    // 日課計画マスタ初期情報取得(IN)
  const inputData: DailyScheduleMasterInitSelectInEntity = {
    shisetuId: localOneway.or06618Oneway.dailyScheduleMasterInData.shisetuId,
    svJigyoId: localOneway.or06618Oneway.dailyScheduleMasterInData.svJigyoId,
    svJigyoIdList: localOneway.or06618Oneway.dailyScheduleMasterInData.svJigyoIdList,
  }

  // 日課計画マスタ初期情報取得
  const ret: DailyScheduleMasterInitSelectOutEntity = await ScreenRepository.select(
    'dailyScheduleMasterInitSelect',
    inputData
  )
  // 戻り値はテーブルデータとして処理されます
  // 初期取込
  local.or01086.initialImport = ret.data.initialUptake
  // 時間
  local.or01080.timeFlg = ret.data.time
  // 文字サイズ
  local.or01083.textSizeFlg = ret.data.charSize
  // 文字位置
  local.or01084.letterPositionFlg = ret.data.charPosition
  // 文字色
  local.or01085.color = convertDecimalToHex(Number(ret.data.charColor))
  // 背景色
  local.or01082.color = convertDecimalToHex(Number(ret.data.backgroundColor))

  // 初期取込
  local.or06618.initialUptake = ret.data.initialUptake
  local.or06618.initialUptakeBunrui3 = ret.data.initialUptakeBunrui3
  local.or06618.initialUptakeModifiedCnt = ret.data.initialUptakeModifiedCnt
  // 時間
  local.or06618.time = ret.data.time
  local.or06618.timeBunrui3 = ret.data.timeBunrui3
  local.or06618.timeModifiedCnt = ret.data.timeModifiedCnt
  // 文字サイズ
  local.or06618.charSize = ret.data.charSize
  local.or06618.charSizeBunrui3 = ret.data.charSizeBunrui3
  local.or06618.charSizeModifiedCnt = ret.data.charSizeModifiedCnt
  // 文字位置
  local.or06618.charPosition = ret.data.charPosition
  local.or06618.charPositionBunrui3 = ret.data.charPositionBunrui3
  local.or06618.charPositionModifiedCnt = ret.data.charPositionModifiedCnt
  // 文字色
  local.or06618.charColor = ret.data.charColor
  local.or06618.charColorBunrui3 = ret.data.charColorBunrui3
  local.or06618.charColorModifiedCnt = ret.data.charColorModifiedCnt
  // 背景色
  local.or06618.backgroundColor = ret.data.backgroundColor
  local.or06618.backgroundColorBunrui3 = ret.data.backgroundColorBunrui3
  local.or06618.backgroundColorModifiedCnt = ret.data.backgroundColorModifiedCnt
  // 事業所
  if (ret.data.svJigyoListInfo) {
    localOneway.mo00040Oneway.items = ret.data.svJigyoListInfo
    local.mo00040.modelValue = localOneway.or06618Oneway.dailyScheduleMasterInData.svJigyoId
  }

  setChildCpBinds(props.uniqueCpId, {
    [Or01080Const.CP_ID(0)]: {
      twoWayValue: {
        timeFlg: local.or01080.timeFlg,
      },
    },
    [Or01082Const.CP_ID(0)]: {
      twoWayValue: {
        color: local.or01082.color,
      },
    },
    [Or01083Const.CP_ID(0)]: {
      twoWayValue: {
        textSizeFlg: local.or01083.textSizeFlg,
      },
    },
    [Or01084Const.CP_ID(0)]: {
      twoWayValue: {
        letterPositionFlg: local.or01084.letterPositionFlg,
      },
    },
    [Or01085Const.CP_ID(0)]: {
      twoWayValue: {
        color: local.or01085.color,
      },
    },
    [Or01086Const.CP_ID(0)]: {
      twoWayValue: {
        initialImport: local.or01086.initialImport,
      },
    },
  })
}

/**************************************************
 * 関数
 **************************************************/
/**
 * 保存
 */
async function insert() {
  const bunrui3SetList = [] as DailyScheduleMasterBunrui3[]
  // 初期取込
  if (
    !local.or06618.initialUptakeBunrui3 ||
    local.or06618.initialUptakeBunrui3 === '' ||
    local.or06618.initialUptake !== Or01086Logic.data.get(or01086.value.uniqueCpId)?.initialImport
  ) {
    const carePlanOneBunrui3 = {
      bunrui3Id: Or06618Const.DEFAULT.INITIAL_UPTAKE,
      bunrui3Value: Or01086Logic.data.get(or01086.value.uniqueCpId)?.initialImport,
      modifiedCnt: local.or06618.initialUptakeModifiedCnt,
    } as DailyScheduleMasterBunrui3
    bunrui3SetList.push(carePlanOneBunrui3)
  }
  // 時間
  if (
    !local.or06618.timeBunrui3 ||
    local.or06618.timeBunrui3 === '' ||
    local.or06618.time !== Or01080Logic.data.get(or01080.value.uniqueCpId)?.timeFlg
  ) {
    const carePlanOneBunrui3 = {
      bunrui3Id: Or06618Const.DEFAULT.TIME,
      bunrui3Value: Or01080Logic.data.get(or01080.value.uniqueCpId)?.timeFlg,
      modifiedCnt: local.or06618.timeModifiedCnt,
    } as DailyScheduleMasterBunrui3
    bunrui3SetList.push(carePlanOneBunrui3)
  }
  // 文字サイズ
  if (
    !local.or06618.charSizeBunrui3 ||
    local.or06618.charSizeBunrui3 === '' ||
    local.or06618.charSize !== Or01083Logic.data.get(or01083.value.uniqueCpId)?.textSizeFlg
  ) {
    const carePlanOneBunrui3 = {
      bunrui3Id: Or06618Const.DEFAULT.CHAR_SIZE,
      bunrui3Value: Or01083Logic.data.get(or01083.value.uniqueCpId)?.textSizeFlg,
      modifiedCnt: local.or06618.charSizeModifiedCnt,
    } as DailyScheduleMasterBunrui3
    bunrui3SetList.push(carePlanOneBunrui3)
  }
  // 文字位置
  if (
    !local.or06618.charPositionBunrui3 ||
    local.or06618.charPositionBunrui3 === '' ||
    local.or06618.charPosition !== Or01084Logic.data.get(or01084.value.uniqueCpId)?.letterPositionFlg
  ) {
    const carePlanOneBunrui3 = {
      bunrui3Id: Or06618Const.DEFAULT.CHAR_POSITION,
      bunrui3Value: Or01084Logic.data.get(or01084.value.uniqueCpId)?.letterPositionFlg,
      modifiedCnt: local.or06618.charPositionModifiedCnt,
    } as DailyScheduleMasterBunrui3
    bunrui3SetList.push(carePlanOneBunrui3)
  }
  // 文字色
  const charColor = Or01085Logic.data.get(or01085.value.uniqueCpId)?.color ?? ''
  if (
    !local.or06618.charColorBunrui3 ||
    local.or06618.charColorBunrui3 === '' ||
    local.or06618.charColor !== convertHexToDecimal(charColor)
  ) {
    const carePlanOneBunrui3 = {
      bunrui3Id: Or06618Const.DEFAULT.CHAR_COLOR,
      bunrui3Value: convertHexToDecimal(charColor),
      modifiedCnt: local.or06618.charColorModifiedCnt,
    } as DailyScheduleMasterBunrui3
    bunrui3SetList.push(carePlanOneBunrui3)
  }
  // 背景色
  const backgroundColor = Or01082Logic.data.get(or01082.value.uniqueCpId)?.color ?? ''
  if (
    !local.or06618.backgroundColorBunrui3 ||
    local.or06618.backgroundColorBunrui3 === '' ||
    local.or06618.backgroundColor !== convertHexToDecimal(backgroundColor)
  ) {
    const carePlanOneBunrui3 = {
      bunrui3Id: Or06618Const.DEFAULT.BACK_GROUND_COLOR,
      bunrui3Value: convertHexToDecimal(backgroundColor),
      modifiedCnt: local.or06618.backgroundColorModifiedCnt,
    } as DailyScheduleMasterBunrui3
    bunrui3SetList.push(carePlanOneBunrui3)
  }
  if (bunrui3SetList.length <= 0) {
    return {
      error: false,
      info: false,
      msg: '',
      isBreak: false
    }
  }
  const param: DailyScheduleMasterInsertInEntity = {
    shisetuId: localOneway.or06618Oneway.dailyScheduleMasterInData.shisetuId,
    svJigyoId: localOneway.or06618Oneway.dailyScheduleMasterInData.svJigyoId,
    bunrui3SetList: bunrui3SetList,
  }
  await ScreenRepository.update('dailyScheduleMasterUpdate', param)

  return {
      error: false,
      info: false,
      msg: '',
      isBreak: false
    }
}
defineExpose({
  insert,
})

/**
 * 事業所を監視
 *
 * @description
 */
watch(
  () => local.mo00040,
  async (newValue) => {
    if (newValue?.modelValue && newValue?.modelValue !== localOneway.or06618Oneway.dailyScheduleMasterInData.svJigyoId) {
      if (isEdit.value ) {
        const dialogResult = await openEditDialog()
        switch (dialogResult) {
          case Or06618Const.DEFAULT.DIALOG_RESULT_YES:
            await insert()
            localOneway.or06618Oneway.dailyScheduleMasterInData.svJigyoId = newValue.modelValue
            await fetchInitData()
            break
          case Or06618Const.DEFAULT.DIALOG_RESULT_NO:
            // いいえ選択時は編集内容を破棄するので何もしない
            localOneway.or06618Oneway.dailyScheduleMasterInData.svJigyoId = newValue.modelValue
            await fetchInitData()
            break
          case Or06618Const.DEFAULT.DIALOG_RESULT_CANCEL:
            // キャンセル選択時は複写データの作成を行わずに終了する
            local.mo00040.modelValue = localOneway.or06618Oneway.dailyScheduleMasterInData.svJigyoId
            return
        }
      } else {
        localOneway.or06618Oneway.dailyScheduleMasterInData.svJigyoId = newValue.modelValue
        await fetchInitData()
      }
    }

  }
)

/**
 * 編集破棄ダイアログ表示
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
async function openEditDialog(): Promise<string> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)

        let result = Or06618Const.DEFAULT.DIALOG_RESULT_CANCEL

        if (event?.firstBtnClickFlg) {
          result = Or06618Const.DEFAULT.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or06618Const.DEFAULT.DIALOG_RESULT_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = Or06618Const.DEFAULT.DIALOG_RESULT_CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  return useScreenStore().isEditNavControl()
})
</script>

<template>
  <c-v-sheet class="view">
    <c-v-row class="operationArea">
      <c-v-col
        cols="auto"
        class="ma-1 pt-1 mr-4"
      >
        <base-at-label :value="t('label.office-selection')" />
      </c-v-col>
      <c-v-col>
        <base-mo00040
          v-model="local.mo00040"
          :oneway-model-value="localOneway.mo00040Oneway"
          v-bind="{ ...$attrs }"
        />
      </c-v-col>
    </c-v-row>
    <!-- 予防計画書からの初期取込 -->
    <c-v-row class="subSection">
      <c-v-col
        class="sectionHeaderOne"
        cols="3"
      >
        <base-mo01338
          :oneway-model-value="localOneway.mo01338Oneway"
          style="background-color: rgb(var(--v-theme-background)) !important"
        />
      </c-v-col>
      <c-v-col class="sectionContentOne">
        <g-custom-or01086
          v-bind="or01086"
          :oneway-model-value="localOneway.or01086Oneway"
          :unique-cp-id="or01086.uniqueCpId"
        /> </c-v-col></c-v-row
    ><c-v-row class="subSection">
      <!-- 初期値 -->
      <c-v-col
        class="sectionHeaderTwo"
        style="writing-mode: vertical-lr; text-align: center; display: grid"
        cols="1"
      >
        <base-mo01338
          :oneway-model-value="localOneway.mo01338"
          style="background-color: rgb(var(--v-theme-background)) !important"
        />
      </c-v-col>
      <c-v-col class="sectionVol">
        <!---->
        <c-v-row class="subSection">
          <c-v-col
            class="sectionHeader"
            cols="2"
          >
            <base-mo01338
              :oneway-model-value="localOneway.mo01338Twoway"
              style="background-color: rgb(var(--v-theme-background)) !important"
            />
          </c-v-col>
          <c-v-col class="sectionContent">
            <g-custom-or01080
              v-bind="or01080"
              :oneway-model-value="localOneway.or01086Oneway"
              :unique-cp-id="or01080.uniqueCpId"
            /> </c-v-col
        ></c-v-row>
        <!---->
        <c-v-row class="subSection">
          <c-v-col
            class="sectionHeader"
            cols="2"
          >
            <base-mo01338
              :oneway-model-value="localOneway.mo01338Threeway"
              style="background-color: rgb(var(--v-theme-background)) !important"
            />
          </c-v-col>
          <c-v-col class="sectionContent">
            <g-custom-or01083
              v-bind="or01083"
              :oneway-model-value="localOneway.or01083Oneway"
              :unique-cp-id="or01083.uniqueCpId"
            /> </c-v-col
        ></c-v-row>
        <!---->
        <c-v-row class="subSection">
          <c-v-col
            class="sectionHeader"
            cols="2"
          >
            <base-mo01338
              :oneway-model-value="localOneway.mo01338Fourway"
              style="background-color: rgb(var(--v-theme-background)) !important"
            />
          </c-v-col>
          <c-v-col class="sectionContent">
            <g-custom-or01084
              v-bind="or01084"
              :oneway-model-value="localOneway.or01084Oneway"
              :unique-cp-id="or01084.uniqueCpId"
            /> </c-v-col
        ></c-v-row>
        <!---->
        <c-v-row class="subSection">
          <c-v-col
            class="sectionHeader"
            cols="2"
          >
            <base-mo01338
              :oneway-model-value="localOneway.mo01338Fiveway"
              style="background-color: rgb(var(--v-theme-background)) !important"
            />
          </c-v-col>
          <c-v-col class="sectionContent">
            <g-custom-or01085
              v-bind="or01085"
              :unique-cp-id="or01085.uniqueCpId"
            /> </c-v-col
        ></c-v-row>
        <!---->
        <c-v-row class="subSection">
          <c-v-col
            class="sectionHeader"
            cols="2"
          >
            <base-mo01338
              :oneway-model-value="localOneway.mo01338Sixway"
              style="background-color: rgb(var(--v-theme-background)) !important"
            />
          </c-v-col>
          <c-v-col class="sectionContent">
            <g-custom-or01082
              v-bind="or01082"
              :unique-cp-id="or01082.uniqueCpId"
            /> </c-v-col
        ></c-v-row>
      </c-v-col>
    </c-v-row>
  </c-v-sheet>
  <!-- データ変更確認ダイアログ -->
  <g-base-or21814 v-bind="or21814_1" />
</template>

<style scoped lang="scss">
.view {
  min-height: 280px;
  min-width: 520px;
  display: flex;
  margin: 0px;
  padding: 8px;
  flex-direction: column;

  .v-row {
    margin: unset;
  }

  .operationArea {
    flex: 0 0 auto;
    height: 55px;

    .v-col {
      padding-left: 0px;
      padding-right: 0px;
      padding-top: 0px;
      padding-bottom: 0px;
    }

    .v-col:last-child {
      text-align: right;
    }
  }

  .subSection {
    .sectionHeaderOne {
      border: solid thin rgb(var(--v-theme-light));
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: left;
      padding: 0;
      --v-theme-overlay-multiplier: var(--v-theme-background-overlay-multiplier);
      background-color: rgb(var(--v-theme-background)) !important;
      color: rgb(var(--v-theme-on-background)) !important;
      font-weight: bold;

      span {
        width: 100%;
        padding-left: 8px;
        font-size: 14px;
      }

      small {
        width: 100%;
        padding-left: 0px;
        font-weight: normal;
      }
    }
    .sectionHeaderTwo {
      border: solid thin rgb(var(--v-theme-light));
      border-top: none;
      display: flex;
      flex-direction: column;
      height: 256px;
      justify-content: center;
      align-items: left;
      padding: 0;
      --v-theme-overlay-multiplier: var(--v-theme-background-overlay-multiplier);
      background-color: rgb(var(--v-theme-background)) !important;
      color: rgb(var(--v-theme-on-background)) !important;
      font-weight: bold;

      span {
        width: 100%;
        padding-left: 8px;
        font-size: 14px;
      }

      small {
        width: 100%;
        padding-left: 0px;
        font-weight: normal;
      }
    }

    .sectionHeader {
      border: solid thin rgb(var(--v-theme-light));
      border-left: none;
      border-top: none;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: left;
      min-width: 148px;
      padding: 0;
      --v-theme-overlay-multiplier: var(--v-theme-background-overlay-multiplier);
      background-color: rgb(var(--v-theme-background)) !important;
      color: rgb(var(--v-theme-on-background)) !important;
      font-weight: bold;

      span {
        width: 100%;
        padding-left: 0px;
        font-size: 14px;
      }

      small {
        width: 100%;
        padding-left: 0px;
        font-weight: normal;
      }
    }
    .sectionVol {
      padding-left: 0;
      padding-right: 0;
      padding-top: 0;
      padding-bottom: 0;
    }
  }

  .sectionContentOne {
    border: solid thin rgb(var(--v-theme-light));
    border-left: none;
    width: 100%;
    display: flex;
    align-items: center;
    padding: 8px !important;

    .v-sheet {
      width: 100%;

      :deep(.radio-group) {
        width: 100%;

        .v-col-auto {
          width: 100%;
        }
      }
    }
  }

  .sectionContent {
    border: solid thin rgb(var(--v-theme-light));
    border-left: none;
    border-top: none;
    width: 100%;
    display: flex;
    align-items: center;
    padding: 8px !important;

    .v-sheet {
      width: 100%;

      :deep(.radio-group) {
        width: 100%;

        .v-col-auto {
          width: 100%;
        }
      }
    }
  }
}
</style>
