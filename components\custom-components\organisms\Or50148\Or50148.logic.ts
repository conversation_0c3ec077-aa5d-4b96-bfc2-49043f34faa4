import { Or50148Const } from './Or50148.constants'
import type {
  Or50148StateType,
  Or50148TwoWayType,
  TableDataList,
  Service_list,
  <PERSON><PERSON><PERSON>_list,
  <PERSON><PERSON>_shiyen_list,
  Shogu_shiyen_list,
  Te<PERSON>yoJigyoList,
} from './Or50148.type'
import {
  useInitialize,
  useOneWayBindAccessor,
  useTwoWayBindAccessor,
} from '~/composables/useComponentLogic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import { Or26257Const } from '~/components/custom-components/organisms/Or26257/Or26257.constants'
import { Or26257Logic } from '~/components/custom-components/organisms/Or26257/Or26257.logic'
import { Or28256Const } from '~/components/custom-components/organisms/Or28256/Or28256.constants'
import { Or28256Logic } from '~/components/custom-components/organisms/Or28256/Or28256.logic'
import { Or11017Const } from '~/components/custom-components/organisms/Or11017/Or11017.constants'
import { Or11017Logic } from '~/components/custom-components/organisms/Or11017/Or11017.logic'
import { Or52335Const } from '~/components/custom-components/organisms/Or52335/Or52335.constants'
import { Or52335Logic } from '~/components/custom-components/organisms/Or52335/Or52335.logic'
import { Or27011Const } from '~/components/custom-components/organisms/Or27011/Or27011.constants'
import { Or27011Logic } from '~/components/custom-components/organisms/Or27011/Or27011.logic'
import { Or27220Const } from '~/components/custom-components/organisms/Or27220/Or27220.constants'
import { Or27220Logic } from '~/components/custom-components/organisms/Or27220/Or27220.logic'
import { Or27592Const } from '~/components/custom-components/organisms/Or27592/Or27592.constants'
import { Or27592Logic } from '~/components/custom-components/organisms/Or27592/Or27592.logic'

/**
 * Or50148:タイトル
 * GUI01258_支援経過記録
 *
 * @description
 * 処理ロジック
 *
 * <AUTHOR> NGUYEN NHUT THANH
 */
export namespace Or50148Logic {
  /**
   * initialize
   *
   * @description
   * コンポーネントの初期化処理。
   * 共通関数を呼びpiniaの領域を初期化する。
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize<Or50148TwoWayType>({
      cpId: Or50148Const.CP_ID(0),
      uniqueCpId,
      initTwoWayValue: {
        kaigo_shiyen_list: [] as Kaigo_shiyen_list[],
        shubetsu_list: [] as Shubetsu_list[],
        shogu_shiyen_list: [] as Shogu_shiyen_list[],
        service_list: [] as Service_list[],
        shiyen_list_mapping: [] as TableDataList[],
        tekiyoJigyoList: [] as TekiyoJigyoList[],
      },
      childCps: [
        { cpId: Or21814Const.CP_ID(0) },
        { cpId: Or51775Const.CP_ID(0) },
        { cpId: Or26257Const.CP_ID(0) },
        { cpId: Or28256Const.CP_ID(0) },
        { cpId: Or11017Const.CP_ID(0) },
        { cpId: Or52335Const.CP_ID(0) },
        { cpId: Or27011Const.CP_ID(0) },
        { cpId: Or27220Const.CP_ID(0) },
        { cpId: Or27592Const.CP_ID(0) },
      ],
    })

    // 子コンポーネントのセットアップ
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(0)].uniqueCpId)
    Or51775Logic.initialize(childCpIds[Or51775Const.CP_ID(0)].uniqueCpId)
    Or26257Logic.initialize(childCpIds[Or26257Const.CP_ID(0)].uniqueCpId)
    Or28256Logic.initialize(childCpIds[Or28256Const.CP_ID(0)].uniqueCpId)
    Or11017Logic.initialize(childCpIds[Or11017Const.CP_ID(0)].uniqueCpId)
    Or52335Logic.initialize(childCpIds[Or52335Const.CP_ID(0)].uniqueCpId)
    Or27011Logic.initialize(childCpIds[Or27011Const.CP_ID(0)].uniqueCpId)
    Or27220Logic.initialize(childCpIds[Or27220Const.CP_ID(0)].uniqueCpId)
    Or27592Logic.initialize(childCpIds[Or27592Const.CP_ID(0)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }
  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or50148StateType>(Or50148Const.CP_ID(0))
  /**
   * TwoWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const data = useTwoWayBindAccessor<Or50148TwoWayType>(Or50148Const.CP_ID(0))
}
