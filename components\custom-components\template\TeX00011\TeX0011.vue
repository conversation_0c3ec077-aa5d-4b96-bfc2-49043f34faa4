<script setup lang="ts">
/**
 * GUI00892_チェック項目画面
 *
 * @description
 * GUI00892_チェック項目画面
 *
 * <AUTHOR>
 */

import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or13850Const } from '../../organisms/Or13850/Or13850.constants'
import { Or13844Const } from '../../organisms/Or13844/Or13844.constants'
import { Or13872Const } from '../../organisms/Or13872/Or13872.constants'
import type { Or51642OneWayType, Or51642Param } from '../../organisms/Or51642/Or51642.type'
import { Or51642Logic } from '../../organisms/Or51642/Or51642.logic'
import { OrX0115Logic } from '../../organisms/OrX0115/OrX0115.logic'
import { Or26166Logic } from '../../organisms/Or26166/Or26166.logic'
import { OrX0115Const } from '../../organisms/OrX0115/OrX0115.constants'
import { Or26166Const } from '../../organisms/Or26166/Or26166.constants'
import { Or13844Logic } from '../../organisms/Or13844/Or13844.logic'
import { Or26257Logic } from '../../organisms/Or26257/Or26257.logic'
import { Or26257Const } from '../../organisms/Or26257/Or26257.constants'
import { Or51642Const } from '../../organisms/Or51642/Or51642.constants'
import { Or13872Logic } from '../../organisms/Or13872/Or13872.logic'
import { Or10406Logic } from '../../organisms/Or10406/Or10406.logic'
import { Or10406Const } from '../../organisms/Or10406/Or10406.constants'
import type { RirekiInfoDataListItem } from '../../organisms/Or26166/Or26166.type'
import type { JigyoInfo } from './TeX0011.type'
import { TeX0011Const } from './TeX0011.constants'
import {
  useSetupChildProps,
  useUserListInfo,
  useSystemCommonsStore,
  useJigyoList,
  hasRegistAuth,
  useScreenStore,
} from '#imports'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import type { Or13850OnewayType } from '~/types/cmn/business/components/Or13850Type'
import { CustomClass } from '~/types/CustomClassType'
import { Or21735Const } from '~/components/base-components/organisms/Or21735/Or21735.constants'
import { Or21736Const } from '~/components/base-components/organisms/Or21736/Or21736.constants'
import { Or21737Const } from '~/components/base-components/organisms/Or21737/Or21737.constants'
import { Or21738Const } from '~/components/base-components/organisms/Or21738/Or21738.constants'

import type { Mo00020OnewayType } from '~/types/business/components/Mo00020Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  CheckItemsSelectInEntity,
  CheckItemsSelectOutEntity,
  RirekiInfo,
} from '~/repositories/cmn/entities/CheckItemsSelectEntity'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import { ResBodyStatusCode } from '~/constants/api-constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { DIALOG_BTN, UPDATE_KBN } from '~/constants/classification-constants'
import type {
  CheckItemsInsertSelectInEntity,
  CheckItemsInsertSelectOutEntity,
} from '~/repositories/cmn/entities/CheckItemsInsertSelectEntity'
import type { Or13844OnewayType } from '~/types/cmn/business/components/Or13844Type'
import type { Or13872OnewayType } from '~/types/cmn/business/components/Or13872Type'
import type { Or26166OnewayType, Or26166Type } from '~/types/cmn/business/components/Or26166Type'
import type { OrX0115OnewayType } from '~/types/cmn/business/components/OrX0115Type'
import type {
  CheckItemsPlanPeriodSelectInEntity,
  CheckItemsPlanPeriodSelectOutEntity,
} from '~/repositories/cmn/entities/CheckItemsPlanPeriodSelectEntity'
import type { Or26257OnewayType, Or26257Type } from '~/types/cmn/business/components/Or26257Type'
import type {
  CheckItemsHistorySelectInEntity,
  CheckItemsHistorySelectOutEntity,
} from '~/repositories/cmn/entities/CheckItemsHistorySelectEntity'
import type { Or10406OnewayType, Or10406Type } from '~/types/cmn/business/components/Or10406Type'

/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

const { syscomUserSelectWatchFunc } = useUserListInfo()
const { jigyoListWatch } = useJigyoList()

// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()

/**
 * 利用者ID
 */
const userId = ref<string>(systemCommonsStore.getUserId!)

/**
 * 事業所情報
 */
const jigyoInfo = ref<JigyoInfo>()

// 共通処理の編集権限チェック
const editFlg = ref(false)
// 期間件数
const planCount = ref(0)
// 新規
const isNew = ref(false)
/**
 * 期間管理フラグ
 */
const plannningPeriodManageFlg = ref<string>('1')
//Or21814_有機体:確認ダイアログ
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })

const or11871 = ref({ uniqueCpId: '' })
const or00248 = ref({ uniqueCpId: '' })
const or13850 = ref({ uniqueCpId: '' })
const or13850_2 = ref({ uniqueCpId: '' })
const or13844 = ref({ uniqueCpId: '' })
const or13872 = ref({ uniqueCpId: '' })
const or21735 = ref({ uniqueCpId: '' })
const or21736 = ref({ uniqueCpId: '' })
const or21737 = ref({ uniqueCpId: '' })
const or21738 = ref({ uniqueCpId: '' })
const or41179 = ref({ uniqueCpId: '' })
const orX0115 = ref({ uniqueCpId: OrX0115Const.CP_ID(1) })
const or26166 = ref({ uniqueCpId: Or26166Const.CP_ID(1) })
const or51642 = ref({ uniqueCpId: '' })
const or10406 = ref({ uniqueCpId: '' })
const or26257 = ref({ uniqueCpId: '' })

/**
 * ロカール
 */
const local = reactive({
  createDate: {
    value: '',
    mo01343: {
      value: '',
      mo00024: {
        isOpen: false,
      },
    },
  },
  // 実行無効
  executionDisabled: false,
  //削除
  delFlag: false,
  // 履歴選択ダイアログ
  or26166: {
    rirekiInfoDataList: [],
  } as Or26166Type,
})

/**
 * ロカールoneway
 */
const localOneway = reactive({
  officeSelectOneway: {
    officeId: '',
    officeName: '',
  },
  // 作成者選択
  authorSelectOneway: {
    itemTitle: t('label.author'),
    id: '',
    name: '',
    iconBtnDisabled: local.delFlag,
    showLabelMode: false,
  } as Or13850OnewayType,
  // 作成日
  createDateOneway: {
    itemLabel: t('label.create-date'),
    isRequired: true,
    isVerticalLabel: true,
    showItemLabel: true,
    showSelectArrow: false,
    customClass: new CustomClass({
      outerClass: 'createDateOuterClass d-flex flex-column',
      labelClass: 'ma-1 ml-0 pt-1',
    }),
    width: '140',
    totalWidth: '220',
  } as Mo00020OnewayType,
  // 計画対象期間選択
  planningPeriodSelectOneway: {
    plainningPeriodManageFlg: '',
    planningPeriodList: [],
    pageBtnAutoDisabled: false,
    labelAutoDisabled: false,
    showLabelMode: false,
  } as Or13844OnewayType,
  orX0115Oneway: {
    kindId: systemCommonsStore.getSyubetu,
    sc1Id: '',
  } as OrX0115OnewayType,
  // 履歴選択
  hisotrySelectOneway: {
    /** 履歴情報 */
    historyInfo: {
      /** アセスメントID */
      gdlId: '',
      /** 履歴番号 */
      krirekiNo: '',
      /** 履歴総件数 */
      krirekiCnt: '',
      /** ケアチェックID */
      cc1Id: '',
    },
    pageBtnAutoDisabled: false,
    showLabelMode: false,
  } as Or13872OnewayType,
  // 履歴選択ダイアログ
  or26166Oneway: {
    rirekiInfoList:{
        /**事業者ID*/
        svJigyoId: '',
        /**計画期間ID*/
        sc1Id: '',
        /**利用者ID*/
        userId: '',
        /**履歴ID*/
        assId: '',
      }
  } as Or26166OnewayType,
  or51642Oneway: {
    checkType: Or51642Const.DEFAULT.Main,
  } as Or51642OneWayType,
})

const or26257Type = ref<Or26257Type>({
  shokuin: {
    shokuin1Knj: '',
    shokuin2Knj: '',
  },
} as Or26257Type)

const or26257Data: Or26257OnewayType = {
  // システム略称
  sysCdKbn: '',
  // アカウント設定
  secAccountAllFlg: '',
  // 適用事業所ＩＤリスト
  svJigyoIdList: systemCommonsStore.getSvJigyoIdList.map((item) => {
    return {
      svJigyoId: item,
    }
  }),
  // 職員ID
  shokuinId: '',
  // システムコード
  gsysCd: '',
  // モード
  selectMode: TeX0011Const.DEFAULT.SELECT_MODE_12,
  // 基準日
  kijunYmd: systemCommonsStore.getSystemDate ?? '',
  // 事業所ID
  defSvJigyoId: '',
  // フィルターフラグ
  filterDwFlg: TeX0011Const.DEFAULT.FILTER_DW_FLG_1,
  // 雇用状態
  koyouState: '',
  // 地域フラグ
  areaFlg: '',
  // 表示名称リスト
  hyoujiColumnList: [],
  // 未設定フラグ
  misetteiFlg: TeX0011Const.DEFAULT.MISETTEI_FLG_OK,
  // 他職員参照権限
  otherRead: '',
  // 中止フラグ
  refStopFlg: '',
  // 処理フラグ
  syoriFlg: '',
  // メニュー１ID
  menu1Id: '',
  // 件数フラグ
  kensuFlg: '',
  // 職員IDリスト
  shokuinIdList: [],
}

const historyInfo = ref({} as RirekiInfo)

const isInit = ref(false)

const Or10406Data: Or10406OnewayType = {
  type: '1',
}

const or10406Type = ref<Or10406Type>({
  value: '',
})
/**************************************************
 * Pinia
 **************************************************/
useSetupChildProps(props.uniqueCpId, {
  [Or11871Const.CP_ID]: or11871.value,
  [Or00248Const.CP_ID(1)]: or00248.value,
  [Or13850Const.CP_ID(1)]: or13850.value,
  [Or13844Const.CP_ID(0)]: or13844.value,
  [Or13872Const.CP_ID(0)]: or13872.value,
  [Or13850Const.CP_ID(2)]: or13850_2.value,
  [Or21735Const.CP_ID(1)]: or21735.value,
  [Or21736Const.CP_ID(1)]: or21736.value,
  [Or21737Const.CP_ID(1)]: or21737.value,
  [Or21738Const.CP_ID(1)]: or21738.value,
  [OrX0115Const.CP_ID(1)]: orX0115.value,
  [Or41179Const.CP_ID(1)]: or41179.value,
  [Or26166Const.CP_ID(1)]: or26166.value,
  [Or26257Const.CP_ID(1)]: or26257.value,
  [Or51642Const.CP_ID(1)]: or51642.value,
  [Or10406Const.CP_ID(1)]: or10406.value,
})

// 子コンポーネントに対して初期設定を行う
Or11871Logic.state.set({
  uniqueCpId: or11871.value.uniqueCpId,
  state: {
    screenTitleLabel: t('チェック項目'),
    showFavorite: true,
    showViewSelect: false,
    viewSelectItems: [],
    showSaveBtn: true,
    showCreateBtn: true,
    showCreateMenuCopy: false,
    showPrintBtn: true,
    showMasterBtn: true,
    showOptionMenuBtn: true,
    showOptionMenuDelete: true,
  },
})

/**************************************************
 * computed関数
 **************************************************/
// ダイアログ表示フラグ
const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

// 計画対象期間ダイアログ表示フラグ
const showDialogOrX0115 = computed(() => {
  // OrX0115 のダイアログ開閉状態
  return OrX0115Logic.state.get(orX0115.value.uniqueCpId)?.isOpen ?? false
})

/**
 * ダイアログ表示フラグ
 */
const showDialogOr26166 = computed(() => {
  // Or26166 のダイアログ開閉状態
  return Or26166Logic.state.get(or26166.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr26257 = computed(() => {
  // Or26257のダイアログ開閉状態
  return Or26257Logic.state.get(or26257.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr10406 = computed(() => {
  // Or10406のダイアログ開閉状態
  return Or10406Logic.state.get(or10406.value.uniqueCpId)?.isOpen ?? false
})

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => useScreenStore().isEditNavControl())

/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(async () => {
  editFlg.value = await hasRegistAuth()
  editFlg.value = true
  initControl()
  officeSelect()
})
/**************************************************
 * 関数定義
 **************************************************/
/**
 * 利用者選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで処理を実行する
 *
 * @param newSelfId - 変更後の利用者番号
 */
function callbackUserChange(newSelfId: string) {
  if (newSelfId !== '') {
    userId.value = newSelfId

    // 事業所に設定
    Or41179Logic.state.set({
      uniqueCpId: or41179.value.uniqueCpId,
      state: {
        searchCriteria: {
          selfId: newSelfId,
        },
      },
    })
  }
}
// ★利用者選択監視関数を実行
syscomUserSelectWatchFunc(callbackUserChange)

/**
 * 初期化処理
 */
function initControl() {
  localOneway.officeSelectOneway.officeName = '特別養護ほのぼの'
}

/**
 * 事業所選択初期化処理
 */
function officeSelect() {
  // 事業所選択プルダウンの検索条件設定
  // 検索条件未指定だと、ヘッダーで選択した適用事業所が表示される
  // 検索条件が変更されると、共通処理にて事業所の情報が更新される
  // 更新後やプルダウンの値変更後、変更後の事業所に基づいて処理を行う場合、
  // 関数「 jigyoListWatch 」にコールバック関数を渡す
  if (systemCommonsStore.getUserSelectSelfId()) {
    userId.value = systemCommonsStore.getUserSelectSelfId() ?? ''
    Or41179Logic.state.set({
      uniqueCpId: or41179.value.uniqueCpId,
      state: {
        searchCriteria: {
          selfId: systemCommonsStore.getUserSelectSelfId(),
        },
      },
    })
  }
}

/**
 * 事業所選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newJigyoId - 変更後の事業者ID
 */
function callbackFuncJigyo(newJigyoId: string) {
  if (newJigyoId !== '') {
    const jigyoInfoList = Or41179Logic.state.get(or41179.value.uniqueCpId)?.jigyoInfoList
    const jigyoInfoData = jigyoInfoList?.find((jigyoInfo) => jigyoInfo.svJigyoId === newJigyoId)
    jigyoInfo.value = jigyoInfoData
  }
}

// ★事業所選択監視関数を実行
jigyoListWatch(or41179.value.uniqueCpId, callbackFuncJigyo)

/**
 * 画面最新情報を取得する
 *
 * @param historyUpdateKbn - 履歴更新区分
 */
const getData = (historyUpdateKbn: string) => {
  Or51642Logic.state.set({
    uniqueCpId: or51642.value.uniqueCpId,
    state: {
      param: {
        executeFlag: 'getData',
        //法人ID：共通情報.法人ID
        houjinId: systemCommonsStore.getHoujinId,
        //施設ID：共通情報.施設ID
        shisetuId: jigyoInfo.value?.shisetuId,
        //事業者ID：共通情報.事業者ID
        svJigyoId: jigyoInfo.value?.svJigyoId,
        //種別ID:共通情報.種別ID
        syubetsuId: systemCommonsStore.getSvJigyoId,
        //利用者ID：共通情報.利用者ID
        userId: userId.value,
        //計画期間ID：画面.計画期間ID
        sc1Id: localOneway.planningPeriodSelectOneway.planningPeriodInfo!.id,
        // 履歴更新区分
        historyUpdateKbn,
      } as Or51642Param,
    },
  })
}
/**
 * 画面最新情報を保存する
 *
 * @param editFlag - 変更操作によって保存状態をトリガーする
 */
const save = (editFlag: string) => {
  // 画面入力データ変更があるかどうかを判定する
  if (!isEdit.value) {
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: {
        // ダイアログタイトル
        dialogTitle: t('label.confirm'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-21800'),
        firstBtnType: 'blank',
        secondBtnType: 'normal1',
        secondBtnLabel: t('btn.yes'),
        thirdBtnType: 'blank',
      },
    })
    // 確認ダイアログをオープン
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: {
        isOpen: true,
      },
    })
    return
  }
  Or51642Logic.state.set({
    uniqueCpId: or51642.value.uniqueCpId,
    state: {
      param: {
        executeFlag: 'save',
        editFlag: editFlag,
      } as Or51642Param,
    },
  })
}
/**
 * 画面情報を削除する
 */
const del = () => {
  Or51642Logic.state.set({
    uniqueCpId: or51642.value.uniqueCpId,
    state: {
      param: {
        executeFlag: 'del',
      } as Or51642Param,
    },
  })
}

/**
 * 「新規ボタン」押下
 */
const addClickBtn = async () => {
  //期間管理フラグが「管理する」、かつ、計画対象期間情報.期間総件数 = 0（期間なし）
  if (
    plannningPeriodManageFlg.value === TeX0011Const.DEFAULT.PLANNING_PERIOD_MANAGE &&
    !planCount.value
  ) {
    const result = await getOr21814_1DialogResult(t('message.i-cmn-11300'))
    if (result === DIALOG_BTN.YES) {
      //はい：AC011を実行
      await planningPeriodSelectClick()
      return
    }
  }

  //二回目新規ボタン押下する場合
  if (isNew.value) {
    await getOr21814_1DialogResult(t('message.i-cmn-11265', TeX0011Const.DEFAULT.ASSESSMENT))
    return
  }

  // 画面入力データ変更があるかどうかを判定する
  if (isEdit.value) {
    const result = await getOr21814_3DialogResult(t('message.i-cmn-10430'))
    switch (result) {
      case DIALOG_BTN.YES:
        save('add')
        break
      case DIALOG_BTN.NO:
        await getNewDataInfo()
        break
      default:
        break
    }
  } else {
    await getNewDataInfo()
  }
}
/**
 * 「削除」押下
 */
const delClickBtn = async () => {
  const res = await getOr21814_2DialogResult(
    t('message.i-cmn-11326', [local.createDate.value, TeX0011Const.DEFAULT.CHECK_ITEM])
  )
  if (res !== DIALOG_BTN.YES) {
    return
  }
  //・以下のボタンは実行無効にする。
  //  新規、複写、独自印刷、印刷、削除
  local.executionDisabled = true
  //以下の項目を非活性にする
  //作成者選択アイコンボタン、作成日、作成日カレンダ
  local.delFlag = true
  //・以下の項目を非表示にする。
  //  入力フームのすべて項目
  del()
}

/**
 * 初期情報取得
 */
async function getInitDataInfo() {
  // チェック項目画面情報取得(IN)
  const inputData: CheckItemsSelectInEntity = {
    shisetuId: jigyoInfo.value?.shisetuId ?? '',
    svJigyoId: jigyoInfo.value?.svJigyoId ?? '',
    syubetsuId: systemCommonsStore.getSyubetu ?? '',
    userId: userId.value,
  }

  // チェック項目画面初期情報取得
  const res: CheckItemsSelectOutEntity = await ScreenRepository.select(
    'checkItemsSelect',
    inputData
  )

  if (res.statusCode !== '200') {
    return
  }
  isInit.value = false
  plannningPeriodManageFlg.value = res.data.kikanFlg
  planCount.value = Number(res.data.kikanObj.kikanTotalCnt)
  // 計画対象期間情報を設定して表示する。
  localOneway.planningPeriodSelectOneway.plainningPeriodManageFlg = res.data.kikanFlg
  let period = res.data.kikanObj.startYmd + '～' + res.data.kikanObj.endYmd
  if (!res.data.kikanObj.startYmd || !res.data.kikanObj.endYmd) {
    period = ''
  }
  localOneway.planningPeriodSelectOneway.planningPeriodInfo = {
    id: res.data.kikanObj.sc1Id,
    period: period,
    periodNo: res.data.kikanObj.periodNo ?? '0',
    periodCnt: res.data.kikanObj.kikanTotalCnt ?? '0',
  }

  historyInfo.value = res.data.rirekiInfoList[0]
  // チェック項目ヘッダ履歴情報1件以上
  if (res.data.rirekiInfoList.length) {
    localOneway.hisotrySelectOneway.historyInfo = {
      gdlId: res.data.rirekiInfoList[0].assId,
      krirekiNo: '1',
      krirekiCnt: res.data.rirekiInfoList[0].rowCount,
    }
    // 作成日に設定する
    local.createDate.value = res.data.rirekiInfoList[0].createYmd
    // 作成者に設定する
    localOneway.authorSelectOneway.id = res.data.rirekiInfoList[0].shokuId
    localOneway.authorSelectOneway.name = res.data.rirekiInfoList[0].shokuId
  }
  // チェック項目ヘッダ履歴情報0件
  else {
    localOneway.hisotrySelectOneway.historyInfo = {
      gdlId: '',
      krirekiNo: '0',
      krirekiCnt: '0',
    }
    // 作成日に設定する
    local.createDate.value = systemCommonsStore.getSystemDate ?? ''
    // 作成者に設定する
    localOneway.authorSelectOneway.id = systemCommonsStore.getCurrentUser.chkShokuId ?? ''
    localOneway.authorSelectOneway.name = systemCommonsStore.getCurrentUser.shokuinKnj ?? ''
  }
  // 取得した情報画面項目に設定する。
  localOneway.or51642Oneway = {
    ...localOneway.or51642Oneway,
    checkItemInfoList: res.data.checkItemInfoList,
    checkMainRirekiList: res.data.checkMainRirekiList,
    rirekiInfo: res.data.rirekiInfoList[0],
  }
  // ページの状態をリセットする
  isNew.value = false
  local.executionDisabled = false
  local.delFlag = false
  getData('')
}

/**
 * 新規情報取得
 */
async function getNewDataInfo() {
  // チェック項目画面情報取得(IN)
  const inputData: CheckItemsInsertSelectInEntity = {
    shisetuId: jigyoInfo.value?.shisetuId ?? '',
    svJigyoId: jigyoInfo.value?.svJigyoId ?? '',
    houjinId: systemCommonsStore.getHoujinId ?? '',
    userId: userId.value,
    sc1Id: String(localOneway.planningPeriodSelectOneway.planningPeriodInfo?.id),
    createYmd: systemCommonsStore.getSystemDate ?? '',
    shokuId: localOneway.authorSelectOneway.id ?? '',
    rowCount: localOneway.hisotrySelectOneway.historyInfo?.krirekiCnt ?? '',
  }
  // チェック項目画面新規情報取得
  const res: CheckItemsInsertSelectOutEntity = await ScreenRepository.select(
    'checkItemsInsertSelect',
    inputData
  )

  if (res.statusCode !== ResBodyStatusCode.SUCCESS) {
    return
  }
  isInit.value = false
  // 履歴総件数
  const totalCount = Number(localOneway.hisotrySelectOneway.historyInfo!.krirekiCnt)
  // 履歴総件数+1
  localOneway.hisotrySelectOneway.historyInfo = {
    gdlId: res.data.rirekiInfo.assId,
    krirekiNo: String(totalCount + 1),
    krirekiCnt: String(totalCount + 1),
  }
  // システム時間
  local.createDate.value = systemCommonsStore.getSystemDate ?? ''
  // 作成者に設定する
  localOneway.authorSelectOneway.id = systemCommonsStore.getCurrentUser.chkShokuId ?? ''
  localOneway.authorSelectOneway.name = systemCommonsStore.getCurrentUser.shokuinKnj ?? ''
  // 取得した情報画面項目に設定する。
  localOneway.or51642Oneway = {
    ...localOneway.or51642Oneway,
    checkItemInfoList: res.data.checkItemInfoList,
    checkMainRirekiList: res.data.checkMainRirekiList,
    rirekiInfo: res.data.rirekiInfo,
  }

  // 新規
  isNew.value = true
  getData('')
}

/**
 * メニューエベントを設定
 *
 * @param event - イベント
 */
const setOr11871Event = (event: Record<string, boolean>) => {
  Or11871Logic.event.set({
    uniqueCpId: or11871.value.uniqueCpId,
    events: event,
  })
}

/**
 * Or13872イベント発火
 *
 * @param event - イベント
 */
function setOr13872Event(event: Record<string, boolean>) {
  Or13872Logic.event.set({
    uniqueCpId: or13872.value.uniqueCpId,
    events: event,
  })
}

/**
 * Or13844イベント発火
 *
 * @param event - イベント
 */
function setOr13844Event(event: Record<string, boolean>) {
  Or13844Logic.event.set({
    uniqueCpId: or13844.value.uniqueCpId,
    events: event,
  })
}

/**
 * 計画対象期間選択開くボタンクリック
 *
 */
async function planningPeriodSelectClick() {
  //画面入力データに変更がある場合
  if (isEdit.value) {
    const result = await getOr21814_3DialogResult(t('message.i-cmn-10430'))
    switch (result) {
      //はい：AC003(保存処理)を実行し、処理続き
      case DIALOG_BTN.YES:
        save('')
        return
      //いいえ：処理続き
      case DIALOG_BTN.NO:
        break
      //キャンセル：処理終了
      default:
        break
    }
  }
  // 計画対象期間選択画面を開く
  OrX0115Logic.state.set({
    uniqueCpId: orX0115.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 計画期間取得
 *
 * @param pageFlg - ページ区分
 */
async function getPlanPeroidInfo(pageFlg: string) {
  // チェック項目画面情報取得(IN)
  const inputData: CheckItemsPlanPeriodSelectInEntity = {
    shisetuId: jigyoInfo.value?.shisetuId ?? '',
    svJigyoId: jigyoInfo.value?.svJigyoId ?? '',
    syubetsuId: systemCommonsStore.getSyubetu ?? '',
    userId: userId.value,
    sc1Id: localOneway.planningPeriodSelectOneway.planningPeriodInfo!.id,
    pageFlag: pageFlg,
  }
  // チェック項目画面情報取得
  const res: CheckItemsPlanPeriodSelectOutEntity = await ScreenRepository.select(
    'checkItemsPlanPeriodSelect',
    inputData
  )

  if (res.statusCode !== ResBodyStatusCode.SUCCESS) {
    return
  }
  isInit.value = false
  planCount.value = Number(res.data.kikanObj.kikanTotalCnt)
  // 計画対象期間情報を設定して表示する。
  localOneway.planningPeriodSelectOneway.planningPeriodInfo = {
    id: res.data.kikanObj.sc1Id,
    period: res.data.kikanObj.startYmd + '～' + res.data.kikanObj.endYmd,
    periodNo: res.data.kikanObj.periodNo ?? '0',
    periodCnt: res.data.kikanObj.kikanTotalCnt ?? '0',
  }
  historyInfo.value = res.data.rirekiInfoList[0]
  // チェック項目ヘッダ履歴情報1件以上
  if (res.data.rirekiInfoList.length) {
    localOneway.hisotrySelectOneway.historyInfo = {
      gdlId: res.data.rirekiInfoList[0].assId,
      krirekiNo: '1',
      krirekiCnt: res.data.rirekiInfoList[0].rowCount,
    }
    // 作成日に設定する
    local.createDate.value = res.data.rirekiInfoList[0].createYmd
    // 作成者に設定する
    localOneway.authorSelectOneway.id = res.data.rirekiInfoList[0].shokuId
    localOneway.authorSelectOneway.name = res.data.rirekiInfoList[0].shokuId
  }
  // チェック項目ヘッダ履歴情報0件
  else {
    localOneway.hisotrySelectOneway.historyInfo = {
      gdlId: '',
      krirekiNo: '0',
      krirekiCnt: '0',
    }
    // 作成日に設定する
    local.createDate.value = systemCommonsStore.getSystemDate ?? ''
    // 作成者に設定する
    localOneway.authorSelectOneway.id = systemCommonsStore.getCurrentUser.chkShokuId ?? ''
    localOneway.authorSelectOneway.name = systemCommonsStore.getCurrentUser.shokuinKnj ?? ''
  }
  // 取得した情報画面項目に設定する。
  localOneway.or51642Oneway = {
    ...localOneway.or51642Oneway,
    checkItemInfoList: res.data.checkItemInfoList,
    checkMainRirekiList: res.data.checkMainRirekiList,
    rirekiInfo: res.data.rirekiInfoList[0],
  }
  // ページの状態をリセットする
  isNew.value = false
  local.executionDisabled = false
  local.delFlag = false
  // 履歴更新区分が"U"に設定する。
  getData(UPDATE_KBN.UPDATE)
}

/**
 * 履歴選択開くボタンクリック
 *
 */
function historySelectClick() {
  // 履歴選択画面を開く
  localOneway.or26166Oneway = {
    rirekiInfoList:
      {
        /**事業者ID*/
        svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
        /**計画期間ID*/
        sc1Id: localOneway.planningPeriodSelectOneway.planningPeriodInfo?.id ?? '',
        /**利用者ID*/
        userId: userId.value,
        /**履歴ID*/
        assId: localOneway.hisotrySelectOneway.historyInfo?.gdlId ?? '',
      },

  }
  // Or26166のダイアログ開閉状態を更新する
  Or26166Logic.state.set({
    uniqueCpId: or26166.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 履歴選択変更処理
 *
 * @param historyInfo - 履歴情報
 */
async function historySelectChange(historyInfo: RirekiInfoDataListItem) {
  localOneway.hisotrySelectOneway.historyInfo!.gdlId = historyInfo.assId
  await getChangeHistoryInfo(TeX0011Const.DEFAULT.HISTORY_KBN_OPEN, UPDATE_KBN.UPDATE)
}

/**
 * 歴史変更情報取得
 *
 * @param pageFlg - ページ区分
 *
 * @param updateKbn - 更新区分
 */
async function getChangeHistoryInfo(pageFlg: string, updateKbn: string) {
  // チェック項目画面情報取得(IN)
  const inputData: CheckItemsHistorySelectInEntity = {
    shisetuId: jigyoInfo.value?.shisetuId ?? '',
    svJigyoId: jigyoInfo.value?.svJigyoId ?? '',
    userId: userId.value,
    sc1Id: localOneway.planningPeriodSelectOneway.planningPeriodInfo!.id,
    ass1Id: localOneway.hisotrySelectOneway.historyInfo?.gdlId ?? '',
    pageFlag: pageFlg,
  }
  // チェック項目画面情報取得
  const res: CheckItemsHistorySelectOutEntity = await ScreenRepository.select(
    'checkItemsHistorySelect',
    inputData
  )

  if (res.statusCode !== '200') {
    return
  }

  isInit.value = false
  historyInfo.value = res.data.rirekiInfoList[0]
  // 履歴情報に設定する
  localOneway.hisotrySelectOneway.historyInfo = {
    gdlId: res.data.rirekiInfoList[0].assId,
    krirekiNo: res.data.rirekiInfoList[0].krirekiNo,
    krirekiCnt: res.data.rirekiInfoList[0].rowCount,
  }
  // 作成日に設定する
  local.createDate.value = res.data.rirekiInfoList[0].createYmd
  // 作成者に設定する
  localOneway.authorSelectOneway.id = res.data.rirekiInfoList[0].shokuId
  localOneway.authorSelectOneway.name = res.data.rirekiInfoList[0].shokuId
  // 取得した情報画面項目に設定する。
  localOneway.or51642Oneway = {
    ...localOneway.or51642Oneway,
    checkItemInfoList: res.data.checkItemInfoList,
    checkMainRirekiList: res.data.checkMainRirekiList,
    rirekiInfo: res.data.rirekiInfoList[0],
  }
  // ページの状態をリセットする
  isNew.value = false
  local.executionDisabled = false
  local.delFlag = false
  // 履歴更新区分が"U"に設定する。
  getData(updateKbn)
}

/**
 * 作成者選択画面クリック
 *
 */
function authorSelectClick() {
  // Or26257のダイアログ開閉状態を更新する
  Or26257Logic.state.set({
    uniqueCpId: or26257.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * マスタ他ボタンが押下された場合、マスタ他設定画面を表示する
 *
 */
function masterIconClick() {
  // Or10406のダイアログ開閉状態を更新する
  Or10406Logic.state.set({
    uniqueCpId: or10406.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 子オーガニズムが処理状態を返す
 *
 * @param result - 処理状態
 */
const resultChange = async (result: 'save' | 'add' | '') => {
  switch (result) {
    case 'save':
      await getChangeHistoryInfo(TeX0011Const.DEFAULT.HISTORY_KBN_OPEN, '')
      break
    case 'add':
      await getNewDataInfo()
      break
    default:
      break
  }
}

/**
 * Or21814_1ダイアログを呼び出す、返却値を取得する
 *
 * @param dialogText -ダイアログ内容
 */
function getOr21814_1DialogResult(dialogText: string): Promise<'yes' | ''> {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      dialogText: dialogText,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })
  // 閉じるタイミングで監視を実行
  return new Promise((resolve) => {
    let result: 'yes' | '' = ''
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      (newValue) => {
        if (!newValue) {
          const event = Or21814Logic.event.get(or21814.value.uniqueCpId)
          if (event?.firstBtnClickFlg) {
            result = 'yes'
          }
          Or21814Logic.event.set({
            uniqueCpId: or21814.value.uniqueCpId,
            events: {
              firstBtnClickFlg: false,
              secondBtnClickFlg: false,
              closeBtnClickFlg: false,
            },
          })
          resolve(result)
        }
      },
      { once: true }
    )
  })
}
/**
 * Or21814_2ダイアログを呼び出す、返却値を取得する
 *
 * @param dialogText -ダイアログ内容
 */
function getOr21814_2DialogResult(dialogText: string): Promise<'yes' | 'no' | ''> {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      dialogText: dialogText,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
    },
  })
  // 閉じるタイミングで監視を実行
  return new Promise((resolve) => {
    let result: 'yes' | 'no' | '' = ''
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      (newValue) => {
        if (!newValue) {
          const event = Or21814Logic.event.get(or21814.value.uniqueCpId)
          if (event?.firstBtnClickFlg) {
            result = 'yes'
          }
          if (event?.secondBtnClickFlg) {
            result = 'no'
          }
          Or21814Logic.event.set({
            uniqueCpId: or21814.value.uniqueCpId,
            events: {
              firstBtnClickFlg: false,
              secondBtnClickFlg: false,
              closeBtnClickFlg: false,
            },
          })
          resolve(result)
        }
      },
      { once: true }
    )
  })
}

/**
 * Or21814_1ダイアログを呼び出す、返却値を取得する
 *
 * @param message - ダイアログ内容
 */
const getOr21814_3DialogResult = (message: string) => {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: message,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = 'cancel'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })
        resolve(result)
      },
      { once: true }
    )
  })
}

/**************************************************
 * watch関数
 **************************************************/
/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or11871Logic.event.get(or11871.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.favoriteEventFlg) {
      // お気に入りボタンが押下された場合、お気に入り処理を実行する
      setOr11871Event({ favoriteEventFlg: false })
    }
    if (newValue.saveEventFlg) {
      // 保存ボタンが押下された場合、保存処理を実行する
      save('')
      setOr11871Event({ saveEventFlg: false })
    }
    if (newValue.createEventFlg) {
      //実行無効
      if (local.executionDisabled) {
        return
      }
      // 新規ボタンが押下された場合、新規作成処理を実行する
      void addClickBtn()
      setOr11871Event({ createEventFlg: false })
    }
    if (newValue.deleteEventFlg) {
      //実行無効
      if (local.executionDisabled) {
        return
      }
      // 「削除」押下
      void delClickBtn()
      setOr11871Event({ deleteEventFlg: false })
    }
    if (newValue.printEventFlg) {
      // 実行無効制御
      // if (!deleteFlag.value) {
      //   // 印刷ボタンが押下された場合、印刷設定画面を表示する
      //   void printSettingIconClick()
      //   setOr11871Event({ printEventFlg: false })
      // }
    }
    if (newValue.masterEventFlg) {
      // // マスタ他ボタンが押下された場合、マスタ他設定画面を表示する
      void masterIconClick()
      setOr11871Event({ masterEventFlg: false })
    }
  }
)

/**
 * 計画対象期間履歴チェンジワッチャー
 */
watch(
  () => Or13844Logic.event.get(or13844.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.preBtnClickFlg === false && newValue.nextBtnClickFlg === false) {
      return
    }
    setOr13844Event({ preBtnClickFlg: false, nextBtnClickFlg: false })
    //画面入力データに変更がある場合
    if (isEdit.value) {
      const result = await getOr21814_3DialogResult(t('message.i-cmn-10430'))
      switch (result) {
        //はい：AC003(保存処理)を実行し、処理続き
        case DIALOG_BTN.YES:
          save('')
          return
        //いいえ：処理続き
        case DIALOG_BTN.NO:
          break
        //キャンセル：処理終了
        default:
          break
      }
    }
    // 「計画対象期間-前へアイコンボタン」押下
    if (newValue?.preBtnClickFlg) {
      // 1件目の期間データが表示されている状態
      if (localOneway.planningPeriodSelectOneway.planningPeriodInfo?.periodNo === '1') {
        await getOr21814_1DialogResult(t('message.i-cmn-11262'))
        return
      }
      // 計画期間変更情報取得
      await getPlanPeroidInfo(TeX0011Const.DEFAULT.PERIOD_KBN_PREV)
    }
    // 「計画対象期間-次へアイコンボタン」押下
    if (newValue?.nextBtnClickFlg) {
      // 最終件目の期間データが表示されている状態
      if (
        localOneway.planningPeriodSelectOneway.planningPeriodInfo?.periodNo ===
        localOneway.planningPeriodSelectOneway.planningPeriodInfo?.periodCnt
      ) {
        await getOr21814_1DialogResult(t('message.i-cmn-11263'))
        return
      }
      // 計画期間変更情報取得
      await getPlanPeroidInfo(TeX0011Const.DEFAULT.PERIOD_KBN_NEXT)
    }
  },
  { deep: true }
)
/**
 * 履歴情報履歴チェンジワッチャー
 */
watch(
  () => Or13872Logic.event.get(or13872.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.preBtnClickFlg === false && newValue.nextBtnClickFlg === false) {
      return
    }
    setOr13872Event({ preBtnClickFlg: false, nextBtnClickFlg: false })
    //画面入力データに変更がある場合
    if (isEdit.value) {
      const result = await getOr21814_3DialogResult(t('message.i-cmn-10430'))
      switch (result) {
        //はい：AC003(保存処理)を実行し、処理続き
        case DIALOG_BTN.YES:
          save('')
          return
        //いいえ：処理続き
        case DIALOG_BTN.NO:
          break
        //キャンセル：処理終了
        default:
          break
      }
    }
    // 「履歴-前へアイコンボタン」押下
    if (newValue?.preBtnClickFlg) {
      //1件目の履歴データが表示されている状態
      if (localOneway.hisotrySelectOneway.historyInfo?.krirekiNo === '1') {
        //処理終了にする。
        return
      }
      // 履歴変更情報取得
      await getChangeHistoryInfo(TeX0011Const.DEFAULT.PERIOD_KBN_PREV, UPDATE_KBN.UPDATE)
    }
    // 「履歴-次へアイコンボタン」押下
    if (newValue?.nextBtnClickFlg) {
      // 最終件目の履歴データが表示されている状態
      if (
        localOneway.hisotrySelectOneway.historyInfo?.krirekiNo ===
        localOneway.hisotrySelectOneway.historyInfo?.krirekiCnt
      ) {
        //処理終了にする。
        return
      }
      // 履歴変更情報取得
      await getChangeHistoryInfo(TeX0011Const.DEFAULT.PERIOD_KBN_NEXT, UPDATE_KBN.UPDATE)
    }
  },
  { deep: true }
)

// 計画対象期間選択変更ワッチャー
watch(
  () => OrX0115Logic.event.get(orX0115.value.uniqueCpId),
  async (newValue) => {
    if (newValue) {
      //選択前の対象期間から変更がない場合
      if (newValue.kikanId === localOneway.planningPeriodSelectOneway.planningPeriodInfo!.id) {
        return
      }
      //選択前の対象期間から変更がある場合
      localOneway.planningPeriodSelectOneway.planningPeriodInfo!.id = newValue.kikanId
      await getPlanPeroidInfo(TeX0011Const.DEFAULT.PERIOD_KBN_OPEN)
    }
  }
)

/**
 * 「作成者選択アイコンボタン」押下
 */
watch(
  () => or26257Type.value,
  () => {
    //画面.作成者名に返却情報.選択された職員名（姓）+" "+返却情報.選択された職員名（名）を設定する
    const name = or26257Type.value.shokuin.shokuin1Knj + or26257Type.value.shokuin.shokuin2Knj
    localOneway.authorSelectOneway.name = name
    Or51642Logic.state.set({
      uniqueCpId: or51642.value.uniqueCpId,
      state: {
        param: {
          executeFlag: 'update',
          author: or26257Type.value.shokuin.chkShokuId,
          createDate: local.createDate.value,
        } as Or51642Param,
      },
    })
  }
)

/**
 * 「作成日」変更
 */
watch(
  () => local.createDate,
  () => {
    if (!isInit.value) {
      isInit.value = true
      return
    }
    // 画面.作成日にダイアログで変更後の日付を設定する
    Or51642Logic.state.set({
      uniqueCpId: or51642.value.uniqueCpId,
      state: {
        param: {
          executeFlag: 'update',
          author: or26257Type.value.shokuin.chkShokuId,
          createDate: local.createDate.value,
        } as Or51642Param,
      },
    })
  }
)

/**
 * 事業所名選択変更
 */
watch(
  () => jigyoInfo.value,
  async () => {
    //画面入力データに変更がある場合
    if (isEdit.value) {
      const result = await getOr21814_3DialogResult(t('message.i-cmn-10430'))
      switch (result) {
        //はい：AC003(保存処理)を実行し、処理続き
        case DIALOG_BTN.YES:
          save('')
          break
        //いいえ：処理続き
        case DIALOG_BTN.NO:
          await getInitDataInfo()
          break
        //キャンセル：処理終了
        default:
          break
      }
      return
    }
    await getInitDataInfo()
  }
)
</script>

<template>
  <c-v-sheet class="view d-flex flex-column h-100">
    <!-- ヘーダー 操作ボタンエリア -->
    <g-base-or11871 v-bind="or11871">
      <template #optionMenuItems>
        <c-v-list-item
          :title="t('btn.log')"
          prepend-icon="file_upload"
        >
        </c-v-list-item>
      </template>
      <template #createItems>
        <c-v-list-item
          :title="t('btn.copy')"
          prepend-icon="file_copy"
        />
      </template>
    </g-base-or11871>
    <!-- アクションエリア -->
    <c-v-row
      no-gutters
      class="main-Content d-flex flex-0-1 h-100 overflow-hidden"
    >
      <!-- ナビゲーションエリア -->
      <c-v-col
        cols="auto"
        class="hidden-scroll h-100 pa-2 pr-0"
      >
        <!-- 利用者選択一覧 -->
        <g-base-or00248 v-bind="or00248" />
      </c-v-col>
      <c-v-col class="main-right d-flex flex-column overflow-hidden h-100 pa-2 pl-0">
        <!-- 計画管理詳細 -->
        <c-v-row
          no-gutters
          class="d-flex flex-between pb-2"
        >
          <!-- 事業所 -->
          <c-v-col
            cols="auto"
            class="off-select mr-2"
          >
            <g-base-or41179 v-bind="or41179" />
          </c-v-col>
          <c-v-col
            v-show="plannningPeriodManageFlg === TeX0011Const.DEFAULT.PLANNING_PERIOD_MANAGE"
            cols="auto"
            class="mr-2"
          >
            <!-- 計画対象期間 -->
            <g-custom-or13844
              v-bind="or13844"
              :oneway-model-value="localOneway.planningPeriodSelectOneway"
              @open-btn-click="planningPeriodSelectClick"
            ></g-custom-or13844>
            <!-- 計画対象期間選択画面 -->
            <g-custom-or-x-0115
              v-if="showDialogOrX0115"
              v-bind="orX0115"
              :oneway-model-value="localOneway.orX0115Oneway"
            />
          </c-v-col>
          <c-v-row
            v-show="
              plannningPeriodManageFlg === TeX0011Const.DEFAULT.PLANNING_PERIOD_MANAGE && planCount
            "
            no-gutters
          >
            <!-- 歴史 -->
            <c-v-col cols="2">
              <!-- 履歴 -->
              <g-custom-or13872
                v-bind="or13872"
                :oneway-model-value="localOneway.hisotrySelectOneway"
                @open-btn-click="historySelectClick"
              ></g-custom-or13872>
              <!-- 履歴選択画面 -->
              <g-custom-or-26166
                v-if="showDialogOr26166"
                v-bind="or26166"
                v-model="local.or26166"
                :oneway-model-value="localOneway.or26166Oneway"
                @update:model-value="historySelectChange"
              />
            </c-v-col>
            <!-- 作成者 -->
            <c-v-col cols="auto">
              <g-custom-or13850
                v-bind="or13850_2"
                :oneway-model-value="localOneway.authorSelectOneway"
                class="mr-6"
                @open-btnclick="authorSelectClick"
              ></g-custom-or13850>
              <!-- 職員検索画面 -->
              <g-custom-or-26257
                v-if="showDialogOr26257"
                v-bind="or26257"
                v-model="or26257Type"
                :oneway-model-value="or26257Data"
              />
            </c-v-col>
            <!-- 作成日 -->
            <c-v-col cols="auto">
              <base-mo00020
                v-model="local.createDate"
                :oneway-model-value="localOneway.createDateOneway"
              />
            </c-v-col>
          </c-v-row>
        </c-v-row>
        <c-v-row
          v-show="
            plannningPeriodManageFlg === TeX0011Const.DEFAULT.PLANNING_PERIOD_MANAGE && !planCount
          "
          no-gutters
          class="mb-4 plan-err align-center ga-1"
        >
          <base-at-icon icon="info" />
          {{ t('label.plan-no-data-label2') }}
        </c-v-row>
        <!-- コンテンツエリア -->
        <c-v-row
          no-gutters
          class="flex-1-1 h-100 border-top pt-16"
        >
          <c-v-col cols="12">
            <div class="w-100">
              <g-custom-or51642
                v-bind="or51642"
                :oneway-model-value="localOneway.or51642Oneway"
                @update:model-value="resultChange"
              />
            </div>
          </c-v-col>
        </c-v-row>
        <!-- パネルエリア -->
      </c-v-col>
    </c-v-row>
  </c-v-sheet>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  />
  <g-custom-or-10406
    v-if="showDialogOr10406"
    v-bind="or10406"
    v-model="or10406Type"
    :oneway-model-value="Or10406Data"
  />
</template>

<style lang="scss" scoped>
.off-select :deep(.v-row) {
  flex-direction: column;
}

.plan-err {
  width: 80% !important;
  padding: 8px !important;
  background-color: #ffefef;
  border: 1px solid rgb(var(--v-theme-red-300));
  border-left: 8px solid rgb(var(--v-theme-red-700));
  border-radius: 8px;
  color: red;
  position: relative;
}
.view {
  background-color: transparent;
}

.view {
  background: rgb(var(--v-theme-background));
  .main-Content {
    .main-left {
      max-width: 20%;
    }

    .main-right {
      .middleContent {
        min-height: 0;

        .v-window {
          width: 100%;
        }
      }
    }
  }
}

.createDateOuterClass {
  width: auto !important;
  background: rgb(var(--v-theme-background));

  :deep(.must-badge) {
    margin-left: unset;
  }

  :deep(.v-input__details) {
    display: none;
  }
}
.border-top {
  border-top: 1px rgb(180, 197, 220) solid;
}
:deep(.v-sheet) {
  background-color: transparent !important;
}
:deep(.v-col-3) {
  flex: 0 0 20% !important;
}
</style>
