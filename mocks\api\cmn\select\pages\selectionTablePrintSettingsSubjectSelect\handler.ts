import { HttpResponse } from 'msw'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { ISelectionTablePrintSettingsSubjectSelectInEntity, ISelectionTablePrintSettingsSubjectSelectOutEntity, PrintSubjectHistoryEntity } from '~/repositories/cmn/entities/SelectionTablePrintSettingsEntity.ts'
/**
 * GUI00844_印刷設定
 *
 * @description
 * GUI00844_印刷設定対象一覧情報データを返却する。
 * dataName："selectionTablePrintSettingsSubjectSelect"
 */
export function handler(inEntity: ISelectionTablePrintSettingsSubjectSelectInEntity) {
  const list: PrintSubjectHistoryEntity[] = [] as PrintSubjectHistoryEntity[]
  if(inEntity && inEntity.userList) {
    for(let i =0;i< inEntity.userList.length;i++) {
      if(inEntity.userList[i]) {
        list.push({
          userId: inEntity.userList[i].userId,
          userName: inEntity.userList[i].userName,
          raiId: String(i),
          sc1Id: String(i),
          startYmd: '2025/07/01',
          endYmd: '2025/07/30',
          capType: i % 2 === 0 ? '1' : i % 3 === 0 ? '2' : '3',
          capDateYmd: '2025/07/19',
          capShokuId: String(i),
          result: i % 2 === 0 ? '履歴がありません' : i % 3 === 0 ? '対象外のアセスメント種別です' : ''
        } as PrintSubjectHistoryEntity)
      }
    }
  }
  
  const result: ISelectionTablePrintSettingsSubjectSelectOutEntity = {
    data: {
      printSubjectHistoryList: list
    }
  } as ISelectionTablePrintSettingsSubjectSelectOutEntity

  const responceJson: BaseResponseBody = {
    statusCode: 'success',
    data: {
      ...result.data,
    },
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
