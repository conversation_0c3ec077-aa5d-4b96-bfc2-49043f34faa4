<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or15780Const } from '../Or15780/Or15780.constants'
import { Or15780Logic } from '../Or15780/Or15780.logic'
import { Or15781Const } from '../Or15781/Or15781.constants'
import { Or15781Logic } from '../Or15781/Or15781.logic'
import { Or15783Const } from '../Or15783/Or15783.constants'
import { Or15783Logic } from '../Or15783/Or15783.logic'
import { Or15784Const } from '../Or15784/Or15784.constants'
import { Or15784Logic } from '../Or15784/Or15784.logic'
import { Or15786Const } from '../Or15786/Or15786.constants'
import { Or15786Logic } from '../Or15786/Or15786.logic'
import { Or15787Const } from '../Or15787/Or15787.constants'
import { Or15787Logic } from '../Or15787/Or15787.logic'
import { Or15797Const } from '../Or15797/Or15797.constants'
import { Or15797Logic } from '../Or15797/Or15797.logic'
import { Or15792Const } from '../Or15792/Or15792.constants'
import { Or15792Logic } from '../Or15792/Or15792.logic'
import { Or15793Const } from '../Or15793/Or15793.constants'
import { Or15793Logic } from '../Or15793/Or15793.logic'
import { Or15794Const } from '../Or15794/Or15794.constants'
import { Or15794Logic } from '../Or15794/Or15794.logic'
import { Or16661Const } from '../Or16661/Or16661.constants'
import { Or16661Logic } from '../Or16661/Or16661.logic'
import { Or16648Const } from '../Or16648/Or16648.constants'
import { Or16648Logic } from '../Or16648/Or16648.logic'
import { Or16649Const } from '../Or16649/Or16649.constants'
import { Or16649Logic } from '../Or16649/Or16649.logic'
import { Or15823Const } from '../Or15823/Or15823.constants'
import { Or15823Logic } from '../Or15823/Or15823.logic'
import { Or15822Const } from '../Or15822/Or15822.constants'
import { Or15822Logic } from '../Or15822/Or15822.logic'

import { Or10583Const } from './Or10583.constants'
import type { Or10583StateType } from './Or10583.type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { useSetupChildProps, useScreenOneWayBind, useScreenUtils } from '#imports'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Or10583OnewayType } from '~/types/cmn/business/components/Or10583Type'
import type { Or15783OnewayType, Or15783Type } from '~/types/cmn/business/components/Or15783Type'
import type { Or15780OnewayType, Or15780Type } from '~/types/cmn/business/components/Or15780Type'
import type { Or15784OnewayType, Or15784Type } from '~/types/cmn/business/components/Or15784Type'
import type { Or15781OnewayType, Or15781Type } from '~/types/cmn/business/components/Or15781Type'
import type { Or15786OnewayType, Or15786Type } from '~/types/cmn/business/components/Or15786Type'
import type { Or15787OnewayType, Or15787Type } from '~/types/cmn/business/components/Or15787Type'
import type { Or15797OnewayType, Or15797Type } from '~/types/cmn/business/components/Or15797Type'
import type { Or15792OnewayType, Or15792Type } from '~/types/cmn/business/components/Or15792Type'
import type { Or15793OnewayType, Or15793Type } from '~/types/cmn/business/components/Or15793Type'
import type { Or15794OnewayType, Or15794Type } from '~/types/cmn/business/components/Or15794Type'
import type { Or16648OnewayType, Or16648Type } from '~/types/cmn/business/components/Or16648Type'
import type { Or16649OnewayType, Or16649Type } from '~/types/cmn/business/components/Or16649Type'
import type { Or16661OnewayType, Or16661Type } from '~/types/cmn/business/components/Or16661Type'
import type { Or15823OnewayType, Or15823Type } from '~/types/cmn/business/components/Or15823Type'
import type { Or15822OnewayType, Or15822Type } from '~/types/cmn/business/components/Or15822Type'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo00038Type } from '~/types/business/components/Mo00038Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00040OnewayType, Mo00040Type } from '~/types/business/components/Mo00040Type'
import type { Or07213OnewayType } from '~/types/cmn/business/components/Or07213Type'
import type {
  ComMocPrtiniSSelectInEntity,
  ComMocPrtiniSSelectOutEntity,
  DoubleSidedPrinting,
  CarePlan1,
  CarePlan2,
  WeekPlan,
  DailyCarePlan,
  CarePlanPrint,
} from '~/repositories/cmn/entities/ComMocPrtiniSSelectEntity'
import type { ComMocPrtiniSUpdateInEntity } from '~/repositories/cmn/entities/ComMocPrtiniSUpdateEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'

/**
 * Or10583:有機体:（計画書一括印刷）ダイアログ
 * GUI00936_計画書一括印刷
 *
 * @description
 * （計画書一括印刷）ダイアログ画面が表示される。
 *
 * <AUTHOR>
 */
const { setChildCpBinds } = useScreenUtils()
const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or10583OnewayType
  uniqueCpId: string
}

// 引継情報を取得する
const props = defineProps<Props>()
// 印刷帳票選択
const or15822_1 = ref({ uniqueCpId: '' })

// 計画（１）欄
const or15781_1 = ref({ uniqueCpId: '' })
const or15786_1 = ref({ uniqueCpId: '' })
const or15780_1 = ref({ uniqueCpId: '' })
const or15784_1 = ref({ uniqueCpId: '' })
const or15787_1 = ref({ uniqueCpId: '' })
const or15783_1 = ref({ uniqueCpId: '' })
// 計画（２）欄
const or15781_2 = ref({ uniqueCpId: '' })
const or15786_2 = ref({ uniqueCpId: '' })
const or15797_1 = ref({ uniqueCpId: '' })
const or15792_1 = ref({ uniqueCpId: '' })
const or15793_1 = ref({ uniqueCpId: '' })
const or15794_1 = ref({ uniqueCpId: '' })
const or15783_2 = ref({ uniqueCpId: '' })
// 週間計画欄
const or15781_3 = ref({ uniqueCpId: '' })
const or15786_3 = ref({ uniqueCpId: '' })
const or16661_1 = ref({ uniqueCpId: '' })
const or16648_1 = ref({ uniqueCpId: '' })
const or15792_2 = ref({ uniqueCpId: '' })
const or16649_1 = ref({ uniqueCpId: '' })
const or15794_2 = ref({ uniqueCpId: '' })
const or15783_3 = ref({ uniqueCpId: '' })

// 両面印刷オプション設定
const or15823_1 = ref({ uniqueCpId: '' })

// 日課計画
const or15781_4 = ref({ uniqueCpId: '' })
const or15786_4 = ref({ uniqueCpId: '' })
const or15792_3 = ref({ uniqueCpId: '' })
const or15783_4 = ref({ uniqueCpId: '' })

// 両面印刷オプション設定
const isVisUseSlip = ref(false)

const defaultComponents = {
  // 印刷帳票選択
  or15822: {
    mo00018: [] as Mo00018Type[],
  } as Or15822Type,

  // （計画書（１））敬称変更
  or15781: {
    mo00018: { modelValue: false } as Mo00018Type,
    mo00045: { value: '' } as Mo00045Type,
  } as Or15781Type,

  // （計画書（１））承認欄を印刷する
  or15786: {
    mo00018: { modelValue: false } as Mo00018Type,
  } as Or15786Type,

  // （計画書（１））印刷枠の高さを自動調整する
  or15780: {
    mo00018: { modelValue: false } as Mo00018Type,
  } as Or15780Type,

  // （計画書（１））事業名略称を印刷する
  or15784: {
    mo00018: { modelValue: false } as Mo00018Type,
  } as Or15784Type,

  // （計画書（１））要支援、事業対象者の要介護度を印刷
  or15787: {
    mo00018: { modelValue: false } as Mo00018Type,
  } as Or15787Type,

  // （計画書（１））作成年月日印刷区分
  or15783: { createYmdPrintSet: '' } as Or15783Type,

  // （計画書（２））敬称変更
  or15795: {
    mo00018: { modelValue: false } as Mo00018Type,
    mo00045: { value: '' } as Mo00045Type,
  } as Or15781Type,

  // （計画書（２））承認欄を印刷する
  or15802: {
    mo00018: { modelValue: false } as Mo00018Type,
  } as Or15786Type,

  // （計画書（２））行の先頭で改ページする
  or15797: {
    mo00018: { modelValue: false } as Mo00018Type,
  } as Or15797Type,

  // （計画書（２））印刷する要介護度
  or15792: {
    mo00040: { modelValue: 'label.print-nursing-care-required-1-title' } as Mo00040Type,
  } as Or15792Type,

  // （計画書（２））印刷の高さを最小行に設定
  or15793: {
    mo00018: { modelValue: false } as Mo00018Type,
    mo00038: {
      mo00045: { value: '' } as Mo00045Type,
    } as Mo00038Type,
  } as Or15793Type,

  // （計画書（２））右上に事業所名を印刷
  or15794: {
    mo00018: { modelValue: false } as Mo00018Type,
    mo00040: { modelValue: 'label.sv_jigyo-short-name' } as Mo00040Type,
  } as Or15794Type,

  // （計画書（２））作成年月日印刷区分
  or15800: {
    createYmdPrintSet: '',
  } as Or15783Type,

  // （週間計画）敬称変更
  or16652: {
    mo00018: { modelValue: false } as Mo00018Type,
    mo00045: { value: '' } as Mo00045Type,
  } as Or15781Type,

  // （週間計画）承認欄を印刷する
  or16662: {
    mo00018: { modelValue: false } as Mo00018Type,
  } as Or15786Type,

  // （週間計画）処理年月印刷
  or16661: {
    mo00018ProcessYm: { modelValue: false } as Mo00018Type,
    mo00045ProcessYmPrintInput: { value: '' } as Mo00045Type,
    mo00018TopRight: { modelValue: false } as Mo00018Type,
  } as Or16661Type,

  // （週間計画）モノクロ印刷モード
  or16648: {
    mo00018: { modelValue: false } as Mo00018Type,
  } as Or16648Type,

  // （週間計画）印刷する要介護度
  or16650: {
    mo00040: { modelValue: 'label.print-nursing-care-required-1-title' } as Mo00040Type,
  } as Or15792Type,

  // （週間計画）印刷する週単位以外のサービス
  or16649: {
    mo00040: { modelValue: 'label.print-nonweekly-services-1-title' } as Mo00040Type,
  } as Or16649Type,

  or16651: {
    mo00018: { modelValue: false } as Mo00018Type,
    mo00040: { modelValue: 'label.sv_jigyo-short-name' } as Mo00040Type,
  } as Or15794Type,

  // （週間計画）作成年月日印刷区分
  or16657: {
    createYmdPrintSet: '',
  } as Or15783Type,

  // 両面印刷オプション設定
  or15823: {
    doubleSidedPrintingSet: '',
  } as Or15823Type,

  // （日課計画）敬称変更
  or17335: {
    mo00018: { modelValue: false } as Mo00018Type,
    mo00045: { value: '' } as Mo00045Type,
  } as Or15781Type,

  // （日課計画）承認欄を印刷
  or17342: {
    mo00018: { modelValue: false } as Mo00018Type,
  } as Or15786Type,

  // （日課計画）印刷する要介護度
  or17334: {
    mo00040: { modelValue: 'label.print-nursing-care-required-1-title' } as Mo00040Type,
  } as Or15792Type,

  // （日課計画）作成年月日印刷区分
  or17340: {
    createYmdPrintSet: '',
  } as Or15783Type,
}

const defaultOneway = reactive({
  or15822Oneway: {
    printFormSelectItems: [
      {
        isVisUseSlip: true,
        displayNo: '1',
        carePlanIndex: '1',
        carePlanDisplayName: t('label.full-jp-care-plan1'),
        carePlanSectionNo: '1',
        carePlanFunctionName: t('label.full-jp-care-plan1'),
        mo00018: {
          modelValue: false,
        } as Mo00018Type,
        mo00018OnewayType: {
          name: 'fullJpCarePlan1',
          checkboxLabel: t('label.care-plan1'),
          showItemLabel: false,
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: '',
            labelClass: 'ma-1',
          }),
        } as Mo00018OnewayType,
      },
      {
        isVisUseSlip: true,
        displayNo: '2',
        carePlanIndex: '2',
        carePlanDisplayName: t('label.full-jp-care-plan2'),
        carePlanSectionNo: '2',
        carePlanFunctionName: t('label.full-jp-care-plan2'),
        checkItem: {
          modelValue: false,
        } as Mo00018Type,
        mo00018OnewayType: {
          name: 'fullJpCarePlan2',
          checkboxLabel: t('label.care-plan2'),
          showItemLabel: false,
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: '',
            labelClass: 'ma-1',
          }),
        } as Mo00018OnewayType,
      },
      {
        isVisUseSlip: true,
        displayNo: '3',
        carePlanIndex: '5',
        carePlanDisplayName: t('label.week-plan'),
        carePlanSectionNo: '3',
        carePlanFunctionName: t('label.week-plan'),
        checkItem: {
          modelValue: false,
        } as Mo00018Type,
        mo00018OnewayType: {
          name: 'weekPlanData',
          checkboxLabel: t('label.week-plan'),
          showItemLabel: false,
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: '',
            labelClass: 'ma-1',
          }),
        } as Mo00018OnewayType,
      },
      {
        isVisUseSlip: true,
        displayNo: '4',
        carePlanIndex: '6',
        carePlanDisplayName: t('label.daily-care-plan'),
        carePlanSectionNo: '4',
        carePlanFunctionName: t('label.daily-care-plan'),
        checkItem: {
          modelValue: false,
        } as Mo00018Type,
        mo00018OnewayType: {
          name: 'dailyCarePlanData',
          checkboxLabel: t('label.daily-care-plan'),
          showItemLabel: false,
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: '',
            labelClass: 'ma-1',
          }),
        } as Mo00018OnewayType,
      },
      {
        isVisUseSlip: false,
        displayNo: '5',
        carePlanIndex: '7',
        carePlanDisplayName: t('label.use-slip'),
        carePlanSectionNo: '5',
        carePlanFunctionName: t('label.use-slip'),
        checkItem: {
          modelValue: false,
        } as Mo00018Type,
        mo00018OnewayType: {
          name: 'useSlip',
          checkboxLabel: t('label.use-slip'),
          showItemLabel: false,
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: '',
            labelClass: 'ma-1',
          }),
        } as Mo00018OnewayType,
      },
      {
        isVisUseSlip: false,
        displayNo: '6',
        carePlanIndex: '8',
        carePlanDisplayName: t('label.use-slip-type'),
        carePlanSectionNo: '6',
        carePlanFunctionName: t('label.use-slip-type'),
        checkItem: {
          modelValue: false,
        } as Mo00018Type,
        mo00018OnewayType: {
          name: 'useSlipType',
          checkboxLabel: t('label.use-slip-type'),
          showItemLabel: false,
          isVerticalLabel: false,
          customClass: new CustomClass({
            outerClass: '',
            labelClass: 'ma-1',
          }),
        } as Mo00018OnewayType,
      },
    ],
  } as Or15822OnewayType,
  or15781Oneway: {
    mo00018: { modelValue: false },
    mo00045: { value: '' },
  } as Or15781OnewayType,
  or15786Oneway: {
    mo00018: { modelValue: false },
  } as Or15786OnewayType,
  or15780Oneway: { mo00018: { modelValue: false } } as Or15780OnewayType,
  or15784Oneway: { mo00018: { modelValue: false } } as Or15784OnewayType,
  or15787Oneway: { mo00018: { modelValue: false } } as Or15787OnewayType,
  or15783Oneway: {
    radioItems: [
      { label: t('label.printing'), value: '1' },
      { label: t('label.space'), value: '2'},
      { label: t('label.no-printing'), value: '3' },
    ],
  } as Or15783OnewayType,
  or15797Oneway: { mo00018: { modelValue: false } } as Or15797OnewayType,
  or15792Oneway: {
    mo00040Oneway: {
      itemLabel: 'Or15792',
      showItemLabel: false,
      isRequired: false,
      items: [
        { value: 1, title: 'label.print-nursing-care-required-1-title' },
        { value: 2, title: 'label.print-nursing-care-required-2-title' },
        { value: 3, title: 'label.print-nursing-care-required-3-title' },
        { value: 4, title: 'label.print-nursing-care-required-4-title' },
      ],
      customClass: new CustomClass({
        outerClass: 'mr-2',
        labelClass: 'ma-1',
        itemStyle: 'width: 240px;',
      }),
      hideDetails: true,
    } as Mo00040OnewayType,
  } as Or15792OnewayType,
  or15793Oneway: { mo00018: { modelValue: false } } as Or15793OnewayType,
  or15794Oneway: {
    mo00040Oneway: {
      itemLabel: 'Or15792',
      showItemLabel: false,
      isRequired: false,
      items: [
        { value: 1, title: 'label.sv_jigyo-short-name' },
        { value: 2, title: 'label.sv_jigyo-full-name' },
      ],
      customClass: new CustomClass({
        itemStyle: 'width: 160px;',
      }),
      hideDetails: true,
    } as Mo00040OnewayType,
  } as Or15794OnewayType,
  or16661Oneway: {
    mo00018ProcessYm: { modelValue: false },
    mo00045ProcessYmPrintInput: { value: '' },
    mo00018TopRight: { modelValue: false },
  } as Or16661OnewayType,
  or16648Oneway: { mo00018: { modelValue: false } } as Or16648OnewayType,
  or16649Oneway: {
    mo00040Oneway: {
      itemLabel: 'Or16649',
      showItemLabel: false,
      isRequired: false,
      items: [
        { value: 1, title: 'label.print-nonweekly-services-1-title' },
        { value: 2, title: 'label.print-nonweekly-services-2-title' },
        { value: 3, title: 'label.print-nonweekly-services-3-title' },
      ],
      customClass: new CustomClass({
        outerClass: 'mr-2',
        labelClass: 'ma-1',
        itemStyle: 'width: 240px;',
      }),
      hideDetails: true,
    } as Mo00040OnewayType,
  } as Or16649OnewayType,
  // 両面印刷オプション設定
  or15823Oneway: {
    radioItems: [
      { label: t('label.user-unit'), value: '1'},
      { label: t('label.care-plan-unit'), value: '2'},
    ],
  } as Or15823OnewayType,
  or07213Oneway: { printParm: '' } as Or07213OnewayType,
})

const localComponents = reactive({
  or15822: {
    ...defaultComponents.or15822,
  } as Or15822Type,
  or15781: {
    ...defaultComponents.or15781,
  } as Or15781Type,
  or15786: {
    ...defaultComponents.or15786,
  } as Or15786Type,
  or15780: {
    ...defaultComponents.or15780,
  } as Or15780Type,
  or15784: {
    ...defaultComponents.or15784,
  } as Or15784Type,
  or15787: {
    ...defaultComponents.or15787,
  } as Or15787Type,
  or15783: {
    ...defaultComponents.or15783,
  } as Or15783Type,

  or15795: {
    ...defaultComponents.or15781,
  } as Or15781Type,
  or15802: {
    ...defaultComponents.or15786,
  } as Or15786Type,
  or15797: {
    ...defaultComponents.or15797,
  } as Or15797Type,
  or15792: {
    ...defaultComponents.or15792,
  } as Or15792Type,
  or15793: {
    ...defaultComponents.or15793,
  } as Or15793Type,
  or15794: {
    ...defaultComponents.or15794,
  } as Or15794Type,
  or15800: {
    ...defaultComponents.or15783,
  } as Or15783Type,

  or16652: {
    ...defaultComponents.or15781,
  } as Or15781Type,
  or16662: {
    ...defaultComponents.or15786,
  } as Or15786Type,
  or16661: {
    ...defaultComponents.or16661,
  } as Or16661Type,
  or16648: {
    ...defaultComponents.or16648,
  } as Or16648Type,
  or16650: {
    ...defaultComponents.or15792,
  } as Or15792Type,
  or16649: {
    ...defaultComponents.or16649,
  } as Or16649Type,
  or16651: {
    ...defaultComponents.or15794,
  } as Or15794Type,
  or16657: {
    ...defaultComponents.or15783,
  } as Or15783Type,
  or15823: {
    ...defaultComponents.or15823,
  } as Or15823Type,

  or17335: {
    ...defaultComponents.or15781,
  } as Or15781Type,
  or17342: {
    ...defaultComponents.or15786,
  } as Or15786Type,
  or17334: {
    ...defaultComponents.or15792,
  } as Or15792Type,
  or17340: {
    ...defaultComponents.or15783,
  } as Or15783Type,
})

const localOneway = reactive({
  paramp: '',
  modifiedCnt: '',
  carePlan1Data: {
    modifiedCnt: '',
  },
  carePlan2Data: {
    modifiedCnt: '',
  },
  weekPlanData: {
    modifiedCnt: '',
  },
  dailyCarePlanData: {
    modifiedCnt: '',
  },
  or10583: {
    ...props.onewayModelValue,
  },

  // 印刷帳票選択タイトル
  mo00615Sevenway: {
    itemLabelFontWeight: 'bold',
    itemLabel: t('label.print-form-select'),
    customClass: new CustomClass({
      outerClass: 'mr-0',
      labelClass: 'ma-1',
      outerStyle: 'background: rgba(0, 0, 0, 0);',
    }),
  } as Mo00615OnewayType,
  or15822Oneway: {
    ...defaultOneway.or15822Oneway,
  } as Or15822OnewayType,

  // 計画書（１）印刷設定タイトル
  mo00615Oneway: {
    itemLabelFontWeight: 'bold',
    itemLabel: t('label.full-jp-care-plan1'),
    customClass: new CustomClass({
      outerClass: 'mr-0',
      labelClass: 'ma-1',
      outerStyle: 'background: rgba(0, 0, 0, 0);',
    }),
  } as Mo00615OnewayType,
  or15781Oneway: {
    ...defaultOneway.or15781Oneway,
  } as Or15781OnewayType,
  or15786Oneway: {
    ...defaultOneway.or15786Oneway,
  } as Or15786OnewayType,
  or15780Oneway: {
    ...defaultOneway.or15780Oneway,
  } as Or15780OnewayType,
  or15784Oneway: {
    ...defaultOneway.or15784Oneway,
  } as Or15784OnewayType,
  or15787Oneway: {
    ...defaultOneway.or15787Oneway,
  } as Or15787OnewayType,
  or15783Oneway: {
    ...defaultOneway.or15783Oneway,
  } as Or15783OnewayType,

  // 計画書（２）印刷設定タイトル
  mo00615Twoway: {
    itemLabelFontWeight: 'bold',
    itemLabel: t('label.full-jp-care-plan2'),
    customClass: new CustomClass({
      outerClass: 'mr-0',
      labelClass: 'ma-1',
      outerStyle: 'background: rgba(0, 0, 0, 0);',
    }),
  } as Mo00615OnewayType,
  or15795Oneway: {
    ...defaultOneway.or15781Oneway,
  } as Or15781OnewayType,
  or15802Oneway: {
    ...defaultOneway.or15786Oneway,
  } as Or15786OnewayType,
  or15797Oneway: {
    ...defaultOneway.or15797Oneway,
  } as Or15797OnewayType,
  or15792Oneway: {
    ...defaultOneway.or15792Oneway,
  } as Or15792OnewayType,
  or15793Oneway: {
    ...defaultOneway.or15793Oneway,
  } as Or15793OnewayType,
  or15794Oneway: {
    ...defaultOneway.or15794Oneway,
  } as Or15794OnewayType,
  or15800Oneway: {
    ...defaultOneway.or15783Oneway,
  } as Or15783OnewayType,

  // 週間計画印刷設定タイトル
  mo00615Threeway: {
    itemLabelFontWeight: 'bold',
    itemLabel: t('label.week-plan'),
    customClass: new CustomClass({
      outerClass: 'mr-0',
      labelClass: 'ma-1',
      outerStyle: 'background: rgba(0, 0, 0, 0);',
    }),
  } as Mo00615OnewayType,
  or16652Oneway: {
    ...defaultOneway.or15781Oneway,
  } as Or15781OnewayType,
  or16662Oneway: {
    ...defaultOneway.or15786Oneway,
  } as Or15786OnewayType,
  or16661Oneway: {
    ...defaultOneway.or16661Oneway,
  } as Or16661OnewayType,
  or16648Oneway: {
    ...defaultOneway.or16648Oneway,
  } as Or16648OnewayType,
  or16650Oneway: {
    ...defaultOneway.or15792Oneway,
  } as Or15792OnewayType,
  or16649Oneway: {
    ...defaultOneway.or16649Oneway,
  } as Or16649OnewayType,
  or16651Oneway: {
    ...defaultOneway.or15794Oneway,
  } as Or15794OnewayType,
  or16657Oneway: {
    ...defaultOneway.or15783Oneway,
  } as Or15783OnewayType,

  // 両面印刷オプション設定タイトル
  mo00615Fourway: {
    itemLabelFontWeight: 'bold',
    itemLabel: t('label.double-sided-printing-set'),
    customClass: new CustomClass({
      outerClass: 'mr-0',
      labelClass: 'ma-1',
      outerStyle: 'background: rgba(0, 0, 0, 0);',
    }),
  } as Mo00615OnewayType,

  // 日課計画設定タイトル
  mo00615Fiveway: {
    itemLabelFontWeight: 'bold',
    itemLabel: t('label.daily-care-plan'),
    customClass: new CustomClass({
      outerClass: 'mr-0',
      labelClass: 'ma-1',
      outerStyle: 'background: rgba(0, 0, 0, 0);',
    }),
  } as Mo00615OnewayType,

  or17335Oneway: {
    ...defaultOneway.or15781Oneway,
  } as Or15781OnewayType,
  or17342Oneway: {
    ...defaultOneway.or15786Oneway,
  } as Or15786OnewayType,
  or17334Oneway: {
    ...defaultOneway.or15792Oneway,
  } as Or15792OnewayType,
  or17340Oneway: {
    ...defaultOneway.or15783Oneway,
  } as Or15783OnewayType,
  // 利用票・別表
  mo00615Sixway: {
    itemLabelFontWeight: 'bold',
    itemLabel: t('label.use-slip-or-other-slip'),
    customClass: new CustomClass({
      outerClass: 'mr-0',
      labelClass: 'ma-1',
      outerStyle: 'background: rgba(0, 0, 0, 0);',
    }),
  } as Mo00615OnewayType,
  or15823Oneway: {
    ...defaultOneway.or15823Oneway,
  } as Or15823OnewayType,
  or07213Oneway: {
    ...defaultOneway.or07213Oneway,
  } as Or07213OnewayType,

  // 閉じるコンポーネント,
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 印刷コンポーネント
  mo00609UpdateOneway: {
    btnLabel: t('btn.print'),
  } as Mo00609OnewayType,
  // 印刷設定ダイアログ
  mo00024Oneway: {
    maxWidth: '1348px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or10583',
      toolbarTitle: t('label.print-set'),
      toolbarName: 'Or10583ToolBar',
      font: '20px',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
      cardTextClass: 'card-text pa-3',
    },
  } as Mo00024OnewayType,
})

/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or10583Const.DEFAULT.IS_OPEN,
})
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or10583StateType>({
  cpId: Or10583Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or10583Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or15822Const.CP_ID(1)]: or15822_1.value,

  [Or15781Const.CP_ID(1)]: or15781_1.value,
  [Or15786Const.CP_ID(1)]: or15786_1.value,
  [Or15780Const.CP_ID(1)]: or15780_1.value,
  [Or15784Const.CP_ID(1)]: or15784_1.value,
  [Or15787Const.CP_ID(1)]: or15787_1.value,
  [Or15783Const.CP_ID(1)]: or15783_1.value,

  [Or15781Const.CP_ID(2)]: or15781_2.value,
  [Or15786Const.CP_ID(2)]: or15786_2.value,
  [Or15797Const.CP_ID(1)]: or15797_1.value,
  [Or15792Const.CP_ID(1)]: or15792_1.value,
  [Or15793Const.CP_ID(1)]: or15793_1.value,
  [Or15794Const.CP_ID(1)]: or15794_1.value,
  [Or15783Const.CP_ID(2)]: or15783_2.value,

  [Or15781Const.CP_ID(3)]: or15781_3.value,
  [Or15786Const.CP_ID(3)]: or15786_3.value,
  [Or16661Const.CP_ID(1)]: or16661_1.value,
  [Or16648Const.CP_ID(1)]: or16648_1.value,
  [Or15792Const.CP_ID(2)]: or15792_2.value,
  [Or16649Const.CP_ID(1)]: or16649_1.value,
  [Or15794Const.CP_ID(2)]: or15794_2.value,
  [Or15783Const.CP_ID(3)]: or15783_3.value,

  [Or15823Const.CP_ID(1)]: or15823_1.value,

  [Or15781Const.CP_ID(4)]: or15781_4.value,
  [Or15786Const.CP_ID(4)]: or15786_4.value,
  [Or15792Const.CP_ID(3)]: or15792_3.value,
  [Or15783Const.CP_ID(4)]: or15783_4.value,
})

onMounted(async () => {
  await initCodes()
  // 利用票・別表設定の表示有無を判定
  if (
    Or10583Const.OFFICE_CD_LIST.includes(props.onewayModelValue.carePlanPrintAllInData.jigyoTypeCd)
  ) {
    isVisUseSlip.value = true
  } else {
    isVisUseSlip.value = false
  }

  const inputData: ComMocPrtiniSSelectInEntity = {
    houjinId: props.onewayModelValue.carePlanPrintAllInData.legalPersonId,
    shisetuId: props.onewayModelValue.carePlanPrintAllInData.shisetuId,
    svJigyoId: props.onewayModelValue.carePlanPrintAllInData.officeData.officeId,
    cksFlg: props.onewayModelValue.carePlanPrintAllInData.carePlanStyle,
    shokuId: props.onewayModelValue.carePlanPrintAllInData.employeeId,
    svJigyoCd: props.onewayModelValue.carePlanPrintAllInData.jigyoTypeCd,
  }
  const ret: ComMocPrtiniSSelectOutEntity = await ScreenRepository.select(
    'comMocPrtiniSSelect',
    inputData
  )
  console.log('jigyoTypeCd======', props.onewayModelValue.carePlanPrintAllInData.jigyoTypeCd)
  // 計画書印刷
  localOneway.or15822Oneway.printFormSelectItems = []
  localComponents.or15822.mo00018 = []
  if (ret.data && ret.data.carePlanPrintDataList?.length > 0) {
    ret.data.carePlanPrintDataList.forEach((item) => {
      localOneway.or15822Oneway.printFormSelectItems.push({
        isVisUseSlip: Or10583Const.OFFICE_CD_LIST.includes(
          props.onewayModelValue.carePlanPrintAllInData.jigyoTypeCd
        ),
        displayNo: item.carePlanIndex,
        carePlanIndex: item.carePlanIndex,
        carePlanDisplayName: item.carePlanDisplayName,
        carePlanSectionNo: item.carePlanSectionNo,
        carePlanFunctionName: item.carePlanKinouName,
        mo00018: {
          modelValue: item.carePlanCheck === '1',
        } as Mo00018Type,
        mo00018OnewayType: {
          name: 'fullJpCarePlan' + item.carePlanIndex,
          checkboxLabel: item.carePlanDisplayName,
          showItemLabel: false,
          isVerticalLabel: item.isVisible === '1'? true:false,
          disabled: item.isLock === '1'? true:false,
          customClass: new CustomClass({
            outerClass: '',
            labelClass: 'ma-1',
          }),
        } as Mo00018OnewayType,
      })
      localComponents.or15822.mo00018.push({
        modelValue: item.carePlanCheck === '1',
      })
    })
  }
  // 計画書（１）
  localComponents.or15781 = {
    mo00018: {
      modelValue: ret.data.carePlan1Data.param03 === '1',
    },
    mo00045: { value: ret.data.carePlan1Data.param04 },
  }
  localComponents.or15786 = { mo00018: { modelValue: ret.data.carePlan1Data.param05 === '1' } }
  localComponents.or15780 = { mo00018: { modelValue: ret.data.carePlan1Data.param06 === '1' } }
  localComponents.or15784 = { mo00018: { modelValue: ret.data.carePlan1Data.param07 === '1' } }
  localComponents.or15787 = { mo00018: { modelValue: ret.data.carePlan1Data.param08 === '1' } }
  localComponents.or15783 = { createYmdPrintSet: ret.data.carePlan1Data.param09 }
  localOneway.carePlan1Data.modifiedCnt = ret.data.carePlan1Data.modifiedCnt
  // 計画書（２）
  localComponents.or15795 = {
    mo00018: { modelValue: ret.data.carePlan2Data.param03 === '1' },
    mo00045: { value: ret.data.carePlan2Data.param04 },
  }
  localComponents.or15802 = { mo00018: { modelValue: ret.data.carePlan2Data.param05 === '1' } }
  localComponents.or15797 = { mo00018: { modelValue: ret.data.carePlan2Data.param10 === '1' } }
  localComponents.or15792 = { mo00040: { modelValue: ret.data.carePlan2Data.param09 } }
  localComponents.or15793 = {
    mo00018: { modelValue: ret.data.carePlan2Data.param06 === '1' },
    mo00038: { mo00045: { value: ret.data.carePlan2Data.param12 } },
  }
  localComponents.or15794 = {
    mo00018: { modelValue: ret.data.carePlan2Data.param30 === '1' },
    mo00040: { modelValue: ret.data.carePlan2Data.param31 },
  }
  localComponents.or15800 = { createYmdPrintSet: ret.data.carePlan2Data.param13 }
  localOneway.carePlan2Data.modifiedCnt = ret.data.carePlan2Data.modifiedCnt
  // 週間計画
  localComponents.or16652 = {
    mo00018: { modelValue: ret.data.weekPlanData.param03 === '1' },
    mo00045: { value: ret.data.weekPlanData.param04 },
  }
  localComponents.or16662 = { mo00018: { modelValue: ret.data.weekPlanData.param05 === '1' } }
  localComponents.or16661 = {
    mo00018ProcessYm: { modelValue: ret.data.weekPlanData.param07 === '1' },
    mo00045ProcessYmPrintInput: { value: ret.data.weekPlanData.param09 },
    mo00018TopRight: { modelValue: ret.data.weekPlanData.param08 === '1' },
  }
  localComponents.or16648 = { mo00018: { modelValue: ret.data.weekPlanData.param06 === '1' } }
  localComponents.or16650 = { mo00040: { modelValue: ret.data.weekPlanData.param10 } }
  localComponents.or16649 = { mo00040: { modelValue: ret.data.weekPlanData.param11 } }
  localComponents.or16651 = {
    mo00018: { modelValue: ret.data.weekPlanData.param30 === '1' },
    mo00040: { modelValue: ret.data.weekPlanData.param31 },
  }
  localComponents.or16657 = { createYmdPrintSet: ret.data.weekPlanData.param13 }
  localOneway.weekPlanData.modifiedCnt = ret.data.weekPlanData.modifiedCnt
  // 両面印刷オプション設定
  localComponents.or15823 = { doubleSidedPrintingSet: ret.data.doubleSidedPrintingData.param04 }
  // 日課計画
  localComponents.or17335 = {
    mo00018: { modelValue: ret.data.dailyCarePlanData.param03 === '1' },
    mo00045: { value: ret.data.dailyCarePlanData.param04 },
  }
  localComponents.or17342 = { mo00018: { modelValue: ret.data.dailyCarePlanData.param05 === '1' } }
  localComponents.or17334 = { mo00040: { modelValue: ret.data.dailyCarePlanData.param07 } }
  localComponents.or17340 = {
    createYmdPrintSet: ret.data.dailyCarePlanData.param13,
  }
  localOneway.dailyCarePlanData.modifiedCnt = ret.data.dailyCarePlanData.modifiedCnt
  localOneway.paramp = ret.data.paramp
  localOneway.modifiedCnt = ret.data.modifiedCnt
  setChildCpBinds(props.uniqueCpId, {
    Or15822_1: {
      twoWayValue: {
        mo00018: localComponents.or15822.mo00018,
      },
    },
    Or15781_1: {
      twoWayValue: {
        mo00018: { modelValue: localComponents.or15781.mo00018.modelValue },
        mo00045: { modelValue: localComponents.or15781.mo00045?.value },
      },
    },
    Or15786_1: {
      twoWayValue: {
        mo00018: { modelValue: localComponents.or15786.mo00018.modelValue },
      },
    },
    Or15780_1: {
      twoWayValue: {
        mo00018: { modelValue: localComponents.or15780.mo00018.modelValue },
      },
    },
    Or15784_1: {
      twoWayValue: {
        mo00018: { modelValue: localComponents.or15784.mo00018.modelValue },
      },
    },
    Or15787_1: {
      twoWayValue: {
        mo00018: { modelValue: localComponents.or15787.mo00018.modelValue },
      },
    },
    Or15783_1: {
      twoWayValue: {
        createYmdPrintSet: localComponents.or15783.createYmdPrintSet,
      },
    },
    Or15781_2: {
      twoWayValue: {
        mo00018: { modelValue: localComponents.or15795.mo00018.modelValue },
        mo00045: { modelValue: localComponents.or15795.mo00045?.value },
      },
    },
    Or15786_2: {
      twoWayValue: {
        mo00018: { modelValue: localComponents.or15802.mo00018.modelValue },
      },
    },
    Or15797_1: {
      twoWayValue: {
        mo00018: { modelValue: localComponents.or15797.mo00018.modelValue },
      },
    },
    Or15792_1: {
      twoWayValue: {
        mo00040: { modelValue: localComponents.or15792.mo00040.modelValue },
      },
    },
    Or15793_1: {
      twoWayValue: {
        mo00018: { modelValue: localComponents.or15793.mo00018.modelValue },
        mo00038: {
          mo00045: localComponents.or15793.mo00038?.mo00045,
        },
      },
    },
    Or15794_1: {
      twoWayValue: {
        mo00018: { modelValue: localComponents.or15794.mo00018.modelValue },
        mo00040: { modelValue: localComponents.or15794.mo00040.modelValue },
      },
    },
    Or15783_2: {
      twoWayValue: {
        createYmdPrintSet: localComponents.or15800.createYmdPrintSet,
      },
    },

    Or15781_3: {
      twoWayValue: {
        mo00018: { modelValue: localComponents.or16652.mo00018.modelValue },
        mo00045: { modelValue: localComponents.or16652.mo00045?.value },
      },
    },
    Or15786_3: {
      twoWayValue: {
        mo00018: { modelValue: localComponents.or16662.mo00018.modelValue },
      },
    },
    Or16661_1: {
      twoWayValue: {
        mo00018ProcessYm: { modelValue: localComponents.or16661.mo00018ProcessYm.modelValue },
        mo00045ProcessYmPrintInput: {
          value: localComponents.or16661.mo00045ProcessYmPrintInput?.value,
        },
        mo00018TopRight: { modelValue: localComponents.or16661.mo00018TopRight.modelValue },
      },
    },
    Or16648_1: {
      twoWayValue: {
        mo00018: { modelValue: localComponents.or16648.mo00018.modelValue },
      },
    },
    Or15792_2: {
      twoWayValue: {
        mo00040: { modelValue: localComponents.or16650.mo00040.modelValue },
      },
    },
    Or16649_1: {
      twoWayValue: {
        mo00040: { modelValue: localComponents.or16649.mo00040.modelValue },
      },
    },
    Or15794_2: {
      twoWayValue: {
        mo00018: { modelValue: localComponents.or16651.mo00018.modelValue },
        mo00040: { modelValue: localComponents.or16651.mo00040.modelValue },
      },
    },
    Or15783_3: {
      twoWayValue: {
        createYmdPrintSet: localComponents.or16657.createYmdPrintSet,
      },
    },
    Or15823_1: {
      twoWayValue: {
        doubleSidedPrintingSet: localComponents.or15823.doubleSidedPrintingSet,
      },
    },

    Or15781_4: {
      twoWayValue: {
        mo00018: { modelValue: localComponents.or17335.mo00018.modelValue },
        mo00045: { modelValue: localComponents.or17335.mo00045?.value },
      },
    },
    Or15786_4: {
      twoWayValue: {
        mo00018: { modelValue: localComponents.or17342.mo00018.modelValue },
      },
    },
    Or15792_3: {
      twoWayValue: {
        mo00040: { modelValue: localComponents.or17334.mo00040.modelValue },
      },
    },
    Or15783_4: {
      twoWayValue: {
        createYmdPrintSet: localComponents.or17340.createYmdPrintSet,
      },
    },
  })
})

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 印刷する要介護度（新書式）
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SELECT_CD_KBN_KAIGO },
    // 事業所名の表示
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SELECT_CD_KBN_JIGYOSYO },
    // 印刷する週単位以外のサービス
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SELECT_CD_KBN_SERVICE },
    // 両面印刷オプション
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SELECT_CD_KBN_OPTION },
    // 作成年月日印刷出力設定
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SELECT_CD_KBN_YMD_PRINT_SET },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  // コード取得
  // 作成年月日印刷出力設定
  localOneway.or15783Oneway.radioItems = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_SELECT_CD_KBN_YMD_PRINT_SET
  )
  // 印刷する要介護度（新書式）
  localOneway.or15792Oneway.mo00040Oneway.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_SELECT_CD_KBN_KAIGO
  ).map((item) => {
    return { value: item.value, title: item.label }
  })
  // 事業所名の表示
  localOneway.or15794Oneway.mo00040Oneway.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_SELECT_CD_KBN_JIGYOSYO
  ).map((item) => {
    return { value: item.value, title: item.label }
  })
  localOneway.or16651Oneway.mo00040Oneway.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_SELECT_CD_KBN_JIGYOSYO
  ).map((item) => {
    return { value: item.value, title: item.label }
  })
  // 印刷する週単位以外のサービス
  localOneway.or16649Oneway.mo00040Oneway.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_SELECT_CD_KBN_SERVICE
  ).map((item) => {
    return { value: item.value, title: item.label }
  })
  // 両面印刷オプション
  localOneway.or15823Oneway.radioItems = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_SELECT_CD_KBN_OPTION
  )
}

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
async function close() {
  await save()
  setState({ isOpen: false })
}

/**
 * 「印刷」ボタン押下
 */
async function print() {
  await save()
  // GUI04507_印刷設定画面を開く TODO
}

/**
 * 計画書一括印刷情報保存する
 */
async function save() {
  const param: ComMocPrtiniSUpdateInEntity = {
    houjinId: props.onewayModelValue.carePlanPrintAllInData.legalPersonId,
    shisetuId: props.onewayModelValue.carePlanPrintAllInData.shisetuId,
    svJigyoId: props.onewayModelValue.carePlanPrintAllInData.officeData.officeId,
    cksFlg: props.onewayModelValue.carePlanPrintAllInData.carePlanStyle,
    shokuId: props.onewayModelValue.carePlanPrintAllInData.employeeId,
    JigyoCd: props.onewayModelValue.carePlanPrintAllInData.jigyoTypeCd,
    paramp: localOneway.paramp,
    modifiedCnt: localOneway.modifiedCnt,
    carePlanPrintDataList: [] as CarePlanPrint[],
    doubleSidedPrintingData: {
      param04: Or15823Logic.data.get(or15823_1.value.uniqueCpId)?.doubleSidedPrintingSet,
    } as DoubleSidedPrinting,
    carePlan1Data: {
      param03: Or15781Logic.data.get(or15781_1.value.uniqueCpId)?.mo00018.modelValue ? '1' : '0',
      param04: Or15781Logic.data.get(or15781_1.value.uniqueCpId)?.mo00045?.value,
      param05: Or15786Logic.data.get(or15786_1.value.uniqueCpId)?.mo00018.modelValue ? '1' : '0',
      param06: Or15780Logic.data.get(or15780_1.value.uniqueCpId)?.mo00018.modelValue ? '1' : '0',
      param07: Or15784Logic.data.get(or15784_1.value.uniqueCpId)?.mo00018.modelValue ? '1' : '0',
      param08: Or15787Logic.data.get(or15787_1.value.uniqueCpId)?.mo00018.modelValue ? '1' : '0',
      param09: Or15783Logic.data.get(or15783_1.value.uniqueCpId)?.createYmdPrintSet,
      modifiedCnt: localOneway.carePlan1Data.modifiedCnt,
    } as CarePlan1,
    carePlan2Data: {
      param03: Or15781Logic.data.get(or15781_2.value.uniqueCpId)?.mo00018.modelValue ? '1' : '0',
      param04: Or15781Logic.data.get(or15781_2.value.uniqueCpId)?.mo00045?.value,
      param05: Or15786Logic.data.get(or15786_2.value.uniqueCpId)?.mo00018.modelValue ? '1' : '0',
      param06: Or15793Logic.data.get(or15793_1.value.uniqueCpId)?.mo00018.modelValue ? '1' : '0',
      param12: Or15793Logic.data.get(or15793_1.value.uniqueCpId)?.mo00038?.mo00045.value,
      param10: Or15797Logic.data.get(or15797_1.value.uniqueCpId)?.mo00018.modelValue ? '1' : '0',
      param09: Or15792Logic.data.get(or15792_1.value.uniqueCpId)?.mo00040.modelValue,
      param30: Or15794Logic.data.get(or15794_1.value.uniqueCpId)?.mo00018.modelValue ? '1' : '0',
      param31: Or15794Logic.data.get(or15794_1.value.uniqueCpId)?.mo00040.modelValue,
      param13: Or15783Logic.data.get(or15783_2.value.uniqueCpId)?.createYmdPrintSet,
      modifiedCnt: localOneway.carePlan2Data.modifiedCnt,
    } as CarePlan2,
    weekPlanData: {
      param03: Or15781Logic.data.get(or15781_3.value.uniqueCpId)?.mo00018.modelValue ? '1' : '0',
      param04: Or15781Logic.data.get(or15781_3.value.uniqueCpId)?.mo00045?.value,
      param05: Or15786Logic.data.get(or15786_3.value.uniqueCpId)?.mo00018.modelValue ? '1' : '0',
      param06: Or16648Logic.data.get(or16648_1.value.uniqueCpId)?.mo00018.modelValue ? '1' : '0',
      param07: Or16661Logic.data.get(or16661_1.value.uniqueCpId)?.mo00018ProcessYm.modelValue
        ? '1'
        : '0',
      param08: Or16661Logic.data.get(or16661_1.value.uniqueCpId)?.mo00018TopRight.modelValue? '1' : '0',
      param09: Or16661Logic.data.get(or16661_1.value.uniqueCpId)?.mo00045ProcessYmPrintInput?.value,
      param10: Or15792Logic.data.get(or15792_2.value.uniqueCpId)?.mo00040.modelValue,
      param11: Or16649Logic.data.get(or16649_1.value.uniqueCpId)?.mo00040.modelValue,
      param13: Or15783Logic.data.get(or15783_3.value.uniqueCpId)?.createYmdPrintSet,
      param30: Or15794Logic.data.get(or15794_2.value.uniqueCpId)?.mo00018.modelValue ? '1' : '0',
      param31: Or15794Logic.data.get(or15794_2.value.uniqueCpId)?.mo00040.modelValue,
      modifiedCnt: localOneway.weekPlanData.modifiedCnt,
    } as WeekPlan,
    dailyCarePlanData: {
      param03: Or15781Logic.data.get(or15781_4.value.uniqueCpId)?.mo00018.modelValue ? '1' : '0',
      param04: Or15781Logic.data.get(or15781_4.value.uniqueCpId)?.mo00045?.value,
      param05: Or15786Logic.data.get(or15786_4.value.uniqueCpId)?.mo00018.modelValue ? '1' : '0',
      param07: Or15792Logic.data.get(or15792_3.value.uniqueCpId)?.mo00040.modelValue,
      param13: Or15783Logic.data.get(or15783_4.value.uniqueCpId)?.createYmdPrintSet,
      modifiedCnt: localOneway.dailyCarePlanData.modifiedCnt,
    } as DailyCarePlan,
  }
  const carePlanCheckList = Or15822Logic.data.get(or15822_1.value.uniqueCpId)?.mo00018
  const carePlanPrintDataList = []
  for (let i = 0; i < localOneway.or15822Oneway.printFormSelectItems.length; i++) {
    carePlanPrintDataList.push({
      carePlanIndex: localOneway.or15822Oneway.printFormSelectItems[i].carePlanIndex,
      carePlanDisplayName: localOneway.or15822Oneway.printFormSelectItems[i].carePlanDisplayName,
      carePlanSectionNo: localOneway.or15822Oneway.printFormSelectItems[i].carePlanSectionNo,
      carePlanKinouName: localOneway.or15822Oneway.printFormSelectItems[i].carePlanFunctionName,
      carePlanCheck: carePlanCheckList![i].modelValue ? '1' : '0',
      isLock: localOneway.or15822Oneway.printFormSelectItems[i].mo00018OnewayType.disabled ?'1':'0',
      isVisible:
        localOneway.or15822Oneway.printFormSelectItems[i].mo00018OnewayType.isVerticalLabel?'1':'0',
      sortNo: i.toString(),
    } as CarePlanPrint)
  }
  param.carePlanPrintDataList = carePlanPrintDataList
  await ScreenRepository.update('comMocPrtiniSUpdate', param)
}

</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <v-sheet class="view">
        <c-v-row>
          <c-v-col
            cols="2"
            class="v-col-class-1"
          >
            <!--印刷帳票選択-->
            <div class="box-area1">
              <c-v-row
                class="titleArea"
                no-gutters
              >
                <!--印刷帳票選択タイトル-->
                <c-v-col cols="auto">
                  <base-mo00615
                    :oneway-model-value="localOneway.mo00615Sevenway"
                    :item-label-font-weight="localOneway.mo00615Sevenway.itemLabelFontWeight"
                  />
                </c-v-col>
              </c-v-row>
              <c-v-row
                no-gutters
                class="text-center"
              >
                <!--印刷帳票選択チェックボックス-->
                <c-v-col cols="auto">
                  <g-custom-or15822
                    v-bind="or15822_1"
                    :oneway-model-value="localOneway.or15822Oneway"
                  />
                </c-v-col>
              </c-v-row>
            </div>

            <div class="box-area2">
              <c-v-row
                class="titleArea"
                no-gutters
              >
                <!--両面印刷オプション設定-->
                <c-v-col cols="auto">
                  <base-mo00615
                    :oneway-model-value="localOneway.mo00615Fourway"
                    :item-label-font-weight="localOneway.mo00615Fourway.itemLabelFontWeight"
                  />
                </c-v-col>
              </c-v-row>
              <c-v-row
                no-gutters
                class="text-center"
              >
                <!--両面印刷ラジオボタン-->
                <c-v-col cols="auto">
                  <g-custom-or15823
                    v-bind="or15823_1"
                    :oneway-model-value="localOneway.or15823Oneway"
                  />
                </c-v-col>
              </c-v-row>
            </div>
          </c-v-col>
          <c-v-col cols="10">
            <c-v-row>
              <c-v-col
                cols="4"
                class="v-col-class"
              >
                <div class="box-area1">
                  <c-v-row
                    class="titleArea"
                    no-gutters
                  >
                    <!--計画書（１）印刷設定タイトル-->
                    <c-v-col cols="auto">
                      <base-mo00615
                        :oneway-model-value="localOneway.mo00615Oneway"
                        :item-label-font-weight="localOneway.mo00615Oneway.itemLabelFontWeight"
                      />
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    class="text-center"
                  >
                    <!--（計画書（１））敬称変更-->
                    <c-v-col cols="auto">
                      <g-custom-or15781 v-bind="or15781_1" />
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    class="text-center"
                  >
                    <!--（計画書（１））承認欄を印刷する-->
                    <c-v-col cols="auto">
                      <g-custom-or15786 v-bind="or15786_1" />
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    class="text-center"
                  >
                    <!--（計画書（１））印刷枠の高さを自動調整する-->
                    <c-v-col cols="auto">
                      <g-custom-or15780 v-bind="or15780_1" />
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    class="text-center"
                  >
                    <!--（計画書（１））事業名略称を印刷する-->
                    <c-v-col cols="auto">
                      <g-custom-or15784 v-bind="or15784_1" />
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    class="text-center"
                  >
                    <!--（計画書（１））要支援、事業対象者の要介護度を印刷-->
                    <c-v-col cols="auto">
                      <g-custom-or15787 v-bind="or15787_1" />
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    class="month-center"
                  >
                    <!--（計画書（１））作成年月日印刷区分-->
                    <c-v-col cols="auto month-col">
                      <g-custom-or15783
                        v-bind="or15783_1"
                        :oneway-model-value="localOneway.or15783Oneway"
                      />
                    </c-v-col>
                  </c-v-row>
                </div>
                <div class="box-area2">
                  <c-v-row
                    class="titleArea"
                    no-gutters
                  >
                    <!--日課計画設定タイトル-->
                    <c-v-col cols="auto">
                      <base-mo00615
                        :oneway-model-value="localOneway.mo00615Fiveway"
                        :item-label-font-weight="localOneway.mo00615Fiveway.itemLabelFontWeight"
                      />
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    class="text-center"
                  >
                    <!--（日課計画）敬称変更-->
                    <c-v-col cols="auto">
                      <g-custom-or15781 v-bind="or15781_4" />
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    class="text-center"
                  >
                    <!--（日課計画）承認欄を印刷する-->
                    <c-v-col cols="auto">
                      <g-custom-or15786 v-bind="or15786_4" />
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    class="text-center col-padding"
                  >
                    <!--（日課計画）印刷する要介護度-->
                    <c-v-col cols="auto">
                      <g-custom-or15792
                        v-bind="or15792_3"
                        :oneway-model-value="localOneway.or17334Oneway"
                      />
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    class="month-center"
                  >
                    <!--（日課計画）作成年月日印刷区分-->
                    <c-v-col cols="auto">
                      <g-custom-or15783
                        v-bind="or15783_4"
                        :oneway-model-value="localOneway.or17340Oneway"
                      />
                    </c-v-col>
                  </c-v-row>
                </div>
              </c-v-col>
              <c-v-col
                cols="4"
                class="v-col-class"
              >
                <div class="box-area1">
                  <c-v-row
                    class="titleArea"
                    no-gutters
                  >
                    <!--計画書（２）印刷設定タイトル-->
                    <c-v-col cols="auto">
                      <base-mo00615
                        :oneway-model-value="localOneway.mo00615Twoway"
                        :item-label-font-weight="localOneway.mo00615Twoway.itemLabelFontWeight"
                      />
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    class="text-center"
                  >
                    <!--（計画書（２））敬称変更-->
                    <c-v-col cols="auto">
                      <g-custom-or15781 v-bind="or15781_2" />
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    class="text-center"
                  >
                    <!--（計画書（２））承認欄を印刷する-->
                    <c-v-col cols="auto">
                      <g-custom-or15786 v-bind="or15786_2" />
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    class="text-center"
                  >
                    <!--（計画書（２））行の先頭で改ページする-->
                    <c-v-col cols="auto">
                      <g-custom-or15797 v-bind="or15797_1" />
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    class="text-center col-padding"
                  >
                    <!--（計画書（２））印刷する要介護度-->
                    <c-v-col cols="auto">
                      <g-custom-or15792
                        v-bind="or15792_1"
                        :oneway-model-value="localOneway.or15792Oneway"
                      />
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    class="text-center"
                  >
                    <!--（計画書（２））印刷の高さを最小行に設定-->
                    <c-v-col cols="auto">
                      <g-custom-or15793 v-bind="or15793_1" />
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    class="text-center flex-column"
                  >
                    <!--（計画書（２））右上に事業所名を印刷-->
                    <g-custom-or15794
                      v-bind="or15794_1"
                      :oneway-model-value="localOneway.or15794Oneway"
                    />
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    class="month-center"
                  >
                    <!--（計画書（２））作成年月日印刷区分-->
                    <c-v-col cols="auto">
                      <g-custom-or15783
                        v-bind="or15783_2"
                        :oneway-model-value="localOneway.or15800Oneway"
                      />
                    </c-v-col>
                  </c-v-row>
                </div>

                <div
                  v-if="isVisUseSlip"
                  class="box-area2"
                >
                  <c-v-row
                    class="titleArea"
                    no-gutters
                  >
                    <!--利用票・別表設定-->
                    <c-v-col cols="auto">
                      <base-mo00615
                        :oneway-model-value="localOneway.mo00615Sixway"
                        :item-label-font-weight="localOneway.mo00615Sixway.itemLabelFontWeight"
                      />
                    </c-v-col>
                  </c-v-row>
                  <c-v-row
                    no-gutters
                    class="text-center"
                  >
                    <!--（利用票・別表）オプション設定-->
                    <c-v-col cols="auto">
                      <g-custom-or07213
                        v-bind="or15823_1"
                        :oneway-model-value="localOneway.or07213Oneway"
                      />
                    </c-v-col>
                  </c-v-row>
                </div>
              </c-v-col>
              <c-v-col
                cols="4"
                class="v-col-class2"
              >
                <c-v-row
                  class="titleArea"
                  no-gutters
                >
                  <!--週間計画印刷設定タイトル-->
                  <c-v-col cols="auto">
                    <base-mo00615
                      :oneway-model-value="localOneway.mo00615Threeway"
                      :item-label-font-weight="localOneway.mo00615Threeway.itemLabelFontWeight"
                    />
                  </c-v-col>
                </c-v-row>
                <c-v-row
                  no-gutters
                  class="text-center"
                >
                  <!--（週間計画）敬称変更-->
                  <c-v-col cols="auto">
                    <g-custom-or15781 v-bind="or15781_3" />
                  </c-v-col>
                </c-v-row>
                <c-v-row
                  no-gutters
                  class="text-center"
                >
                  <!--（週間計画）承認欄を印刷する-->
                  <c-v-col cols="auto">
                    <g-custom-or15786 v-bind="or15786_3" />
                  </c-v-col>
                </c-v-row>
                <c-v-row
                  no-gutters
                  class="text-center"
                >
                  <!--（週間計画）処理年月印刷-->
                  <c-v-col cols="auto">
                    <g-custom-or16661 v-bind="or16661_1" />
                  </c-v-col>
                </c-v-row>
                <c-v-row
                  no-gutters
                  class="text-center"
                >
                  <!--（週間計画）モノクロ印刷モード-->
                  <c-v-col cols="auto">
                    <g-custom-or16648 v-bind="or16648_1" />
                  </c-v-col>
                </c-v-row>
                <c-v-row
                  no-gutters
                  class="text-center col-padding"
                >
                  <!--（週間計画）印刷する要介護度-->
                  <c-v-col cols="auto">
                    <g-custom-or15792
                      v-bind="or15792_2"
                      :oneway-model-value="localOneway.or16650Oneway"
                    />
                  </c-v-col>
                </c-v-row>
                <c-v-row
                  no-gutters
                  class="text-center col-padding"
                >
                  <!--（週間計画）印刷する週単位以外のサービス-->
                  <c-v-col cols="auto">
                    <g-custom-or16649
                      v-bind="or16649_1"
                      :oneway-model-value="localOneway.or16649Oneway"
                    />
                  </c-v-col>
                </c-v-row>
                <c-v-row
                  no-gutters
                  class="text-center"
                >
                  <!--（週間計画）右上に事業所名を印刷-->
                  <g-custom-or15794
                    v-bind="or15794_2"
                    :oneway-model-value="localOneway.or16651Oneway"
                  />
                </c-v-row>
                <c-v-row
                  no-gutters
                  class="month-center"
                >
                  <!--（週間計画）作成年月日印刷区分-->
                  <c-v-col cols="auto">
                    <g-custom-or15783
                      v-bind="or15783_3"
                      :oneway-model-value="localOneway.or15800Oneway"
                    />
                  </c-v-col>
                </c-v-row>
              </c-v-col>
            </c-v-row>
          </c-v-col>
        </c-v-row>
      </v-sheet>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          @click="close"
        ></base-mo00611>
        <!-- 印刷ボタン Mo00611 -->
        <base-mo00609
          v-bind="localOneway.mo00609UpdateOneway"
          class="mx-2"
          @click="print"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
</template>

<style scoped lang="scss">
:deep(.v-dialog--scrollable > .v-overlay__content > .v-card > .v-card-text) {
  overflow-y: unset;
}
.view {
  display: flex;
  margin: 0px;
  padding: 0px;
  flex-direction: column;

  .box-area1 {
    height: 450px;
  }
  .box-area2 {
    height: 340px;
  }
  .v-col-class {
    padding: 0;
    border-left: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
  }
  .v-col-class2 {
    height: 790px;
    padding: 0;
    border-left: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
  }

  .v-col-class-1 {
    padding: 0;
    border-left: 1px solid #ddd;
  }

  .col-padding {
    padding-left: 16px;
  }

  .titleArea {
    border-bottom: 1px solid #ddd;
    padding: 8px;
    background: rgba(237, 241, 247, 1) !important;
    align-content: center;
  }

  .month-center {
    display: flex;
    margin-left: 8px;
  }

  .month-col {
    margin-top: 112px;
  }
  .flex-column {
    flex-direction: column;
  }
}
</style>
