<script setup lang="ts">
/**
 * Or10578:有機体:モーダル（計画書（1）マスタモーダル）
 * GUI00934_計画書（1）マスタ画面
 *
 * @description
 * ［計画書（1）マスタ］画面が表示される。
 *
 * <AUTHOR>
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or15790Const } from '../Or15790/Or15790.constants'
import { Or15789Const } from '../Or15789/Or15789.constants'
import { Or15791Logic } from '../Or15791/Or15791.logic'
import { Or15791Const } from '../Or15791/Or15791.constants'
import { Or15789Logic } from '../Or15789/Or15789.logic'
import { Or15790Logic } from '../Or15790/Or15790.logic'
import type { Or10578StateType, Or10578Type } from './Or10578.type'
import { Or10578Const } from './Or10578.constants'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import { useScreenOneWayBind, useScreenStore, useSetupChildProps } from '#imports'
import { useScreenUtils } from '~/utils/useScreenUtils'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Or10578OnewayType } from '~/types/cmn/business/components/Or10578Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type {
  CarePlanOneInitSelectInEntity,
  CarePlanOneInitSelectOutEntity,
} from '~/repositories/cmn/entities/CarePlanOneInitSelectEntity'
import { CustomClass } from '~/types/CustomClassType'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  CarePlanOneBunrui3,
  CarePlanOneInsertInEntity,
} from '~/repositories/cmn/entities/CarePlanOneInsertEntity'
import type { Or15791OneWayType } from '~/types/cmn/business/components/Or15791Type'
import type { Or15790OneWayType } from '~/types/cmn/business/components/Or15790Type'
import type { Or15789OneWayType } from '~/types/cmn/business/components/Or15789Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { CmnMCdKbnId } from '~/constants/cmn/system-code-constants'
import type { Mo00040OnewayType, Mo00040Type } from '~/types/business/components/Mo00040Type'

const { setChildCpBinds } = useScreenUtils()
const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or10578OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

const or15791_1 = ref({ uniqueCpId: '' })
const or15790_1 = ref({ uniqueCpId: '' })
const or15789_1 = ref({ uniqueCpId: '' })
const local = reactive({
  or10578: {
    kaigo: '',
    kaigoBunrui3: '',
    kaigoModifiedCnt: '',
    dantou: '',
    dantouBunrui3: '',
    dantouModifiedCnt: '',
    mojiSize: '',
    mojiSizeBunrui3: '',
    mojiSizeModifiedCnt: '',
  } as Or10578Type,
  mo00040 : {} as Mo00040Type
})
const localOneway = reactive({
  or10578OneWay: {
    ...props.onewayModelValue,
  },
  // 要介護度入力
  or15791: { kaigo: '' },
  // 担当者入力
  or15790: { dantou: '' },
  // 印刷時の文字サイズ入力
  or15789: { mojiSize: '' },
  // 要介護度入力
  or15791OneWay: { radioItemsList: [] as CodeType[] } as Or15791OneWayType,
  // 担当者入力
  or15790OneWay: { radioItemsList: [] as CodeType[] } as Or15790OneWayType,
  // 印刷時の文字サイズ入力
  or15789OneWay: { radioItemsList: [] as CodeType[] } as Or15789OneWayType,
  // 閉じるコンポーネント,
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 確定コンポーネント
  mo00609UpdateOneway: {
    btnLabel: t('btn.save'),
  } as Mo00609OnewayType,
  // 計画書（1）マスタダイアログ
  mo00024Oneway: {
    width: '680px',
    height: '500px',
    persistent: true,
    showCloseBtn: true,
    disabledCloseBtnEvent: true,
    mo01344Oneway: {
      name: 'Or10578',
      toolbarTitle: t('label.care-plan1-master'),
      toolbarName: 'Or10578ToolBar',
      font: '24px',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
      cardTextClass: 'card-text pa-2',
    },
  } as Mo00024OnewayType,
  mo01338Oneway: {
    value: t('label.level-of-care-required'),
    valueFontWeight: 'bold',
  } as Mo01338OnewayType,
  mo01338Twoway: {
    value: t('label.care-manager'),
    valueFontWeight: 'bold',
  } as Mo01338OnewayType,
  mo01338Threeway: {
    value: t('label.text-size-when-printing'),
    valueFontWeight: 'bold',
  } as Mo01338OnewayType,
  mo01338Fourway: {
    value: t('label.preserved-by-each-office'),
    customClass: new CustomClass({
      outerClass: 'mr-0',
      labelClass: 'ma-7',
      itemClass: 'ml-0 align-left',
      itemStyle: 'color:  rgb(var(--v-theme-black-500));',
    }),
  } as Mo01338OnewayType,
  mo00040Oneway: {
    showItemLabel: false,
    itemLabel: t('label.office-selection'),
    itemTitle: 'jigyoKnj',
    itemValue: 'jigyoId',
    width: '180px',
    items: [],
  } as Mo00040OnewayType,
})

/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or10578Const.DEFAULT.IS_OPEN,
})
const or21814_1 = ref({ uniqueCpId: '' })

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or10578StateType>({
  cpId: Or10578Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or10578Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or15789Const.CP_ID(1)]: or15789_1.value,
  [Or15790Const.CP_ID(1)]: or15790_1.value,
  [Or15791Const.CP_ID(1)]: or15791_1.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
})

onMounted(async () => {
  await initCodes()
  await init()
  // データ変更確認ダイアログを初期化(i.cmn.10430)
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })
})

/**
 * 初期情報取得
 */
async function init() {
  // 計画書（1）マスタ初期情報取得(IN)
  const inputData: CarePlanOneInitSelectInEntity = {
    shisetuId: localOneway.or10578OneWay.carePlan1MasterInData.shisetuId,
    jigyoId: localOneway.or10578OneWay.carePlan1MasterInData.jigyoId,
    svJigyoIdList: localOneway.or10578OneWay.carePlan1MasterInData.svJigyoIdList,
  }

  await fetchInitData(inputData)
  setChildCpBinds(props.uniqueCpId, {
    [Or15791Const.CP_ID(1)]: {
      twoWayValue: {
        kaigo: localOneway.or15791.kaigo,
      },
    },
    [Or15790Const.CP_ID(1)]: {
      twoWayValue: {
        dantou: localOneway.or15790.dantou,
      },
    },
    [Or15789Const.CP_ID(1)]: {
      twoWayValue: {
        mojiSize: localOneway.or15789.mojiSize,
      },
    },
  })
}

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 要介護度
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_KAIGO },
    // 担当者
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DANTOU },
    // 全体のまとめの選択肢
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_FONT_SIZE },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 要介護度
  localOneway.or15791OneWay.radioItemsList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_KAIGO
  )
  // 担当者選択
  localOneway.or15790OneWay.radioItemsList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_DANTOU
  )
  // 印刷時の文字サイズ
  localOneway.or15789OneWay.radioItemsList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_FONT_SIZE
  )
}

async function fetchInitData(inputData: CarePlanOneInitSelectInEntity) {
  // 計画書（1）マスタ初期情報取得
  const ret: CarePlanOneInitSelectOutEntity = await ScreenRepository.select(
    'carePlanOneInitSelect',
    inputData
  )
  // 戻り値はテーブルデータとして処理されます
  localOneway.or15791.kaigo = ret.data.kaigo
  localOneway.or15789.mojiSize = ret.data.mojiSize
  localOneway.or15790.dantou = ret.data.dantou

  local.or10578.kaigo = ret.data.kaigo
  local.or10578.kaigoBunrui3 = ret.data.kaigoBunrui3
  local.or10578.kaigoModifiedCnt = ret.data.kaigoModifiedCnt
  local.or10578.dantou = ret.data.dantou
  local.or10578.dantouBunrui3 = ret.data.dantouBunrui3
  local.or10578.dantouModifiedCnt = ret.data.dantouModifiedCnt
  local.or10578.mojiSize = ret.data.mojiSize
  local.or10578.mojiSizeBunrui3 = ret.data.mojiSizeBunrui3
  local.or10578.mojiSizeModifiedCnt = ret.data.mojiSizeModifiedCnt
  // 事業所
  if (ret.data.svJigyoInfoList) {
    localOneway.mo00040Oneway.items = ret.data.svJigyoInfoList
    local.mo00040.modelValue = localOneway.or10578OneWay.carePlan1MasterInData.jigyoId
  }
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.emitType,
  async () => {
    if (mo00024.value.emitType === 'closeBtnClick') {
      await close()
      mo00024.value.emitType = undefined
    }
  }
)

async function insert() {
  await save()
}

async function save() {
  const bunrui3SetList = [] as CarePlanOneBunrui3[]
  if (
    !local.or10578.kaigoBunrui3 ||
    local.or10578.kaigoBunrui3 === '' ||
    local.or10578.kaigo !== Or15791Logic.data.get(or15791_1.value.uniqueCpId)?.kaigo
  ) {
    const carePlanOneBunrui3 = {
      bunrui3Id: Or10578Const.DEFAULT.KAIGO,
      bunrui3Value: Or15791Logic.data.get(or15791_1.value.uniqueCpId)?.kaigo,
      modifiedCnt: local.or10578.kaigoModifiedCnt,
    } as CarePlanOneBunrui3
    bunrui3SetList.push(carePlanOneBunrui3)
  }

  if (
    !local.or10578.dantouBunrui3 ||
    local.or10578.dantouBunrui3 === '' ||
    local.or10578.dantou !== Or15790Logic.data.get(or15790_1.value.uniqueCpId)?.dantou
  ) {
    const carePlanOneBunrui3 = {
      bunrui3Id: Or10578Const.DEFAULT.DANTOU,
      bunrui3Value: Or15790Logic.data.get(or15790_1.value.uniqueCpId)?.dantou,
      modifiedCnt: local.or10578.dantouModifiedCnt,
    } as CarePlanOneBunrui3
    bunrui3SetList.push(carePlanOneBunrui3)
  }

  if (
    !local.or10578.mojiSizeBunrui3 ||
    local.or10578.mojiSizeBunrui3 === '' ||
    local.or10578.mojiSize !== Or15789Logic.data.get(or15789_1.value.uniqueCpId)?.mojiSize
  ) {
    const carePlanOneBunrui3 = {
      bunrui3Id: Or10578Const.DEFAULT.MOJISIZE,
      bunrui3Value: Or15789Logic.data.get(or15789_1.value.uniqueCpId)?.mojiSize,
      modifiedCnt: local.or10578.mojiSizeModifiedCnt,
    } as CarePlanOneBunrui3
    bunrui3SetList.push(carePlanOneBunrui3)
  }
  if (bunrui3SetList.length <= 0) {
    setState({ isOpen: false })
  }
  const param: CarePlanOneInsertInEntity = {
    shisetuId: localOneway.or10578OneWay.carePlan1MasterInData.shisetuId,
    jigyoId: localOneway.or10578OneWay.carePlan1MasterInData.jigyoId,
    bunrui3SetList: bunrui3SetList,
  }
  await ScreenRepository.insert('carePlanOneInsert', param)
  setState({ isOpen: false })

}
/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
async function close() {
  if (isEdit.value) {
    const dialogResult = await openEditDialog()
    switch (dialogResult) {
      case Or10578Const.DEFAULT.DIALOG_RESULT_YES:
        await insert()
        break
      case Or10578Const.DEFAULT.DIALOG_RESULT_NO:
        // いいえ選択時は編集内容を破棄するので何もしない
        setState({ isOpen: false })
        break
      case Or10578Const.DEFAULT.DIALOG_RESULT_CANCEL:
        // キャンセル選択時は複写データの作成を行わずに終了する
        return
    }
  } else {
    setState({ isOpen: false })
  }
}

/**
 * 事業所を監視
 *
 * @description
 */
watch(
  () => local.mo00040,
  async (newValue) => {
    if (newValue?.modelValue && newValue?.modelValue !== localOneway.or10578OneWay.carePlan1MasterInData.jigyoId) {
      if (isEdit.value ) {
        const dialogResult = await openEditDialog()
        switch (dialogResult) {
          case Or10578Const.DEFAULT.DIALOG_RESULT_YES:
            await insert()
            localOneway.or10578OneWay.carePlan1MasterInData.jigyoId = newValue.modelValue
            await init()
            break
          case Or10578Const.DEFAULT.DIALOG_RESULT_NO:
            // いいえ選択時は編集内容を破棄するので何もしない
            localOneway.or10578OneWay.carePlan1MasterInData.jigyoId = newValue.modelValue
            await init()
            break
          case Or10578Const.DEFAULT.DIALOG_RESULT_CANCEL:
            // キャンセル選択時は複写データの作成を行わずに終了する
            local.mo00040.modelValue = localOneway.or10578OneWay.carePlan1MasterInData.jigyoId
            return
        }
      } else {
        localOneway.or10578OneWay.carePlan1MasterInData.jigyoId = newValue.modelValue
        await init()
      }
    }

  }
)

/**
 * 編集破棄ダイアログ表示
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
async function openEditDialog(): Promise<string> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)

        let result = Or10578Const.DEFAULT.DIALOG_RESULT_CANCEL

        if (event?.firstBtnClickFlg) {
          result = Or10578Const.DEFAULT.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or10578Const.DEFAULT.DIALOG_RESULT_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = Or10578Const.DEFAULT.DIALOG_RESULT_CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  return useScreenStore().isEditNavControl()
})
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet class="view">
        <c-v-row class="operationArea">
          <c-v-col
            cols="auto"
            class="ma-1 pt-1 mr-4"
          >
            <base-at-label :value="t('label.office-selection')" />
          </c-v-col>
          <c-v-col>
            <base-mo00040
              v-model="local.mo00040"
              :oneway-model-value="localOneway.mo00040Oneway"
              v-bind="{ ...$attrs }"
            />
          </c-v-col>
        </c-v-row>

        <c-v-row class="subSection">
          <c-v-col class="sectionHeader">
            <base-mo01338
              :oneway-model-value="localOneway.mo01338Oneway"
              style="background-color: rgb(var(--v-theme-background)) !important"
            />
          </c-v-col>
          <c-v-col class="sectionContent">
            <g-custom-or15791
              v-bind="or15791_1"
              :oneway-model-value="localOneway.or15791OneWay"
              :unique-cp-id="or15791_1.uniqueCpId"
            />
          </c-v-col>
        </c-v-row>
        <c-v-row class="subSection">
          <c-v-col class="sectionHeader">
            <base-mo01338
              :oneway-model-value="localOneway.mo01338Twoway"
              style="background-color: rgb(var(--v-theme-background)) !important"
            />
          </c-v-col>
          <c-v-col class="sectionContent">
            <g-custom-or15790
              v-bind="or15790_1"
              :oneway-model-value="localOneway.or15790OneWay"
              :unique-cp-id="or15790_1.uniqueCpId"
            />
          </c-v-col>
        </c-v-row>
        <c-v-row class="subSection">
          <c-v-col class="sectionHeader">
            <base-mo01338
              :oneway-model-value="localOneway.mo01338Threeway"
              style="background-color: rgb(var(--v-theme-background)) !important"
            />
          </c-v-col>
          <c-v-col class="sectionContent">
            <g-custom-or15789
              v-bind="or15789_1"
              :oneway-model-value="localOneway.or15789OneWay"
              :unique-cp-id="or15789_1.uniqueCpId"
            />
          </c-v-col>
        </c-v-row>
      </c-v-sheet>
      <c-v-row
        no-gutters
        class="label-comment"
      >
        <c-v-col style="padding: 0px 8px 0px 8px !important">
          <base-mo01338 :oneway-model-value="localOneway.mo01338Fourway" /> </c-v-col
      ></c-v-row>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          @click="close"
        ></base-mo00611>
        <!-- 確定ボタン Mo00611 -->
        <base-mo00609
          v-bind="localOneway.mo00609UpdateOneway"
          class="mx-2"
          @click="insert"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- データ変更確認ダイアログ -->
  <g-base-or21814 v-bind="or21814_1" />
</template>

<style scoped>
.view {
  min-height: 280px;
  min-width: 520px;
  display: flex;
  margin: 0px;
  padding: 0px;
  flex-direction: column;

  .v-row {
    margin: unset;
  }

  .operationArea {
    flex: 0 0 auto;
    height: 55px;

    .v-col {
      padding-left: 0;
      padding-right: 0;
      padding-top: 0;
      padding-bottom: 8px;
    }

    .v-col:last-child {
      text-align: right;
    }
  }

  .subSection {
    border: solid thin rgb(var(--v-theme-light));
    .sectionHeader {
      display: flex;
      flex-direction: column;
      min-width: 100px;
      max-width: 150px;
      justify-content: center;
      align-items: left;
      padding: 0;
      --v-theme-overlay-multiplier: var(--v-theme-background-overlay-multiplier);
      background-color: rgb(var(--v-theme-background)) !important;
      color: rgb(var(--v-theme-on-background)) !important;
      font-weight: bold;

      span {
        width: 100%;
        padding-left: 8px;
        font-size: 14px;
      }

      small {
        width: 100%;
        padding-left: 8px;
        font-weight: normal;
      }
    }
  }

  .sectionContent {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 8px !important;

    .v-sheet {
      width: 100%;

      :deep(.radio-group) {
        width: 100%;

        .v-col-auto {
          width: 100%;
        }
      }
    }
  }
}

.buttonArea {
  button:not(:nth-child(1)) {
    margin-left: 8px;
  }
}
.label-comment {
  color: rgb(var(--v-theme-black-536)) !important;
  margin: 0px 0px 0px 0px;
  height: 20px;
  position: fixed !important;
  bottom: 0;
  left: 0;
  right: 0;
  padding-bottom: 130px;
}
</style>
