<script setup lang="ts">
/**
 * Or01083：有機体：(日課計画マスタ)文字サイズ入力
 */
import { reactive, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or01083Const } from './Or01083.constants'
import type { Mo00039OnewayType } from '~/types/cmn/business/components/Mo00039Type'
import {  useScreenTwoWayBind } from '#imports'
import type { Or01083OneWayType, Or01083Type } from '~/types/cmn/business/components/Or01083Type'

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  onewayModelValue: Or01083OneWayType
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const localOneway = reactive({
  or01083OneWay: {
    ...props.onewayModelValue,
  } as Or01083OneWayType,
  mo00039Oneway: {
    // デフォルト値の設定
    name: Or01083Const.CP_ID(0),
    itemLabel: t('label.level-of-care-required'),
    showItemLabel: false,
    hideDetails: true,
  } as Mo00039OnewayType,
})
/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<Or01083Type>({
  cpId: Or01083Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
watch(
  () => props.onewayModelValue.radioItemsList,
  () => {
    localOneway.or01083OneWay.radioItemsList = props.onewayModelValue.radioItemsList
  }
)
</script>

<template>
  <c-v-row
    no-gutters
    class="text-center"
  >
    <base-mo00039
      v-if="refValue"
      v-model="refValue!.textSizeFlg"
      :oneway-model-value="localOneway.mo00039Oneway"
    >
      <base-at-radio
        v-for="item in localOneway.or01083OneWay.radioItemsList"
        :key="item.value"
        v-model="refValue!.textSizeFlg"
        style="width: 140px"
        :name="item.label"
        :radio-label="item.label"
        :value="item.value"
      />
    </base-mo00039>
  </c-v-row>
</template>

<style scoped lang="scss">
.text-center {
  align-items: baseline;
}
</style>
