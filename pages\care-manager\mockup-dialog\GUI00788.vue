<script setup lang="ts">
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or10279Const } from '~/components/custom-components/organisms/Or10279/Or10279.constants'
import { Or10279Logic } from '~/components/custom-components/organisms/Or10279/Or10279.logic'
import type { Or10279OneWayType } from '~/types/cmn/business/components/Or10279Type'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI00788'
// ルーティング
const routing = 'GUI00788/pinia'
// 画面物理名
const screenName = 'GUI00788'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or10279 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00788' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  //Or10279Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or10279.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI00788',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or10279Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or10279Const.CP_ID(1)]: or10279.value,
})

const or10279Oneway = ref<Or10279OneWayType>({
  staffId: '1',
  baseDate: '20250308',
  officeId: '2',
})

/**
 *  ボタン押下時の処理(Or10279)
 *
 */
function onClickOr10279() {
  // Or10279のダイアログ開閉状態を更新する
  Or10279Logic.state.set({
    uniqueCpId: or10279.value.uniqueCpId,
    state: { isOpen: true },
  })
}
// ダイアログ表示フラグ
const showDialogOr10279 = computed(() => {
  // Or10279のダイアログ開閉状態
  return Or10279Logic.state.get(or10279.value.uniqueCpId)?.isOpen ?? false
})
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr10279"
        >GUI00788_［アセスメント（インターライ）CSV出力］画面
      </v-btn>
      <g-custom-or-10279
        v-if="showDialogOr10279"
        v-bind="or10279"
        :oneway-model-value="or10279Oneway"
      />
    </c-v-col>
  </c-v-row>
</template>
