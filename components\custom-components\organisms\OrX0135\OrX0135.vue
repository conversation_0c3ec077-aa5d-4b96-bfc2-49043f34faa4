<script setup lang="ts">
/**
 * OrX0135:有機体:承認欄登録画面モーダル
 * GUI00617_承認欄登録画面
 *
 * @description
 * GUI00617_承認欄登録画面
 *
 *  <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { Or52916Logic } from '../Or52916/Or52916.logic'
import { Or52916Const } from '../Or52916/Or52916.constants'
import { OrX0135Const } from './OrX0135.constants'
import type {
  ApprovalColumnInputsListType,
  DraggingStyleListType,
  OrX0135StateType,
  ShowFlgObjectType,
  TextRenderStyleType,
} from './OrX0135.type'
import {
  computed,
  onMounted,
  reactive,
  ref,
  useScreenOneWayBind,
  useScreenStore,
  useScreenTwoWayBind,
  useSetupChildProps,
  watch,
} from '#imports'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00610OnewayType } from '~/types/business/components/Mo00610Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { CmnMCdKbnId } from '~/constants/cmn/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import { CustomClass } from '~/types/CustomClassType'
import { hasRegistAuth } from '~/utils/useCmnAuthz'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type {
  ApprovalColumnRegistrationSelectInEntity,
  ApprovalColumnRegistrationSelectOutEntity,
  ShoninList,
} from '~/repositories/cmn/entities/ApprovalColumnRegistrationSelectEntity'
import type { ApprovalColumnRegistrationUpdateInEntity } from '~/repositories/cmn/entities/ApprovalColumnRegistrationUpdateEntity'
import type { OrX0135OnewayType, OrX0135Type } from '~/types/cmn/business/components/OrX0135Type'
import type { Or52916OnewayType, Or52916Type } from '~/types/cmn/business/components/Or52916Type'
/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: OrX0135Type
  onewayModelValue: OrX0135OnewayType
  uniqueCpId: string
}

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

// 定数の分割代入
const {
  IS_OPEN,
  EMPTY,
  OFFSET_6,
  DEFAULT_TEXTKNJ,
  DEFAULT_DAYKNJ,
  SHOW_ROW_1_VALUE,
  SHOW_ROW_2_VALUE,
  SHOW_ROW_3_VALUE,
  SHOW_ROW_4_VALUE,
  FONT_SIZE_9,
  FONT_SIZE_10,
  FONT_SIZE_11,
  FONT_SIZE_12,
  WIDTH_PX,
  WIDTH_0_PX,
  WIDTH_1190_PX,
  WIDTH_10_PX,
  WIDTH_1200_PX,
  WIDTH_592_PX,
  WIDTH_608_PX,
} = OrX0135Const.DEFAULT

// 引継情報を取得する
const props = defineProps<Props>()

// 画面タイトル
const mo00024 = ref<Mo00024Type>({
  isOpen: IS_OPEN,
})

// 親画面
const defaultOnewayModelValue: OrX0135OnewayType = {
  // ★ｻｰﾋﾞｽ事業者ID
  svJigyoId: EMPTY,
  // ★法人ID
  houjinId: EMPTY,
  // ★施設ID
  shisetuId: EMPTY,
  // 帳票コード
  chohyoCd: EMPTY,
}

// mo00045コンポーネント共通設定
const mo00045OnewayCommom = {
  itemLabel: '',
  maxlength: '150',
  showItemLabel: false,
  customClass: { outerClass: 'mr-0' },
} as unknown as Mo00045OnewayType

// mo00039コンポーネント共通設定
const mo00039Common = {
  showItemLabel: true,
  hideDetails: true,
  customClass: new CustomClass({
    labelClass: 'itemLabel',
  }),
}

// 本コンポーネント内の状態定義
const localOneway = reactive({
  orx0135: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // 承認欄登録画面モーダル
  mo00024Oneway: {
    width: '1234px',
    height: '828px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'OrX0135',
      toolbarTitle: t('label.approval-column-login'),
      toolbarName: 'orx0135ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  // 複写コンポーネント入力パラメータ
  or52916Data: {
    svJigyoId: '',
    sectionNo: '',
    shoninFlg: '',
    sysRyaku: '',
  } as Or52916OnewayType,
  // 複写ボタン
  mo00610DuplicateBtnOneWay: {
    btnLabel: t('btn.duplicate'),
  } as Mo00610OnewayType,
  // 初期値ボタン
  mo00611InitialValueBtnOneWay: {
    btnLabel: t('btn.initial-value'),
  } as Mo00611OnewayType,
  // 承認欄inputコンポーネントのリスト
  approvalColumnInputsList: [
    {
      textKey: 'text1Font',
      // 承認欄1行目下線無
      leftMo00045: {
        inputComponent: {
          ...mo00045OnewayCommom,
        },
        leftName: 'text1Knj',
        widthKey: 'text1Width',
      },
      // 承認欄1行目下線有
      rightMo00045: {
        inputComponent: {
          ...mo00045OnewayCommom,
        },
        rightName: 'day1Knj',
        widthKey: 'day1Width',
      },
    },
    {
      textKey: 'text2Font',
      // 承認欄2行目下線無
      leftMo00045: {
        inputComponent: {
          ...mo00045OnewayCommom,
        },
        leftName: 'text2Knj',
        widthKey: 'text2Width',
      },
      // 承認欄2行目下線有
      rightMo00045: {
        inputComponent: {
          ...mo00045OnewayCommom,
        },
        rightName: 'day2Knj',
        widthKey: 'day2Width',
      },
    },
    {
      textKey: 'text3Font',
      // 承認欄3行目下線無
      leftMo00045: {
        inputComponent: {
          ...mo00045OnewayCommom,
        },
        leftName: 'text3Knj',
        widthKey: 'text3Width',
      },
      // 承認欄3行目下線有
      rightMo00045: {
        inputComponent: {
          ...mo00045OnewayCommom,
        },
        rightName: 'day3Knj',
        widthKey: 'day3Width',
      },
    },
    {
      textKey: 'text4Font',
      // 承認欄4行目下線無
      leftMo00045: {
        inputComponent: {
          ...mo00045OnewayCommom,
        },
        leftName: 'text4Knj',
        widthKey: 'text4Width',
      },
      // 承認欄4行目下線有
      rightMo00045: {
        inputComponent: {
          ...mo00045OnewayCommom,
        },
        rightName: 'day4Knj',
        widthKey: 'day4Width',
      },
    },
  ] as ApprovalColumnInputsListType[],
  // フォント設定ラジオグループ
  approvalColumnRadioList: [
    // 1行目文字サイズ
    {
      // デフォルト値の設定
      ...mo00039Common,
      name: 'font-size-1',
      itemLabel: t('label.font-size-1'),
    },
    // 2行目文字サイズ
    {
      // デフォルト値の設定
      ...mo00039Common,
      name: 'font-size-2',
      itemLabel: t('label.font-size-2'),
    },
    // 3行目文字サイズ
    {
      // デフォルト値の設定
      ...mo00039Common,
      name: 'font-size-3',
      itemLabel: t('label.font-size-3'),
    },
    // 4行目文字サイズ
    {
      // デフォルト値の設定
      ...mo00039Common,
      name: 'font-size-4',
      itemLabel: t('label.font-size-4'),
    },
  ],
  // 印刷時のイメージラベル
  mo01338PrintingImageOneway: {
    value: t('label.printing-image'),
    valueFontWeight: 'bold',
    customClass: {
      itemClass: 'ml-0 my-2',
    },
  } as Mo01338OnewayType,
  // 表示する行数Radio
  mo00039ShowRowNumOneway: {
    // デフォルト値の設定
    name: 'show-row-number',
    itemLabel: t('label.show-row-number'),
    showItemLabel: true,
    hideDetails: true,
    customClass: new CustomClass({
      labelClass: 'itemLabel',
    }),
  } as Mo00039OnewayType,
  // テキストボックスに直接入力してくださいラベル
  mo01338Text1Oneway: {
    value: t('label.pringting-text-1'),
    valueFontWeight: 'bold',
    customClass: new CustomClass({
      itemClass: 'txt-class',
    }),
  } as Mo01338OnewayType,
  // テキストボックス境界線をドラッグして、下線なし/あり部分の割合を変更することができます
  mo01338Text2Oneway: {
    value: t('label.pringting-text-4'),
    valueFontWeight: 'bold',
    customClass: new CustomClass({
      itemClass: 'txt-class',
    }),
  } as Mo01338OnewayType,
  // 帳票に印刷される承認欄は「印刷時のイメージ」で確認して下さいラベル
  mo01338Text3Oneway: {
    value: t('label.pringting-text-3'),
    valueFontWeight: 'bold',
    customClass: new CustomClass({
      itemClass: 'txt-class',
    }),
  } as Mo01338OnewayType,
  // 閉じるボタン
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 確定ボタン
  mo00609SaveBtnOneway: {
    btnLabel: t('btn.confirm'),
  } as Mo00609OnewayType,
  // 表示する行数
  mo00039ShowRowNumberOneway: [
    {
      label: t('label.show-row-1'),
      value: SHOW_ROW_1_VALUE,
    },
    {
      label: t('label.show-row-2'),
      value: SHOW_ROW_2_VALUE,
    },
    {
      label: t('label.show-row-3'),
      value: SHOW_ROW_3_VALUE,
    },
    {
      label: t('label.show-row-4'),
      value: SHOW_ROW_4_VALUE,
    },
  ] as CodeType[],
  // 文字サイズリスト
  mo00039FontSizeOneway: [] as CodeType[],
})

// ローカル双方向bind
const local = reactive({
  // 承認欄の複写ModelValue
  or52916ModelValue: {} as Or52916Type,
  chohyoCd: '',
})

// 各行の承認欄の表示可否オブジェクト群
const showFlgObject = reactive<ShowFlgObjectType>({
  // 承認欄1行目下線無の幅
  text1KnjFlg: true,
  // 承認欄1行目下線有の幅
  day1KnjFlg: true,
  // 承認欄2行目下線無の幅
  text2KnjFlg: true,
  // 承認欄2行目下線有の幅
  day2KnjFlg: true,
  // 承認欄3行目下線無の幅
  text3KnjFlg: true,
  // 承認欄3行目下線有の幅
  day3KnjFlg: true,
  // 承認欄4行目下線無の幅
  text4KnjFlg: true,
  // 承認欄4行目下線有の幅
  day4KnjFlg: true,
})

// プレビュー文字スタイルのリスト
const textRenderStyle = reactive<TextRenderStyleType[]>([
  {
    leftStyle: {
      width: '',
    },
    rightStyle: {
      width: '',
    },
  },
  {
    leftStyle: {
      width: '',
    },
    rightStyle: {
      width: '',
    },
  },
  {
    leftStyle: {
      width: '',
    },
    rightStyle: {
      width: '',
    },
  },
  {
    leftStyle: {
      width: '',
    },
    rightStyle: {
      width: '',
    },
  },
])

// 確認ダイアログ
const or21814 = ref({ uniqueCpId: '' })

//承認欄の複写画面
const or52916 = ref({ uniqueCpId: '' })

// ドラッグフラグ
const isDragging = ref(false)

// 各行の開始位置
const startX = [0, 0, 0, 0]

// 各行左側の input 開始幅
const startLeftInputWidth = [0, 0, 0, 0]

// 現在ドラッグ中のインデックス
let currentDraggIngInex = -1

// ドラッグハンドルのスタイルセット
const draggingStyleList = reactive<DraggingStyleListType[]>([
  { left: '' },
  { left: '' },
  { left: '' },
  { left: '' },
])

// ■共通処理の登録権限チェックを行う
const hasView = (await hasRegistAuth('')) || true

/**************************************************
 * 算出プロパティ
 **************************************************/
// ダイアログ表示フラグ
const showDialog21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr52916 = computed(() => {
  // Or00586のダイアログ開閉状態
  return Or52916Logic.state.get(or52916.value.uniqueCpId)?.isOpen ?? false
})

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  return useScreenStore().isEditNavControl()
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<OrX0135StateType>({
  cpId: OrX0135Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value?: boolean) => {
      mo00024.value.isOpen = value ?? IS_OPEN
    },
  },
})
const { refValue } = useScreenTwoWayBind<OrX0135Type>({
  cpId: OrX0135Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or52916Const.CP_ID(0)]: or52916.value,
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  // 汎用コードマスタデータを取得し初期化
  await initCodes()
  // 初期化
  await getDataInfo()
})

/**************************************************
 * 関数
 **************************************************/
/**
 *  初期情報取得
 */
async function getDataInfo() {
  // 承認欄登録画面初期情報取得(IN)
  const inputData: ApprovalColumnRegistrationSelectInEntity = {
    svJigyoId: localOneway.orx0135.svJigyoId,
    chohyoCd: localOneway.orx0135.chohyoCd,
  }
  // 承認欄登録画面初期情報取得する。
  const resData: ApprovalColumnRegistrationSelectOutEntity = await ScreenRepository.select(
    'approvalColumnRegistrationSelect',
    inputData
  )
  const { shoninList } = resData.data
  if (!shoninList?.length) {
    void onInit()
    refValue.value!.chohyoCd = localOneway.orx0135.chohyoCd
  } else {
    // データ処理
    void processData(shoninList[0])
  }
  // 初期値に設定する
  setRefValue()
}

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 文字サイズリスト
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_FONT_SIZE_LIST },
  ]
  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  // 文字サイズリスト
  localOneway.mo00039FontSizeOneway = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_FONT_SIZE_LIST
  )
}

/**
 * AC003_「複写ボタン」押下
 *
 */
function duplicateBtn() {
  // 承認欄の複写画面起動パラメータ
  // 事業所ID：親画面.事業所ID
  // セクション番号：-
  // 帳票毎の保持：-
  localOneway.or52916Data = {
    svJigyoId: localOneway.orx0135.svJigyoId,
    sectionNo: '',
    shoninFlg: '',
    sysRyaku: '',
  }
  Or52916Logic.state.set({
    uniqueCpId: or52916.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * AC004_「初期値ボタン」押下
 *
 */
function onInit() {
  //画面項目の初期値を設定する。
  // 画面.承認欄1行目～画面.承認欄4行目の設定
  // 画面.承認欄1行目下線無 = "上記サービス計画について説明を受け、内容に同意しました。"
  refValue.value!.text1Knj = { value: DEFAULT_TEXTKNJ }
  // 画面.承認欄1行目下線有＝"同意年月日年月日署名印"
  refValue.value!.day1Knj = { value: DEFAULT_DAYKNJ }
  // 画面.承認欄2行目下線無＝""
  refValue.value!.text2Knj = { value: EMPTY }
  // 画面.承認欄2行目下線有＝""
  refValue.value!.day2Knj = { value: EMPTY }
  // 画面.承認欄3行目下線無＝""
  refValue.value!.text3Knj = { value: EMPTY }
  // 画面.承認欄3行目下線有＝""
  refValue.value!.day3Knj = { value: EMPTY }
  // 画面.承認欄4行目下線無＝""
  refValue.value!.text4Knj = { value: EMPTY }
  // 画面.承認欄4行目下線有＝""
  refValue.value!.day4Knj = { value: EMPTY }
  //   画面.承認欄1行目～画面.承認欄4行目の幅の設定
  // 画面.承認欄1行目下線無の幅＝608
  refValue.value!.text1Width = WIDTH_608_PX + WIDTH_PX
  // 画面.承認欄1行目下線有の幅＝592
  refValue.value!.day1Width = WIDTH_592_PX + WIDTH_PX
  // 画面.承認欄2行目下線無の幅＝1200
  refValue.value!.text2Width = WIDTH_1200_PX + WIDTH_PX
  // 画面.承認欄2行目下線有の幅＝0
  refValue.value!.day2Width = WIDTH_0_PX + WIDTH_PX
  // 画面.承認欄3行目下線無の幅＝1200
  refValue.value!.text3Width = WIDTH_1200_PX + WIDTH_PX
  // 画面.承認欄3行目下線有の幅＝0
  refValue.value!.day3Width = WIDTH_0_PX + WIDTH_PX
  // 画面.承認欄4行目下線無の幅＝1200
  refValue.value!.textWidth = WIDTH_1200_PX + WIDTH_PX
  // 画面.承認欄4行目下線有の幅＝0
  refValue.value!.day4Width = WIDTH_0_PX + WIDTH_PX
  // 画面項目の初期値を設定する。
  // 画面.表示する行数と画面.1行目文字～画面.4行目文字の設定
  // 画面.表示する行数：2行が選択される
  refValue.value!.dispKbn = SHOW_ROW_2_VALUE
  // 画面.1行目文字サイズ：普通が選択される
  refValue.value!.text1Font = FONT_SIZE_11
  // 画面.2行目文字サイズ：普通が選択される
  refValue.value!.text2Font = FONT_SIZE_11
  // 画面.3行目文字サイズ：普通が選択される
  refValue.value!.text3Font = FONT_SIZE_11
  // 画面.4行目文字サイズ：普通が選択される
  refValue.value!.text4Font = FONT_SIZE_11
  //各行のプレビュー文字の幅を設定
  textRenderStyle.forEach((item) => {
    item.leftStyle.width = WIDTH_608_PX + WIDTH_PX
    item.rightStyle.width = WIDTH_592_PX + WIDTH_PX
  })

  // 各行の入力フォームの幅を設定
  localOneway.approvalColumnInputsList.forEach((item, index) => {
    if (!index) {
      // 承認欄1行目下の入力フォームの幅を設定
      item.leftMo00045.inputComponent.width = WIDTH_608_PX + WIDTH_PX
      item.rightMo00045.inputComponent.width = WIDTH_592_PX + WIDTH_PX
      //1行目のプレビュー文字の幅を設定
      textRenderStyle[index].leftStyle.width = WIDTH_608_PX + WIDTH_PX
      textRenderStyle[index].rightStyle.width = WIDTH_592_PX + WIDTH_PX
      //ドラッグハンドルスタイル設定
      draggingStyleList[index].left = WIDTH_608_PX + OFFSET_6 + WIDTH_PX
      //表示設定
      showFlgObject[item.leftMo00045.leftName + 'Flg'] = true
      showFlgObject[item.rightMo00045.rightName + 'Flg'] = true
    } else {
      // 承認欄の他の行目下の入力フォームの幅を設定する。
      item.leftMo00045.inputComponent.width = WIDTH_1200_PX + WIDTH_PX
      item.rightMo00045.inputComponent.width = WIDTH_0_PX + WIDTH_PX
      //他の行行目のプレビュー文字の幅を設定
      textRenderStyle[index].leftStyle.width = WIDTH_1200_PX + WIDTH_PX
      textRenderStyle[index].rightStyle.width = WIDTH_0_PX + WIDTH_PX
      //表示設定
      showFlgObject[item.leftMo00045.leftName + 'Flg'] = true
      showFlgObject[item.rightMo00045.rightName + 'Flg'] = false
      //ドラッグハンドルスタイル設定
      draggingStyleList[index].left = WIDTH_1200_PX + OFFSET_6 + WIDTH_PX
    }
  })
}

/**
 * 承認欄登録画面情報保存
 *
 */
async function shoninDataSave() {
  const param: ApprovalColumnRegistrationUpdateInEntity = {
    // ★ｻｰﾋﾞｽ事業者ID
    svJigyoId: localOneway.orx0135.svJigyoId,
    // ★法人ID
    houjinId: localOneway.orx0135.houjinId,
    // ★施設ID
    shisetuId: localOneway.orx0135.shisetuId,
    // 承認欄1行目
    text1Knj: refValue.value!.text1Knj.value,
    // 承認欄2行目
    text2Knj: refValue.value!.text2Knj.value,
    // 下線部分1行目
    day1Knj: refValue.value!.day1Knj.value,
    // 下線部分2行目
    day2Knj: refValue.value!.day2Knj.value,
    // 1行目文字サイズ
    text1Font: refValue.value!.text1Font,
    // 2行目文字サイズ
    text2Font: refValue.value!.text2Font,
    // 承認欄1行目幅
    text1Width: refValue.value!.text1Width,
    // 承認欄2行目幅
    text2Width: refValue.value!.text2Width,
    // 下線部分1行目幅
    day1Width: refValue.value!.day1Width,
    // 下線部分2行目幅
    day2Width: refValue.value!.day2Width,
    // 承認欄3行目
    text3Knj: refValue.value!.text3Knj.value,
    // 承認欄4行目
    text4Knj: refValue.value!.text4Knj.value,
    // 下線部分3行目
    day3Knj: refValue.value!.day3Knj.value,
    // 下線部分4行目
    day4Knj: refValue.value!.day4Knj.value,
    // 3行目文字サイズ
    text3Font: refValue.value!.text3Font,
    // 4行目文字サイズ
    text4Font: refValue.value!.text4Font,
    // 承認欄3行目幅
    text3Width: refValue.value!.text3Width,
    // 承認欄4行目幅
    text4Width: refValue.value!.text4Width,
    // 下線部分3行目幅
    day3Width: refValue.value!.day3Width,
    // 下線部分4行目幅
    day4Width: refValue.value!.day4Width,
    // 表示行数
    dispKbn: refValue.value!.dispKbn,
    // 更新回数
    modifiedCnt: refValue.value!.modifiedCnt,
    // 帳票コード
    chohyoCd: refValue.value!.chohyoCd,
  }
  await ScreenRepository.update('approvalColumnRegistrationUpdate', param)
  // 画面項目値に変更が無い場合
  setState({ isOpen: false })
}

/**
 * AC022_「閉じるボタン」押下
 *
 */
function close() {
  // 画面項目値に変更がある場合
  if (isEdit.value) {
    // 判断結果コードが０の場合
    if (!hasView) {
      // メッセージを表示する。
      showOr21814MsgTwoBtn(t('message.i-cmn-10006'))
    } else {
      // 判断結果コードが０以外の場合
      // メッセージを表示する。
      showOr21814MsgThreeBtn(t('message.i-cmn-10430'))
    }
  } else {
    // 画面項目値に変更が無い場合
    setState({ isOpen: false })
  }
}

/**
 * 確認ダイアログの開閉
 *
 * @param errormsg - Message
 */
function showOr21814MsgTwoBtn(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: errormsg,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 *  refValueを更新する
 */
function setRefValue() {
  useScreenStore().setCpTwoWay({
    cpId: OrX0135Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: refValue.value,
    isInit: true,
  })
}

/**
 * 確認ダイアログの開閉
 *
 * @param errormsg - Message
 */
function showOr21814MsgThreeBtn(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: errormsg,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 *  ドラッグ開始
 *
 * @param event -event
 *
 * @param index -承認欄行目
 */
const startDrag = (event: { clientX: number }, index: number) => {
  isDragging.value = true
  currentDraggIngInex = index
  startX[index] = event.clientX
  startLeftInputWidth[index] = Number(
    localOneway.approvalColumnInputsList[index].leftMo00045.inputComponent.width?.replace(/px$/, '')
  )
  // イベントリスナーをバインドする
  document.addEventListener('mousemove', onDrag)
  document.addEventListener('mouseup', stopDrag)
}

/**
 *  ドラッグ中
 *
 */
const onDrag = (event: { clientX: number }) => {
  if (!isDragging.value) return
  // 差分
  const diff = event.clientX - startX[currentDraggIngInex]
  // 新しい左側の input の幅
  const newLeftWidth = startLeftInputWidth[currentDraggIngInex] + diff
  // 現在非表示にする対象の倫理名を取得する
  const leftKey_title = 'text' + (currentDraggIngInex + 1) + 'KnjFlg'
  const rightKey_title = 'day' + (currentDraggIngInex + 1) + 'KnjFlg'
  if (newLeftWidth >= WIDTH_1190_PX) {
    // 左侧宽度大于1190 的情况，此时只显示左侧组件，右侧组件非表示
    // 感度を10px増加して、より簡単にトリガーできるようにする。
    showFlgObject[rightKey_title] = false
    localOneway.approvalColumnInputsList[currentDraggIngInex].leftMo00045.inputComponent.width =
      WIDTH_1200_PX + WIDTH_PX
    textRenderStyle[currentDraggIngInex].leftStyle.width = WIDTH_1200_PX + WIDTH_PX
    // ハンドルの位置を更新する
    draggingStyleList[currentDraggIngInex].left = WIDTH_1200_PX + OFFSET_6 + WIDTH_PX
  } else if (WIDTH_10_PX <= newLeftWidth && newLeftWidth < WIDTH_1190_PX) {
    //10から1190の範囲内で、左側と右側のコンポーネントが同時に表示される場合
    showFlgObject[rightKey_title] = true
    showFlgObject[leftKey_title] = true
    localOneway.approvalColumnInputsList[currentDraggIngInex].leftMo00045.inputComponent.width =
      newLeftWidth + WIDTH_PX
    textRenderStyle[currentDraggIngInex].leftStyle.width = newLeftWidth + WIDTH_PX
    // 右側の幅 = コンテナの総幅 − 左側の幅
    localOneway.approvalColumnInputsList[currentDraggIngInex].rightMo00045.inputComponent.width =
      WIDTH_1200_PX - newLeftWidth + WIDTH_PX
    textRenderStyle[currentDraggIngInex].rightStyle.width = WIDTH_1200_PX - newLeftWidth + WIDTH_PX
    // ハンドルの位置を更新する
    const handlePosition = newLeftWidth + OFFSET_6
    draggingStyleList[currentDraggIngInex].left = handlePosition + WIDTH_PX
  } else if (newLeftWidth < WIDTH_10_PX) {
    // 左側が10未満の場合
    // 左側非表示
    showFlgObject[leftKey_title] = false
    localOneway.approvalColumnInputsList[currentDraggIngInex].rightMo00045.inputComponent.width =
      WIDTH_1200_PX + WIDTH_PX
    textRenderStyle[currentDraggIngInex].rightStyle.width = WIDTH_1200_PX + WIDTH_PX
    // ハンドルの位置を更新する
    draggingStyleList[currentDraggIngInex].left = WIDTH_0_PX + WIDTH_PX
  }
}

/**
 *  データ処理（しょり
 *
 * @param  data - データベースの返り値
 */
const processData = (data: ShoninList | Or52916Type) => {
  // データ処理
  localOneway.approvalColumnInputsList.forEach((item, index) => {
    // 左側の入力の幅を調整する
    const resLeftWidth = data?.[item.leftMo00045.widthKey] ?? WIDTH_0_PX
    // 右側の入力の幅を調整する
    const resRightWidth = data?.[item.rightMo00045.widthKey] ?? WIDTH_0_PX
    // 左側の内容
    const leftknj = data?.[item.leftMo00045.leftName] ?? ''
    // 右側の内容
    const rightKnj = data?.[item.rightMo00045.rightName] ?? ''
    // 左側を表示するかどうか
    const isShowLeft = Number(resLeftWidth) !== WIDTH_0_PX
    // 右側を表示するかどうか
    const isShowRight = Number(resRightWidth) !== WIDTH_0_PX
    // 各行の入力フォームの幅を設定
    item.leftMo00045.inputComponent.width = resLeftWidth + WIDTH_PX
    item.rightMo00045.inputComponent.width = resRightWidth + WIDTH_PX
    // 各行のプレビュー文字の幅を設定
    textRenderStyle[index].leftStyle.width = resLeftWidth + WIDTH_PX
    textRenderStyle[index].rightStyle.width = resRightWidth + WIDTH_PX
    //ドラッグハンドルスタイル設定
    draggingStyleList[index].left = Number(resLeftWidth) + (isShowLeft ? OFFSET_6 : 0) + WIDTH_PX
    //表示設定
    showFlgObject[item.leftMo00045.leftName + 'Flg'] = isShowLeft
    showFlgObject[item.rightMo00045.rightName + 'Flg'] = isShowRight
    // refValueを更新する
    // 文字の幅
    refValue.value![item.leftMo00045.widthKey] = resLeftWidth
    refValue.value![item.rightMo00045.widthKey] = resRightWidth
    //文字の内容
    ;(refValue.value![item.leftMo00045.leftName] as Mo00045Type).value = leftknj
    ;(refValue.value![item.rightMo00045.rightName] as Mo00045Type).value = rightKnj
    // 文字のサイズ
    refValue.value![item.textKey] = data[item.textKey]
  })
  // refValueの残余引数を更新する
  // 表示行数
  refValue.value!.dispKbn = data.dispKbn
  refValue.value!.modifiedCnt = data.modifiedCnt
  // 帳票コード
  refValue.value!.chohyoCd = data.chohyoCd
}

/**
 *  ドラッグ停止
 *
 */
const stopDrag = () => {
  isDragging.value = false
  // イベントリスナーを解除する
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('mouseup', stopDrag)
  const getCurrentLeftKey: string =
    localOneway.approvalColumnInputsList[currentDraggIngInex].leftMo00045.widthKey
  const getCurrentRightKey: string =
    localOneway.approvalColumnInputsList[currentDraggIngInex].rightMo00045.widthKey
  // refValueを更新する
  // 左側の入力の幅を更新する
  refValue.value![getCurrentLeftKey] =
    localOneway.approvalColumnInputsList[
      currentDraggIngInex
    ].leftMo00045.inputComponent.width?.replace(/px$/, '') ?? EMPTY
  // 右側の入力の幅を更新する
  refValue.value![getCurrentRightKey] =
    localOneway.approvalColumnInputsList[
      currentDraggIngInex
    ].rightMo00045.inputComponent.width?.replace(/px$/, '') ?? EMPTY
}

/**************************************************
 * ウォッチャー
 **************************************************/

/**
 * AC002_「×ボタン」押下
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    mo00024.value.isOpen = true
    if (!newValue) {
      // 本画面を閉じる。（AC022と同じ）
      close()
    }
  }
)

/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  (newValue) => {
    if (!newValue) return
    // 判断結果コードが０の場合
    if (!hasView) {
      if (newValue.firstBtnClickFlg) {
        // はい：画面を閉じる。
        setState({ isOpen: false })
      } else {
        // いいえ：処理終了
        return
      }
    } else {
      // 判断結果コードが０以外の場合
      if (newValue.firstBtnClickFlg) {
        // はい：確定処理を実行し（AC023-1-2と同じ）、画面を閉じる
        void shoninDataSave()
      } else if (newValue.secondBtnClickFlg) {
        // いいえ：承認欄登録画面を閉じる。
        setState({ isOpen: false })
      } else {
        // キャンセル：処理終了
        return
      }
    }
  }
)

/**
 *  or52916コンポーネントの返却データ監視
 *
 */
watch(
  () => local.or52916ModelValue,
  (newVal) => {
    // データ処理
    processData(newVal)
  }
)
</script>
<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <div class="d-flex justify-end">
        <!-- 複写ボタン Mo00610 -->
        <base-mo00610
          v-bind="localOneway.mo00610DuplicateBtnOneWay"
          class="mx-2"
          @click="duplicateBtn"
        >
          <!--ツールチップ表示："表示されているデータを複写します"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.care-plan2-copy-btn')"
            open-delay="200"
          />
        </base-mo00610>
        <!-- 初期値ボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611InitialValueBtnOneWay"
          @click="onInit"
        >
          <!--ツールチップ表示："初期値に戻します"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.frame-Width-modified-default-value')"
            open-delay="200"
          />
        </base-mo00611>
      </div>
      <hr class="v-divider mt-2" />
      <!-- 承認欄登録コンテナ -->
      <div
        v-if="refValue"
        class="dic-radius-1 mt-2"
      >
        <template
          v-for="(item, index) in localOneway.approvalColumnInputsList"
          :key="index"
        >
          <div v-show="index < Number(refValue?.dispKbn)">
            <div class="d-flex pt-2 px-2 align-center resize-container">
              <!-- 承認欄1行目下線無 -->
              <base-mo00045
                v-show="showFlgObject[item.leftMo00045.leftName + 'Flg']"
                v-model="refValue[item.leftMo00045.leftName]"
                :oneway-model-value="item.leftMo00045.inputComponent"
                class="bordered-transparent"
                :class="{
                  'font-size-9': refValue?.[item.textKey] === FONT_SIZE_9,
                  'font-size-10': refValue?.[item.textKey] === FONT_SIZE_10,
                  'font-size-11': refValue?.[item.textKey] === FONT_SIZE_11,
                  'font-size-12': refValue?.[item.textKey] === FONT_SIZE_12,
                }"
              />

              <!-- 可拖拽的按钮 -->
              <div
                :style="draggingStyleList[index]"
                class="resize-button"
                @mousedown="(e) => startDrag(e, index)"
              ></div>

              <!-- 承認欄1行目下線有 -->
              <base-mo00045
                v-show="showFlgObject[item.rightMo00045.rightName + 'Flg']"
                v-model="refValue[item.rightMo00045.rightName]"
                :oneway-model-value="item.rightMo00045.inputComponent"
                class="bordered-black"
                :class="{
                  'font-size-9': refValue?.[item.textKey] === FONT_SIZE_9,
                  'font-size-10': refValue?.[item.textKey] === FONT_SIZE_10,
                  'font-size-11': refValue?.[item.textKey] === FONT_SIZE_11,
                  'font-size-12': refValue?.[item.textKey] === FONT_SIZE_12,
                }"
              />
            </div>
          </div>
        </template>
      </div>
      <!-- 印刷時のイメージラベル -->
      <div v-if="refValue">
        <base-mo-01338 :oneway-model-value="localOneway.mo01338PrintingImageOneway" />
        <div class="dic-radius-2">
          <!-- 承認欄 -->
          <template
            v-for="(item, index) in localOneway.approvalColumnInputsList"
            :key="index"
          >
            <div v-show="index < Number(refValue?.dispKbn)">
              <div class="d-flex pt-2 pl-2 height-30">
                <!-- 承認欄下線無プレビューラベル -->
                <div
                  v-show="showFlgObject[item.leftMo00045.leftName + 'Flg']"
                  :style="textRenderStyle[index].leftStyle"
                  class="overflow-hidden text-no-wrap pl-2 mr-3"
                  :class="{
                    'font-size-9': refValue?.[item.textKey] === FONT_SIZE_9,
                    'font-size-10': refValue?.[item.textKey] === FONT_SIZE_10,
                    'font-size-11': refValue?.[item.textKey] === FONT_SIZE_11,
                    'font-size-12': refValue?.[item.textKey] === FONT_SIZE_12,
                  }"
                >
                  {{ (refValue?.[item.leftMo00045.leftName] as Mo00045Type)!.value ?? '' }}
                </div>

                <!-- 承認欄下線有プレビューラベル -->
                <div
                  v-show="showFlgObject[item.rightMo00045.rightName + 'Flg']"
                  :style="textRenderStyle[index].rightStyle"
                  class="bordered-black overflow-hidden text-no-wrap pl-2"
                  :class="{
                    'font-size-9': refValue?.[item.textKey] === FONT_SIZE_9,
                    'font-size-10': refValue?.[item.textKey] === FONT_SIZE_10,
                    'font-size-11': refValue?.[item.textKey] === FONT_SIZE_11,
                    'font-size-12': refValue?.[item.textKey] === FONT_SIZE_12,
                  }"
                >
                  {{ (refValue?.[item.rightMo00045.rightName] as Mo00045Type).value ?? '' }}
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
      <div
        v-if="refValue"
        class="radio-group"
      >
        <c-v-row
          no-gutters
          class="pt-2 aling-center"
        >
          <c-v-col cols="atuo">
            <!-- 表示する行数 -->
            <base-mo00039
              v-model="refValue!.dispKbn"
              class="radio-show-row"
              :oneway-model-value="localOneway.mo00039ShowRowNumOneway"
            >
              <base-at-radio
                v-for="item in localOneway.mo00039ShowRowNumberOneway"
                :key="item.value"
                :name="item.label"
                :radio-label="item.label"
                :value="item.value"
              />
            </base-mo00039>
          </c-v-col>
        </c-v-row>
        <c-v-row
          v-for="(item, index) in localOneway.approvalColumnRadioList"
          :key="index"
          no-gutters
          class="pt-2 aling-center"
        >
          <c-v-col
            v-show="index < Number(refValue?.dispKbn)"
            cols="atuo"
          >
            <!-- 1行目文字サイズ -->
            <base-mo00039
              v-model="refValue[`text${index + 1}Font`]"
              :oneway-model-value="item"
              class="radio-show-row"
            >
              <base-at-radio
                v-for="el in localOneway.mo00039FontSizeOneway"
                :key="el.value"
                :name="el.label"
                :radio-label="el.label"
                :value="el.value"
              />
            </base-mo00039>
          </c-v-col>
        </c-v-row>
      </div>
      <div class="d-flex justify-end">
        <div class="pt-2">
          <div>
            <!-- テキストボックスに直接入力してくださいラベル -->
            <base-mo-01338 :oneway-model-value="localOneway.mo01338Text1Oneway" />
          </div>
          <div class="pt-2">
            <!-- テキストボックス境界線をドラッグして、下線なし/あり部分の割合を変更することができます -->
            <base-mo-01338 :oneway-model-value="localOneway.mo01338Text2Oneway" />
          </div>
          <div class="pt-2">
            <!-- 帳票に印刷される承認欄は「印刷時のイメージ」で確認して下さいラベル -->
            <base-mo-01338 :oneway-model-value="localOneway.mo01338Text3Oneway" />
          </div>
        </div>
      </div>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close()"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <!-- 確定ボタン Mo00609 -->
        <base-mo00609
          v-bind="localOneway.mo00609SaveBtnOneway"
          @click="shoninDataSave"
        >
          <!--ツールチップ表示："設定を確定します"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.confirm-btn')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-show="showDialog21814"
    v-bind="or21814"
  />
  <!-- GUI00618_承認欄の複写画面 -->
  <g-custom-or-52916
    v-if="showDialogOr52916"
    v-bind="or52916"
    v-model="local.or52916ModelValue"
    :oneway-model-value="localOneway.or52916Data"
  />
</template>
<style scoped lang="scss">
.dic-radius-1 {
  border-radius: 5px;
  border: 1px solid rgb(var(--v-theme-black-100));
  width: fit-content;
  height: 190px;
}

.height-30 {
  height: 30px;
}

.dic-radius-2 {
  border-radius: 5px;
  border: 1px solid rgb(var(--v-theme-black-100));
  width: 100%;
  height: 130px;
}
.radio-group {
  height: 205px;
}

.radio-show-row {
  display: flex;
  align-items: center;
  :deep(.itemLabel) {
    width: 10%;
    flex-grow: unset;
  }
  :deep(label) {
    width: 55px;
  }
}

:deep(.font-size-9 input),
.font-size-9 {
  font-size: 9px;
}

:deep(.font-size-10 input),
.font-size-10 {
  font-size: 10px;
}

:deep(.font-size-11 input),
.font-size-11 {
  font-size: 11px;
}

:deep(.font-size-12 input),
.font-size-12 {
  font-size: 12px;
}

.bordered-black {
  border-bottom: 1px solid rgb(var(--v-theme-black-800));
}
.bordered-transparent {
  border-bottom: 1px solid transparent;
}
.resize-button {
  transition: width 0.2s ease;
  cursor: ew-resize;
  position: absolute;
  width: 8px;
  height: 38px;
  z-index: 1;
  background-color: rgb(var(--v-theme-yellow-400));
  border: 1px solid rgb(var(--v-theme-black-300));
  border-radius: 4px;
}
.resize-container {
  position: relative;
}
</style>
