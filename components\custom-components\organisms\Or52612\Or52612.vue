<script setup lang="ts">
/**
 * Or52612:［表示設定］画面
 * GUI00625_［表示設定］画面
 *
 * @description
 * GUI00625_［表示設定］画面
 *
 * <AUTHOR>
 */

import { useI18n } from 'vue-i18n'
import { reactive, ref, onMounted, watch } from 'vue'
import type { displaySettingReqOutEntity } from '../../../../repositories/cmn/entities/DisplayConfigureInitSelectEntity'
import type {
  displaySettingDataItem,
  displaySettingReqDataItem,
  Or52612StateType,
} from './Or52612.type'
import { Or52612Const } from './Or52612.constants'
import { useColorUtils, useScreenOneWayBind } from '#imports'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Or52612OnewayType } from '~/types/cmn/business/components/Or52612Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  displaySettingInEntity,
  displaySettingOutEntity,
} from '~/repositories/cmn/entities/DisplayConfigureInitSelectEntity'
import type {
  Mo01354Headers,
  Mo01354OnewayType,
  Mo01354Type,
} from '~/components/base-components/molecules/Mo01354/Mo01354Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CustomClass } from '~/types/CustomClassType'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import type { Mo01360OnewayType } from '~/types/business/components/Mo01360Type'
const { convertDecimalToHex, convertHexToDecimal } = useColorUtils()
const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: displaySettingDataItem
  onewayModelValue: Or52612OnewayType
  uniqueCpId: string
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const defaultOnewayModelValue = {
  defaultData: {
    svJigyoId: '0',
  },
}
const local = reactive({
  // 宛先の初期値
  defaultDestination: '1' as string | undefined,
})
//全体の判断display
const showFlg = ref(true)

// システム共有情報取得
const systemCommonsStore = useSystemCommonsStore()

const mo01354Oneway = ref<Mo01354OnewayType>({
  rowHeight: '32px',
  /** 初期値：ヘッダー情報 */
  headers: [
    // 番号
    {
      title: t('label.number'),
      key: 'recno',
      minWidth: '75px',
      sortable: false,
    },
    // 予定名称
    {
      title: t('label.schedule-name'),
      key: 'recName',
      minWidth: '186px',
      width: '186px',
      sortable: false,
    },
    //表示
    {
      title: t('label.display'),
      key: 'dmyUseKbn',
      minWidth: '204px',
      sortable: false,
    },
    //表示色
    {
      title: t('label.display-color'),
      key: 'dmyDispCd',
      minWidth: '120px',
      sortable: false,
    },
  ] as Mo01354Headers[],
  height: '264.3px',
})

const localOneway = reactive({
  or52612: {
    ...defaultOnewayModelValue.defaultData,
    ...props.onewayModelValue,
  },
  defaultDestinationCategory: {
    name: 'defaultDestinationCategory',
    showItemLabel: false,
    hideDetails: true,
    customClass: new CustomClass({
      outerClass: 'pt-1',
    }),
  } as Mo00039OnewayType,

  or52612OnewayTypeAtesakiRadioBtn: [
    {
      label: t('label.facility-destination'),
      value: '0',
    },
    {
      label: t('label.office-destination'),
      value: '1',
    },
  ],
  //宛先の初期値
  mo01299Oneway: {
    title: t('label.destination-default'),
  },
  mo01360oneway: {
    items: [
      {
        label: t('label.display'),
        value: '0',
      },
      {
        label: t('label.hidden'),
        value: '1',
      },
    ],
  } as Mo01360OnewayType,
  //職員単位単位での保存
  mo01338onewayOfficeUnit: {
    value: t('label.staff-unit-save'),
    customClass: {
      itemClass: 'footerLabel',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00039Oneway: {
    name: '',
    itemLabel: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  mo00024Oneway: {
    width: '550px',
    persistent: true,
    showCloseBtn: true,
    tooltipTextCloseBtn: t('label.close'),
    mo01344Oneway: {
      toolbarTitle: t('label.display-configure'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
    },
  },
})
const tableData = ref<Mo01354Type>({
  values: {
    selectedRowId: '0',
    selectedRowIds: [],
    items: [] as displaySettingDataItem[],
  },
})
// 閉じるボタン設置
const mo00611Oneway: Mo00611OnewayType = {
  btnLabel: t('btn.close'),
  width: '90px',
  tooltipText: t('tooltip.screen-close'),
}

// 確定ボタン設置
const mo00609Oneway: Mo00609OnewayType = {
  btnLabel: t('btn.confirm'),
  width: '90px',
  tooltipText: t('tooltip.confirm-settings'),
}

// ダイアログ設置
const mo00024 = ref<Mo00024Type>({
  isOpen: Or52612Const.DEFAULT.IS_OPEN,
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or52612StateType>({
  cpId: Or52612Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or52612Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  if (localOneway.or52612.svJigyoId === '0') {
    showFlg.value = true
  } else {
    showFlg.value = false
  }

  // 初期情報取得
  await getInitDataInfo()
})

/** 初期情報取得 */
const getInitDataInfo = async () => {
  const jigyoList = systemCommonsStore.getSvJigyoCdList.map((item) => {
    return {
      svJigyoCd: item,
    }
  })
  // バックエンドAPIから初期情報取得
  const inputData: displaySettingInEntity = {
    svJigyoId: localOneway.or52612.svJigyoId,
    jigyoList: jigyoList,
    shokuinId: '1',
    sysCd: '1',
  }

  const resData: displaySettingOutEntity = await ScreenRepository.select(
    'displayConfigureInitSelect',
    inputData
  )
  const dataList = resData.data.detailList
  // データ情報設定
  tableData.value.values.items = dataList.map((item) => {
    return {
      ...item,
      id: item.recno,
      dmyDispCd: convertDecimalToHex(Number(item.dmyDispCd)),
      dmyUseKbn: { value: item.dmyUseKbn },
    }
  })
  local.defaultDestination = resData.data.updateType
}

/**
 * 「確定」ボタン押下
 */
const onConfirmBtn = async () => {
  const detailList: displaySettingReqDataItem[] = tableData.value.values.items.map((items) => {
    const item = items as displaySettingDataItem
    return {
        recno: item.recno,
        dmyUseKbn: item.dmyUseKbn.value,
        dmyDispCd: convertHexToDecimal(item.dmyDispCd),
      }
  })
  const inputData: displaySettingReqOutEntity = {
    svJigyoId: localOneway.or52612.svJigyoId,
    shokuinId: systemCommonsStore.getStaffId ?? '1',
    hyoujiSetList: detailList,
    defaultAtesaki: local.defaultDestination,
    sysCd: systemCommonsStore.getSystemCode ?? '1',
  }
  await ScreenRepository.update('displayConfigureUpdate', inputData)
  onClickCloseBtn()
}

/**
 * 閉じるボタン押下時
 */
const onClickCloseBtn = (): void => {
  setState({ isOpen: false })
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      onClickCloseBtn()
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet>
        <c-v-row no-gutters>
          <c-v-col
            cols="12"
            class="table-header"
          >
            <base-mo-01354
              v-model="tableData"
              class="list-wrapper"
              hide-default-footer
              :oneway-model-value="mo01354Oneway"
            >
              <template #[`item.recno`]="{ item }">
                <base-mo-01337
                  class="text-right"
                  :oneway-model-value="{ value: item.recno }"
                />
              </template>
              <template #[`item.recName`]="{ item }">
                <base-mo-01337
                  class="px-4"
                  :oneway-model-value="{ value: item.recName }"
                />
              </template>
              <template #[`item.dmyUseKbn`]="{ item }">
                <div class="px-2 usekbn">
                  <base-mo01360
                    v-model="item.dmyUseKbn"
                    :oneway-model-value="localOneway.mo01360oneway"
                    class="radio"
                  />
                </div>
              </template>
              <template #[`item.dmyDispCd`]="{ item }">
                <div class="px-4 dispcd">
                  <g-custom-or-26281 v-model="item.dmyDispCd" />
                </div>
              </template>
            </base-mo-01354>
          </c-v-col>
        </c-v-row>
        <div
          v-show="showFlg"
          class="mt-2 show-height"
        >
          <base-mo01338
            class="mb-2"
            :oneway-model-value="localOneway.mo01338onewayOfficeUnit"
          ></base-mo01338>
          <base-mo01299
            :oneway-model-value="localOneway.mo01299Oneway"
            class="padding-none"
          >
            <template #content>
              <div class="radio-w">
                <c-v-row
                  no-gutters
                  class="pa-0"
                >
                  <base-mo00039
                    v-model="local.defaultDestination"
                    :oneway-model-value="localOneway.defaultDestinationCategory"
                  >
                    <base-at-radio
                      v-for="(item, index) in localOneway.or52612OnewayTypeAtesakiRadioBtn"
                      :key="index"
                      :name="'radio' + index"
                      :value="item.value"
                      :radio-label="item.label"
                    ></base-at-radio>
                  </base-mo00039>
                </c-v-row>
              </div>
            </template>
          </base-mo01299>
        </div>
        <base-mo01338
          class="mt-2"
          :oneway-model-value="localOneway.mo01338onewayOfficeUnit"
        />
      </c-v-sheet>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row
        no-gutters
        class="text-right px-0"
      >
        <c-v-col>
          <c-v-spacer />
          <!-- 閉じるボタン -->
          <base-mo00611
            :oneway-model-value="mo00611Oneway"
            class="mr-2"
            @click="onClickCloseBtn"
          >
          </base-mo00611>
          <!-- 確定ボタン-->
          <base-mo00609
            :oneway-model-value="mo00609Oneway"
            @click="onConfirmBtn"
          >
          </base-mo00609>
        </c-v-col>
      </c-v-row>
    </template>
  </base-mo00024>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table.scss';

.table-header :deep(.v-table__wrapper) {
  border-bottom: 1px solid rgb(var(--v-theme-light), 0.55) !important;
}
.table-header {
  box-sizing: border-box !important;
}

:deep(.h-100 .flex-space-between) {
  height: 100%;
}
:deep(.devider) {
  margin-top: 1px;
}
.radio-w {
  width: 116px;
}
.show-height {
  height: 112px;
}
.text-right {
  text-align: right;
  padding: 0 16px;
}
:deep(.color-picker) {
  margin: 0 4px !important;
}
:deep(.view) {
  width: 45px !important;
}
div:has(> .footerLabel) {
  :nth-child(2) {
    :first-child {
      :first-child {
        color: #808080;
      }
    }
  }
}
.usekbn {
  height: 31px !important;
}
.padding-none > :first-child {
  padding: 0 !important;
}
:deep(.footerLabel > div) {
  padding: 0 !important;
}
.background-none {
  background: none;
}
:deep(.color-picker) {
  padding-left: 12px;
}
.table-header :deep(.v-table__wrapper .selected-row) {
  background-color: transparent !important;
}
:deep(.dispcd > div > div.color-picker.mr-1) {
  margin: 0px !important;
  padding: 0px !important;
}
:deep(.radio > div) {
  padding: 0px !important;
}
:deep(.radio .v-radio) {
  height: 31px !important;
  min-height: 31px !important;
}
:deep(.radio .v-selection-control--density-default) {
  --v-selection-control-size: 21px !important
;
}
.radio {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
:deep(.dispcd > div > div:last-child button) {
  height: 31px;
}
:deep(.dispcd > div > div:last-child) {
  height: 31px;
}
:deep(.dispcd > div) {
  display: flex;
  align-items: center;
}

:deep(.v-table--density-compact) {
  --v-table-row-height: 31px !important;
}
</style>
