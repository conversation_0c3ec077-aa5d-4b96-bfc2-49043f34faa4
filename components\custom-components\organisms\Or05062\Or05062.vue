<script setup lang="ts">
/**
 * Or05062:(見通し)行のアクションボタン
 * GUI00916_見通し
 *
 * @description
 * (見通し)行のアクションボタン
 *
 * <AUTHOR> NGUYEN MANH HUNG
 */
import { useI18n } from 'vue-i18n'
import { computed, reactive, ref } from 'vue'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: {
    selectedIndex: number
    totalLine: number
  }
}
const props = defineProps<Props>()
/**
 * useI18n
 */
const { t } = useI18n()
const emit = defineEmits(['click'])
/**
 *  ローカルOneway
 */
const localOneway = reactive({
  mo00009OnewayAssessment: {
    btnIcon: 'edit_square',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
    tooltipText: t('tooltip.displays-the-assessment-import-screen'),
  } as Mo00009OnewayType,
  mo00009OnewayStatusFactReference: {
    btnIcon: 'edit_square',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
    tooltipText: t('tooltip.displays-the-status-fact-reference-screen'),
  } as Mo00009OnewayType,
  mo00009OnewayDisplaysTheNotesScreen: {
    btnIcon: 'edit_square',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
    tooltipText: t('tooltip.displays-the-notes-screen'),
  } as Mo00009OnewayType,
  mo00009OnewaySortby: {
    btnIcon: 'edit_square',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
    tooltipText: t('tooltip.display-order-dialog'),
  } as Mo00009OnewayType,
  mo00611OnewayAdd: {
    btnLabel: t('btn.add'),
    prependIcon: 'add',
    tooltipText: t('tooltip.add-row'),
  } as Mo00611OnewayType,
  mo00611OnewayInsert: {
    btnLabel: t('btn.insert'),
    prependIcon: 'add',
    tooltipText: t('tooltip.insert-row'),
  } as Mo00611OnewayType,
  mo00611OnewayDuplicate: {
    btnLabel: t('btn.duplicate-row'),
    prependIcon: 'content_copy',
    tooltipText: t('tooltip.duplicate-row'),
  } as Mo00611OnewayType,
  mo01265OnewayDelete: {
    btnLabel: t('btn.delete-row'),
    prependIcon: 'delete',
    tooltipText: t('tooltip.delete-row'),
  } as Mo01265OnewayType,
  or28285Oneway: {
    btnIcon: 'edit_square',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
    tooltipText: t('tooltip.care-plan2-select-next'),
  } as Mo00009OnewayType,
})

/**
 * フォーム値のローカル状態
 */
const local = reactive({})

/**
 * or26184コンポーネントの状態
 */
const or26184 = ref({
  selectedIndex: computed(() => props.modelValue.selectedIndex || 0),
  totalLine: computed(() => props.modelValue.totalLine || 0),
})
/** 表示頁ラベルの単方向モデル */
const mo01338OnewayAssessment = ref<Mo01338OnewayType>({
  value: t('label.assessment'),
  customClass: new CustomClass({
    outerClass: 'm-0 p-0',
    labelClass: 'm-0 p-0',
    itemClass: 'm-0 p-0',
  }),
})
/** 表示頁ラベルの単方向モデル */
const mo01338OnewayAssessmentStateFactReference = ref<Mo01338OnewayType>({
  value: t('label.state-fact-reference'),
  customClass: new CustomClass({
    outerClass: 'm-0 p-0',
    labelClass: 'm-0 p-0',
    itemClass: 'm-0 p-0',
  }),
})
/** 表示頁ラベルの単方向モデル */
const mo01338OnewayNotes = ref<Mo01338OnewayType>({
  value: t('label.notes'),
  customClass: new CustomClass({
    outerClass: 'm-0 p-0',
    labelClass: 'm-0 p-0',
    itemClass: 'm-0 p-0',
  }),
})
/** 表示頁ラベルの単方向モデル */
const mo01338OnewaySortby = ref<Mo01338OnewayType>({
  value: t('label.sort-by'),
  customClass: new CustomClass({
    outerClass: 'm-0 p-0',
    labelClass: 'm-0 p-0',
    itemClass: 'm-0 p-0',
  }),
})

function onClick(type: string) {
  emit('click', type)
}
</script>

<template>
  <div
    class="d-flex justify-space-between"
    style="padding-left: 8px; padding-right: 8px"
  >
    <div>
      <base-mo-00611
        :oneway-model-value="localOneway.mo00611OnewayAdd"
        class="mr-1"
        @click.stop="onClick('add')"
      />
      <base-mo-00611
        :oneway-model-value="localOneway.mo00611OnewayInsert"
        class="mx-1"
        @click.stop="onClick('insert')"
      />
      <base-mo-00611
        :oneway-model-value="localOneway.mo00611OnewayDuplicate"
        class="mx-1"
        @click.stop="onClick('duplicate')"
      />
      <!-- 分子：優先度2破壊ボタン -->
      <base-mo01265
        :oneway-model-value="localOneway.mo01265OnewayDelete"
        class="mx-1"
        @click.stop="onClick('delete')"
      />
    </div>
    <div class="d-flex align-center">
      <!-- 表示順ラベル -->
      <base-mo01338
        :oneway-model-value="mo01338OnewaySortby"
        class="mr-1"
      />
      <!-- 表示順ボタン -->
      <base-mo-00009
        :oneway-model-value="localOneway.mo00009OnewaySortby"
        class="border-left mr-1"
        @click.stop="onClick('sortby')"
      />
      <!-- アセスメントラベル -->
      <base-mo01338
        :oneway-model-value="mo01338OnewayAssessment"
        class="mr-1"
      />
      <!-- アセスメントボタン -->
      <base-mo-00009
        :oneway-model-value="localOneway.mo00009OnewayAssessment"
        class="border-left mr-1"
        @click.stop="onClick('assessment')"
      />
      <!-- 状態の事実参照ラベル -->
      <base-mo01338
        :oneway-model-value="mo01338OnewayAssessmentStateFactReference"
        class="mr-1"
      />
      <!-- 状態の事実参照ボタン -->
      <base-mo-00009
        :oneway-model-value="localOneway.mo00009OnewayStatusFactReference"
        class="border-left mr-1"
        @click.stop="onClick('status-fact-reference')"
      />
      <!-- 注釈ラベル -->
      <base-mo01338
        :oneway-model-value="mo01338OnewayNotes"
        class="mr-1"
      />
      <!-- 注釈ボタン -->
      <base-mo-00009
        :oneway-model-value="localOneway.mo00009OnewayDisplaysTheNotesScreen"
        class="border-left mr-2"
        @click.stop="onClick('notes')"
      />
      <!-- 'データ-ページング -->
      <g-custom-or-26184
        v-model="or26184"
        @up="onClick('up')"
        @down="onClick('down')"
      />
    </div>
  </div>
</template>
<style scoped>
.border-left {
  border-left: 1px solid rgba(var(--v-theme-black-300));
  border-radius: 0 !important;
  align-self: center;
}
</style>
