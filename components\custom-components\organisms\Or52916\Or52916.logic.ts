import { Or52916Const } from './Or52916.constants'
import type { Or52916StateType } from './Or52916.type'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'

/**
 * Or52916:有機体:承認欄の複写画面
 * GUI00618_承認欄の複写画面
 *
 * @description
 * initialize処理とpinia領域操作用の処理を提供する
 *
 * <AUTHOR>
 */
export namespace Or52916Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or52916Const.CP_ID(0),
      uniqueCpId,
      initTwoWayValue: {
        items: [],
      },
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [],
    })

    // 子コンポーネントのセットアップ

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }

  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or52916StateType>(Or52916Const.CP_ID(0))
}
