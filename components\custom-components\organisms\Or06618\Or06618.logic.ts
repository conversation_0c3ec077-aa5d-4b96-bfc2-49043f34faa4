import { Or01080Const } from '../Or01080/Or01080.constants'
import { Or01080Logic } from '../Or01080/Or01080.logic'
import { Or01082Const } from '../Or01082/Or01082.constants'
import { Or01082Logic } from '../Or01082/Or01082.logic'
import { Or01083Const } from '../Or01083/Or01083.constants'
import { Or01083Logic } from '../Or01083/Or01083.logic'
import { Or01084Const } from '../Or01084/Or01084.constants'
import { Or01084Logic } from '../Or01084/Or01084.logic'
import { Or01085Const } from '../Or01085/Or01085.constants'
import { Or01085Logic } from '../Or01085/Or01085.logic'
import { Or01086Const } from '../Or01086/Or01086.constants'
import { Or01086Logic } from '../Or01086/Or01086.logic'
import { Or28214Const } from '../Or28214/Or28214.constants'
import { Or28214Logic } from '../Or28214/Or28214.logic'
import { Or06618Const } from './Or06618.constants'
import type { Or06618StateType } from './Or06618.type'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'

/**
 * Or06618:有機体:(日課計画マスタ)コンテンツエリアタブ
 * GUI01056_日課計画マスタ
 *
 * @description
 * 処理ロジック initialize処理とpinia領域操作用の処理を提供する
 *
 * <AUTHOR>
 */
export namespace Or06618Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or06618Const.CP_ID(0),
      uniqueCpId,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [
        { cpId: Or01080Const.CP_ID(0) },
        { cpId: Or01082Const.CP_ID(0) },
        { cpId: Or01083Const.CP_ID(0) },
        { cpId: Or01084Const.CP_ID(0) },
        { cpId: Or01085Const.CP_ID(0) },
        { cpId: Or01086Const.CP_ID(0) },
        { cpId: Or28214Const.CP_ID(1) },
      ],
      // 編集フラグ不要
      // ※ 元々双方向領域を持たないため記載不要だが、サンプル用にナビゲーション制御領域で持つデータを分かりやすくするために設定
    })

    // 子コンポーネントのセットアップ
    Or01080Logic.initialize(childCpIds[Or01080Const.CP_ID(0)].uniqueCpId)
    Or01082Logic.initialize(childCpIds[Or01082Const.CP_ID(0)].uniqueCpId)
    Or01083Logic.initialize(childCpIds[Or01083Const.CP_ID(0)].uniqueCpId)
    Or01084Logic.initialize(childCpIds[Or01084Const.CP_ID(0)].uniqueCpId)
    Or01085Logic.initialize(childCpIds[Or01085Const.CP_ID(0)].uniqueCpId)
    Or01086Logic.initialize(childCpIds[Or01086Const.CP_ID(0)].uniqueCpId)
    Or28214Logic.initialize(childCpIds[Or28214Const.CP_ID(1)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }
  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or06618StateType>(Or06618Const.CP_ID(0))
}
