import { Or53105Const } from '../Or53105/Or53105.constants'
import { Or53105Logic } from '../Or53105/Or53105.logic'
import { Or34069Const } from '../Or34069/Or34069.constants'
import { OrX0096Const } from '../OrX0096/OrX0096.constants'
import { Or34069Logic } from '../Or34069/Or34069.logic'
import { OrX0096Logic } from '../OrX0096/OrX0096.logic'
import { Or03244Const } from './Or03244.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { useInitialize, useTwoWayBindAccessor } from '~/composables/useComponentLogic'
import type { Or03244DataType } from '~/types/cmn/business/components/Or03244Type'
/**
 * Or03249:有機体:［アセスメント（包括）］画面 心理
 * ［アセスメント（包括）］画面 心理
 *
 * @description
 * 処理ロジック initialize処理とpinia領域操作用の処理を提供する
 *
 * <AUTHOR>
 */
export namespace Or03244Logic {
  /**
   * initialize
   *
   * @description
   * コンポーネントの初期化処理。
   * 共通関数を呼びpiniaの領域を初期化する。
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or03244Const.CP_ID(0),
      uniqueCpId,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [
        { cpId: Or34069Const.CP_ID(6) },
        { cpId: Or53105Const.CP_ID(0) },
        { cpId: OrX0096Const.CP_ID(1) },
        { cpId: Or21814Const.CP_ID(1) },
      ],
    })

    // 子コンポーネントのセットアップ
    Or34069Logic.initialize(childCpIds[Or34069Const.CP_ID(6)].uniqueCpId)
    Or53105Logic.initialize(childCpIds[Or53105Const.CP_ID(0)].uniqueCpId)
    OrX0096Logic.initialize(childCpIds[OrX0096Const.CP_ID(1)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(1)].uniqueCpId)
    return {
      cpId,
      uniqueCpId: uniqCpId,
    }
  }

  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const data = useTwoWayBindAccessor<Or03244DataType>(Or03244Const.CP_ID(0))
}
