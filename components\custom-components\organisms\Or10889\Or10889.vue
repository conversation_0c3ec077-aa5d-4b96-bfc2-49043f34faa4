<script setup lang="ts">
/**
 * Or10889:有機体:薬剤検索マスタ
 * GUI00670_薬剤検索マスタ
 *
 * @description
 * 薬剤検索マスタ
 *
 * <AUTHOR>
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or10889Const } from './Or10889.constants'
import type { Or10889StateType, MedicineSearchMaster, DrugBun } from './Or10889.type'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Mo00045Type, Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import { useScreenOneWayBind, useSetupChildProps } from '#imports'
import type {
  RespDataInfoType,
  DrugClassificationMasterInfo,
  DrugMasterInfo,
  Or10889Type,
  Or10889OnewayType,
} from '~/types/cmn/business/components/Or10889Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type {
  treeItem,
  Mo01300OnewayType,
  Mo01300Type,
} from '~/types/business/components/Mo01300Type'
import type { Mo00039OnewayType } from '~/types/cmn/business/components/Mo00039Type'
import type { Mo01274Type } from '~/types/business/components/Mo01274Type'
import type { Mo01336OnewayType } from '~/types/business/components/Mo01336Type'
import type { Mo01278Type } from '~/types/business/components/Mo01278Type'
import { Or27751Const } from '~/components/custom-components/organisms/Or27751/Or27751.constants'
import { Or27751Logic } from '~/components/custom-components/organisms/Or27751/Or27751.logic'
import type { Or27751OnewayType } from '~/types/cmn/business/components/Or27751Type'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  IMedicationSearchMasterInEntity,
  IMedicationSearchMasterOutEntity,
} from '~/repositories/cmn/entities/MedicationSearchMasterEntity'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { ResBodyStatusCode } from '~/constants/api-constants'

const { t } = useI18n()

/**************************************************
 * POP画面ポップアップ
 **************************************************/
const or27751 = ref({ uniqueCpId: '' })

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or10889Type
  onewayModelValue: Or10889OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

const localOneway = reactive({
  or10889: {
    ...props.onewayModelValue,
  },
  // 薬剤検索マスタダイアログ
  mo00024Oneway: {
    width: '1000px',
    height: '491px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or10889',
      toolbarTitle: t('label.medicine-search-master'),
      toolbarName: 'Or10889ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
      cardTextClass: 'card-text px-2 py-0',
      height: '300px',
    },
  } as Mo00024OnewayType,
  mo00039OnewayType: {
    name: '',
    itemLabel: '',
    showItemLabel: false,
    inline: true,
    customClass: { outerClass: '', labelClass: '' },
  } as Mo00039OnewayType,
  // 検索テキストボックス
  mo00045SerchOneway: {
    disabled: false,
    showItemLabel: false,
    width: '632px',
  } as Mo00045OnewayType,
  // 絞込みボタン
  mo00611OnewayModelValue: {
    btnLabel: t('label.filter'),
    class: 'btn-height mr-2',
  } as Mo00611OnewayType,
  // マスタ他アイコンボタン
  mo00009MasterOtherOneway: {
    btnIcon: 'database',
    tooltipLocation: 'bottom',
    tooltipText: t('tooltip.others-function'),
    density: 'compact',
  },
  // 検索ボタン
  mo00611SearchOneWay: {
    width: '90px',
    btnLabel: t('btn.search'),
  } as Mo00611OnewayType,
  // 閉じるコンポーネント
  mo00611OneWay: {
    btnLabel: t('btn.close'),
    tooltipLocation: 'bottom',
    tooltipText: t('tooltip.screen-close'),
  } as Mo00611OnewayType,
  // 確定コンポーネント
  mo00609OneWay: {
    btnLabel: t('btn.confirm'),
    tooltipLocation: 'bottom',
    tooltipText: t('tooltip.confirm-btn'),
  } as Mo00609OnewayType,
  // 薬剤マスタ画面
  or27751OneWay: {
    type: '1',
  } as Or27751OnewayType,
})

const local = reactive({
  mo00039: Or10889Const.SEARCH_AT_KEYWORD,
  // 検索テキストボックス
  mo00045Search: {
    value: '',
  } as Mo00045Type,
})

// ダイアログ表示フラグ
const showDialogOr27751 = computed(() => {
  // Or27751のダイアログ開閉状態
  return Or27751Logic.state.get(or27751.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * 変数定義
 **************************************************/

const mo00024 = ref<Mo00024Type>({
  isOpen: Or10889Const.DEFAULT.IS_OPEN,
})
// "*"
const required: string = Or10889Const.DEFAULT.REQUIRED
// 薬剤マスタツリービュー
const mo01300Model = ref<Mo01300Type>({
  value: '',
  opened: [],
})
const isShow = ref(false)
const isVisible = ref(true)
// 各一覧明細情報
const respDataInfoType: RespDataInfoType = {
  drugClassificationMasterInfoList: [],
  drugMasterInfoList: [],
}
const mo01300OnewayModel = ref<Mo01300OnewayType>({
  items: [
    {
      id: Or10889Const.LEVEL_CLASSIFICATION,
      title: t('label.classification'),
      children: [],
    },
    {
      id: Or10889Const.LEVEL_ALL,
      title: t('label.level-all'),
    },
  ],
  openAll: false,
  openActivated: true,
  disabled: false,
  height: 'calc(100% - 8px)',
})
// 検索区分_リスト
const searchCategoryList = ref<{ label: string; value: string }[]>([
  {
    label: t('label.search-at-keyword'),
    value: Or10889Const.SEARCH_AT_KEYWORD,
  },
  {
    label: t('label.search-at-medical-supplies-code'),
    value: Or10889Const.SEARCH_AT_MEDICAL_SUPPLIES_CODE,
  },
])
// 検索対象区分
const searchSubjectCategory = ref('')
// 選択した行のindex
const selectedItemIndex = ref<number>(Or10889Const.UNSELECT)
/** 薬剤分類テーブルの様式 */
const medicineClassificationTablesStyle = ref({
  height: '198px',
  width: 'auto',
})
/** 薬剤検索マスタテーブルの様式 */
const medicineSearchMasterTablesStyle = ref({
  height: '213px',
})
// 薬剤分類テーブルヘッダ
const medicineClassificationTableHeaders = ref([
  { title: t('label.id'), key: 'bunruiId', minWidth: '80px' },
  {
    title: t('label.category-name'),
    key: 'n1A',
    minWidth: '550px',
    required: true,
  },
  { title: t('label.display-order'), key: 'sort', minWidth: '80px' },
])
// 薬剤検索マスタテーブルヘッダ
const medicineSearchMasterTableHeaders = ref([
  { title: t('label.id'), key: 'drugId', minWidth: '80px' },
  {
    title: t('label.medicine-name'),
    key: 'n1A',
    minWidth: '300px',
    required: true,
  },
  { title: t('label.medical-kana'), key: 'n1B', minWidth: '200px', required: true },
  { title: t('label.medical-supplies-code'), key: 'n1CKnj', minWidth: '200px' },
  { title: t('label.rece-calculation-code'), key: 'n1D', minWidth: '200px' },
  { title: t('label.agent-type'), key: 'n1E', minWidth: '120px' },
  { title: t('label.action-efficacy'), key: 'n1F', minWidth: '200px' },
  { title: t('label.display-order'), key: 'sort', minWidth: '80px' },
])

// 「剤型」の選択肢
const shapeSelectItems = ref<CodeType[]>([])

// 薬剤分類Tableのデータ
const drugBunTableData = ref<DrugBun[]>([])
// 薬剤検索マスタTableのデータ
const drugTableData = ref<MedicineSearchMaster[]>([])
// 前回値
const previousKey = ref('')
// 降順フラグ
const descFlag = ref(true)

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or10889StateType>({
  cpId: Or10889Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or10889Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or27751Const.CP_ID(1)]: or27751.value,
})

onMounted(() => {
  // 汎用コードマスタデータを取得し初期化
  void initCodes()
  init()
})

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      close()
    }
  }
)

/** 初期情報取得 */
function init() {
  try {
    void medicationSearchMasterInit()
  } catch (error) {
    console.error('Error:', error)
  }
  // 薬剤分類リストを表示
  isShow.value = true
  // 選択中の項目のIDを設定
  mo01300Model.value.value = Or10889Const.LEVEL_CLASSIFICATION
  // 展開されている項目のIDリストを設定
  mo01300Model.value.opened = new Array(mo01300Model.value.value)
  // 一行目を選択状態にする。
  selectedItemIndex.value = Or10889Const.FIRST_ROW
}

/**
 * 薬剤分類マスタ情報
 */
async function medicationSearchMasterInit() {
  // バックエンドAPIから初期情報取得
  const inputData: IMedicationSearchMasterInEntity = {
    // 職員ID
    staffId: localOneway.or10889.staffId,
  }
  const medicationSearchMasterResp: IMedicationSearchMasterOutEntity =
    await ScreenRepository.select('medicationSearchMasterSelect', inputData)
  if (
    ResBodyStatusCode.SUCCESS === medicationSearchMasterResp.statusCode &&
    medicationSearchMasterResp.data
  ) {
    // 薬剤分類マスタ情報リスト
    respDataInfoType.drugClassificationMasterInfoList =
      medicationSearchMasterResp.data.drugClassificationMasterInfoList
    // 薬剤マスタ情報リスト
    respDataInfoType.drugMasterInfoList = medicationSearchMasterResp.data.drugMasterInfoList

    if (
      respDataInfoType.drugClassificationMasterInfoList &&
      respDataInfoType.drugClassificationMasterInfoList.length > 0
    ) {
      // 薬剤分類ツリーを作成
      mo01300OnewayModel.value.items = getTreeData(
        mo01300OnewayModel.value.items,
        respDataInfoType.drugClassificationMasterInfoList
      )
      // 表示順を基づき、データをソートする
      const tempSortedList = respDataInfoType.drugClassificationMasterInfoList
      tempSortedList.sort((a, b) => a.sort.localeCompare(b.sort))
      // 取得した薬剤分類データを薬剤分類リストに設定する。
      setDrugBunTableData(tempSortedList)
    }
    if (respDataInfoType.drugMasterInfoList && respDataInfoType.drugMasterInfoList.length > 0) {
      // 表示順を基づき、データをソートする
      const tempSortedList = respDataInfoType.drugMasterInfoList
      tempSortedList.sort((a, b) => a.sort.localeCompare(b.sort))
      // 取得した薬剤検索マスタデータを薬剤検索マスタリストに設定する。
      setDrugTableData(tempSortedList)
    }
  }
}

/**
 * 薬剤分類ツリーを作成
 *
 * @param treeData - ツリーデータ
 *
 * @param items - 薬剤分類マスタリスト
 */
function getTreeData(treeData: treeItem[], items: DrugClassificationMasterInfo[]) {
  if (treeData && treeData.length > 0) {
    if (items) {
      for (const data of treeData) {
        const children: treeItem[] = []
        for (const item of items) {
          if (item && data) {
            if (data.id === item.parentId) {
              children.push({
                id: item.bunruiId,
                title: item.drugBunKnj,
              } as treeItem)
            }
          }
        }
        if (children.length > 0) {
          data.children = getTreeData(children, items)
        }
      }
    }
  }

  return treeData
}

/**
 * 薬剤分類Tableのデータを設定
 *
 * @description
 * 取得した薬剤分類データを薬剤分類Tableに設定する。
 *
 * @param list - 薬剤分類マスタ情報リスト
 */
function setDrugBunTableData(list: DrugClassificationMasterInfo[]) {
  drugBunTableData.value = []
  for (const data of list) {
    const item = {
      bunruiId: {
        onewayModelValue: { value: parseInt(data.bunruiId), unit: '' } as Mo01336OnewayType,
      },
      drugBunKnj: { modelValue: { value: data.drugBunKnj } as Mo00045Type },
      sort: { modelValue: { value: data.sort } as Mo01278Type },
    } as DrugBun
    drugBunTableData.value.push(item)
  }
  // 一行目を選択
  onSelectRow(Or10889Const.FIRST_ROW)
}

/**
 * 薬剤検索マスタTableのデータを設定
 *
 * @description
 * 取得した薬剤検索マスタデータを薬剤検索マスタTableに設定する。
 *
 * @param list - 薬剤マスタ情報リスト
 */
function setDrugTableData(list: DrugMasterInfo[]) {
  drugTableData.value = []
  for (const data of list) {
    const item = {
      drugId: {
        onewayModelValue: { value: parseInt(data.drugId), unit: '' } as Mo01336OnewayType,
      },
      drugKnj: { modelValue: { value: data.drugKnj } as Mo01274Type },
      drugKana: { modelValue: { value: data.drugKana } as Mo01274Type },
      drugCode: { modelValue: { value: data.drugCode } as Mo01274Type },
      drugReceCode: { modelValue: { value: data.drugReceCode } as Mo01274Type },
      shape: shapeSelectItems.value?.find((item) => item.value === data.shape)?.label,
      memoKnj: { modelValue: { value: data.memoKnj } as Mo01274Type },
      sort: { modelValue: { value: data.sort } as Mo01278Type },
    } as MedicineSearchMaster
    drugTableData.value.push(item)
  }
  // 一行目を選択
  onSelectRow(Or10889Const.FIRST_ROW)
}

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 剤型
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_AGENT_TYPE },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  // 剤型
  shapeSelectItems.value = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_AGENT_TYPE)
}

/**
 * ツリービューの選択状態を監視
 *
 * @description
 * 薬剤分類リスト表示/非表示切替
 */
watch(
  () => mo01300Model?.value.value,
  (newValue) => {
    if (newValue !== '') {
      // 薬剤ツリーセクション.薬剤分類一覧セクション1（第1階層-薬剤分類）.「全ラベル」が選択された場合は、非表示
      if (newValue === Or10889Const.LEVEL_ALL) {
        isShow.value = false
        if (respDataInfoType.drugMasterInfoList && respDataInfoType.drugMasterInfoList.length > 0) {
          // 表示順を基づき、データをソートする
          const tempSortedList = respDataInfoType.drugMasterInfoList
          tempSortedList.sort((a, b) => a.sort.localeCompare(b.sort))
          // 取得した薬剤検索マスタデータを薬剤検索マスタリストに設定する。
          setDrugTableData(tempSortedList)
        }
      } else {
        // 選択されたツリーの階層と薬剤分類名を取得
        const level = ref<string | undefined>('')
        const drugBunKnj = ref<string | undefined>('')
        for (const item of respDataInfoType.drugClassificationMasterInfoList) {
          if (newValue === item.bunruiId) {
            level.value = item.level
            drugBunKnj.value = item.drugBunKnj
            break
          }
        }
        // 薬剤ツリーセクション.薬剤分類一覧セクション3（第3階層-薬剤分類）が選択された場合は、非表示
        if (level.value === Or10889Const.MAX_LEVEL) {
          isShow.value = false
          if (
            respDataInfoType.drugMasterInfoList &&
            respDataInfoType.drugMasterInfoList.length > 0
          ) {
            // 表示順を基づき、データをソートする
            const tempSortedList = respDataInfoType.drugMasterInfoList
            tempSortedList.sort((a, b) => a.sort.localeCompare(b.sort))
            // 取得した薬剤検索マスタデータを薬剤検索マスタリストに設定する。
            setDrugTableData(tempSortedList)
          }
        } else {
          isShow.value = true
          // 表示順を基づき、データをソートする
          const tempSortedList = respDataInfoType.drugClassificationMasterInfoList
          tempSortedList.sort((a, b) => a.sort.localeCompare(b.sort))
          // 取得した薬剤分類データを薬剤分類リストに設定する。
          setDrugBunTableData(tempSortedList)
        }
      }
      // 一行目を選択
      selectedItemIndex.value = Or10889Const.FIRST_ROW
    }
  }
)

/**
 * 検索ラジオボタンの選択状態を監視
 *
 * @description
 * 検索バラメ-タの入カデ-タをクリア
 */
watch(
  () => local.mo00039,
  () => {
    local.mo00045Search.value = ''
  }
)

/**
 * 「検索」押下
 */
function search() {
  // 「医薬品コ-ドで検索」を選択する場合
  // 選択したノ-ドが(「最終子分類階層」と「階層"全":ノ-ドの薬剤分類IDが-1」)以外の場合
  if (local.mo00039 === Or10889Const.SEARCH_AT_MEDICAL_SUPPLIES_CODE && isShow.value) {
    // 処理終了
    return
  } else {
    let filteredItems = []
    if (isShow.value) {
      // 検率対象区分=0:薬剤分類
      searchSubjectCategory.value = Or10889Const.MEDICINE_CLASSIFICATION
      // キーワード検索
      if (local.mo00039 === Or10889Const.SEARCH_AT_KEYWORD) {
        // 分類名称により、検索内容あいまい検索
        filteredItems = respDataInfoType.drugClassificationMasterInfoList?.filter((item) =>
          item.drugBunKnj?.includes(local.mo00045Search.value)
        )
        setDrugBunTableData(filteredItems)
      }
    } else {
      // 検率対象区分 =1:薬剤
      searchSubjectCategory.value = Or10889Const.MEDICINE
      // キーワード検索
      if (local.mo00039 === Or10889Const.SEARCH_AT_KEYWORD) {
        // 薬剤名により、検索内容あいまい検索
        filteredItems = respDataInfoType.drugMasterInfoList?.filter((item) =>
          item.drugKnj?.toLowerCase().includes(local.mo00045Search.value.toLowerCase())
        )
      } else {
        // 医薬品コードの検索は前方一致の場合、
        filteredItems = respDataInfoType.drugMasterInfoList?.filter((item) =>
          item.drugCode?.startsWith(local.mo00045Search.value)
        )
      }
      setDrugTableData(filteredItems)
    }
  }
  // 入力デ-タ桁数チエック
  // 入力デ一タの桁数>半角100文字/全角100文字の場合
  if (
    local.mo00045Search.value &&
    local.mo00045Search.value.length >= Or10889Const.NUMBER_OF_INPUT_DATA
  ) {
    // 入力デ一タを削除する
    local.mo00045Search.value = ''
  }
}

/**
 * 「絞込み」押下
 *
 * @description
 * 検索セクションの表示/非表示状態を切り替え
 */
function filter() {
  if (isVisible.value) {
    isVisible.value = false
  } else {
    isVisible.value = true
  }
}

/**
 * 「マスタ」押下
 *
 * @description
 * 薬剤マスタ画面を開く
 */
function onMaster() {
  // Or27751のダイアログ開閉状態を更新する
  Or27751Logic.state.set({
    uniqueCpId: or27751.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 薬剤分類テーブルヘッダをクリック
 *
 * @description
 * 押下されているヘッダにより、テーブルデータをソートする
 *
 * @param key - 列の鍵
 */
function clickMedicineClassificationTableHeaders(key: string) {
  const sortedItems: DrugBun[] = drugBunTableData.value
  // 押下されているヘッダが前回値ではない場合
  if (previousKey.value !== key) {
    descFlag.value = true
  }
  switch (key) {
    // ID
    case 'bunruiId':
      sortedItems.sort((a, b) => {
        if (a.bunruiId.onewayModelValue.value && b.bunruiId.onewayModelValue.value) {
          return a.bunruiId.onewayModelValue.value - b.bunruiId.onewayModelValue.value
        }
        return 0
      })
      break
    // 分類名称
    case 'n1A':
      sortedItems.sort((a, b) =>
        a.drugBunKnj.modelValue.value.localeCompare(b.drugBunKnj.modelValue.value)
      )
      break
    // 表示順
    case 'sort':
      sortedItems.sort((a, b) => a.sort.modelValue.value.localeCompare(b.sort.modelValue.value))
      break
    default:
      return
  }
  // 降順の場合
  if (descFlag.value) {
    sortedItems.reverse()
    descFlag.value = false
  } else {
    descFlag.value = true
  }
  drugBunTableData.value = sortedItems
  previousKey.value = key
}

/**
 * 薬剤検索マスタテーブルヘッダをクリック
 *
 * @description
 * 押下されているヘッダにより、テーブルデータをソートする
 *
 * @param key - 列の鍵
 */
function clickMedicineSearchMasterTableHeaders(key: string) {
  const sortedItems: MedicineSearchMaster[] = drugTableData.value
  // 押下されているヘッダが前回値ではない場合
  if (previousKey.value !== key) {
    descFlag.value = true
  }
  switch (key) {
    // ID
    case 'drugId':
      sortedItems.sort((a, b) => {
        if (a.drugId.onewayModelValue.value && b.drugId.onewayModelValue.value) {
          return a.drugId.onewayModelValue.value - b.drugId.onewayModelValue.value
        }
        return 0
      })
      break
    // 薬剤名
    case 'n1A':
      sortedItems.sort((a, b) =>
        a.drugKnj.modelValue.value.localeCompare(b.drugKnj.modelValue.value)
      )
      break
    // 薬剤カナ
    case 'n1B':
      sortedItems.sort((a, b) =>
        a.drugKana.modelValue.value.localeCompare(b.drugKana.modelValue.value)
      )
      break
    // 医薬品コード
    case 'n1CKnj':
      sortedItems.sort((a, b) =>
        a.drugCode.modelValue.value.localeCompare(b.drugCode.modelValue.value)
      )
      break
    // レセ電算コード
    case 'n1D':
      sortedItems.sort((a, b) =>
        a.drugReceCode.modelValue.value.localeCompare(b.drugReceCode.modelValue.value)
      )
      break
    // 剤型
    case 'n1E':
      sortedItems.sort((a, b) => a.shape.localeCompare(b.shape))
      break
    // 作用・効能等
    case 'n1F':
      sortedItems.sort((a, b) =>
        a.memoKnj.modelValue.value.localeCompare(b.memoKnj.modelValue.value)
      )
      break
    // 表示順
    case 'sort':
      sortedItems.sort((a, b) => a.sort.modelValue.value.localeCompare(b.sort.modelValue.value))
      break
    default:
      return
  }
  // 降順の場合
  if (descFlag.value) {
    sortedItems.reverse()
    descFlag.value = false
  } else {
    descFlag.value = true
  }
  drugTableData.value = sortedItems
  previousKey.value = key
}

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  setState({ isOpen: false })
}

/**
 * 「確定ボタン」押下
 */
function confirm() {
  // 表示されているタブ画面の表示順欄の値で薬剤一覧の内容をソートする
  // ※表示順欄に値のない行は順次に最後に移動する
  const rtnData = {
    drugId: props.modelValue.drugId,
  }
  // 選択したノ-ドが「最終子分類階層」または「階層"全"-ドの薬剤分類IDが-1」の場合
  if (!isShow.value) {
    // 該当ノードの一覧データがある場合
    if (drugTableData.value && drugTableData.value.length > 0) {
      rtnData.drugId = drugTableData.value[selectedItemIndex.value].drugCode.modelValue.value
      // test用
      alert('薬剤コード:' + rtnData.drugId)
    } else {
      // 本画面を閉じ、親画面に返却する。
      close()
    }
  } else {
    // 本画面を閉じ、親画面に返却する。
    close()
  }
  // 返却情報.薬剤一覧 = ソート後の薬剤一覧の内容
  emit('update:modelValue', rtnData)
  // 本画面を閉じ、親画面に返却する。
  close()
}
/**
 * 行選択
 *
 * @param index - 選択した行のindex
 */
function onSelectRow(index: number) {
  selectedItemIndex.value = index
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        class="d-flex align-center justify-end my-2"
        no-gutters
      >
        <!-- 絞込みボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611OnewayModelValue"
          @click="filter"
        />
        <!-- マスタ他アイコンボタン -->
        <base-mo-00009
          :oneway-model-value="localOneway.mo00009MasterOtherOneway"
          @click="onMaster"
        >
          <!-- ツールチップ表示 -->
          <c-v-tooltip
            v-if="localOneway.mo00009MasterOtherOneway.tooltipText"
            :text="localOneway.mo00009MasterOtherOneway.tooltipText"
            :location="localOneway.mo00009MasterOtherOneway.tooltipLocation"
            activator="parent"
          />
        </base-mo-00009>
      </c-v-row>
      <c-v-row
        style="margin-left: -8px; margin-right: -8px"
        no-gutters
        ><c-v-divider
      /></c-v-row>
      <c-v-row no-gutters>
        <c-v-col
          col="1"
          sm="3"
          class="h-100"
        >
          <div class="tree_content">
            <!-- ツリービュー -->
            <base-mo01300
              v-model="mo01300Model"
              :oneway-model-value="mo01300OnewayModel"
            />
          </div>
        </c-v-col>
        <c-v-divider
          class="my-0"
          style="padding: -8px"
          vertical
        />
        <c-v-col
          col="1"
          sm="9"
        >
          <c-v-row
            style="height: 84px"
            no-gutters
          >
            <c-v-card
              v-show="isVisible"
              elevation="0"
            >
              <c-v-col class="pa-0">
                <c-v-row
                  class="mt-1 d-flex align-center"
                  style="height: 36px"
                  no-gutters
                >
                  <!-- ラジオボタングループ -->
                  <base-mo00039
                    v-model="local.mo00039"
                    :oneway-model-value="localOneway.mo00039OnewayType"
                  >
                    <base-at-radio
                      v-for="(item, index) in searchCategoryList"
                      :key="'or10889-' + index"
                      :name="'or10889-radio-' + index"
                      class="mr-2"
                      :radio-label="item.label"
                      :value="item.value"
                    />
                  </base-mo00039>
                </c-v-row>
                <c-v-row
                  class="ml-2 mb-2"
                  no-gutters
                >
                  <c-v-col>
                    <!-- 検索テキストボックス -->
                    <base-mo00045
                      v-model="local.mo00045Search"
                      :oneway-model-value="localOneway.mo00045SerchOneway"
                    />
                  </c-v-col>
                  <!-- 検索ボタン -->
                  <c-v-col>
                    <base-mo00611
                      v-bind="localOneway.mo00611SearchOneWay"
                      @click="search"
                    />
                  </c-v-col>
                </c-v-row>
              </c-v-col>
            </c-v-card>
          </c-v-row>
          <c-v-row no-gutters>
            <c-v-divider style="margin-right: -8px"></c-v-divider>
          </c-v-row>
          <c-v-row
            class="mt-2"
            no-gutters
          >
            <!-- 薬剤分類リスト -->
            <c-v-data-table
              v-if="isShow"
              :headers="medicineClassificationTableHeaders"
              class="table-header"
              hide-default-footer
              fixed-header
              :style="medicineClassificationTablesStyle"
              :items="drugBunTableData"
              :items-per-page="-1"
            >
              <!-- ヘッダ Part -->
              <template #headers="{ columns }">
                <tr>
                  <th
                    v-for="column in columns"
                    :key="column.key"
                    :style="{ minWidth: column.minWidth, cursor: 'pointer' }"
                    @click.stop="clickMedicineClassificationTableHeaders(column.key)"
                  >
                    <span
                      v-if="column.required"
                      style="color: red"
                    >
                      {{ required }}
                    </span>
                    <span>{{ column.title }}</span>
                  </th>
                </tr>
              </template>
              <template #item="{ item, index }">
                <tr
                  :class="{ 'select-row': selectedItemIndex === index }"
                  @click="onSelectRow(index)"
                >
                  <!-- IDラベル -->
                  <td>
                    <base-mo01336
                      class="px-4"
                      :oneway-model-value="item.bunruiId.onewayModelValue"
                    />
                  </td>
                  <!-- 分類名称テキストフィールド -->
                  <td>
                    <span class="overflowText">{{ item.drugBunKnj.modelValue.value }}</span>
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :max-width="500"
                      :text="item.drugBunKnj.modelValue.value"
                      open-delay="200"
                    />
                  </td>
                  <!-- 表示順テキストフィールド-->
                  <td>
                    <base-mo01278
                      ref="sortInput"
                      v-model="item.sort.modelValue"
                      style="max-width: 80px !important"
                      :oneway-model-value="{ readonly: true }"
                    />
                  </td>
                </tr>
              </template>
            </c-v-data-table>
            <!-- 薬剤検索マスタリスト -->
            <c-v-data-table
              v-else
              :headers="medicineSearchMasterTableHeaders"
              class="table-header overflow-y-auto table-wrapper d-flex"
              :style="medicineSearchMasterTablesStyle"
              style="width: 1480px"
              hide-default-footer
              fixed-header
              :items="drugTableData"
              :items-per-page="-1"
            >
              <!-- ヘッダ Part -->
              <template #headers="{ columns }">
                <tr>
                  <th
                    v-for="column in columns"
                    :key="column.key"
                    :style="{ minWidth: column.minWidth, cursor: 'pointer' }"
                    @click.stop="clickMedicineSearchMasterTableHeaders(column.key)"
                  >
                    <span
                      v-if="column.required"
                      style="color: red"
                    >
                      {{ required }}
                    </span>
                    <span>{{ column.title }}</span>
                  </th>
                </tr>
              </template>
              <template #item="{ item, index }">
                <tr
                  :class="{ 'select-row': selectedItemIndex === index }"
                  @click="onSelectRow(index)"
                >
                  <!-- IDラベル -->
                  <td>
                    <base-mo01336
                      class="px-4"
                      :oneway-model-value="item.drugId.onewayModelValue"
                    />
                  </td>
                  <!-- 薬剤名テキストフィールド -->
                  <td>
                    <span class="overflowText">{{ item.drugKnj.modelValue.value }}</span>
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :max-width="500"
                      :text="item.drugKnj.modelValue.value"
                      open-delay="200"
                    />
                  </td>
                  <!-- 薬剤カナテキストフィールド -->
                  <td>
                    <span class="overflowText">{{ item.drugKana.modelValue.value }}</span>
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :max-width="500"
                      :text="item.drugKana.modelValue.value"
                      open-delay="200"
                    />
                  </td>
                  <!-- 医薬品コードテキストフィールド -->
                  <td>
                    <span class="overflowText">{{ item.drugCode.modelValue.value }}</span>
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :max-width="500"
                      :text="item.drugCode.modelValue.value"
                      open-delay="200"
                    />
                  </td>
                  <!-- レセ電算コードテキストフィールド -->
                  <td>
                    <span class="overflowText">{{ item.drugReceCode.modelValue.value }}</span>
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :max-width="500"
                      :text="item.drugReceCode.modelValue.value"
                      open-delay="200"
                    />
                  </td>
                  <!-- 剤型プルダウン -->
                  <td>
                    <span class="overflowText">{{ item.shape }}</span>
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :max-width="500"
                      :text="item.shape"
                      open-delay="200"
                    />
                  </td>
                  <!-- 作用・効能等テキストフィールド -->
                  <td>
                    <span class="overflowText">{{ item.memoKnj.modelValue.value }}</span>
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :max-width="500"
                      :text="item.memoKnj.modelValue.value"
                      open-delay="200"
                    />
                  </td>
                  <!-- 表示順テキストフィールド-->
                  <td>
                    <base-mo01278
                      ref="sortInput"
                      :model-value="item.sort.modelValue"
                      style="max-width: 80px !important"
                      :oneway-model-value="{ readonly: true }"
                    />
                  </td>
                </tr>
              </template>
            </c-v-data-table>
          </c-v-row>
          <!-- 注釈ラベル -->
          <c-v-row no-gutters>
            <c-v-col
              v-if="isShow"
              style="height: 15px"
            />
            <c-v-col
              cols="12"
              class="label-comment"
            >
              {{ t('label.all-common') }}
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          @click="close"
        >
          <!-- ツールチップ表示 -->
          <c-v-tooltip
            v-if="localOneway.mo00611OneWay.tooltipText"
            :text="localOneway.mo00611OneWay.tooltipText"
            :location="localOneway.mo00611OneWay.tooltipLocation"
            activator="parent"
          />
        </base-mo00611>
        <!-- 確定ボタン -->
        <base-mo00609
          v-bind="localOneway.mo00609OneWay"
          class="ml-2"
          @click="confirm"
        >
          <!-- ツールチップ表示 -->
          <c-v-tooltip
            v-if="localOneway.mo00609OneWay.tooltipText"
            :text="localOneway.mo00609OneWay.tooltipText"
            :location="localOneway.mo00609OneWay.tooltipLocation"
            activator="parent"
          />
        </base-mo00609>
      </c-v-row>
      <g-custom-or-27751
        v-if="showDialogOr27751"
        v-bind="or27751"
        :oneway-model-value="localOneway.or27751OneWay"
      />
    </template>
  </base-mo00024>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table.scss';
$font-size-root: 14px;

/* テーブルのスタイル */
.table-wrapper :deep(.v-table__wrapper) {
  overflow-x: auto !important;
}

.table-header :deep(.v-table__wrapper) {
  overflow-x: hidden;
  margin-left: 8px;
}

/* ヘッダーのスタイル */
.table-header :deep(.v-table__wrapper th) {
  white-space: nowrap !important;
  background-color: rgb(var(--v-theme-black-100)) !important;
  border-top: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-right: 1px rgb(var(--v-theme-black-200)) solid !important;
}

.table-header :deep(.v-table__wrapper th:first-child) {
  border-left: 1px rgb(var(--v-theme-black-200)) solid !important;
}
.table-header .v-table__wrapper .select-row td {
  background-color: rgb(var(--v-theme-blue-100)) !important;
}

/* 明細のスタイル */
.table-header .v-table__wrapper td {
  background-color: #FAFAFA;
  font-size: $font-size-root !important;
  height: 32px !important;
  padding: 0 !important;
  border-right: 1px rgb(var(--v-theme-black-200)) solid !important;

  :deep(.v-col) {
    padding: 0 !important;
  }
  :deep(.table-cell) {
    border: none !important;
    outline: none !important;
  }
  :deep(.v-field__input) {
    height: 31px !important;
    min-height: 31px !important;
    padding: 0px 16px !important;
  }
  :deep(.v-field--focused) {
    border: none !important;
    box-shadow: none;
    .v-field__outline {
      border: none !important;
    }
  }
}
.table-header :deep(.v-table__wrapper td:last-child) {
  border-right: 1px rgb(var(--v-theme-black-200)) solid !important;
}

.v-table .v-table__wrapper > table > tbody > tr:last-child > td {
  border-bottom: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.table-header .v-table__wrapper td:first-child {
  border-left: 1px rgb(var(--v-theme-black-200)) solid !important;
}

/* 選択行のスタイル */
.table-header :deep(.v-table__wrapper .selected-row) {
  background-color: rgb(var(--v-theme-blue-100)) !important;
}

:deep(.txt:disabled) {
  background: rgb(var(--v-theme-secondaryBackground));
}

:deep(.full-width-field) {
  padding: 0px 16px !important;
}

// 注釈
.label-comment {
  color: rgb(var(--v-theme-subText)) !important;
  margin: 8px 0px 8px 8px;
  height: 20px;
}

// 選択した行のCSS
.select-row {
  background: rgb(var(--v-theme-blue-100)) !important;
}

.btn-height {
  height: 24px !important;
  min-height: 24px !important;
}

.tree_content {
  width: 238px;
  height: 342px;
  overflow-y: auto;
  margin-right: 8px;
}

.overflowText {
  text-wrap: auto;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  line-clamp: 1;
  -webkit-line-clamp: 1;
  padding: 0 16px !important;
}

:deep(.v-treeview-item__level) {
  width: 0 !important;
}
</style>
