import { HttpResponse } from 'msw'
import defaultData from './data/default.json'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { FreeAssessmentIssuesPlanningPrintSettingsHistorySelectInEntity } from '~/repositories/cmn/entities/FreeAssessmentIssuesPlanningPrintSettingsSelectEntity.ts'
/**
 * GUI00909_印刷設定
 *
 * @description
 * GUI00909_印刷設定対象一覧情報データを返却する。
 * dataName："freeAssessmentIssuesPlanningPrintSettingsHistorySelect"
 */
export function handler(inEntity: FreeAssessmentIssuesPlanningPrintSettingsHistorySelectInEntity) {
  const responceJson: BaseResponseBody = {
    statusCode: 'success',
    data: {
      ...defaultData,
    },
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
