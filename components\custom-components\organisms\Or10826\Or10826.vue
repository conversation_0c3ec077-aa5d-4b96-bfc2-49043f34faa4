<script setup lang="ts">
/**
 * Or10826:有機体:日課表パターン（設定）モーダル
 * GUI00990_日課表パターン（設定）
 *
 * @description
 * GUI00990_日課表パターン（設定）画面
 *
 * <AUTHOR>
 */

import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or01533Const } from '../Or01533/Or01533.constants'
import { OrX0053Const } from '../OrX0053/OrX0053.constants'
import { OrX0054Const } from '../OrX0054/OrX0054.constants'
import type { Or10826StateType,  AsyncFunction} from './Or10826.type'
import { Or10826Const } from './Or10826.constants'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import { useScreenOneWayBind, useScreenStore, useSetupChildProps } from '#build/imports'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Or10826OneWayType } from '~/types/cmn/business/components/Or10826Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type { OrX0053OnewayType, OrX0053Type } from '~/types/cmn/business/components/OrX0053Type'
import type { OrX0054OnewayType, OrX0054Type } from '~/types/cmn/business/components/OrX0054Type'

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  onewayModelValue: Or10826OneWayType
}
const props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/
// ローカルTwoway
const local = reactive({
  // 本画面ダイアログ
  mo00024: {
    isOpen: Or10826Const.DEFAULT.IS_OPEN,
  } as Mo00024Type,
  mo00043: { id: Or10826Const.TAB.TAB_ID_SETTING } as Mo00043Type,
  mo00043Transfer: { id: Or10826Const.TAB.TAB_ID_SETTING } as Mo00043Type,
  mo00043Change: { id: Or10826Const.TAB.TAB_ID_SETTING } as Mo00043Type,
  orX0053: { editFlg: false } as OrX0053Type,
  orX0054: { editFlg: false } as OrX0054Type,
})

// ローカルOneway
const localOneway = reactive({
  // 本画面
  or10826Oneway: {
    ...props.onewayModelValue,
  },
  // タブ：グループ
  orX0053OneWay: {
    ...props.onewayModelValue,
  } as OrX0053OnewayType,
  // タブ：タイトル
  orX0054OneWay: {
    ...props.onewayModelValue,
  } as OrX0054OnewayType,
  // 本画面ダイアログOneway
  mo00024Oneway: {
    width: '950px',
    height: '900px',
    persistent: true,
    showCloseBtn: true,
    disabledCloseBtnEvent: true,
    mo01344Oneway: {
      name: 'Or10826',
      toolbarTitle: t('label.daily-table-pattern'),
      toolbarTitleCenteredFlg: false,
      toolbarName: '',
      showCardActions: true,
      cardTextClass: 'card-text pa-0',
    },
  },
  // 閉じるコンポーネント
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  mo00609SaveOneway: {
    btnLabel: t('btn.save'),
  } as Mo00609OnewayType,
  // タブ
  mo00043OneWay: {
    tabItems: [
      { id: Or10826Const.TAB.TAB_ID_TITLE, title: t('label.title') },
      {
        id: Or10826Const.TAB.TAB_ID_SETTING,
        title: t('label.setting'),
      },
      {
        id: Or10826Const.TAB.TAB_ID_GROUP,
        title: t('label.group'),
      },
    ],
  } as Mo00043OnewayType,
})
const or01533_1 = ref({ uniqueCpId: Or01533Const.CP_ID(1) })
const orX0053_1 = ref({ uniqueCpId: OrX0053Const.CP_ID(1) })
const orX0054_1 = ref({ uniqueCpId: OrX0054Const.CP_ID(1) })
const or21814_1 = ref({ uniqueCpId: '' })

const or01533Ref = ref<{ insert: AsyncFunction, initData: AsyncFunction}>()
const orX0053Ref = ref<{ save: AsyncFunction }>()
const orX0054Ref = ref<{ save: AsyncFunction }>()
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or10826StateType>({
  cpId: Or10826Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      local.mo00024.isOpen = value ?? Or10826Const.DEFAULT.IS_OPEN
    },
  },
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or01533Const.CP_ID(1)]: or01533_1.value,
  [OrX0053Const.CP_ID(1)]: orX0053_1.value,
  [OrX0054Const.CP_ID(1)]: orX0054_1.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
})

/**************************************************
 * ウォッチャー
 **************************************************/
onMounted(() => {
  switch (props.onewayModelValue.mstKbn) {
    case Or10826Const.DEFAULT.MSTKBN_LIST.DAILY_PARTTEN:
      localOneway.mo00024Oneway.mo01344Oneway.toolbarTitle = t('label.daily-table-pattern')
      break
    case Or10826Const.DEFAULT.MSTKBN_LIST.WEEK_PARTTEN:
      localOneway.mo00024Oneway.mo01344Oneway.toolbarTitle = t('label.week-table-pattern')
      break
    case Or10826Const.DEFAULT.MSTKBN_LIST.MONTHYEAR_PARTTEN:
      localOneway.mo00024Oneway.mo01344Oneway.toolbarTitle = t('label.monthly-yearly-table-pattern')
      break
    default:
      break
  }
  // データ変更確認ダイアログを初期化(i.cmn.10430)
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })
})

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => local.mo00024.emitType,
  async () => {
    if (local.mo00024.emitType === 'closeBtnClick') {
      await close()
      local.mo00024.emitType = undefined
    }
  }
)

/**************************************************
 * 関数
 **************************************************/
// メニュー切替
watch(
  () => local.mo00043Transfer.id,
  async (newValue) => {
    if (local.mo00043.id !== newValue) {
      if (isEdit.value) {
        local.mo00043Change.id = newValue
        local.mo00043Transfer.id = local.mo00043.id
        const dialogResult = await openEditDialog()
        switch (dialogResult) {
          case Or10826Const.DEFAULT.DIALOG_RESULT_YES:
            await insert()
            await initData()
            local.mo00043.id = newValue
            local.mo00043Transfer.id = newValue
            break
          case Or10826Const.DEFAULT.DIALOG_RESULT_NO:
            await initData()
            local.mo00043.id = newValue
            local.mo00043Transfer.id = newValue
            break
          case Or10826Const.DEFAULT.DIALOG_RESULT_CANCEL:
            return
        }
      } else {
        local.mo00043.id = newValue
      }
    }
  }
)


/**
 * 保存
 */
async function save() {
  await insert()
  await initData()
  setState({ isOpen: false })
  return true
}

/**
 * 保存
 */
 async function insert() {
  // 画面入力データ変更があるかどうかを判定する
  // はい選択時は入力内容を保存する
   if (local.mo00043.id === Or10826Const.TAB.TAB_ID_SETTING) {
     await or01533Ref.value?.insert()
   } else if (local.mo00043.id === Or10826Const.TAB.TAB_ID_GROUP) {
    await orX0053Ref.value?.save()
   } else if (local.mo00043.id === Or10826Const.TAB.TAB_ID_TITLE) {
    await orX0054Ref.value?.save()
   }
 }
 /**
  *  initData
  */
async function initData() {
  if (local.mo00043.id === Or10826Const.TAB.TAB_ID_SETTING) {
    await or01533Ref.value?.initData()
  } else if (local.mo00043.id === Or10826Const.TAB.TAB_ID_GROUP) {
    console.log('initData')
  } else if (local.mo00043.id === Or10826Const.TAB.TAB_ID_TITLE) {
    console.log('initData')
  }


}
/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
async function close() {
  if (isEdit.value) {
    const dialogResult = await openEditDialog()
    switch (dialogResult) {
      case Or10826Const.DEFAULT.DIALOG_RESULT_YES:
        await insert()
        await initData()
        setState({ isOpen: false })
        break
      case Or10826Const.DEFAULT.DIALOG_RESULT_NO:
        await initData()
        setState({ isOpen: false })
        break
      case Or10826Const.DEFAULT.DIALOG_RESULT_CANCEL:
        return
    }
  } else {
    setState({ isOpen: false })
  }
}

/**
 * 編集破棄ダイアログ表示
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
async function openEditDialog(): Promise<string> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)

        let result = Or10826Const.DEFAULT.DIALOG_RESULT_CANCEL

        if (event?.firstBtnClickFlg) {
          result = Or10826Const.DEFAULT.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or10826Const.DEFAULT.DIALOG_RESULT_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = Or10826Const.DEFAULT.DIALOG_RESULT_CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  return useScreenStore().isEditNavControl()
})
</script>

<template>
  <!-- ダイアログ -->
  <base-mo00024
    v-model="local.mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <div class="hidden-scroll">
        <base-mo00043
          v-model="local.mo00043Transfer"
          :oneway-model-value="localOneway.mo00043OneWay"
        ></base-mo00043>
        <c-v-window v-model="local.mo00043.id">
          <c-v-window-item value="title">
            <!-- タブ：タイトル -->
            <g-custom-or-x-0054
              ref="orX0054Ref"
              v-model="local.orX0054"
              v-bind="orX0054_1"
              :oneway-model-value="localOneway.orX0054OneWay"
              :parent-unique-cp-id="props.uniqueCpId"
            ></g-custom-or-x-0054>
          </c-v-window-item>
          <c-v-window-item value="setting">
            <g-custom-Or01533
              v-if="local.mo00043.id === Or10826Const.TAB.TAB_ID_SETTING "
              ref="or01533Ref"
              v-bind="or01533_1"
              :unique-cp-id="or01533_1.uniqueCpId"
            />
          </c-v-window-item>
          <c-v-window-item value="group">
            <!-- タブ：タイトル -->
            <g-custom-or-x-0053
              v-if="local.mo00043.id === Or10826Const.TAB.TAB_ID_GROUP"
              ref="orX0053Ref"
              v-model="local.orX0053"
              v-bind="orX0053_1"
              :oneway-model-value="localOneway.orX0053OneWay"
              :parent-unique-cp-id="props.uniqueCpId"
            ></g-custom-or-x-0053></c-v-window-item>
        </c-v-window>
      </div>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          @click="close"
        ></base-mo00611>
        <!-- 確定ボタン Mo00611 -->
        <base-mo00609
          v-bind="localOneway.mo00609SaveOneway"
          class="mx-2"
          @click="save"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <g-base-or21814 v-bind="or21814_1"></g-base-or21814>
</template>

<style scoped lang="scss">
.view {
  overflow-y: hidden;
  display: flex;
  margin: 0px;
  flex-direction: column;
}

.v-divider {
  margin-top: 8px;
  margin-bottom: 8px;
}

.intentionList {
  height: 250px;
}

.buttonArea {
  button:not(:nth-child(1)) {
    margin-left: 8px;
  }
}
:deep(.v-dialog--scrollable > .v-overlay__content > form > .v-card > .v-card-text) {
  overflow-y: hidden;
}
</style>
