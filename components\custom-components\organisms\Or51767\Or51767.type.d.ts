/**
 * Or51767:有機体:印刷設定モーダル（画面/特殊コンポーネント）
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR>
 */
export interface Or51767StateType {
  /**
   * 開閉フラグ
   */
  isOpen?: boolean
  /**
   * パラメータ
   */
  param: Or51767Param
}

/**
 * パラメータ構造
 */
export interface Or51767Param {
  /**
   * 職員ID
   */
  shokuId: string

  /**
   * 帳票番号
   */
  prtNo: string
  /**
   * 事業者ID
   */
  svJigyoId: string
  /**
   * 施設ID
   */
  shisetuId: string
  /**
   * 担当者ID
   */
  tantoId: string
  /**
   * 種別ID
   */
  syubetsuId: string
  /**
   * セクション名
   */
  sectionName: string
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 事業所名
   */
  svJigyoKnj: string
  /**
   * 処理年月日
   */
  processYmd: string
  /**
   * アセスメントID
   */
  assessmentId: string
  /**
   * 利用者情報リストにデータを選択フラゲ
   */
  parentUserIdSelectDataFlag: boolean
  /**
   * フォーカス設定用イニシャル
   */
  focusSettingInitial: string[]
  /**
   * 初期選択状態の担当者カウンタ値
   */
  selectedUserCounter: string
}
