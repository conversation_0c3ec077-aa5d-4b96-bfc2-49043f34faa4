<script setup lang="ts">
/**
 * Or28394:有機体:印刷設定モーダル
 * GUI01065_［印刷設定］画面
 *
 * @description
 * ［印刷設定］画面
 *
 * <AUTHOR>
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

import { Or26261Logic } from '../Or26261/Or26261.logic'
import { Or26261Const } from '../Or26261/Or26261.constants'
import type { OrX0130TableType } from '../OrX0130/OrX0130.type'
import type { OrX0143TableData } from '../OrX0143/OrX0143.type'
import { OrX0143Logic } from '../OrX0143/OrX0143.logic'
import { Or28780Const } from '../Or28780/Or28780.constants'
import { Or28780Logic } from '../Or28780/Or28780.logic'
import { Or18615Const } from '../Or18615/Or18615.constants'
import { Or18615Logic } from '../Or18615/Or18615.logic'
import { Or26331Const } from '../Or26331/Or26331.constants'
import { Or26331Logic } from '../Or26331/Or26331.logic'
import { Or26326Const } from '../Or26326/Or26326.constants'
import { Or26326Logic } from '../Or26326/Or26326.logic'
import { Or26328Const } from '../Or26328/Or26328.constants'
import { Or26328Logic } from '../Or26328/Or26328.logic'
import { Or31319Const } from '../Or31319/Or31319.constants'
import { OrX0135Logic } from '../OrX0135/OrX0135.logic'
import { OrX0135Const } from '../OrX0135/OrX0135.constants'
import { OrX0145Const } from '../OrX0145/OrX0145.constants'
import { Or28394Const } from '../Or28394/Or28394.constants'
import type { Or28394StateType } from './Or28394.type'
import { useScreenOneWayBind, useSetupChildProps, useScreenUtils } from '#imports'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'

import type {
  Mo01334Items,
  Mo01334OnewayType,
  Mo01334Type,
} from '~/types/business/components/Mo01334Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { OrX0117Const } from '~/components/custom-components/organisms/OrX0117/OrX0117.constants'
import { OrX0117Logic } from '~/components/custom-components/organisms/OrX0117/OrX0117.logic'
import type { OrX0117History, OrX0117OnewayType } from '~/types/cmn/business/components/OrX0117Type'
import type { Mo00039OnewayType } from '@/types/business/components/Mo00039Type'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import { OrX0130Logic } from '~/components/custom-components/organisms/OrX0130/OrX0130.logic'
import type { Mo00040OnewayType, Mo00040Type } from '~/types/business/components/Mo00040Type'
import type { OrX0143OnewayType } from '~/types/cmn/business/components/OrX0143Type'
import { OrX0143Const } from '~/components/custom-components/organisms/OrX0143/OrX0143.constants'
import { hasPrintAuth } from '~/utils/useCmnAuthz'
import type { InWebEntity } from '@/repositories/AbstructWebRepository'
import { reportOutputType } from '~/utils/useReportUtils'
import type { Or26261OnewayType, Or26261Type } from '~/types/cmn/business/components/Or26261Type'
import type {
  IPrintInfo,
  PrintOnewayEntity,
  PrintSelectInEntity,
} from '~/repositories/cmn/entities/PrintSelectEntity'
import type { PrintCloseUpdateEntity } from '~/repositories/cmn/entities/PrintCloseUpdateEntity'
import type { PrintSubjectSelectInEntity } from '~/repositories/cmn/entities/PrintSubjectSelectEntity'
import { usePrint } from '~/utils/usePrint'
import type {
  TantoFilterUserIdSelectInEntity,
  TantoFilterUserIdSelectOutEntity,
} from '~/repositories/cmn/entities/TantoFilterUserIdSelectEntity'
import type {
  DailyRoutinePlanPrintSettingsInitUpdateOutEntity,
  KikanRirekiData,
} from '~/repositories/cmn/entities/DailyRoutinePlanPrintSettingsInitUpdateEntity'
import type { Or31319OnewayType } from '~/types/cmn/business/components/Or31319Type'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import type { DailyRoutinePlanInitMasterInfo } from '~/repositories/cmn/entities/DailyRoutinePlanInitSelectEntity'
import type {
  DailyRoutinePlanPrintSettingsUserChangeSelectInEntity,
  DailyRoutinePlanPrintSettingsUserChangeSelectOutEntity,
} from '~/repositories/cmn/entities/DailyRoutinePlanPrintSettingsUserChangeSelectEntity'
import type {
  DailyRoutinePlanPrintSettingsSubjectSelectOutEntity,
  PrtHistoryList,
} from '~/repositories/cmn/entities/DailyRoutinePlanPrintSettingsSubjectSelectEntity'
import type { OrX0135Type, OrX0135OnewayType } from '~/types/cmn/business/components/OrX0135Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'

const { setChildCpBinds } = useScreenUtils()
const { t } = useI18n()

/************************************************
 * Props
 ************************************************/
interface Props {
  onewayModelValue: PrintOnewayEntity<DailyRoutinePlanInitMasterInfo>
  uniqueCpId: string
}

const props = defineProps<Props>()

// 引継情報を取得する
const or21813 = ref({ uniqueCpId: Or21813Const.CP_ID(0) })
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
const orX0130 = ref({ uniqueCpId: OrX0130Const.CP_ID(0) })
const orX0117 = ref({ uniqueCpId: OrX0117Const.CP_ID(0) })
const orX0143 = ref({ uniqueCpId: OrX0143Const.CP_ID(0) })
const or26261 = ref({ uniqueCpId: Or26261Const.CP_ID(0) })
const or28780 = ref({ uniqueCpId: Or28780Const.CP_ID(0) })
const or18615 = ref({ uniqueCpId: Or18615Const.CP_ID(0) })
const or26331 = ref({ uniqueCpId: Or26331Const.CP_ID(0) })
const or26326 = ref({ uniqueCpId: Or26326Const.CP_ID(0) })
const or26328 = ref({ uniqueCpId: Or26328Const.CP_ID(0) })
const or31319 = ref({ uniqueCpId: Or31319Const.CP_ID(0) })
const orX0135 = ref({ uniqueCpId: OrX0135Const.CP_ID(0) })
const orX0145 = ref({ uniqueCpId: OrX0145Const.CP_ID(0) })
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()

// 印刷共通処理
const printCom = usePrint()
// 印刷権限
const prtFlg: boolean = await hasPrintAuth()

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21813Const.CP_ID(0)]: or21813.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [OrX0143Const.CP_ID(0)]: orX0143.value,
  [OrX0130Const.CP_ID(0)]: orX0130.value,
  [OrX0117Const.CP_ID(0)]: orX0117.value,
  [Or26261Const.CP_ID(0)]: or26261.value,
  [Or28780Const.CP_ID(0)]: or28780.value,
  [Or18615Const.CP_ID(0)]: or18615.value,
  [Or26331Const.CP_ID(0)]: or26331.value,
  [Or26326Const.CP_ID(0)]: or26326.value,
  [Or26328Const.CP_ID(0)]: or26328.value,
  [Or31319Const.CP_ID(0)]: or31319.value,
  [OrX0145Const.CP_ID(0)]: orX0145.value,
})

// ダイアログ表示フラグ
const showDialogOrx0117 = computed(() => {
  // OrX0117のダイアログ開閉状態
  return OrX0117Logic.state.get(orX0117.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr26261 = computed(() => {
  // Or26261のダイアログ開閉状態
  return Or26261Logic.state.get(or26261.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr00586 = computed(() => {
  // Or00586のダイアログ開閉状態
  return OrX0135Logic.state.get(orX0135.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * 変数定義
 **************************************************/
// 利用者列幅
const userCols = ref<number>(4)
// 履歴一覧セクションフラゲ
const mo01334TypeHistoryFlag = ref<boolean>(false)
// 選択した利用者データ
const selectedUserList = ref<OrX0130TableType[]>([])
// 選択した履歴データ
const selectedRirekiData = ref<KikanRirekiData[]>([])
const isInit = ref<boolean>(false)

// ローカル双方向bind
const local = reactive({
  or28394: {
    // 印刷設定情報リスト
    prtList: [] as IPrintInfo[],
    // 期間履歴情報リスト
    kikanRirekiList: [] as KikanRirekiData[],
    // 期間管理フラグ
    kikanFlag: '',
  },
  // 作成年月日印刷区分
  mo00039TypePrintDateCreation: '',
  // 作成年月日
  mo00040: { modelValue: '' } as Mo00040Type,
  // 帳票タイトル
  mo01334: {
    value: '',
    values: [],
  } as Mo01334Type,
  // 敬称
  textInput: {
    value: '',
  } as Mo00045Type,
  // 日付印刷区分
  mo00039Type: '',
  // 指定日
  mo00020Type: {
    value: '',
  } as Mo00020Type,
  // 基準日
  mo00020TypeKijunbi: {
    value: systemCommonsStore.getSystemDate!,
  } as Mo00020Type,
  // 帳票タイトル
  titleInput: {
    value: '',
  } as Mo00045Type,
  // 敬称を変更する
  mo00018TypeChangeTitle: {
    modelValue: false,
  } as Mo00018Type,
  // 記入用シートを印刷する
  mo00018TypePrintTheForm: {
    modelValue: false,
  } as Mo00018Type,
  // 要介護度
  mo00040PrintNursingCareRequired: {
    modelValue: '',
  } as Mo00040Type,
  // 承認欄を印刷する
  mo00018TypeConfirmFormCopying: {
    modelValue: true,
  } as Mo00018Type,
  // 記入日を印刷する
  mo00018TypePrintMode: {
    modelValue: false,
  } as Mo00018Type,
  // GUI01186_担当ケアマネ検索画面
  or26261: {
    shokuin: {
      shokuin1Knj: '',
      shokuin2Knj: '',
    },
  } as Or26261Type,
  or26261Id: '',
  // GUI01187_帳票設定画面
  orX0135: {
    // 承認欄1行目下線無
    text1Knj: { value: '' },
    // 承認欄1行目下線有
    day1Knj: { value: '' },
    // 承認欄2行目下線無
    text2Knj: { value: '' },
    // 承認欄2行目下線有
    day2Knj: { value: '' },
    // 承認欄3行目下線無
    text3Knj: { value: '' },
    // 承認欄3行目下線有
    day3Knj: { value: '' },
    // 承認欄4行目下線無
    text4Knj: { value: '' },
    // 承認欄4行目下線有
    day4Knj: { value: '' },
    // 表示行数
    dispKbn: '',
    // 1行目文字サイズ
    text1Font: '',
    // 1行目文字数入力
    text1Width: '',
    // 下線部分2行目幅
    day1Width: '',
    // 2行目文字サイズ
    text2Font: '',
    // 承認欄2行目幅
    text2Width: '',
    // 下線部分2行目幅
    day2Width: '',
    // 3行目文字サイズ
    text3Font: '',
    // 承認欄3行目幅
    text3Width: '',
    // 下線部分3行目幅
    day3Width: '',
    // 4行目文字サイズ
    text4Font: '',
    // 4行目文字数入力
    text4Width: '',
    // 下線部分4行目幅
    day4Width: '',
    // 更新回数
    modifiedCnt: '',
  } as OrX0135Type,
  orX0145: {
    value: {} as TantoCmnShokuin,
  } as OrX0145Type,
})

// 担当ケアマネ
local.orX0145.value = {
  counter: Or28394Const.EMPTY,
  chkShokuId: Or28394Const.EMPTY,
  houjinId: Or28394Const.EMPTY,
  shisetuId: Or28394Const.EMPTY,
  svJigyoId: Or28394Const.EMPTY,
  shokuin1Kana: Or28394Const.EMPTY,
  shokuin2Kana: Or28394Const.EMPTY,
  shokuin1Knj: Or28394Const.EMPTY,
  shokuin2Knj: Or28394Const.EMPTY,
  sex: Or28394Const.EMPTY,
  birthdayYmd: Or28394Const.EMPTY,
  zip: Or28394Const.EMPTY,
  kencode: Or28394Const.EMPTY,
  citycode: Or28394Const.EMPTY,
  areacode: Or28394Const.EMPTY,
  addressKnj: Or28394Const.EMPTY,
  tel: Or28394Const.EMPTY,
  kaikeiId: Or28394Const.EMPTY,
  kyuyoKbn: Or28394Const.EMPTY,
  partKbn: Or28394Const.EMPTY,
  inYmd: Or28394Const.EMPTY,
  outYmd: Or28394Const.EMPTY,
  shozokuId: Or28394Const.EMPTY,
  shokushuId: Or28394Const.EMPTY,
  shokuId: Or28394Const.EMPTY,
  timeStmp: Or28394Const.EMPTY,
  delFlg: Or28394Const.EMPTY,
  shokuNumber: Or28394Const.EMPTY,
  caremanagerKbn: Or28394Const.EMPTY,
  shokuType1: Or28394Const.EMPTY,
  shokuType2: Or28394Const.EMPTY,
  kGroupid: Or28394Const.EMPTY,
  bmpPath: Or28394Const.EMPTY,
  bmpYmd: Or28394Const.EMPTY,
  hankoPath: Or28394Const.EMPTY,
  kojinPath: Or28394Const.EMPTY,
  keitaitel: Or28394Const.EMPTY,
  eMail: Or28394Const.EMPTY,
  senmonNo: Or28394Const.EMPTY,
  sgfFlg: Or28394Const.EMPTY,
  srvSekiKbn: Or28394Const.EMPTY,
  shokushuId2: Or28394Const.EMPTY,
  shokushuId3: Or28394Const.EMPTY,
  shokushuId4: Or28394Const.EMPTY,
  shokushuId5: Or28394Const.EMPTY,
  shikakuId1: Or28394Const.EMPTY,
  shikakuId2: Or28394Const.EMPTY,
  shikakuId3: Or28394Const.EMPTY,
  shikakuId4: Or28394Const.EMPTY,
  shikakuId5: Or28394Const.EMPTY,
  kyuseiFlg: Or28394Const.EMPTY,
  kyuseiKana: Or28394Const.EMPTY,
  kyuseiKnj: Or28394Const.EMPTY,
  sort: Or28394Const.EMPTY,
  selfNumber: Or28394Const.EMPTY,
  ichiranShokushuIdNm: Or28394Const.EMPTY,
  shokushuId2Nm: Or28394Const.EMPTY,
  shokushuId3Nm: Or28394Const.EMPTY,
  shokushuId4Nm: Or28394Const.EMPTY,
  shokushuId5Nm: Or28394Const.EMPTY,
  stopFlg: Or28394Const.EMPTY,
  shokuinKnj: '',
  shokuinKana: Or28394Const.EMPTY,
  title: Or28394Const.EMPTY,
  value: Or28394Const.EMPTY,
} as TantoCmnShokuin

// ローカルOneway
const localOneway = reactive({
  // 利用者選択
  orX0130Oneway: {
    selectMode: Or28394Const.DEFAULT.TANI,
    tableStyle: 'width:270px',
  } as OrX0130OnewayType,
  // 期間履歴
  orX0143Oneway: {
    // 期間管理フラグ
    kikanFlg: '1',
    // 単一複数フラグ(0:単一,1:複数)
    singleFlg: Or28394Const.DEFAULT.TANI,
    tableStyle: 'width:335px',
    kikanRirekiTitleList: [
      // 作成日
      { title: 'create-date', width: '93', key: 'createYmd' },
      // 作成者
      { title: 'shoku_knj', width: '171', key: 'shokuKnj' },
    ],
    // 履歴ID
    rirekiId: '',
    // 履歴情報
    rirekiList: [] as OrX0143TableData[],
  } as OrX0143OnewayType,
  // 閉じるボタン
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.close-btn'),
  } as Mo00611OnewayType,
  // 印刷ボタン
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
  } as Mo00609OnewayType,
  // 日付印刷
  mo00039OneWay: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  // 利用者選択
  mo00039OneWayUserSelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  // 履歴選択
  mo00039OneWayHistorySelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  Or28394: {
    ...props.onewayModelValue,
  },
  // 敬称を変更するチェックボックス
  mo00018OneWayChangeTitle: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.honorifics-change'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 敬称テキストボックス
  mo00045OnewayTextInput: {
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
    width: '60',
    maxLength: '2',
  } as Mo00045OnewayType,
  // 記入用シートを印刷するチェックボックス
  mo00018OneWayPrintTheForm: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-entry-sheet'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 印刷する要介護度
  mo00040OnewayPrintNursingCareRequired: {
    name: 'PrintNursingCareRequiredSelect',
    showItemLabel: true,
    itemLabel: t('label.print-nursing-care-required'),
    itemTitle: 'label',
    itemValue: 'value',
    isRequired: false,
    items: [],
  } as Mo00040OnewayType,
  // 担当ケアマネラベル
  mo01338OneWayCareManagerInCharge: {
    value: t('label.care-manager-in-charge'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  // 担当ケアマネ選択アイコン
  mo00009OnewayType: {
    icon: true,
    btnIcon: 'edit_square',
    prependIcon: 'edit_square',
  } as Mo00009OnewayType,
  // 担当ケアマネ表示ラベル
  mo01338OneWayCareManagerInChargeLabel: {
    value: '',
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  // 印刷設定帳票出力状態リスト
  orX0117Oneway: {
    reportId: '',
    type: '1',
    reportData: {} as InWebEntity,
    outputType: reportOutputType.DOWNLOAD,
    historyList: [] as OrX0117History[],
    replaceKey: 'printHistoryList',
  } as OrX0117OnewayType,
  // 「印刷設定」ダイアログ
  mo00024Oneway: {
    width: 'auto',
    maxWidth: '1420px',
    height: 'auto',
    persistent: true,
    showCloseBtn: true,
    disabledCloseBtnEvent: false,
    mo01344Oneway: {
      toolbarTitle: t('label.print-set'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
      scrollable: false,
    },
  },
  // チェックボックス
  mo00018OnewayType: {
    showItemLabel: false,
    checkboxLabel: '',
    isVerticalLabel: true,
    hideDetails: true,
    outerDivClass: '',
  } as Mo00018OnewayType,
  // GUI01186_担当ケアマネ検索画面
  or26261OneWay: {
    // 未設定フラグ
    misetteiFlg: '',
    // モード
    selectMode: '',
    // 職員ID
    shokuinId: [],
    // 適用事業所ＩＤリスト
    svJigyoId: [],
    // 表示するカラム
    hyoujiColumn: [],
    // 本日の勤務予定者のみを選択する
    isToday: true,
    // 勤務表パターン表示フラグ
    pattern: '',
    // 資格免許非活性フラグ
    qualification: '',
    // 初期選択事業所ID
    defSvJigyoId: '',
    // 基準日
    kijunYmd: '',
    // フィルターフラグ
    filterDwFlg: '',
  } as Or26261OnewayType,
  // 出力帳票名一覧
  mo01334Oneway: {
    headers: [
      {
        title: t('label.report'),
        key: 'prtTitle',
        sortable: false,
        minWidth: '100',
      },
    ],
    items: [],
    height: 655,
  } as Mo01334OnewayType,
  // 作成年月日印刷
  or31319Oneway: {
    // 作成年月日印刷区分
    mo00039OneWayPrintDateCreation: {
      name: '',
      showItemLabel: false,
      inline: false,
    } as Mo00039OnewayType,
    // 作成年月日
    mo01338OneWayPrintDateCreation: {
      value: '',
      customClass: {
        labelStyle: 'display: none',
      } as CustomClass,
    } as Mo01338OnewayType,
  } as Or31319OnewayType,
  // 承認欄を印刷する
  mo00018OneWayConfirmFormCopying: {
    checkboxLabel: t('label.confirm-form-copying'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 承認欄の登録ボタン
  mo00611ApprovalColumnRegist: {
    btnLabel: t('btn.confirm-form-registration‌'),
  } as Mo00611OnewayType,
  // 承認欄登録
  orX0135Oneway: {
    // ★ｻｰﾋﾞｽ事業者ID
    svJigyoId: '',
    // 法人ID
    houjinId: '',
    // 施設ID
    shisetuId: '',
    // 帳票コード
    chohyoCd: '',
  } as OrX0135OnewayType,
  // 担当ケアマネ
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: true,
    multiple: false,
    selectedUserCounter: '2',
    customClass: new CustomClass({ outerClass: 'mr-0', labelClass: 'ma-0' }),
  } as OrX0145OnewayType,
})

// ダイアログ設置
const mo00024 = ref<Mo00024Type>({
  isOpen: Or28394Const.DEFAULT.IS_OPEN,
})

/**************************************************
 * Pinia
 **************************************************/

const { setState } = useScreenOneWayBind<Or28394StateType>({
  cpId: Or28394Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or28394Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子モジュールに値を渡す
setChildrenValue()

/**
 * エラーダイアログの開閉
 *
 * @param text - メッセージ
 *
 * @param btn - ボタンラベル
 *
 * @param isError - エラーかどうか
 *
 * @returns ダイアログの選択結果（yes）
 */
const showMessageBox = async (text: string, btn: string, isError = true) => {
  if (isError) {
    return await openErrorDialog(text, btn)
  } else {
    return await openInfoDialog(text)
  }
}

/**
 * ダイアログの開閉
 *
 * @param text - メッセージ
 *
 * @param btn - ボタンラベル
 */
function openErrorDialog(text: string, btn: string) {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      dialogTitle: t('label.error'),
      dialogText: t(text),
      firstBtnType: 'normal1',
      firstBtnLabel: t(btn) ?? t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813.value.uniqueCpId)
        let result = Or28394Const.DEFAULT.YES
        if (event?.firstBtnClickFlg) {
          result = Or28394Const.DEFAULT.YES
        }
        // 確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 確認ダイアログの開閉
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes）
 */
async function openInfoDialog(paramDialogText: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t(paramDialogText),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

onMounted(async () => {
  isInit.value = true
  // 指定日 システム日付
  local.mo00020Type.value = systemCommonsStore.getSystemDate!
  // 汎用コードマスタデータを取得し初期化
  await initCodes()
  // 印刷設定情報リスト
  await getPrintSettingList()
})

/**
 * 汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 日付印刷区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    // 単複数選択区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY },
    // 作成年月日印刷区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CREATED_DATE_PRINT_TYPE },
    // 印刷する要介護度（新）
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_PRINTED_CARE_LEVEL_NEW },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  // 回数区分
  localOneway.mo00039OneWay.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )

  // 利用者選択
  localOneway.mo00039OneWayUserSelectType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )

  // 履歴選択
  localOneway.mo00039OneWayHistorySelectType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )

  // 作成年月日印刷区分
  localOneway.or31319Oneway.mo00039OneWayPrintDateCreation.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_CREATED_DATE_PRINT_TYPE
  )

  // 印刷する要介護度（新）
  localOneway.mo00040OnewayPrintNursingCareRequired.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_PRINTED_CARE_LEVEL_NEW
  )

  // 選択モート 初期値
  localOneway.orX0130Oneway.selectMode = Or28394Const.DEFAULT.TANI
  // 選択モート 単一複数フラグ
  localOneway.orX0143Oneway.singleFlg = Or28394Const.DEFAULT.TANI
  // 履歴一覧セクションフラグ
  mo01334TypeHistoryFlag.value = true
  // 利用者列幅
  userCols.value = 6
}

/**
 * 印刷オプションの有効化設定
 */
function setPrintOptions() {
  // 画面.利用者選択が「複数」、又は、画面.履歴選択が「複数」の場合
  if (
    localOneway.orX0130Oneway.selectMode === Or28394Const.DEFAULT.HUKUSUU ||
    localOneway.orX0143Oneway.singleFlg === Or28394Const.DEFAULT.HUKUSUU
  ) {
    // チェックOFF、非活性
    localOneway.mo00018OneWayPrintTheForm.disabled = false
  }
  // 以外の場合
  else {
    // 活性
    localOneway.mo00018OneWayPrintTheForm.disabled = true
  }
}

/**
 * 担当ケアマネ絞込み利用者ID取得
 */
async function getFilterUserId() {
  const inputData: TantoFilterUserIdSelectInEntity = {
    // 担当ケアマネID：画面.担当ケアマネID
    tantoId: localOneway.Or28394.tantoShokuId,
    // 担当開始日：親画面.システム年月日
    startYmd: localOneway.Or28394.sysYmd,
    // 担当終了日：親画面.システム年月日
    endYmd: localOneway.Or28394.sysYmd,
  }

  // 担当ケアマネ絞込み利用者ID取得
  const ret: TantoFilterUserIdSelectOutEntity = await ScreenRepository.select(
    'tantoFilterUserIdSelect',
    inputData
  )

  // API戻り値.担当ケアマネ絞込み利用者IDリストの件数が0以外の場合
  if (ret.data.tantoFltUserIdList?.length > 0) {
    // API戻り値.担当ケアマネ絞込み利用者IDリストより、親画面.ユーザリストをフィルタする
    const allowedUserIds = ret.data.tantoFltUserIdList.map((user) => user.userId)

    localOneway.Or28394.userList = localOneway.Or28394.userList.filter((user) =>
      allowedUserIds.includes(user.userId)
    )
  }
}

/**
 * 印刷設定画面初期情報を取得する
 */
async function getPrintSettingList() {
  const inputData: PrintSelectInEntity = {
    // セクション名:親画面.セクション名
    sectionName: localOneway.Or28394.sectionName,
    // 職員ID:親画面.職員ID
    shokuId: localOneway.Or28394.shokuId,
    // 法人ID:親画面.法人ID
    houjinId: localOneway.Or28394.houjinId,
    // 施設ID:親画面.施設ID
    shisetuId: localOneway.Or28394.shisetuId,
    // 事業所ID:親画面.事業所ID
    svJigyoId: localOneway.Or28394.svJigyoId,
    // システムコード:共通情報.システムコード
    gsyscd: localOneway.Or28394.systemCode,
    // 利用者ID:親画面.利用者ID
    userId: localOneway.Or28394.userId,
  }

  // バックエンドAPIから初期情報取得
  const ret = await printCom.doPrintGet<
    PrintSelectInEntity,
    IPrintInfo,
    KikanRirekiData,
    DailyRoutinePlanPrintSettingsInitUpdateOutEntity
  >(inputData, 'dailyRoutinePlanPrintSettingsInitUpdate')
  // 印刷設定情報リスト、期間履歴情報リスト、期間管理フラグを取得
  printCom.doGetPrintData<
    DailyRoutinePlanPrintSettingsInitUpdateOutEntity,
    IPrintInfo,
    KikanRirekiData
  >(ret, local.or28394)
  // Onewayの期間管理フラグを設定
  localOneway.orX0143Oneway.kikanFlg = localOneway.Or28394.kikanFlg

  // 週間表履歴リスト
  localOneway.orX0143Oneway.rirekiList = local.or28394.kikanRirekiList

  // Onewayの印刷設定情報リストを設定
  const mo01334OnewayList: Mo01334Items[] = []
  for (const item of local.or28394.prtList) {
    if (item) {
      mo01334OnewayList.push({
        id: item.prtNo,
        mo01337OnewayReport: {
          value: item.prtTitle,
          unit: '',
        } as Mo01337OnewayType,
        ...item,
      } as Mo01334Items)
    }
  }
  // Onewayの印刷設定情報リストを設定
  localOneway.mo01334Oneway.items = mo01334OnewayList

  // 印刷設定情報リストの1件目を設定する
  if (local.or28394.prtList.length > 0) {
    local.mo01334.value = local.or28394.prtList[0].prtNo
  }

  // 印刷オプションの有効化設定
  setPrintOptions()
  // 子モジュールに値を渡す
  setChildrenValue()
}

/**
 * 履歴情報を取得する
 *
 * @param userId - 利用者ID
 */
async function getHistoricalInfoList(userId: string) {
  localOneway.orX0143Oneway.rirekiList = []
  const ret = await printCom.doUserClick<
    DailyRoutinePlanPrintSettingsUserChangeSelectInEntity,
    KikanRirekiData,
    DailyRoutinePlanPrintSettingsUserChangeSelectOutEntity
  >(
    {
      // 事業所ＩＤ:親画面.事業所ＩＤ
      svJigyoId: localOneway.Or28394.svJigyoId,
      // 利用者ID:画面.利用者一覧に選択行の利用者ID
      userId: userId,
      // 期間管理フラグ
      kikanFlg: localOneway.Or28394.kikanFlg,
    },
    'dailyRoutinePlanPrintSettingsUserChangeSelect'
  )
  // 週間表履歴リスト
  localOneway.orX0143Oneway.rirekiList = ret
  // 初期表示時のみ、親画面.履歴IDを設定
  if (isInit.value) {
    localOneway.orX0143Oneway.rirekiId = localOneway.Or28394.rirekiId
    isInit.value = false
  } else {
    localOneway.orX0143Oneway.rirekiId = ''
  }
}

/**
 * 子モジュールに値を渡す
 *
 */
function setChildrenValue() {
  setChildCpBinds(props.uniqueCpId, {
    // 帳票タイトル
    [Or26328Const.CP_ID(0)]: {
      twoWayValue: {
        titleInput: local.titleInput,
      },
    },
    // 出力帳票名一覧
    [Or26326Const.CP_ID(0)]: {
      twoWayValue: {
        mo01334Type: local.mo01334,
      },
    },
    // 日付印刷
    [Or28780Const.CP_ID(0)]: {
      twoWayValue: {
        mo00039Type: local.mo00039Type,
        mo00020Type: local.mo00020Type,
      },
    },
    // 作成年月日印刷
    [Or31319Const.CP_ID(0)]: {
      twoWayValue: {
        mo00039TypePrintDateCreation: local.mo00039TypePrintDateCreation,
      },
    },
    // 敬称変更
    [Or18615Const.CP_ID(0)]: {
      twoWayValue: {
        mo00018TypeChangeTitle: local.mo00018TypeChangeTitle,
        mo00045Type: local.textInput,
      },
    },
    // 利用者選択方法
    [Or26331Const.CP_ID(0)]: {
      twoWayValue: {
        mo00039OneWayUserSelectType: localOneway.orX0130Oneway.selectMode,
        mo00020TypeKijunbi: local.mo00020TypeKijunbi,
        mo00039OneWayHistorySelectType: localOneway.orX0143Oneway.singleFlg,
      },
    },
  })
}

/**
 * 「閉じるボタン」押下
 */
async function close() {
  setPrtList(local.mo01334.value)
  // 画面の印刷設定情報を保存する
  await printCom.doPrintClose(
    local.titleInput,
    {
      // セクション名:親画面.セクション名
      sectionName: localOneway.Or28394.sectionName,
      // システムコード：親画面.システムコード
      gsyscd: localOneway.Or28394.systemCode,
      // 職員ID：親画面.職員ID
      shokuId: localOneway.Or28394.shokuId,
      // 法人ID：親画面.法人ID
      houjinId: localOneway.Or28394.houjinId,
      // 施設ID：親画面.施設ID
      shisetuId: localOneway.Or28394.shisetuId,
      // 事業者ID：親画面.事業者ID
      svJigyoId: localOneway.Or28394.svJigyoId,
      // 印刷設定情報リスト：印刷設定情報リスト
      prtList: local.or28394.prtList,
    },
    'dailyRoutinePlanPrintSettingsInfoUpdate',
    localOneway.mo01334Oneway.items,
    local.mo01334.value,
    showMessageBox,
    closeDialog
  )
}

/**
 * 本画面を閉じる
 */
function closeDialog() {
  setState({ isOpen: false })
}

/**
 * 「印刷」ボタン押下
 */
async function print() {
  // 業務チェックを行う
  if (
    !printCom.doCheckBeforePrint(
      local.titleInput.value,
      local.mo00018TypePrintTheForm.modelValue,
      localOneway.Or28394.userList.length,
      localOneway.orX0143Oneway.rirekiList.length,
      showMessageBox
    )
  ) {
    return
  }
  debugger
  setPrtList(local.mo01334.value)
  // PDFダウンロード
  // 業務共通化処理が下記を参照する
  await executePrint()
}

/**
 *  PDFダウンロード
 */
async function executePrint() {
  // 利用者配下の履歴情報を再取得する
  const resSubjectData = await printCom.doRetryRirekiData<
    PrintSubjectSelectInEntity,
    PrtHistoryList,
    DailyRoutinePlanPrintSettingsSubjectSelectOutEntity
  >(
    {
      // 利用者リスト：画面.利用者一覧で選択した利用者リスト
      userList: selectedUserList.value.map((x) => {
        return {
          userId: x.userId,
          userName: x.name1Knj + ' ' + x.name2Knj,
        }
      }),
      // 事業所ID：親画面.事業所ID
      svJigyoId: localOneway.Or28394.svJigyoId,
      // 基準日：画面.基準日
      kijunbiYmd: local.mo00020TypeKijunbi.value,
    },
    localOneway.orX0130Oneway.selectMode,
    'dailyRoutinePlanPrintSettingsSubjectSelect'
  )

  // 画面の印刷設定情報を保存する。
  const inputData: PrintCloseUpdateEntity<IPrintInfo> = {
    // セクション名:親画面.セクション名
    sectionName: localOneway.Or28394.sectionName,
    // システムコード：親画面.システムコード
    gsyscd: localOneway.Or28394.systemCode,
    // 職員ID：親画面.職員ID
    shokuId: localOneway.Or28394.shokuId,
    // 法人ID：親画面.法人ID
    houjinId: localOneway.Or28394.houjinId,
    // 施設ID：親画面.施設ID
    shisetuId: localOneway.Or28394.shisetuId,
    // 事業者ID：親画面.事業者ID
    svJigyoId: localOneway.Or28394.svJigyoId,
    // 印刷設定情報リスト：印刷設定情報リスト
    prtList: local.or28394.prtList,
  }
  // 印刷設定情報を保存する
  await printCom.doPrintUpdate(inputData, 'dailyRoutinePlanPrintSettingsInfoUpdate', showMessageBox)
  // 利用者選択が「単一」、且つ、履歴選択が「単一」の場合
  if (
    localOneway.orX0130Oneway.selectMode === Or28394Const.DEFAULT.TANI &&
    localOneway.orX0143Oneway.singleFlg === Or28394Const.DEFAULT.TANI
  ) {
    // 業務共通化処理の設定
    const selectedPrtData = local.or28394.prtList.find((x) => x.prtNo === local.mo01334.value)
    if (selectedPrtData) {
      void printCom.doReportOutput(
        selectedPrtData,
        resSubjectData,
        localOneway.orX0130Oneway.selectMode,
        localOneway.orX0143Oneway.singleFlg,
        selectedUserList.value,
        selectedRirekiData.value,
        {
          // 記入用シートを印刷するフラグ
          printTheForm: local.mo00018TypePrintTheForm.modelValue,
          // 指定日
          designatedDate: local.mo00020Type.value,
          // 初期設定マスタの情報
          initMasterObj: localOneway.Or28394.initMasterObj,
          // 事業者名
          svJigyoId: localOneway.Or28394.svJigyoId,
        }
      )
    }
  } else {
    // PDFダウンロードを行う
    OrX0117Logic.state.set({
      uniqueCpId: orX0117.value.uniqueCpId,
      state: {
        isOpen: true,
      },
    })
  }
}

/**
 * 承認欄登録ボタン押下時の処理
 */
function onClickApprovalColumnRegist() {
  // 承認欄情報：親画面.承認欄情報（0：共有する、1：帳票毎保持する）
  // localOneway.orX0135Oneway.shoninFlg = localOneway.Or28394.initMasterObj.shoninFlg
  // 事業所ID：親画面.事業所ID
  localOneway.orX0135Oneway.svJigyoId = localOneway.Or28394.svJigyoId
  // セクション：印刷設定情報リスト.プロファイル
  localOneway.orX0135Oneway.chohyoCd =
    local.or28394.prtList.find((x) => x.prtNo === local.mo01334.value)?.profile ?? ''
  // 法人ID：親画面.法人ID
  localOneway.orX0135Oneway.houjinId = localOneway.Or28394.houjinId
  // 施設ID：親画面.施設ID
  localOneway.orX0135Oneway.shisetuId = localOneway.Or28394.shisetuId

  // 「GUI00617_承認欄登録」画面をポップアップで起動する
  OrX0135Logic.state.set({
    uniqueCpId: orX0135.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 印刷設定情報リストの値を設定する
 *
 * @param index - 印刷設定情報リストのインデックス
 */
function setPrtList(index: string) {
  // 印刷設定情報リストのインデックス
  const idx = local.or28394.prtList.findIndex((x) => x.prtNo === index)
  if (idx !== -1) {
    // 選択された印刷設定情報リストのデータ
    const selectedData = local.or28394.prtList[idx]
    // 帳票タイトル名
    selectedData.prtTitle = local.titleInput.value
    // 日付表示有無
    selectedData.prnDate = local.mo00039Type
    // 敬称を変更する
    selectedData.param03 = local.mo00018TypeChangeTitle.modelValue
      ? Or28394Const.DEFAULT.CHECK_ON
      : Or28394Const.DEFAULT.CHECK_OFF
    // 敬称
    selectedData.param04 = local.textInput.value
    // 承認欄を印刷する
    selectedData.param05 = local.mo00018TypeConfirmFormCopying.modelValue
      ? Or28394Const.DEFAULT.CHECK_ON
      : Or28394Const.DEFAULT.CHECK_OFF
    // 記入日を印刷する
    selectedData.param06 = local.mo00018TypePrintMode.modelValue
      ? Or28394Const.DEFAULT.CHECK_ON
      : Or28394Const.DEFAULT.CHECK_OFF
    // 印刷する要介護度
    selectedData.param07 = local.mo00040PrintNursingCareRequired.modelValue ?? ''
    // 作成年月日印刷区分
    selectedData.param13 = local.mo00039TypePrintDateCreation
  }
}

/**
 * 「出力帳票名」選択
 */
watch(
  () => Or26326Logic.data.get(or26326.value.uniqueCpId)?.mo01334Type.value,
  (newValue, oldValue) => {
    if (newValue) {
      local.mo01334.value = newValue
      setPrintOptions()
    }
    if (oldValue) {
      for (const item of localOneway.mo01334Oneway.items) {
        if (item) {
          if (oldValue === item.id) {
            // 画面.帳票タイトルが空白以外の場合
            // 画面.出力帳票一覧明細に切替前選択される行.帳票タイトル = 画面.帳票タイトル
            if (local.titleInput.value) item.prtTitle = local.titleInput.value
            // 画面.出力帳票一覧明細に切替前選択される行.日付表示有無 = 画面.日付印刷区分
            item.prnDate = local.mo00039Type
            // 指定日
            item.mo00020Type = local.mo00020Type.value ?? systemCommonsStore.getSystemDate!
            // 画面.出力帳票一覧明細に切替前選択された行.パラメータ03 = 画面.敬称を変更する
            item.param03 = local.mo00018TypeChangeTitle.modelValue
              ? Or28394Const.DEFAULT.CHECK_ON
              : Or28394Const.DEFAULT.CHECK_OFF
            // 画面.出力帳票一覧明細に切替前選択された行.パラメータ04 = 画面.敬称
            item.param04 = local.textInput.value
            // 画面.出力帳票一覧明細に切替前選択された行.パラメータ05 = 画面.承認欄を印刷する
            item.param05 = local.mo00018TypeConfirmFormCopying.modelValue
              ? Or28394Const.DEFAULT.CHECK_ON
              : Or28394Const.DEFAULT.CHECK_OFF
            // 画面.出力帳票一覧明細に切替前選択された行.パラメータ06 = 画面.記入日を印刷する
            item.param06 = local.mo00018TypePrintMode.modelValue
              ? Or28394Const.DEFAULT.CHECK_ON
              : Or28394Const.DEFAULT.CHECK_OFF
            // 画面.出力帳票一覧明細に切替前選択された行.パラメータ07 = 画面.印刷する要介護度
            item.param07 = local.mo00040PrintNursingCareRequired.modelValue ?? ''
            // 画面.基準日
            item.mo00020TypeKijunbi = local.mo00020TypeKijunbi.value
            // 画面.出力帳票一覧明細に切替前選択された行.パラメータ13 = 画面.作成年月日印刷区分
            item.param13 = local.mo00039TypePrintDateCreation
            setPrtList(oldValue)
          }
        }
      }
    }
    for (const item of localOneway.mo01334Oneway.items) {
      if (item) {
        if (newValue === item.id) {
          // 画面.帳票タイトル = 画面.出力帳票一覧明細に選択される行.帳票タイトル
          local.titleInput.value = item?.prtTitle as string
          // 画面.出力帳票一覧明細に切替前選択される行.日付表示有無 = 画面.日付印刷区分
          local.mo00039Type = item?.prnDate as string
          // 指定日
          local.mo00020Type = {
            value: (item?.mo00020Type as string) ?? systemCommonsStore.getSystemDate!,
          } as Mo00020Type
          // 画面.敬称を変更する = 画面.出力帳票一覧明細に選択される行.パラメータ03
          local.mo00018TypeChangeTitle.modelValue = item?.param03 === Or28394Const.DEFAULT.CHECK_ON
          // 画面.敬称 = 画面.出力帳票一覧明細に選択される行.パラメータ04
          local.textInput.value = item?.param04 as string
          // 画面.記入用シートを印刷する = 0(チェックオフ)
          local.mo00018TypePrintTheForm.modelValue = true
          // 画面.承認欄を印刷する = 画面.出力帳票一覧明細に選択される行.パラメータ05
          local.mo00018TypeConfirmFormCopying.modelValue =
            item?.param05 === Or28394Const.DEFAULT.CHECK_ON
          // 画面.画面.記入日を印刷する = 画面.出力帳票一覧明細に選択される行.パラメータ06
          local.mo00018TypePrintMode.modelValue = item?.param06 === Or28394Const.DEFAULT.CHECK_ON
          // 画面.画面.印刷する要介護度 = 画面.出力帳票一覧明細に選択される行.パラメータ07
          local.mo00040PrintNursingCareRequired.modelValue = item?.param07 as string
          // 画面.基準日
          local.mo00020TypeKijunbi.value = (item?.mo00020TypeKijunbi ??
            systemCommonsStore.getSystemDate!) as string
          // 画面.作成年月日印刷区分 = 画面.出力帳票一覧明細に選択される行.パラメータ13
          local.mo00039TypePrintDateCreation = item?.param13 as string
          setPrtList(newValue)
        }
      }
    }
    // 子モジュールに値を渡す
    setChildrenValue()
  }
)

/**
 * （ダイアログ）の開閉状態を監視
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    mo00024.value.isOpen = true
    if (!newValue) {
      await close()
    }
  }
)

/**
 * 履歴選択ラジオボタン選択の監視
 */
watch(
  () => Or26331Logic.data.get(or26331.value.uniqueCpId)?.mo00039OneWayHistorySelectType,
  (newValue) => {
    if (newValue) {
      // 履歴選択方法が「単一」の場合
      if (newValue === Or28394Const.DEFAULT.TANI) {
        // 単一複数フラグ
        localOneway.orX0143Oneway.singleFlg = OrX0143Const.DEFAULT.TANI
        localOneway.orX0117Oneway.type = '1'
      }
      if (newValue === Or28394Const.DEFAULT.HUKUSUU) {
        // 単一複数フラグ
        localOneway.orX0143Oneway.singleFlg = OrX0143Const.DEFAULT.HUKUSUU
        localOneway.orX0117Oneway.type = '0'
        // 画面.記入用シートを印刷するをチェックオフ、非活性にする
        local.mo00018TypePrintTheForm.modelValue = false
      }
      // 印刷オプションの有効化設定
      setPrintOptions()
      // 子モジュールに値を渡す
      setChildrenValue()
    }
  }
)

/**
 * 利用者選択ラジオボタン選択の監視
 */
watch(
  () => Or26331Logic.data.get(or26331.value.uniqueCpId)?.mo00039OneWayUserSelectType,
  (newValue) => {
    if (newValue) {
      if (newValue === Or28394Const.DEFAULT.TANI) {
        // 選択モート
        localOneway.orX0130Oneway.selectMode = Or28394Const.DEFAULT.TANI
        userCols.value = 6
        mo01334TypeHistoryFlag.value = true
        localOneway.orX0117Oneway.type = '1'
      }
      if (newValue === Or28394Const.DEFAULT.HUKUSUU) {
        localOneway.orX0117Oneway.type = '0'
        // 選択モート
        localOneway.orX0130Oneway.selectMode = OrX0130Const.DEFAULT.HUKUSUU
        // 画面.記入用シートを印刷するをチェックオフ、非活性にする
        local.mo00018TypePrintTheForm.modelValue = false
        userCols.value = 11
        mo01334TypeHistoryFlag.value = false
      }
      // 印刷オプションの有効化設定
      setPrintOptions()
      // 子モジュールに値を渡す
      setChildrenValue()
    }
  }
)

/**
 * 「履歴一覧」明細選択の監視
 */
watch(
  () => OrX0143Logic.event.get(orX0143.value.uniqueCpId),
  (newValue) => {
    if (!newValue) return
    selectedRirekiData.value = newValue.orX0143DetList as KikanRirekiData[]
    // 画面.利用者選択が「複数」選択、又は、画面.履歴選択が「複数」選択の場合
    if (
      localOneway.orX0143Oneway.singleFlg === OrX0143Const.DEFAULT.HUKUSUU ||
      localOneway.orX0130Oneway.selectMode === OrX0130Const.DEFAULT.HUKUSUU
    ) {
      // 空を設定
      localOneway.or31319Oneway.mo01338OneWayPrintDateCreation.value = ''
    }
    // 以外の場合
    else {
      // 画面.履歴一覧.選択行.作成日を設定
      localOneway.or31319Oneway.mo01338OneWayPrintDateCreation.value =
        selectedRirekiData.value.at(0)?.createYmd ?? ''
    }
  }
)

/**
 * 日付印刷の監視
 */
watch(
  () => Or28780Logic.data.get(or28780.value.uniqueCpId),
  (newValue) => {
    if (newValue) {
      // 日付印刷区分
      local.mo00039Type = newValue.mo00039Type
      // 指定日
      local.mo00020Type = newValue.mo00020Type
    }
  }
)

/**
 * 帳票タイトルの監視
 */
watch(
  () => Or26328Logic.data.get(or26328.value.uniqueCpId),
  (newValue) => {
    if (newValue) {
      // タイトル
      local.titleInput = newValue.titleInput
    }
  }
)

/**
 * 印刷オプションの監視
 */
watch(
  () => Or18615Logic.data.get(or18615.value.uniqueCpId),
  (newValue) => {
    if (newValue) {
      // 敬称を変更する
      local.mo00018TypeChangeTitle = newValue.mo00018TypeChangeTitle
      // 敬称テキスト
      local.textInput = newValue.mo00045Type
    }
  }
)

/**
 * 担当ケアマネ検索画面の開閉状態を監視
 */
watch(
  () => local.or26261,
  async (newValue) => {
    // 画面.担当ケアマネ表示ラベルに戻り値.担当ケアマネ名を設定する
    localOneway.mo01338OneWayCareManagerInChargeLabel.value =
      newValue.shokuin.shokuin1Knj + ' ' + newValue.shokuin.shokuin2Knj
    // 画面.担当ケアマネIDに戻り値.担当ケアマネIDを設定する
    local.or26261Id = newValue.shokuin.shokuId
    // 利用者選択が「単一」の場合
    // 選択用の利用者IDに表示用「利用者リスト」一件目.利用者IDを設定する
    // 利用者選択が「複数」の場合
    // 選択用の利用者IDに空文字を設定する
    await getHistoricalInfoList(
      localOneway.orX0130Oneway.selectMode === Or28394Const.DEFAULT.TANI
        ? (newValue.shokuIdList.at(0) ?? '')
        : ''
    )
    // 担当ケアマネ絞込み利用者ID取得
    await getFilterUserId()
  }
)

/**
 * 「利用者選択」の監視
 */
watch(
  () => OrX0130Logic.event.get(orX0130.value.uniqueCpId),
  async (newValue) => {
    selectedUserList.value = newValue!.userList
    if (newValue?.clickFlg && localOneway.orX0130Oneway.selectMode === Or28394Const.DEFAULT.TANI) {
      // 利用者選択
      // 利用者選択方法が「単一」の場合
      await getHistoricalInfoList(newValue.userList[0].userId)
    }
  }
)

/**
 * 担当ケアマネの監視
 */
watch(
  () => local.orX0145,
  (newValue) => {
    if (newValue) {
      localOneway.orX0130Oneway.tantouCareManager = (newValue.value as TantoCmnShokuin).shokuinKnj
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        no-gutter
        class="or28394_screen"
      >
        <!-- 帳票 -->
        <g-custom-or-26326
          v-bind="or26326"
          :oneway-model-value="localOneway.mo01334Oneway"
        />
        <c-v-col
          cols="12"
          sm="4"
          class="pa-0 pt-2 or28394_border_right content_center"
        >
          <!-- タイトル -->
          <g-custom-or-26328 v-bind="or26328" />
          <c-v-divider class="my-0"></c-v-divider>
          <!-- 日付印刷 -->
          <g-custom-or-28780
            v-bind="or28780"
            :oneway-model-value="localOneway.mo00039OneWay"
          />
          <c-v-divider class="my-0"></c-v-divider>
          <!-- 作成年月日印刷 -->
          <g-custom-or-31319
            v-bind="or31319"
            :oneway-model-value="localOneway.or31319Oneway"
          />
          <c-v-divider class="my-0"></c-v-divider>
          <!-- 印刷オプション -->
          <g-custom-or-18615
            v-bind="or18615"
            :oneway-model-value="{
              mo00018OneWayChangeTitle: localOneway.mo00018OneWayChangeTitle,
              mo00045OnewayTextInput: localOneway.mo00045OnewayTextInput,
            }"
          >
            <template #optionPrintItems>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-2"
              >
                <c-v-row no-gutter>
                  <c-v-col cols="auto">
                    <!-- 承認欄を印刷する -->
                    <base-mo00018
                      v-model="local.mo00018TypeConfirmFormCopying"
                      :oneway-model-value="localOneway.mo00018OneWayConfirmFormCopying"
                    >
                    </base-mo00018>
                  </c-v-col>
                  <c-v-col style="align-content: center">
                    <base-mo00611
                      :oneway-model-value="localOneway.mo00611ApprovalColumnRegist"
                      @click="onClickApprovalColumnRegist()"
                    ></base-mo00611>
                  </c-v-col>
                </c-v-row>
              </c-v-col>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-2"
              >
                <!-- 記入用シートを印刷するチェックボックス -->
                <base-mo00018
                  v-model="local.mo00018TypePrintTheForm"
                  :oneway-model-value="localOneway.mo00018OneWayPrintTheForm"
                >
                </base-mo00018>
              </c-v-col>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-0 ml-2"
              >
                <!-- 印刷する要介護度 -->
                <base-mo00040
                  v-model="local.mo00040PrintNursingCareRequired"
                  :oneway-model-value="localOneway.mo00040OnewayPrintNursingCareRequired"
                  class="mr-2 pl-2 pr-2"
                />
              </c-v-col>
            </template>
          </g-custom-or-18615>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="6"
          class="pa-2"
        >
          <c-v-row
            class="or28394_row"
            no-gutter
            style="align-items: center"
          >
            <!-- 利用者選択 -->
            <g-custom-or-26331
              v-bind="or26331"
              :oneway-model-value="{
                mo00039OneWayUserSelectType: localOneway.mo00039OneWayUserSelectType,
                mo00039OneWayHistorySelectType: localOneway.mo00039OneWayHistorySelectType,
              }"
              :unique-cp-id="or26331.uniqueCpId"
            />
            <!-- 担当ケアマネラベル -->
            <g-custom-or-x-0145
              v-bind="orX0145"
              v-model="local.orX0145"
              :oneway-model-value="localOneway.orX0145Oneway"
            ></g-custom-or-x-0145>
          </c-v-row>
          <c-v-row
            class="or28394_row grid-width"
            no-gutter
          >
            <c-v-col
              :cols="userCols"
              style="overflow-x: auto"
            >
              <!-- 印刷設定画面利用者一覧 -->
              <g-custom-or-x-0130
                v-if="localOneway.orX0130Oneway.selectMode"
                v-bind="orX0130"
                :oneway-model-value="localOneway.orX0130Oneway"
              >
              </g-custom-or-x-0130>
            </c-v-col>
            <c-v-col
              v-if="mo01334TypeHistoryFlag"
              cols="12"
              sm="6"
              class="pl-2 pr-0"
            >
              <!-- 計画期間＆履歴一覧 -->
              <g-custom-or-x-0143
                v-if="localOneway.orX0143Oneway.singleFlg"
                v-bind="orX0143"
                :oneway-model-value="localOneway.orX0143Oneway"
              ></g-custom-or-x-0143>
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>

    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close()"
        ></base-mo00611>
        <!-- 印刷ボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          class="mr-2"
          :disabled="prtFlg"
          @click="print()"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  >
  </g-base-or21813>
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  >
  </g-base-or21814>
  <!-- 印刷設定帳票出力状態リスト画面 -->
  <g-custom-or-x-0117
    v-if="showDialogOrx0117"
    v-bind="orX0117"
    :oneway-model-value="localOneway.orX0117Oneway"
  ></g-custom-or-x-0117>
  <g-custom-or-26261
    v-if="showDialogOr26261"
    v-bind="or26261"
    :oneway-model-value="localOneway.or26261OneWay"
  />
  <!-- 承認欄登録画面 -->
  <g-custom-or-x0135
    v-if="showDialogOr00586"
    v-bind="orX0135"
    v-model="local.orX0135"
    :oneway-model-value="localOneway.orX0135Oneway"
    :parent-unique-cp-id="orX0135.uniqueCpId"
  />
</template>

<style scoped lang="scss">
.or28394_screen {
  margin: -8px !important;
}

.or28394_border_right {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.or28394_row {
  margin: 0px !important;
}

.content_center {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  padding: 0px !important;

  .label_left {
    padding-right: 0px;
  }

  .label_right {
    padding-left: 0px;
    padding-right: 0px;
  }

  .printerOption {
    background-color: rgb(var(--v-theme-black-100));
  }

  :deep(.label-area-style) {
    display: none;
  }

  .customCol {
    margin-left: 0px;
    margin-right: 0px;
  }
}

.flex-center {
  display: flex;
  align-items: center;
}

.customCol {
  margin-left: 0px;
  margin-right: 0px;
}

.grid-width {
  min-width: 694px;
}
</style>
