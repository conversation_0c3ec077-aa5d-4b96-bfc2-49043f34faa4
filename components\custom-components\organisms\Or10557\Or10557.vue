<script setup lang="ts">
/**
 * Or10557有機体:共通サービスマスタモーダル
 * GUI01055_共通サービスマスタ
 *
 * @description
 * 共通サービスマスタ画面
 *
 * <AUTHOR>
 */

import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or28980Const } from '../Or28980/Or28980.constant'
import { Or06618Const } from '../Or06618/Or06618.constants'
import { Or10557Const } from './Or10557.constant'
import type { AsyncFunction, Or10557StateType } from './Or10557.type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import { useScreenOneWayBind, useScreenStore, useSetupChildProps } from '#build/imports'
import { useScreenUtils } from '~/utils/useScreenUtils'
import type {
  Or10557OneWayType,
} from '~/types/cmn/business/components/Or10557Type'
import type {
  CommonServiceMasterListHeader,
  CommonServiceMasterListItem,
  Or28980Type,
} from '~/types/cmn/business/components/Or28980Type'
import type { Or06618OneWayType } from '~/types/cmn/business/components/Or06618Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Or21814FirstBtnType, Or21814SecondBtnType, Or21814ThirdBtnType } from '~/components/base-components/organisms/Or21814/Or21814.type'

/**
 * useScreenUtils
 */
const { setChildCpBinds } = useScreenUtils()
/**
 * useI18n
 */
const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  onewayModelValue: Or10557OneWayType
}
/**
 * props
 */
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
/**
 * or28980:有機体:(内容マスタ)内容情報テーブル
 */
const or28980 = ref({ uniqueCpId: Or28980Const.CP_ID(0) })
/**
 * Or06618:有機体:(日課計画マスタ)コンテンツエリアタブ
 */
const or06618 = ref({ uniqueCpId: Or06618Const.CP_ID(0) })
/**
 * ローカルTwoway
 */
const local = reactive({
  // 本画面ダイアログ
  mo00024: {
    isOpen: Or10557Const.DEFAULT.IS_OPEN,
  } as Mo00024Type,
  mo00043: { id: Or10557Const.TAB.TAB_ID_COMMON_SERVICE_MASTER } as Mo00043Type,
  mo00043Transfer: { id: Or10557Const.TAB.TAB_ID_COMMON_SERVICE_MASTER } as Mo00043Type,
  mo00043Change: { id: Or10557Const.TAB.TAB_ID_COMMON_SERVICE_MASTER } as Mo00043Type,
  // 内容情報テーブル一覧
  or28980: {
    headers: [] as CommonServiceMasterListHeader[],
    items: [] as CommonServiceMasterListItem[],
  } as Or28980Type,
})

/**
 * ローカルOneway
 */
const localOneway = reactive({
  // 本画面
  or10557Oneway: {
    ...props.onewayModelValue,
  },
  or06618OneWay: { dailyScheduleMasterInData: {} } as Or06618OneWayType,
  // 本画面ダイアログOneway
  mo00024Oneway: {
    width: '900px',
    height: '850px',
    persistent: true,
    showCloseBtn: true,
    disabledCloseBtnEvent: true,
    mo01344Oneway: {
      name: 'Or10557',
      toolbarTitle: t('label.common-service-master'),
      toolbarTitleCenteredFlg: false,
      toolbarName: '',
      showCardActions: true,
      cardTextClass: 'card-text pa-0',
    },
  },
  // 閉じるコンポーネント
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  mo00609SaveOneway: {
    btnLabel: t('btn.save'),
  } as Mo00609OnewayType,
  // タブ
  mo00043OneWay: {
    tabItems: [
      { id: Or10557Const.TAB.TAB_ID_DAILY_CARE_PLANE, title: t('label.daily-care-plan') },
      {
        id: Or10557Const.TAB.TAB_ID_COMMON_SERVICE_MASTER,
        title: t('label.common-service-master'),
      },
    ],
  } as Mo00043OnewayType,
  mo01338Fourway: {
    value: t('label.preserved-by-each-office'),
    customClass: new CustomClass({
      outerClass: 'mr-0',
      labelClass: 'ma-0',
      itemClass: 'ml-0 align-left',
      itemStyle: 'color:  rgb(var(--v-theme-black-500));',
    }),
  } as Mo01338OnewayType,
})
/**
 * 有機体:(内容マスタ)内容情報テーブルRef
 */
const or28980Ref = ref<{ insert: AsyncFunction }>()
/**
 * 有機体:(日課計画マスタ)コンテンツエリアタブ Ref
 */
const or06618Ref = ref<{ insert: AsyncFunction }>()
/**************************************************
 * Pinia
 **************************************************/
/**
 * 確認ダイアログ
 */
const or21814_1 = ref({ uniqueCpId: '' })
const or21813_1 = ref({ uniqueCpId: '' })
useSetupChildProps(props.uniqueCpId, {
  [Or21813Const.CP_ID(1)]: or21813_1.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or28980Const.CP_ID(0)]: or28980.value,
  [Or06618Const.CP_ID(0)]: or06618.value,
})
/**
 * OneWayBind領域に関する処理
 */
const { setState } = useScreenOneWayBind<Or10557StateType>({
  cpId: Or10557Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    /**
     * 開閉フラグ
     *
     * @param value - 開閉フラグ
     */
    isOpen: (value) => {
      local.mo00024.isOpen = value ?? Or10557Const.DEFAULT.IS_OPEN
    },
  },
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/

/**************************************************
 * ウォッチャー
 **************************************************/
onMounted(() => {
  // コントロール設定
  local.mo00043.id = Or10557Const.TAB.TAB_ID_COMMON_SERVICE_MASTER
  localOneway.or06618OneWay.dailyScheduleMasterInData.shisetuId =
    localOneway.or10557Oneway.commonServiceMasterInData.shisetuId
  localOneway.or06618OneWay.dailyScheduleMasterInData.svJigyoId =
    localOneway.or10557Oneway.commonServiceMasterInData.svJigyoId
  localOneway.or06618OneWay.dailyScheduleMasterInData.svJigyoIdList =
    localOneway.or10557Oneway.commonServiceMasterInData.svJigyoIdList

  setChildCpBinds(props.uniqueCpId, {
    [Or06618Const.CP_ID(0)]: {
      twoWayValue: {},
    }
  })
})

/**
 * ダイアログ表示フラグ
 */
const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen ?? false
})
/**
 * ダイアログ表示フラグ
 */
const showDialogOr21813 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21813Logic.state.get(or21813_1.value.uniqueCpId)?.isOpen ?? false
})
/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => local.mo00024.emitType,
  async () => {
    if (local.mo00024.emitType === 'closeBtnClick') {
      await close()
      local.mo00024.emitType = undefined
    }
  }
)
/**************************************************
 * 関数
 **************************************************/
// メニュー切替
watch(
  () => local.mo00043Transfer.id,
  async (newValue) => {
    if (local.mo00043Transfer.id !== local.mo00043.id) {
      if (isEdit.value) {
        local.mo00043Transfer.id = local.mo00043.id
        local.mo00043Change.id = newValue
        const dialogResult = await openInfoDialog(t('message.i-cmn-10430'),'normal1', 'destroy1', 'normal3')

        switch (dialogResult) {
          case Or10557Const.DEFAULT.DIALOG_RESULT_YES:
            // はい選択時は入力内容を保存する
            if (local.mo00043.id === Or10557Const.TAB.TAB_ID_COMMON_SERVICE_MASTER) {
              const insertResult = await or28980Ref.value?.insert()
              if (insertResult?.isBreak) {
                if (insertResult.info) {
                  await openInfoDialog(insertResult.msg,'normal1', 'blank', 'blank')
                  break
                } else if(insertResult.error) {
                  await openErrorDialog(insertResult.msg)
                  break
                }
              }
            } else {
              await or06618Ref.value?.insert()
            }
            local.mo00043.id = local.mo00043Change.id
            local.mo00043Transfer.id = local.mo00043Change.id
            localOneway.mo00024Oneway.mo01344Oneway.name = local.mo00043Transfer.id
            if (local.mo00043Transfer.id === Or10557Const.TAB.TAB_ID_DAILY_CARE_PLANE) {
              localOneway.mo00024Oneway.mo01344Oneway.toolbarTitle = t('label.daily-care-plan')
            } else {
              localOneway.mo00024Oneway.mo01344Oneway.toolbarTitle = t(
                'label.common-service-master'
              )
            }
            break
          case Or10557Const.DEFAULT.DIALOG_RESULT_NO:
            // いいえ選択時は編集内容を破棄するので何もしない
            break
          case Or10557Const.DEFAULT.DIALOG_RESULT_CANCEL:
            // キャンセル選択時は複写データの作成を行わずに終了する
            local.mo00043.id = local.mo00043Change.id
            local.mo00043Transfer.id = local.mo00043Change.id
            localOneway.mo00024Oneway.mo01344Oneway.name = local.mo00043Transfer.id
            if (local.mo00043Transfer.id === Or10557Const.TAB.TAB_ID_DAILY_CARE_PLANE) {
              localOneway.mo00024Oneway.mo01344Oneway.toolbarTitle = t('label.daily-care-plan')
            } else {
              localOneway.mo00024Oneway.mo01344Oneway.toolbarTitle = t(
                'label.common-service-master'
              )
            }
          break
        }
      } else {
        local.mo00043.id = newValue
        localOneway.mo00024Oneway.mo01344Oneway.name = local.mo00043Transfer.id
        if (local.mo00043Transfer.id === Or10557Const.TAB.TAB_ID_DAILY_CARE_PLANE) {
          localOneway.mo00024Oneway.mo01344Oneway.toolbarTitle = t('label.daily-care-plan')
        } else {
          localOneway.mo00024Oneway.mo01344Oneway.toolbarTitle = t(
            'label.common-service-master'
          )
        }
      }
    }

  }
)

/**
 * 保存
 */
async function save() {
  // 画面入力データ変更があるかどうかを判定する
  // はい選択時は入力内容を保存する
  if (local.mo00043.id === Or10557Const.TAB.TAB_ID_DAILY_CARE_PLANE) {
    await or06618Ref.value?.insert()
  } else {
    const insertResult = await or28980Ref.value?.insert()
    if (insertResult?.isBreak) {
      if (insertResult.info) {
        await openInfoDialog(insertResult.msg,'normal1', 'blank', 'blank')
      } else if(insertResult.error) {
        await openErrorDialog(insertResult.msg)
      }
      return
    }
  }
  setState({ isOpen: false })
}

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
async function close() {
  if (isEdit.value) {
    const dialogResult = await openInfoDialog(t('message.i-cmn-10430'),'normal1', 'destroy1', 'normal3')
    switch (dialogResult) {
      case 'yes':
        await save()
        break
      case 'no':
        setState({ isOpen: false })
        break
      case 'cancel':
        setState({ isOpen: false })
        return
    }
  } else {
    setState({ isOpen: false })
  }
}
/**
 * ナビゲーション制御領域のいずれかの編集フラグがON
 */
const isEdit = computed(() => {
  return useScreenStore().isEditNavControl()
})

/**
 * errorダイアログを閉じたタイミングで結果を返却
 *
 * @param msg - msg
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
 async function openErrorDialog(msg: string): Promise<string> {
  // 選択行削除確認ダイアログを開く
  Or21813Logic.state.set({
    uniqueCpId: or21813_1.value.uniqueCpId,
    state: {
      isOpen: true,
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: msg,
      firstBtnType: 'blank',
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: 'blank',
    },
  })

  /**
   * 選択行削除確認ダイアログを閉じたタイミングで結果を返却
   *
   * @returns ダイアログの選択結果（yes, no）
   */
  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813_1.value.uniqueCpId)

        let result = Or28980Const.DEFAULT.DIALOG_RESULT_YES

        if (event?.firstBtnClickFlg) {
          result = Or28980Const.DEFAULT.DIALOG_RESULT_YES
        }

        // 選択行削除確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 編集破棄ダイアログ表示
 *
 * @param msg - msg
 *
 * @param firstBtn - firstBtn
 *
 * @param secondBtn - secondBtn
 *
 * @param thirdBtn - thirdBtn
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
 async function openInfoDialog(msg: string, firstBtn : Or21814FirstBtnType, secondBtn:Or21814SecondBtnType, thirdBtn : Or21814ThirdBtnType): Promise<string> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      isOpen: true,
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: msg,
      firstBtnType: firstBtn,
      firstBtnLabel: t('btn.yes'),
      secondBtnType: secondBtn,
      secondBtnLabel: t('btn.no'),
      thirdBtnType: thirdBtn,
      thirdBtnLabel: t('btn.cancel'),
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)

        let result = Or10557Const.DEFAULT.DIALOG_RESULT_CANCEL

        if (event?.firstBtnClickFlg) {
          result = Or10557Const.DEFAULT.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or10557Const.DEFAULT.DIALOG_RESULT_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = Or10557Const.DEFAULT.DIALOG_RESULT_CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
</script>

<template>
  <!-- ダイアログ -->
  <base-mo00024
    v-model="local.mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <base-mo00043
        v-model="local.mo00043Transfer"
        :oneway-model-value="localOneway.mo00043OneWay"
        style="margin: 0px !important; padding: 0px !important;  height: 36px !important;"
      ></base-mo00043>
      <c-v-window v-model="local.mo00043.id" style="margin: 0px !important; padding: 0px !important;" >
        <c-v-window-item value="or26846" style="margin: 0px !important; padding: 0px !important;"
          ><c-v-row
            ><c-v-col
              ><g-custom-or06618
                v-if="local.mo00043.id === 'or26846'"
                ref="or06618Ref"
                v-bind="or06618"
                :oneway-model-value="localOneway.or06618OneWay"
                :unique-cp-id="or06618.uniqueCpId"
              /> </c-v-col
          ></c-v-row>
        </c-v-window-item>
        <c-v-window-item value="or10577"  style="margin: 0px !important; padding: 0px !important;">
          <c-v-row>
            <c-v-col >
              <g-custom-or28980
                v-if="local.mo00043.id === 'or10577'"
                v-bind="or28980"
                ref="or28980Ref"
                :unique-cp-id="or28980.uniqueCpId"
                :parent-unique-cp-id="props.uniqueCpId"
              >
              </g-custom-or28980></c-v-col
          ></c-v-row>
        </c-v-window-item>
      </c-v-window>
      <c-v-row
        no-gutters
        class="label-comment"
      >
        <c-v-col style="padding: 8px 8px 8px 8px !important">
          <base-mo01338 :oneway-model-value="localOneway.mo01338Fourway" /> </c-v-col
      ></c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          class="mx-0 mr-2"
          @click="close"
        ></base-mo00611>
        <!-- 確定ボタン Mo00611 -->
        <base-mo00609
          v-bind="localOneway.mo00609SaveOneway"
          class="mx-0 mr-2"
          @click="save"
        >
        </base-mo00609>
      </c-v-row>
    </template>
    <!-- スロットの使用例 -->
    <g-base-or21814
      v-if="showDialogOr21814"
      v-bind="or21814_1"
    >
    </g-base-or21814>
    <!-- スロットの使用例 -->
    <g-base-or21813
      v-if="showDialogOr21813"
      v-bind="or21813_1"
    >
    </g-base-or21813>
  </base-mo00024>
</template>

<style scoped lang="scss">
.label-comment {
  color: rgb(var(--v-theme-black-536)) !important;
  margin: 0px 0px 0px 0px;
  height: 15px;
  position: fixed !important;
  bottom: 0;
  left: 0;
  right: 0;
  padding-bottom: 88px;
}
</style>
