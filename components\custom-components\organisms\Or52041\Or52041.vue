<script setup lang="ts">
/**
 * Or52041:有機体:［印刷設定］画面
 * GUI00623_［印刷設定］画面
 *
 * @description
 * ［印刷設定］画面
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { reactive, ref, onMounted, watch } from 'vue'
import type { Mo00045Type } from '../Or00386/Or00386.type'
import { Or52041Const } from './Or52041.constants'
import type { Or52041StateType, PrintHistory, OutputLedgerPrint } from './Or52041.type'
import { useSetupChildProps, useScreenOneWayBind, dateUtils } from '#imports'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01338OnewayType } from '@/types/business/components/Mo01338Type'
import type { Mo01344OnewayType } from '@/types/business/components/Mo01344Type'
import type { Mo00039Items, Mo00039OnewayType } from '@/types/business/components/Mo00039Type'
import type { Mo00020Type, Mo00020OnewayType } from '@/types/business/components/Mo00020Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { DIALOG_BTN } from '~/constants/classification-constants'
import type {
  Mo01334OnewayType,
  Mo01334Type,
  Mo01334Items,
} from '~/types/business/components/Mo01334Type'

import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { CustomClass } from '~/types/CustomClassType'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'

import type { Or52041Param } from '~/components/custom-components/organisms/Or52041/Or52041.type'

import { reportOutputType, useReportUtils } from '~/utils/useReportUtils'

import type {
  PrintSettingInitInfoSelectInEntity,
  PrintSettingInitInfoSelectOutEntity,
} from '~/repositories/cmn/entities/PrintSettingInitInfoSelectEntity'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import type {
  ReportDataType,
  SavePrintSettingInfoUpdateInEntity,
} from '~/repositories/cmn/entities/SavePrintSettingInfoUpdateEntity'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'

const { convertDateToSeireki } = dateUtils()
const { reportOutput } = useReportUtils()
const { t } = useI18n()
/************************************************
 * Props
 ************************************************/
interface Props {
  uniqueCpId: string
}

const props = defineProps<Props>()

// 子コンポーネント用変数
const or21813 = ref({ uniqueCpId: '' })

/**************************************************
 * 変数定義
 **************************************************/

const localOneway = reactive({
  /**
   * タイトル
   */
  mo01338OneWayTitle: {
    value: t('label.print-settings-title'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * タイトル表示
   */
  mo00045OneWay: {
    readonly: true,
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: 'background-color: #fafafa;',
    } as CustomClass,
  } as Mo00045OnewayType,
  /**
   * 日付印刷区分
   */
  mo00039OneWay: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  /**
   * 指定日
   */
  mo00020OneWay: {
    showItemLabel: false,
    showSelectArrow: false,
    mo00009OnewayBack: {},
    mo00009OnewayForward: {},
  } as Mo00020OnewayType,

  /**
   * 閉じるボタン
   */
  mo00611OneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.screen-close'),
  } as Mo00611OnewayType,
  /**
   * PDFダウンロードボタン
   */
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
    tooltipText: t('tooltip.pdf-download-btn'),
  } as Mo00609OnewayType,
})

/**
 * 親画面の情報
 */
const local = {
  /**
   * システムコード
   */
  sysCd: Or52041Const.DEFAULT.STR.EMPTY,
  /**
   * 期間管理フラグ
   */
  kikanFlg: Or52041Const.DEFAULT.STR.EMPTY,
  /**
   * システム略称
   */
  sysRyaku: Or52041Const.DEFAULT.STR.EMPTY,
  /**
   * セクション名
   */
  sectionName: Or52041Const.DEFAULT.STR.EMPTY,
  /**
   * 法人ID
   */
  houjinId: Or52041Const.DEFAULT.STR.EMPTY,
  /**
   * 施設ID
   */
  shisetuId: Or52041Const.DEFAULT.STR.EMPTY,
  /**
   * 事業者ID
   */
  svJigyoId: Or52041Const.DEFAULT.STR.EMPTY,
  /**
   * 職員ID
   */
  shokuId: Or52041Const.DEFAULT.STR.EMPTY,
  /**
   * 選択帳票番号
   */
  prtNo: Or52041Const.DEFAULT.STR.EMPTY,
  /**
   * 機能名
   */
  kinounameKnj: Or52041Const.DEFAULT.STR.EMPTY,
  /**
   *事業者名
   */
  svJigyoKnj: Or52041Const.DEFAULT.STR.EMPTY,
}

// 初期化排他
let firstFlg = false

// API初期化保存用
const localData: PrintSettingInitInfoSelectOutEntity = {
  data: {},
} as PrintSettingInitInfoSelectOutEntity

/**
 *ダイアログ
 */
const mo00024Oneway = ref<Mo00024OnewayType>({
  width: '730px',
  height: '506px',
  persistent: true,
  showCloseBtn: true,
  mo01344Oneway: {
    name: 'Or52041',
    toolbarTitle: t('label.print-set'),
    toolbarName: 'Or52041ToolBar',
    toolbarTitleCenteredFlg: false,
    showCardActions: true,
    cardTextClass: 'or52041_content',
  } as Mo01344OnewayType,
})

/**
 *ダイヤログ
 */
const mo00024 = ref<Mo00024Type>({
  isOpen: Or52041Const.DEFAULT.IS_OPEN,
})

/**
 * 出力帳票名一覧
 */
const mo01334Oneway = ref<Mo01334OnewayType>({
  headers: [
    {
      title: t('label.ledge-name-left-sidebar'),
      key: 'ledgerName',
      sortable: false,
      minWidth: '100',
    },
  ],
  items: [],
  height: 388,
  rowHeight: 50,
})

/**
 * 出力帳票名一覧
 */
const mo01334Type = ref<Mo01334Type>({
  value: Or52041Const.DEFAULT.STR.EMPTY,
  values: [],
} as Mo01334Type)
const mo00045OneWay = ref<Mo00045Type>({
  value: '',
} as Mo00045Type)
/**
 * 日付印刷区分
 */
const mo00039Type = ref<string>(Or52041Const.DEFAULT.STR.EMPTY)

/**
 * 指定日
 */
const mo00020Type = ref<Mo00020Type>({
  value: convertDateToSeireki(undefined),
} as Mo00020Type)

const currentsectionNo = ref(Or52041Const.DEFAULT.STR.EMPTY)

/**
 * 指定日非表示/表示フラゲ
 */
const mo00020Flag = ref<boolean>(false)

/**************************************************
 * Pinia
 **************************************************/

/**
 *State
 */
const { setState } = useScreenOneWayBind<Or52041StateType>({
  cpId: Or52041Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or52041Const.DEFAULT.IS_OPEN
    },
    param: (value) => {
      if (value) {
        local.sysCd = value.sysCd ?? Or52041Const.DEFAULT.STR.EMPTY
        local.sectionName = value.sectionName
        local.houjinId = value.houjinId
        local.shisetuId = value.shisetuId
        local.svJigyoId = value.svJigyoId
        local.shokuId = value.shokuId
        local.prtNo = value.prtNo
        local.kinounameKnj = value.kinounameKnj
        local.svJigyoKnj = value.svJigyoKnj
      }
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21813Const.CP_ID(0)]: or21813.value,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  // 汎用コード取得API実行
  await initCodes()

  // 初期情報取得
  await init()
})

/**
 * 汎用コード取得API実行
 */
const initCodes = async () => {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 日付印刷区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 日付印刷区分
  const bizukePrintCategoryCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )
  if (bizukePrintCategoryCodeTypes?.length > 0) {
    mo00039Type.value = bizukePrintCategoryCodeTypes[0].value
    const list: Mo00039Items[] = []
    for (const item of bizukePrintCategoryCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    localOneway.mo00039OneWay.items = list
  }
}

/**
 * 初期情報取得
 */
const init = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: PrintSettingInitInfoSelectInEntity = {
    sysRyaku: local.sysRyaku,
    sectionName: local.sectionName,
    houjinId: local.houjinId,
    shisetuId: local.shisetuId,
    svJigyoId: local.svJigyoId,
    shokuId: local.shokuId,
    prtNo: local.prtNo,
    kinounameKnj: local.kinounameKnj,
  } as PrintSettingInitInfoSelectInEntity
  const resp: PrintSettingInitInfoSelectOutEntity = await ScreenRepository.select(
    'printSettingInitInfoSelect',
    inputData
  )
  if (resp?.data) {
    localData.data = { ...resp?.data }
    const choPrtList: Mo01334Items[] = []
    if (resp?.data?.choPrtList) {
      for (const item of resp.data.choPrtList) {
        if (item) {
          choPrtList.push({
            id: item.sectionNo,
            mo01337OnewayLedgerName: {
              value: item.prtTitle,
              unit: Or52041Const.DEFAULT.STR.EMPTY,
            } as Mo01337OnewayType,
            prnDate: item.prnDate,
            selectable: true,
            choPro: item.choPro,
            prtNo: item.prtNo,
          } as Mo01334Items)
        }
      }
    }
    mo01334Oneway.value.items = choPrtList
    if (mo01334Oneway.value.items.length !== 0) {
      mo01334Type.value.value = mo01334Oneway.value.items[0].id
    }
  }
}

/**
 *
 * 画面印刷設定内容を保存
 *
 */
const save = async () => {
  // バックエンドAPIから印刷設定情報保存
  const inputData: SavePrintSettingInfoUpdateInEntity = {
    sysCd: local.sysCd,
    sysRyaku: local.sysCd,
    kinounameKnj: Or52041Const.DEFAULT.KINOU_NAME_KNJ,
    houjinId: local.houjinId,
    shisetuId: local.shisetuId,
    svJigyoId: local.svJigyoId,
    shokuId: local.shokuId,
    choPro: getProfileBySectionNo(mo01334Type.value.value),
    kojinhogoFlg: Or52041Const.DEFAULT.KOJINHOGO_USED_FLG,
    sectionAddNo: Or52041Const.DEFAULT.SECTION_ADD_NO,
    choPrtList: localData.data.choPrtList,
    iniDataList: localData.data.iniDataList,
    modifiedCnt: localData.data.modifiedCnt,
  } as SavePrintSettingInfoUpdateInEntity
  await ScreenRepository.update('savePrintSettingInfoUpdate', inputData)
}

/**
 * 「閉じるボタン」押下
 */
const close = async () => {
  if (getProfileBySectionNo(mo01334Type.value.value) === Or52041Const.DEFAULT.STR.EMPTY) {
    await openConfirmDialogErr()
  } else {
    await save()
    setState({
      isOpen: false,
      param: {} as Or52041Param,
    })
  }
}

/**
 * 「PDFダウンロード」ボタン押下
 */
const pdfDownload = async () => {
  // 入力チェックを行う
  if (!mo01334Oneway.value.items.length) {
    await openConfirmDialogErr()
    return
  }
  // 画面の帳票出力設定内容を保存する
  await save()
  const currentItem = localData.data.choPrtList?.find((item) => {
    return item.sectionNo === currentsectionNo.value
  })
  // 帳票reportId取得
  let reportId = Or52041Const.DEFAULT.STR.EMPTY
  switch (currentItem?.prtTitle) {
    case Or52041Const.DEFAULT.USERLIST:
      reportId = Or52041Const.DEFAULT.USERLISTREPORT
      break
    case Or52041Const.DEFAULT.USERNAME:
      reportId = Or52041Const.DEFAULT.USERNAMEREPORT
      break
    case Or52041Const.DEFAULT.SINTYOKUKANRICSTM:
      reportId = Or52041Const.DEFAULT.SINTYOKUKANRICSTMREPORT
      break
    case Or52041Const.DEFAULT.IKATUKEISAN:
      reportId = Or52041Const.DEFAULT.IKATUKEISANREPORT
      break
    case Or52041Const.DEFAULT.KEIKAKUFUKUSYAKEIKOKU:
      reportId = Or52041Const.DEFAULT.KEIKAKUFUKUSYAKEIKOKUREPORT
      break
  }

  const reportData: ReportDataType = {
    svJigyoKnj: local.svJigyoKnj,
    printConfigure: {
      shiTeiKubun: mo00039Type.value,
      shiTeiDate: mo00020Flag.value ? mo00020Type.value.value : Or52041Const.DEFAULT.STR.EMPTY,
    },
    printOption: {
      emptyFlg: Or52041Const.DEFAULT.STR.EMPTY,
      kinyuAssType: Or52041Const.DEFAULT.STR.EMPTY,
      colorFlg: Or52041Const.DEFAULT.STR.EMPTY,
    },
    printHistoryList: [
      {
        userId: Or52041Const.DEFAULT.STR.EMPTY,
        userName: Or52041Const.DEFAULT.STR.EMPTY,
        sc1Id: Or52041Const.DEFAULT.STR.EMPTY,
        startYmd: Or52041Const.DEFAULT.STR.EMPTY,
        endYmd: Or52041Const.DEFAULT.STR.EMPTY,
        raiId: Or52041Const.DEFAULT.STR.EMPTY,
        assType: Or52041Const.DEFAULT.STR.EMPTY,
        assDateYmd: Or52041Const.DEFAULT.STR.EMPTY,
        assShokuId: Or52041Const.DEFAULT.STR.EMPTY,
        result: Or52041Const.DEFAULT.STR.EMPTY,
        outputLedgerPrintList: [
          {
            printSubject: currentItem,
          },
        ] as OutputLedgerPrint[],
      },
    ] as PrintHistory[],
  }
  await reportOutput(reportId, reportData, reportOutputType.DOWNLOAD)
}

/**
 * Errダイアログをオープンする
 *
 */
const openConfirmDialogErr = () => {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      isOpen: true,
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.e-cmn-40172'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      // 第2ボタンボタンタイプ
      secondBtnType: 'blank',
      // 第3ボタンタイプ
      thirdBtnType: 'blank',
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813.value.uniqueCpId)

        let result = DIALOG_BTN.CANCEL

        if (event?.firstBtnClickFlg) {
          result = DIALOG_BTN.YES
        }

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 切替後の印刷設定を画面に設定する
 *
 * @param selectId - 出力帳票ID
 */
const setAfterChangePrintData = (selectId: string) => {
  if (selectId) {
    for (const item of localData.data.choPrtList ?? []) {
      if (selectId === item.sectionNo) {
        // 日付表示有無
        mo00039Type.value = item.prnDate ?? Or52041Const.DEFAULT.STR.EMPTY
        break
      }
    }
  }
}

/**
 * 「出力帳票名」選択
 *
 * @param selectId - 出力帳票ID
 */
const outputLedgerName = (selectId: string) => {
  let label = Or52041Const.DEFAULT.STR.EMPTY
  for (const item of mo01334Oneway.value.items) {
    if (item) {
      if (selectId === item.id && item.mo01337OnewayLedgerName) {
        const data = item.mo01337OnewayLedgerName as Mo01337OnewayType
        label = data.value
        mo01334Type.value.value = item.id
        break
      }
    }
  }
  mo00045OneWay.value.value = label
}

/**
 * current profile 取得
 *
 *@param sectionNo -current sectionNo
 */
const getProfileBySectionNo = (sectionNo: string) => {
  const found = localData.data.choPrtList?.find((item) => item.sectionNo === sectionNo)
  return found ? found.choPro : null
}

/**
 * 「出力帳票名」選択
 */
watch(
  () => mo01334Type.value.value,
  (newValue) => {
    currentsectionNo.value = newValue

    setAfterChangePrintData(newValue)

    outputLedgerName(newValue)

    // 日付印刷区分が2の場合
    if (Or52041Const.DEFAULT.STR.TWO === mo00039Type.value) {
      // 指定日を活性表示にする。
      mo00020Flag.value = true
    } else {
      // 指定日を非表示にする。
      mo00020Flag.value = false
    }
  }
)

/**
 * 「日付印刷区分」ラジオボタン押下
 */
watch(
  () => mo00039Type.value,
  (newValue) => {
    if (firstFlg) {
      if (!mo01334Oneway.value.items.length) return
      for (const item of localData.data.choPrtList ?? []) {
        if (mo01334Type.value.value === item.sectionNo) {
          // 日付表示有無
          item.prnDate = mo00039Type.value
          break
        }
      }
      if (Or52041Const.DEFAULT.STR.TWO === newValue) {
        // 指定日を活性表示にする
        mo00020Flag.value = true
      } else {
        // 指定日を非表示にする
        mo00020Flag.value = false
      }
    }
    firstFlg = true
  }
)
/**
 * mo00024ダイヤログclose
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    if (!newValue) {
      if (getProfileBySectionNo(mo01334Type.value.value) === Or52041Const.DEFAULT.STR.EMPTY) {
        mo00024.value.isOpen = true
        await openConfirmDialogErr()
      } else {
        void close()
      }
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        class="or52041_row pa-2"
        no-gutter
      >
        <c-v-col
          cols="12"
          sm="4"
          class="or52041_table table-h table-header"
        >
          <base-mo-01334
            v-model="mo01334Type"
            :oneway-model-value="mo01334Oneway"
            class="list-wrapper mo01334_class"
          >
            <!-- 帳票 -->
            <template #[`item.ledgerName`]="{ item }">
              <!-- 分子：一覧専用ラベル（文字列型） -->
              <base-mo01337 :oneway-model-value="item.mo01337OnewayLedgerName" />
            </template>
            <!-- ページングを非表示 -->
            <template #bottom />
          </base-mo-01334>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="8"
          class="content_center"
        >
          <c-v-row
            no-gutter
            class="customCol or52041_row"
          >
            <c-v-col
              cols="12"
              sm="2"
              class="label_left d-flex align-center pl-2 py-2"
            >
              <base-mo01338 :oneway-model-value="localOneway.mo01338OneWayTitle"></base-mo01338>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="10"
              class="label_right px-2 py-2"
            >
              <base-mo00045
                v-model="mo00045OneWay"
                :oneway-model-value="localOneway.mo00045OneWay"
              ></base-mo00045>
            </c-v-col>
          </c-v-row>
          <c-v-divider></c-v-divider>
          <c-v-row
            no-gutter
            class="customCol or52041_row"
          >
            <c-v-col
              cols="12"
              sm="5"
              class="px-0 py-2"
            >
              <base-mo00039
                v-model="mo00039Type"
                :oneway-model-value="localOneway.mo00039OneWay"
              >
              </base-mo00039>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="5"
              class="pl-0 pr-2 py-2"
            >
              <base-mo00020
                v-if="mo00020Flag"
                v-model="mo00020Type"
                :oneway-model-value="localOneway.mo00020OneWay"
              >
              </base-mo00020>
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611OneWay"
          @click="close"
        >
        </base-mo00611>
        <!-- PDFダウンロードボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          class="ml-2"
          @click="pdfDownload()"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <g-base-or21813 v-bind="or21813" />
</template>
<style>
.or52041_content {
  padding: 0px !important;
}
.mo01334_class {
  text-align: center;
  background-color: rgb(250, 250, 250);
}

</style>
<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';

:deep(.v-table__wrapper) {
  overflow-x: auto !important;
  border: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}
.table-h {
  height: 388px;
}
.or52041_table {
  padding: 0px !important;
}

.or52041_row {
  margin: 0px !important;
}

.content_center {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  border-top: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  border-bottom: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  padding: 0px !important;

  .label_left {
    padding-right: 0px;
  }

  .label_right {
    padding-left: 0px;
    padding-right: 0px;
  }

  :deep(.label-area-style) {
    display: none;
  }

  .customCol {
    margin-left: 0px;
    margin-right: 0px;
  }
}
:deep(thead th) {
  display: flex;
  justify-content: left;
  align-items: center;
  height: 38px !important;
  border-right: none !important;
  border-top: none !important;
  border-left: none !important;
}
:deep(tbody td) {
  display: flex;
  justify-content: left;
  align-items: center;
  text-align: left;
}
</style>
