import type { InWebEntity } from '~/types/common/api/InWebEntity'
import type { OutWebEntity } from '~/types/common/api/OutWebEntity'

/**
 * ログイン入力エンティティ
 */
export interface ILoginInEntity extends InWebEntity {
  /** ユーザーID */
  userId: string
  /** パスワード */
  password: string
  /** DB番号 */
  dbNumber: string
  /** ログインタイプ */
  loginType: string
  /** トークン種別 */
  tokenType: string
}

/**
 * ログイン出力エンティティ
 */
export type ILoginOutEntity = OutWebEntity<ILoginData>

/**
 * ログイン出力エンティティ
 */
export interface ILoginData {
  /** 認証トークン */
  authToken: string
  /** 職員ID */
  chkShokuId: string
  /** 職員名 */
  shokuinKnj: string
  /** DB番号 */
  dbNumber: string
}
