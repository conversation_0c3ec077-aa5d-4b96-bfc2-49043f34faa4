/**
 * Or10600:構造
 * GUI03248_月間・年間表パターン画面(タイトル)
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR>
 */
export interface Or10600StateType {
  /**
   * 開閉フラグ
   */
  isOpen?: boolean
}

/**
 * saveResult
 */
export interface saveResult {
  /**
   * error
   */
  error: boolean
  /**
   * info
   */
  info: boolean
  /**
   * msg
   */
  msg: string
  /**
   * isBreak
   */
  isBreak: boolean
}
/**
 * Async Function
 */
type AsyncFunction = () => Promise<saveResult>
