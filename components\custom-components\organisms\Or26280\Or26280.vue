<script setup lang="ts">
/**
 * Or26280:有機体:頻度登録画面モーダル
 * GUI01052_頻度登録画面
 *
 * @description
 * 頻度登録画面
 *
 * <AUTHOR>
 */
import { onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

import type { Or26280StateType } from './Or26280.type'

import {
  useScreenOneWayBind,
  useScreenTwoWayBind,
  useScreenUtils,
  useScreenStore,
  useSetupChildProps,
  computed,
} from '#imports'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type {
  AlternateWeekSpecificationType,
  MonthDaySpecificationType,
  Or26280OnewayType,
  Or26280Type,
  DayOfWeek,
} from '~/types/cmn/business/components/Or26280Type'
import { Or26280Const } from '~/components/custom-components/organisms/Or26280/Or26280.constants'
import type { Mo00020OnewayType } from '~/types/business/components/Mo00020Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { CodeGroup, CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type { Mo01282OnewayType } from '~/types/business/components/Mo01282Type'
import type { Mo00046Type } from '~/types/business/components/Mo00046Type'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
const { t } = useI18n()

/************************************************
 * Props
 ************************************************/
interface Props {
  modelValue: Or26280Type
  onewayModelValue: Or26280OnewayType
  uniqueCpId: string
  parentCpId: string
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
// 選択した行のindex
const selectedMonthDaySpecificationListItemIndex = ref<number>(-1)
const selectedAlternateWeekSpecificationItemIndex = ref<number>(-1)

const localOneway = reactive({
  mo01338Key1Oneway: {
    value: t('label.frequency-preview'),
  } as Mo01338OnewayType,
  mo01338Oneway: {
    value: t('label.toggle-day'),
  } as Mo01338OnewayType,
  mo01338key2OnewayType: {
    value: t('label.warning1'),
    valueFontWeight: 'weight',
  } as Mo01338OnewayType,
  mo01338key1OnewayType: {
    itemLabel: t('label.lbl1'),
    valueFontWeight: 'weight',
  } as Mo01338OnewayType,
  mo00039Oneway: {
    hideDetails: true,
    showItemLabel: false,
  },
  mo00046oneway: {
    maxLength: 100,
    isVerticalLabel: false,
    showItemLabel: false,
  } as Mo00046Type,
  or26280Oneway: {
    ...props.onewayModelValue,
  } as Or26280OnewayType,
  // 行追加
  mo00611OneWay: {
    btnLabel: t('btn.add-row'),
    prependIcon: 'add',
  } as Mo00611OnewayType,
  // 行複写
  mo00611OneWayCopy: {
    btnLabel: t('btn.duplicate-row'),
    prependIcon: 'file_copy',
    disabled: true,
  } as Mo00611OnewayType,
  // 日付input
  mo00020OneWay: {
    hideDetails: true,
    maxlength: '8',
    width: '140px',
    isVerticalLabel: false,
    showItemLabel: false,
  } as Mo00020OnewayType,
  // 行削除 年月指定
  mo01265OneWay: {
    btnLabel: t('btn.delete-row'),
    prependIcon: 'delete',
    disabled: true,
  } as Mo01265OnewayType,
  // 行削除 隔週指定
  mo01265OneWayAlternateWeekSpecification: {
    btnLabel: t('btn.delete-row'),
    prependIcon: 'delete',
    disabled: true,
  } as Mo01265OnewayType,
  // 「頻度登録」ダイアログ
  mo00024Oneway: {
    width: '550px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      toolbarTitle: t('label.frequencyGamen'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
      scrollable: false,
    },
  },
  mo00018Oneway: {
    name: 'or24338',
    itemLabel: '',
    hideDetails: true,
    checkboxLabel: t('label.lbl2'),
    showItemLabel: false,
  },
  mo00609Oneway: {
    btnLabel: t('btn.confirm'),
    width: '90px',
  },
  mo00611Oneway: {
    btnLabel: t('btn.close'),
    width: '90px',
  },
  mo01282Oneway: {
    items: [],
    itemTitle: 'name',
    itemValue: 'Kbn',
  } as Mo01282OnewayType,
  // // 閉じる確認ダイアログ
  // orX0020BeforeCloseDialogOneWay: {
  //   message: t('message.i-cmn-10430'),
  //   mo00024Oneway: {
  //     persistent: true,
  //   } as Mo00024OnewayType,
  // } as OrX0020OnewayType,
})

// ポスト最小幅
const columnMinWidth = ref<number[]>([160, 70, 110])

const defaultModelValue = {
  monthDaySelections: [],
  alternateWeekSpecifications: [],
  selectedAreaValue: '1',
  rangeSelectionFlag: { modelValue: false },
  frequencyPreview: { value: '' } as Mo00046Type,
  specificDateSelections: [] as number[],
  previewData: '',
  weeklySelectionRows: Array.from({ length: 5 }, () => ({
    selectedDays: [],
  })),
} as Or26280Type

// ローカル双方向bind
const local = reactive({
  or26280: {
    ...defaultModelValue,
    ...props.modelValue,
  } as Or26280Type,
  // 汎用コード取得
  commonCode: {
    selectCodeKbnList: [] as CodeType[],
    weeklyIntervalList: [] as CodeType[],
  },
})
//画面を開く時の初期値
const initValue = {
  selectedAreaValue: local.or26280.selectedAreaValue,
  rangeSelectionFlag: local.or26280.rangeSelectionFlag,
  frequencyPreview: local.or26280.frequencyPreview,
  previewData: local.or26280.previewData,
  monthDaySelections: Array.from(local.or26280.monthDaySelections || []),
  alternateWeekSpecifications: Array.from(local.or26280.alternateWeekSpecifications || []),
  specificDateSelections: Array.from(local.or26280.specificDateSelections || []),
  weeklySelectionRows: Array.from(local.or26280.weeklySelectionRows || []),
  frequencyPreviewValue: local.or26280.frequencyPreview.value,
  rangeSelectionFlagModelValue: local.or26280.rangeSelectionFlag.modelValue,
}
const initSelectedDays1 = Array.from(local.or26280.weeklySelectionRows[0].selectedDays)
const initSelectedDays2 = Array.from(local.or26280.weeklySelectionRows[1].selectedDays)
const initSelectedDays3 = Array.from(local.or26280.weeklySelectionRows[2].selectedDays)
const initSelectedDays4 = Array.from(local.or26280.weeklySelectionRows[3].selectedDays)
const initSelectedDays5 = Array.from(local.or26280.weeklySelectionRows[4].selectedDays)

const daysOfWeek: DayOfWeek[] = ['日', '月', '火', '水', '木', '金', '土']

// ダイアログ設置
const mo00024 = ref<Mo00024Type>({
  isOpen: Or26280Const.DEFAULT.IS_OPEN,
})

// ナビゲーション制御領域のいずれかの編集フラグがON
useSystemCommonsStore().setShowEditDiscardDialog(useScreenStore().isEditNavControl())

// initValueとthisValueが一致するかのチェック
function isDeepEqual(initValue: Or26280Type, thisValue: Or26280Type): boolean {
  //radio選択
  if (initValue.selectedAreaValue !== thisValue.selectedAreaValue) return false
  //範囲選択
  if (initValue.rangeSelectionFlagModelValue !== thisValue.rangeSelectionFlag.modelValue)
    return false
  //previewの値
  if (initValue.previewData !== thisValue.previewData) return false

  return true
}

/**************************************************
 * Pinia
 **************************************************/
const or21813_1 = ref({ uniqueCpId: '' })
const or21814_1 = ref({ uniqueCpId: '' })
const or21815_1 = ref({ uniqueCpId: '' })

const { refValue } = useScreenTwoWayBind<Or26280Type>({
  cpId: Or26280Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

const { setState } = useScreenOneWayBind<Or26280StateType>({
  cpId: Or26280Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or26280Const.DEFAULT.IS_OPEN
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21813Const.CP_ID(1)]: or21813_1.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or21815Const.CP_ID(1)]: or21815_1.value,
})

const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813_1.value.uniqueCpId)?.isOpen ?? false
})
const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen ?? false
})
const showDialogOr21815 = computed(() => {
  // Or21815のダイアログ開閉状態
  return Or21815Logic.state.get(or21815_1.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])
watch(
  () => local.or26280,
  () => {
    refValue.value = local.or26280
  },
  { deep: true }
)

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  // 汎用コード取得
  await initCodes()

  //親画面.年月日指定可能フラグが「年月日指定不可」の 且つ、親画面.週単位以外のサービス区分が「年月日指定」の場合
  if (
    localOneway.or26280Oneway.showSelectDate === false &&
    localOneway.or26280Oneway.serviceType === Or26280Const.DEFAULT.NUM_1
  ) {
    //親画面.週単位以外のサービス区分が「日付指定」を設定する
    localOneway.or26280Oneway.nonWeeklyServiceCategory = t('label.toggle-day-appoint')
  }

  //親画面.年月日指定可能フラグが「年月日指定可能」の場合  且つ、親画面.週単位以外のサービス区分が「年月日指定」の場合  且つ、サービス種類が（※１）に含む場合
  if (
    localOneway.or26280Oneway.showSelectDate === true &&
    [
      '21',
      '22',
      '23',
      '24',
      '25',
      '26',
      '2A',
      '2B',
      '38',
      '39',
      '27',
      '28',
      '68',
      '69',
      '79',
    ].includes(localOneway.or26280Oneway.serviceType)
  ) {
    localOneway.or26280Oneway.serviceType = t('label.toggle-day-appoint')
  } else {
    //ラジオボタン「年月日指定」、非表示にする
    local.commonCode.selectCodeKbnList.splice(0, 1)
  }
  //親画面.頻度選択時に隔週指定フラグが「隔週指定可能」、ラジオボタン「隔週指定」、表示する
  if (localOneway.or26280Oneway.allowAlternateWeekSelection !== true) {
    local.commonCode.selectCodeKbnList = local.commonCode.selectCodeKbnList.filter(
      (item) => item.value !== Or26280Const.DEFAULT.NUM_4
    )
  }

  Or21815Logic.state.set({
    uniqueCpId: or21815_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.warning'),
      // ダイアログテキスト
      dialogText: t('message.w-cmn-20793'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
    },
  })
})

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SELECTION },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_WEEKLYINTERVALSELECT },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  // コード取得
  local.commonCode.selectCodeKbnList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_SELECTION
  )

  local.commonCode.weeklyIntervalList =
    CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_WEEKLYINTERVALSELECT) ??
    ({} as CodeGroup)

  localOneway.mo01282Oneway.items =
    CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_WEEKLYINTERVALSELECT) ??
    ({} as CodeGroup)
}

// 日付の選択状態を切り替える
const toggleDay = (day: number) => {
  //範囲選択
  if (local.or26280.rangeSelectionFlag.modelValue) {
    if (local.or26280.selectedAreaValue === Or26280Const.DEFAULT.NUM_1) {
      if (local.or26280.specificDateSelections.length >= 2) {
        local.or26280.specificDateSelections = []
        local.or26280.specificDateSelections.push(day)
      } else if (local.or26280.specificDateSelections.length === 1) {
        const existDay: number = local.or26280.specificDateSelections[0]
        if (existDay < day) {
          for (let i = existDay + 1; i <= day; i++) {
            if (!local.or26280.specificDateSelections.includes(i)) {
              local.or26280.specificDateSelections.push(i)
            }
          }
        }
        if (existDay > day)
          for (let i = day; i <= existDay; i++) {
            if (!local.or26280.specificDateSelections.includes(i)) {
              local.or26280.specificDateSelections.push(i)
            }
          }
      } else {
        //選択0件の場合
        // 未選択の場合、配列に追加
        local.or26280.specificDateSelections.push(day)
      }
    }
  } else {
    const index = local.or26280.specificDateSelections.indexOf(day)

    if (index === -1) {
      // 未選択の場合、配列に追加
      local.or26280.specificDateSelections.push(day)
    } else {
      // 選択済みの場合、配列から削除
      local.or26280.specificDateSelections.splice(index, 1)
    }
  }

  // 配列をソートして順序を保持
  local.or26280.specificDateSelections.sort((a, b) => a - b)

  if (local.or26280.specificDateSelections.length === 0) {
    local.or26280.frequencyPreview.value = ''
  }
  if (local.or26280.specificDateSelections.length === 31) {
    local.or26280.frequencyPreview.value = t('label.every-day')
  } else {
    const ranges = findConsecutiveRanges(local.or26280.specificDateSelections)

    const parts = ranges.map((range) => {
      if (range.end - range.start >= 2) {
        // 連続数字は3個以上
        return `${range.start}${t('label.day-short-sunday')}${t('label.wavy-withoutblank')}${range.end}${t('label.day-short-sunday')}`
      } else if (range.start === range.end) {
        return `${range.start}${t('label.day-short-sunday')}`
      } else {
        return `${range.start}${t('label.day-short-sunday')}${t('label.connect')}${range.end}${t('label.day-short-sunday')}`
      }
    })
    local.or26280.frequencyPreview.value = parts.join(t('label.connect'))
  }
}

// radioの選択状態を切り替える
const clickRadio = (selectedValue: string) => {
  if (selectedValue !== Or26280Const.DEFAULT.NUM_1) {
    local.or26280.specificDateSelections = []
  }

  if (selectedValue !== Or26280Const.DEFAULT.NUM_2) {
    //1~5行の曜日選択をクリアする
    local.or26280.weeklySelectionRows.forEach((row) => {
      row.selectedDays = []
    })
  }

  local.or26280.frequencyPreview.value = ''
  if (selectedValue === Or26280Const.DEFAULT.NUM_3) {
    const result = calculateMonthDayPreview(local.or26280.monthDaySelections)
    local.or26280.frequencyPreview.value = result
  } else if (selectedValue === Or26280Const.DEFAULT.NUM_4) {
    const result = calculateAlternateWeekPreview(local.or26280.alternateWeekSpecifications)
    local.or26280.frequencyPreview.value = result
  }
}

/**
 * 毎日ボタンクリック
 */
const clickEveryDay = () => {
  if (local.or26280.specificDateSelections.length === 31) {
    local.or26280.specificDateSelections = []
  } else {
    for (let day = 1; day <= 31; day++) {
      if (!local.or26280.specificDateSelections.includes(day)) {
        local.or26280.specificDateSelections.push(day)
      }
    }
  }
  local.or26280.frequencyPreview.value = t('label.every-day')
}

/**
 * 確定チェック
 */
function doConfirmCheck() {
  let noErrFlg = true
  //画面.指定ラジオボが「年月日指定」の場合
  if (local.or26280.selectedAreaValue === Or26280Const.DEFAULT.NUM_3) {
    for (const item of local.or26280.monthDaySelections) {
      if (item.dateFrom.value === '') {
        showOr21813Msg(t('message.e-cmn-40163'))
        //「開始日を設定してください。」
        console.log(t('message.e-cmn-40163')) //'開始日を設定してください。'
        noErrFlg = false
        break
      }
      if (item.dateTo.value === '') {
        //「終了日を設定してください。」
        showOr21813Msg(t('message.e-cmn-40164'))
        console.log(t('message.e-cmn-40164')) //'終了日を設定してください。'
        noErrFlg = false
        break
      }
      if (item.dateFrom.value > item.dateTo.value) {
        //「開始日と終了日に誤りがあります。」
        showOr21813Msg(t('message.e-cmn-40165'))
        console.log(t('message.e-cmn-40165')) //'開始日と終了日に誤りがあります。'
        noErrFlg = false
        break
      }
      if (item.dateFrom.value === item.dateTo.value) {
        //「重複している日付があります。」
        showOr21813Msg(t('message.e-cmn-40166'))
        console.log(t('message.e-cmn-40166')) //'重複している日付があります。'
        noErrFlg = false
        break
      }
    }
  } else if (local.or26280.selectedAreaValue === Or26280Const.DEFAULT.NUM_4) {
    //画面.指定ラジオボが隔週指定の場合
    if (local.or26280.alternateWeekSpecifications.length === 0) {
      //「隔週基準年月日を設定してください。」
      showOr21813Msg(t('message.e-cmn-40167'))
      console.log(t('message.e-cmn-40167'))
      noErrFlg = false
    } else {
      for (let i = 1; i < local.or26280.alternateWeekSpecifications.length; i++) {
        if (!noErrFlg) {
          break
        }
        if (local.or26280.alternateWeekSpecifications[i].date.value === '') {
          showOr21813Msg(t('message.e-cmn-40167'))
          //「隔週基準年月日を設定してください。」
          console.log(t('message.e-cmn-40167'))
          noErrFlg = false
          break
        }
        if (
          local.or26280.alternateWeekSpecifications[i].alternateWeekSpecificationValue
            .modelValue === ''
        ) {
          //「隔週基準間隔を設定してください。」
          showOr21813Msg(t('message.e-cmn-40168'))
          console.log(t('message.e-cmn-40168'))
          noErrFlg = false
          break
        }
      }

      const weekdays = local.or26280.alternateWeekSpecifications.map((item) => item.weekday)
      const uniqueWeekdays = new Set(weekdays)
      if (uniqueWeekdays.size !== weekdays.length) {
        showOr21813Msg(t('message.e-cmn-40169'))
        //「重複している曜日があります。」
        console.log(t('message.e-cmn-40169'))
        noErrFlg = false
      }
    }
  }
  return noErrFlg
}

/**
 * 確認ボタンクリック
 */
const onConfirmBtn = () => {
  const noErrFlg = doConfirmCheck()
  if (noErrFlg) {
    doConfirm()
  }
}

// 配列中の連続した数値の範囲を見つける
const findConsecutiveRanges = (numbers: number[]): { start: number; end: number }[] => {
  if (numbers.length === 0) return []

  const sortedUnique = [...new Set(numbers)].sort((a, b) => a - b)

  const ranges: { start: number; end: number }[] = []
  let start = sortedUnique[0]
  let prev = sortedUnique[0]

  for (let i = 1; i < sortedUnique.length; i++) {
    if (sortedUnique[i] === prev + 1) {
      prev = sortedUnique[i]
    } else {
      ranges.push({ start, end: prev })
      start = sortedUnique[i]
      prev = sortedUnique[i]
    }
  }
  ranges.push({ start, end: prev })

  return ranges
}

// 曜日を正しい順序でソートする関数
const sortDays = (days: DayOfWeek[]): DayOfWeek[] => {
  const order = [
    t('label.day-short-sunday'),
    t('label.day-short-monday'),
    t('label.day-short-tuesday'),
    t('label.day-short-wednesday'),
    t('label.day-short-thursday'),
    t('label.day-short-friday'),
    t('label.day-short-saturday'),
  ]
  return [...days].sort((a, b) => order.indexOf(a) - order.indexOf(b))
}

const toggleWeekly = (thisRowIndex: number, day: DayOfWeek) => {
  //範囲選択の場合
  if (local.or26280.rangeSelectionFlag.modelValue) {
    //選択された曜日のcount
    let selectWeeklyCount = 0
    //選択された曜日行の行index
    const selectWeeklyIndexes: number[] = []
    local.or26280.weeklySelectionRows.forEach((row, rowIndex) => {
      if (row.selectedDays.length > 0) {
        selectWeeklyCount = selectWeeklyCount + row.selectedDays.length
        selectWeeklyIndexes.push(rowIndex)
      }
    })

    if (selectWeeklyCount >= 2) {
      //1~5行の曜日選択をクリアする
      local.or26280.weeklySelectionRows.forEach((row) => {
        row.selectedDays = []
      })

      const selectedDays = local.or26280.weeklySelectionRows[thisRowIndex].selectedDays
      const index = selectedDays.indexOf(day)
      if (index === -1) {
        selectedDays.push(day)
      } else {
        selectedDays.splice(index, 1)
      }
    } else if (selectWeeklyCount === 1) {
      //すでに選択されたのrowIndex
      const existWeeklyRowNumber = selectWeeklyIndexes[0]
      //すでに選択されたのrowInfoの曜日Index
      const existWeeklyItemIndex = daysOfWeek.indexOf(
        local.or26280.weeklySelectionRows[existWeeklyRowNumber].selectedDays[0]
      )
      if (existWeeklyRowNumber < thisRowIndex) {
        for (let i = existWeeklyRowNumber; i < thisRowIndex; i++) {
          const selectedDays = local.or26280.weeklySelectionRows[i].selectedDays
          const existWeeklyInfoWithOneDailyStr = selectedDays[0]

          //選択された行のindex
          const existWeeklyInfoWithOneDailyNum = daysOfWeek.indexOf(existWeeklyInfoWithOneDailyStr)
          for (let i = existWeeklyInfoWithOneDailyNum; i < 7 - 1; i++) {
            selectedDays.push(daysOfWeek[i + 1])
          }
        }
        //選択された行
        const selectedDays = local.or26280.weeklySelectionRows[thisRowIndex].selectedDays
        const selectedWeeklyInfoWithOneDailyNum = daysOfWeek.indexOf(day)
        for (let i = 0; i <= selectedWeeklyInfoWithOneDailyNum; i++) {
          selectedDays.push(daysOfWeek[i])
        }
      } else if (existWeeklyRowNumber > thisRowIndex) {
        //選択された行
        const selectedDays = local.or26280.weeklySelectionRows[thisRowIndex].selectedDays
        for (let i = daysOfWeek.indexOf(day); i < 7; i++) {
          selectedDays.push(daysOfWeek[i])
        }

        for (let i = thisRowIndex + 1; i < existWeeklyRowNumber; i++) {
          const selectedDays = local.or26280.weeklySelectionRows[i].selectedDays
          for (let i = 0; i < 7; i++) {
            selectedDays.push(daysOfWeek[i])
          }
        }

        //最後行
        const lastRowSelectedDays =
          local.or26280.weeklySelectionRows[existWeeklyRowNumber].selectedDays
        for (let i = 0; i < existWeeklyItemIndex; i++) {
          lastRowSelectedDays.push(daysOfWeek[i])
        }
      }
    } else if (selectWeeklyCount === 0) {
      const selectedDays = local.or26280.weeklySelectionRows[thisRowIndex].selectedDays
      const index = selectedDays.indexOf(day)
      if (index === -1) {
        selectedDays.push(day)
      } else {
        selectedDays.splice(index, 1)
      }
    }
  } else {
    const selectedDays = local.or26280.weeklySelectionRows[thisRowIndex].selectedDays
    const index = selectedDays.indexOf(day)
    if (index === -1) {
      selectedDays.push(day)
    } else {
      selectedDays.splice(index, 1)
    }
  }

  //範囲選択と範囲選択以外の共通処理
  const result: string[] = []
  local.or26280.weeklySelectionRows.forEach((row, rowIndex) => {
    if (row.selectedDays.length > 0) {
      const sortedDays = sortDays(row.selectedDays)
      const dayStrings = sortedDays.map(
        (day) => `${t('label.day-short-unit')}${rowIndex + 1}${day}${t('label.day-short-week')}`
      )
      result.push(...dayStrings)
    }
  })

  local.or26280.frequencyPreview.value = result.join(t('label.connect'))
}

/**
 * 閉じるボタン押下時
 */
const onClickCloseBtn = async () => {
  // 画面入力データ変更があるかどうかを判定する
  // 変更がない場合、画面を閉じる。
  // if (!isEdit.value) {
  if (isDeepEqual(initValue, local.or26280)) {
    setState({ isOpen: false })
  } else {
    // 変更がある場合、確認ダイアログを表示する。
    const dialogResult = await showOr21814Msg()
    switch (dialogResult) {
      case Or26280Const.DEFAULT.DIALOG_RESULT_YES:
        doConfirm()
        break
      case Or26280Const.DEFAULT.DIALOG_RESULT_NO:
        // いいえ：画面を閉じる。
        setState({ isOpen: false })
        // 保存しないため、local.or26280の値を前回の初期値で保存した上で、返却する
        local.or26280.selectedAreaValue = initValue.selectedAreaValue
        local.or26280.rangeSelectionFlag.modelValue = initValue.rangeSelectionFlagModelValue
        local.or26280.frequencyPreview.value = initValue.frequencyPreviewValue
        local.or26280.previewData = initValue.previewData
        local.or26280.monthDaySelections = initValue.monthDaySelections
        local.or26280.alternateWeekSpecifications = initValue.alternateWeekSpecifications
        local.or26280.specificDateSelections = initValue.specificDateSelections
        local.or26280.weeklySelectionRows[0].selectedDays = initSelectedDays1
        local.or26280.weeklySelectionRows[1].selectedDays = initSelectedDays2
        local.or26280.weeklySelectionRows[2].selectedDays = initSelectedDays3
        local.or26280.weeklySelectionRows[3].selectedDays = initSelectedDays4
        local.or26280.weeklySelectionRows[4].selectedDays = initSelectedDays5
        emit('update:modelValue', local.or26280)
        break
      case Or26280Const.DEFAULT.DIALOG_RESULT_CANCEL:
        // キャンセル：処理終了。
        break
    }
  }
}

/**
 * 年月日指定行選択
 *
 * @param index - 選択した行のindex
 */
const selectMonthDaySpecificationRow = (index: number) => {
  selectedMonthDaySpecificationListItemIndex.value = index
  // // 行削除活性
  localOneway.mo01265OneWay.disabled = false
}

/**
 * 年月日指定行追加ボタン押下
 */
const addMonthDaySpecificationListRow = () => {
  local.or26280.monthDaySelections.push({
    dateFrom: {
      value: '',
      mo01343: undefined,
    },
    dateTo: {
      value: '',
      mo01343: undefined,
    },
  })
  if (local.or26280.monthDaySelections.length !== 0) {
    localOneway.mo01265OneWay.disabled = false
  }
}

/**
 * 年月日指定行削除ボタン押下
 */
const deleteMonthDaySpecificationListRow = async () => {
  if (selectedMonthDaySpecificationListItemIndex.value !== -1) {
    const dialogResult = await showOr21815Msg()
    switch (dialogResult) {
      case Or26280Const.DEFAULT.DIALOG_RESULT_YES:
        // はい：処理継続
        break
      case Or26280Const.DEFAULT.DIALOG_RESULT_NO:
        // いいえ：以降の処理を行わない。
        return
    }
    local.or26280.monthDaySelections.splice(selectedMonthDaySpecificationListItemIndex.value, 1)

    if (local.or26280.monthDaySelections.length === 0) {
      localOneway.mo01265OneWay.disabled = true
    }
  }
}

/**
 * 隔週指定行選択
 *
 * @param index - 選択した行のindex
 */
const selectAlternateWeekSpecificationListRow = (index: number) => {
  selectedAlternateWeekSpecificationItemIndex.value = index
  // 行削除活性
  localOneway.mo01265OneWayAlternateWeekSpecification.disabled = false
  // 行複写活性
  localOneway.mo00611OneWayCopy.disabled = false
}

/**
 * 隔週指定行追加ボタン押下
 */
const addAlternateWeekSpecificationList = () => {
  //隔週基準年月日
  local.or26280.alternateWeekSpecifications.push({
    date: {
      value: '',
    },
    weekday: '',
    alternateWeekSpecificationValue: {
      modelValue: '1',
    },
  })
}
/**
 * 行複写
 */
const copyAlternateWeekSpecification = () => {
  if (selectedAlternateWeekSpecificationItemIndex.value !== -1) {
    local.or26280.alternateWeekSpecifications.push(
      JSON.parse(
        JSON.stringify(
          local.or26280.alternateWeekSpecifications[
            selectedAlternateWeekSpecificationItemIndex.value
          ]
        )
      ) as (typeof local.or26280.alternateWeekSpecifications)[0]
    )
  }
}

/**
 * 隔週指定行削除ボタン押下
 */
const deleteAlternateWeekSpecificationListRow = async () => {
  if (selectedAlternateWeekSpecificationItemIndex.value !== -1) {
    const dialogResult = await showOr21815Msg()
    switch (dialogResult) {
      case Or26280Const.DEFAULT.DIALOG_RESULT_YES:
        // はい：処理継続
        break
      case Or26280Const.DEFAULT.DIALOG_RESULT_NO:
        // いいえ：以降の処理を行わない。
        return
    }

    local.or26280.alternateWeekSpecifications.splice(
      selectedAlternateWeekSpecificationItemIndex.value,
      1
    )

    if (local.or26280.alternateWeekSpecifications.length === 0) {
      localOneway.mo01265OneWayAlternateWeekSpecification.disabled = true
    }
  }
}
/**
 * 年月指定プレビューを計算する
 *
 * @param specificationList - 年月指定明細データ
 *
 * @returns - プレビュー文字列
 */
const calculateMonthDayPreview = (specificationList: MonthDaySpecificationType[]): string => {
  const result: string[] = []
  specificationList.forEach((specification) => {
    const { dateFrom, dateTo } = specification
    const fromValue = dateFrom.value
    const toValue = dateTo.value

    if (fromValue && toValue) {
      result.push(`${fromValue}~${toValue}`)
    }
  })
  return result.join('、')
}

/**
 * 隔週指定プレビューを計算する
 *
 * @param specificationList - 隔週指定明細データ
 *
 * @returns - プレビュー文字列
 */
const calculateAlternateWeekPreview = (
  specificationList: AlternateWeekSpecificationType[]
): string => {
  const result: string[] = []
  specificationList.forEach((specification) => {
    const date = specification.date.value
    const weekday = getDayOfWeek(date)
    specification.weekday = weekday
    const alternateWeekSpecificationValue = specification.alternateWeekSpecificationValue.modelValue

    let alternateWeekSpecificationName = ''
    if (localOneway.mo01282Oneway.items) {
      const foundItem = localOneway.mo01282Oneway.items.find(
        (item): item is CodeType & { Kbn: number; name: string } =>
          'Kbn' in item && 'name' in item && item.Kbn === Number(alternateWeekSpecificationValue)
      )
      if (foundItem) {
        alternateWeekSpecificationName = foundItem.name
      }
    }

    result.push(`${date}${t('label.wavy-withoutblank')}${alternateWeekSpecificationName}${weekday}`)
  })

  return result.join(t('label.connect'))
}

/**
 * 指定された日付文字列から曜日を取得する
 *
 * @param dateString - 日付を表す文字列（例: "YYYY-MM-DD"形式）
 *
 * @returns 曜日の文字列表現（例: "(月)"、"(火)"など）。無効な日付の場合、空文字を返す。
 */
function getDayOfWeek(dateString: string): string {
  const date = new Date(dateString)
  const daysOfWeek = [
    t('label.left-bracket') + t('label.day-short-sunday') + t('label.right-bracket'),
    t('label.left-bracket') + t('label.day-short-monday') + t('label.right-bracket'),
    t('label.left-bracket') + t('label.day-short-tuesday') + t('label.right-bracket'),
    t('label.left-bracket') + t('label.day-short-wednesday') + t('label.right-bracket'),
    t('label.left-bracket') + t('label.day-short-thursday') + t('label.right-bracket'),
    t('label.left-bracket') + t('label.day-short-friday') + t('label.right-bracket'),
    t('label.left-bracket') + t('label.day-short-saturday') + t('label.right-bracket'),
  ]

  if (isNaN(date.getTime())) {
    return ''
  }

  const dayIndex = date.getDay()
  return daysOfWeek[dayIndex]
}

/**
 * 確定ボタン処理
 *
 */
function doConfirm() {
  const { setChildCpBinds } = useScreenUtils()

  setChildCpBinds(props.parentCpId, {
    Or26280: {
      twoWayValue: local.or26280,
    },
  })

  emit('update:modelValue', local.or26280)

  setState({ isOpen: false })
}

/**
 * 入力エラーを表示する
 *
 * @param errormsg - エラー内容
 */
function showOr21813Msg(errormsg: string) {
  Or21813Logic.state.set({
    uniqueCpId: or21813_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: errormsg,
      firstBtnType: 'blank',
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })
  // 確認ダイアログをオープン
  Or21813Logic.state.set({
    uniqueCpId: or21813_1.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 情報ダイアログ表示
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
async function showOr21814Msg(): Promise<string> {
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })

  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)

        let result = Or26280Const.DEFAULT.DIALOG_RESULT_CANCEL

        if (event?.firstBtnClickFlg) {
          result = Or26280Const.DEFAULT.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or26280Const.DEFAULT.DIALOG_RESULT_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = Or26280Const.DEFAULT.DIALOG_RESULT_CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 警告を表示する
 *
 * @returns ダイアログの選択結果（yes, no）
 */
async function showOr21815Msg() {
  // 確認ダイアログをオープン
  Or21815Logic.state.set({
    uniqueCpId: or21815_1.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
  // 警告ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815_1.value.uniqueCpId)

        let result = Or26280Const.DEFAULT.DIALOG_RESULT_CANCEL

        if (event?.firstBtnClickFlg) {
          result = Or26280Const.DEFAULT.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or26280Const.DEFAULT.DIALOG_RESULT_NO
        }

        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: or21815_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

// 年月指定明細データを監視する
watch(
  () => local.or26280.monthDaySelections,
  (newValue) => {
    const result = calculateMonthDayPreview(newValue)
    local.or26280.frequencyPreview.value = result
  },
  { deep: true }
)

// 隔週指定明細データを監視する
watch(
  () => local.or26280.alternateWeekSpecifications,
  (newValue) => {
    const result = calculateAlternateWeekPreview(newValue)
    local.or26280.frequencyPreview.value = result
  },
  { deep: true }
)

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    if (!newValue) {
      await onClickCloseBtn()
    }
  }
)

watch(
  () => local.or26280.frequencyPreview.value,
  (newValue) => {
    if (newValue) {
      local.or26280.previewData = newValue
    }
  }
)

watch(
  () => refValue.value,
  () => {},
  { deep: true }
)

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    mo00024.value.isOpen = true
    if (!newValue) {
      close()
    }
  }
)

const dayShortUnit = ref<string>(t('label.day-short-unit'))
const wavyWithoutblank = ref<string>(t('label.wavy-withoutblank'))
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet>
        <!--日付選択radio-->
        <c-v-row
          no-gutters
          style="padding-bottom: 0px; border-bottom: 1px solid rgb(224, 224, 224)"
        >
          <c-v-col>
            <base-mo00039
              v-model="local.or26280.selectedAreaValue"
              :oneway-model-value="localOneway.mo00039Oneway"
            >
              <base-at-radio
                v-for="(item, index) in local.commonCode.selectCodeKbnList"
                :key="index"
                :name="'radio-' + index"
                :radio-label="item.label"
                :value="item.value"
                @click="clickRadio(item.value)"
              />
            </base-mo00039>
          </c-v-col>
        </c-v-row>
        <!--範囲で選択-->
        <c-v-row
          v-if="
            local.or26280.selectedAreaValue === Or26280Const.DEFAULT.NUM_1 ||
            local.or26280.selectedAreaValue === Or26280Const.DEFAULT.NUM_2
          "
          no-gutters
          style="padding-top: 6px; border-bottom: 1px solid rgb(224, 224, 224)"
        >
          <c-v-col cols="auto">
            <base-mo00018
              v-model="local.or26280.rangeSelectionFlag"
              :oneway-model-value="localOneway.mo00018Oneway"
            />
          </c-v-col>
        </c-v-row>
        <!--日付指定セクションstart-->
        <c-v-row
          v-if="local.or26280.selectedAreaValue === Or26280Const.DEFAULT.NUM_1"
          style="margin-top: 8px; margin-bottom: 0px"
        >
          <c-v-data-table
            class="table-wapper mt-2 container"
            style="padding-left: 8px; padding-right: 8px"
            hide-default-footer
            :items-per-page="-1"
          >
            <thead>
              <tr>
                <th
                  class="text-left"
                  colspan="11"
                  style="padding-left: 4px; cursor: default"
                >
                  {{ t('label.toggle-day') }}
                </th>
              </tr>
            </thead>
            <tr>
              <td
                v-for="day in 10"
                :key="day"
                style="line-height: 50px; padding-left: 5px"
                :style="
                  day === 1
                    ? 'line-height: 50px; padding-left: 5px;border-left: 1px rgb(var(--v-theme-black-200)) solid !important'
                    : 'line-height: 50px; padding-left: 5px;'
                "
              >
                <base-mo00611
                  :oneway-model-value="{ btnLabel: day, minWidth: '41px' }"
                  :class="{ buttonActive: local.or26280.specificDateSelections.includes(day) }"
                  @click="toggleDay(day)"
                />
              </td>
            </tr>
            <tr>
              <td
                v-for="day in [11, 12, 13, 14, 15, 16, 17, 18, 19, 20]"
                :key="day"
                :style="
                  day === 11
                    ? 'line-height: 50px; padding-left: 5px;border-left: 1px rgb(var(--v-theme-black-200)) solid !important'
                    : 'line-height: 50px; padding-left: 5px;'
                "
              >
                <base-mo00611
                  :oneway-model-value="{ btnLabel: day, minWidth: '41px' }"
                  :class="{ buttonActive: local.or26280.specificDateSelections.includes(day) }"
                  @click="toggleDay(day)"
                />
              </td>
            </tr>
            <tr>
              <td
                v-for="day in [21, 22, 23, 24, 25, 26, 27, 28, 29, 30]"
                :key="day"
                :style="
                  day === 21
                    ? 'line-height: 50px; padding-left: 5px;border-left: 1px rgb(var(--v-theme-black-200)) solid !important'
                    : 'line-height: 50px; padding-left: 5px;'
                "
              >
                <base-mo00611
                  :oneway-model-value="{ btnLabel: day, minWidth: '41px' }"
                  :class="{ buttonActive: local.or26280.specificDateSelections.includes(day) }"
                  @click="toggleDay(day)"
                />
              </td>
            </tr>
            <tr>
              <td
                style="
                  line-height: 50px;
                  padding-left: 5px;
                  border-left: 1px rgb(var(--v-theme-black-200)) solid !important;
                "
              >
                <base-mo00611
                  v-for="day in [31]"
                  :key="day"
                  :oneway-model-value="{ btnLabel: day, minWidth: '41px' }"
                  :class="{ buttonActive: local.or26280.specificDateSelections.includes(day) }"
                  @click="toggleDay(day)"
                />
              </td>
            </tr>
          </c-v-data-table>
        </c-v-row>
        <c-v-row
          v-if="local.or26280.selectedAreaValue === Or26280Const.DEFAULT.NUM_1"
          style="height: 46px; margin-top: -30px"
        >
          <c-v-col cols="6">
            <base-mo01338 :oneway-model-value="localOneway.mo01338key1OnewayType"
          /></c-v-col>
          <c-v-col
            cols="6"
            style="text-align: right; padding-right: 8px !important"
          >
            <base-mo00611
              :v-bind="{ ...$attrs }"
              :oneway-model-value="{ btnLabel: t('btn.every-day') }"
              @click="clickEveryDay"
            />
          </c-v-col>
        </c-v-row>
        <!--日付指定セクションend-->

        <!--曜日指定セクションstart-->
        <c-v-row
          v-if="local.or26280.selectedAreaValue === Or26280Const.DEFAULT.NUM_2"
          style="margin-top: 8px"
        >
          <c-v-col>
            <c-v-data-table
              class="table-wapper mt-2 container2"
              style="padding-left: 8px; padding-right: 8px"
              hide-default-footer
              :items-per-page="-1"
            >
              <thead>
                <tr>
                  <th class="text-left"></th>
                  <th
                    class="text-left"
                    colspan="7"
                    style="border-left: 0px solid !important; padding-left: 4px; cursor: default"
                  >
                    {{ t('label.jpy-day') }}
                  </th>
                </tr>
              </thead>
              <tr
                v-for="index in [0, 1, 2, 3, 4]"
                :key="index"
                style="line-height: 50px"
              >
                <td
                  style="
                    text-align: center;
                    width: 30%;
                    border-left: 1px rgb(var(--v-theme-black-200)) solid !important;
                  "
                >
                  {{ dayShortUnit }}{{ index + 1 }}
                </td>
                <td
                  v-for="weekly in daysOfWeek"
                  :key="weekly"
                  style="padding-left: 5px"
                >
                  <base-mo00611
                    :v-bind="{ ...$attrs }"
                    :oneway-model-value="{ btnLabel: weekly, minWidth: '41px' }"
                    :class="{
                      buttonRed: weekly === t('label.day-short-sunday'),
                      buttonBlue: weekly === t('label.day-short-saturday'),
                      buttonActive:
                        local.or26280.weeklySelectionRows[index].selectedDays.includes(weekly),
                    }"
                    @click="toggleWeekly(index, weekly)"
                  >
                  </base-mo00611>
                </td>
              </tr>
            </c-v-data-table>
          </c-v-col>
        </c-v-row>
        <c-v-row v-if="local.or26280.selectedAreaValue === Or26280Const.DEFAULT.NUM_2">
          <c-v-col>
            <base-mo01338
              :oneway-model-value="localOneway.mo01338key2OnewayType"
              style="margin-left: -2px"
            />
          </c-v-col>
        </c-v-row>
        <c-v-row
          v-if="local.or26280.selectedAreaValue === Or26280Const.DEFAULT.NUM_3"
          style="padding-top: 20px; padding-left: 8px; padding-right: 8px"
        >
          <c-v-col>
            <c-v-row style="padding-left: 8px; padding-right: 8px; padding-bottom: 0px">
              <!--行追加-->
              <base-mo00611
                class="mr-1"
                :oneway-model-value="localOneway.mo00611OneWay"
                @click="addMonthDaySpecificationListRow"
              />
              <!--行削除-->
              <base-mo01265
                class="ml-1"
                :oneway-model-value="localOneway.mo01265OneWay"
                @click="deleteMonthDaySpecificationListRow"
              />
            </c-v-row>
            <c-v-row>
              <c-v-col cols="8">
                <c-v-data-table
                  fixed-header
                  :items="local.or26280.monthDaySelections"
                  class="table-wapper2 mt-2 container3"
                  hide-default-footer
                  :items-per-page="-1"
                  :headers="[]"
                  style="
                    padding-top: 8px;
                    padding-bottom: 8px;
                    padding-left: 8px;
                    padding-right: 8px;
                  "
                >
                  <!-- 一覧 -->
                  <template #item="{ index }">
                    <tr
                      :class="{
                        'select-row': selectedMonthDaySpecificationListItemIndex === index,
                      }"
                      @click="selectMonthDaySpecificationRow(index)"
                    >
                      <td
                        :style="
                          index + 1 === local.or26280.monthDaySelections.length
                            ? 'border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;'
                            : ''
                        "
                      >
                        <span style="display: flex; align-items: center">
                          <base-mo00020
                            v-model="local.or26280.monthDaySelections[index].dateFrom"
                            :oneway-model-value="localOneway.mo00020OneWay"
                            v-bind="{ ...$attrs }"
                          />
                          {{ wavyWithoutblank }}
                          <base-mo00020
                            v-model="local.or26280.monthDaySelections[index].dateTo"
                            :oneway-model-value="localOneway.mo00020OneWay"
                            v-bind="{ ...$attrs }"
                          />
                        </span>
                      </td>
                    </tr>
                  </template>
                </c-v-data-table>
              </c-v-col>
              <c-v-col> </c-v-col>
            </c-v-row>
          </c-v-col>
        </c-v-row>
        <c-v-row
          v-if="local.or26280.selectedAreaValue === Or26280Const.DEFAULT.NUM_4"
          style="padding-top: 20px; padding-left: 8px; padding-right: 8px"
        >
          <c-v-col>
            <c-v-row style="padding-left: 8px">
              <!--行追加-->
              <base-mo00611
                class="mr-1"
                :oneway-model-value="localOneway.mo00611OneWay"
                @click="addAlternateWeekSpecificationList"
              />
              <!--行複写-->
              <base-mo00611
                class="mr-1"
                :oneway-model-value="localOneway.mo00611OneWayCopy"
                @click="copyAlternateWeekSpecification"
              />
              <!--行削除-->
              <base-mo01265
                class="ml-1"
                :oneway-model-value="localOneway.mo01265OneWayAlternateWeekSpecification"
                @click="deleteAlternateWeekSpecificationListRow"
              />
            </c-v-row>
            <c-v-row>
              <c-v-col cols="auto">
                <c-v-data-table
                  v-resizable-grid="{ columnWidths: columnMinWidth }"
                  class="table-wapper mt-2 container4"
                  fixed-header
                  hide-default-footer
                  :items-per-page="-1"
                  style="
                    padding-top: 8px;
                    padding-bottom: 8px;
                    padding-left: 8px;
                    padding-right: 8px;
                  "
                >
                  <thead>
                    <tr>
                      <th style="cursor: default; width: 160px">
                        <span style="color: orange">*</span>{{ t('label.kijun-day') }}
                      </th>
                      <th style="cursor: default; width: 70px">
                        {{ t('label.jpy-day') }}
                      </th>
                      <th style="cursor: default; width: 110px">
                        <span style="color: orange">*</span>{{ t('label.next-time') }}
                      </th>
                    </tr>
                  </thead>
                  <!-- 一覧 -->
                  <tbody>
                    <tr
                      v-for="(item, index) in local.or26280.alternateWeekSpecifications"
                      :key="index"
                      :class="{
                        'select-row': selectedAlternateWeekSpecificationItemIndex === index,
                      }"
                      @click="selectAlternateWeekSpecificationListRow(index)"
                    >
                      <td
                        style="
                          text-align: center;
                          border-left: 1px rgb(var(--v-theme-black-200)) solid !important;
                          width: 160px;
                        "
                      >
                        <span style="display: flex; align-items: center">
                          <base-mo00020
                            v-model="local.or26280.alternateWeekSpecifications[index].date"
                            :oneway-model-value="localOneway.mo00020OneWay"
                            v-bind="{ ...$attrs }"
                          />
                        </span>
                      </td>
                      <td style="text-align: center; width: 70px">{{ item.weekday }}</td>
                      <td style="width: 110px">
                        <base-mo01282
                          v-model="
                            local.or26280.alternateWeekSpecifications[index]
                              .alternateWeekSpecificationValue
                          "
                          :oneway-model-value="localOneway.mo01282Oneway"
                          class="cell-select"
                        ></base-mo01282>
                      </td>
                    </tr>
                  </tbody>
                </c-v-data-table>
              </c-v-col>
              <!-- <c-v-col> </c-v-col> -->
            </c-v-row>
          </c-v-col>
        </c-v-row>
        <!--頻度プレビュー-->
        <c-v-row>
          <c-v-data-table
            class="table-wapper2 mt-2"
            style="padding-left: 8px; padding-right: 8px; padding-bottom: 8px"
            hide-default-footer
            :items-per-page="-1"
          >
            <thead>
              <tr>
                <th
                  class="text-left"
                  style="padding-left: 6px"
                >
                  {{ localOneway.mo01338Key1Oneway.value }}
                </th>
              </tr>
            </thead>
            <tr>
              <td style="border: none !important">
                <base-mo00046
                  v-model="local.or26280.frequencyPreview"
                  :oneway-model-value="localOneway.mo00046oneway"
                />
              </td>
            </tr>
          </c-v-data-table>
        </c-v-row>
      </c-v-sheet>
      <!-- 確認ダイアログ
      <g-custom-or-x-0020
        v-model="orX0020BeforeCloseDialog"
        :oneway-model-value="localOneway.orX0020BeforeCloseDialogOneWay"
      ></g-custom-or-x-0020>-->
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611Oneway"
          @click="onClickCloseBtn"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.screen-close')"
          />
        </base-mo00611>
        <!-- 確定ボタン-->
        <base-mo00609
          class="mx-2"
          :oneway-model-value="localOneway.mo00609Oneway"
          @click="onConfirmBtn"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.confirm')"
          />
        </base-mo00609>
      </c-v-row>
      <g-base-or21813
        v-if="showDialogOr21813"
        v-bind="or21813_1"
      ></g-base-or21813>
      <g-base-or21814
        v-if="showDialogOr21814"
        v-bind="or21814_1"
      ></g-base-or21814>
      <g-base-or21815
        v-if="showDialogOr21815"
        v-bind="or21815_1"
      ></g-base-or21815>
    </template>
  </base-mo00024>
</template>

<style lang="scss" scoped>
@use '@/styles/cmn/dialog-data-table.scss';
.table-wapper :deep(.v-table__wrapper th) {
  background-color: rgb(var(--v-theme-black-100)) !important;
  border-top: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-bottom: none !important;
  font-size: 14px;
  font-weight: bold;
}
.table-wapper :deep(.v-table__wrapper) {
  th:first-child {
    border-left: 1px rgb(var(--v-theme-black-200)) solid !important;
    border-right: 1px rgb(var(--v-theme-black-200)) solid !important;
  }
  th:nth-child(2) {
    border-left: none !important;
    border-right: 1px rgb(var(--v-theme-black-200)) solid !important;
  }
  th:nth-child(3) {
    border-left: none !important;
    border-right: 1px rgb(var(--v-theme-black-200)) solid !important;
  }
}

.table-wapper .v-table__wrapper td {
  border-top: none !important;
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-left: none !important;
  border-right: 1px rgb(var(--v-theme-black-200)) solid !important;
  font-size: 14px;
  border-color: lightgrey;
  padding: 0;
}

.table-wapper2 :deep(.v-table__wrapper th) {
  background-color: rgb(var(--v-theme-black-100)) !important;
  border-top: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-bottom: none !important;
  border-left: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-right: 1px rgb(var(--v-theme-black-200)) solid !important;
  font-size: 14px;
  font-weight: bold;
}
.table-wapper2 .v-table__wrapper td {
  border-top: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-bottom: none !important;
  border-left: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-right: 1px rgb(var(--v-theme-black-200)) solid !important;
  font-size: 14px;
  border-color: lightgrey;
  padding: 0;
}

.table-wapper2 :deep(.v-col) {
  padding: 0 !important;
}

/** 行選択の様式 */
.select-row {
  background: rgb(var(--v-theme-blue-100));
}

tr {
  cursor: pointer;
}

.v-col {
  padding-left: 0px !important;
  padding-right: 0px !important;
  padding-top: 2px !important;
  padding-bottom: 2px !important;
}

.buttonRed {
  background: #ffe8dd;
  :deep(.v-btn__content > span) {
    color: red !important;
  }
}

.buttonBlue {
  background: #efecfb;
  :deep(.v-btn__content > span) {
    color: blue !important;
  }
}

.buttonActive {
  background: #dbeefe;
  color: white;
  border-color: black;
  :deep(.v-btn__content > span) {
    color: rgb(var(--v-theme-black-500)) !important;
  }
}

.v-row {
  margin: -8px;
}

.container {
  height: 262px;
  overflow-y: auto;
}

.container2 {
  height: 300px;
  overflow-y: auto;
}

.container3 {
  height: 330px;
  overflow-y: auto;
}

.container4 {
  height: 319px;
  overflow-y: auto;
}

.bottomline {
  border-bottom: 1px solid rgb(224, 224, 224);
  width: 100%;
  height: 50px;
}
:deep(.v-sheet) {
  background-color: transparent !important;
}
</style>
