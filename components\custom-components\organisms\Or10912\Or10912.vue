<script setup lang="ts">
import { reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or10912Const } from './Or10912.constants'
import type {
  Or10912OneWayType,
  Or10912SelectType,
  Or10912StateType,
  Or10912Type,
} from './Or10912.type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo01344OnewayType } from '~/types/business/components/Mo01344Type'
import { useScreenOneWayBind } from '#imports'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { CustomClass } from '~/types/CustomClassType'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo00040OnewayType, Mo00040Type } from '~/types/business/components/Mo00040Type'
import type {
  StyleSelectSelectInEntity,
  StyleSelectSelectOutEntity,
} from '~/repositories/cmn/entities/StyleSelectSelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'

/**
 * Or10912:様式選択ダイアログ
 * Gui01229_様式選択
 *
 * @description
 * 様式選択ダイアログ
 *
 * <AUTHOR>
 */

/************************************************
 * Props
 ************************************************/

interface Props {
  uniqueCpId: string
  onewayModelValue: Or10912OneWayType
  modelValue: Or10912Type
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

// ダイアログ
const mo00024Oneway = ref<Mo00024OnewayType>({
  width: '357px',
  persistent: true,
  showCloseBtn: true,
  mo01344Oneway: {
    name: 'Or10912',
    toolbarTitle: t('label.style-select'),
    toolbarName: 'Or10912ToolBar',
    toolbarTitleCenteredFlg: false,
    showCardActions: true,
    cardTextClass: 'pa-2',
  } as Mo01344OnewayType,
})

// 注記ラベル
const mo01338NotesOneway = ref({
  value: t('label.style-select-note'),
  valueFontWeight: '400',
  customClass: {
    itemStyle: 'color:gray',
  } as CustomClass,
} as Mo01338OnewayType)

/** 様式名ラベル */
const mo00615Oneway: Mo00615OnewayType = {
  itemLabel: t('label.style-name'),
  showItemLabel: true,
  showRequiredLabel: false,
  customClass: {
    outerClass: 'mr-2',
  } as CustomClass,
}
/** 様式名プルダウン */
const mo00040Oneway = ref<Mo00040OnewayType>({
  itemLabel: '',
  showItemLabel: false,
  isRequired: false,
  items: [],
  width: '200px',
})

const mo00024 = ref<Mo00024Type>({
  isOpen: Or10912Const.DEFAULT.IS_OPEN,
})

const local = reactive({
  or10912: {
    ...props.modelValue,
  },
  youshikiList: [] as Or10912SelectType[],
})
const localOneway = reactive({
  or10912: {
    ...props.onewayModelValue,
  },
  mo00040: {
    modelValue: '',
  } as Mo00040Type,
})

const defaultOneway = reactive({
  // 閉じるコンポーネント
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 確定コンポーネント
  mo00609ConfirmOneway: {
    btnLabel: t('btn.confirm'),
  } as Mo00609OnewayType,
})

/**************************************************
 * ライフサイクルフック
 **************************************************/

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or10912StateType>({
  cpId: Or10912Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or10912Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * コンポーネント固有処理
 **************************************************/

/**
 * AC009 「確定ボタン」押下
 */
const certain = () => {
  if (localOneway.mo00040.modelValue === undefined) {
    localOneway.mo00040.modelValue = ''
  }
  local.or10912.youshikiId = localOneway.mo00040.modelValue
  emit('update:modelValue', local.or10912.youshikiId)
  close()
}

/**
 * 画面最新情報を取得する
 */
const getData = async () => {
  // 様式選択情報取得(IN)
  const inputData: StyleSelectSelectInEntity = {
    youshikiId: local.or10912.youshikiId,
    ...localOneway.or10912,
  }
  // 様式選択初期情報取得
  const res: StyleSelectSelectOutEntity = await ScreenRepository.select(
    'styleSelectSelect',
    inputData
  )
  if (res.statusCode === 'success') {
    local.youshikiList = []
    res.data.youshikiList.forEach((item) => {
      local.youshikiList.push({
        title: item.youshikiKnj,
        value: item.youshikiId,
      })
    })
    mo00040Oneway.value.items = local.youshikiList
    localOneway.mo00040.modelValue = res.data.youshikiId
  }
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    mo00024.value.isOpen = true
    // 組織dialog自動クローズを手動判定に変更
    if (!newValue) {
      void close()
    } else {
      await getData()
    }
  }
)

/**
 * 画面が閉じます
 */
/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
const close = () => {
  // 画面を閉じる。
  setState({ isOpen: false })
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        no-gutters
        class="mb-2"
      >
        <!-- 注記ラベル -->
        <base-mo01338 :oneway-model-value="mo01338NotesOneway" />
      </c-v-row>

      <c-v-row
        no-gutters
      >
        <!-- 様式名ラベル -->
        <base-mo00615 :oneway-model-value="mo00615Oneway" />
        <!-- 様式名プルダウン -->
        <base-mo00040
          v-model="localOneway.mo00040"
          :oneway-model-value="mo00040Oneway"
        >
        </base-mo00040>
      </c-v-row>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="defaultOneway.mo00611CloseBtnOneWay"
          @click="close"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <!-- 確定ボタン Mo00611 -->
        <base-mo00609
          v-bind="defaultOneway.mo00609ConfirmOneway"
          class="ml-2"
          @click="certain()"
        >
          <!--ツールチップ表示："確定します"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.confirm-btn')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
</template>

<style scoped lang="scss"></style>
