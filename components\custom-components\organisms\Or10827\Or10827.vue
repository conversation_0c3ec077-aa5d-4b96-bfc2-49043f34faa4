<script setup lang="ts">
/**
 * Or10827:有機体:日課表マスタパターン設定モダール
 * GUI00987_日課表マスタパターン設定
 *
 * @description
 * 日課表マスタパターン設定
 *
 * <AUTHOR>
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import type { AsyncFunction, Or10827StateType } from '../Or10827/Or10827.type'
import { Or00734Const } from '../Or00734/Or00734.constants'
import { Or00725Const } from '../Or00725/Or00725.constants'
import { Or10827Const } from './Or10827.constants'
import { useScreenOneWayBind, useScreenStore, useSetupChildProps } from '#build/imports'
import type { Or10827OnewayType } from '~/types/cmn/business/components/Or10827Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Or00734OnewayType } from '~/types/cmn/business/components/Or00734Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  onewayModelValue: Or10827OnewayType
}
const props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/
// ローカルTwoway
const local = reactive({
  // 本画面ダイアログ
  mo00024: {
    isOpen: Or10827Const.DEFAULT.IS_OPEN,
  } as Mo00024Type,

  mo00043: { id: Or10827Const.TAB.TAB_ID_DAILYSCHEDULE } as Mo00043Type,
  mo00043Transfer: { id: Or10827Const.TAB.TAB_ID_DAILYSCHEDULE } as Mo00043Type,
})

// ローカルOneway
const localOneway = reactive({
  or00734OneWay: {
    svJigyoId: props.onewayModelValue.svJigyoId,
    shisetuId: props.onewayModelValue.shisetuId,
    svJigyoIdList: props.onewayModelValue.svJigyoIdList,
  } as Or00734OnewayType,
  or10827OneWay: {
    ...props.onewayModelValue,
  },
  // 本画面ダイアログOneway
  mo00024Oneway: {
    width: '900px',
    height: '650px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: Or10827Const.CP_ID(0),
      toolbarTitle: t('label.daily-table-master'),
      toolbarTitleCenteredFlg: false,
      toolbarName: '',
      showCardActions: true,
      cardTextClass: 'card-text pa-0',
    },
  },
  // 閉じるコンポーネント
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    width: '70px',
    minWidth: '70px',
  } as Mo00611OnewayType,
  // 確定ボタン
  mo00609ConfirmOneway: {
    btnLabel: t('btn.confirm'),
    width: '70px',
    minWidth: '70px',
  } as Mo00609OnewayType,
  // タブ
  mo00043OneWay: {
    tabItems: [
      { id: Or10827Const.TAB.TAB_ID_DAILYSCHEDULE, title: t('label.daily-table') },
      { id: Or10827Const.TAB.TAB_ID_CONTENT, title: t('label.content') },
      {
        id: Or10827Const.TAB.TAB_ID_DAILYLIFEANDCARESERVICE,
        title: t('label.daily-life-and-nursing-care-services-examples'),
      },
    ],
  } as Mo00043OnewayType,
  mo01338Threeway: {
    value: t('label.save-by-bussiness-unit'),
    customClass: new CustomClass({ itemClass: 'copyright-text' , itemStyle: 'color:  rgb(var(--v-theme-black-500));',}),
  } as Mo01338OnewayType,
})
const tabChange = ref<number>(0)
const or21814 = ref({ uniqueCpId: '' })
const or00734 = ref({ uniqueCpId: Or00734Const.CP_ID(1) })
const or00725 = ref({ uniqueCpId: Or00725Const.CP_ID(0) })
const or00734Ref = ref<{ save: AsyncFunction; clearEditFlg: () => void }>()
const or00725Ref = ref<{ save: AsyncFunction }>()

/**************************************************
 * コンポーネント固有処理
 **************************************************/

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or00734Const.CP_ID(1)]: or00734.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
    [Or00725Const.CP_ID(0)]: or00725.value,
})

onMounted(() => {
  // データ変更確認ダイアログを初期化(i.cmn.10430)
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })
})
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or10827StateType>({
  cpId: Or10827Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      local.mo00024.isOpen = value ?? Or10827Const.DEFAULT.IS_OPEN
    },
  },
})
/**************************************************
 * 関数
 **************************************************/
// 画面開閉フラグ
watch(
  () => local.mo00024.isOpen,
  async (newValue) => {
    if (!newValue) {
      local.mo00024.isOpen = true
      await close()
    }
  }
)
// メニュー切替
watch(
  () => local.mo00043Transfer.id,
  async (newValue) => {
    tabChange.value = 1
    if (local.mo00043.id === newValue) {
      return
    }
    // 画面入力データ変更があるかどうかを判定する
    if (isEdit.value) {
      const dialogResult = await openInfoDialog()
      switch (dialogResult) {
        case 'yes':
          await insert()
          break
        case 'no':
          or00734Ref.value?.clearEditFlg()
          break
        case 'cancel':
          // キャンセル選択時は終了する
          local.mo00043Transfer.id = local.mo00043.id
          return
      }
      local.mo00043.id = newValue
      local.mo00043Transfer.id = newValue
    } else {
      local.mo00043.id = newValue
    }
  }
)

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
async function close() {
  if (isEdit.value) {
    const dialogResult = await openInfoDialog()
    switch (dialogResult) {
      case Or10827Const.DIALOG_RESULT_YES:
        await insert()
        break
      case Or10827Const.DIALOG_RESULT_NO:
        // いいえ選択時は編集内容を破棄するので何もしない
        setState({ isOpen: false })
        break
      case Or10827Const.DIALOG_RESULT_CANCEL:
        // キャンセル選択時は複写データの作成を行わずに終了する
        break
    }
  } else {
    setState({ isOpen: false })
  }
}

/**
 * 保存ボタン押下
 */
async function insert() {
  if (isEdit.value) {
    let status
    if (local.mo00043.id === Or10827Const.TAB.TAB_ID_CONTENT) {
      status =  await or00725Ref.value?.save()
    } else if (local.mo00043.id === Or10827Const.TAB.TAB_ID_DAILYSCHEDULE) {
    status = await or00734Ref.value?.save()
    }
    if (status) {
      setState({ isOpen: false })
    }
  } else {
    setState({ isOpen: false })
  }
}

/**
 * 確認ダイアログ表示
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
async function openInfoDialog(): Promise<string> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = Or10827Const.DIALOG_RESULT_CANCEL

        if (event?.firstBtnClickFlg) {
          result = Or10827Const.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or10827Const.DIALOG_RESULT_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = Or10827Const.DIALOG_RESULT_CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  return useScreenStore().isEditNavControl()
})
</script>

<template>
  <!-- ダイアログ -->
  <base-mo00024
    v-model="local.mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <base-mo00043
        v-model="local.mo00043Transfer"
        :oneway-model-value="localOneway.mo00043OneWay"
      ></base-mo00043>
      <c-v-window v-model="local.mo00043.id">
        <c-v-window-item value="GUI00987" class="windows-height">
          <g-custom-or00734
            v-if="local.mo00043.id === 'GUI00987'"
            v-bind="or00734"
            ref="or00734Ref"
            :oneway-model-value="localOneway.or00734OneWay"
            :parent-unique-cp-id="props.uniqueCpId"
          >
          </g-custom-or00734>
        </c-v-window-item>
        <c-v-window-item value="GUI00935" class="windows-height">
          <c-v-sheet class="content">
            <c-v-row class="ma-0">
              <c-v-col class="v-col">
                <g-custom-or00725
            v-if="local.mo00043.id === 'GUI00935'"
                  v-bind="or00725"
                  ref="or00725Ref"
                  class="intentionList"
                  :parent-unique-cp-id="props.uniqueCpId"
                >
                </g-custom-or00725></c-v-col
            ></c-v-row>
          </c-v-sheet>
        </c-v-window-item>
        <c-v-window-item value="GUI00988" class="windows-height">
          {{ t('label.daily-life-and-nursing-care-services-examples') }}</c-v-window-item
        >
      </c-v-window>
  <c-v-row no-gutters>
    <c-v-col>
      <base-mo01338 :oneway-model-value="localOneway.mo01338Threeway" />
    </c-v-col>
  </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          @click="close"
        ></base-mo00611>
        <!-- 確定ボタン Mo00609 -->
        <base-mo00609
          v-bind="localOneway.mo00609ConfirmOneway"
          class="mx-2"
          @click="insert"
        >
        </base-mo00609>
      </c-v-row>
    </template>
    <!-- Or21814:有機体:確認ダイアログ -->
    <g-base-or21814 v-bind="or21814" />
  </base-mo00024>
</template>

<style scoped>
.windows-height{
  height:475px
}
</style>
