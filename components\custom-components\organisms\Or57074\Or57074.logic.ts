import { Or10016Const } from '../Or10016/Or10016.constants'
import { Or10016Logic } from '../Or10016/Or10016.logic'
import { OrX0135Const } from '../OrX0135/OrX0135.constants'
import { OrX0135Logic } from '../OrX0135/OrX0135.logic'
import { Or57074Const } from './Or57074.constants'
import type { Or57074StateType } from './Or57074.type'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { OrX0117Const } from '~/components/custom-components/organisms/OrX0117/OrX0117.constants'
import { OrX0117Logic } from '~/components/custom-components/organisms/OrX0117/OrX0117.logic'
import { OrX0128Const } from '~/components/custom-components/organisms/OrX0128/OrX0128.constants'
import { OrX0128Logic } from '~/components/custom-components/organisms/OrX0128/OrX0128.logic'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import { OrX0130Logic } from '~/components/custom-components/organisms/OrX0130/OrX0130.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'

/**
 * Or57074:有機体:モーダル（特殊コンポーネント）
 * 処理ロジック
 *
 * @description
 * initialize処理とpinia領域操作用の処理を提供する
 *
 * <AUTHOR>
 */
export namespace Or57074Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or57074Const.CP_ID(0),
      uniqueCpId,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [
        { cpId: Or21815Const.CP_ID(0) },
        { cpId: OrX0117Const.CP_ID(0) },
        { cpId: OrX0128Const.CP_ID(0) },
        { cpId: OrX0130Const.CP_ID(0) },
        { cpId: Or21814Const.CP_ID(0) },
        { cpId: OrX0135Const.CP_ID(1) },
        { cpId: Or10016Const.CP_ID(1) },
        { cpId: Or21815Const.CP_ID(0) },
      ],
    })

    // 子コンポーネントのセットアップ
    Or21815Logic.initialize(childCpIds[Or21815Const.CP_ID(0)].uniqueCpId)
    OrX0117Logic.initialize(childCpIds[OrX0117Const.CP_ID(0)].uniqueCpId)
    OrX0128Logic.initialize(childCpIds[OrX0128Const.CP_ID(0)].uniqueCpId)
    OrX0130Logic.initialize(childCpIds[OrX0130Const.CP_ID(0)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(0)].uniqueCpId)
    Or10016Logic.initialize(childCpIds[Or10016Const.CP_ID(1)].uniqueCpId)
    OrX0135Logic.initialize(childCpIds[OrX0135Const.CP_ID(1)].uniqueCpId)
    Or21815Logic.initialize(childCpIds[Or21815Const.CP_ID(0)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }

  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or57074StateType>(Or57074Const.CP_ID(0))
}
