import { Or10016Const } from '../Or10016/Or10016.constants'
import { Or10016Logic } from '../Or10016/Or10016.logic'
import { Or26257Const } from '../Or26257/Or26257.constants'
import { Or26257Logic } from '../Or26257/Or26257.logic'
import { OrX0130Const } from '../OrX0130/OrX0130.constants'
import { OrX0130Logic } from '../OrX0130/OrX0130.logic'
import { Or14727Const } from '../Or14727/Or14727.constants'
import { Or14727Logic } from '../Or14727/Or14727.logic'
import { OrX0145Const } from '../OrX0145/OrX0145.constants'
import { OrX0145Logic } from '../OrX0145/OrX0145.logic'
import { Or27592Const } from './Or27592.constants'
import type { Or27592StateType } from './Or27592.type'
import { Or00094Const } from '~/components/base-components/organisms/Or00094/Or00094.constants'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'
import { Or00094Logic } from '~/components/base-components/organisms/Or00094/Or00094.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
/**
 * Or27592:支援経過記録一覧印刷設定モーダル
 * GUI01264_印刷設定
 *
 * @description
 * 処理ロジック
 *
 * <AUTHOR> PHAM TIEN THANH
 */
export namespace Or27592Logic {
  /**
   * initialize
   *
   * @description
   * コンポーネントの初期化処理。
   * 共通関数を呼びpiniaの領域を初期化する。
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or27592Const.CP_ID(0),
      uniqueCpId,
      initTwoWayValue: {
        choPrtList: [],
      },
      childCps: [
        { cpId: Or00094Const.CP_ID(0) },
        { cpId: Or21813Const.CP_ID(0) },
        { cpId: Or21815Const.CP_ID(0) },
        { cpId: Or10016Const.CP_ID(0) },
        { cpId: Or26257Const.CP_ID(0) },
        { cpId: OrX0130Const.CP_ID(0) },
        { cpId: Or14727Const.CP_ID(0) },
        { cpId: OrX0145Const.CP_ID(0) },
      ],
      // 編集フラグ不要
      // ※ 元々双方向領域を持たないため記載不要だが、サンプル用にナビゲーション制御領域で持つデータを分かりやすくするために設定
      editFlgNecessity: false,
    })

    // 子コンポーネントのセットアップ
    Or00094Logic.initialize(childCpIds[Or00094Const.CP_ID(0)].uniqueCpId)
    Or21813Logic.initialize(childCpIds[Or21813Const.CP_ID(0)].uniqueCpId)
    Or21815Logic.initialize(childCpIds[Or21815Const.CP_ID(0)].uniqueCpId)
    Or10016Logic.initialize(childCpIds[Or10016Const.CP_ID(0)].uniqueCpId)
    Or26257Logic.initialize(childCpIds[Or26257Const.CP_ID(0)].uniqueCpId)
    Or14727Logic.initialize(childCpIds[Or14727Const.CP_ID(0)].uniqueCpId)
    OrX0130Logic.initialize(childCpIds[OrX0130Const.CP_ID(0)].uniqueCpId)
    OrX0145Logic.initialize(childCpIds[OrX0145Const.CP_ID(0)].uniqueCpId)
    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }

  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or27592StateType>(Or27592Const.CP_ID(0))
}
