import { Or24551Const } from '../Or24551/Or24551.constants'
import { Gui00048Logic } from '../Gui00048/Gui00048.logic'
import { Gui00048Const } from '../Gui00048/Gui00048.constants'
import { Or24551Logic } from '../Or24551/Or24551.logic'
import { Or51809Const } from './Or51809.constants'
import type { Or51809StateType } from './Or51809.type'
import { useInitialize, useOneWayBindAccessor } from '#imports'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type { Or51809Type } from '~/types/cmn/business/components/Or51809Type'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { OrX0145Logic } from '~/components/custom-components/organisms/OrX0145/OrX0145.logic'
import { OrX0145Const } from '~/components/custom-components/organisms/OrX0145/OrX0145.constants'

/**
 * Or51809:有機体:サービス利用票・別表データ移動
 * 処理ロジック
 *
 * @description
 * initialize処理とpinia領域操作用の処理を提供する
 *
 * <AUTHOR>
 */
export namespace Or51809Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize<Or51809Type>({
      cpId: Or51809Const.CP_ID(1),
      uniqueCpId,
      initOneWayState: {
        isOpen: Or51809Const.DEFAULT.IS_OPEN,
      },
      childCps: [
        { cpId: Or21814Const.CP_ID(1) },
        { cpId: Or21814Const.CP_ID(2) },
        { cpId: Or21814Const.CP_ID(3) },
        { cpId: Or21813Const.CP_ID(1) },
        { cpId: Or24551Const.CP_ID(1) },
        { cpId: Gui00048Const.CP_ID(1) },
        { cpId: OrX0145Const.CP_ID(0) },
      ],
    })

    // 子コンポーネントのセットアップ
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(1)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(2)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(3)].uniqueCpId)
    Or21813Logic.initialize(childCpIds[Or21813Const.CP_ID(1)].uniqueCpId)
    Or24551Logic.initialize(childCpIds[Or24551Const.CP_ID(1)].uniqueCpId)
    Gui00048Logic.initialize(childCpIds[Gui00048Const.CP_ID(1)].uniqueCpId)
    OrX0145Logic.initialize(childCpIds[OrX0145Const.CP_ID(0)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }

  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or51809StateType>(Or51809Const.CP_ID(0))
}
