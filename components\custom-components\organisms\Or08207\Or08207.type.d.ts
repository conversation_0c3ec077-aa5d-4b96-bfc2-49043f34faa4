/**
 * Or08207_基本情報画面入力フォーム
 * GUI01067_基本情報
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR> HOANG SY TOAN
 */

import type { Mo00045Type } from '@/types/business/components/Mo00045Type'
import type { Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00040Type } from '~/types/business/components/Mo00040Type'
import type { Mo00046Type } from '~/types/business/components/Mo00046Type'

/**
 *Or08207OnewayType
 */
export interface Or08207OnewayType {
  /**
   *基本情報ID
   */
  khn11Id: string
  /**
   *コピーフラグ
   */
  isCopyFlag: boolean
}

/**
 *Or08207StateType
 */
export interface Or08207StateType {
  /** 予防基本複写詳細_基本情報リスト */
  kihonList: BasicInfo
}

/**
 *スクリーンデータ
 */
 export interface ScreenData {
  /**
   *データ
   */
  data: {
    consulationMethod: string
    consulationRouteOthers: Mo00045Type
    homeVisitKind: string
    lastConsultationDate: Mo00020Type

    atHome: Mo00018Type
    hospitalized: Mo00018Type
    institutionalized: Mo00018Type
    presentConditionOthers: Mo00045Type

    handycapSeniorEverydayLifeIndependenceLevel: string
    degreeIndepDailyLivingEldDem: string

    levelOfCareRequired: Mo00040Type
    validTimeLimitStartDate: Mo00020Type
    validTimeLimitEndDate: Mo00020Type
    prevCareDegree: Mo00040Type
    basicChecklistEntryResult: string
    basicChecklistEntryDate: Mo00020Type

    disability: Mo00018Type
    disabilityLevel: Mo00040Type
    upbringing: Mo00018Type
    remedialEducationGrade: Mo00040Type
    spirit: Mo00018Type
    mentalGrade: Mo00040Type
    incrudableDisease: Mo00018Type
    memo1: Mo00045Type
    memo2: Mo00045Type

    personalHome: Mo00018Type
    rentedHouse: Mo00018Type
    detachedHouse: Mo00018Type
    apartment: Mo00018Type
    privateRoom: string
    privateRoomFloor: Mo00045Type
    housingRepair: string

    nationalPension: Mo00018Type
    welfarePension: Mo00018Type
    disabPension: Mo00018Type
    lifeProtection: Mo00018Type
    economySituation: Mo00046Type

    nameConsent: Mo00045Type
    relationship: Mo00045Type
    addressContactInfo: Mo00046Type

    Or31675: {
      line1: {
        name: string
        relationship: string
        address: string
      }
      line2: {
        name: string
        relationship: string
        address: string
      }
      line3: {
        name: string
        relationship: string
        address: string
      }
      line4: {
        name: string
        relationship: string
        address: string
      }
    }
    familyDiagramZoomOutFlag: boolean
    familyRelationEtcSituation: Mo00046Type
  }
}

/**
 * ConsultationData
 */
export interface ConsultationData {
  /** 予防基本複写詳細_基本情報リスト */
  kihonList: BasicInfo[]

  /** 予防基本複写詳細_介護予防リスト */
  kaigoList: CarePreventionInfo[]

  /** 予防基本複写詳細_病歴と経過リスト */
  byourekiList: MedicalHistory[]

  /** 家族構成図リスト */
  kozokuList: FamilyStructure[]
}
/** 予防基本複写詳細_基本情報リスト */
export interface BasicInfo {
  /** カウンター */
  khn12Id: string

  /** 基本情報ID */
  khn11Id: string

  /** 相談方法 */
  soudanH: string

  /** 相談方法（その他） */
  soudanIn: string

  /** 訪問の種別 */
  soudanH2: string

  /** 前回の相談日 */
  maesouInYmd: string

  /** 在宅有無 */
  ztkUmu: string

  /** 入院有無 */
  nyuinUmu: string

  /** 入所有無 */
  nyushoUmu: string

  /** 現況その他 */
  genkyoKnj: string

  /** 障害高齢者の日常生活自立度 */
  netaCd1: string

  /** 認知症高齢者の日常生活自立度 */
  netaCd2: string

  /** 要介護度状態区分 */
  ninteiHyouji: string

  /** 認定有効開始日 */
  startYmd1: string

  /** 認定有効終了日 */
  endYmd1: string

  /** 前回の要介護度 */
  maeyokaiHyouji: string

  /** 基本チェックリスト記入結果 */
  chklistkekaSentaku: string

  /** 基本チェックリスト記入日 */
  chklistYmdHyouji: string

  /** 身障手帳有無 */
  sinshoUmu: string

  /** 身障等級 */
  sinshoHyouji: string

  /** 療育手帳有無 */
  ryoikuUmu: string

  /** 療育等級 */
  ryoikuHyouji: string

  /** 精神手帳 */
  seisinUmu: string

  /** 精神等級 */
  seisinHyouji: string

  /** 難病有無 */
  nanbyoUmu: string

  /** メモ1 */
  nanbyoMemoknj1: string

  /** メモ2 */
  nanbyoMemoknj2: string

  /** 自宅 */
  jitaKu: string

  /** 借家 */
  shakuya: string

  /** 一戸建て */
  ikkodate: string

  /** 集合住宅 */
  shugou: string

  /** 自室の有無 */
  jisiTu: string

  /** 自室の階 */
  jisituKai: string

  /** 住宅改修の有無 */
  kaishu: string

  /** 国民年金 */
  kokuNen: string

  /** 厚生年金 */
  kouNen: string

  /** 障害年金 */
  shoNen: string

  /** 生活保護 */
  seikatuHogo: string

  /** 経済状況その他 */
  keizai: string

  /** 相談者氏名 */
  sdNameKnj: string

  /** 相談者住所 */
  sdAddrKnj: string

  /** 相談者続柄 */
  sdZokugaraKnj: string

  /** 緊急連絡先氏名１ */
  sdNameKnj1: string

  /** 緊急連絡先続柄１ */
  sdZokugaraKnj1: string

  /** 緊急連絡先住所等１ */
  jyuusyoKnj1: string

  /** 緊急連絡先氏名２ */
  sdNameKnj2: string

  /** 緊急連絡先続柄２ */
  sdZokugaraKnj2: string

  /** 緊急連絡先住所等２ */
  jyuusyoKnj2: string

  /** 緊急連絡先氏名３ */
  sdNameKnj3: string

  /** 緊急連絡先続柄３ */
  sdZokugaraKnj3: string

  /** 緊急連絡先住所等３ */
  jyuusyoKnj3: string

  /** 緊急連絡先氏名４ */
  sdNameKnj4: string

  /** 緊急連絡先続柄４ */
  sdZokugaraKnj4: string

  /** 緊急連絡先住所等４ */
  jyuusyoKnj4: string

  /** 家族関係等の状況 */
  kazokuKankeiIn: string

  /** 家族図縮小フラグ */
  shukushoFlg: string
}
/** 予防基本複写詳細_介護予防リスト */
export interface CarePreventionInfo {
  /** カウンター */
  khn13Id: string

  /** 基本情報ID */
  khn11Id: string

  /** 今までの生活 */
  imamadeKnj: string

  /** 1日の過ごし方 */
  sugosikataKnj: string

  /** 趣味、楽しみ、特技 */
  shumiKnj: string

  /** 友人、地域との関係 */
  kankeiKnj: string

  /** 現在受けている公的サービス */
  koutekisvKnj: string

  /** 現在受けている非公的サービス */
  hikoutekisvKnj: string
}
/** 予防基本複写詳細_病歴と経過リスト */
export interface MedicalHistory {
  /** カウンター */
  khn133Id: string

  /** 基本情報ID */
  khn11Id: string

  /** 年月日等 */
  byoymdKnj: string

  /** 病名 */
  byoumeiKnj: string

  /** 医療機関・医師名 */
  iryokikanKnj: string

  /** 主治医意見書作成者 */
  ikenshoCd: string

  /** 医療機関TEL */
  iryokikanTel: string

  /** 経過 */
  keikaKnj: string

  /** 治療中の場合の内容 */
  naiyoKnj: string
}
/** 家族構成図リスト */
export interface FamilyStructure {
  /** 基本情報ID */
  khn11Id: string

  /** 種別（枠、棒） */
  type: string

  /** 図番号（連番） */
  zuNo: string

  /** X座標 */
  xpos: string

  /** Y座標 */
  ypos: string

  /** x2座標 */
  xpos2: string

  /** y2座標 */
  ypos2: string

  /** 備考 */
  memoKnj: string

  /** 表示マーク */
  mark: string

  /** 表示色 */
  color: string

  /** 部品種別 */
  hatchFlg: string

  /** 塗りの種類 */
  hatchType: string

  /** 枠線の種類 */
  lineType: string
}

/**
 * PlanData
 */
export interface PlanData {
  /** 表示項目 */
  showItems: ShowItem[]

  /** 詳細データ取得用項目 */
  detailGetItems: DetailGetItem[]

  /** データリスト */
  dataList: DataItem[]
}

/**
 * 表示項目
 */
export interface ShowItem {
  /** ラベル */
  label: string

  /** フィールド */
  field: string
}

/**
 * 詳細データ取得用項目
 */
export interface DetailGetItem {
  /** フィールド名 */
  fieldName: string

  /** フィールド */
  field: string
}

/**
 * データリスト
 */
export interface DataItem {
  /** 開始日 */
  startYmd: string

  /** 終了日 */
  endYmd: string

  /** 計画期間ID */
  sc1Id: string

  /** 基本情報ID */
  khn11Id: string

  /** 作成日 */
  createYMD: string

  /** 作成者 */
  shokuKnj: string

  /** 事業名（略称） */
  jigyoRyakuKnj: string
}
