import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * OrX0042:計画書（２）有機体（画面コンポーネント）
 *
 * @description
 * 静的データ
 *
 * <AUTHOR>
 */
export namespace OrX0042Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('OrX0042', seq)

  /**
   * 全
   */
  export const STR_ALL = '全'

  /**
   * ケアプラン方式
   */
  export const CARE_PLAN_METHOD = {
    /**
     * 包括的自立支援プログラム
     */
    COMPREHENSIVE_INDEPENDENCE_SUPPORT: '1',
    /**
     * フリーアセスメント
     */
    FREE_ASSESSMENT: '7',
    /**
     * 情報収集
     */
    COLLECT_INFO: '8',
    /**
     * インタライン
     */
    INTER_LINE: '9',
  }

  /**
   * ページアクション
   */
  export const PAGE_ACTION = {
    /**
     * 新規
     */
    NEW: 'C',
  }
  /**
   *  画面入力データの変更がある場合のタイプ
   */
  export const CHANGED_DIALOG_ACTION = {
    /**
     * 新規
     */
    NEW: 'NEW',
    /**
     * 履歴
     */
    HIST: 'HIST',
    /**
     * 「履歴-前へアイコンボタン」押下
     */
    HIST_PRE_PAGE: 'HIST_PRE_PAGE',
    /**
     * 「履歴-次へアイコンボタン」押下
     */
    HIST_NEXT_PAGE: 'HIST_NEXT_PAGE',
  }
  /**
   * 履歴変更区分
   */
  export const HISTORY_CHANGE_KBN = {
    /**
     * 0:選択されている計画書ID
     */
    SELECTED_ID: '0',
    /**
     * 1:選択している計画書IDの前
     */
    BEFORE_SELECTED_ID: '1',
    /**
     * 1:選択している計画書IDの次
     */
    AFTER_SELECTED_ID: '2',
  }

  /**
   * 期間管理フラグ
   */
  export const PERIOD_MANAGEMENT_FLG = {
    /** 0:管理しない */
    NO_MANAGEMENT: '0',
    /** 1:管理する */
    MANAGEMENT: '1',
  }
  /**
   * 計画書様式フラグ
   */
  export const CARE_PLAN_STYLE_FLG = {
    /** 2(居宅) */
    HOME: '2',
  }
  /**
   * 事業者コードのリスト
   */
  export const OFFICE_CD_LIST = ['30010', '50010', '23031', '43031', '61084', '71084', '61104']
  /**
   * 期間の管理
   */
  export const PERIOD_MANAGER = {
    /** 0(日付) */
    DATE: '0',
    /** 文章 */
    DOC: '1',
  }
  /**
   * 番号フラグ
   */
  export const NUMBER_FLG = {
    /** 0(番号フラグ) */
    NOT_SHOW: '0',
  }
  /**
   * 取込設定
   */
  export const IMPORT_SETTINGS ={
    /** ０:短期目標 */
    TANKI_KNG: '0',
    /** 1:サービス内容の場合 */
    KAIGO_KNG: '1',
  }
  /**
   * システム略称
   */
  export const SYS_ABBR = {
    /** CMN */
    CMN: 'CMN',
    /** CPN */
    CPN: 'CPN',
  }
  /**
   * システム略称
   */
  export const DATE_FLG = {
    /** ?月?日 */
    MM_MO_DD_DAY: '2',
    /** ?/? */
    MM_DD: '3',
  }
  /**
   * 電子ファイル保存設定区分
   */
  export const ELEC_FILE_SAVE_TYPE = {
    /** 適用する */
    APPLY: '1',
  }

  /**
   * 履歴更新区分
   */
  export const HISTORY_UPD_KBN = {
    /**
     * C(新規)
     */
    C: 'C',
  }
  /**
   * メッセージ表示フラグ
   */
  export const MESSAGE_SHOW_FLG = {
    /**
     * 1.表示する
     */
    SHOW: '1',
  }
  /**
   * 曜日
   */
  export const YOBI = {
    /**
     * 1.選択
     */
    SELECT: '1',
    /**
     * 0000000.未選択
     */
    UN_SELECT: '0000000',
  }
  /**
   * 頻度
   */
  export const FREQUENCY = {
    /**
     * 00:00
     */
    TIME_ZERO: '00:00',
  }
  /**
   * 改行
   */
  export const ENTER = '\n'
  /**
   * 取込モード
   */
  export const IMPORT_MODE = {
    /**
     * 0.追加
     */
    ADD: '0',
    /**
     * 1.行追加
     */
    ADD_LINE: '1',
  }
  /**
   * 画面ID
   */
  export const SCREEN_ID = 'GUI01014'
  /**
   * 分類CD
   */
  export const CLASSIFICATION_CD = {
    /**
     * 大分類CD:820
     */
    BIG_820: '820',
    /**
     * 中分類CD:1
     */
    CENTRE_1: '1',
    /**
     * 中分類CD:1
     */
    CENTRE_2: '2',
    /**
     * 中分類CD:3
     */
    CENTRE_3: '3',
    /**
     * 中分類CD:4
     */
    CENTRE_4: '4',
    /**
     * 中分類CD:5
     */
    CENTRE_5: '5',
    /**
     * 中分類CD:6
     */
    CENTRE_6: '6',
    /**
     * 中分類CD:10
     */
    CENTRE_10: '10',
    /**
     * 中分類CD:11
     */
    CENTRE_11: '11',
    /**
     * 小分類CD:0
     */
    SMALL_0: '0',
  }
  /**
   * テーブル名
   */
  export const TABLE_NAME = 'cpn_tuc_cks22'
  /**
   * カラム名
   */
  export const COLUMN_NAME = {
    /**
     * カラム名:"gutaiteki_knj"
     */
    GUTAITEKI_KNJ_SNAKE : 'gutaiteki_knj',
    /**
     * カラム名:"gutaitekiKnj"
     */
    GUTAITEKI_KNJ_CAMEL : 'gutaitekiKnj',
    /**
     * カラム名:"chouki_knj"
     */
    CHOUKI_KNJ_SNAKE : 'chouki_knj',
    /**
     * カラム名:"choukiKnj"
     */
    CHOUKI_KNJ_CAMEL : 'choukiKnj',
    /**
     * カラム名:"cho_kikan_knj"
     */
    CHO_KIKAN_KNJ_SNAKE : 'cho_kikan_knj',
    /**
     * カラム名:"choKikanKnj"
     */
    CHO_KIKAN_KNJ_CAMEL : 'choKikanKnj',
    /**
     * カラム名:"tanki_knj"
     */
    TANKI_KNJ_SNAKE : 'tanki_knj',
    /**
     * カラム名:"tankiKnj"
     */
    TANKI_KNJ_CAMEL : 'tankiKnj',
    /**
     * カラム名:"tan_kikan_knj"
     */
    TAN_KIKAN_KNJ_SNAKE : 'tan_kikan_knj',
    /**
     * カラム名:"tanKikanKnj"
     */
    TAN_KIKAN_KNJ_CAMEL : 'tanKikanKnj',
    /**
     * カラム名:"kaigo_knj"
     */
    KAIGO_KNJ_SNAKE : 'kaigo_knj',
    /**
     * カラム名:"kaigoKnj"
     */
    KAIGO_KNJ_CAMEL : 'kaigoKnj',
    /**
     * カラム名:"hindo_knj"
     */
    HINDO_KNJ_SNAKE : 'hindo_knj',
    /**
     * カラム名:"hindoKnj"
     */
    HINDO_KNJ_CAMEL : 'hindoKnj',
    /**
     * カラム名:"kikan_knj"
     */
    KIKAN_KNJ_SNAKE : 'kikan_knj',
    /**
     * カラム名:"kikanKnj"
     */
    KIKAN_KNJ_CAMEL: 'kikanKnj',
    /**
     * カラム名:"svShuKnj"
     */
    SV_SHU_KNJ_CAMEL : 'svShuKnj',
    /**
     * カラム名:"jigyoNameKnj"
     */
    JIGYO_NAME_KNJ_CAMEL : 'jigyoNameKnj',
  }
  /**
   * 機能ID
   */
  export const FUNCTION_ID_ZERO = '0'
  /**
   * 呼出元フラグ
   */
  export const CALLER_FLAG_ZERO = '0'
}
