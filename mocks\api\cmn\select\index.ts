import { HttpResponse, type ResponseResolver } from 'msw'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { InEntity } from '~/repositories/AbstructWebRepository'
import LongTermGoalImport from '@/mocks/api/cmn/select/pages/longTermGoalImport/handler'
import DischargeFromHospitalLeavingInfoImportSelect from '@/mocks/api/cmn/select/pages/dischargeFromHospitalLeavingInfoImportSelect/handler'
import WeekPlanPatternTitleSelect from '@/mocks/api/cmn/select/pages/weekPlanPatternTitleSelect/handler'
import DailyPlanPatternSettingInitSelect from '@/mocks/api/cmn/select/pages/dailyPlanPatternSettingInitSelect/handler'
import DailyPlanPatternTitleInitSelect from '@/mocks/api/cmn/select/pages/dailyPlanPatternTitleInitSelect/handler'
import TermInfoSelect from '@/mocks/api/cmn/select/pages/termInfoSelect/handler'
import VisitSituationInfoSelect from '@/mocks/api/cmn/select/pages/visitSituationInfoSelect/handler'
import IssuesObjectivesImportSelect from '@/mocks/api/cmn/select/pages/issuesObjectivesImportSelect/handler'
import WeekCopyHistorySelect from '@/mocks/api/cmn/select/pages/weekCopyHistorySelect/handler'
import weekCopyPlanPeriodSelect from '@/mocks/api/cmn/select/pages/weekCopyPlanPeriodSelect/handler'
import weekCopyInfoSelect from '@/mocks/api/cmn/select/pages/weekCopyInfoSelect/handler'
import userSelectListInfoSelect from '@/mocks/api/cmn/select/pages/userSelectListInfoSelect/handler'
import duplicateAssessmentSelectInEntity from '@/mocks/api/cmn/select/pages/duplicateAssessmentSelectInEntity/handler'
import GUIyyyy from '@/mocks/api/cmn/select/pages/sample_page/hidden-item/input-sample/GUIyyyy/handler'
import GUIxxxx from '@/mocks/api/cmn/select/pages/sample_page/hidden-item/use-sample/GUIxxxx/handler'
import HistorySelectTotal from '@/mocks/api/cmn/select/pages/historySelectTotal/handler'
import DailyTaskImportSelect from '@/mocks/api/cmn/select/pages/dailyTaskImportSelect/handler'
import Cp1HistInfoAcquisitionSelect from '@/mocks/api/cmn/select/pages/cp1HistInfoAcquisitionSelect/handler'
import AdditionInfoSelect from '@/mocks/api/cmn/select/pages/additionInfoSelect/handler'
import ApplicableOfficeSelect from '@/mocks/api/cmn/select/pages/applicableOfficeSelect/handler'
import ApplicableOfficeConfirmSelect from '@/mocks/api/cmn/select/pages/applicableOfficeConfirmSelect/handler'
import AssessmentInterRAIQInitSelect from '@/mocks/api/cmn/select/pages/assessmentInterRAIQInitSelect/handler'
import WeekTableMst from '@/mocks/api/cmn/select/pages/weekTableMst/handler'
import AssessmentComprehensivePrintSettingsInitialSelect from '@/mocks/api/cmn/select/pages/assessmentComprehensivePrintSettingsInitialSelect/handler'
import AssessmentComprehensivePrintSettingsHistorySelect from '@/mocks/api/cmn/select/pages/assessmentComprehensivePrintSettingsHistorySelect/handler'
import GUI00380 from '@/mocks/api/cmn/select/pages/dch/users/um-asp/GUI00380/handler'
import { handler as HistorySelectWeekPlan } from '@/mocks/api/cmn/select/pages/historySelectWeekPlan/handler'
import NinteiInfoInputSelect from '@/mocks/api/cmn/select/pages/ninteiInfoInputSelect/handler'
import evaluationTableDuplicatePlanPeriodSelect from '@/mocks/api/cmn/select/pages/evaluationTableDuplicatePlanPeriodSelect/handler'
import assessmentSummaryImportSettingSelect from '@/mocks/api/cmn/select/pages/assessmentSummaryImportSettingSelect/handler'
import difficultyMasterSelect from '@/mocks/api/cmn/select/pages/difficultyMasterSelect/handler'
import inhibitoryFactorsMasterSelect from '@/mocks/api/cmn/select/pages/inhibitoryFactorsMasterSelect/handler'
import issueOrganizeSummaryMasterSelect from '@/mocks/api/cmn/select/pages/issueOrganizeSummaryMasterSelect/handler'
import supportElapsedRecordSelect from '@/mocks/api/cmn/select/pages/supportElapsedRecordSelect/handler'
import getKadaiMasterSetting from '@/mocks/api/cmn/select/pages/getKadaiMasterSetting/handler'
import isKadaiListDel from '@/mocks/api/cmn/select/pages/isKadaiListDel/handler'
import issueOrganizeSummaryMasterCheckSelect from '@/mocks/api/cmn/select/pages/issueOrganizeSummaryMasterCheckSelect/handler'
import evaluationTableCarePlan2ImportSelect from '@/mocks/api/cmn/select/pages/evaluationTableCarePlan2ImportSelect/handler'
import isExistsKadaiList2 from '@/mocks/api/cmn/select/pages/isExistsKadaiList2/handler'
import IncentivesItemsPrintSettingsHistorySelect from '@/mocks/api/cmn/select/pages/incentivesItemsPrintSettingsHistorySelect/handler'
import IncentivesItemsPrintSettingsSubjectSelect from '@/mocks/api/cmn/select/pages/incentivesItemsPrintSettingsSubjectSelect/handler'
import getYoshikiKadaiInfo from '@/mocks/api/cmn/select/pages/getYoshikiKadaiInfo/handler'
import isKadaiMasterRowDel from '@/mocks/api/cmn/select/pages/isKadaiMasterRowDel/handler'
import maxDifficultyCdSelect from '@/mocks/api/cmn/select/pages/maxDifficultyCdSelect/handler'
import inquiryContentsImportInfoSelect from '@/mocks/api/cmn/select/pages/inquiryContentsImportInfoSelect/handler'
import inquiryContentsImportHistoryListSelect from '@/mocks/api/cmn/select/pages/inquiryContentsImportHistoryListSelect/handler'
import inquiryContentsImportHistoryDetailsListSelect from '@/mocks/api/cmn/select/pages/inquiryContentsImportHistoryDetailsListSelect/handler'
import narrowDownConditionsSettingsSelect from '@/mocks/api/cmn/select/pages/narrowDownConditionsSettingsSelect/handler'
import considerInitialInfoSelect from '@/mocks/api/cmn/select/pages/considerInitialInfoSelect/handler'
import considerPeriodSelect from '@/mocks/api/cmn/select/pages/considerPeriodSelect/handler'
import considerHistoryModifiedSelect from '@/mocks/api/cmn/select/pages/considerHistoryModifiedSelect/handler'
import pensionBookSelectionSelect from '@/mocks/api/cmn/select/pages/pensionBookSelectionSelect/handler'
import historySelectAssessmentFaceSheetSelect from '@/mocks/api/cmn/select/pages/historySelectAssessmentFaceSheetSelect/handler'
import symbolSelectionSelect from '@/mocks/api/cmn/select/pages/symbolSelectionSelect/handler'
import OccupationInfoSelect from '@/mocks/api/cmn/select/pages/occupationInfoSelect/handler'
import WeekPlanMasterInfoSelect from '@/mocks/api/cmn/select/pages/weekPlanMasterInfoSelect/handler'
import HolidaySetSelect from '@/mocks/api/cmn/select/pages/holidaySetSelect/handler'
import CommonServiceInputSupportSelect from '@/mocks/api/cmn/select/pages/commonServiceInputSupportSelect/handler'
import userPlanInfoSelectService from '@/mocks/api/cmn/select/pages/userPlanInfoSelectService/handler'
import MonitoringConfigureMasterInitSelect from '@/mocks/api/cmn/select/pages/monitoringConfigureMasterInitSelect/handler'
import CaseListInfoSelect from '@/mocks/api/cmn/select/pages/caseListInfoSelect/handler'
import CarePlanOneInitSelect from '@/mocks/api/cmn/select/pages/carePlanOneInitSelect/handler'
import CarePlan2InitSelect from '@/mocks/api/cmn/select/pages/carePlan2InitSelect/handler'
import PrintSettingInitInfoSelect from '@/mocks/api/cmn/select/pages/printSettingInitInfoSelect/handler'
import CarePlan2PlanPeriodSelect from '@/mocks/api/cmn/select/pages/carePlan2PlanPeriodSelect/handler'
import CarePlan2HistorySelect from '@/mocks/api/cmn/select/pages/carePlan2HistorySelect/handler'
import AssessmentComprehensivePrintSettinguserSwitchingSelect from '@/mocks/api/cmn/select/pages/assessmentComprehensivePrintSettinguserSwitchingSelect/handler'
import CarePlan2MasterSelect from '@/mocks/api/cmn/select/pages/carePlan2MasterSelect/handler'
import Careplan2TermIdSelect from '@/mocks/api/cmn/select/pages/careplan2TermIdSelect/handler'
import FreeAssessmentCheckPrintSettingsHistorySelect from '@/mocks/api/cmn/select/pages/freeAssessmentCheckPrintSettingsHistorySelect/handler'
import ValidPeriodIdSelect from '@/mocks/api/cmn/select/pages/validPeriodIdSelect/handler'
import IssuesAnalysisPrintSettingsHistorySelect from '@/mocks/api/cmn/select/pages/issuesAnalysisPrintSettingsHistorySelect/handler'
import AssessmentMasterSelect from '~/mocks/api/cmn/select/pages/assessmentMasterSelect/handler'
import CpnTucGdlComInfoSelect from '~/mocks/api/cmn/select/pages/cpnTucGdlComInfoSelect/handler'
import AssessmentHomeCommonInfoSelect from '~/mocks/api/cmn/select/pages/assessmentHomeCommonInfoSelect/handler'
import AssessmentHomePlanningPeriodChangeSelect from '~/mocks/api/cmn/select/pages/assessmentHomePlanningPeriodChangeSelect/handler'
import AssessmentHomePrintSettingsSelect from '~/mocks/api/cmn/select/pages/assessmentHomePrintSettingsSelect/handler'
import AssessmentHomePrintSettinguserSwitchingSelect from '~/mocks/api/cmn/select/pages/assessmentHomePrintSettinguserSwitchingSelect/handler'
import AssessmentHomeHistoryChangeSelect from '~/mocks/api/cmn/select/pages/assessmentHomeHistoryChangeSelect/handler'
import SamplePrintUserInfoSelect from '~/mocks/api/cmn/select/pages/samplePrintUserInfoSelect/handler'
import AssessmentHomeServiceInitSelect from '~/mocks/api/cmn/select/pages/assessmentHomeServiceInitSelect/handler'
import AssessmentShisetuShybetuChangeSelect from '~/mocks/api/cmn/select/pages/assessmentShisetuShybetuChangeSelect/handler'
import AssessmentHomeTab5Select from '~/mocks/api/cmn/select/pages/assessmentHomeTab5Select/handler'
import AssessmentHomeTab61Select from '~/mocks/api/cmn/select/pages/assessmentHomeTab61Select/handler'
import AssessmentHomeTab62Select from '~/mocks/api/cmn/select/pages/assessmentHomeTab62Select/handler'
import AssessmentHomeTab65Select from '~/mocks/api/cmn/select/pages/assessmentHomeTab65Select/handler'
import AssessmentDuplicateSelect from '~/mocks/api/cmn/select/pages/assessmentDuplicateSelect/handler'
import AssessmentDuplicateHistoryChange from '~/mocks/api/cmn/select/pages/assessmentDuplicateHistoryChange/handler'
import PlanMonitoringPeriodSelect from '~/mocks/api/cmn/select/pages/planMonitoringPeriodSelect/handler'
//import PlanMonitoringHistorySelect from '~/mocks/api/cmn/select/pages/planMonitoringHistorySelect/handler'
//import PlanMonitoringInitInfoSelect from '~/mocks/api/cmn/select/pages/planMonitoringInitInfoSelect/handler'
import AssessmentPackageDuplicateInitSelect from '~/mocks/api/cmn/select/pages/assessmentPackageDuplicateInitSelect/handler'
import ApprovalColumnRegistrationSelect from '~/mocks/api/cmn/select/pages/approvalColumnRegistrationSelect/handler'

import AssessmentHomeAttendingPhysicianOpinionTabSelect from '~/mocks/api/cmn/select/pages/assessmentHomeAttendingPhysicianOpinionTabSelect/handler'
import AssessmentHomeScheduleSelectTabSelect from '~/mocks/api/cmn/select/pages/assessmentHomeScheduleSelectTabSelect/handler'
import AssessmentInterRAIBNewSelect from '~/mocks/api/cmn/select/pages/assessmentInterRAIBNewSelect/handler'
import AssessmentInterRAIBSelect from '~/mocks/api/cmn/select/pages/assessmentInterRAIBSelect/handler'
import AssessmentInterRAICInitSelect from '~/mocks/api/cmn/select/pages/assessmentInterRAICInitSelect/handler'
import AssessmentInterRAICsvOutputInitSelect from '~/mocks/api/cmn/select/pages/assessmentInterRAICsvOutputInitSelect/handler'
import AssessmentInterRAICsvOutputListSelect from '~/mocks/api/cmn/select/pages/assessmentInterRAICsvOutputListSelect/handler'
import AssessmentInterRAIESelect from '~/mocks/api/cmn/select/pages/assessmentInterRAIESelect/handler'
import AssessmentInterRAIGSelect from '~/mocks/api/cmn/select/pages/assessmentInterRAIGSelect/handler'
import AssessmentInterRAIKSelect from '~/mocks/api/cmn/select/pages/assessmentInterRAIKSelect/handler'
import DisplayConfigureInitSelect from '~/mocks/api/cmn/select/pages/displayConfigureInitSelect/handler'
import AssessmentInterRAIHSelect from '~/mocks/api/cmn/select/pages/assessmentInterRAIHSelect/handler'
import AssessmentInterRAIOSelect from '~/mocks/api/cmn/select/pages/assessmentInterRAIOSelect/handler'
import AssessmentInterRAIRSelect from '~/mocks/api/cmn/select/pages/assessmentInterRAIRSelect/handler'
import AssessmentInterRAISSelect from '~/mocks/api/cmn/select/pages/assessmentInterRAISSelect/handler'
import AssessmentInterRAIPSelect from '@/mocks/api/cmn/select/pages/assessmentInterRAIPSelect/handler'
import AssessmentInterRAIFInitSelect from '~/mocks/api/cmn/select/pages/assessmentInterRAIFInitSelect/handler'
import CountKghTucCheckHeadSelect from '~/mocks/api/cmn/select/pages/countKghTucCheckHeadSelect/handler'
import faceSheetPackage4InitSelect from '~/mocks/api/cmn/select/pages/faceSheetPackage4InitSelect/handler'
import FreeAssessmentCheckPrintSettingsSubjectSelect from '~/mocks/api/cmn/select/pages/freeAssessmentCheckPrintSettingsSubjectSelect/handler'
import AssessmentInterRAII1InitSelect from '~/mocks/api/cmn/select/pages/assessmentInterRAII1InitSelect/handler'
import AssessmentInterRAIDInitSelect from '~/mocks/api/cmn/select/pages/assessmentInterRAIDInitSelect/handler'
import AssessmentInterRAIVInitSelect from '~/mocks/api/cmn/select/pages/assessmentInterRAIVInitSelect/handler'
import AssessmentInterRAIAHistorySelect from '~/mocks/api/cmn/select/pages/assessmentInterRAIAHistorySelect/handler'
import AssessmentPackageplanInitSelect from '~/mocks/api/cmn/select/pages/assessmentPackageplanInitSelect/handler'
import AssessmentPackageplanInitCommonSelect from '~/mocks/api/cmn/select/pages/assessmentPackageplanInitCommonSelect/handler'
import AssessmentPackageplanHistoryChangeSelect from '~/mocks/api/cmn/select/pages/assessmentPackageplanHistoryChangeSelect/handler'
import AssessmentPackageplanPeriodChangeSelect from '~/mocks/api/cmn/select/pages/assessmentPackageplanPeriodChangeSelect/handler'

import HistorySelectScreenAssessmentInterRAISelect from '~/mocks/api/cmn/select/pages/historySelectScreenAssessmentInterRAISelect/handler'
import HistorySelectScreenCheckItemsSelect from '~/mocks/api/cmn/select/pages/historySelectScreenCheckItemsSelect/handler'
import ContextMasterInitSelect from '~/mocks/api/cmn/select/pages/contextMasterInitSelect/handler'
import IssueAndGoalImportSelect from '@/mocks/api/cmn/select/pages/issueAndGoalImportSelect/handler'
import TaskSummaryHistorySelect from '~/mocks/api/cmn/select/pages/taskSummaryHistorySelect/handler'
import IssueAndServiceImportSelect from '~/mocks/api/cmn/select/pages/issueAndServiceImportSelect/handler'
import MonthAndYearHistorySelect from '@/mocks/api/cmn/select/pages/monthAndYearHistorySelect/handler'
import DailyScheduleMasterInitSelect from '@/mocks/api/cmn/select/pages/dailyScheduleMasterInitSelect/handler'
import PrintOrderModifiedSelect from '@/mocks/api/cmn/select/pages/printOrderModifiedSelect/handler'
import CarePlan1InitSelect from '@/mocks/api/cmn/select/pages/carePlan1InitSelect/handler'
import ComprehensivePlanInitSelect from '@/mocks/api/cmn/select/pages/comprehensivePlanInitSelect/handler'
import OrganizingIssuesImportSelect from '@/mocks/api/cmn/select/pages/organizingIssuesImportSelect/handler'
import OrganizingIssuesSelect from '@/mocks/api/cmn/select/pages/organizingIssuesSelect/handler'
import IssuesConsiderBlankFormImportSelect from '@/mocks/api/cmn/select/pages/issuesConsiderBlankFormImportSelect/handler'
import ImplementPlanOneMasterInitSelect from '@/mocks/api/cmn/select/pages/implementPlanOneMasterInitSelect/handler'
import ImplementationPlanTwoHistorySelect from '@/mocks/api/cmn/select/pages/implementationPlanTwoHistorySelect/handler'
import implementationMonitoringRegistInitSelect from '@/mocks/api/cmn/select/pages/implementationMonitoringRegistInitSelect/handler'
import ImportFreeAssessmentSelect from '~/mocks/api/cmn/select/pages/importFreeAssessmentSelect/handler'
import FrequencyRegistrationSelect from '@/mocks/api/cmn/select/pages/frequencyRegistrationSelect/handler'
import ContentSelect from '@/mocks/api/cmn/select/pages/contentSelect/handler'
import ConsiderTablePrintSettingsSelect from '@/mocks/api/cmn/select/pages/considerTablePrintSettingsSelect/handler'
import MonthlyYearlyPatternSettingSelect from '@/mocks/api/cmn/select/pages/monthlyYearlyPatternSettingSelect/handler'
import AssessmentHomeFamilyInitSelect from '@/mocks/api/cmn/select/pages/assessmentHomeFamilyInitSelect/handler'
import dailyPlanInputSelect from '@/mocks/api/cmn/select/pages/dailyPlanInputSelect/handler'
import AssessmentFaceInfoSelect from '@/mocks/api/cmn/select/pages/assessmentFaceInfoSelect/handler'
import MonthlyYearlyTableSelect from '@/mocks/api/cmn/select/pages/monthlyYearlyTableSelect/handler'
import MonthlyYearlyTableCopyReturnSelect from '@/mocks/api/cmn/select/pages/monthlyYearlyTableCopyReturnSelect/handler'
import weeklyPlanCopyHistorySelect from '@/mocks/api/cmn/select/pages/weeklyPlanCopyHistorySelect/handler'
import weeklyPlanCopyDetailHistorySelect from '@/mocks/api/cmn/select/pages/weeklyPlanCopyDetailHistorySelect/handler'
import ThinkToKeepInMindInfoSelect from '@/mocks/api/cmn/select/pages/thinkToKeepInMindInfoSelect/handler'
import HistorySelectImplementationPlan from '@/mocks/api/cmn/select/pages/historySelectImplementationPlan/handler'
import WeekHistInfoSelect from '@/mocks/api/cmn/select/pages/weekHistInfoSelect/handler'
import AssessmentInterRAISenteHistroyInitSelect from '@/mocks/api/cmn/select/pages/assessmentInterRAISenteHistroyInitSelect/handler'
import HistorySelectAssessmentIncentivesItemInterRAIInitSelect from '@/mocks/api/cmn/select/pages/historySelectAssessmentIncentivesItemInterRAIInitSelect/handler'
import PlanMoniHistoryInfoSelectEntity from '@/mocks/api/cmn/select/pages/planMoniHistoryInfoSelect/handler'
import planImplementationDuplicateChangeSelec from '@/mocks/api/cmn/select/pages/planImplementationDuplicateChangeSelec/handler'
import PreventionEvaluationTableMasterInitEntity from '@/mocks/api/cmn/select/pages/preventionEvaluationTableMasterInitSelect/handler'
import evaluationTableImplementationPlanImportSelect from '@/mocks/api/cmn/select/pages/evaluationTableImplementationPlanImportSelect/handler'
import OfficeNmInfoSelect from '@/mocks/api/cmn/select/pages/officeNmInfoSelect/handler'
import IntegratedPlanMasterInitSelect from '@/mocks/api/cmn/select/pages/integratedPlanMasterInitSelect/handler'
import ImplementationPlanMstSelect from '@/mocks/api/cmn/select/pages/implementationPlanMstSelect/handler'
import IssuesAnalysisPrintSettingsDetailListHistorySelect from '@/mocks/api/cmn/select/pages/issuesAnalysisPrintSettingsDetailListHistorySelect/handler'
import IssuesAnalysisPrintSettingsListHistorySelect from '@/mocks/api/cmn/select/pages/issuesAnalysisPrintSettingsListHistorySelect/handler'
import Steps1Select from '@/mocks/api/cmn/select/pages/steps1Select/handler'
import Steps2Select from '@/mocks/api/cmn/select/pages/steps2Select/handler'
import Steps3Select from '@/mocks/api/cmn/select/pages/steps3Select/handler'
import Steps4Select from '@/mocks/api/cmn/select/pages/steps4Select/handler'
import StandardCarePlanSelect from '@/mocks/api/cmn/select/pages/standardCarePlanSelect/handler'
import CertificationInfoInputScreenSelect from '@/mocks/api/cmn/select/pages/certificationInfoInputScreenSelect/handler'
import GaibuItakuryouSelect from '@/mocks/api/cmn/select/pages/gaibuItakuryouSelect/handler'
import WeekPlanEstimateDisplaySelect from '@/mocks/api/cmn/select/pages/weekPlanEstimateDisplaySelect/handler'
import PatternTitleInfoSelect from '@/mocks/api/cmn/select/pages/patternTitleInfoSelect/handler'
import PatternGroupInfoSelect from '@/mocks/api/cmn/select/pages/patternGroupInfoSelect/handler'
import CsvFileExportSelect from '@/mocks/api/cmn/select/pages/csvFileExportSelect/handler'
import CsvExportInitInfoSelect from '@/mocks/api/cmn/select/pages/csvExportInitInfoSelect/handler'
import CheckRirekListInitInfoSelect from '@/mocks/api/cmn/select/pages/checkRirekListInitInfoSelect/handler'
import ConsiderTableInterRAIPrintSettingsHistorySelect from '@/mocks/api/cmn/select/pages/considerTableInterRAIPrintSettingsHistorySelect/handler'
import ShousaiInfoSelect from '@/mocks/api/cmn/select/pages/shousaiInfoSelect/handler'
import DiffcultyDegreeInputSupportInfoSelect from '@/mocks/api/cmn/select/pages/diffcultyDegreeInputSupportInfoSelect/handler'
import evaluationTableNewSelect from '@/mocks/api/cmn/select/pages/evaluationTableNewSelect/handler'
import AssessmentInterRAILSelect from '~/mocks/api/cmn/select/pages/assessmentInterRAILSelect/handler'
import EvaluationSelect from '@/mocks/api/cmn/select/pages/evaluationSelect/handler'
import CommonServiceMasterInitSelect from '@/mocks/api/cmn/select/pages/commonServiceMasterInitSelect/handler'
import IssuesShortTermGoalImportSelect from '@/mocks/api/cmn/select/pages/issuesShortTermGoalImportSelect/handler'
import ServiceSelect from '@/mocks/api/cmn/select/pages/serviceSelect/handler'
import AssessmentInterRAIUInitSelect from '@/mocks/api/cmn/select/pages/assessmentInterRAIUInitSelect/handler'
import assessmentHomeTab63Select from '@/mocks/api/cmn/select/pages/assessmentHomeTab63Select/handler'
import ScheduleMasterSelect from '@/mocks/api/cmn/select/pages/scheduleMasterSelect/handler'
import evaluationTableMasterInfoSelect from '@/mocks/api/cmn/select/pages/evaluationTableMasterInfoSelect/handler'
import MonthlyYearlyTablePtnTitleCntSelect from '@/mocks/api/cmn/select/pages/monthlyYearlyTablePtnTitleCntSelect/handler'
import AssessmentInterRAINSelect from '~/mocks/api/cmn/select/pages/assessmentInterRAINSelect/handler'
import assessmentInterRAINInitSelect from '~/mocks/api/cmn/select/pages/assessmentInterRAINInitSelect/handler'
import AssessmentDiagnosSelect from '~/mocks/api/cmn/select/pages/assessmentDiagnosSelect/handler'
import AssessmentInterRAIMSelect from '@/mocks/api/cmn/select/pages/assessmentInterRAIMSelect/handler'
import EvaluationInfoSelect from '@/mocks/api/cmn/select/pages/evaluationInfoSelect/handler'
import ImplementationPlan1Select from '@/mocks/api/cmn/select/pages/implementationPlan1Select/handler'
import ImplementationPlan2Select from '@/mocks/api/cmn/select/pages/implementationPlan2Select/handler'
import KikanNaiRirekiSelect from '@/mocks/api/cmn/select/pages/kikanNaiRirekiSelect/handler'
import AssessmentHomeTab66Select from '~/mocks/api/cmn/select/pages/assessmentHomeTab66Select/handler'
import ConcreteContentsCorrespondenceCareSelect from '~/mocks/api/cmn/select/pages/concreteContentsCorrespondenceCareSelect/handler'
import evaluationTableDuplicateHistorySelect from '~/mocks/api/cmn/select/pages/evaluationTableDuplicateHistorySelect/handler'
import AssessmentMemoInitSelect from '@/mocks/api/cmn/select/pages/assessmentMemoInitSelect/handler'
import PloblemListImportSelect from '@/mocks/api/cmn/select/pages/ploblemListImportSelect/handler'
import ServiceKbnInputSupportSelect from '@/mocks/api/cmn/select/pages/serviceKbnInputSupportSelect/handler'
import OfficeAuthorInfoSelect from '@/mocks/api/cmn/select/pages/officeAuthorInfoSelect/handler'
import InputSupportCareManagerSelect from '@/mocks/api/cmn/select/pages/inputSupportCareManagerSelect/handler'
import careProvisionMasterSelect from '@/mocks/api/cmn/select/pages/careProvisionMasterSelect/handler'
import UserList from '@/mocks/api/cmn/select/user-list/handler'
import ShokuinList from '@/mocks/api/cmn/select/shokuin-list/handler'
import CareProvisionLocationMasterSelect from '@/mocks/api/cmn/select/pages/careProvisionLocationMasterSelect/handler'
import WeekPlanInitialInfoSelect from '@/mocks/api/cmn/select/pages/weekPlanInitialInfoSelect/handler'
import WeekPlanInvWithinSelect from '@/mocks/api/cmn/select/pages/weekPlanInvWithinSelect/handler'
import WeekPlanHistorySelect from '@/mocks/api/cmn/select/pages/weekPlanHistorySelect/handler'
import WeekPlanDetailsListSelect from '@/mocks/api/cmn/select/pages/weekPlanDetailsListSelect/handler'
import InfoCollectionInitSelect from '@/mocks/api/cmn/select/pages/infoCollectionInitSelect/handler'
import InfoCollectionDrugSelect from '@/mocks/api/cmn/select/pages/infoCollectionDrugSelect/handler'
import InfoCollectionHistorySelect from '@/mocks/api/cmn/select/pages/infoCollectionHistorySelect/handler'
import InfoCollection1InitSelect from '@/mocks/api/cmn/select/pages/infoCollection1InitSelect/handler'
import ImplementationPlanTwoMasterInitSelect from '@/mocks/api/cmn/select/pages/implementPlanTwoMasterInitSelect/handler'
import copyInfoFetchSelect from '@/mocks/api/cmn/select/pages/copyInfoFetchSelect/handler'
import assessmentHomeTab4Select from '@/mocks/api/cmn/select/pages/assessmentHomeTab4Select/handler'
import GetDailyScheduleSelect from '@/mocks/api/cmn/select/pages/getDailyScheduleSelect/handler'
import SDCareMasterEasyRegistSelect from '@/mocks/api/cmn/select/pages/SDCareMasterEasyRegistSelect/handler'
import weekPlanPatternSettingsInitSelect from '@/mocks/api/cmn/select/pages/weekPlanPatternSettingsInitSelect/handler'
import DailyscheduleImportInitSelect from '@/mocks/api/cmn/select/pages/dailyscheduleImportInitSelect/handler'
import HistoryInformationtDailySelect from '@/mocks/api/cmn/select/pages/historyInformationtDailySelect/handler'
import UserManagementOrLedgerSelect from '@/mocks/api/cmn/select/pages/userManagementOrLedgerSelect/handler'
import NotebookInfoSelect from '@/mocks/api/cmn/select/pages/notebookInfoSelect/handler'
import SurveySlipSelect from '@/mocks/api/cmn/select/pages/surveySlipSelect/handler'
import AttendingPhysicianSelect from '@/mocks/api/cmn/select/pages/attendingPhysicianSelect/handler'
import DisplayOrderModifiedAssessmentSelect from '@/mocks/api/cmn/select/pages/displayOrderModifiedAssessmentSelect/handler'
import SDCareMasterMaintenanceSelect from '@/mocks/api/cmn/select/pages/SDCareMasterMaintenanceSelect/handler'
import FamilyMasterSelect from '@/mocks/api/cmn/select/pages/familyMasterSelect/handler'
import MonthlyImportSelect from '@/mocks/api/cmn/select/pages/monthlyImportSelect/handler'
import summaryFlgSelect from '@/mocks/api/cmn/select/pages/summaryFlgSelect/handler'
import Careplan2ImportSelect from '@/mocks/api/cmn/select/pages/careplan2ImportSelect/handler'
import careCheckMasterSelect from '@/mocks/api/cmn/select/pages/careCheckMasterSelect/handler'
import KigoImiSelectEntity from '@/mocks/api/cmn/select/pages/KigoImiSelectEntity/handler'
import consentFieldEditInitSelect from '@/mocks/api/cmn/select/pages/consentFieldEditInitSelect/handler'
import FaceSheetPackage2InitSelectEntity from '@/mocks/api/cmn/select/pages/faceSheetPackage2InitSelect/handler'
import historySelectPagePreventionBaseSelect from '~/mocks/api/cmn/select/pages/historySelectPagePreventionBaseSelect/handler'
import ServiceTypeInfoSelect from '@/mocks/api/cmn/select/pages/serviceTypeInfoSelect/handler'
import selectionTableAssessmentInterraiSelect from '@/mocks/api/cmn/select/pages/selectionTableAssessmentInterrai/handler'
import specialNoteMatterSelect from '@/mocks/api/cmn/select/pages/specialNoteMatterSelect/handler'
import MedicationSearchMasterSelect from '~/mocks/api/cmn/select/pages/medicationSearchMasterSelect/handler'
import MedicationMasterSelect from '@/mocks/api/cmn/select/pages/medicationMasterSelect/handler'
import freeAssessmentCheckMasterSelect from '@/mocks/api/cmn/select/pages/freeAssessmentCheckMasterSelect/handler'
import freeAssessmentCheckMasterDataSelect from '@/mocks/api/cmn/select/pages/freeAssessmentCheckMasterDataSelect/handler'
import FreeAssessmentIssuesPlanningDuplicateSelect from '~/mocks/api/cmn/select/pages/freeAssessmentIssuesPlanningDuplicateSelect/handler'
import FreeAssessmentIssuesPlanningHistoryChangeSelect from '~/mocks/api/cmn/select/pages/freeAssessmentIssuesPlanningHistoryChangeSelect/handler'
import FreeAssessmentIssuesPlanningInitialSelect from '~/mocks/api/cmn/select/pages/freeAssessmentIssuesPlanningInitialSelect/handler'
import FreeAssessmentIssuesPlanningNewSelect from '~/mocks/api/cmn/select/pages/freeAssessmentIssuesPlanningNewSelect/handler'
import FreeAssessmentIssuesPlanningPeriodChangeSelect from '~/mocks/api/cmn/select/pages/freeAssessmentIssuesPlanningPeriodChangeSelect/handler'
import CarePlanDuplicateCp2Select from '@/mocks/api/cmn/select/pages/carePlanDuplicateCp2Select/handler'
import BaseCheckListHistorySelect from '@/mocks/api/cmn/select/pages/baseCheckListHistorySelect/handler'
import InsuranceSelect from '@/mocks/api/cmn/select/pages/insuranceSelect/handler'
import IssueConsiderationItem from '@/mocks/api/cmn/select/pages/issueConsiderationItem/handler'
import ReviewTableCopyHistoryInfoSelect from '@/mocks/api/cmn/select/pages/reviewTableCopyHistoryInfoSelect/handler'
import IssueReviewCopyHistorySelect from '@/mocks/api/cmn/select/pages/issueReviewCopyHistorySelect/handler'
import DisplayOrderSettingSelect from '@/mocks/api/cmn/select/pages/displayOrderSettingSelect/handler'
import ConsultationUserInitSelect from '@/mocks/api/cmn/select/pages/consultationUserInitSelect/handler'
import StaffInputAssistantSelect from '@/mocks/api/cmn/select/pages/staffInputAssistantSelect/handler'
import ImplementationPlan1CopyReturnSelect from '@/mocks/api/cmn/select/pages/implementationPlan1CopyReturnSelect/handler'
import ImplementationPlan2CopyReturnSelect from '@/mocks/api/cmn/select/pages/implementationPlan2CopyReturnSelect/handler'
import ImplementationPlan2CopySelect from '@/mocks/api/cmn/select/pages/implementationPlan2CopySelect/handler'
import PreventionPlanSelect from '@/mocks/api/cmn/select/pages/preventionPlanSelect/handler'
import LevelOfCareRequiredModifiedUserListSelect from '@/mocks/api/cmn/select/pages/levelOfCareRequiredModifiedUserListSelect/handler'
import PreventionPlanCopyReturnSelect from '@/mocks/api/cmn/select/pages/preventionPlanCopyReturnSelect/handler'
import PreventionPlanCopyInitSelect from '@/mocks/api/cmn/select/pages/preventionPlanCopyInitSelect/handler'
import AssessmentInterRAIAInitSelect from '@/mocks/api/cmn/select/pages/assessmentInterRAIAInitSelect/handler'
import AssessmentInterRAIANewSelect from '@/mocks/api/cmn/select/pages/assessmentInterRAIANewSelect/handler'
import AssessmentInterRAIATabSelect from '@/mocks/api/cmn/select/pages/assessmentInterRAIATabSelect/handler'
import AssessmentInterRAIAPeriodChangeSelect from '@/mocks/api/cmn/select/pages/assessmentInterRAIAPeriodChangeSelect/handler'
import IncentivesItemPlanPeriodselect from '@/mocks/api/cmn/select/pages/incentivesItemPlanPeriodselect/handler'
import CorrespondingTableAssessmentInterrai from '@/mocks/api/cmn/select/pages/correspondingTableAssessmentInterrai/handler'
import IncentivesItemHistoryselect from '@/mocks/api/cmn/select/pages/incentivesItemHistoryselect/handler'
import IncentivesItemDetailselect from '@/mocks/api/cmn/select/pages/incentivesItemDetailselect/handler'
import AssessmentInterRAIJInitSelect from '@/mocks/api/cmn/select/pages/assessmentInterRAIJInitSelect/handler'
import prevPlanCopySourceSelect from '~/mocks/api/cmn/select/pages/prevPlanCopySourceSelect/handler'
import prevPlanCopyHistorySelect from '~/mocks/api/cmn/select/pages/prevPlanCopyHistorySelect/handler'
import Or00251TopHeaderInfo from '@/mocks/api/cmn/select/pages/header/topHeaderInfo/handler'
import Or00251NoticeCount from '@/mocks/api/cmn/select/pages/header/noticeCount/handler'
import EvaluationTablePrintSettingsSubjectSelect from '@/mocks/api/cmn/select/pages/evaluationTablePrintSettingsSubjectSelect/handler'
import AssessmentInterRAITSelect from '@/mocks/api/cmn/select/pages/assessmentInterRAITSelect/handler'
import assessmentResidentialHistoryInfoSelect from '@/mocks/api/cmn/select/pages/assessmentResidentialHistoryInfoSelect/handler'
import preventionCarePlanMasterInitSelect from '@/mocks/api/cmn/select/pages/preventionCarePlanMasterInitSelect/handler'
import assessmentComprehensiveMealIPeriodChangeSelect from '@/mocks/api/cmn/select/pages/assessmentComprehensiveMealIPeriodChangeSelect/handler'
import serviceTypeInputSupoortSelect from '@/mocks/api/cmn/select/pages/serviceTypeInputSupoortSelect/handler'
import assessmentConsolidationImportInfoSelect from '@/mocks/api/cmn/select/pages/assessmentConsolidationImportInfoSelect/handler'
import IssuesPlanningCategoryMasterInitSelect from '@/mocks/api/cmn/select/pages/issuesPlanningCategoryMasterInitSelect/handler'
import CmPreventionSentenceMasterSelectService from '@/mocks/api/cmn/select/pages/cmPreventionSentenceMasterSelectService/handler'
import assessmentComprehensiveMealIHistoryChangeSelect from '@/mocks/api/cmn/select/pages/assessmentComprehensiveMealIHistoryChangeSelect/handler'
import RegularMedicineInfoSelect from '@/mocks/api/cmn/select/pages/regularMedicineInfoSelect/handler'
import ExecutionConfirmationMasterInitSelect from '@/mocks/api/cmn/select/pages/executionConfirmationMasterInitSelect/handler'
import AffectedAreaImportSettingSelect from '@/mocks/api/cmn/select/pages/affectedAreaImportSettingSelect/handler'
import ItiranInfoInitSelect from '@/mocks/api/cmn/select/pages/itiranInfoInitSelect/handler'
import choosingDoctorSelect from '@/mocks/api/cmn/select/pages/choosingDoctorSelect/handler'
import DailyTaskTableMasterInitSelect from '@/mocks/api/cmn/select/pages/dailyTaskTableMasterInitSelect/handler'
import DailyTaskTablePrintSettingsUserChangeSelect from '@/mocks/api/cmn/select/pages/dailyTaskTablePrintSettingsUserChangeSelect/handler'
import DailyTaskTablePrintSettingsSubjectSelect from '@/mocks/api/cmn/select/pages/dailyTaskTablePrintSettingsSubjectSelect/handler'
import AssessmentIncludeBathingSelect from '~/mocks/api/cmn/select/pages/assessmentIncludeBathingSelect/handler'
import assessmentComprehensiveExcretionInitSelect from '~/mocks/api/cmn/select/pages/assessmentComprehensiveExcretionInitSelect/handler'
import ItakuryouKikanItemSelect from '@/mocks/api/cmn/select/pages/itakuryouKikanItemSelect/handler'
import HistorySelectScreenAssessmentPackageSelect from '@/mocks/api/cmn/select/pages/historySelectScreenAssessmentPackageSelect/handler'
import BaseCheckListHistoryChooseSelect from '@/mocks/api/cmn/select/pages/baseCheckListHistoryChooseSelect/handler'
import YuSenJunISelect from '@/mocks/api/cmn/select/pages/yuSenJunISelect/handler'
import OpinionMasterSelect from '@/mocks/api/cmn/select/pages/opinionMasterSelect/handler'
import RelationshipTypeNameSelect from '@/mocks/api/cmn/select/pages/relationshipTypeNameSelect/handler'
import RelativesRelatedPersonSelect from '@/mocks/api/cmn/select/pages/relativesRelatedPersonSelect/handler'
import AssessmentComprehensiveBaseInitSelect from '@/mocks/api/cmn/select/pages/assessmentComprehensiveBaseInitSelect/handler'
import SituationMasterSelect from '@/mocks/api/cmn/select/pages/situationMasterSelect/handler'
import SupportImplentationSelect from '@/mocks/api/cmn/select/pages/supportImplentationSelect/handler'
import BasicSituationImportInitSelect from '@/mocks/api/cmn/select/pages/basicSituationImportInitSelect/handler'
import problemDotSolutionEtcInfoSelect from '@/mocks/api/cmn/select/pages/problemDotSolutionEtcInfoSelect/handler'
import ServiceValidPeriodInfoSelect from '@/mocks/api/cmn/select/pages/serviceValidPeriodInfoSelect/handler'
import AssessmentComprehensiveWashroomInitSelect from '@/mocks/api/cmn/select/pages/assessmentComprehensiveWashroomInitSelect/handler'
import AssessmentComprehensiveBathingInitSelect from '@/mocks/api/cmn/select/pages/assessmentComprehensiveBathingInitSelect/handler'
import assessmentComprehensivePsychologyInitSelect from '@/mocks/api/cmn/select/pages/assessmentComprehensivePsychologyInitSelect/handler'
import AssessmentComprehensiveMedicalCareInitSelect from '@/mocks/api/cmn/select/pages//assessmentComprehensiveMedicalCareInitSelect/handler'
import StyleSelectSelect from '@/mocks/api/cmn/select/pages/styleSelectSelect/handler'
import MeetingInquiryContentsHistoryInfoSelect from '@/mocks/api/cmn/select/pages/meetingInquiryContentsHistoryInfoSelect/handler'
import InquiryContentsHistSelectInfoKaigi from '@/mocks/api/cmn/select/pages/inquiryContentsHistSelectInfoKaigi/handler'
import summaryTableInfoSelectService from '@/mocks/api/cmn/select/pages/summaryTableInfoSelectService/handler'
import PlanTableImportInitSelect from '@/mocks/api/cmn/select/pages/planTableImportInitSelect/handler'
import PlanImplementationDuplicateInitSelect from '@/mocks/api/cmn/select/pages/planImplementationDuplicateInitSelect/handler'
import ConfirmationMethodMasterInitSelect from '@/mocks/api/cmn/select/pages/confirmationMethodMasterInitSelect/handler'
import assessmentComprehensiveMealInitSelect from '@/mocks/api/cmn/select/pages/assessmentComprehensiveMealInitSelect/handler'
import certificationSurveyHistoryInfoSelect from '@/mocks/api/cmn/select/pages/certificationSurveyHistoryInfoSelect/handler'
import certificationSurveyHistInfoSelect from '@/mocks/api/cmn/select/pages/certificationSurveyHistInfoSelect/handler'
import implementPlanOneHistoryInformationSelect from '@/mocks/api/cmn/select/pages/implementPlanOneHistoryInformationSelect/handler'
import kentouhyoInfoSelect from '@/mocks/api/cmn/select/pages/kentouhyoInfoSelect/handler'
import kentouhyoCommonInfoSelect from '@/mocks/api/cmn/select/pages/kentouhyoCommonInfoSelect/handler'
import ImplementationPlan3Select from '@/mocks/api/cmn/select/pages/implementationPlan3Select/handler'
import WeekTablePatternInitSelect from '@/mocks/api/cmn/select/pages/weekTablePatternInitSelect/handler'
import WeekTablePatternInfoSelect from '@/mocks/api/cmn/select/pages/weekTablePatternInfoSelect/handler'
import BasicChecklistInitSelect from '@/mocks/api/cmn/select/pages/basicChecklistInitSelect/handler'
import SatisfactionLevelMasterSelect from '@/mocks/api/cmn/select/pages/satisfactionLevelMasterSelect/handler'
import InfoCollectionInitTenSelect from '@/mocks/api/cmn/select/pages/infoCollectionInitTenSelect/handler'
import InfoCollectionPlanPeriodSelect from '@/mocks/api/cmn/select/pages/infoCollectionPlanPeriodSelect/handler'
import OrMedicalHistorySelect from '@/mocks/api/cmn/select/pages/orMedicalHistorySelect/handler'
import ConsiderTablePrintSettingsHistorySelect from '@/mocks/api/cmn/select/pages/considerTablePrintSettingsHistorySelect/handler'
import FrameWidthModifiedSelect from '@/mocks/api/cmn/select/pages/frameWidthModifiedSelect/handler'
import FrameWidthModifiedMasterSelect from '@/mocks/api/cmn/select/pages/frameWidthModifiedMasterSelect/handler'
import relativesAndAssociatesSelect from '@/mocks/api/cmn/select/pages/relativesAndAssociatesSelect/handler'
import FCpnMucGdlKoumokuSelect from '@/mocks/api/cmn/select/pages/fCpnMucGdlKoumokuSelect/handler'
import CorrespondenceMasterInitSelect from '@/mocks/api/cmn/select/pages/correspondenceMasterInitSelect/handler'
import staffSearchSelect from '@/mocks/api/cmn/select/pages/staffSearchSelect/handler'
import FacilitySelectInfoSelect from '@/mocks/api/cmn/select/pages/facilitySelectInfoSelect/handler'
import SettingsCmnSelect from '@/mocks/api/cmn/select/login/handler'
import PreventionBasicPeriodSelect from '@/mocks/api/cmn/select/pages/preventionBasicPeriodSelect/handler'
import PreventionBasicInitialInfoSelect from '@/mocks/api/cmn/select/pages/preventionBasicInitialInfoSelect/handler'
import PreventionBasicHistorySelect from '@/mocks/api/cmn/select/pages/preventionBasicHistorySelect/handler'
import PreventionBasicDetailInfoSelect from '@/mocks/api/cmn/select/pages/preventionBasicDetailInfoSelect/handler'
import PeriodSelect from '@/mocks/api/cmn/select/pages/periodSelect/handler'
import staffFrequencyInfoSelect from '@/mocks/api/cmn/select/pages/staffFrequencyInfoSelect/handler'
import InfoCollectionScreen7InitSelect from '@/mocks/api/cmn/select/pages/infoCollectionScreen7InitSelect/handler'
import MonitoringMasterInitInfoSelect from '@/mocks/api/cmn/select/pages/monitoringMasterInitInfoSelect/handler'
import AttendingPhysicianOpinionHistoryInitialInfoSelect from '@/mocks/api/cmn/select/pages/attendingPhysicianOpinionHistoryInitialInfoSelect/handler'
import useSlipInitInfoSelect from '@/mocks/api/cmn/select/pages/useSlipInitInfoSelect/handler'
import InfoCollection13InitSelect from '@/mocks/api/cmn/select/pages/infoCollection13InitSelect/handler'
import investigatorListInfoSelect from '@/mocks/api/cmn/select/pages/investigatorListInfoSelect/handler'
import historySelectAssessmentComprehensiveSelect from '@/mocks/api/cmn/select/pages/historySelectAssessmentComprehensiveSelect/handler'
import implementationMonitoringRegistInitNewSelect from '@/mocks/api/cmn/select/pages/implementationMonitoringRegistInitNewSelect/handler'
import PreventionCarePlanImportSelect from '@/mocks/api/cmn/select/pages/preventionCarePlanImportSelect/handler'
import PreventionCarePlanImportReturnInfoSelect from '@/mocks/api/cmn/select/pages/preventionCarePlanImportReturnInfoSelect/handler'
import IncentivesItemCorrespondingTableSelect from '@/mocks/api/cmn/select/pages/incentivesItemCorrespondingTableSelect/handler'
import preventionEvaluationTableTitleMasterInitSelect from '@/mocks/api/cmn/select/pages/preventionEvaluationTableTitleMasterInitSelect/handler'
import monitoringTitleMasterSelect from '@/mocks/api/cmn/select/pages/monitoringTitleMasterSelect/handler'

import PreventionEvaluationTableHistInfoSelect from '@/mocks/api/cmn/select/pages/preventionEvaluationTableHistInfoSelect/handler'
import evaluationTablePlanPeriodSelect from '@/mocks/api/cmn/select/pages/evaluationTablePlanPeriodSelect/handler'

import InputSupportGoalAndLife1YearScreenAcquireSelect from '@/mocks/api/cmn/select/pages/inputSupportGoalAndLife1YearScreenAcquireSelect/handler'
import InputSupportGoalAndLife1YearScreenAcquireRedisplaySelect from '@/mocks/api/cmn/select/pages/inputSupportGoalAndLife1YearScreenAcquireRedisplaySelect/handler'
import InputSupportGoalAndLife1YearContentSelect from '@/mocks/api/cmn/select/pages/inputSupportGoalAndLife1YearContentSelect/handler'
import FaceSheetPackage1PlanningPeriodSelect from '~/mocks/api/cmn/select/pages/FaceSheetPackage1PlanningPeriodSelect/handler'
import FaceSheetPackage1InitSelect from '~/mocks/api/cmn/select/pages/FaceSheetPackage1InitSelect/handler'
import FaceSheetPackage1HistorySelect from '~/mocks/api/cmn/select/pages/FaceSheetPackage1HistorySelect/handler'
import FaceSheetPackage1PreviousAddressSelect from '~/mocks/api/cmn/select/pages/FaceSheetPackage1PreviousAddressSelect/handler'
import faceSheet3Select from '~/mocks/api/cmn/select/pages/faceSheet3Select/handler'
import faceSheetCopyKikanSelect from '~/mocks/api/cmn/select/pages/faceSheetCopyKikanSelect/handler'
import faceSheetCopyHistorySelect from '~/mocks/api/cmn/select/pages/faceSheetCopyHistorySelect/handler'
import MaskMastSelect from '@/mocks/api/cmn/select/pages/maskMastSelect/handler'
import MonitoringDisplayOrderSettingSelect from '@/mocks/api/cmn/select/pages/monitoringDisplayOrderSettingSelect/handler'
import basicSurvey1InitSelect from '@/mocks/api/cmn/select/pages/basicSurvey1InitSelect/handler'
import basicSurvey2InitSelect from '@/mocks/api/cmn/select/pages/basicSurvey2InitSelect/handler'
import basicSurvey5InitSelect from '@/mocks/api/cmn/select/pages/basicSurvey5InitSelect/handler'
import bedriddenDementiaRankInfoSelect from '@/mocks/api/cmn/select/pages/bedriddenDementiaRankInfoSelect/handler'
import doctorOpinionInfoSelect from '@/mocks/api/cmn/select/pages/doctorOpinionInfoSelect/handler'
import ImplementationPlan3CopyReturnSelect from '@/mocks/api/cmn/select/pages/implementationPlan3CopyReturnSelect/handler'
import specialnoteMatterInitialSelect from '@/mocks/api/cmn/select/pages/specialnoteMatterInitialSelect/handler'
import faceSheetPrintSettingsSelect from '@/mocks/api/cmn/select/pages/faceSheetPrintSettingsSelect/handler'
import basicSurvey3InitialInfoSelect from '@/mocks/api/cmn/select/pages/basicSurvey3InitialInfoSelect/handler'
import basicSurvey4Select from '@/mocks/api/cmn/select/pages/basicSurvey4Select/handler'
import ImplementationMonitoringMasterSelect from '@/mocks/api/cmn/select/pages/implementationMonitoringMasterSelect/handler'
import weeklyScheduleInputSelect from '@/mocks/api/cmn/select/pages/weeklyScheduleInputSelect/handler'
import weekPlanBundleImportInfoSelect from '@/mocks/api/cmn/select/pages/weekPlanBundleImportInfoSelect/handler'

import NecessaryBusinessProgramsSelect from '@/mocks/api/cmn/select/pages/necessaryBusinessProgramsSelect/handler'
import InterestCheckSheetHistorySelect from '@/mocks/api/cmn/select/pages/orInterestCheckSheetHistorySelect/handler'
import OrCarePreventionServiceSupportPlanTableHeaderHistoryInfoSelect from '@/mocks/api/cmn/select/pages/orCarePreventionServiceSupportPlanTableHeaderHistoryInfoSelect/handler'
import PrevPlanCopyHistorySelect from '@/mocks/api/cmn/select/pages/prevPlanCopyHistorySelect/handler'
import mainInsuranceSelect from '@/mocks/api/cmn/select/pages/mainInsuranceSelect/handler'
import PrevPlanCopyDetailHistorySelect from '@/mocks/api/cmn/select/pages/prevPlanCopyDetailHistorySelect/handler'
import assessmentInterRAIKentoHistroyInitSelect from '@/mocks/api/cmn/select/pages/assessmentInterRAIKentoHistroyInitSelect/handler'
import KentouyousiInfoSelect from '@/mocks/api/cmn/select/pages/kentouyousiInfoSelect/handler'
import assessmentKentoComprehensiveInitSelect from '@/mocks/api/cmn/select/pages/assessmentKentoComprehensiveInitSelect/handler'
import historySelectScreenAssessmentInformationGatheringSelect from '@/mocks/api/cmn/select/pages/historySelectScreenAssessmentInformationGatheringSelect/handler'
import CpnTucRaiAssDReportSelect from '@/mocks/api/cmn/select/pages/cpnTucRaiAssDReportSelect/handler'
import HistorySelectIssuesPlanningInitSelect from '@/mocks/api/cmn/select/pages/historySelectIssuesPlanningInitSelect/handler'
import dischargeLeavingHistoryInitInfoSelect from '@/mocks/api/cmn/select/pages/dischargeLeavingHistoryInitInfoSelect/handler'
import evaluationTableDuplicateSelect from '@/mocks/api/cmn/select/pages/evaluationTableDuplicateSelect/handler'

import AdmissionLeavingUseInfoSelect from '@/mocks/api/cmn/select/pages/admissionLeavingUseInfoSelect/handler'
import PriorityOrderSelect from '@/mocks/api/cmn/select/pages/priorityOrderSelect/handler'
import InquiryContentsDuplicateInitijalInfoAcquisitionSelect from '@/mocks/api/cmn/select/pages/inquiryContentsDuplicateInitijalInfoAcquisitionSelect/handler'
import WeekTableSelect from '@/mocks/api/cmn/select/pages/weekTableSelect/handler'
import PlanTransferInitialInfoSelect from '@/mocks/api/cmn/select/pages/planTransferInitialInfoSelect/handler'
import PlanTransferListInfoSelect from '@/mocks/api/cmn/select/pages/planTransferListInfoSelect/handler'
import faceHistoryInitInfoSelect from '@/mocks/api/cmn/select/pages/faceHistoryInitInfoSelect/handler'
import ItemSelectInitInfoSelect from '@/mocks/api/cmn/select/pages/itemSelectInitInfoSelect/handler'
import CheckItemIssuesPlanningDuplicateInfoSelect from '@/mocks/api/cmn/select/pages/checkItemIssuesPlanningDuplicateInfoSelect/handler'
import DuplicateInfoSelect from '@/mocks/api/cmn/select/pages/duplicateInfoSelect/handler'
import InhibitoryFactorInfoSelect from '@/mocks/api/cmn/select/pages/inhibitoryFactorInfoSelect/handler'
import RirekiSentakuKadaitoukatuSelect from '@/mocks/api/cmn/select/pages/rirekiSentakuKadaitoukatuSelect/handler'
import evaluationTableInitInfoSelect from '@/mocks/api/cmn/select/pages/evaluationTableInitInfoSelect/handler'
import HistorySelectScreenAssessmentIssueExaminationSelect from '@/mocks/api/cmn/select/pages/historySelectScreenAssessmentIssueExaminationSelect/handler'
import lumpUnitOfWelfareEquipmentInitSelect from '@/mocks/api/cmn/select/pages/lumpUnitOfWelfareEquipmentInitSelect/handler'
import EvaluationTableHistoryInitInfoSelect from '@/mocks/api/cmn/select/pages/evaluationTableHistoryInitInfoSelect/handler'
import HistoryInfoSelect from '@/mocks/api/cmn/select/pages/historyInfoSelect/handler'
import KghMocCheckSelect from '@/mocks/api/cmn/select/pages/kghMocCheckSelect/handler'
import KghMocCheckTreeSelect from '@/mocks/api/cmn/select/pages/kghMocCheckTreeSelect/handler'
import staffSearchInitSelect from '@/mocks/api/cmn/select/pages/staffSearchInitSelect/handler'
import staffSearchDateChange from '@/mocks/api/cmn/select/pages/staffSearchDateChange/handler'
import staffSearchOffice from '@/mocks/api/cmn/select/pages/staffSearchOffice/handler'
import staffSearchRelated from '@/mocks/api/cmn/select/pages/staffSearchRelated/handler'
import FreeAssessmentSheetDisplaySettingsTabChangedSelect from '@/mocks/api/cmn/select/pages/freeAssessmentSheetDisplaySettingsTabChangedSelect/handler'
import InterestAndConcernInitialSelect from '@/mocks/api/cmn/select/pages/interestAndConcernInitialSelect/handler'
import IssuesAnalysisInitSelect from '@/mocks/api/cmn/select/pages/issuesAnalysisInitSelect/handler'
import IssuesAnalysisDetailSelect from '@/mocks/api/cmn/select/pages/issuesAnalysisDetailSelect/handler'
import relatedOfficeMstInfoSelect from '@/mocks/api/cmn/select/pages/relatedOfficeMstInfoSelect/handler'
import InquiryContentsInitialInfoAcquisition from '@/mocks/api/cmn/select/pages/inquiryContentsInitialInfoAcquisitionSelect/handler'
import InquiryContentsDuplicateInfoAcquisition from '@/mocks/api/cmn/select/pages/inquiryContentsDuplicateInfoAcquisitionSelect/handler'
import InquiryContentsHistSelectInfoAcquisition from '@/mocks/api/cmn/select/pages/inquiryContentsHistSelectInfoAcquisitionSelect/handler'
import supportElapsedRecordMasterSelectService from '@/mocks/api/cmn/select/pages/supportElapsedRecordMasterSelect/handler'
import WeekPlanInitListInfoSelect from '@/mocks/api/cmn/select/pages/weekPlanInitListInfoSelect/handler'
import WeekPlanServiceListInfoSelect from '@/mocks/api/cmn/select/pages/weekPlanServiceListInfoSelect/handler'
import WeekPlanUsingSlipListCntSelect from '@/mocks/api/cmn/select/pages/weekPlanUsingSlipListCntSelect/handler'
import WeekPlanUsingSlipImportSelect from '@/mocks/api/cmn/select/pages/weekPlanUsingSlipImportSelect/handler'
import PassageManagementSelect from '@/mocks/api/cmn/select/pages/passageManagementSelect/handler'
import HospitalizationTimeInfoOfferSetingMasterSelect from '@/mocks/api/cmn/select/pages/hospitalizationTimeInfoOfferSetingMasterSelect/handler'
import issuesPlanningStyleTitleMasterInitSelect from '@/mocks/api/cmn/select/pages/issuesPlanningStyleTitleMasterInitSelect/handler'
import WeekTableHistorySelect from '@/mocks/api/cmn/select/pages/weekTablePeriodSelect/handler'
import WeekTablePeriodSelect from '@/mocks/api/cmn/select/pages/weekTablePeriodSelect/handler'
import LastBasicSurveyHistoryRevisionInfoSelect from '@/mocks/api/cmn/select/pages/lastBasicSurveyHistoryRevisionInfoSelect/handler'
import supportElapsedKindMasterSelect from '@/mocks/api/cmn/select/pages/supportElapsedKindMasterSelect/handler'
import DailyScheduleImageInitSelect from '@/mocks/api/cmn/select/pages/dailyScheduleImageInitSelect/handler'
import generalSituationSurveyPlanningPeriodInfoSelect from '@/mocks/api/cmn/select/pages/generalSituationSurveyPlanningPeriodInfoSelect/handler'
import weeklyDuplicateSelect from '@/mocks/api/cmn/select/pages/weeklyDuplicateSelect/handler'
import offerKindMasterSelect from '@/mocks/api/cmn/select/pages/offerKindMasterSelectService/handler'
import AssessmentDomainInfoSelectInEntity from '@/mocks/api/cmn/select/pages/assessmentDomainInfoSelect/handler'
import MeetingAtendeeIntakeSelect from '@/mocks/api/cmn/select/pages/meetingAtendeeIntakeSelect/handler'
import HospitalListSelect from '@/mocks/api/cmn/select/pages/hospitalListSelect/handler'
import ClinicalDeptListSelect from '@/mocks/api/cmn/select/pages/clinicalDeptListSelect/handler'
import DoctorListSelect from '@/mocks/api/cmn/select/pages/doctorListSelect/handler'
import OccupationListSelect from '@/mocks/api/cmn/select/pages/occupationListSelect/handler'
import StaffListSelect from '@/mocks/api/cmn/select/pages/staffListSelect/handler'
import OfficeListSelect from '@/mocks/api/cmn/select/pages/officeListSelect/handler'
import FreeAssessmentSheetDisplaySettingsRowSelectedSelect from '@/mocks/api/cmn/select/pages/freeAssessmentSheetDisplaySettingsRowSelectedSelect/handler'
import IssuesPlanningStyleSettingsMasterSelect from '@/mocks/api/cmn/select/pages/issuesPlanningStyleSettingsMasterSelect/handler'
import IssuesPlanningStyleSettingsMasterChangeSelect from '@/mocks/api/cmn/select/pages/issuesPlanningStyleSettingsMasterChangeSelect/handler'
import evaluationTableHistorySelect from '@/mocks/api/cmn/select/pages/evaluationTableHistorySelect/handler'
import requiredcarePrimaryDecisionInitInfoSelect from '@/mocks/api/cmn/select/pages/requiredcarePrimaryDecisionInitInfoSelect/handler'
import comprehensivePlanCopySelect from '@/mocks/api/cmn/select/pages/comprehensivePlanCopySelect/handler'
import freeAssessmentOutputItemSettingsMasterSelect from '@/mocks/api/cmn/select/pages/freeAssessmentOutputItemSettingsMasterSelect/handler'
import freeAssessmentLedgerTitleModifiedSelect from '@/mocks/api/cmn/select/pages/freeAssessmentLedgerTitleModifiedSelect/handler'
import freeAssessmentItemModifiedSelect from '@/mocks/api/cmn/select/pages/freeAssessmentItemModifiedSelect/handler'
import KaigiRokuMstSelect from '@/mocks/api/cmn/select/pages/kaigiRokuMstSelect/handler'
import SpecialNoteMattersRegistInitialSelect from '@/mocks/api/cmn/select/pages/specialNoteMattersRegistInitialSelect/handler'
import UseSlipOtherDtlMaintenanceInitInfoSelect from '@/mocks/api/cmn/select/pages/useSlipOtherDtlMaintenanceInitInfoSelect/handler'
import IssuesAnalysisMasterSelect from '@/mocks/api/cmn/select/pages/issuesAnalysisMasterSelect/handler'
import careManagerDocumentMasterInfoSelect from '@/mocks/api/cmn/select/pages/careManagerDocumentMasterInfoSelect/handler'
import OrMeetingMinutesHistoryInfoSelect from '@/mocks/api/cmn/select/pages/orMeetingMinutesHistoryInfoSelect/handler'
import InitialReferralInfoSelect from '@/mocks/api/cmn/select/pages/initialReferralInfoSelect/handler'
import ReferralJobTypesSelect from '@/mocks/api/cmn/select/pages/referralJobTypesSelect/handler'
import maintainUsingTicketTypeTableSelect from '@/mocks/api/cmn/select/pages/maintainUsingTicketTypeTableSelect/handler'
import CertificationSurveySpecialMatterInitSelect from '@/mocks/api/cmn/select/pages/certificationSurveySpecialMatterInitSelect/handler'
import InsServiceParamSelect from '@/mocks/api/cmn/select/pages/insServiceParamSelect/handler'
import generalSituationSurveyHistoryInfoSelect from '@/mocks/api/cmn/select/pages/generalSituationSurveyHistoryInfoSelect/handler'
import weekTablePatternSelect from '@/mocks/api/cmn/select/pages/weekTablePatternSelect/handler'
import weekTableCopySelect from '@/mocks/api/cmn/select/pages/weekTableCopySelect/handler'
import shisetuSelect from '@/mocks/api/cmn/select/pages/shisetuSelect/handler'
import MeetingMinutesInitSelect from '@/mocks/api/cmn/select/pages/meetingMinutesInitSelect/handler'
import MeetingMinuteInfoCopySelect from '@/mocks/api/cmn/select/pages/meetingMinuteInfoCopySelect/handler'
import MeetingMinuteCopyPlanPeriodSelect from '@/mocks/api/cmn/select/pages/meetingMinuteCopyPlanPeriodSelect/handler'
import MeetingMinuteCopyHistorySelect from '@/mocks/api/cmn/select/pages/meetingMinuteCopyHistorySelect/handler'
import DrugSelect from '@/mocks/api/cmn/select/pages/drugSelect/handler'
import CarePlan2NeedsImportInfoAcquisitionSelect from '@/mocks/api/cmn/select/pages/carePlan2NeedsImportInfoAcquisitionSelect/handler'
import PreventivePlanIssueImportSelect from '@/mocks/api/cmn/select/pages/preventivePlanIssueImportSelect/handler'
import TaskImportInitInfoSelect from '@/mocks/api/cmn/select/pages/taskImportInitInfoSelect/handler'
import TaskImportPeriodHistorySelect from '@/mocks/api/cmn/select/pages/taskImportPeriodHistorySelect/handler'
import TaskImportHistoryDetailSelect from '@/mocks/api/cmn/select/pages/taskImportHistoryDetailSelect/handler'
import evaluationTableSettingsMasterInitSelect from '@/mocks/api/cmn/select/pages/evaluationTableSettingsMasterInitSelect/handler'
import ContentsImportInfoSelect from '@/mocks/api/cmn/select/pages/contentsImportInfoSelect/handler'
import weekTableImageDuplicateSelect from '@/mocks/api/cmn/select/pages/weekTableImageDuplicateSelect/handler'
import weekTableImageHistorySelect from '@/mocks/api/cmn/select/pages/weekTableImageHistorySelect/handler'
import weekTableImageInitSelect from '@/mocks/api/cmn/select/pages/weekTableImageInitSelect/handler'
import weekTableImagePatternSelect from '@/mocks/api/cmn/select/pages/weekTableImagePatternSelect/handler'
import weekTableImagePlanPeriodSelect from '@/mocks/api/cmn/select/pages/weekTableImagePlanPeriodSelect/handler'
import welfareEquipmentLendingUnitCntBundleSettingsInitialInfoSelect from '@/mocks/api/cmn/select/pages/welfareEquipmentLendingUnitCntBundleSettingsInitialInfoSelect/handler'
import ImplementationCarePlanImportSelect from '@/mocks/api/cmn/select/pages/implementationCarePlanImportSelect/handler'
import duplicateHistoryChangeSelect from '@/mocks/api/cmn/select/pages/duplicateHistoryChangeSelect/handler'

import historySelectHospitalizationTimeInfoOfferSelect from '@/mocks/api/cmn/select/pages/historySelectHospitalizationTimeInfoOfferSelect/handler'

import ServiceUseSlipAnnexedTableDataMoveSelect from '@/mocks/api/cmn/select/pages/serviceUseSlipAnnexedTableDataMoveSelect/handler'
import DailyRoutinePlanInitSelect from '@/mocks/api/cmn/select/pages/dailyRoutinePlanInitSelect/handler'
import PrintSettingsScreenInitialInfoSelectGUI00944 from '@/mocks/api/cmn/select/pages/printSettingsScreenInitialInfoSelectGUI00944/handler'
import PrintSettingsScreenInitialInfoSelectGUI01035 from '@/mocks/api/cmn/select/pages/printSettingsScreenInitialInfoSelectGUI01035/handler'
import MonthlyYearlyTableHistoryInfoSelectGUI00944 from '@/mocks/api/cmn/select/pages/monthlyYearlyTableHistoryInfoSelectGUI00944/handler'
import LedgerInitializeDataSelectGUI00944 from '@/mocks/api/cmn/select/pages/ledgerInitializeDataSelectGUI00944/handler'
import LedgerInitializeDataSelectGUI01266 from '@/mocks/api/cmn/select/pages/ledgerInitializeDataSelectGUI01266/handler'
import BunruiSelectionInitialProcessInfoSelect from '@/mocks/api/cmn/select/pages/bunruiSelectionInitialProcessInfoSelect/handler'
import ConsiderTableInterRaiMasterSelect from '@/mocks/api/cmn/select/pages/considerTableInterRaiMasterSelect/handler'
import MeetingMinutesHistorySelect from '@/mocks/api/cmn/select/pages/meetingMinutesHistorySelect/handler'
import MeetingMinutesHistoryPkgSelect from '@/mocks/api/cmn/select/pages/meetingMinutesHistoryPkgSelect/handler'
import MeetingMinutesPeriodSelect from '@/mocks/api/cmn/select/pages/meetingMinutesPeriodSelect/handler'
import DailyRoutinePlanCopyReturnSelect from '@/mocks/api/cmn/select/pages/dailyRoutinePlanCopyReturnSelect/handler'
import freeAssessmentLedgerTitleSettingsOfficeAddRowSelect from '@/mocks/api/cmn/select/pages/freeAssessmentLedgerTitleSettingsOfficeAddRowSelect/handler'
import AssessmentDomainSelectionInfoSelect from '@/mocks/api/cmn/select/pages/assessmentDomainSelectionInfoSelect/handler'
import CheckItemsSelect from '@/mocks/api/cmn/select/pages/checkItemsSelect/handler'
import CheckItemsInsertSelect from '@/mocks/api/cmn/select/pages/checkItemsInsertSelect/handler'
import CheckItemsPlanPeriodSelect from '@/mocks/api/cmn/select/pages/checkItemsPlanPeriodSelect/handler'
import CheckItemsHistorySelect from '@/mocks/api/cmn/select/pages/checkItemsHistorySelect/handler'
import CorrespondingTablePrintSettingsHistorySelect from '@/mocks/api/cmn/select/pages/correspondingTablePrintSettingsHistorySelect/handler'
import CorrespondingTablePrintSettingsSubjectSelect from '@/mocks/api/cmn/select/pages/correspondingTablePrintSettingsSubjectSelect/handler'
import RelatedPersonSelect from '@/mocks/api/cmn/select/pages/relatedPersonSelect/handler'
import calendarInputInitialSelect from '@/mocks/api/cmn/select/pages/calendarInputInitialSelect/handler'
import freeAssessmentLedgerTitleSettingsMasterInitSelect from '@/mocks/api/cmn/select/pages/freeAssessmentLedgerTitleSettingsMasterInitSelect/handler'
import useSlipInfoUpdateBefCheckInfoSelect from '@/mocks/api/cmn/select/pages/useSlipInfoUpdateBefCheckInfoSelect/handler'
import useSlipInfoRecalculationSelect from '@/mocks/api/cmn/select/pages/useSlipInfoRecalculationSelect/handler'
import useSlipDailyRateUsePeriodInitInfoSelect from '@/mocks/api/cmn/select/pages/useSlipDailyRateUsePeriodInitInfoSelect/handler'
import InterestAndConcernPlanPeriod from '@/mocks/api/cmn/select/pages/interestAndConcernPlanPeriodSelect/handler'
import InterestAndConcernHistory from '@/mocks/api/cmn/select/pages/interestAndConcernHistorySelect/handler'
import IssuesAnalysisPlanPeriod from '@/mocks/api/cmn/select/pages/issuesAnalysisPlanPeriodSelect/handler'
import IssuesAnalysisHistory from '@/mocks/api/cmn/select/pages/issuesAnalysisHistorySelect/handler'
import companyMitigationSelect from '@/mocks/api/cmn/select/pages/companyMitigationSelect/handler'
import EvaluationTablePrintSettingsHistorySelect from '@/mocks/api/cmn/select/pages/evaluationTablePrintSettingsHistorySelect/handler'
import mitigationUptakeSelect from '@/mocks/api/cmn/select/pages/mitigationUptakeSelect/handler'
import preventionEvaluationTableSettingsMasterSelect from '@/mocks/api/cmn/select/pages/preventionEvaluationTableSettingsMasterSelect/handler'
import HistorySelectScreenIssuesAnalysisInitSelect from '@/mocks/api/cmn/select/pages/historySelectScreenIssuesAnalysisInitSelect/handler'
import CertificationPeriodShortTermAdmissionUseDateCountSelect from '@/mocks/api/cmn/select/pages/certificationPeriodShortTermAdmissionUseDateCountSelect/handler'
import importMeetingMinutesInitInfoSelect from '@/mocks/api/cmn/select/pages/ImportMeetingMinutesInitInfoSelect/handler'
import chousaCodeMasterInitialSelect from '@/mocks/api/cmn/select/pages/chousaCodeMasterInitialSelect/handler'
import chousaKanaNameSelect from '@/mocks/api/cmn/select/pages/chousaKanaNameSelect/handler'
import SpecialNoteMattersInitSelect from '@/mocks/api/cmn/select/pages/specialNoteMattersSelect/handler'
import IssueSummaryImportRetrievalSelect from '@/mocks/api/cmn/select/pages/issueSummaryImportRetrievalSelect/handler'
import InquiryContentsDuplicateDetailInfoAcquisitionSelect from '@/mocks/api/cmn/select/pages/inquiryContentsDuplicateDetailInfoAcquisitionSelect/handler'
import OrBasicChecklistCopyInit from '@/mocks/api/cmn/select/pages/orBasicChecklistCopyInitSelect/handler'
import OrBasicChecklistCopyPlanPeriodModified from '@/mocks/api/cmn/select/pages/orBasicChecklistCopyPlanPeriodModifiedSelect/handler'
import OrBasicChecklistCopyHistoryModified from '@/mocks/api/cmn/select/pages/orBasicChecklistCopyHistoryModifiedSelect/handler'
import DailyRoutinePlanPtnTitleCntSelect from '@/mocks/api/cmn/select/pages/dailyRoutinePlanPtnTitleCntSelect/handler'
// import InitialSettingMasterSelect from '@/mocks/api/cmn/select/pages/initialSettingMasterSelect/handler'
import InsuranceServicesInitSelect from '@/mocks/api/cmn/select/pages/insuranceServicesInitSelect/handler'
import ServiceTermInfoSelect from '@/mocks/api/cmn/select/pages/serviceTermInfoSelect/handler'
import IncorporateServicesSelect from '@/mocks/api/cmn/select/pages/incorporateServicesSelect/handler'
import InsuranceServicesResetSelect from '@/mocks/api/cmn/select/pages/insuranceServicesResetSelect/handler'
import PrintSettingsScreenInitialInfoSelectGUI01085 from '@/mocks/api/cmn/select/pages/printSettingsScreenInitialInfoSelectGUI01085/handler'
import PrintSettingsScreenInitialInfoSelectGUI1264 from '@/mocks/api/cmn/select/pages/printSettingsScreenInitialInfoSelectGUI1264/handler'
import PrintSettingsScreenInitialInfoSelectGUI01266 from '@/mocks/api/cmn/select/pages/printSettingsScreenInitialInfoSelectGUI01266/handler'
import GetPlanDetailInfoSelect from '@/mocks/api/cmn/select/pages/getPlanDetailInfoSelect/handler'
import KibohudanTorokuSelect from '@/mocks/api/cmn/select/pages/kibohudanTorokuSelect/handler'
import DefaultPrintSettingSelect from '@/mocks/api/cmn/select/pages/defaultPrintSettingSelect/handler'
import Cp2MasterSelect from '@/mocks/api/cmn/select/pages/cp2MasterSelect/handler'
import stylePreviewSelect from '@/mocks/api/cmn/select/pages/stylePreviewSelect/handler'
import AssessmentRirekiTorikomiSelect from '@/mocks/api/cmn/select/pages/assessmentRirekiTorikomiSelect/handler'
import offerOfficeInfoSelect from '@/mocks/api/cmn/select/pages/offerOfficeInfoSelect/handler'
import userInfoSelect from '@/mocks/api/cmn/select/pages/userInfoSelect/handler'
import HistorySelectionPlanTwoSelect from '@/mocks/api/cmn/select/pages/historySelectionPlanTwoSelect/handler'
import DailyRateCalculationConfirmationInfoAcquisitionSelect from '@/mocks/api/cmn/select/pages/dailyRateCalculationConfirmationInfoAcquisitionSelect/handler'
import SealFieldDataSelect from '@/mocks/api/cmn/select/pages/sealFieldDataSelect/handler'
import RirekiKoumokuDetailSelect from '@/mocks/api/cmn/select/pages/rirekiKoumokuDetailSelect/handler'
import KoumokuDetailSelect from '@/mocks/api/cmn/select/pages/koumokuDetailSelect/handler'
import PrintInfoSelect from '@/mocks/api/cmn/select/pages/printInfoSelect/handler'
import WeekPlanInputSelect from '@/mocks/api/cmn/select/pages/weekPlanInputSelect/handler'
import ViewKaisouSelect from '@/mocks/api/cmn/select/pages/viewKaisouSelect/handler'
import ReceiptSectionInitSelect from '@/mocks/api/cmn/select/pages/receiptSectionInitSelect/handler'
import ReceiptSectionSelect from '@/mocks/api/cmn/select/pages/receiptSectionSelect/handler'
import InputSupportCareManagerInChargeInitialProcessSelect from '@/mocks/api/cmn/select/pages/inputSupportCareManagerInChargeInitialProcessSelect/handler'
import PrintSettingsScreenInitialInfoSelect from '@/mocks/api/cmn/select/pages/printSettingsScreenInitialInfoSelect/handler'
import LedgerInitializeDataSelectGUI01085 from '@/mocks/api/cmn/select/pages/ledgerInitializeDataSelectGUI01085/handler'
import FilterForUserIdSelectGUI01085 from '@/mocks/api/cmn/select/pages/filterForUserIdSelectGUI01085/handler'
import FilterForUserIdSelectGUI00944 from '@/mocks/api/cmn/select/pages/filterForUserIdSelectGUI00944/handler'
import LedgerInitializeDataSelectGUI01264 from '@/mocks/api/cmn/select/pages/ledgerInitializeDataSelectGUI01264/handler'
import FilterForUserIdSelectGUI01264 from '@/mocks/api/cmn/select/pages/filterForUserIdSelectGUI01264/handler'
import JigyousyoKensakuSelect from '@/mocks/api/cmn/select/pages/jigyousyoKensakuSelect/handler'
import remarksColumnSelect from '@/mocks/api/cmn/select/pages/remarksColumnSelect/handler'
import printSettingsScreenInitialInfoSelectGUI00986 from '@/mocks/api/cmn/select/pages/printSettingsScreenInitialInfoSelectGUI00986/handler'
import useSlipInfoDelete from '@/mocks/api/cmn/select/pages/useSlipInfoDelete/handler'
import useSlipInfoDeleteRowAftProcSelect from '@/mocks/api/cmn/select/pages/useSlipInfoDeleteRowAftProcSelect/handler'
import useSlipInfoDeleteBefSelect from '@/mocks/api/cmn/select/pages/useSlipInfoDeleteBefSelect/handler'
import useSlipInfoPredictionChangeRowAftProcSelect from '@/mocks/api/cmn/select/pages/useSlipInfoPredictionChangeRowAftProcSelect/handler'
import useSlipInfoNumberOfTimesModifiedBefCheckSelect from '@/mocks/api/cmn/select/pages/useSlipInfoNumberOfTimesModifiedBefCheckSelect/handler'
import useSlipInfoNumberOfTimesModifiedBefSelect from '@/mocks/api/cmn/select/pages/useSlipInfoNumberOfTimesModifiedBefSelect/handler'
import useSlipInfoWeekImportSelect from '@/mocks/api/cmn/select/pages/useSlipInfoWeekImportSelect/handler'
import useSlipInfoHopeBurdenSelect from '@/mocks/api/cmn/select/pages/useSlipInfoHopeBurdenSelect/handler'
import useSlipInfoDuplicateRowAftProcSelect from '@/mocks/api/cmn/select/pages/useSlipInfoDuplicateRowAftProcSelect/handler'
import planDuplicateInitialInfoSelect from '@/mocks/api/cmn/select/pages/planDuplicateInitialInfoSelect/handler'
import planDuplicateUseSlipUserInfoSelect from '@/mocks/api/cmn/select/pages/planDuplicateUseSlipUserInfoSelect/handler'
import planDuplicateInsuranceiInvalidUserInfoSelect from '@/mocks/api/cmn/select/pages/planDuplicateInsuranceiInvalidUserInfoSelect/handler'
import planDuplicateCarePlanEtcDuplicateInfoSelect from '@/mocks/api/cmn/select/pages/planDuplicateCarePlanEtcDuplicateInfoSelect/handler'
import yearMonthFromValidPeriodIDSelect from '@/mocks/api/cmn/select/pages/yearMonthFromValidPeriodIDSelect/handler'
import PrintSettingsScreenInitialInfoSelectGUI01132 from '@/mocks/api/cmn/select/pages/printSettingsScreenInitialInfoSelectGUI01132/handler'
import MeetingMinutesInfoSelectGUI01132 from '@/mocks/api/cmn/select/pages/meetingMinutesInfoSelectGUI01132/handler'
import FilterForUserIdSelectGUI01132 from '@/mocks/api/cmn/select/pages/filterForUserIdSelectGUI01132/handler'
import LedgerInitializeDataSelectGUI01132 from '@/mocks/api/cmn/select/pages/ledgerInitializeDataSelectGUI01132/handler'
import CarePLan1HistoryInfoSelectGUI00938 from '@/mocks/api/cmn/select/pages/carePLan1HistoryInfoSelectGUI00938/handler'
import FilterForUserIdSelectGUI00938 from '@/mocks/api/cmn/select/pages/filterForUserIdSelectGUI00938/handler'
import LedgerInitializeDataSelectGUI00938 from '@/mocks/api/cmn/select/pages/ledgerInitializeDataSelectGUI00938/handler'
import PrintSettingsScreenInitialInfoSelectGUI00938 from '@/mocks/api/cmn/select/pages/printSettingsScreenInitialInfoSelectGUI00938/handler'
import CareManagerCSVInitInfoSelect from '@/mocks/api/cmn/select/pages/careManagerCSVInitInfoSelect/handler'
import weekTableHistoryInfoSelect from '@/mocks/api/cmn/select/pages/weekTableHistoryInfoSelect/handler'
import LedgerInitializeDataSelect from '@/mocks/api/cmn/select/pages/ledgerInitializeDataSelect/handler'
import InquiryContentHeaderInfoSelect from '@/mocks/api/cmn/select/pages/inquiryContentHeaderInfoSelect/handler'
import simulationDollSelectInitInfoSelect from '@/mocks/api/cmn/select/pages/simulationDollSelectInitInfoSelect/handler'
import BasicChecklistHistInfoSelectGUI01085 from '@/mocks/api/cmn/select/pages/basicChecklistHistInfoSelectGUI01085/handler'
import hospitalizationTimeInfoOfferPeriodSelect from '@/mocks/api/cmn/select/pages/hospitalizationTimeInfoOfferPeriodSelect/handler'
import hospitalizationTimeInfoOfferInitInfoSelect from '@/mocks/api/cmn/select/pages/hospitalizationTimeInfoOfferInitInfoSelect/handler'
import GUI00027Init from '@/mocks/api/cmn/select/pages/dch/common/GUI00027/init/handler'
import IndividualDuplicateHistoryInitialSelect from '@/mocks/api/cmn/select/pages/individualDuplicateHistoryInitialSelect/handler'
import AssessmentInterRAIPrintSettingsHistorySelect from '@/mocks/api/cmn/select/pages/assessmentInterRAIPrintSettingsHistorySelect/handler'
import PlanMonitoringPrintSettingsUserChangeSelect from '@/mocks/api/cmn/select/pages/planMonitoringPrintSettingsUserChangeSelect/handler'
import PlanMonitoringPrintSettingsHistorySelect from '@/mocks/api/cmn/select/pages/planMonitoringPrintSettingsHistorySelect/handler'
import IssueOrganizeSummaryPrintSettingsHistorySelect from '@/mocks/api/cmn/select/pages/issueOrganizeSummaryPrintSettingsHistorySelect/handler'
import ImplementationMonitoringPrintSettingsHistorySelect from '@/mocks/api/cmn/select/pages/implementationMonitoringPrintSettingsHistorySelect/handler'
import IssuesConsiderPrintSettingsHistorySelect from '@/mocks/api/cmn/select/pages/issuesConsiderPrintSettingsHistorySelect/handler'
import AssessmentInterRAIPrintSettingsSubjectSelect from '@/mocks/api/cmn/select/pages/assessmentInterRAIPrintSettingsSubjectSelect/handler'
import IssueOrganizeSummaryPrintSettingsListHistorySelect from '@/mocks/api/cmn/select/pages/issueOrganizeSummaryPrintSettingsListHistorySelect/handler'
import SurveySlipDuplicatePlanningPeriodInfoSelect from '@/mocks/api/cmn/select/pages/surveySlipDuplicatePlanningPeriodInfoSelect/handler'
import SurveySlipDuplicateHistoryInfoSelect from '@/mocks/api/cmn/select/pages/surveySlipDuplicateHistoryInfoSelect/handler'
import SurveySlipDuplicateInitialInfoSelect from '@/mocks/api/cmn/select/pages/surveySlipDuplicateInitialInfoSelect/handler'
import AssessmentInterRAIPackagePlanPrintSettingsSelect from '@/mocks/api/cmn/select/pages/assessmentInterRAIPackagePlanPrintSettingsSelect/handler'
import AssessmentInterRAIPackagePlanPrintSettingsHistorySelect from '@/mocks/api/cmn/select/pages/assessmentInterRAIPackagePlanPrintSettingsHistorySelect/handler'
import AssessmentInterRAIPackagePlanPrintSettingsSubjectSelect from '@/mocks/api/cmn/select/pages/assessmentInterRAIPackagePlanPrintSettingsSubjectSelect/handler'
import SurveySlipDuplicateMultipleDetailInfoSelect from '@/mocks/api/cmn/select/pages/surveySlipDuplicateMultipleDetailInfoSelect/handler'
import SpecialInstructionsPeriodSelect from '@/mocks/api/cmn/select/pages/specialInstructionsPeriodSelect/handler'
import AttendingPhysicianStatementSelect from '@/mocks/api/cmn/select/pages/attendingPhysicianStatementSelect/handler'
import AttendingPhysicianStatementCopyReturnSelect from '@/mocks/api/cmn/select/pages/attendingPhysicianStatementCopyReturnSelect/handler'
import SougouKakYoKaigodoListSelect from '@/mocks/api/cmn/select/pages/sougouKakYoKaigodoListSelect/handler'
import SougouKakaTekiyoJigyosyoListInfoSelect from '@/mocks/api/cmn/select/pages/sougouKakaTekiyoJigyosyoListInfoSelect/handler'
import ImplementationPlan1CopySelect from '@/mocks/api/cmn/select/pages/implementationPlan1CopySelect/handler'
import MonthlyYearlyTableCopyInfoSelect from '@/mocks/api/cmn/select/pages/monthlyYearlyTableCopyInfoSelect/handler'
import WeekImportInitialInfoSelect from '@/mocks/api/cmn/select/pages/weekImportInitialInfoSelect/handler'
import PrintSettingsScreenInitialInfoSelectGUI1131 from '@/mocks/api/cmn/select/pages/printSettingsScreenInitialInfoSelectGUI1131/handler'
import MeetingMinutesHeaderInfoSelectGUI01131 from '@/mocks/api/cmn/select/pages/meetingMinutesHeaderInfoSelectGUI01131/handler'
import LedgerInitializeDataSelectGUI1131 from '@/mocks/api/cmn/select/pages/ledgerInitializeDataSelectGUI1131/handler'
import FilterForUserIdSelectGUI1131 from '@/mocks/api/cmn/select/pages/filterForUserIdSelectGUI1131/handler'
import SDCareCreateSelect from '@/mocks/api/cmn/select/pages/SDCareCreateSelect/handler'
import CertificateInfoSelect from '@/mocks/api/cmn/select/pages/certificateInfoSelect/handler'
import ShortTermLeavingDateRegistInitSelect from '@/mocks/api/cmn/select/pages/shortTermLeavingDateRegistInitSelect/handler'
import ValidWithOutPeriodServiceSelect from '@/mocks/api/cmn/select/pages/validWithOutPeriodServiceSelect/handler'
import Plan1Select from '@/mocks/api/cmn/select/pages/plan1Select/handler'
import printSettingsScreenInitialInfoSelectGUI01215 from '@/mocks/api/cmn/select/pages/printSettingsScreenInitialInfoSelectGUI01215/handler'
import printSettingsSimulationInfoSelect from '@/mocks/api/cmn/select/pages/printSettingsSimulationInfoSelect/handler'
import ledgerInitializeDataComSelect from '@/mocks/api/cmn/select/pages/ledgerInitializeDataComSelect/handler'
import ImplementationPlan3CopySelect from '@/mocks/api/cmn/select/pages/implementationPlan3CopySelect/handler'
import CareManagerInChargeInitialInfoSelect from '@/mocks/api/cmn/select/pages/careManagerInChargeInitialInfoSelect/handler'
import CareManagerInChargeStaffSelect from '@/mocks/api/cmn/select/pages/careManagerInChargeStaffSelect/handler'
import SecurityUseableOfficeSelect from '@/mocks/api/cmn/select/pages/securityUseableOfficeSelect/handler'
import BenefitStatusInfoSelect from '@/mocks/api/cmn/select/pages/benefitStatusInfoSelect/handler'
import MonthlyPlanInfoSelect from '@/mocks/api/cmn/select/pages/monthlyPlanInfoSelect/handler'
import FactReferenceScreenSelect from '@/mocks/api/cmn/select/pages/factReferenceScreenSelect/handler'
import PrintSettingsScreenInitialInfoSelectGUI1034 from '@/mocks/api/cmn/select/pages/printSettingsScreenInitialInfoSelectGUI1034/handler'
import PlanDoc2History from '@/mocks/api/cmn/select/pages/planDoc2History/handler'
import Plan1CopySelect from '@/mocks/api/cmn/select/pages/plan1CopySelect/handler'
import welfareEquipmentLendingUnitCntBundleSettingsInfoSelect from '@/mocks/api/cmn/select/pages/welfareEquipmentLendingUnitCntBundleSettingsInfoSelect/handler'
import PlanDocumentSelect from '~/mocks/api/cmn/select/pages/planDocumentSelect/handler'
import PlanDocumentCompile from '~/mocks/api/cmn/select/pages/planDocumentCompile/handler'
import CarePlan2AStyleInitSelect from '~/mocks/api/cmn/select/pages/carePlan2AStyleInitSelect/handler'
import CarePlan2AStyleStatisticalInfoSelect from '~/mocks/api/cmn/select/pages/carePlan2AStyleStatisticalInfoSelect/handler'
import CarePlan1AStyleStatisticalInfoSelect from '~/mocks/api/cmn/select/pages/carePlan1AStyleStatisticalInfoSelect/handler'
import useSlipOtherDtlInfoRecalculationSelect from '@/mocks/api/cmn/select/pages/useSlipOtherDtlInfoRecalculationSelect/handler'
import useSlipOtherDtlInfoSocialWelfareOfficeChangeSelect from '@/mocks/api/cmn/select/pages/useSlipOtherDtlInfoSocialWelfareOfficeChangeSelect/handler'
import useSlipInfoTmpImportSelect from '@/mocks/api/cmn/select/pages/useSlipInfoTmpImportSelect/handler'
import areaCloseContactTypeNursingCareServiceValidPeriodInfoSelect from '@/mocks/api/cmn/select/pages/areaCloseContactTypeNursingCareServiceValidPeriodInfoSelect/handler'
import PrintSettingInitInfoSDCareSelect from '@/mocks/api/cmn/select/pages/printSettingInitInfoSDCareSelect/handler'
import printSettingsScreenInitialInfoComSelect from '@/mocks/api/cmn/select/pages/printSettingsScreenInitialInfoComSelect/handler'
import implementationPlanHistoryInfoSelect from '@/mocks/api/cmn/select/pages/implementationPlanHistoryInfoSelect/handler'
import filterForUserIdComSelect from '@/mocks/api/cmn/select/pages/filterForUserIdComSelect/handler'
import useSlipInfoRecalculationBefSelect from '@/mocks/api/cmn/select/pages/useSlipInfoRecalculationBefSelect/handler'
import FilterForUserIdSelectGUI01034 from '@/mocks/api/cmn/select/pages/filterForUserIdSelectGUI01034/handler'
import LedgerInitializeDataSelectGUI01034 from '@/mocks/api/cmn/select/pages/ledgerInitializeDataSelectGUI01034/handler'
import useSlipInfoDailyRateCalculationSelect from '@/mocks/api/cmn/select/pages/useSlipInfoDailyRateCalculationSelect/handler'
import useSlipInfoPersonInsuredPersonSelect from '@/mocks/api/cmn/select/pages/useSlipInfoPersonInsuredPersonSelect/handler'
import organizingIssuesInitSelect from '@/mocks/api/cmn/select/pages/organizingIssuesInitSelect/handler'
import initialSettingMasterSelect from '@/mocks/api/cmn/select/pages/initialSettingMasterSelect/handler'
import IssuesAnalysisDuplicatePlanningPeriodInfoSelect from '@/mocks/api/cmn/select/pages/issuesAnalysisDuplicatePlanningPeriodInfoSelect/handler'
import IssuesAnalysisDuplicateHistoryInfoSelect from '@/mocks/api/cmn/select/pages/issuesAnalysisDuplicateHistoryInfoSelect/handler'
import FetchForInterestCheckSheetCopySelect from '@/mocks/api/cmn/select/pages/fetchForInterestCheckSheetCopySelect/handler'
import DailyRoutinePlanCopySelect from '@/mocks/api/cmn/select/pages/dailyRoutinePlanCopySelect/handler'
import NewNursingCareElderlySupportElapsedRecordSelect from '@/mocks/api/cmn/select/pages/NewNursingCareElderlySupportElapsedRecordSelect/handler'
import ConsiderTableDuplicatePlanningPeriodSelect from '@/mocks/api/cmn/select/pages/considerTableDuplicatePlanningPeriodSelect/handler'
import ConsiderTableDuplicateHistorySelect from '@/mocks/api/cmn/select/pages/considerTableDuplicateHistorySelect/handler'
import printSettingsInitialSelectGUI01147 from '@/mocks/api/cmn/select/pages/printSettingsInitialSelectGUI01147/handler'
import PrintSettingsUserInfoSelect from '@/mocks/api/cmn/select/pages/PrintSettingsUserInfoSelect/handler'
import considerTableDuplicatePlanningPeriodSelect from '@/mocks/api/cmn/select/pages/considerTableDuplicatePlanningPeriodSelect/handler'
import considerTableDuplicateHistorySelect from '@/mocks/api/cmn/select/pages/considerTableDuplicateHistorySelect/handler'
import FetchForInterestCheckSheetCopyPlanPeriodSelect from '@/mocks/api/cmn/select/pages/fetchForInterestCheckSheetCopyPlanPeriodSelect/handler'
import FetchForInterestCheckSheetCopyHistorySelect from '@/mocks/api/cmn/select/pages/fetchForInterestCheckSheetCopyHistorySelect/handler'
import ServiceUseStatusInfoSelect from '@/mocks/api/cmn/select/pages/serviceUseStatusInfoSelect/handler'
import UseServiceInfoSelect from '@/mocks/api/cmn/select/pages/useServiceInfoSelect/handler'
import PrintSettingsScreenInitialInfoSelectGUI01283 from '@/mocks/api/cmn/select/pages/PrintSettingsScreenInitialInfoSelectGUI01283/handler'
import CertificationSurveySlipInfoSelect from '@/mocks/api/cmn/select/pages/certificationSurveySlipInfoSelect/handler'
import InfoCollectionDuplicateInitialProcessSelect from '@/mocks/api/cmn/select/pages/infoCollectionDuplicateInitialProcessSelect/handler'
import AttendingPhysicianStatementPrintSettingsUserChangeSelect from '@/mocks/api/cmn/select/pages/attendingPhysicianStatementPrintSettingsUserChangeSelect/handler'
import AttendingPhysicianStatementPrintSettingsSubjectSelect from '@/mocks/api/cmn/select/pages/attendingPhysicianStatementPrintSettingsSubjectSelect/handler'
import FreeAssessmentFacePrintSettingsUserChangeSelect from '@/mocks/api/cmn/select/pages/freeAssessmentFacePrintSettingsUserChangeSelect/handler'
import FreeAssessmentFacePrintSettingsHistorySelect from '@/mocks/api/cmn/select/pages/freeAssessmentFacePrintSettingsHistorySelect/handler'
import FactorImportPeriodInfoSelect from '@/mocks/api/cmn/select/pages/factorImportPeriodInfoSelect/handler'
import FactorImportHistoryInfoSelect from '@/mocks/api/cmn/select/pages/factorImportHistoryInfoSelect/handler'
import FactorImportDetailInfoSelect from '@/mocks/api/cmn/select/pages/factorImportDetailInfoSelect/handler'
import FactorImportScreenCloseSelect from '@/mocks/api/cmn/select/pages/factorImportScreenCloseSelect/handler'
import certificationInfoSelect from '@/mocks/api/cmn/select/pages/certificationInfoSelect/handler'
import useSlipInfoRecalculationBef2Select from '@/mocks/api/cmn/select/pages/useSlipInfoRecalculationBef2Select/handler'
import serviceTimeSelect from '@/mocks/api/cmn/select/pages/serviceTimeSelect/handler'
import ChkKghSmglKinouIdSelect from '@/mocks/api/cmn/select/pages/chkKghSmglKinouIdSelect/handler'
import PrintSettingsScreenInitialInfoSelectGUI1119 from '@/mocks/api/cmn/select/pages/printSettingsScreenInitialInfoSelectGUI1119/handler'
import KyoumiKansinCheckSheet1HSelect from '@/mocks/api/cmn/select/pages/kyoumiKansinCheckSheet1HSelect/handler'
import FilterForUserIdSelectGUI01119 from '@/mocks/api/cmn/select/pages/filterForUserIdSelectGUI01119/handler'
import LedgerInitializeDataSelectGUI01119 from '@/mocks/api/cmn/select/pages/ledgerInitializeDataSelectGUI01119/handler'
import Plan1CopyReturnSelect from '@/mocks/api/cmn/select/pages/plan1CopyReturnSelect/handler'
import Plan1PrintSettingsUserChangeSelect from '@/mocks/api/cmn/select/pages/plan1PrintSettingsUserChangeSelect/handler'
import Plan1PrintSettingsSubjectSelect from '@/mocks/api/cmn/select/pages/plan1PrintSettingsSubjectSelect/handler'
import InfoCollectionReferPeriodSelect from '@/mocks/api/cmn/select/pages/infoCollectionReferPeriodSelect/handler'
import InfoCollectionReferHistorySelect from '@/mocks/api/cmn/select/pages/infoCollectionReferHistorySelect/handler'
import InfoCollectionReferDetailInfoSelect from '@/mocks/api/cmn/select/pages/infoCollectionReferDetailInfoSelect/handler'
import InfoCollectionReferDrugInfoSelect from '@/mocks/api/cmn/select/pages/infoCollectionReferDrugInfoSelect/handler'
import InfoCollectionDuplicateMultipleInfoSearchSelect from '@/mocks/api/cmn/select/pages/infoCollectionDuplicateMultipleInfoSearchSelect/handler'
import outOfInsuranceServiceRegistrationInitSelect from '@/mocks/api/cmn/select/pages/outOfInsuranceServiceRegistrationInitSelect/handler'
import issueOrganizeSummaryCopyInfoPeriodSelect from '@/mocks/api/cmn/select/pages/issueOrganizeSummaryCopyInfoPeriodSelect/handler'
import issueOrganizeSummaryCopyInfoHistorySelect from '@/mocks/api/cmn/select/pages/issueOrganizeSummaryCopyInfoHistorySelect/handler'
import CpnSypKeikaSelect from '@/mocks/api/cmn/select/pages/cpnSypKeikaSelect/handler'
import faceSheetNotebookInfoSelect from '@/mocks/api/cmn/select/pages/faceSheetNotebookInfoSelect/handler'
import PrintSettingInitialInfoSelect from '@/mocks/api/cmn/select/pages/printSettingInitialInfoSelect/handler'
import utilizePrintSettingsInitialSelect from '@/mocks/api/cmn/select/pages/utilizePrintSettingsInitialSelect/handler'
import PrintSettingsScreenInitialInfoSelectGUI01079 from '@/mocks/api/cmn/select/pages/printSettingsScreenInitialInfoSelectGUI01079/handler'
import RespondentInputSupportInitialInfoAcquisitionSelect from '@/mocks/api/cmn/select/pages/respondentInputSupportInitialInfoAcquisitionSelect/handler'
import printWriteSettingsSelect from '@/mocks/api/cmn/select/pages/printWriteSettingsSelect/handler'
import getPrinterSettingsListInfoSelect from '@/mocks/api/cmn/select/pages/getPrinterSettingsListInfoSelect/handler'
import printSettingsScreenInitialInfoSelectGUI01211 from '@/mocks/api/cmn/select/pages/printSettingsScreenInitialInfoSelectGUI01211/handler'
import hospitalizationTimeInfoOfferCopyInitInfoSelect from '@/mocks/api/cmn/select/pages/hospitalizationTimeInfoOfferCopyInitInfoSelect/handler'
import HospitalizationTimeInfoOfferHistorySelect from '@/mocks/api/cmn/select/pages/hospitalizationTimeInfoOfferHistorySelect/handler'
import useSlipInfoPredictionChangeBtnAftProcSelect from '@/mocks/api/cmn/select/pages/useSlipInfoPredictionChangeBtnAftProcSelect/handler'
import useSlipInfoPredictionLendingChangeRowAftProcSelect from '@/mocks/api/cmn/select/pages/useSlipInfoPredictionLendingChangeRowAftProcSelect/handler'
import useSlipInfoSortSelect from '@/mocks/api/cmn/select/pages/useSlipInfoSortSelect/handler'
import useSlipInfoEditRowBefProcSelect from '@/mocks/api/cmn/select/pages/useSlipInfoEditRowBefProcSelect/handler'
import benefitSituationListInitSelect from '@/mocks/api/cmn/select/pages/benefitSituationListInitSelect/handler'
import assessmentComprehensiveConsiderTableDuplicatePlanningPeriodSelect from '@/mocks/api/cmn/select/pages/assessmentComprehensiveConsiderTableDuplicatePlanningPeriodSelect/handler'
import assessmentComprehensiveConsiderTableDuplicateHistorySelect from '@/mocks/api/cmn/select/pages/assessmentComprehensiveConsiderTableDuplicateHistorySelect/handler'
import InfoCollectionImportInfoInitSelect from '@/mocks/api/cmn/select/pages/infoCollectionImportInfoInitSelect/handler'
import InfoCollectionImportHistoryInfoSelect from '@/mocks/api/cmn/select/pages/infoCollectionImportHistoryInfoSelect/handler'
import InfoCollectionImportDetailInfoSelect from '@/mocks/api/cmn/select/pages/infoCollectionImportDetailInfoSelect/handler'
import InfoCollectionDuplicateInfoSearchSelect from '@/mocks/api/cmn/select/pages/infoCollectionDuplicateInfoSearchSelect/handler'
import KaigiBasyoMstSelect from '@/mocks/api/cmn/select/pages/kaigiBasyoMstSelect/handler'
import TantoCmnShokuinSelect from '@/mocks/api/cmn/select/pages/tantoCmnShokuinSelect/handler'
import InfoCollectionDuplicateConfirm from '@/mocks/api/cmn/select/pages/infoCollectionDuplicateConfirm/handler'
import AssessmentInterRAIDuplicatePlanningPeriodSelect from '@/mocks/api/cmn/select/pages/assessmentInterRAIDuplicatePlanningPeriodSelect/handler'
import AssessmentInterRAIDuplicateHistorySelect from '@/mocks/api/cmn/select/pages/assessmentInterRAIDuplicateHistorySelect/handler'
import AssessmentInterRAIDuplicateHistoryChangeSelect from '@/mocks/api/cmn/select/pages/assessmentInterRAIDuplicateHistoryChangeSelect/handler'
import DailyRoutinePlanPrintSettingsUserChangeSelect from '@/mocks/api/cmn/select/pages/dailyRoutinePlanPrintSettingsUserChangeSelect/handler'
import DailyScheduleCopyInfoSelect from '@/mocks/api/cmn/select/pages/dailyScheduleCopyInfoSelect/handler'
// import DailyScheduleImageCopyReturnSelect from '@/mocks/api/cmn/select/pages/dailyScheduleImageCopyReturnSelect/handler'
import GetInitialPrintSettingsInfoSelect from '@/mocks/api/cmn/select/pages/getInitialPrintSettingsInfoSelect/handler'
import FaceSheetHistoryInfoSelect from '@/mocks/api/cmn/select/pages/faceSheetHistoryInfoSelect/handler'
import benefitSituationListOfficeSelect from '@/mocks/api/cmn/select/pages/benefitSituationListOfficeSelect/handler'
import simInitInfoSelect from '@/mocks/api/cmn/select/pages/simInitInfoSelect/handler'
import simNewTreatmentSelect from '@/mocks/api/cmn/select/pages/simNewTreatmentSelect/handler'
import simReCalculationCheckSelect from '@/mocks/api/cmn/select/pages/simReCalculationCheckSelect/handler'
import simReCalculationSelect from '@/mocks/api/cmn/select/pages/simReCalculationSelect/handler'
import HospitalizationTimeInfoOfferNewInfoSelect from '@/mocks/api/cmn/select/pages/hospitalizationTimeInfoOfferNewInfoSelect/handler'
import ImplementationPlan1PrintSettingsUserChangeSelect from '@/mocks/api/cmn/select/pages/implementationPlan1PrintSettingsUserChangeSelect/handler'
import ImplementationPlan1PrintSettingsSubjectSelect from '@/mocks/api/cmn/select/pages/implementationPlan1PrintSettingsSubjectSelect/handler'
import BaseCheckListSelect from '@/mocks/api/cmn/select/pages/baseCheckListSelect/handler'
import printSettingsScreenInfoSelectGUI00976 from '@/mocks/api/cmn/select/pages/printSettingsScreenInfoSelectGUI00976/handler'
import LeavingInfoRecordPrintSettinguserSwitchingSelect from '@/mocks/api/cmn/select/pages/leavingInfoRecordPrintSettinguserSwitchingSelect/handler'
import LeavingInfoRecordPrintSettingsHistorySelect from '@/mocks/api/cmn/select/pages/leavingInfoRecordPrintSettingsHistorySelect/handler'
import InterestAndConcernCopyInitSelect from '@/mocks/api/cmn/select/pages/interestAndConcernCopyInitSelect/handler'
import ServiceTypeInputSupoortRiyohyoMeisaiSelect from '@/mocks/api/cmn/select/pages/serviceTypeInputSupoortRiyohyoMeisaiSelect/handler'
import informationPrintSettingsInitialSelect from '@/mocks/api/cmn/select/pages/informationPrintSettingsInitialSelect/handler'
import assessmentHistoryInfoSelect from '@/mocks/api/cmn/select/pages/assessmentHistoryInfoSelect/handler'
import remarksColumnDuplicateSelect from '@/mocks/api/cmn/select/pages/remarksColumnDuplicateSelect/handler'
import PreventionPlanPrintUserSettingsUserChangeSelect from '@/mocks/api/cmn/select/pages/preventionPlanPrintUserSettingsUserChangeSelect/handler'
import PreventionPlanPrintUserSettingsInitSelect from '@/mocks/api/cmn/select/pages/preventionPlanPrintUserSettingsInitSelect/handler'
import DailyRoutinePlanPrintSettingsSubjectSelect from '@/mocks/api/cmn/select/pages/dailyRoutinePlanPrintSettingsSubjectSelect/handler'
import kentouyousiCommonInfoSelect from '@/mocks/api/cmn/select/pages/kentouyousiCommonInfoSelect/handler'
import organizingIssuesPlanPeriodSelect from '@/mocks/api/cmn/select/pages/organizingIssuesPlanPeriodSelect/handler'
import ImplementationPlan2PrintSettingsUserChangeSelect from '@/mocks/api/cmn/select/pages/implementationPlan2PrintSettingsUserChangeSelect/handler'
import orBasicChecklistPlanPeriodModifiedSelect from '@/mocks/api/cmn/select/pages/orBasicChecklistPlanPeriodModifiedSelect/handler'
import TucPlanSelect from '@/mocks/api/cmn/select/pages/tucPlanSelect/handler'
import orBasicChecklistHistoryModifiedSelect from '@/mocks/api/cmn/select/pages/orBasicChecklistHistoryModifiedSelect/handler'
import PreventionPlanPrintSettingsUserChangeSelect from '@/mocks/api/cmn/select/pages/preventionPlanPrintSettingsUserChangeSelect/handler'
import CarePlan2InitInfoSelect from '@/mocks/api/cmn/select/pages/carePlan2InitInfoSelect/handler'
import CarePlan2TabulationInfoSelect from '@/mocks/api/cmn/select/pages/carePlan2TabulationInfoSelect/handler'
import printSettingsScreenInitialInfoSelectGUI01054 from '@/mocks/api/cmn/select/pages/printSettingsScreenInitialInfoSelectGUI01054/handler'
import weekpPlanHistoryInfoAcquisitionSelect from '@/mocks/api/cmn/select/pages/weekpPlanHistoryInfoAcquisitionSelect/handler'
import PreventionPlanPrintSettingsSubjectSelect from '@/mocks/api/cmn/select/pages/preventionPlanPrintSettingsSubjectSelect/handler'
import SetUserInfoSelect from '@/mocks/api/cmn/select/pages/setUserInfoSelect/handler'
import DailyTablePatternConfigInitSelect from '@/mocks/api/cmn/select/pages/dailyTablePatternConfigInitSelect/handler'
import AttendingPhysicianStatementCschCntSelect from '@/mocks/api/cmn/select/pages/attendingPhysicianStatementCschCntSelect/handler'
import OrganizingIssuesHistorySelect from '@/mocks/api/cmn/select/pages/organizingIssuesHistorySelect/handler'
import AttendingPhysicianStatementCopySelect from '@/mocks/api/cmn/select/pages/attendingPhysicianStatementCopySelect/handler'
import FilterForUserIdSelect from '@/mocks/api/cmn/select/pages/filterForUserIdSelect/handler'
import AssessmentInterRAIAHistoryChangeSelect from '@/mocks/api/cmn/select/pages/assessmentInterRAIAHistoryChangeSelect/handler'
import SougouKakKeikakuKikanChangeInfoSelect from '@/mocks/api/cmn/select/pages/sougouKakKeikakuKikanChangeInfoSelect/handler'
import SougouKakRirekiChangeInfoSelect from '@/mocks/api/cmn/select/pages/sougouKakRirekiChangeInfoSelect/handler'
import OrganizingIssuesNewInitSelect from '@/mocks/api/cmn/select/pages/organizingIssuesNewInitSelect/handler'
import PrintSettingsInitialInfoSelect from '@/mocks/api/cmn/select/pages/printSettingsInitialInfoSelect/handler'
import PrintSettingsInitialInfoSelectGUI04470 from '@/mocks/api/cmn/select/pages/printSettingsInitialInfoSelectGUI04470/handler'

const post: ResponseResolver = async ({ request }) => {
  const requestBody = (await request.json()) as InEntity
  const business = requestBody.data.business
  const dataName = business.dataName
  let filePath = ''
  let handlerParam

  if (dataName) {
    filePath = ['./pages', dataName].join('/')
    handlerParam = business
  } else {
    const screenDefId = business.definitionJson.definition.screenDefId
    const routing = business.definitionJson.definition.routing
    const screenName = business.definitionJson.definition.screenPhysicalName
    handlerParam = business.definitionJson

    filePath = ['./pages', routing, screenName, screenDefId].join('/')
  }

  const handler = _handler(filePath)
  if (handler) {
    return handler(handlerParam)
  } else {
    const responceJson: BaseResponseBody = {
      statusCode: 'success',
      data: {
        definitionJson: business.definitionJson,
      },
    }
    return HttpResponse.json(responceJson, { status: 200 })
  }
}
function _handler(path: string): Function | undefined {
  try {
    if (path === './pages/longTermGoalImportSelect') {
      return LongTermGoalImport.handler
    } else if (path === './pages/dischargeFromHospitalLeavingInfoImportSelect') {
      return DischargeFromHospitalLeavingInfoImportSelect.handler
    } else if (path === './pages/weekPlanPatternTitleSelect') {
      return WeekPlanPatternTitleSelect.handler
    } else if (path === './pages/dailyPlanPatternSettingInitSelect') {
      return DailyPlanPatternSettingInitSelect.handler
    } else if (path === './pages/dailyPlanPatternTitleInitSelect') {
      return DailyPlanPatternTitleInitSelect.handler
    } else if (path === './pages/userSelectListInfoSelect') {
      return userSelectListInfoSelect.handler
    } else if (path === './pages/termInfoSelect') {
      return TermInfoSelect.handler
    } else if (path === './pages/issuesObjectivesImportSelect') {
      return IssuesObjectivesImportSelect.handler
    } else if (path === './pages/sample_page/hidden-item/input-sample/GUIyyyy') {
      return GUIyyyy.handler
    } else if (path === './pages/sample_page/hidden-item/use-sample/GUIxxxx') {
      return GUIxxxx.handler
    } else if (path === './pages/historySelectTotal') {
      return HistorySelectTotal.handler
    } else if (path === './pages/checkItemsSelect') {
      return CheckItemsSelect.handler
    } else if (path === './pages/checkItemsInsertSelect') {
      return CheckItemsInsertSelect.handler
    } else if (path === './pages/checkItemsPlanPeriodSelect') {
      return CheckItemsPlanPeriodSelect.handler
    } else if (path === './pages/checkItemsHistorySelect') {
      return CheckItemsHistorySelect.handler
    } else if (path === './pages/relatedPersonSelect') {
      return RelatedPersonSelect.handler
    } else if (path === './pages/dailyTaskImportSelect') {
      return DailyTaskImportSelect.handler
    } else if (path === './pages/cp1HistInfoAcquisitionSelect') {
      return Cp1HistInfoAcquisitionSelect.handler
    } else if (path === './pages/additionInfoSelect') {
      return AdditionInfoSelect.handler
    } else if (path === './pages/applicableOfficeSelect') {
      return ApplicableOfficeSelect.handler
    } else if (path === './pages/applicableOfficeConfirmSelect') {
      return ApplicableOfficeConfirmSelect.handler
    } else if (path === './pages/weekTableMst') {
      return WeekTableMst.handler
    } else if (path === './pages/dch/users/um-asp/GUI00380') {
      return GUI00380.handler
    } else if (path === './pages/historySelectWeekPlan') {
      return HistorySelectWeekPlan
    } else if (path === './pages/ninteiInfoInputSelect') {
      return NinteiInfoInputSelect.handler
    } else if (path === './pages/difficultyMasterSelect') {
      return difficultyMasterSelect.handler
    } else if (path === './pages/inhibitoryFactorsMasterSelect') {
      return inhibitoryFactorsMasterSelect.handler
    } else if (path === './pages/freeAssessmentLedgerTitleSettingsOfficeAddRowSelect') {
      return freeAssessmentLedgerTitleSettingsOfficeAddRowSelect.handler
    } else if (path === './pages/freeAssessmentLedgerTitleSettingsMasterInitSelect') {
      return freeAssessmentLedgerTitleSettingsMasterInitSelect.handler
    } else if (path === './pages/issueOrganizeSummaryMasterSelect') {
      return issueOrganizeSummaryMasterSelect.handler
    } else if (path === './pages/supportElapsedRecordSelect') {
      return supportElapsedRecordSelect.handler
    } else if (path === './pages/KadaiMasterSettingSelect') {
      return getKadaiMasterSetting.handler
    } else if (path === './pages/duplicateHistoryChangeSelect') {
      return duplicateHistoryChangeSelect.handler
    } else if (path === './pages/isKadaiDataDel') {
      return isKadaiListDel.handler
    } else if (path === './pages/issueOrganizeSummaryMasterCheckSelect') {
      return issueOrganizeSummaryMasterCheckSelect.handler
    } else if (path === './pages/specialNoteMattersSelect') {
      return SpecialNoteMattersInitSelect.handler
    } else if (path === './pages/isExistsKadaiList2') {
      return isExistsKadaiList2.handler
    } else if (path === './pages/selectYoshikiKadaiList') {
      return getYoshikiKadaiInfo.handler
    } else if (path === './pages/selectedKadaiList') {
      return isKadaiMasterRowDel.handler
    } else if (path === './pages/maxDifficultyCdSelect') {
      return maxDifficultyCdSelect.handler
    } else if (path === './pages/inquiryContentsImportInfoSelect') {
      return inquiryContentsImportInfoSelect.handler
    } else if (path === './pages/inquiryContentsImportHistoryListSelect') {
      return inquiryContentsImportHistoryListSelect.handler
    } else if (path === './pages/inquiryContentsImportHistoryDetailsListSelect') {
      return inquiryContentsImportHistoryDetailsListSelect.handler
    } else if (path === './pages/narrowDownConditionsSettingsSelect') {
      return narrowDownConditionsSettingsSelect.handler
    } else if (path === './pages/considerInitialInfoSelect') {
      return considerInitialInfoSelect.handler
    } else if (path === './pages/considerPeriodSelect') {
      return considerPeriodSelect.handler
    } else if (path === './pages/considerHistoryModifiedSelect') {
      return considerHistoryModifiedSelect.handler
    } else if (path === './pages/pensionBookSelectionSelect') {
      return pensionBookSelectionSelect.handler
    } else if (path === './pages/historySelectAssessmentFaceSheetSelect') {
      return historySelectAssessmentFaceSheetSelect.handler
    } else if (path === './pages/symbolSelectionSelect') {
      return symbolSelectionSelect.handler
    } else if (path === './pages/occupationInfoSelect') {
      return OccupationInfoSelect.handler
    } else if (path === './pages/weekPlanMasterSelect') {
      return WeekPlanMasterInfoSelect.handler
    } else if (path === './pages/holidaySetSelect') {
      return HolidaySetSelect.handler
    } else if (path === './pages/commonServiceInputSupportSelect') {
      return CommonServiceInputSupportSelect.handler
    } else if (path === './pages/caseListInfoSelect') {
      return CaseListInfoSelect.handler
    } else if (path === './pages/carePlanOneInitSelect') {
      return CarePlanOneInitSelect.handler
    } else if (path === './pages/carePlan2MasterSelect') {
      return CarePlan2MasterSelect.handler
    } else if (path === './pages/carePlan2InitSelect') {
      return CarePlan2InitSelect.handler
    } else if (path === './pages/carePlan2PlanPeriodSelect') {
      return CarePlan2PlanPeriodSelect.handler
    } else if (path === './pages/carePlan2HistorySelect') {
      return CarePlan2HistorySelect.handler
    } else if (path === './pages/careplan2TermIdSelect') {
      return Careplan2TermIdSelect.handler
    } else if (path === './pages/validPeriodIdSelect') {
      return ValidPeriodIdSelect.handler
    } else if (path === './pages/assessmentMasterSelect') {
      return AssessmentMasterSelect.handler
    } else if (path === './pages/cpnTucGdlComInfoSelect') {
      return CpnTucGdlComInfoSelect.handler
    } else if (path === './pages/samplePrintUserInfoSelect') {
      return SamplePrintUserInfoSelect.handler
    } else if (path === './pages/assessmentHomeCommonInfoSelect') {
      return AssessmentHomeCommonInfoSelect.handler
    } else if (path === './pages/assessmentHomePlanningPeriodChangeSelect') {
      return AssessmentHomePlanningPeriodChangeSelect.handler
    } else if (path === './pages/assessmentHomePrintSettingsSelect') {
      return AssessmentHomePrintSettingsSelect.handler
    } else if (path === './pages/assessmentHomePrintSettinguserSwitchingSelect') {
      return AssessmentHomePrintSettinguserSwitchingSelect.handler
    } else if (path === './pages/assessmentHomeHistoryChangeSelect') {
      return AssessmentHomeHistoryChangeSelect.handler
    } else if (path === './pages/specialNoteMattersRegistInitialSelect') {
      return SpecialNoteMattersRegistInitialSelect.handler
    } else if (path === './pages/assessmentHomeTab5Select') {
      return AssessmentHomeTab5Select.handler
    } else if (path === './pages/assessmentHomeTab61Select') {
      return AssessmentHomeTab61Select.handler
    } else if (path === './pages/contextMasterInitSelect') {
      return ContextMasterInitSelect.handler
    } else if (path === './pages/taskSummaryHistorySelect') {
      return TaskSummaryHistorySelect.handler
    } else if (path === './pages/issueAndGoalImportSelect') {
      return IssueAndGoalImportSelect.handler
    } else if (path === './pages/issueAndServiceImportSelect') {
      return IssueAndServiceImportSelect.handler
    } else if (path === './pages/useSlipOtherDtlMaintenanceInitInfoSelect') {
      return UseSlipOtherDtlMaintenanceInitInfoSelect.handler
    } else if (path === './pages/monthAndYearHistorySelect') {
      return MonthAndYearHistorySelect.handler
    } else if (path === './pages/implementationPlanTwoHistorySelect') {
      return ImplementationPlanTwoHistorySelect.handler
    } else if (path === './pages/incentivesItemsPrintSettingsHistorySelect') {
      return IncentivesItemsPrintSettingsHistorySelect.handler
    } else if (path === './pages/incentivesItemsPrintSettingsSubjectSelect') {
      return IncentivesItemsPrintSettingsSubjectSelect.handler
    } else if (path === './pages/dailyScheduleMasterInitSelect') {
      return DailyScheduleMasterInitSelect.handler
    } else if (path === './pages/printOrderModifiedSelect') {
      return PrintOrderModifiedSelect.handler
    } else if (path === './pages/carePlan1InitSelect') {
      return CarePlan1InitSelect.handler
    } else if (path === './pages/organizingIssuesImportSelect') {
      return OrganizingIssuesImportSelect.handler
    } else if (path === './pages/organizingIssuesSelect') {
      return OrganizingIssuesSelect.handler
    } else if (path === './pages/duplicateAssessmentSelectInEntity') {
      return duplicateAssessmentSelectInEntity.handler
    } else if (path === './pages/issuesConsiderBlankFormImportSelect') {
      return IssuesConsiderBlankFormImportSelect.handler
    } else if (path === './pages/implementPlanOneMasterInitSelect') {
      return ImplementPlanOneMasterInitSelect.handler
    } else if (path === './pages/importFreeAssessmentSelect') {
      return ImportFreeAssessmentSelect.handler
    } else if (path === './pages/frequencyRegistrationSelect') {
      return FrequencyRegistrationSelect.handler
    } else if (path === './pages/freeAssessmentCheckPrintSettingsSubjectSelect') {
      return FreeAssessmentCheckPrintSettingsSubjectSelect.handler
    } else if (path === './pages/contentSelect') {
      return ContentSelect.handler
    } else if (path === './pages/correspondingTablePrintSettingsHistorySelect') {
      return CorrespondingTablePrintSettingsHistorySelect.handler
    } else if (path === './pages/correspondingTablePrintSettingsSubjectSelect') {
      return CorrespondingTablePrintSettingsSubjectSelect.handler
    } else if (path === './pages/monthlyYearlyPatternSettingSelect') {
      return MonthlyYearlyPatternSettingSelect.handler
    } else if (path === './pages/assessmentHomeFamilyInitSelect') {
      return AssessmentHomeFamilyInitSelect.handler
    } else if (path === './pages/dailyPlanInputSelect') {
      return dailyPlanInputSelect.handler
    } else if (path === './pages/assessmentHomeTab62Select') {
      return AssessmentHomeTab62Select.handler
    } else if (path === './pages/assessmentHomeTab65Select') {
      return AssessmentHomeTab65Select.handler
    } else if (path === './pages/assessmentDuplicateSelect') {
      return AssessmentDuplicateSelect.handler
    } else if (path === './pages/assessmentDuplicateHistoryChange') {
      return AssessmentDuplicateHistoryChange.handler
    } else if (path === './pages/planMonitoringPeriodSelect') {
      return PlanMonitoringPeriodSelect.handler
    } else if (path === './pages/planMonitoringHistorySelect') {
      //return PlanMonitoringHistorySelect.handler
    } else if (path === './pages/planMonitoringInitInfoSelect') {
      //return PlanMonitoringInitInfoSelect.handler
    } else if (path === './pages/assessmentPackageDuplicateInitSelect') {
      return AssessmentPackageDuplicateInitSelect.handler
    } else if (path === './pages/approvalColumnRegistrationSelect') {
      return ApprovalColumnRegistrationSelect.handler
    } else if (path === './pages/assessmentHomeAttendingPhysicianOpinionTabSelect') {
      return AssessmentHomeAttendingPhysicianOpinionTabSelect.handler
    } else if (path === './pages/assessmentHomeScheduleSelectTabSelect') {
      return AssessmentHomeScheduleSelectTabSelect.handler
    } else if (path === './pages/assessmentFaceInfoSelect') {
      return AssessmentFaceInfoSelect.handler
    } else if (path === './pages/monthlyYearlyTableSelect') {
      return MonthlyYearlyTableSelect.handler
    } else if (path === './pages/monthlyYearlyTableCopyReturnSelect') {
      return MonthlyYearlyTableCopyReturnSelect.handler
    } else if (path === './pages/weeklyPlanCopyHistorySelect') {
      return weeklyPlanCopyHistorySelect.handler
    } else if (path === './pages/weeklyPlanCopyDetailHistorySelect') {
      return weeklyPlanCopyDetailHistorySelect.handler
    } else if (path === './pages/ryuuiKnjImportInitSelect') {
      return ThinkToKeepInMindInfoSelect.handler
    } else if (path === './pages/historySelectImplementationPlan') {
      return HistorySelectImplementationPlan.handler
    } else if (path === './pages/weekHistInfoSelect') {
      return WeekHistInfoSelect.handler
    } else if (path === './pages/assessmentInterRAIBNewSelect') {
      return AssessmentInterRAIBNewSelect.handler
    } else if (path === './pages/assessmentInterRAIBSelect') {
      return AssessmentInterRAIBSelect.handler
    } else if (path === './pages/assessmentInterRAICInitSelect') {
      return AssessmentInterRAICInitSelect.handler
    } else if (path === './pages/issuesAnalysisPrintSettingsDetailListHistorySelect') {
      return IssuesAnalysisPrintSettingsDetailListHistorySelect.handler
    } else if (path === './pages/issuesAnalysisPrintSettingsListHistorySelect') {
      return IssuesAnalysisPrintSettingsListHistorySelect.handler
    } else if (path === './pages/assessmentInterRAICsvOutputInitSelect') {
      return AssessmentInterRAICsvOutputInitSelect.handler
    } else if (path === './pages/assessmentInterRAICsvOutputListSelect') {
      return AssessmentInterRAICsvOutputListSelect.handler
    } else if (path === './pages/assessmentInterRAIESelect') {
      return AssessmentInterRAIESelect.handler
    } else if (path === './pages/assessmentInterRAIGSelect') {
      return AssessmentInterRAIGSelect.handler
    } else if (path === './pages/assessmentInterRAIPSelect') {
      return AssessmentInterRAIPSelect.handler
    } else if (path === './pages/assessmentInterRAIFInitSelect') {
      return AssessmentInterRAIFInitSelect.handler
    } else if (path === './pages/assessmentComprehensivePrintSettingsInitialSelect') {
      return AssessmentComprehensivePrintSettingsInitialSelect.handler
    } else if (path === './pages/countKghTucCheckHeadSelect') {
      return CountKghTucCheckHeadSelect.handler
    } else if (path === './pages/faceSheetPackage4InitSelect') {
      return faceSheetPackage4InitSelect.handler
    } else if (path === './pages/faceSheetNotebookInfoSelect') {
      return faceSheetNotebookInfoSelect.handler
    } else if (path === './pages/preventionEvaluationTableTitleMasterInitSelect') {
      return preventionEvaluationTableTitleMasterInitSelect.handler
    } else if (path === './pages/monitoringTitleMasterSelect') {
      return monitoringTitleMasterSelect.handler
    } else if (path === './pages/assessmentInterRAII1InitSelect') {
      return AssessmentInterRAII1InitSelect.handler
    } else if (path === './pages/assessmentInterRAIAHistorySelect') {
      return AssessmentInterRAIAHistorySelect.handler
    } else if (path === './pages/assessmentPackageplanInitSelect') {
      return AssessmentPackageplanInitSelect.handler
    } else if (path === './pages/assessmentPackageplanInitCommonSelect') {
      return AssessmentPackageplanInitCommonSelect.handler
    } else if (path === './pages/assessmentPackageplanHistoryChangeSelect') {
      return AssessmentPackageplanHistoryChangeSelect.handler
    } else if (path === './pages/assessmentPackageplanPeriodChangeSelect') {
      return AssessmentPackageplanPeriodChangeSelect.handler
    } else if (path === './pages/assessmentComprehensivePrintSettinguserSwitchingSelect') {
      return AssessmentComprehensivePrintSettinguserSwitchingSelect.handler
    } else if (path === './pages/historySelectScreenAssessmentInterRAISelect') {
      return HistorySelectScreenAssessmentInterRAISelect.handler
    } else if (path === './pages/historySelectScreenCheckItemsSelect') {
      return HistorySelectScreenCheckItemsSelect.handler
    } else if (path === './pages/assessmentInterRAIDInitSelect') {
      return AssessmentInterRAIDInitSelect.handler
    } else if (path === './pages/assessmentInterRAIVInitSelect') {
      return AssessmentInterRAIVInitSelect.handler
    } else if (path === './pages/assessmentInterRAISenteHistroyInitSelect') {
      return AssessmentInterRAISenteHistroyInitSelect.handler
    } else if (path === './pages/historySelectAssessmentIncentivesItemInterRAIInitSelect') {
      return HistorySelectAssessmentIncentivesItemInterRAIInitSelect.handler
    } else if (path === './pages/planMoniHistoryInfoSelect') {
      return PlanMoniHistoryInfoSelectEntity.handler
    } else if (path === './pages/preventionEvaluationTableMasterInitSelect') {
      return PreventionEvaluationTableMasterInitEntity.handler
    } else if (path === './pages/evaluationTableNewSelect') {
      return evaluationTableNewSelect.handler
    } else if (path === './pages/officeNmInfoSelect') {
      return OfficeNmInfoSelect.handler
    } else if (path === './pages/integratedPlanMasterInitSelect') {
      return IntegratedPlanMasterInitSelect.handler
    } else if (path === './pages/implementationPlanMstSelect') {
      return ImplementationPlanMstSelect.handler
    } else if (path === './pages/steps1Select') {
      return Steps1Select.handler
    } else if (path === './pages/steps2Select') {
      return Steps2Select.handler
    } else if (path === './pages/userPlanInfoSelectService') {
      return userPlanInfoSelectService.handler
    } else if (path === './pages/steps3Select') {
      return Steps3Select.handler
    } else if (path === './pages/steps4Select') {
      return Steps4Select.handler
    } else if (path === './pages/standardCarePlanSelect') {
      return StandardCarePlanSelect.handler
    } else if (path === './pages/certificationInfoInputInfoSelect') {
      return CertificationInfoInputScreenSelect.handler
    } else if (path === './pages/gaibuItakuryouSelect') {
      return GaibuItakuryouSelect.handler
    } else if (path === './pages/weekPlanEstimateDisplaySelect') {
      return WeekPlanEstimateDisplaySelect.handler
    } else if (path === './pages/patternTitleInfoSelect') {
      return PatternTitleInfoSelect.handler
    } else if (path === './pages/patternGroupInfoSelect') {
      return PatternGroupInfoSelect.handler
    } else if (path === './pages/shousaiInfoSelect') {
      return ShousaiInfoSelect.handler
    } else if (path === './pages/csvFileExportSelect') {
      return CsvFileExportSelect.handler
    } else if (path === './pages/csvExportInitInfoSelect') {
      return CsvExportInitInfoSelect.handler
    } else if (path === './pages/checkRirekListInitInfoSelect') {
      return CheckRirekListInitInfoSelect.handler
    } else if (path === './pages/difficultyScaleSelect') {
      return DiffcultyDegreeInputSupportInfoSelect.handler
    } else if (path === './pages/sougouKakSelect') {
      return ComprehensivePlanInitSelect.handler
    } else if (path === './pages/assessmentInterRAILSelect') {
      return AssessmentInterRAILSelect.handler
    } else if (path === './pages/evaluationTableDuplicateSelect') {
      return evaluationTableDuplicateSelect.handler
    } else if (path === './pages/evaluationSelect') {
      return EvaluationSelect.handler
    } else if (path === './pages/commonServiceMasterInitSelect') {
      return CommonServiceMasterInitSelect.handler
    } else if (path === './pages/issuesShortTermGoalImportSelect') {
      return IssuesShortTermGoalImportSelect.handler
    } else if (path === './pages/serviceSelect') {
      return ServiceSelect.handler
    } else if (path === './pages/assessmentInterRAIUSelect') {
      return AssessmentInterRAIUInitSelect.handler
    } else if (path === './pages/assessmentHomeTab63Select') {
      return assessmentHomeTab63Select.handler
    } else if (path === './pages/scheduleMasterInfoSelect') {
      return ScheduleMasterSelect.handler
    } else if (path === './pages/evaluationTableMasterInfoSelect') {
      return evaluationTableMasterInfoSelect.handler
    } else if (path === './pages/monthlyYearlyTablePtnTitleCntSelect') {
      return MonthlyYearlyTablePtnTitleCntSelect.handler
    } else if (path === './pages/assessmentInterRAINSelect') {
      return AssessmentInterRAINSelect.handler
    } else if (path === './pages/assessmentInterRAINInitSelect') {
      return assessmentInterRAINInitSelect.handler
    } else if (path === './pages/assessmentDiagnosSelect') {
      return AssessmentDiagnosSelect.handler
    } else if (path === './pages/evaluationTableDuplicatePlanPeriodSelect') {
      return evaluationTableDuplicatePlanPeriodSelect.handler
    } else if (path === './pages/assessmentInterRAIMSelect') {
      return AssessmentInterRAIMSelect.handler
    } else if (path === './pages/evaluationSettingSelect') {
      return EvaluationInfoSelect.handler
    } else if (path === './pages/visitSituationInfoSelect') {
      return VisitSituationInfoSelect.handler
    } else if (path === './pages/freeAssessmentCheckPrintSettingsHistorySelect') {
      return FreeAssessmentCheckPrintSettingsHistorySelect.handler
    } else if (path === './pages/kikanNaiRirekiSelect') {
      return KikanNaiRirekiSelect.handler
    } else if (path === './pages/implementationPlan1Select') {
      return ImplementationPlan1Select.handler
    } else if (path === './pages/evaluationTablePrintSettingsSubjectSelect') {
      return EvaluationTablePrintSettingsSubjectSelect.handler
    } else if (path === './pages/implementationPlan1CopyReturnSelect') {
      return ImplementationPlan1CopyReturnSelect.handler
    } else if (path === './pages/implementationPlan2Select') {
      return ImplementationPlan2Select.handler
    } else if (path === './pages/implementationPlan2CopyReturnSelect') {
      return ImplementationPlan2CopyReturnSelect.handler
    } else if (path === './pages/implementationPlan2CopySelect') {
      return ImplementationPlan2CopySelect.handler
    } else if (path === './pages/preventionPlanSelect') {
      return PreventionPlanSelect.handler
    } else if (path === './pages/levelOfCareRequiredModifiedUserListSelect') {
      return LevelOfCareRequiredModifiedUserListSelect.handler
    } else if (path === './pages/preventionPlanCopyReturnSelect') {
      return PreventionPlanCopyReturnSelect.handler
    } else if (path === './pages/preventionPlanCopyInitSelect') {
      return PreventionPlanCopyInitSelect.handler
    } else if (path === './pages/assessmentHomeTab66Select') {
      return AssessmentHomeTab66Select.handler
    } else if (path === './pages/concreteContentsCorrespondenceCareSelect') {
      return ConcreteContentsCorrespondenceCareSelect.handler
    } else if (path === './pages/assessmentMemoInitSelect') {
      return AssessmentMemoInitSelect.handler
    } else if (path === './pages/ploblemListImportSelect') {
      return PloblemListImportSelect.handler
    } else if (path === './pages/serviceKbnInputSupportSelect') {
      return ServiceKbnInputSupportSelect.handler
    } else if (path === './pages/officeAuthorInfoSelect') {
      return OfficeAuthorInfoSelect.handler
    } else if (path === './pages/inputSupportCareManagerSelect') {
      return InputSupportCareManagerSelect.handler
    } else if (path === './pages/careProvisionMasterSelect') {
      return careProvisionMasterSelect.handler
    } else if (path === './pages/user-list') {
      return UserList.handler // 利用者一覧情報の取得
    } else if (path === './pages/shokuin-list') {
      return ShokuinList.handler // 職員一覧情報の取得
    } else if (path === './pages/careProvisionLocationMasterSelect') {
      return CareProvisionLocationMasterSelect.handler
    } else if (path === './pages/weekPlanInitialInfoSelect') {
      return WeekPlanInitialInfoSelect.handler
    } else if (path === './pages/weekPlanInvWithinSelect') {
      return WeekPlanInvWithinSelect.handler
    } else if (path === './pages/weekPlanHistorySelect') {
      return WeekPlanHistorySelect.handler
    } else if (path === './pages/weekPlanDetailsListSelect') {
      return WeekPlanDetailsListSelect.handler
    } else if (path === './pages/infoCollectionInitSelect') {
      return InfoCollectionInitSelect.handler
    } else if (path === './pages/infoCollectionPlanPeriodSelect') {
      return InfoCollectionPlanPeriodSelect.handler
    } else if (path === './pages/infoCollectionDrugSelect') {
      return InfoCollectionDrugSelect.handler
    } else if (path === './pages/infoCollectionHistorySelect') {
      return InfoCollectionHistorySelect.handler
    } else if (path === './pages/infoCollection1InitSelect') {
      return InfoCollection1InitSelect.handler
    } else if (path === './pages/implementPlanTwoMasterInitSelect') {
      return ImplementationPlanTwoMasterInitSelect.handler
    } else if (path === './pages/dailyRoutineTableInputInitSelect') {
      // return DailyRoutineTableInputInitSelect.handler
    } else if (path === './pages/assessmentHomeServiceInitSelect') {
      return AssessmentHomeServiceInitSelect.handler
    } else if (path === './pages/assessmentShisetuShybetuChangeSelect') {
      return AssessmentShisetuShybetuChangeSelect.handler
    } else if (path === './pages/assessmentHomeTab4Select') {
      return assessmentHomeTab4Select.handler
    } else if (path === './pages/getDailyScheduleSelect') {
      return GetDailyScheduleSelect.handler
    } else if (path === './pages/SDCareMasterEasyRegistSelect') {
      return SDCareMasterEasyRegistSelect.handler
    } else if (path === './pages/weekPlanPatternSettingsInitSelect') {
      return weekPlanPatternSettingsInitSelect.handler
    } else if (path === './pages/dailyscheduleImportInitSelect') {
      return DailyscheduleImportInitSelect.handler
    } else if (path === './pages/historyInformationtDailySelect') {
      return HistoryInformationtDailySelect.handler
    } else if (path === './pages/userManagementOrLedgerSelect') {
      return UserManagementOrLedgerSelect.handler
    } else if (path === './pages/notebookInfoSelect') {
      return NotebookInfoSelect.handler
    } else if (path === './pages/surveySlipSelect') {
      return SurveySlipSelect.handler
    } else if (path === './pages/attendingPhysicianSelect') {
      return AttendingPhysicianSelect.handler
    } else if (path === './pages/displayOrderModifiedAssessmentSelect') {
      return DisplayOrderModifiedAssessmentSelect.handler
    } else if (path === './pages/evaluationTableImplementationPlanImportSelect') {
      return evaluationTableImplementationPlanImportSelect.handler
    } else if (path === './pages/SDCareMasterMaintenanceSelect') {
      return SDCareMasterMaintenanceSelect.handler
    } else if (path === './pages/assessmentInterRAIQInitSelect') {
      return AssessmentInterRAIQInitSelect.handler
    } else if (path === './pages/assessmentInterRAIKSelect') {
      return AssessmentInterRAIKSelect.handler
    } else if (path === './pages/displayConfigureInitSelect') {
      return DisplayConfigureInitSelect.handler
    } else if (path === './pages/assessmentInterRAIHSelect') {
      return AssessmentInterRAIHSelect.handler
    } else if (path === './pages/assessmentInterRAIOSelect') {
      return AssessmentInterRAIOSelect.handler
    } else if (path === './pages/assessmentInterRAIRSelect') {
      return AssessmentInterRAIRSelect.handler
    } else if (path === './pages/assessmentInterRAISSelect') {
      return AssessmentInterRAISSelect.handler
    } else if (path === './pages/familyMasterSelect') {
      return FamilyMasterSelect.handler
    } else if (path === './pages/monthlyImportSelect') {
      return MonthlyImportSelect.handler
    } else if (path === './pages/summaryFlgSelect') {
      return summaryFlgSelect.handler
    } else if (path === './pages/careplan2ImportSelect') {
      return Careplan2ImportSelect.handler
    } else if (path === './pages/careCheckMasterSelect') {
      return careCheckMasterSelect.handler
    } else if (path === './pages/evaluationTablePrintSettingsHistorySelect') {
      return EvaluationTablePrintSettingsHistorySelect.handler
    } else if (path === './pages/KigoImiSelect') {
      return KigoImiSelectEntity.handler
    } else if (path === './pages/consentFieldEditInitSelect') {
      return consentFieldEditInitSelect.handler
    } else if (path === './pages/faceSheetPackage2InitSelect') {
      return FaceSheetPackage2InitSelectEntity.handler
    } else if (path === './pages/jobTypeInfoSelect') {
    } else if (path === './pages/historySelectPagePreventionBaseSelect') {
      return historySelectPagePreventionBaseSelect.handler
    } else if (path === './pages/selectionTableAssessmentInterrai') {
      return selectionTableAssessmentInterraiSelect.handler
    } else if (path === './pages/serviceTypeInfoSelect') {
      return ServiceTypeInfoSelect.handler
    } else if (path === './pages/specialNoteMatterSelect') {
      return specialNoteMatterSelect.handler
    } else if (path === './pages/medicationSearchMasterSelect') {
      return MedicationSearchMasterSelect.handler
    } else if (path === './pages/printSettingInitInfoSelect') {
      return PrintSettingInitInfoSelect.handler
    } else if (path === './pages/medicationMasterSelect') {
      return MedicationMasterSelect.handler
    } else if (path === './pages/freeAssessmentCheckMasterSelect') {
      return freeAssessmentCheckMasterSelect.handler
    } else if (path === './pages/freeAssessmentCheckMasterDataSelect') {
      return freeAssessmentCheckMasterDataSelect.handler
    } else if (path === './pages/freeAssessmentIssuesPlanningDuplicateSelect') {
      return FreeAssessmentIssuesPlanningDuplicateSelect.handler
    } else if (path === './pages/freeAssessmentIssuesPlanningHistoryChangeSelect') {
      return FreeAssessmentIssuesPlanningHistoryChangeSelect.handler
    } else if (path === './pages/freeAssessmentIssuesPlanningInitialSelect') {
      return FreeAssessmentIssuesPlanningInitialSelect.handler
    } else if (path === './pages/freeAssessmentIssuesPlanningNewSelect') {
      return FreeAssessmentIssuesPlanningNewSelect.handler
    } else if (path === './pages/freeAssessmentIssuesPlanningPeriodChangeSelect') {
      return FreeAssessmentIssuesPlanningPeriodChangeSelect.handler
    } else if (path === './pages/serviceTypeInfoSelect') {
      return ServiceTypeInfoSelect.handler
    } else if (path === './pages/carePlanDuplicateCp2Select') {
      return CarePlanDuplicateCp2Select.handler
    } else if (path === './pages/evaluationTableDuplicateHistorySelect') {
      return evaluationTableDuplicateHistorySelect.handler
    } else if (path === './pages/baseCheckListHistorySelect') {
      return BaseCheckListHistorySelect.handler
    } else if (path === './pages/insuranceSelect') {
      return InsuranceSelect.handler
    } else if (path === './pages/issueConsiderationItem') {
      return IssueConsiderationItem.handler
    } else if (path === './pages/reviewTableCopyHistoryInfoSelect') {
      return ReviewTableCopyHistoryInfoSelect.handler
    } else if (path === './pages/issuesAnalysisPrintSettingsHistorySelect') {
      return IssuesAnalysisPrintSettingsHistorySelect.handler
    } else if (path === './pages/issueReviewCopyHistorySelect') {
      return IssueReviewCopyHistorySelect.handler
    } else if (path === './pages/displayOrderSettingSelect') {
      return DisplayOrderSettingSelect.handler
    } else if (path === './pages/consultationUserInitSelect') {
      return ConsultationUserInitSelect.handler
    } else if (path === './pages/staffInputAssistantSelect') {
      return StaffInputAssistantSelect.handler
    } else if (path === './pages/assessmentInterRAIAInitSelect') {
      return AssessmentInterRAIAInitSelect.handler
    } else if (path === './pages/assessmentInterRAIANewSelect') {
      return AssessmentInterRAIANewSelect.handler
    } else if (path === './pages/assessmentInterRAIATabSelect') {
      return AssessmentInterRAIATabSelect.handler
    } else if (path === './pages/assessmentInterRAIAPeriodChangeSelect') {
      return AssessmentInterRAIAPeriodChangeSelect.handler
    } else if (path === './pages/incentivesItemPlanPeriodselect') {
      return IncentivesItemPlanPeriodselect.handler
    } else if (path === './pages/correspondingTableAssessmentInterrai') {
      return CorrespondingTableAssessmentInterrai.handler
    } else if (path === './pages/incentivesItemHistoryselect') {
      return IncentivesItemHistoryselect.handler
    } else if (path === './pages/incentivesItemDetailselect') {
      return IncentivesItemDetailselect.handler
    } else if (path === './pages/assessmentInterRAIJInitSelect') {
      return AssessmentInterRAIJInitSelect.handler
    } else if (path === './pages/header/topHeaderInfo') {
      return Or00251TopHeaderInfo.handler
    } else if (path === './pages/header/noticeCount') {
      return Or00251NoticeCount.handler
    } else if (path === './pages/assessmentInterRAITSelect') {
      return AssessmentInterRAITSelect.handler
    } else if (path === './pages/assessmentResidentialHistoryInfoSelect') {
      return assessmentResidentialHistoryInfoSelect.handler
    } else if (path === './pages/preventionCarePlanMasterInitSelect') {
      return preventionCarePlanMasterInitSelect.handler
    } else if (path === './pages/planImplementationDuplicateChangeSelec') {
      return planImplementationDuplicateChangeSelec.handler
    } else if (path === './pages/planImplementationDuplicateInitSelect') {
      return PlanImplementationDuplicateInitSelect.handler
    } else if (path === './pages/assessmentComprehensiveMealIPeriodChangeSelect') {
      return assessmentComprehensiveMealIPeriodChangeSelect.handler
    } else if (path === './pages/serviceTypeInputSupoortSelect') {
      return serviceTypeInputSupoortSelect.handler
    } else if (path === './pages/assessmentConsolidationImportInfoSelect') {
      return assessmentConsolidationImportInfoSelect.handler
    } else if (path === './pages/issuesPlanningCategoryMasterInitSelect') {
      return IssuesPlanningCategoryMasterInitSelect.handler
    } else if (path === './pages/cmPreventionSentenceMasterSelectService') {
      return CmPreventionSentenceMasterSelectService.handler
    } else if (path === './pages/assessmentComprehensiveMealIHistoryChangeSelect') {
      return assessmentComprehensiveMealIHistoryChangeSelect.handler
    } else if (path === './pages/regularMedicineInfoSelect') {
      return RegularMedicineInfoSelect.handler
    } else if (path === './pages/executionConfirmationMasterInitSelect') {
      return ExecutionConfirmationMasterInitSelect.handler
    } else if (path === './pages/affectedAreaImportSettingSelect') {
      return AffectedAreaImportSettingSelect.handler
    } else if (path === './pages/itiranInfoInitSelect') {
      return ItiranInfoInitSelect.handler
    } else if (path === './pages/incentivesItemCorrespondingTableSelect') {
      return IncentivesItemCorrespondingTableSelect.handler
    } else if (path === './pages/choosingDoctorSelect') {
      return choosingDoctorSelect.handler
    } else if (path === './pages/dailyTaskTableMasterInitSelect') {
      return DailyTaskTableMasterInitSelect.handler
    } else if (path === './pages/dailyTaskTablePrintSettingsUserChangeSelect') {
      return DailyTaskTablePrintSettingsUserChangeSelect.handler
    } else if (path === './pages/dailyTaskTablePrintSettingsSubjectSelect') {
      return DailyTaskTablePrintSettingsSubjectSelect.handler
    } else if (path === './pages/evaluationTableCarePlan2ImportSelect') {
      return evaluationTableCarePlan2ImportSelect.handler
    } else if (path === './pages/assessmentIncludeBathingSelect') {
      return AssessmentIncludeBathingSelect.handler
    } else if (path === './pages/assessmentComprehensiveExcretionInitSelect') {
      return assessmentComprehensiveExcretionInitSelect.handler
    } else if (path === './pages/itakuryouKikanItemSelect') {
      return ItakuryouKikanItemSelect.handler
    } else if (path === './pages/historySelectScreenAssessmentPackageSelect') {
      return HistorySelectScreenAssessmentPackageSelect.handler
    } else if (path === './pages/baseCheckListHistoryChooseSelect') {
      return BaseCheckListHistoryChooseSelect.handler
    } else if (path === './pages/assessmentComprehensiveBaseInitSelect') {
      return AssessmentComprehensiveBaseInitSelect.handler
    } else if (path === './pages/prevPlanCopySourceSelect') {
      return prevPlanCopySourceSelect.handler
    } else if (path === './pages/prevPlanCopyHistorySelect') {
      return prevPlanCopyHistorySelect.handler
    } else if (path === './pages/yuSenJunISelect') {
      return YuSenJunISelect.handler
    } else if (path === './pages/opinionMasterSelect') {
      return OpinionMasterSelect.handler
    } else if (path === './pages/relationshipTypeNameSelect') {
      return RelationshipTypeNameSelect.handler
    } else if (path === './pages/relativesRelatedPersonSelect') {
      return RelativesRelatedPersonSelect.handler
    } else if (path === './pages/situationMasterSelect') {
      return SituationMasterSelect.handler
    } else if (path === './pages/supportImplentationSelect') {
      return SupportImplentationSelect.handler
    } else if (path === './pages/basicSituationImportInitSelect') {
      return BasicSituationImportInitSelect.handler
    } else if (path === './pages/problemDotSolutionEtcInfoSelect') {
      return problemDotSolutionEtcInfoSelect.handler
    } else if (path === './pages/serviceValidPeriodInfoSelect') {
      return ServiceValidPeriodInfoSelect.handler
    } else if (path === './pages/assessmentComprehensiveWashroomInitSelect') {
      return AssessmentComprehensiveWashroomInitSelect.handler
    } else if (path === './pages/assessmentComprehensiveBathingInitSelect') {
      return AssessmentComprehensiveBathingInitSelect.handler
    } else if (path === './pages/styleSelectSelect') {
      return StyleSelectSelect.handler
    } else if (path === './pages/assessmentComprehensivePsychologyInitSelect') {
      return assessmentComprehensivePsychologyInitSelect.handler
    } else if (path === './pages/meetingInquiryContentsHistoryInfoSelect') {
      return MeetingInquiryContentsHistoryInfoSelect.handler
    } else if (path === './pages/inquiryContentsHistSelectInfoKaigi') {
      return InquiryContentsHistSelectInfoKaigi.handler
    } else if (path === './pages/summaryTableInfoSelectService') {
      return summaryTableInfoSelectService.handler
    } else if (path === './pages/planTableImportInitSelect') {
      return PlanTableImportInitSelect.handler
    } else if (path === './pages/confirmationMethodMasterInitSelect') {
      return ConfirmationMethodMasterInitSelect.handler
    } else if (path === './pages/assessmentComprehensiveWashroomInitSelect') {
      return AssessmentComprehensiveWashroomInitSelect.handler
    } else if (path === './pages/assessmentComprehensiveMealInitSelect') {
      return assessmentComprehensiveMealInitSelect.handler
    } else if (path === './pages/certificationSurveyHistoryInfoSelect') {
      return certificationSurveyHistoryInfoSelect.handler
    } else if (path === './pages/certificationSurveyHistInfoSelect') {
      return certificationSurveyHistInfoSelect.handler
    } else if (path === './pages/implementPlanOneHistoryInformationSelect') {
      return implementPlanOneHistoryInformationSelect.handler
    } else if (path === './pages/kentouhyoInfoSelect') {
      return kentouhyoInfoSelect.handler
    } else if (path === './pages/kentouhyoCommonInfoSelect') {
      return kentouhyoCommonInfoSelect.handler
    } else if (path === './pages/individualDuplicateHistoryInitialSelect') {
      return IndividualDuplicateHistoryInitialSelect.handler
    } else if (path === './pages/assessmentComprehensiveMedicalCareInitSelect') {
      return AssessmentComprehensiveMedicalCareInitSelect.handler
    } else if (path === './pages/implementationPlan3Select') {
      return ImplementationPlan3Select.handler
    } else if (path === './pages/weekTablePatternInitSelect') {
      return WeekTablePatternInitSelect.handler
    } else if (path === './pages/weekTablePatternInfoSelect') {
      return WeekTablePatternInfoSelect.handler
    } else if (path === './pages/basicChecklistInitSelect') {
      return BasicChecklistInitSelect.handler
    } else if (path === './pages/satisfactionLevelMasterSelect') {
      return SatisfactionLevelMasterSelect.handler
    } else if (path === './pages/infoCollectionInitTenSelect') {
      return InfoCollectionInitTenSelect.handler
    } else if (path === './pages/orMedicalHistorySelect') {
      return OrMedicalHistorySelect.handler
    } else if (path === './pages/defaultWidthInfoSelect') {
      return FrameWidthModifiedSelect.handler
    } else if (path === './pages/cpnMucMygWidthSelect') {
      return FrameWidthModifiedMasterSelect.handler
    } else if (path === './pages/relativesAndAssociatesSelect') {
      return relativesAndAssociatesSelect.handler
    } else if (path === './pages/fCpnMucGdlKoumokuSelect') {
      return FCpnMucGdlKoumokuSelect.handler
    } else if (path === './pages/correspondenceMasterInitSelect') {
      return CorrespondenceMasterInitSelect.handler
    } else if (path === './pages/staffSearchSelect') {
      return staffSearchSelect.handler
    } else if (path === './pages/facilitySelectInitInfoSelect') {
      return FacilitySelectInfoSelect.handler
    } else if (path === './pages/settingsCmnSelect') {
      return SettingsCmnSelect.handler
    } else if (path === './pages/preventionBasicPeriodSelect') {
      return PreventionBasicPeriodSelect.handler
    } else if (path === './pages/preventionBasicInitialInfoSelect') {
      return PreventionBasicInitialInfoSelect.handler
    } else if (path === './pages/preventionBasicHistorySelect') {
      return PreventionBasicHistorySelect.handler
    } else if (path === './pages/preventionBasicDetailInfoSelect') {
      return PreventionBasicDetailInfoSelect.handler
    } else if (path === './pages/periodSelect') {
      return PeriodSelect.handler
    } else if (path === './pages/staffFrequencyInfoSelect') {
      return staffFrequencyInfoSelect.handler
    } else if (path === './pages/infoCollectionScreen7InitSelect') {
      return InfoCollectionScreen7InitSelect.handler
    } else if (path === './pages/monitoringMasterInitInfoSelect') {
      return MonitoringMasterInitInfoSelect.handler
    } else if (path === './pages/attendingPhysicianOpinionHistoryInitialInfoSelect') {
      return AttendingPhysicianOpinionHistoryInitialInfoSelect.handler
    } else if (path === './pages/useSlipInitInfoSelect') {
      return useSlipInitInfoSelect.handler
    } else if (path === './pages/infoCollection13InitSelect') {
      return InfoCollection13InitSelect.handler
    } else if (path === './pages/investigatorListInfoSelect') {
      return investigatorListInfoSelect.handler
    } else if (path === './pages/historySelectAssessmentComprehensiveSelect') {
      return historySelectAssessmentComprehensiveSelect.handler
    } else if (path === './pages/implementationMonitoringRegistInitNewSelect') {
      return implementationMonitoringRegistInitNewSelect.handler
    } else if (path === './pages/historySelectAssessmentComprehensiveSelect') {
      return historySelectAssessmentComprehensiveSelect.handler
    } else if (path === './pages/preventionCarePlanImportSelect') {
      return PreventionCarePlanImportSelect.handler
    } else if (path === './pages/preventionCarePlanImportReturnInfoSelect') {
      return PreventionCarePlanImportReturnInfoSelect.handler
    } else if (path === './pages/inputSupportGoalAndLife1YearScreenAcquireSelect') {
      return InputSupportGoalAndLife1YearScreenAcquireSelect.handler
    } else if (path === './pages/inputSupportGoalAndLife1YearScreenAcquireRedisplaySelect') {
      return InputSupportGoalAndLife1YearScreenAcquireRedisplaySelect.handler
    } else if (path === './pages/considerTablePrintSettingsHistorySelect') {
      return ConsiderTablePrintSettingsHistorySelect.handler
    } else if (path === './pages/inputSupportGoalAndLife1YearContentSelect') {
      return InputSupportGoalAndLife1YearContentSelect.handler
    } else if (path === './pages/FaceSheetPackage1PlanningPeriodSelect') {
      return FaceSheetPackage1PlanningPeriodSelect.handler
    } else if (path === './pages/FaceSheetPackage1InitSelect') {
      return FaceSheetPackage1InitSelect.handler
    } else if (path === './pages/FaceSheetPackage1HistorySelect') {
      return FaceSheetPackage1HistorySelect.handler
    } else if (path === './pages/FaceSheetPackage1PreviousAddressSelect') {
      return FaceSheetPackage1PreviousAddressSelect.handler
    } else if (path === './pages/faceSheet3Select') {
      return faceSheet3Select.handler
    } else if (path === './pages/faceSheetCopyKikanSelect') {
      return faceSheetCopyKikanSelect.handler
    } else if (path === './pages/faceSheetCopyHistorySelect') {
      return faceSheetCopyHistorySelect.handler
    } else if (path === './pages/maskMastSelect') {
      return MaskMastSelect.handler
    } else if (path === './pages/monitoringDisplayOrderSettingSelect') {
      return MonitoringDisplayOrderSettingSelect.handler
    } else if (path === './pages/basicSurvey1InitSelect') {
      return basicSurvey1InitSelect.handler
    } else if (path === './pages/basicSurvey2InitSelect') {
      return basicSurvey2InitSelect.handler
    } else if (path === './pages/basicSurvey5InitSelect') {
      return basicSurvey5InitSelect.handler
    } else if (path === './pages/bedriddenDementiaRankInfoSelect') {
      return bedriddenDementiaRankInfoSelect.handler
    } else if (path === './pages/doctorOpinionInfoSelect') {
      return doctorOpinionInfoSelect.handler
    } else if (path === './pages/implementationPlan3CopyReturnSelect') {
      return ImplementationPlan3CopyReturnSelect.handler
    } else if (path === './pages/specialnoteMatterInitialSelect') {
      return specialnoteMatterInitialSelect.handler
    } else if (path === './pages/faceSheetPrintSettingsSelect') {
      return faceSheetPrintSettingsSelect.handler
    } else if (path === './pages/basicSurvey3InitialInfoSelect') {
      return basicSurvey3InitialInfoSelect.handler
    } else if (path === './pages/basicSurvey4Select') {
      return basicSurvey4Select.handler
    } else if (path === './pages/implementationMonitoringMasterSelect') {
      return ImplementationMonitoringMasterSelect.handler
    } else if (path === './pages/necessaryBusinessProgramsSelect') {
      return NecessaryBusinessProgramsSelect.handler
    } else if (path === './pages/preventionEvaluationTableHistInfoSelect') {
      return PreventionEvaluationTableHistInfoSelect.handler
    } else if (path === './pages/orInterestCheckSheetHistorySelect') {
      return InterestCheckSheetHistorySelect.handler
    } else if (path === './pages/orCarePreventionServiceSupportPlanTableHeaderHistoryInfoSelect') {
      return OrCarePreventionServiceSupportPlanTableHeaderHistoryInfoSelect.handler
    } else if (path === './pages/prevPlanCopyHistorySelect') {
      return PrevPlanCopyHistorySelect.handler
    } else if (path === './pages/mainInsuranceSelect') {
      return mainInsuranceSelect.handler
    } else if (path === './pages/prevPlanCopyDetailHistorySelect') {
      return PrevPlanCopyDetailHistorySelect.handler
    } else if (path === './pages/assessmentInterRAIKentoHistroyInitSelect') {
      return assessmentInterRAIKentoHistroyInitSelect.handler
    } else if (path === './pages/kentouyousiInfoSelect') {
      return KentouyousiInfoSelect.handler
    } else if (path === './pages/historySelectIssuesPlanningInitSelect') {
      return HistorySelectIssuesPlanningInitSelect.handler
    } else if (path === './pages/assessmentKentoComprehensiveInitSelect') {
      return assessmentKentoComprehensiveInitSelect.handler
    } else if (path === './pages/historySelectScreenAssessmentInformationGatheringSelect') {
      return historySelectScreenAssessmentInformationGatheringSelect.handler
    } else if (path === './pages/cpnTucRaiAssDReportSelect') {
      return CpnTucRaiAssDReportSelect.handler
    } else if (path === './pages/dischargeLeavingHistoryInitInfoSelect') {
      return dischargeLeavingHistoryInitInfoSelect.handler
    } else if (path === './pages/admissionLeavingUseInfoSelect') {
      return AdmissionLeavingUseInfoSelect.handler
    } else if (path === './pages/weeklyScheduleInputSelect') {
      return weeklyScheduleInputSelect.handler
    } else if (path === './pages/weekPlanBundleImportInfoSelect') {
      return weekPlanBundleImportInfoSelect.handler
    } else if (path === './pages/assessmentSummaryImportSettingSelect') {
      return assessmentSummaryImportSettingSelect.handler
    } else if (path === './pages/implementationMonitoringRegistInitSelect') {
      return implementationMonitoringRegistInitSelect.handler
    } else if (path === './pages/inquiryContentsDuplicateInitijalInfoAcquisitionSelect') {
      return InquiryContentsDuplicateInitijalInfoAcquisitionSelect.handler
    } else if (path === './pages/priorityOrderSelect') {
      return PriorityOrderSelect.handler
    } else if (path === './pages/weekTableSelect') {
      return WeekTableSelect.handler
    } else if (path === './pages/planTransferInitialInfoSelect') {
      return PlanTransferInitialInfoSelect.handler
    } else if (path === './pages/planTransferListInfoSelect') {
      return PlanTransferListInfoSelect.handler
    } else if (path === './pages/faceHistoryInitInfoSelect') {
      return faceHistoryInitInfoSelect.handler
    } else if (path === './pages/itemSelectInitInfoSelect') {
      return ItemSelectInitInfoSelect.handler
    } else if (path === './pages/checkItemIssuesPlanningDuplicateInfoSelect') {
      return CheckItemIssuesPlanningDuplicateInfoSelect.handler
    } else if (path === './pages/duplicateInfoSelect') {
      return DuplicateInfoSelect.handler
    } else if (path === './pages/evaluationTablePlanPeriodSelect') {
      return evaluationTablePlanPeriodSelect.handler
    } else if (path === './pages/inhibitoryFactorInfoSelect') {
      return InhibitoryFactorInfoSelect.handler
    } else if (path === './pages/rirekiSentakuKadaitoukatuSelect') {
      return RirekiSentakuKadaitoukatuSelect.handler
    } else if (path === './pages/evaluationTableInitInfoSelect') {
      return evaluationTableInitInfoSelect.handler
    } else if (path === './pages/historySelectScreenAssessmentIssueExaminationSelect') {
      return HistorySelectScreenAssessmentIssueExaminationSelect.handler
    } else if (path === './pages/lumpUnitOfWelfareEquipmentInitSelect') {
      return lumpUnitOfWelfareEquipmentInitSelect.handler
    } else if (path === './pages/weekCopyHistorySelect') {
      return WeekCopyHistorySelect.handler
    } else if (path === './pages/evaluationTableHistoryInitInfoSelect') {
      return EvaluationTableHistoryInitInfoSelect.handler
    } else if (path === './pages/weekCopyPlanPeriodSelect') {
      return weekCopyPlanPeriodSelect.handler
    } else if (path === './pages/historyInfoSelect') {
      return HistoryInfoSelect.handler
    } else if (path === './pages/kghMocCheckTreeSelect') {
      return KghMocCheckTreeSelect.handler
    } else if (path === './pages/kghMocCheckSelect') {
      return KghMocCheckSelect.handler
    } else if (path === './pages/staffSearchInitSelect') {
      return staffSearchInitSelect.handler
    } else if (path === './pages/staffSearchDateChangeSelect') {
      return staffSearchDateChange.handler
    } else if (path === './pages/staffSearchOfficeSelect') {
      return staffSearchOffice.handler
    } else if (path === './pages/staffSearchRelatedSelect') {
      return staffSearchRelated.handler
    } else if (path === './pages/freeAssessmentSheetDisplaySettingsTabChangedSelect') {
      return FreeAssessmentSheetDisplaySettingsTabChangedSelect.handler
    } else if (path === './pages/interestAndConcernInitialSelect') {
      return InterestAndConcernInitialSelect.handler
    } else if (path === './pages/issuesAnalysisInitSelect') {
      return IssuesAnalysisInitSelect.handler
    } else if (path === './pages/issuesAnalysisDetailSelect') {
      return IssuesAnalysisDetailSelect.handler
    } else if (path === './pages/issuesPlanningStyleTitleMasterInitSelect') {
      return issuesPlanningStyleTitleMasterInitSelect.handler
    } else if (path === './pages/relatedOfficeMstInfoSelect') {
      return relatedOfficeMstInfoSelect.handler
    } else if (path === './pages/inquiryContentsInitialInfoAcquisitionSelect') {
      return InquiryContentsInitialInfoAcquisition.handler
    } else if (path === './pages/inquiryContentsDuplicateInfoAcquisitionSelect') {
      return InquiryContentsDuplicateInfoAcquisition.handler
    } else if (path === './pages/inquiryContentsHistSelectInfoAcquisitionSelect') {
      return InquiryContentsHistSelectInfoAcquisition.handler
    } else if (path === './pages/supportElapsedRecordMasterSelect') {
      return supportElapsedRecordMasterSelectService.handler
    } else if (path === './pages/weekPlanInitListInfoSelect') {
      return WeekPlanInitListInfoSelect.handler
    } else if (path === './pages/weekPlanServiceListInfoSelect') {
      return WeekPlanServiceListInfoSelect.handler
    } else if (path === './pages/weekPlanUsingSlipListCntSelect') {
      return WeekPlanUsingSlipListCntSelect.handler
    } else if (path === './pages/weekPlanUsingSlipImportSelect') {
      return WeekPlanUsingSlipImportSelect.handler
    } else if (path === './pages/hospitalizationTimeInfoOfferSetingMasterSelect') {
      return HospitalizationTimeInfoOfferSetingMasterSelect.handler
    } else if (path === './pages/weekTableHistorySelect') {
      return WeekTableHistorySelect.handler
    } else if (path === './pages/weekTablePeriodSelect') {
      return WeekTablePeriodSelect.handler
    } else if (path === './pages/lastBasicSurveyHistoryRevisionInfoSelect') {
      return LastBasicSurveyHistoryRevisionInfoSelect.handler
    } else if (path === './pages/supportElapsedKindMasterSelect') {
      return supportElapsedKindMasterSelect.handler
    } else if (path === './pages/dailyScheduleImageInitSelect') {
      return DailyScheduleImageInitSelect.handler
    } else if (path === './pages/generalSituationSurveyPlanningPeriodInfoSelect') {
      return generalSituationSurveyPlanningPeriodInfoSelect.handler
    } else if (path === './pages/weeklyDuplicateSelect') {
      return weeklyDuplicateSelect.handler
    } else if (path === './pages/offerKindMasterSelect') {
      return offerKindMasterSelect.handler
    } else if (path === './pages/assessmentDomainInfoSelect') {
      return AssessmentDomainInfoSelectInEntity.handler
    } else if (path === './pages/meetingAtendeeIntakeSelect') {
      return MeetingAtendeeIntakeSelect.handler
    } else if (path === './pages/hospitalListSelect') {
      return HospitalListSelect.handler
    } else if (path === './pages/clinicalDeptListSelect') {
      return ClinicalDeptListSelect.handler
    } else if (path === './pages/doctorListSelect') {
      return DoctorListSelect.handler
    } else if (path === './pages/occupationListSelect') {
      return OccupationListSelect.handler
    } else if (path === './pages/staffListSelect') {
      return StaffListSelect.handler
    } else if (path === './pages/officeListSelect') {
      return OfficeListSelect.handler
    } else if (path === './pages/freeAssessmentSheetDisplaySettingsRowSelectedSelect') {
      return FreeAssessmentSheetDisplaySettingsRowSelectedSelect.handler
    } else if (path === './pages/issuesPlanningStyleSettingsMasterSelect') {
      return IssuesPlanningStyleSettingsMasterSelect.handler
    } else if (path === './pages/issuesPlanningStyleSettingsMasterChangeSelect') {
      return IssuesPlanningStyleSettingsMasterChangeSelect.handler
    } else if (path === './pages/evaluationTableHistorySelect') {
      return evaluationTableHistorySelect.handler
    } else if (path === './pages/requiredcarePrimaryDecisionInitInfoSelect') {
      return requiredcarePrimaryDecisionInitInfoSelect.handler
    } else if (path === './pages/monitoringConfigureMasterInitSelect') {
      return MonitoringConfigureMasterInitSelect.handler
    } else if (path === './pages/comprehensivePlanDuplicateInitialProcessSelect') {
      return comprehensivePlanCopySelect.handler
    } else if (path === './pages/freeAssessmentOutputItemSettingsMasterSelect') {
      return freeAssessmentOutputItemSettingsMasterSelect.handler
    } else if (path === './pages/kaigiRokuMstSelect') {
      return KaigiRokuMstSelect.handler
    } else if (path === './pages/freeAssessmentLedgerTitleModifiedSelect') {
      return freeAssessmentLedgerTitleModifiedSelect.handler
    } else if (path === './pages/freeAssessmentItemModifiedSelect') {
      return freeAssessmentItemModifiedSelect.handler
    } else if (path === './pages/passageManagementSelect') {
      return PassageManagementSelect.handler
    } else if (path === './pages/issuesAnalysisMasterSelect') {
      return IssuesAnalysisMasterSelect.handler
    } else if (path === './pages/careManagerDocumentMasterInfoSelect') {
      return careManagerDocumentMasterInfoSelect.handler
    } else if (path === './pages/orMeetingMinutesHistoryInfoSelect') {
      return OrMeetingMinutesHistoryInfoSelect.handler
    } else if (path === './pages/copyInfoFetchSelect') {
      return copyInfoFetchSelect.handler
    } else if (path === './pages/maintainUsingTicketTypeTableSelect') {
      return maintainUsingTicketTypeTableSelect.handler
    } else if (path === './pages/certificationSurveySpecialMatterInitSelect') {
      return CertificationSurveySpecialMatterInitSelect.handler
    } else if (path === './pages/insServiceParamSelect') {
      return InsServiceParamSelect.handler
    } else if (path === './pages/generalSituationSurveyHistoryInfoSelect') {
      return generalSituationSurveyHistoryInfoSelect.handler
    } else if (path === './pages/weekTablePatternSelect') {
      return weekTablePatternSelect.handler
    } else if (path === './pages/weekTableCopySelect') {
      return weekTableCopySelect.handler
    } else if (path === './pages/shisetuInfoInitSelect') {
      return shisetuSelect.handler
    } else if (path === './pages/drugSelect') {
      return DrugSelect.handler
    } else if (path === './pages/evaluationTableSettingsMasterInitSelect') {
      return evaluationTableSettingsMasterInitSelect.handler
    } else if (path === './pages/cp2NeedsImportInfoAcquisitionSelect') {
      return CarePlan2NeedsImportInfoAcquisitionSelect.handler
    } else if (path === './pages/preventivePlanIssueImportSelect') {
      return PreventivePlanIssueImportSelect.handler
    } else if (path === './pages/taskImportInitInfoSelect') {
      return TaskImportInitInfoSelect.handler
    } else if (path === './pages/contentsImportInfoSelect') {
      return ContentsImportInfoSelect.handler
    } else if (path === './pages/taskImportPeriodHistorySelect') {
      return TaskImportPeriodHistorySelect.handler
    } else if (path === './pages/weekTableImageDuplicateSelect') {
      return weekTableImageDuplicateSelect.handler
    } else if (path === './pages/taskImportHistoryDetailSelect') {
      return TaskImportHistoryDetailSelect.handler
    } else if (path === './pages/weekTableImageHistorySelect') {
      return weekTableImageHistorySelect.handler
    } else if (path === './pages/weekTableImageInitSelect') {
      return weekTableImageInitSelect.handler
    } else if (path === './pages/weekTableImagePatternSelect') {
      return weekTableImagePatternSelect.handler
    } else if (path === './pages/weekTableImagePlanPeriodSelect') {
      return weekTableImagePlanPeriodSelect.handler
    } else if (path === './pages/welfareEquipmentLendingUnitCntBundleSettingsInitialInfoSelect') {
      return welfareEquipmentLendingUnitCntBundleSettingsInitialInfoSelect.handler
    } else if (path === './pages/implementationCarePlanImportSelect') {
      return ImplementationCarePlanImportSelect.handler
    } else if (path === './pages/historySelectHospitalizationTimeInfoOfferSelect') {
      return historySelectHospitalizationTimeInfoOfferSelect.handler
    } else if (path === './pages/dailyRoutinePlanInitSelect') {
      return DailyRoutinePlanInitSelect.handler
    } else if (path === './pages/printSettingsScreenInitialInfoSelectGUI00944') {
      return PrintSettingsScreenInitialInfoSelectGUI00944.handler
    } else if (path === './pages/monthlyYearlyTableHistoryInfoSelectGUI00944') {
      return MonthlyYearlyTableHistoryInfoSelectGUI00944.handler
    } else if (path === './pages/ledgerInitializeDataSelectGUI00944') {
      return LedgerInitializeDataSelectGUI00944.handler
    } else if (path === './pages/printSettingsScreenInitialInfoSelectGUI1035') {
      return PrintSettingsScreenInitialInfoSelectGUI01035.handler
    } else if (path === './pages/ledgerInitializeDataSelectGUI01266') {
      return LedgerInitializeDataSelectGUI01266.handler
    } else if (path === './pages/bunruiSelectionInitialProcessInfoSelect') {
      return BunruiSelectionInitialProcessInfoSelect.handler
    } else if (path === './pages/considerTableInterRaiMasterSelect') {
      return ConsiderTableInterRaiMasterSelect.handler
    } else if (path === './pages/meetingMinutesInitSelect') {
      return MeetingMinutesInitSelect.handler
    } else if (path === './pages/meetingMinuteInfoCopySelect') {
      return MeetingMinuteInfoCopySelect.handler
    } else if (path === './pages/meetingMinuteCopyPlanPeriodSelect') {
      return MeetingMinuteCopyPlanPeriodSelect.handler
    } else if (path === './pages/meetingMinuteCopyHistorySelect') {
      return MeetingMinuteCopyHistorySelect.handler
    } else if (path === './pages/initialReferralInfoSelect') {
      return InitialReferralInfoSelect.handler
    } else if (path === './pages/referralJobTypesSelect') {
      return ReferralJobTypesSelect.handler
    } else if (path === './pages/meetingMinutesHistorySelect') {
      return MeetingMinutesHistorySelect.handler
    } else if (path === './pages/meetingMinutesHistoryPkgSelect') {
      return MeetingMinutesHistoryPkgSelect.handler
    } else if (path === './pages/meetingMinutesPeriodSelect') {
      return MeetingMinutesPeriodSelect.handler
    } else if (path === './pages/serviceUseSlipAnnexedTableDataMoveSelect') {
      return ServiceUseSlipAnnexedTableDataMoveSelect.handler
    } else if (path === './pages/dailyRoutinePlanCopyReturnSelect') {
      return DailyRoutinePlanCopyReturnSelect.handler
    } else if (path === './pages/assessmentDomainSelectionInfoSelect') {
      return AssessmentDomainSelectionInfoSelect.handler
    } else if (path === './pages/calendarInputInitialSelect') {
      return calendarInputInitialSelect.handler
    } else if (path === './pages/useSlipInfoUpdateBefCheckInfoSelect') {
      return useSlipInfoUpdateBefCheckInfoSelect.handler
    } else if (path === './pages/useSlipInfoRecalculationSelect') {
      return useSlipInfoRecalculationSelect.handler
    } else if (path === './pages/useSlipDailyRateUsePeriodInitInfoSelect') {
      return useSlipDailyRateUsePeriodInitInfoSelect.handler
    } else if (path === './pages/interestAndConcernPlanPeriodSelect') {
      return InterestAndConcernPlanPeriod.handler
    } else if (path === './pages/interestAndConcernHistorySelect') {
      return InterestAndConcernHistory.handler
    } else if (path === './pages/companyMitigationSelect') {
      return companyMitigationSelect.handler
    } else if (path === './pages/issuesAnalysisPlanPeriodSelect') {
      return IssuesAnalysisPlanPeriod.handler
    } else if (path === './pages/issuesAnalysisHistorySelect') {
      return IssuesAnalysisHistory.handler
    } else if (path === './pages/mitigationUptakeSelect') {
      return mitigationUptakeSelect.handler
    } else if (path === './pages/preventionEvaluationTableSettingsMasterSelect') {
      return preventionEvaluationTableSettingsMasterSelect.handler
    } else if (path === './pages/historySelectScreenIssuesAnalysisInitSelect') {
      return HistorySelectScreenIssuesAnalysisInitSelect.handler
    } else if (path === './pages/certificationPeriodShortTermAdmissionUseDateCountSelect') {
      return CertificationPeriodShortTermAdmissionUseDateCountSelect.handler
    } else if (path === './pages/importMeetingMinutesInitInfoSelect') {
      return importMeetingMinutesInitInfoSelect.handler
    } else if (path === './pages/chousaCodeMasterInitialSelect') {
      return chousaCodeMasterInitialSelect.handler
    } else if (path === './pages/chousaKanaNameSelect') {
      return chousaKanaNameSelect.handler
    } else if (path === './pages/issueSummaryImportRetrievalSelect') {
      return IssueSummaryImportRetrievalSelect.handler
    } else if (path === './pages/inquiryContentsDuplicateDetailInfoAcquisitionSelect') {
      return InquiryContentsDuplicateDetailInfoAcquisitionSelect.handler
    } else if (path === './pages/orBasicChecklistCopyInitSelect') {
      return OrBasicChecklistCopyInit.handler
    } else if (path === './pages/orBasicChecklistCopyPlanPeriodModifiedSelect') {
      return OrBasicChecklistCopyPlanPeriodModified.handler
    } else if (path === './pages/orBasicChecklistCopyHistoryModifiedSelect') {
      return OrBasicChecklistCopyHistoryModified.handler
    } else if (path === './pages/dailyRoutinePlanPtnTitleCntSelect') {
      return DailyRoutinePlanPtnTitleCntSelect.handler
      // } else if (path === './pages/initialSettingMasterSelect') {
      //   return InitialSettingMasterSelect.handler
    } else if (path === './pages/insuranceServicesInitSelect') {
      return InsuranceServicesInitSelect.handler
    } else if (path === './pages/serviceTermInfoSelect') {
      return ServiceTermInfoSelect.handler
    } else if (path === './pages/incorporateServicesSelect') {
      return IncorporateServicesSelect.handler
    } else if (path === './pages/insuranceServicesResetSelect') {
      return InsuranceServicesResetSelect.handler
    } else if (path === './pages/printSettingsScreenInitialInfoSelectGUI01085') {
      return PrintSettingsScreenInitialInfoSelectGUI01085.handler
    } else if (path === './pages/printSettingsScreenInitialInfoSelectGUI01266') {
      return PrintSettingsScreenInitialInfoSelectGUI01266.handler
    } else if (path === './pages/getPlanDetailInfoSelect') {
      return GetPlanDetailInfoSelect.handler
    } else if (path === './pages/printSettingsScreenInitialInfoSelectGUI1264') {
      return PrintSettingsScreenInitialInfoSelectGUI1264.handler
    } else if (path === './pages/kibohudanTorokuSelect') {
      return KibohudanTorokuSelect.handler
    } else if (path === './pages/defaultPrintSettingSelect') {
      return DefaultPrintSettingSelect.handler
    } else if (path === './pages/cp2MasterSelect') {
      return Cp2MasterSelect.handler
    } else if (path === './pages/stylePreviewSelect') {
      return stylePreviewSelect.handler
    } else if (path === './pages/assessmentRirekiTorikomiSelect') {
      return AssessmentRirekiTorikomiSelect.handler
    } else if (path === './pages/offerOfficeInfoSelect') {
      return offerOfficeInfoSelect.handler
    } else if (path === './pages/userInfoSelect') {
      return userInfoSelect.handler
    } else if (path === './pages/historySelectionPlanTwoSelect') {
      return HistorySelectionPlanTwoSelect.handler
    } else if (path === './pages/dailyRateCalculationConfirmationInfoAcquisitionSelect') {
      return DailyRateCalculationConfirmationInfoAcquisitionSelect.handler
    } else if (path === './pages/sealFieldDataSelect') {
      return SealFieldDataSelect.handler
    } else if (path === './pages/rirekiKoumokuDetailSelect') {
      return RirekiKoumokuDetailSelect.handler
    } else if (path === './pages/koumokuDetailSelect') {
      return KoumokuDetailSelect.handler
    } else if (path === './pages/weekPlanInputSelect') {
      return WeekPlanInputSelect.handler
    } else if (path === './pages/viewKaisouSelect') {
      return ViewKaisouSelect.handler
    } else if (path === './pages/receiptSectionInitSelect') {
      return ReceiptSectionInitSelect.handler
    } else if (path === './pages/receiptSectionSelect') {
      return ReceiptSectionSelect.handler
    } else if (path === './pages/inputSupportCareManagerInChargeInitialProcessSelect') {
      return InputSupportCareManagerInChargeInitialProcessSelect.handler
    } else if (path === './pages/printSettingsScreenInitialInfoSelect') {
      return PrintSettingsScreenInitialInfoSelect.handler
    } else if (path === './pages/printInfoSelect') {
      return PrintInfoSelect.handler
    } else if (path === './pages/ledgerInitializeDataSelectGUI01085') {
      return LedgerInitializeDataSelectGUI01085.handler
    } else if (path === './pages/filterForUserIdSelectGUI01085') {
      return FilterForUserIdSelectGUI01085.handler
    } else if (path === './pages/filterForUserIdSelectGUI00944') {
      return FilterForUserIdSelectGUI00944.handler
    } else if (path === './pages/ledgerInitializeDataSelectGUI01264') {
      return LedgerInitializeDataSelectGUI01264.handler
    } else if (path === './pages/filterForUserIdSelectGUI01264') {
      return FilterForUserIdSelectGUI01264.handler
    } else if (path === './pages/jigyousyoKensakuSelect') {
      return JigyousyoKensakuSelect.handler
    } else if (path === './pages/remarksColumnSelect') {
      return remarksColumnSelect.handler
    } else if (path === './pages/printSettingsScreenInitialInfoSelectGUI00986') {
      return printSettingsScreenInitialInfoSelectGUI00986.handler
    } else if (path === './pages/useSlipInfoDelete') {
      return useSlipInfoDelete.handler
    } else if (path === './pages/useSlipInfoDeleteBefSelect') {
      return useSlipInfoDeleteBefSelect.handler
    } else if (path === './pages/useSlipInfoDeleteRowAftProcSelect') {
      return useSlipInfoDeleteRowAftProcSelect.handler
    } else if (path === './pages/useSlipInfoPredictionChangeRowAftProcSelect') {
      return useSlipInfoPredictionChangeRowAftProcSelect.handler
    } else if (path === './pages/useSlipInfoNumberOfTimesModifiedBefCheckSelect') {
      return useSlipInfoNumberOfTimesModifiedBefCheckSelect.handler
    } else if (path === './pages/useSlipInfoNumberOfTimesModifiedBefSelect') {
      return useSlipInfoNumberOfTimesModifiedBefSelect.handler
    } else if (path === './pages/useSlipInfoWeekImportSelect') {
      return useSlipInfoWeekImportSelect.handler
    } else if (path === './pages/useSlipInfoHopeBurdenSelect') {
      return useSlipInfoHopeBurdenSelect.handler
    } else if (path === './pages/useSlipInfoDuplicateRowAftProcSelect') {
      return useSlipInfoDuplicateRowAftProcSelect.handler
    } else if (path === './pages/planDuplicateInitialInfoSelect') {
      return planDuplicateInitialInfoSelect.handler
    } else if (path === './pages/planDuplicateUseSlipUserInfoSelect') {
      return planDuplicateUseSlipUserInfoSelect.handler
    } else if (path === './pages/planDuplicateInsuranceiInvalidUserInfoSelect') {
      return planDuplicateInsuranceiInvalidUserInfoSelect.handler
    } else if (path === './pages/planDuplicateCarePlanEtcDuplicateInfoSelect') {
      return planDuplicateCarePlanEtcDuplicateInfoSelect.handler
    } else if (path === './pages/yearMonthFromValidPeriodIDSelect') {
      return yearMonthFromValidPeriodIDSelect.handler
    } else if (path === './pages/printSettingsScreenInitialInfoSelectGUI01132') {
      return PrintSettingsScreenInitialInfoSelectGUI01132.handler
    } else if (path === './pages/meetingMinutesInfoSelectGUI01132') {
      return MeetingMinutesInfoSelectGUI01132.handler
    } else if (path === './pages/considerTablePrintSettingsSelect') {
      return ConsiderTablePrintSettingsSelect.handler
    } else if (path === './pages/filterForUserIdSelectGUI01132') {
      return FilterForUserIdSelectGUI01132.handler
    } else if (path === './pages/ledgerInitializeDataSelectGUI01132') {
      return LedgerInitializeDataSelectGUI01132.handler
    } else if (path === './pages/carePLan1HistoryInfoSelectGUI00938') {
      return CarePLan1HistoryInfoSelectGUI00938.handler
    } else if (path === './pages/filterForUserIdSelectGUI00938') {
      return FilterForUserIdSelectGUI00938.handler
    } else if (path === './pages/ledgerInitializeDataSelectGUI00938') {
      return LedgerInitializeDataSelectGUI00938.handler
    } else if (path === './pages/printSettingsScreenInitialInfoSelectGUI00938') {
      return PrintSettingsScreenInitialInfoSelectGUI00938.handler
    } else if (path === './pages/careManagerCSVInitInfoSelect') {
      return CareManagerCSVInitInfoSelect.handler
    } else if (path === './pages/weekTableHistoryInfoSelect') {
      return weekTableHistoryInfoSelect.handler
    } else if (path === './pages/ledgerInitializeDataSelect') {
      return LedgerInitializeDataSelect.handler
    } else if (path === './pages/inquiryContentHeaderInfoSelect') {
      return InquiryContentHeaderInfoSelect.handler
    } else if (path === './pages/simulationDollSelectInitInfoSelect') {
      return simulationDollSelectInitInfoSelect.handler
    } else if (path === './pages/basicChecklistHistInfoSelectGUI01085') {
      return BasicChecklistHistInfoSelectGUI01085.handler
    } else if (path === './pages/dch/common/GUI00027/init') {
      return GUI00027Init.handler
    } else if (path === './pages/assessmentInterRAIPrintSettingsHistorySelect') {
      return AssessmentInterRAIPrintSettingsHistorySelect.handler
    } else if (path === './pages/planMonitoringPrintSettingsUserChangeSelect') {
      return PlanMonitoringPrintSettingsUserChangeSelect.handler
    } else if (path === './pages/planMonitoringPrintSettingsHistorySelect') {
      return PlanMonitoringPrintSettingsHistorySelect.handler
    } else if (path === './pages/issueOrganizeSummaryPrintSettingsHistorySelect') {
      return IssueOrganizeSummaryPrintSettingsHistorySelect.handler
    } else if (path === './pages/implementationMonitoringPrintSettingsHistorySelect') {
      return ImplementationMonitoringPrintSettingsHistorySelect.handler
    } else if (path === './pages/issuesConsiderPrintSettingsHistorySelect') {
      return IssuesConsiderPrintSettingsHistorySelect.handler
    } else if (path === './pages/assessmentInterRAIPrintSettingsSubjectSelect') {
      return AssessmentInterRAIPrintSettingsSubjectSelect.handler
    } else if (path === './pages/issueOrganizeSummaryPrintSettingsListHistorySelect') {
      return IssueOrganizeSummaryPrintSettingsListHistorySelect.handler
    } else if (path === './pages/assessmentComprehensivePrintSettingsHistorySelect') {
      return AssessmentComprehensivePrintSettingsHistorySelect.handler
    } else if (path === './pages/hospitalizationTimeInfoOfferPeriodSelect') {
      return hospitalizationTimeInfoOfferPeriodSelect.handler
    } else if (path === './pages/hospitalizationTimeInfoOfferInitInfoSelect') {
      return hospitalizationTimeInfoOfferInitInfoSelect.handler
    } else if (path === './pages/surveySlipDuplicatePlanningPeriodInfoSelect') {
      return SurveySlipDuplicatePlanningPeriodInfoSelect.handler
    } else if (path === './pages/surveySlipDuplicateHistoryInfoSelect') {
      return SurveySlipDuplicateHistoryInfoSelect.handler
    } else if (path === './pages/surveySlipDuplicateInitialInfoSelect') {
      return SurveySlipDuplicateInitialInfoSelect.handler
    } else if (path === './pages/assessmentInterRAIPackagePlanPrintSettingsSelect') {
      return AssessmentInterRAIPackagePlanPrintSettingsSelect.handler
    } else if (path === './pages/assessmentInterRAIPackagePlanPrintSettingsHistorySelect') {
      return AssessmentInterRAIPackagePlanPrintSettingsHistorySelect.handler
    } else if (path === './pages/assessmentInterRAIPackagePlanPrintSettingsSubjectSelect') {
      return AssessmentInterRAIPackagePlanPrintSettingsSubjectSelect.handler
    } else if (path === './pages/surveySlipDuplicateMultipleDetailInfoSelect') {
      return SurveySlipDuplicateMultipleDetailInfoSelect.handler
    } else if (path === './pages/specialInstructionsPeriodSelect') {
      return SpecialInstructionsPeriodSelect.handler
    } else if (path === './pages/attendingPhysicianStatementSelect') {
      return AttendingPhysicianStatementSelect.handler
    } else if (path === './pages/attendingPhysicianStatementCopyReturnSelect') {
      return AttendingPhysicianStatementCopyReturnSelect.handler
    } else if (path === './pages/sougouKakYoKaigodoListSelect') {
      return SougouKakYoKaigodoListSelect.handler
    } else if (path === './pages/sougouKakaTekiyoJigyosyoListInfoSelect') {
      return SougouKakaTekiyoJigyosyoListInfoSelect.handler
    } else if (path === './pages/implementationPlan1CopySelect') {
      return ImplementationPlan1CopySelect.handler
    } else if (path === './pages/monthlyYearlyTableCopyInfoSelect') {
      return MonthlyYearlyTableCopyInfoSelect.handler
    } else if (path === './pages/weekImportInitialInfoSelect') {
      return WeekImportInitialInfoSelect.handler
    } else if (path === './pages/printSettingsScreenInitialInfoSelectGUI1131') {
      return PrintSettingsScreenInitialInfoSelectGUI1131.handler
    } else if (path === './pages/meetingMinutesHeaderInfoSelectGUI01131') {
      return MeetingMinutesHeaderInfoSelectGUI01131.handler
    } else if (path === './pages/ledgerInitializeDataSelectGUI1131') {
      return LedgerInitializeDataSelectGUI1131.handler
    } else if (path === './pages/filterForUserIdSelectGUI1131') {
      return FilterForUserIdSelectGUI1131.handler
    } else if (path === './pages/SDCareCreateSelect') {
      return SDCareCreateSelect.handler
    } else if (path === './pages/certificateInfoSelect') {
      return CertificateInfoSelect.handler
    } else if (path === './pages/shortTermLeavingDateRegistInitSelect') {
      return ShortTermLeavingDateRegistInitSelect.handler
    } else if (path === './pages/validWithOutPeriodServiceSelect') {
      return ValidWithOutPeriodServiceSelect.handler
    } else if (path === './pages/factReferenceScreenSelect') {
      return FactReferenceScreenSelect.handler
    } else if (path === './pages/plan1Select') {
      return Plan1Select.handler
    } else if (path === './pages/weekCopyInfoSelect') {
      return weekCopyInfoSelect.handler
    } else if (path === './pages/printSettingsScreenInitialInfoSelectGUI01215') {
      return printSettingsScreenInitialInfoSelectGUI01215.handler
    } else if (path === './pages/printSettingsSimulationInfoSelect') {
      return printSettingsSimulationInfoSelect.handler
    } else if (path === './pages/ledgerInitializeDataComSelect') {
      return ledgerInitializeDataComSelect.handler
    } else if (path === './pages/implementationPlan3CopySelect') {
      return ImplementationPlan3CopySelect.handler
    } else if (path === './pages/careManagerInChargeInitialInfoSelect') {
      return CareManagerInChargeInitialInfoSelect.handler
    } else if (path === './pages/careManagerInChargeStaffSelect') {
      return CareManagerInChargeStaffSelect.handler
    } else if (path === './pages/securityUseableOfficeSelect') {
      return SecurityUseableOfficeSelect.handler
    } else if (path === './pages/benefitStatusInfoSelect') {
      return BenefitStatusInfoSelect.handler
    } else if (path === './pages/monthlyPlanInfoSelect') {
      return MonthlyPlanInfoSelect.handler
    } else if (path === './pages/printSettingsScreenInitialInfoSelectGUI1034') {
      return PrintSettingsScreenInitialInfoSelectGUI1034.handler
    } else if (path === './pages/planDoc2History') {
      return PlanDoc2History.handler
    } else if (path === './pages/plan1CopySelect') {
      return Plan1CopySelect.handler
    } else if (path === './pages/welfareEquipmentLendingUnitCntBundleSettingsInfoSelect') {
      return welfareEquipmentLendingUnitCntBundleSettingsInfoSelect.handler
    } else if (path === './pages/planDocumentSelect') {
      return PlanDocumentSelect.handler
    } else if (path === './pages/planDocumentCompile') {
      return PlanDocumentCompile.handler
    } else if (path === './pages/carePlan2AStyleInitSelect') {
      return CarePlan2AStyleInitSelect.handler
    } else if (path === './pages/carePlan2AStyleStatisticalInfoSelect') {
      return CarePlan2AStyleStatisticalInfoSelect.handler
    } else if (path === './pages/carePlan1AStyleStatisticalInfoSelect') {
      return CarePlan1AStyleStatisticalInfoSelect.handler
    } else if (path === './pages/useSlipOtherDtlInfoRecalculationSelect') {
      return useSlipOtherDtlInfoRecalculationSelect.handler
    } else if (path === './pages/useSlipOtherDtlInfoSocialWelfareOfficeChangeSelect') {
      return useSlipOtherDtlInfoSocialWelfareOfficeChangeSelect.handler
    } else if (path === './pages/useSlipInfoTmpImportSelect') {
      return useSlipInfoTmpImportSelect.handler
    } else if (path === './pages/areaCloseContactTypeNursingCareServiceValidPeriodInfoSelect') {
      return areaCloseContactTypeNursingCareServiceValidPeriodInfoSelect.handler
    } else if (path === './pages/printSettingInitInfoSDCareSelect') {
      return PrintSettingInitInfoSDCareSelect.handler
    } else if (path === './pages/printSettingsScreenInitialInfoComSelect') {
      return printSettingsScreenInitialInfoComSelect.handler
    } else if (path === './pages/implementationPlanHistoryInfoSelect') {
      return implementationPlanHistoryInfoSelect.handler
    } else if (path === './pages/filterForUserIdComSelect') {
      return filterForUserIdComSelect.handler
    } else if (path === './pages/useSlipInfoRecalculationBefSelect') {
      return useSlipInfoRecalculationBefSelect.handler
    } else if (path === './pages/ledgerInitializeDataSelectGUI01034') {
      return LedgerInitializeDataSelectGUI01034.handler
    } else if (path === './pages/filterForUserIdSelectGUI01034') {
      return FilterForUserIdSelectGUI01034.handler
    } else if (path === './pages/useSlipInfoDailyRateCalculationSelect') {
      return useSlipInfoDailyRateCalculationSelect.handler
    } else if (path === './pages/useSlipInfoPersonInsuredPersonSelect') {
      return useSlipInfoPersonInsuredPersonSelect.handler
    } else if (path === './pages/organizingIssuesInitSelect') {
      return organizingIssuesInitSelect.handler
    } else if (path === './pages/initialSettingMasterSelect') {
      return initialSettingMasterSelect.handler
    } else if (path === './pages/issuesAnalysisDuplicatePlanningPeriodInfoSelect') {
      return IssuesAnalysisDuplicatePlanningPeriodInfoSelect.handler
    } else if (path === './pages/issuesAnalysisDuplicateHistoryInfoSelect') {
      return IssuesAnalysisDuplicateHistoryInfoSelect.handler
    } else if (path === './pages/fetchForInterestCheckSheetCopySelect') {
      return FetchForInterestCheckSheetCopySelect.handler
    } else if (path === './pages/dailyRoutinePlanCopySelect') {
      return DailyRoutinePlanCopySelect.handler
    } else if (path === './pages/NewNursingCareElderlySupportElapsedRecordSelect') {
      return NewNursingCareElderlySupportElapsedRecordSelect.handler
    } else if (path === './pages/considerTableDuplicatePlanningPeriodSelect') {
      return ConsiderTableDuplicatePlanningPeriodSelect.handler
    } else if (path === './pages/considerTableDuplicateHistorySelect') {
      return ConsiderTableDuplicateHistorySelect.handler
    } else if (path === './pages/printSettingsInitialSelectGUI01147') {
      return printSettingsInitialSelectGUI01147.handler
    } else if (path === './pages/PrintSettingsUserInfoSelect') {
      return PrintSettingsUserInfoSelect.handler
    } else if (path === './pages/considerTableDuplicatePlanningPeriodSelect') {
      return considerTableDuplicatePlanningPeriodSelect.handler
    } else if (path === './pages/considerTableDuplicateHistorySelect') {
      return considerTableDuplicateHistorySelect.handler
    } else if (path === './pages/fetchForInterestCheckSheetCopyPlanPeriodSelect') {
      return FetchForInterestCheckSheetCopyPlanPeriodSelect.handler
    } else if (path === './pages/fetchForInterestCheckSheetCopyHistorySelect') {
      return FetchForInterestCheckSheetCopyHistorySelect.handler
    } else if (path === './pages/serviceUseStatusInfoSelect') {
      return ServiceUseStatusInfoSelect.handler
    } else if (path === './pages/useServiceInfoSelect') {
      return UseServiceInfoSelect.handler
    } else if (path === './pages/printSettingsScreenInitialInfoSelectGUI01283') {
      return PrintSettingsScreenInitialInfoSelectGUI01283.handler
    } else if (path === './pages/considerTableInterRAIPrintSettingsHistorySelect') {
      return ConsiderTableInterRAIPrintSettingsHistorySelect.handler
    } else if (path === './pages/certificationSurveySlipInfoSelect') {
      return CertificationSurveySlipInfoSelect.handler
    } else if (path === './pages/infoCollectionDuplicateInitialProcessSelect') {
      return InfoCollectionDuplicateInitialProcessSelect.handler
    } else if (path === './pages/attendingPhysicianStatementPrintSettingsUserChangeSelect') {
      return AttendingPhysicianStatementPrintSettingsUserChangeSelect.handler
    } else if (path === './pages/attendingPhysicianStatementPrintSettingsSubjectSelect') {
      return AttendingPhysicianStatementPrintSettingsSubjectSelect.handler
    } else if (path === './pages/freeAssessmentFacePrintSettingsUserChangeSelect') {
      return FreeAssessmentFacePrintSettingsUserChangeSelect.handler
    } else if (path === './pages/freeAssessmentFacePrintSettingsHistorySelect') {
      return FreeAssessmentFacePrintSettingsHistorySelect.handler
    } else if (path === './pages/factorImportPeriodInfoSelect') {
      return FactorImportPeriodInfoSelect.handler
    } else if (path === './pages/factorImportHistoryInfoSelect') {
      return FactorImportHistoryInfoSelect.handler
    } else if (path === './pages/certificationInfoSelect') {
      return certificationInfoSelect.handler
    } else if (path === './pages/useSlipInfoRecalculationBef2Select') {
      return useSlipInfoRecalculationBef2Select.handler
    } else if (path === './pages/serviceTimeSelect') {
      return serviceTimeSelect.handler
    } else if (path === './pages/chkKghSmglKinouIdSelect') {
      return ChkKghSmglKinouIdSelect.handler
    } else if (path === './pages/ledgerInitializeDataSelectGUI01119') {
      return LedgerInitializeDataSelectGUI01119.handler
    } else if (path === './pages/filterForUserIdSelectGUI01119') {
      return FilterForUserIdSelectGUI01119.handler
    } else if (path === './pages/kyoumiKansinCheckSheet1HSelect') {
      return KyoumiKansinCheckSheet1HSelect.handler
    } else if (path === './pages/printSettingsScreenInitialInfoSelectGUI1119') {
      return PrintSettingsScreenInitialInfoSelectGUI1119.handler
    } else if (path === './pages/plan1CopyReturnSelect') {
      return Plan1CopyReturnSelect.handler
    } else if (path === './pages/plan1PrintSettingsUserChangeSelect') {
      return Plan1PrintSettingsUserChangeSelect.handler
    } else if (path === './pages/plan1PrintSettingsSubjectSelect') {
      return Plan1PrintSettingsSubjectSelect.handler
    } else if (path === './pages/infoCollectionReferPeriodSelect') {
      return InfoCollectionReferPeriodSelect.handler
    } else if (path === './pages/infoCollectionReferHistorySelect') {
      return InfoCollectionReferHistorySelect.handler
    } else if (path === './pages/infoCollectionReferDetailInfoSelect') {
      return InfoCollectionReferDetailInfoSelect.handler
    } else if (path === './pages/infoCollectionReferDrugInfoSelect') {
      return InfoCollectionReferDrugInfoSelect.handler
    } else if (path === './pages/infoCollectionDuplicateMultipleInfoSearchSelect') {
      return InfoCollectionDuplicateMultipleInfoSearchSelect.handler
    } else if (path === './pages/outOfInsuranceServiceRegistrationInitSelect') {
      return outOfInsuranceServiceRegistrationInitSelect.handler
    } else if (path === './pages/issueOrganizeSummaryCopyInfoPeriodSelect') {
      return issueOrganizeSummaryCopyInfoPeriodSelect.handler
    } else if (path === './pages/issueOrganizeSummaryCopyInfoHistorySelect') {
      return issueOrganizeSummaryCopyInfoHistorySelect.handler
    } else if (path === './pages/cpnSypKeikaSelect') {
      return CpnSypKeikaSelect.handler
    } else if (path === './pages/printSettingInitialInfoSelect') {
      return PrintSettingInitialInfoSelect.handler
    } else if (path === './pages/utilizePrintSettingsInitialSelect') {
      return utilizePrintSettingsInitialSelect.handler
    } else if (path === './pages/printSettingsScreenInitialInfoSelectGUI01079') {
      return PrintSettingsScreenInitialInfoSelectGUI01079.handler
    } else if (path === './pages/respondentInputSupportInitialInfoAcquisitionSelect') {
      return RespondentInputSupportInitialInfoAcquisitionSelect.handler
    } else if (path === './pages/printWriteSettingsSelect') {
      return printWriteSettingsSelect.handler
    } else if (path === './pages/getPrinterSettingsListInfoSelect') {
      return getPrinterSettingsListInfoSelect.handler
    } else if (path === './pages/printSettingsScreenInitialInfoSelectGUI01211') {
      return printSettingsScreenInitialInfoSelectGUI01211.handler
    } else if (path === './pages/hospitalizationTimeInfoOfferCopyInitInfoSelect') {
      return hospitalizationTimeInfoOfferCopyInitInfoSelect.handler
    } else if (path === './pages/hospitalizationTimeInfoOfferHistorySelect') {
      return HospitalizationTimeInfoOfferHistorySelect.handler
    } else if (path === './pages/infoCollectionDuplicateInfoSearchSelect') {
      return InfoCollectionDuplicateInfoSearchSelect.handler
    } else if (path === './pages/kaigiBasyoMstSelect') {
      return KaigiBasyoMstSelect.handler
    } else if (path === './pages/factorImportDetailInfoSelect') {
      return FactorImportDetailInfoSelect.handler
    } else if (path === './pages/factorImportScreenCloseSelect') {
      return FactorImportScreenCloseSelect.handler
    } else if (path === './pages/useSlipInfoPredictionChangeBtnAftProcSelect') {
      return useSlipInfoPredictionChangeBtnAftProcSelect.handler
    } else if (path === './pages/useSlipInfoPredictionLendingChangeRowAftProcSelect') {
      return useSlipInfoPredictionLendingChangeRowAftProcSelect.handler
    } else if (path === './pages/useSlipInfoSortSelect') {
      return useSlipInfoSortSelect.handler
    } else if (path === './pages/useSlipInfoEditRowBefProcSelect') {
      return useSlipInfoEditRowBefProcSelect.handler
    } else if (path === './pages/benefitSituationListInitSelect') {
      return benefitSituationListInitSelect.handler
    } else if (
      path === './pages/assessmentComprehensiveConsiderTableDuplicatePlanningPeriodSelect'
    ) {
      return assessmentComprehensiveConsiderTableDuplicatePlanningPeriodSelect.handler
    } else if (path === './pages/assessmentComprehensiveConsiderTableDuplicateHistorySelect') {
      return assessmentComprehensiveConsiderTableDuplicateHistorySelect.handler
    } else if (path === './pages/infoCollectionImportInfoInitSelect') {
      return InfoCollectionImportInfoInitSelect.handler
    } else if (path === './pages/infoCollectionImportHistoryInfoSelect') {
      return InfoCollectionImportHistoryInfoSelect.handler
    } else if (path === './pages/infoCollectionImportDetailInfoSelect') {
      return InfoCollectionImportDetailInfoSelect.handler
    } else if (path === './pages/tantoCmnShokuinSelect') {
      return TantoCmnShokuinSelect.handler
    } else if (path === './pages/infoCollectionDuplicateConfirm') {
      return InfoCollectionDuplicateConfirm.handler
    } else if (path === './pages/assessmentInterRAIDuplicatePlanningPeriodSelect') {
      return AssessmentInterRAIDuplicatePlanningPeriodSelect.handler
    } else if (path === './pages/assessmentInterRAIDuplicateHistorySelect') {
      return AssessmentInterRAIDuplicateHistorySelect.handler
    } else if (path === './pages/assessmentInterRAIDuplicateHistoryChangeSelect') {
      return AssessmentInterRAIDuplicateHistoryChangeSelect.handler
    } else if (path === './pages/dailyRoutinePlanPrintSettingsUserChangeSelect') {
      return DailyRoutinePlanPrintSettingsUserChangeSelect.handler
    } else if (path === './pages/dailyScheduleCopyInfoSelect') {
      return DailyScheduleCopyInfoSelect.handler
    } else if (path === './pages/getInitialPrintSettingsInfoSelect') {
      return GetInitialPrintSettingsInfoSelect.handler
    } else if (path === './pages/faceSheetHistoryInfoSelect') {
      return FaceSheetHistoryInfoSelect.handler
    } else if (path === './pages/benefitSituationListOfficeSelect') {
      return benefitSituationListOfficeSelect.handler
    } else if (path === './pages/simInitInfoSelect') {
      return simInitInfoSelect.handler
    } else if (path === './pages/simNewTreatmentSelect') {
      return simNewTreatmentSelect.handler
    } else if (path === './pages/simReCalculationCheckSelect') {
      return simReCalculationCheckSelect.handler
    } else if (path === './pages/simReCalculationSelect') {
      return simReCalculationSelect.handler
    } else if (path === './pages/hospitalizationTimeInfoOfferNewInfoSelect') {
      return HospitalizationTimeInfoOfferNewInfoSelect.handler
    } else if (path === './pages/implementationPlan1PrintSettingsUserChangeSelect') {
      return ImplementationPlan1PrintSettingsUserChangeSelect.handler
    } else if (path === './pages/implementationPlan1PrintSettingsSubjectSelect') {
      return ImplementationPlan1PrintSettingsSubjectSelect.handler
    } else if (path === './pages/baseCheckListSelect') {
      return BaseCheckListSelect.handler
    } else if (path === './pages/printSettingsScreenInfoSelectGUI00976') {
      return printSettingsScreenInfoSelectGUI00976.handler
    } else if (path === './pages/leavingInfoRecordPrintSettinguserSwitchingSelect') {
      return LeavingInfoRecordPrintSettinguserSwitchingSelect.handler
    } else if (path === './pages/leavingInfoRecordPrintSettingsHistorySelect') {
      return LeavingInfoRecordPrintSettingsHistorySelect.handler
    } else if (path === './pages/interestAndConcernCopyInitSelect') {
      return InterestAndConcernCopyInitSelect.handler
    } else if (path === './pages/serviceTypeInputSupoortRiyohyoMeisaiSelect') {
      return ServiceTypeInputSupoortRiyohyoMeisaiSelect.handler
    } else if (path === './pages/informationPrintSettingsInitialSelect') {
      return informationPrintSettingsInitialSelect.handler
    } else if (path === './pages/assessmentHistoryInfoSelect') {
      return assessmentHistoryInfoSelect.handler
    } else if (path === './pages/remarksColumnDuplicateSelect') {
      return remarksColumnDuplicateSelect.handler
    } else if (path === './pages/preventionPlanPrintUserSettingsUserChangeSelect') {
      return PreventionPlanPrintUserSettingsUserChangeSelect.handler
    } else if (path === './pages/preventionPlanPrintUserSettingsInitSelect') {
      return PreventionPlanPrintUserSettingsInitSelect.handler
    } else if (path === './pages/dailyRoutinePlanPrintSettingsSubjectSelect') {
      return DailyRoutinePlanPrintSettingsSubjectSelect.handler
    } else if (path === './pages/kentouyousiCommonInfoSelect') {
      return kentouyousiCommonInfoSelect.handler
    } else if (path === './pages/organizingIssuesPlanPeriodSelect') {
      return organizingIssuesPlanPeriodSelect.handler
    } else if (path === './pages/implementationPlan2PrintSettingsUserChangeSelect') {
      return ImplementationPlan2PrintSettingsUserChangeSelect.handler
    } else if (path === './pages/orBasicChecklistPlanPeriodModifiedSelect') {
      return orBasicChecklistPlanPeriodModifiedSelect.handler
    } else if (path === './pages/tucPlanSelect') {
      return TucPlanSelect.handler
    } else if (path === './pages/orBasicChecklistHistoryModifiedSelect') {
      return orBasicChecklistHistoryModifiedSelect.handler
    } else if (path === './pages/preventionPlanPrintSettingsUserChangeSelect') {
      return PreventionPlanPrintSettingsUserChangeSelect.handler
    } else if (path === './pages/preventionPlanPrintSettingsSubjectSelect') {
      return PreventionPlanPrintSettingsSubjectSelect.handler
    } else if (path === './pages/carePlan2InitInfoSelect') {
      return CarePlan2InitInfoSelect.handler
    } else if (path === './pages/carePlan2TabulationInfoSelect') {
      return CarePlan2TabulationInfoSelect.handler
    } else if (path === './pages/setUserInfoSelect') {
      return SetUserInfoSelect.handler
    } else if (path === './page/attendingPhysicianStatementCschCntSelect') {
      return AttendingPhysicianStatementCschCntSelect.handler
    } else if (path === './pages/dailyTablePatternConfigInitSelect') {
      return DailyTablePatternConfigInitSelect.handler
    } else if (path === './pages/printSettingsScreenInitialInfoSelectGUI01054') {
      return printSettingsScreenInitialInfoSelectGUI01054.handler
    } else if (path === './pages/weekpPlanHistoryInfoAcquisitionSelect') {
      return weekpPlanHistoryInfoAcquisitionSelect.handler
    } else if (path === './pages/organizingIssuesHistorySelect') {
      return OrganizingIssuesHistorySelect.handler
    } else if (path === './pages/attendingPhysicianStatementCopySelect') {
      return AttendingPhysicianStatementCopySelect.handler
    } else if (path === './pages/filterForUserIdSelect') {
      return FilterForUserIdSelect.handler
    } else if (path === './pages/assessmentInterRAIAHistoryChangeSelect') {
      return AssessmentInterRAIAHistoryChangeSelect.handler
    } else if (path === './pages/sougouKakKeikakuKikanChangeInfoSelect') {
      return SougouKakKeikakuKikanChangeInfoSelect.handler
    } else if (path === './pages/sougouKakRirekiChangeInfoSelect') {
      return SougouKakRirekiChangeInfoSelect.handler
    } else if (path === './pages/organizingIssuesNewInitSelect') {
      return OrganizingIssuesNewInitSelect.handler
    } else if (path === './pages/printSettingsInitialInfoSelect') {
      return PrintSettingsInitialInfoSelect.handler
    } else if (path === './pages/printSettingsInitialInfoSelectGUI04470') {
      return PrintSettingsInitialInfoSelectGUI04470.handler
    }
  } catch (error) {
    console.error('Error:', error)
  }

  return undefined
}
export default { post }
