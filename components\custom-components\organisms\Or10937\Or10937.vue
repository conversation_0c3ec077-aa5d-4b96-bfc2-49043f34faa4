<script setup lang="ts">
/**
 * Or10937:［履歴選択］画面 ﾌｪｰｽｼｰﾄ
 * GUI00900_［履歴選択］画面 ﾌｪｰｽｼｰﾄ
 *
 * @description
 * GUI00900_［履歴選択］画面 ﾌｪｰｽｼｰﾄ
 *
 * <AUTHOR>
 */

import { useI18n } from 'vue-i18n'
import { reactive, ref, onMounted, watch } from 'vue'
import type { FaceSheetTableDataItem, Or10937StateType } from './Or10937.type'
import { Or10937Const } from './Or10937.constants'
import { useScreenOneWayBind } from '#imports'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Or10937OnewayType } from '~/types/cmn/business/components/Or10937Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo01334OnewayType } from '~/types/business/components/Mo01334Type'
import type {
  faceHistoryInitInfoSelectInEntity,
  faceHistoryInitInfoSelectOutEntity,
} from '~/repositories/cmn/entities/faceHistoryInitInfoSelectEntity'

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: FaceSheetTableDataItem
  onewayModelValue: Or10937OnewayType
  uniqueCpId: string
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const defaultOnewayModelValue = {
  defaultData: {
    //計画期間ID
    sc1Id: '',
    // 事業者ID
    svJigyoId: '0',
    // 利用者ID
    userId: '',
    // 保存連番
    savSeq: '1',
  },
}

const localOneway = reactive({
  or10937: {
    ...defaultOnewayModelValue.defaultData,
    ...props.onewayModelValue,
  },
  // ﾌｪｰｽｼｰﾄダイアログ
  mo00024Oneway: {
    width: '330px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      toolbarTitle: t('label.history-select'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
    },
  },
  FaceSheetMo01334: {
    // ﾌｪｰｽｼｰﾄデータテーブルのヘッダー
    headers: [
      // 作成日
      {
        title: t('label.create-date'),
        key: 'createDate',
        minWidth: '100px',
        sortable: false,
      },
      // 作成者
      {
        title: t('label.author'),
        key: 'author',
        minWidth: '180px',
        width: '180px',
        sortable: false,
      },
    ],
    height: '358px',
    items: [],
  } as Mo01334OnewayType,
})

// 閉じるボタン設置
const mo00611Oneway: Mo00611OnewayType = {
  btnLabel: t('btn.close'),
  width: '90px',
  tooltipText: t('tooltip.screen-close'),
}

// 確定ボタン設置
const mo00609Oneway: Mo00609OnewayType = {
  btnLabel: t('btn.confirm'),
  width: '90px',
  tooltipText: t('tooltip.confirm-btn'),
}

// ダイアログ設置
const mo00024 = ref<Mo00024Type>({
  isOpen: Or10937Const.DEFAULT.IS_OPEN,
})

// ﾌｪｰｽｼｰﾄデータ設定
const FaceSheetSelectedItem = ref<FaceSheetTableDataItem>({
  savSeq: '',
  shoriYmd: '',
  shokuName: '',
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or10937StateType>({
  cpId: Or10937Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or10937Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  // 初期情報取得
  await getInitDataInfo()
})

/** 初期情報取得 */
const getInitDataInfo = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: faceHistoryInitInfoSelectInEntity = {
    svJigyoId: localOneway.or10937.svJigyoId,
    userId: localOneway.or10937.userId,
    sc1Id: localOneway.or10937.sc1Id,
  }

  const resData: faceHistoryInitInfoSelectOutEntity = await ScreenRepository.select(
    'faceHistoryInitInfoSelect',
    inputData
  )
  const dataList = resData.data.historyHeadList
  // データ情報設定
  localOneway.FaceSheetMo01334.items = dataList.map((item) => {
    return {
      ...item,
      id: item.savSeq,
    }
  })
  // 親画面.履歴IDが存在判断
  FaceSheetSelectedItem.value =
    dataList.find((item) => item.savSeq === localOneway.or10937.savSeq) ?? dataList[0]
}

/**
 * ﾌｪｰｽｼｰﾄ情報選択行
 *
 * @param item - ﾌｪｰｽｼｰﾄ情報選択行
 */
const clickFaceSheetSelectRow = (item: FaceSheetTableDataItem) => {
  FaceSheetSelectedItem.value = item
}

/**
 * ﾌｪｰｽｼｰﾄ情報の行をダブルクリックで選択
 *
 * @param item - ﾌｪｰｽｼｰﾄ情報選択行
 */
const doubleClickFaceSheetSelectRow = (item: FaceSheetTableDataItem) => {
  FaceSheetSelectedItem.value = item
  onConfirmBtn()
}

/**
 * ﾌｪｰｽｼｰﾄ情報選択行設定様式
 *
 * @param item - ﾌｪｰｽｼｰﾄ情報選択行
 */
const FaceSheetIsSelected = (item: { savSeq: null }) =>
  FaceSheetSelectedItem.value?.savSeq === item.savSeq

/**
 * 「確定」ボタン押下
 */
const onConfirmBtn = () => {
  // ﾌｪｰｽｼｰﾄデータがない場合、操作なし
  if (!FaceSheetSelectedItem.value) return
  // 選択情報値戻り
  emit('update:modelValue', FaceSheetSelectedItem.value)
  onClickCloseBtn()
}

/**
 * 閉じるボタン押下時
 */
const onClickCloseBtn = (): void => {
  setState({ isOpen: false })
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      onClickCloseBtn()
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet>
        <c-v-row no-gutters>
          <c-v-col
            cols="12"
            class="table-header"
          >
            <base-mo01334
              class="list-wrapper"
              hide-default-footer
              :oneway-model-value="localOneway.FaceSheetMo01334"
            >
              <template #item="{ item }">
                <tr
                  :class="{ 'selected-row': FaceSheetIsSelected(item) }"
                  @click="clickFaceSheetSelectRow(item)"
                  @dblclick="doubleClickFaceSheetSelectRow(item)"
                >
                  <td>
                    <!-- 作成日 -->
                    <span>{{ item.shoriYmd }}</span>
                  </td>
                  <td>
                    <!-- 作成者 -->
                    <span>{{ item.shokuName }}</span>
                  </td>
                </tr>
              </template>
            </base-mo01334>
          </c-v-col>
        </c-v-row>
      </c-v-sheet>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row
        no-gutters
        class="text-right"
      >
        <c-v-col>
          <c-v-spacer />
          <!-- 閉じるボタン -->
          <base-mo00611
            :oneway-model-value="mo00611Oneway"
            class="mr-2"
            @click="onClickCloseBtn"
          >
          </base-mo00611>
          <!-- 確定ボタン-->
          <base-mo00609
            :oneway-model-value="mo00609Oneway"
            @click="onConfirmBtn"
          >
          </base-mo00609>
        </c-v-col>
      </c-v-row>
    </template>
  </base-mo00024>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';
</style>
