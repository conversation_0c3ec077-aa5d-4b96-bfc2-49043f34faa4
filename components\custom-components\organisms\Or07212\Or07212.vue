<script setup lang="ts">
/**
 * Or07212:日割利用期間ダイアログ
 * GUI04473_日割利用期間
 *
 * @description
 * 日割利用期間ダイアログ
 *
 * <AUTHOR> 董永強
 */
import { reactive, ref, watch, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { OrT0001Logic } from '../OrT0001/OrT0001.logic'
import { OrT0001Const } from '../OrT0001/OrT0001.constants'
import type { OrT0001OneWayType, OrT0001Type } from '../OrT0001/OrT0001.type'
import { Or07212Const } from './Or07212.constants'
import type { Or07212StateType, DailyRateUsingPeriod } from './Or07212.type'
import { Or36647Const } from '~/components/custom-components/organisms/Or36647/Or36647.constants'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import { useScreenOneWayBind, useSetupChildProps } from '~/composables/useComponentVue'
import { Or21735Const } from '~/components/base-components/organisms/Or21735/Or21735.constants'
import { Or21735Logic } from '~/components/base-components/organisms/Or21735/Or21735.logic'
import { Or21738Const } from '~/components/base-components/organisms/Or21738/Or21738.constants'
import { Or21738Logic } from '~/components/base-components/organisms/Or21738/Or21738.logic'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo01276OnewayType } from '~/types/business/components/Mo01276Type'
import type {
  UseSlipDailyRateUsePeriodInitInfoSelectInEntity,
  UseSlipDailyRateUsePeriodInitInfoSelectOutEntity,
} from '~/repositories/cmn/entities/UseSlipDailyRateUsePeriodInitInfoSelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Or07212OnewayType } from '~/types/cmn/business/components/Or07212Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Or21814OnewayType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import { hasViewAuth } from '~/utils/useCmnAuthz'
import type { UseSlipDailyRateUsePeriodInfoUpdateInEntity } from '~/repositories/cmn/entities/UseSlipDailyRateUsePeriodInfoUpdateEntity'

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or07212OnewayType
  uniqueCpId: string
}

// 引継情報を取得する
const props = defineProps<Props>()

const local = reactive({
  dailyRateUsingPeriodList: [] as DailyRateUsingPeriod[],
  orT0001: {
    officeDataList: [],
    termid: 0,
  } as OrT0001Type,
})

const localOneway = reactive({
  or07212: {
    ...props.onewayModelValue,
  },
  // 閉じるボタン
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 保存ボタン
  mo00609OneWay: {
    btnLabel: t('btn.save'),
  } as Mo00609OnewayType,
  //日割利用期間ダイアログ
  mo00024Oneway: {
    width: '550px',
    height: '630px',
    persistent: true,
    showCloseBtn: true,
    disabledCloseBtnEvent: true,
    mo01344Oneway: {
      name: 'Or07212',
      toolbarTitle: t('label.daily-rate-using-period'),
      toolbarName: 'Or07212ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  // 事業所選択アイコンボタン
  mo00009Oneway: {
    btnIcon: 'edit_square',
    density: 'compact',
    disabled: false,
  } as Mo00009OnewayType,
  // 利用日
  mo01276Oneway: {
    readonly: true,
  } as Mo01276OnewayType,
  // 事業所選択
  orT0001Oneway: {} as OrT0001OneWayType,
})

/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or07212Const.DEFAULT.IS_OPEN,
})
const or21735 = ref({ uniqueCpId: Or21735Const.CP_ID(1) })
const or21738 = ref({ uniqueCpId: Or21738Const.CP_ID(1) })
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(1) })
const orT0001 = ref({ uniqueCpId: OrT0001Const.CP_ID(0) })

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21735Const.CP_ID(0)]: or21735.value,
  [Or21738Const.CP_ID(1)]: or21738.value,
  [Or21814Const.CP_ID(2)]: or21814.value,
  [OrT0001Const.CP_ID(0)]: orT0001.value,
})

const selectItemIndex = ref(0)
const isEdit = ref(false)

// テーブルヘッダ
const header = [
  {
    title: t('label.office-name'),
    key: 'jigyoNm',
    width: '213px',
    align: 'center',
    sortable: true,
  },
  {
    title: t('label.utilization-start-date'),
    key: 'startYmd',
    width: '135px',
    align: 'center',
    sortable: true,
    sortRaw(a: DailyRateUsingPeriod, b: DailyRateUsingPeriod) {
      return a.startYmd.value.localeCompare(b.startYmd.value)
    },
  },
  {
    title: t('label.utilization-end-date'),
    key: 'endYmd',
    width: '135px',
    align: 'center',
    sortable: true,
    sortRaw(a: DailyRateUsingPeriod, b: DailyRateUsingPeriod) {
      return a.endYmd.value.localeCompare(b.endYmd.value)
    },
  },
]
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or07212StateType>({
  cpId: Or07212Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value?: boolean) => {
      mo00024.value.isOpen = value ?? Or07212Const.DEFAULT.IS_OPEN
    },
  },
})

onMounted(async () => {
  // 日割利用期間初期情報取得
  await getDailyRateUsingPeriodList()
})

// 日割利用期間
const dailyRateUsingPeriodList = computed(() => {
  return local.dailyRateUsingPeriodList.filter(
    (item) => item.updKbn !== Or07212Const.UPDATE_CATEGORY_DELETE
  )
})

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
async function close() {
  // 明細データの変更がない場合
  if (!isEdit.value) {
    setState({ isOpen: false })
  } else {
    const ret = await openConfirmDialog({
      dialogTitle: t('label.confirm'),
      dialogText: t('message.i-cmn-11142', [t('label.daily-rate-using-period')]),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })
    // 画面入力データの変更がある場合
    if (isEdit.value) {
      // はい：処理継続
      if (ret === Or36647Const.CONFIRM_BTN_YES) {
        // 保存処理
        const saveRet = await saveDailyRateUsingPeriod()
        if (saveRet) {
          // 本画面を閉じる。
          setState({ isOpen: false })
        }
      } else if (ret === Or36647Const.CONFIRM_BTN_NO) {
        // いいえ：本画面を閉じる、処理終了
        setState({ isOpen: false })
        return false
      } else {
        // キャンセル：処理終了
        return false
      }
    }
  }
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value,
  async (newValue) => {
    if (newValue.emitType === Or07212Const.EMIT_TYPE_CLOSE_BTN_CLICK) {
      await close()
    }
  }
)

// 「行追加ボタン」押下
watch(
  () => Or21735Logic.event.get(or21735.value.uniqueCpId),
  () => {
    addNewLine()
  }
)

// 「行削除ボタン」押下
watch(
  () => Or21738Logic.event.get(or21738.value.uniqueCpId),
  async () => {
    await deleteLine()
  }
)

// 事業所選択ダイアログ閉じる
watch(
  () => OrT0001Logic.state.get(orT0001.value.uniqueCpId)?.isOpen,
  (newValue) => {
    if (!newValue) {
      if (local.orT0001.officeDataList.length > 0) {
        dailyRateUsingPeriodList.value[selectItemIndex.value].jigyoId =
          local.orT0001.officeDataList[0].officeId
        dailyRateUsingPeriodList.value[selectItemIndex.value].jigyoNm =
          local.orT0001.officeDataList[0].officeName
      } else {
        dailyRateUsingPeriodList.value[selectItemIndex.value].jigyoId =
          Or07212Const.OFFICE_NO_SELECTED
        dailyRateUsingPeriodList.value[selectItemIndex.value].jigyoNm = ''
      }
      dailyRateUsingPeriodList.value[selectItemIndex.value].updKbn =
        Or07212Const.UPDATE_CATEGORY_UPDATE
      isEdit.value = true
    }
  }
)

/**
 * 利用開始日/終了日更新
 *
 * @param index -index
 */
function changeYmd(index: number) {
  dailyRateUsingPeriodList.value[index].updKbn = Or07212Const.UPDATE_CATEGORY_UPDATE
  isEdit.value = true
}

/**
 * 行追加
 */
function addNewLine() {
  const newLine = {
    userid: localOneway.or07212.userId,
    jigyoId: Or07212Const.OFFICE_NO_SELECTED,
    jigyoNm: '',
    startYmd: {
      value: '',
    },
    endYmd: {
      value: '',
    },
    updKbn: Or07212Const.UPDATE_CATEGORY_NEW,
  } as DailyRateUsingPeriod
  local.dailyRateUsingPeriodList.push(newLine)
  selectItemIndex.value = dailyRateUsingPeriodList.value.length - 1
  isEdit.value = true
}

/**
 * 行削除
 */
async function deleteLine() {
  const ret = await openConfirmDialog({
    dialogTitle: t('label.confirm'),
    dialogText: t('message.i-cmn-11450'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'normal3',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'blank',
  })
  if (ret === Or36647Const.CONFIRM_BTN_YES) {
    dailyRateUsingPeriodList.value[selectItemIndex.value].updKbn =
      Or07212Const.UPDATE_CATEGORY_DELETE
    if (selectItemIndex.value === dailyRateUsingPeriodList.value.length) {
      selectItemIndex.value = dailyRateUsingPeriodList.value.length - 1
    }
    isEdit.value = true
  }
}

/**
 * 確認ダイアログ表示
 *
 * @param state -state
 *
 * @returns ダイアログの選択結果（yes, no, cancel)
 */
async function openConfirmDialog(state: Or21814OnewayType): Promise<'yes' | 'no' | 'cancel'> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)
        let result = 'no' as 'yes' | 'no' | 'cancel'
        if (event?.firstBtnClickFlg) {
          result = Or36647Const.CONFIRM_BTN_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or36647Const.CONFIRM_BTN_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = Or36647Const.CONFIRM_BTN_CANCEL
        }
        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 利用票行選択
 *
 * @param index - 選択した行のindex
 */
function selectRow(index: number) {
  selectItemIndex.value = index
}

// 事業所選択ダイアログ表示フラグ
const showDialogOrT0001 = computed(() => {
  return OrT0001Logic.state.get(orT0001.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 事業所選択ダイアログ表示
 *
 * @param index - 選択した行のindex
 */
function onClickDialog(index: number) {
  selectItemIndex.value = index
  OrT0001Logic.state.set({
    uniqueCpId: 'OrT0001',
    state: { isOpen: true },
  })
}

/**
 * 「保存ボタン」押下
 */
async function saveDailyRateUsingPeriod() {
  const state = {
    dialogTitle: t('label.confirm'),
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.ok'),
    secondBtnType: 'blank',
    thirdBtnType: 'blank',
  } as Or21814OnewayType

  // 入力項目が変更なく、そのまま保存ボタン押下した場合
  if (!isEdit.value) {
    state.dialogText = t('message.i-cmn-21800')
    await openConfirmDialog(state)
    return false
  }

  // 閲覧権限
  const hasView: boolean = await hasViewAuth('/care-manager/using-slip')
  // 閲覧権限がない場合
  if (hasView === false) {
    state.dialogText = t('message.i-cmn-11451')
    await openConfirmDialog(state)
    return false
  }

  const dailyRateList = []
  for (let i = 0; i < dailyRateUsingPeriodList.value.length; i++) {
    const dailyRateUsingPeriod = dailyRateUsingPeriodList.value[i]
    // 事業所未入力の場合
    if (
      dailyRateUsingPeriod.jigyoId === null ||
      dailyRateUsingPeriod.jigyoId === Or07212Const.OFFICE_NO_SELECTED
    ) {
      state.dialogText = t('message.e-com-10030')
      await openConfirmDialog(state)
      return false
    }

    // 利用開始日未入力の場合
    if (dailyRateUsingPeriod.startYmd.value === '') {
      state.dialogText = t('message.e-com-10036', [t('label.utilization-start-date')])
      await openConfirmDialog(state)
      return false
    }

    if (dailyRateUsingPeriod.endYmd.value !== '') {
      // 利用開始日＜＝ 利用終了日の場合
      const startYmd = new Date(dailyRateUsingPeriod.startYmd.value)
      const endYmd = new Date(dailyRateUsingPeriod.endYmd.value)
      if (startYmd <= endYmd) {
        state.dialogText = t('message.i-cmn-11453')
        await openConfirmDialog(state)
        return false
      }

      // 期間の重複有り場合
      const findIndex = dailyRateUsingPeriodList.value
        .filter((item, index) => index !== i)
        .findIndex(
          (item) =>
            item.startYmd.value === dailyRateUsingPeriod.startYmd.value &&
            item.endYmd.value === dailyRateUsingPeriod.endYmd.value
        )

      if (findIndex >= 0) {
        state.dialogText = t('message.i-cmn-11454')
        await openConfirmDialog(state)
        return false
      }
    }

    dailyRateList.push({
      ...dailyRateUsingPeriod,
      updateKbn: dailyRateUsingPeriod.updKbn,
      startYmd: dailyRateUsingPeriod.startYmd.value,
      endYmd: dailyRateUsingPeriod.endYmd.value,
    })
  }

  // 日割利用期間保存処理
  const inputData: UseSlipDailyRateUsePeriodInfoUpdateInEntity = {
    // 日割利用期間一覧情報
    dailyRateList: dailyRateList,
  }
  await ScreenRepository.update('useSlipDailyRateUsePeriodInfoUpdate', inputData)

  // 画面再表示
  await getDailyRateUsingPeriodList()
  return true
}

/**
 * 初期表示
 */
async function getDailyRateUsingPeriodList() {
  const inputData: UseSlipDailyRateUsePeriodInitInfoSelectInEntity = {
    // 利用者ID：親画面.利用者ID
    userId: localOneway.or07212.userId,
  }

  const ret: UseSlipDailyRateUsePeriodInitInfoSelectOutEntity = await ScreenRepository.select(
    'useSlipDailyRateUsePeriodInitInfoSelect',
    inputData
  )

  local.dailyRateUsingPeriodList.length = 0
  for (const dailyRateUsingPeriod of ret.data.dailyRateInfo[0].dailyRateList) {
    local.dailyRateUsingPeriodList.push({
      ...dailyRateUsingPeriod,
      startYmd: {
        value: dailyRateUsingPeriod.startYmd,
      },
      endYmd: {
        value: dailyRateUsingPeriod.endYmd,
      },
      updKbn: '',
    })
  }
  isEdit.value = false
}
</script>

<template>
  <!-- ダイアログ -->
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row no-gutters>
        <c-v-col
          cols="12"
          class="d-flex"
        >
          <!-- 行追加ボタン -->
          <div class="mr-2">
            <g-base-or-21735 v-bind="or21735" />
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.care-plan2-newline-btn')"
            />
          </div>
          <!-- 行削除ボタン -->
          <div>
            <g-base-or-21738 v-bind="or21738" />
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.care-plan2-deleteline-btn')"
            />
          </div>
        </c-v-col>
      </c-v-row>
      <c-v-row no-gutters>
        <c-v-col
          cols="12"
          class="d-flex"
          style="max-height: 368px; min-height: 368px"
        >
          <!-- 日割利用期間一覧 -->
          <c-v-data-table
            class="table-wrapper"
            hide-default-footer
            :headers="header"
            :items="dailyRateUsingPeriodList"
            fixed-header
            hover
            :items-per-page="-1"
          >
            <!-- 事業所 -->
            <template #[`header.jigyoNm`]="{ column, isSorted, getSortIcon }">
              <div class="d-flex justify-center align-center">
                <span class="font-red">*</span>
                <span>{{ column.title }}</span>
                <template v-if="isSorted(column)">
                  <base-at-icon :icon="getSortIcon(column)" />
                </template>
              </div>
            </template>
            <!-- 利用開始日 -->
            <template #[`header.startYmd`]="{ column, isSorted, getSortIcon }">
              <div class="d-flex justify-center align-center">
                <span class="font-red">*</span>
                <span>{{ column.title }}</span>
                <template v-if="isSorted(column)">
                  <base-at-icon :icon="getSortIcon(column)" />
                </template>
              </div>
            </template>
            <!-- 利用終了日 -->
            <template #[`header.endYmd`]="{ column, isSorted, getSortIcon }">
              <div class="d-flex justify-center align-center">
                <span>{{ column.title }}</span>
                <template v-if="isSorted(column)">
                  <base-at-icon :icon="getSortIcon(column)" />
                </template>
              </div>
            </template>
            <template #item="{ item, index }">
              <tr
                :class="{ 'select-row': selectItemIndex === index }"
                @click="selectRow(index)"
              >
                <td class="pa-0">
                  <base-mo00009
                    :oneway-model-value="localOneway.mo00009Oneway"
                    @click="onClickDialog(index)"
                  />
                  {{ item.jigyoNm }}
                </td>
                <td class="pa-0">
                  <base-mo01276
                    v-model="item.startYmd"
                    :oneway-model-value="localOneway.mo01276Oneway"
                    @update:model-value="changeYmd(index)"
                  />
                </td>
                <td class="pa-0">
                  <base-mo01276
                    v-model="item.endYmd"
                    :oneway-model-value="localOneway.mo01276Oneway"
                    @update:model-value="changeYmd(index)"
                  />
                </td>
              </tr>
            </template>
          </c-v-data-table>
        </c-v-col>
      </c-v-row>
      <c-v-row no-gutters>
        <c-v-col cols="12">
          <div class="font-blue">
            {{ t('label.daily-rate-using-period-tips1') }}
          </div>
          <div class="pl-4 font-blue">
            {{ t('label.daily-rate-using-period-tips2') }}
          </div>
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          class="mx-2"
          @click="close"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <!-- 保存ボタン Mo00609 -->
        <base-mo00609
          v-bind="localOneway.mo00609OneWay"
          class="mr-2"
          @click="saveDailyRateUsingPeriod"
        >
          <!--ツールチップ表示："設定を確定します。"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.confirm-btn')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- 確認ダイアログ -->
  <g-base-or-21814 v-bind="or21814" />
  <g-custom-orT-0001
    v-if="showDialogOrT0001"
    v-bind="orT0001"
    v-model="local.orT0001"
    :oneway-model-value="localOneway.orT0001Oneway"
  />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table.scss';

.font-red {
  color: red;
}

.font-blue {
  color: blue;
}

:deep(.v-data-table-rows-no-data) {
  display: none !important;
}
</style>
