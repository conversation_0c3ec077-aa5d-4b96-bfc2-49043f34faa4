<script setup lang="ts">
/**
 * OrX0130：有機体：印刷設定画面利用者一覧
 *
 * @description
 * 印刷設定画面利用者一覧
 *
 * <AUTHOR>
 */
import { nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import type { CodeType } from '../Or28326/Or28326.type'
import { OrX0130Const } from './OrX0130.constants'
import type { OrX0130EventType, OrX0130TableType, TableSelectType } from './OrX0130.type'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import type { Mo01334OnewayType, Mo01334Type } from '~/types/business/components/Mo01334Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { CustomClass } from '~/types/CustomClassType'
import { Or00094Const } from '~/components/base-components/organisms/Or00094/Or00094.constants'
import { useScreenEventStatus, useSetupChildProps } from '#imports'
import { Or00094Logic } from '~/components/base-components/organisms/Or00094/Or00094.logic'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { convHiraganaToHalfKanaMap } from '~/constants/KanaMap'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import type {
  IUserListSelectOutEntity,
  IUserListSelectInEntity,
  displaySettingType,
} from '~/components/base-components/organisms/Or00248/UserListSelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { userListItemType } from '~/components/base-components/organisms/Or00249/Or00249.type'
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: OrX0130OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()
// 子コンポーネント用変数
const or00094 = ref({ uniqueCpId: '' })
const defaultOnewayModelValue: OrX0130OnewayType = {
  // 選択モート: 単一
  selectMode: OrX0130Const.DEFAULT.TANI,
  // 列幅
  tableStyle: 'width:423px',
  // 複数選択時、50音の選択数を表示するか
  showKanaSelectionCount: false,
  // 利用者一覧データネーム
  userListDataName: OrX0130Const.DEFAULT.USER_LIST_DATA_NAME,
}

const localOneway = reactive({
  orX0130Oneway: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // 利用者一覧(単一)
  mo01334OnewayUser: {
    headers: [
      // 利用者名
      {
        title: t('label.user-name'),
        key: 'userName',
        sortable: false,
        minWidth: '141',
      },
      // 利用者番号
      {
        title: t('label.user-number'),
        key: 'userNumber',
        sortable: false,
        minWidth: '130',
      },
      // 性別
      {
        title: t('label.gender'),
        key: 'userGender',
        sortable: false,
        minWidth: '80',
      },
    ],
    items: [],
    height: 475,
    sticky: false,
  } as Mo01334OnewayType,
  // 利用者一覧(複数)
  mo01334OnewayUserHukusuu: {
    headers: [
      // 利用者名
      {
        title: t('label.user-name'),
        key: 'userName',
        sortable: false,
        minWidth: '141',
      },
      // 利用者番号
      {
        title: t('label.user-number'),
        key: 'userNumber',
        sortable: false,
        minWidth: '130',
      },
      // 性別
      {
        title: t('label.gender'),
        key: 'userGender',
        sortable: false,
        minWidth: '80',
      },
    ],
    items: [],
    height: 475,
    showSelect: true,
    selectStrategy: 'all',
    sticky: false,
    mandatory: false,
  } as Mo01334OnewayType,
  // 利用者合計(単一)
  mo01337OnewayUserGokei: {
    value: '',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: 'gokeiClass',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  // 利用者合計(複数)
  mo01337OnewayUserGokeiHukusuu: {
    value: '',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: 'gokeiClass',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  // 性別がコード一覧
  sexCdList: [] as CodeType[],
})

/**
 * 選択したの利用者一覧
 */
const mo01334TypeUser = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)
// グローバル単一選択フラグ
const selectTaniFlag = ref<boolean>(false)
/** 50音ヘッドラインのフィルター適用後の利用者一覧 */
const appliedFilterUserList = ref([] as OrX0130TableType[])
// 全部の利用者一覧
const workAllUserList = ref([] as OrX0130TableType[])
// テーブルの選択情報
const selectInfo = ref<TableSelectType[]>([])
// 50音選択情報
const or00094SelectInfo = ref<string[]>([])
// 初期化フラグ
const isInit = ref<boolean>(false)
/**************************************************
 * Pinia
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or00094Const.CP_ID(0)]: or00094.value,
})
const { setEvent } = useScreenEventStatus<OrX0130EventType>({
  cpId: OrX0130Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(async () => {
  await init()
})

/**
 * 初期化
 */
async function init() {
  isInit.value = true
  // グローバル単一選択フラグ初期化
  selectTaniFlag.value = false
  /** 50音ヘッドラインのフィルター適用後の利用者一覧初期化 */
  appliedFilterUserList.value = []
  // 全部の利用者一覧初期化
  workAllUserList.value = []
  // テーブルの選択情報初期化
  selectInfo.value = []
  // 50音選択情報初期化
  or00094SelectInfo.value = []
  // DataNameが設定
  if (localOneway.orX0130Oneway.userListDataName === '') {
    localOneway.orX0130Oneway.userListDataName = OrX0130Const.DEFAULT.USER_LIST_DATA_NAME
  }
  // 汎用コードマスタデータを取得し初期化
  await initCodes()
  // 一覧情報取得関数を呼び出し
  let workGetUserSelectUserList: userListItemType[] = []
  // 一覧情報取得関数（データネーム指定用）を呼び出し
  workGetUserSelectUserList = (await syscomUserListFunc()).data.userList
  // テーブルのデータ
  const data: OrX0130TableType[] = []
  let id = 0
  for (const userInfo of workGetUserSelectUserList ?? []) {
    data.push({
      id: String(id++),
      userId: id.toString(),
      name1Knj: userInfo.nameSei,
      name2Knj: userInfo.nameMei,
      name1Kana: userInfo.nameKanaSei,
      name2Kana: userInfo.nameKanaMei,
      sex: userInfo.gender.toString(),
      selfId: userInfo.selfId,
      selectable: true,
      displSex: getSexInfo(userInfo.gender.toString()).label,
    } as OrX0130TableType)
  }
  workAllUserList.value = [...data]
  // グローバル単一／複数選択フラグ設定
  if (OrX0130Const.DEFAULT.TANI === localOneway.orX0130Oneway.selectMode) {
    selectTaniFlag.value = true
    localOneway.mo01334OnewayUser.items = data
  } else {
    localOneway.mo01334OnewayUserHukusuu.items = data
  }
  localOneway.mo01337OnewayUserGokei.value = t('label.print-user-gokei-total', [
    localOneway.mo01334OnewayUser.items.length,
  ])
  if (localOneway.orX0130Oneway.showKanaSelectionCount) {
    localOneway.mo01337OnewayUserGokeiHukusuu.value = t('label.print-user-gokei-Hukusuu-total', [
      mo01334TypeUser.value.values.length,
      localOneway.mo01334OnewayUserHukusuu.items.length,
      mo01334TypeUser.value.values.length,
      localOneway.mo01334OnewayUserHukusuu.items.length,
    ])
  } else {
    localOneway.mo01337OnewayUserGokeiHukusuu.value = t('label.print-user-gokei-total', [
      localOneway.mo01334OnewayUserHukusuu.items.length,
    ])
  }
  //利用者一覧明細の1件目を選択状態にする。
  if (
    OrX0130Const.DEFAULT.TANI === localOneway.orX0130Oneway.selectMode &&
    workAllUserList.value.length > 0 &&
    mo01334TypeUser.value.value === ''
  ) {
    if (localOneway.orX0130Oneway.userId) {
      mo01334TypeUser.value.value =
        workAllUserList.value[
          workAllUserList.value.findIndex(
            (item) => item.userId === localOneway.orX0130Oneway.userId
          )
        ].id
    } else {
      mo01334TypeUser.value.value = workAllUserList.value[0].id
    }
  }
  // 50音ヘッドラインの表示設定ボタンを表示
  if (
    localOneway.orX0130Oneway.focusSettingInitial &&
    Array.isArray(localOneway.orX0130Oneway.focusSettingInitial) &&
    localOneway.orX0130Oneway.focusSettingInitial.length > 0
  ) {
    Or00094Logic.state.set({
      uniqueCpId: or00094.value.uniqueCpId,
      state: {
        dispSettingBtnDisplayFlg: false,
        focusSettingFlg: true,
        focusSettingInitial: localOneway.orX0130Oneway.focusSettingInitial,
      },
    })
    calcAppliedFilterUserList(localOneway.orX0130Oneway.focusSettingInitial)
    //画面.利用者一覧明細と親画面.利用者IDを対するレコードを選択状態にする。
    if (
      OrX0130Const.DEFAULT.TANI === localOneway.orX0130Oneway.selectMode &&
      localOneway.orX0130Oneway.userId
    ) {
      const indexId = localOneway.mo01334OnewayUser.items.findIndex(
        (item) => item.userId === localOneway.orX0130Oneway.userId
      )
      if (indexId >= 0) {
        mo01334TypeUser.value.value = localOneway.mo01334OnewayUser.items[indexId].id
      }
    }
  } else {
    Or00094Logic.state.set({
      uniqueCpId: or00094.value.uniqueCpId,
      state: {
        dispSettingBtnDisplayFlg: false,
        focusSettingFlg: true,
        focusSettingInitial: [OrX0130Const.DEFAULT.STR_ALL],
      },
    })
  }
  // 選択したの利用者
  let userIds: string[] = []
  if (
    OrX0130Const.DEFAULT.TANI === localOneway.orX0130Oneway.selectMode &&
    mo01334TypeUser.value.value !== ''
  ) {
    userIds = [mo01334TypeUser.value.value]
  }
  if (
    OrX0130Const.DEFAULT.HUKUSUU === localOneway.orX0130Oneway.selectMode &&
    mo01334TypeUser.value.values.length > 0
  ) {
    userIds = userIds.concat(mo01334TypeUser.value.values)
  }
  userIds = [...new Set(userIds)]
  const userList = workAllUserList.value.filter((item) => userIds.includes(item.id))
  await nextTick()
  setEvent({
    clickFlg: true,
    userList: userList,
  })
  isInit.value = false
}

/**
 * 一覧情報取得関数
 *
 * @description
 * OrX0130（利用者一覧詳細表示）はこの関数を呼ぶことで、対象となる利用者一覧を表示できる。
 * また、他コンポーネントはこの関数を呼ぶことで利用者選択欄を最新の状態に更新することができる。
 */
const syscomUserListFunc = async () => {
  // 最新の選択されている事業所ID
  const saishinSvJigyoIdList = ref([] as string[])
  if (systemCommonsStore.getSvJigyoIdList !== undefined) {
    saishinSvJigyoIdList.value = systemCommonsStore.getSvJigyoIdList as string[] // 適用事業所ID
  }
  const requestInput = {
    svJigyoIdList: saishinSvJigyoIdList.value, // 選択されている事業所IDのリスト
    displaySetting: localOneway.orX0130Oneway.tantouCareManager
      ? ({
          filterSetting: {
            tantouCareManager: {
              checkFlg: true,
              value: localOneway.orX0130Oneway.tantouCareManager,
            },
          },
        } as displaySettingType)
      : undefined, // 表示設定の条件
  } as IUserListSelectInEntity
  // APIにリクエストを送信し、利用者一覧情報をDBから取得
  const res: IUserListSelectOutEntity = await ScreenRepository.select(
    localOneway.orX0130Oneway.userListDataName ?? OrX0130Const.DEFAULT.USER_LIST_DATA_NAME,
    requestInput
  )
  return res
}
/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 性別がコード一覧
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_GENDER_CATEGORY },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  // 性別がコード一覧
  localOneway.sexCdList = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_GENDER_CATEGORY)
}

/**
 * 性別情報の取得
 *
 * @param sexCd - 性別コート
 */
function getSexInfo(sexCd: string) {
  const data = localOneway.sexCdList.filter((item) => item.value === sexCd)
  if (Array.isArray(data) && data.length > 0) {
    return data[0]
  }
  return { label: '', value: '' }
}

/**
 * 50音ヘッドラインのフィルター適用後の利用者一覧を算出し、ローカル変数に設定します。
 * また、システム共有領域．利用者選択領域の更新も行います。
 *
 * @param or00094Select - 50音
 */
function calcAppliedFilterUserList(or00094Select: string[]) {
  or00094SelectInfo.value = or00094Select
  // システム共有領域．利用者選択領域から、今保持している利用者一覧情報の全量を取得
  appliedFilterUserList.value = [] as OrX0130TableType[]

  // システム共有領域．利用者選択領域から、50音ヘッドラインの選択値を取得
  const workFilterInitials = ref([] as string[])
  const workGetUserSelectFilterInitials = or00094Select
  if (workGetUserSelectFilterInitials !== undefined) {
    workFilterInitials.value = workGetUserSelectFilterInitials // readonlyを外すためにasが必要
  }

  if (workFilterInitials.value === undefined || workFilterInitials.value?.length === 0) {
    // 50音ヘッドラインのフィルターが設定値なしの場合、
    // 利用者一覧は全量を設定する
    if (workAllUserList.value !== undefined) {
      appliedFilterUserList.value = workAllUserList.value
    }
  } else if (
    workFilterInitials.value.length > 0 &&
    workFilterInitials.value[0] === OrX0130Const.DEFAULT.STR_ALL
  ) {
    // 50音ヘッドラインで「全」が選択された場合
    appliedFilterUserList.value = workAllUserList.value
  } else if (
    workFilterInitials.value.length > 0 &&
    workFilterInitials.value[0] === OrX0130Const.DEFAULT.STR_OTHER
  ) {
    // 50音ヘッドラインで「他」が選択された場合
    const workUserList = workAllUserList.value.filter((workUser) => {
      // 読み仮名の1文字目がひらがなではなければtrue
      const regex = /^[ぁ-ん]+$/u
      if (!regex.test(workUser.name1Kana.charAt(0))) {
        return true
      }
    })

    // ヒットしたユーザーを"50音ヘッドラインのフィルター適用後の利用者一覧"に追加
    workUserList.forEach((workUser) => {
      appliedFilterUserList.value.push(workUser)
    })
  } else {
    // 50音ヘッドラインのフィルターにヒットする利用者を設定する
    if (workAllUserList.value !== undefined) {
      workFilterInitials.value.forEach((initialValue) => {
        const workUserList = workAllUserList.value.filter((workUser) => {
          // 読み仮名がヒットすればtrue
          if (yomiHitCheck(workUser.name1Kana, initialValue)) {
            return true
          }
        })

        // ヒットしたユーザーを"50音ヘッドラインのフィルター適用後の利用者一覧"に追加
        workUserList.forEach((workUser) => {
          appliedFilterUserList.value.push(workUser)
        })
      })
    }
  }
  if (selectTaniFlag.value) {
    localOneway.mo01334OnewayUser.items = appliedFilterUserList.value
    localOneway.mo01337OnewayUserGokei.value = t('label.print-user-gokei-total', [
      localOneway.mo01334OnewayUser.items.length,
    ])
    //利用者一覧明細の1件目を選択状態にする。
    if (
      OrX0130Const.DEFAULT.TANI === localOneway.orX0130Oneway.selectMode &&
      appliedFilterUserList.value.length > 0
    ) {
      mo01334TypeUser.value.value = appliedFilterUserList.value[0].id
    }
  } else {
    localOneway.mo01334OnewayUserHukusuu.items = appliedFilterUserList.value
    // 「あかさたなはまやらわ」を選択した場合
    if (or00094SelectInfo.value.length > 1) {
      const filteredObjArray = selectInfo.value.filter((item) =>
        or00094SelectInfo.value.includes(item.label)
      )
      let count = 0
      filteredObjArray.forEach((item) => {
        count += item.count
      })
      if (localOneway.orX0130Oneway.showKanaSelectionCount) {
        localOneway.mo01337OnewayUserGokeiHukusuu.value = t(
          'label.print-user-gokei-Hukusuu-total',
          [
            count,
            localOneway.mo01334OnewayUserHukusuu.items.length,
            mo01334TypeUser.value.values.length,
            workAllUserList.value.length,
          ]
        )
      }
    } else if (
      or00094SelectInfo.value.length > 0 &&
      !['', OrX0130Const.DEFAULT.STR_ALL, OrX0130Const.DEFAULT.STR_OTHER].includes(
        or00094SelectInfo.value[0]
      )
    ) {
      const filteredObj = selectInfo.value.find((item) => item.label === or00094SelectInfo.value[0])
      if (localOneway.orX0130Oneway.showKanaSelectionCount) {
        localOneway.mo01337OnewayUserGokeiHukusuu.value = t(
          'label.print-user-gokei-Hukusuu-total',
          [
            filteredObj?.count ?? 0,
            localOneway.mo01334OnewayUserHukusuu.items.length,
            mo01334TypeUser.value.values.length,
            workAllUserList.value.length,
          ]
        )
      }
    } else if (localOneway.orX0130Oneway.showKanaSelectionCount) {
      localOneway.mo01337OnewayUserGokeiHukusuu.value = t('label.print-user-gokei-Hukusuu-total', [
        mo01334TypeUser.value.values.length,
        localOneway.mo01334OnewayUserHukusuu.items.length,
        mo01334TypeUser.value.values.length,
        workAllUserList.value.length,
      ])
    }
  }
}

/**
 * 読み仮名ヒットチェック
 * 対象ワードの1文字目と、検索文字の値が一致するか比較します。
 * 比較の際は、濁点・半濁点を無視するために半角カタカナで比較します。
 * true : 一致する
 * false : 一致しない
 *
 * @param targetWord - 対象ワード
 *
 * @param searchChar - 検索文字（50音ヘッドラインの1文字）
 */
function yomiHitCheck(targetWord: string, searchChar: string) {
  if (!(targetWord.charAt(0) in convHiraganaToHalfKanaMap)) {
    return false
  }
  if (!(searchChar in convHiraganaToHalfKanaMap)) {
    return false
  }

  // 対象ワードの1文字目の半角カタカナ
  const targetWordKana = convHiraganaToHalfKanaMap[targetWord.charAt(0)]

  // 検索文字の半角カタカナ
  const searchCharKana = convHiraganaToHalfKanaMap[searchChar.charAt(0)]

  // 濁点・半濁点を考慮して、1文字目を対象に比較
  if (targetWordKana.startsWith(searchCharKana.charAt(0))) {
    return true
  } else {
    return false
  }
}

/**
 * 50音図と利用者を紐付けして生成
 *
 * @param userIds - 利用者IDのリスト
 */
function or00094UserListMapping(userIds: string[]) {
  selectInfo.value = []
  OrX0130Const.DEFAULT.JP_ARRAY.forEach((word) => {
    const ids = userIds.filter((userId) => {
      const workUser = workAllUserList.value.find((item) => item.id === userId)
      // 読み仮名がヒットすればtrue
      if (workUser) {
        if (yomiHitCheck(workUser.name1Kana, word)) {
          return true
        }
      }
    })
    selectInfo.value.push({
      label: word,
      count: ids.length,
      ids: ids,
    })
  })
}

/**
 * propsの監視
 */
watch(
  () => props.onewayModelValue,
  (newVal) => {
    localOneway.orX0130Oneway.selectMode = newVal.selectMode
    localOneway.orX0130Oneway.tableStyle = newVal.tableStyle
    localOneway.orX0130Oneway.showKanaSelectionCount = newVal.showKanaSelectionCount
    localOneway.orX0130Oneway.focusSettingInitial = newVal.focusSettingInitial
    localOneway.orX0130Oneway.userListDataName = newVal.userListDataName
    localOneway.orX0130Oneway.userId = newVal.userId
    localOneway.orX0130Oneway.tantouCareManager = newVal.tantouCareManager
    // init
    void init()
  },
  { deep: true }
)
/**
 * 利用者選択
 */
watch(
  () => mo01334TypeUser.value,
  async (newValue, oldValue) => {
    await nextTick()
    if (isInit.value) {
      return
    }
    if (
      newValue.values.length === 0 &&
      oldValue.values.length > 0 &&
      workAllUserList.value.length > oldValue.values.length
    ) {
      if (!selectTaniFlag.value) {
        const currentTableUserIds = localOneway.mo01334OnewayUserHukusuu.items.map(
          (item) => item.id
        )
        mo01334TypeUser.value.values = oldValue.values.filter(
          (item) => !currentTableUserIds.includes(item)
        )
      }
    }
    // 選択したの利用者
    let userIds: string[] = []
    if (
      OrX0130Const.DEFAULT.TANI === localOneway.orX0130Oneway.selectMode &&
      mo01334TypeUser.value.value !== ''
    ) {
      userIds = [mo01334TypeUser.value.value]
    }
    if (
      OrX0130Const.DEFAULT.HUKUSUU === localOneway.orX0130Oneway.selectMode &&
      mo01334TypeUser.value.values.length > 0
    ) {
      userIds = userIds.concat(mo01334TypeUser.value.values)
    }
    userIds = [...new Set(userIds)]
    const filteredInfo = or00094SelectInfo.value.filter(
      (item) => !['', OrX0130Const.DEFAULT.STR_ALL, OrX0130Const.DEFAULT.STR_OTHER].includes(item)
    )

    or00094UserListMapping(userIds)
    if (localOneway.orX0130Oneway.showKanaSelectionCount) {
      if (filteredInfo.length > 0) {
        if (filteredInfo.length === 1) {
          const filteredObj = selectInfo.value.find((item) => item.label === filteredInfo[0])
          localOneway.mo01337OnewayUserGokeiHukusuu.value = t(
            'label.print-user-gokei-Hukusuu-total',
            [
              filteredObj?.count ?? 0,
              localOneway.mo01334OnewayUserHukusuu.items.length,
              mo01334TypeUser.value.values.length,
              workAllUserList.value.length,
            ]
          )
        } else {
          const filteredObjArray = selectInfo.value.filter((item) =>
            filteredInfo.includes(item.label)
          )
          let count = 0
          filteredObjArray.forEach((item) => {
            count += item.count
          })
          localOneway.mo01337OnewayUserGokeiHukusuu.value = t(
            'label.print-user-gokei-Hukusuu-total',
            [
              count,
              localOneway.mo01334OnewayUserHukusuu.items.length,
              mo01334TypeUser.value.values.length,
              workAllUserList.value.length,
            ]
          )
        }
      } else {
        localOneway.mo01337OnewayUserGokeiHukusuu.value = t(
          'label.print-user-gokei-Hukusuu-total',
          [
            mo01334TypeUser.value.values.length,
            localOneway.mo01334OnewayUserHukusuu.items.length,
            mo01334TypeUser.value.values.length,
            workAllUserList.value.length,
          ]
        )
      }
    }
    const userList = workAllUserList.value.filter((item) => userIds.includes(item.id))
    setEvent({
      clickFlg: true,
      userList: userList,
    })
  }
)
/**
 * 50音ヘッドラインコンポーネントのイベント監視
 */
watch(
  () => Or00094Logic.event.get(or00094.value.uniqueCpId),
  (newValue) => {
    if (newValue?.charBtnClickFlg) {
      // 50音ヘッドラインの文字ボタン押下イベントを検知
      const workFilterInitials = Or00094Logic.data.get(or00094.value.uniqueCpId)
      if (workFilterInitials?.selectValueArray !== undefined) {
        // システム共有領域．利用者選択領域に50音ヘッドラインの選択値を設定
        // 50音ヘッドラインのフィルター適用後の利用者一覧を算出
        calcAppliedFilterUserList(workFilterInitials.selectValueArray)
      }
    }
  }
)
</script>

<template>
  <c-v-row class="ma-0">
    <!-- マスタアイコンボタン -->
    <c-v-col class="d-flex pa-0">
      <!-- 50音選択 -->
      <div style="width: 30px; z-index: 3">
        <g-base-or-00094 v-bind="or00094" />
      </div>
      <div
        v-if="OrX0130Const.DEFAULT.TANI === localOneway.orX0130Oneway.selectMode"
        class="pl-2 bg-w"
      >
        <base-mo01338
          :oneway-model-value="localOneway.mo01337OnewayUserGokei"
          class="text-right pb-2 pt-2"
        />
        <base-mo-01334
          v-model="mo01334TypeUser"
          :oneway-model-value="localOneway.mo01334OnewayUser"
          class="list-wrapper"
          :style="localOneway.orX0130Oneway.tableStyle"
          fixed-footer
        >
          <!-- 利用者名 -->
          <template #[`item.userName`]="{ item }">
            <!-- 分子：一覧専用ラベル（文字列型） -->
            <base-mo01337
              :oneway-model-value="{
                value: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
              }"
            />
          </template>
          <!-- 利用者番号 -->
          <template #[`item.userNumber`]="{ item }">
            <!-- 分子：一覧専用ラベル（文字列型） -->
            <base-mo01337
              :oneway-model-value="{
                value: item.selfId,
                customClass: {
                  outerStyle: 'background-color: rgba(0, 0, 0, 0);',
                  itemClass: 'text-right',
                },
              }"
            />
          </template>
          <!-- 性別 -->
          <template #[`item.userGender`]="{ item }">
            <!-- 分子：一覧専用ラベル（文字列型） -->
            <base-mo01337
              :oneway-model-value="{
                value: item.displSex,
                valueFontColor: OrX0130Const.DEFAULT.MAN === item.displSex ? 'blue' : 'red',
              }"
              class="text-right"
            />
          </template>
          <!-- ページングを非表示 -->
          <template #bottom />
        </base-mo-01334>
      </div>
      <div
        v-else
        class="pl-2 bg-w"
      >
        <base-mo01338
          :oneway-model-value="localOneway.mo01337OnewayUserGokeiHukusuu"
          class="text-right pb-2 pt-2"
        />
        <base-mo-01334
          v-model="mo01334TypeUser"
          :oneway-model-value="localOneway.mo01334OnewayUserHukusuu"
          class="list-wrapper"
          :style="localOneway.orX0130Oneway.tableStyle"
          fixed-footer
        >
          <!-- 利用者名 -->
          <template #[`item.userName`]="{ item }">
            <!-- 分子：一覧専用ラベル（文字列型） -->
            <base-mo01337
              :oneway-model-value="{
                value: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
              }"
            />
          </template>
          <!-- 利用者番号 -->
          <template #[`item.userNumber`]="{ item }">
            <!-- 分子：一覧専用ラベル（文字列型） -->
            <base-mo01337
              :oneway-model-value="{
                value: item.selfId,
                customClass: {
                  outerStyle: 'background-color: rgba(0, 0, 0, 0);',
                  itemClass: 'text-right',
                },
              }"
            />
          </template>
          <!-- 性別 -->
          <template #[`item.userGender`]="{ item }">
            <!-- 分子：一覧専用ラベル（文字列型） -->
            <base-mo01337
              :oneway-model-value="{
                value: item.displSex,
                valueFontColor: OrX0130Const.DEFAULT.MAN === item.displSex ? 'blue' : 'red',
              }"
              class="text-right"
            />
          </template>
          <!-- ページングを非表示 -->
          <template #bottom />
        </base-mo-01334>
      </div>
    </c-v-col>
  </c-v-row>
</template>

<style scoped lang="scss">
.user-row {
  margin: 0px !important;
}

.bg-w {
  background: white;
}
:deep(.v-table .v-table__wrapper th) {
  font-size: 14px;
  font-weight: bold !important;
}
:deep(.v-table__wrapper > table > thead) {
  position: sticky !important;
  top: 0px !important;
  z-index: 2;
}
</style>
