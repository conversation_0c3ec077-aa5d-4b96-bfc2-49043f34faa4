<script setup lang="ts">
import { onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import type {
  AddImportContentsInfoType,
  HistroyInfoType,
  Or10900RadioType,
  Or10900StateType,
  OverwriteImportContentsInfoType,
  PlanPeriodInfoType,
  ReturnContentsInfoData,
} from './Or10900.type'
import { Or10900Const } from './Or10900.constants'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  PlanTableImportInitSelectInEntity,
  PlanTableImportInitSelectOutEntity,
} from '~/repositories/cmn/entities/PlanTableImportInitSelectEntity'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Or10900OnewayType } from '~/types/cmn/business/components/Or10900Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import { useScreenOneWayBind } from '#imports'
import { useSetupChildProps } from '#build/imports'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type {
  Mo01334Headers,
  Mo01334OnewayType,
  Mo01334Type,
} from '~/types/business/components/Mo01334Type'
import { SPACE_WAVE, DIALOG_BTN } from '~/constants/classification-constants'

/**
 * Or10900:有機体:計画表取込モーダル
 * GUI01218_計画表取込
 *
 * @description
 * 計画表取込モーダル
 *
 * <AUTHOR>
 */
const { t } = useI18n()

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/************************************************
 * Props
 ************************************************/

interface Props {
  onewayModelValue: Or10900OnewayType
  uniqueCpId: string
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or10900Const.DEFAULT.IS_OPEN,
})

const planTableInfoMo01334Headers_1 = [
  //課題番号
  {
    title: t('label.issues-number'),
    key: 'kadaiNo',
    width: '64px',
    sortable: false,
  },
  //目標
  {
    title: t('label.goal'),
    key: 'sogoMokuhyoKnj',
    width: '240px',
    sortable: false,
  },
  //介護保険サービスまたは地域支援事業
  {
    title: t('label.nursing-care-insurance-service-support-project'),
    key: 'hokenServiceKnj',
    width: '280px',
    sortable: false,
  },
  //サービス種別
  {
    title: t('label.service-type-2'),
    key: 'svShubetuKnj',
    width: '230px',
    sortable: false,
  },
  //事業所
  {
    title: t('label.kotobimisho'),
    key: 'svJigyoKnj',
    width: '276px',
    sortable: false,
  },
  //期間
  {
    title: t('label.kikanKnj'),
    key: 'kikanKnj',
    sortable: false,
  },
] as unknown as Mo01334Headers[]

const planTableInfoMo01334Headers_2 = [
  {
    key: 'data-table-select',
    sortable: false,
    width: '56px',
  },
  //課題番号
  {
    title: t('label.issues-number'),
    key: 'kadaiNo',
    width: '64px',
    sortable: false,
  },
  //目標
  {
    title: t('label.goal'),
    key: 'sogoMokuhyoKnj',
    width: '240px',
    sortable: false,
  },
  //介護保険サービスまたは地域支援事業
  {
    title: t('label.nursing-care-insurance-service-support-project'),
    key: 'hokenServiceKnj',
    width: '280px',
    sortable: false,
  },
  //サービス種別
  {
    title: t('label.service-type-2'),
    key: 'svShubetuKnj',
    width: '230px',
    sortable: false,
  },
  //事業所
  {
    title: t('label.kotobimisho'),
    key: 'svJigyoKnj',
    width: '220px',
    sortable: false,
  },
  //期間
  {
    title: t('label.kikanKnj'),
    key: 'kikanKnj',
    sortable: false,
  },
] as unknown as Mo01334Headers[]


const defaultOnewayModelValue: Or10900OnewayType = {
  /** 親画面.事業者ID */
  svJigyoId: '',
  /** 親画面.施設ID */
  shisetuId: '',
  /** 親画面.利用者ID */
  userId: '',
  /** 親画面.種別ID */
  syubetsuId: '',
  /** 親画面.期間管理フラグ */
  periodManageFlag: '',
  /** 共通情報.期間の管理:【0】:日付で管理 【1】:文章で管理 */
  periodManagement: '',
  /** 共通情報.初期設定マスタ:期間のカレンダー取込:【0】：?/?～?/? 【1】：?月?日～?月?日 【2】：?/? 【3】：?月?日 */
  periodCalendarImport: '',
}

const defaultModelValue = {
  //詳細③一覧
  overwriteImportContentsInfo: {
    items: [
      {
        id: '',
        planTableId: '',
        kadaiNo: '',
        sogoMokuhyoKnj: '',
        hokenServiceKnj: '',
        svShubetuKnj: '',
        svJigyoKnj: '',
        kikanKnj: '',
        kikanSYmd: '',
        kikanEYmd: '',
        dmyKikanSep: '',
      },
    ],
  },
  //詳細③一覧
  addImportContentsInfo: {
    items: [
      {
        id: '',
        planTableId: '',
        kadaiNo: '',
        sogoMokuhyoKnj: '',
        hokenServiceKnj: '',
        svShubetuKnj: '',
        svJigyoKnj: '',
        kikanKnj: '',
        kikanSYmd: '',
        kikanEYmd: '',
        dmyKikanSep: '',
      },
    ],
  },
}

const local = reactive({
  //詳細③一覧
  overwriteImportContentsInfo: {
    ...defaultModelValue.overwriteImportContentsInfo,
  },
  //詳細②一覧
  addImportContentsInfo: {
    ...defaultModelValue.addImportContentsInfo,
  },
  //(汎用コードマスタ)取込モードラジオボタン入力取込
  importModeOaRadioList: [] as Or10900RadioType[],
  //取込モード
  importOaRadio: {
    /** 取込モード */
    torikomiModel: Or10900Const.DEFAULT.TORIKOMIMODEL_1,
  },
})

const localOneway = reactive({
  or10900Oneway: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // 「計画表取込」ダイアログ
  mo00024Oneway: {
    width: '1300px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      toolbarTitle: t('label.plan-table-import'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
    },
  },
  //ボタンを閉じますラベル
  mo00611OneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  //確認ボタンラベル
  mo00609OneWay: {
    btnLabel: t('btn.confirm'),
  } as Mo00609OnewayType,
  //(汎用コードマスタ)取込元
  mo00039Oneway_oa: {
    // デフォルト値の設定
    name: 'importModeOa',
    showItemLabel: false,
    hideDetails: true,
  } as Mo00039OnewayType,
  // 計画期間一覧
  planPeriodMo01334: {
    // 計画期間データテーブルのヘッダー
    headers: [
      //計画期間
      {
        title: t('label.plan-period'),
        key: 'planPeriod',
        width: '120px',
        sortable: false,
      },
      //期間内履歴数
      {
        title: t('label.within-the-period-number-of-history'),
        key: 'numberOfHistory',
        width: '80px',
        sortable: false,
      },
    ] as unknown as Mo01334Headers[],
    height: 135,
    items: [
      {
        id: '',
        /**     期間ID */
        planPeriod: '',
        /**     期間内履歴数 */
        numberOfHistory: '',
      },
    ],
  } as Mo01334OnewayType,
  historyMo01334: {
    // 計画期間データテーブルのヘッダー
    headers: [
      //作成日
      {
        title: t('label.create-date'),
        key: 'createYmd',
        width: '128px',
        sortable: false,
      },
      // 担当地域包括支援センター
      {
        title: t('label.charge-regional-comprehensive-support-center'),
        key: 'chiJigyoKnj',
        width: '250px',
        sortable: false,
      },
      //作成者氏名
      {
        title: t('label.author'),
        key: 'knj',
        width: '250px',
        sortable: false,
      },
      //有効期間
      {
        title: t('label.valid-period'),
        key: 'term',
        sortable: false,
      },
    ] as unknown as Mo01334Headers[],
    height: 135,
    items: [
      {
        id: '',
        plan11Id: '',
        sc1Id: '',
        createYmd: '',
        centerShokuKnj: '',
        knj: '',
        chiJigyoKnj: '',
        term: '',
      },
    ],
  } as Mo01334OnewayType,
  // 取込情報データテーブルのヘッダー
  planTableInfoMo01334: {
    showSelect: true,
    selectStrategy: 'all',
    mandatory: false,
    // 計画期間データテーブルのヘッダー
    headers:
      local.importOaRadio.torikomiModel === Or10900Const.DEFAULT.TORIKOMIMODEL_0
        ? planTableInfoMo01334Headers_1
        : (planTableInfoMo01334Headers_2 as unknown as Mo01334Headers[]),
    height: 382,
    items: [
      {
        id: '',
        planTableId: '',
        kadaiNo: '',
        sogoMokuhyoKnj: '',
        hokenServiceKnj: '',
        svShubetuKnj: '',
        svJigyoKnj: '',
        kikanKnj: '',
        kikanSYmd: '',
        kikanEYmd: '',
        dmyKikanSep: '',
      },
    ],
  },
})

//検索した全てのデータを保存
const originData = {
  // (取込)期間一覧
  planPeriodInfoList: {
    items: [] as PlanPeriodInfoType[],
  },
  //(ヘッダ)履歴一覧
  historyInfo: {
    items: [] as HistroyInfoType[],
  },
  //上書取込内容情報一覧
  overwriteImportContentsInfo: {
    items: [] as OverwriteImportContentsInfoType[],
  },
  //追加取込内容情報一覧
  addImportContentsInfo: {
    items: [] as AddImportContentsInfoType[],
  },
}

// 計画期間情報選択行データ設定
const selectedPlanPeriodItem = ref<Mo01334Type>({
  value: '',
  values: [],
})

// 履歴情報選択行データ設定
const selectedHistoryItem = ref<Mo01334Type>({
  value: '',
  values: [],
})

// 日課計画選択行データ設定
const selectedDailyRoutinePlanInfoItem = ref<Mo01334Type>({
  value: '',
  values: [],
})

const or21814_1 = ref({ uniqueCpId: '' })
const or21815_1 = ref({ uniqueCpId: '' })

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or21815Const.CP_ID(1)]: or21815_1.value,
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or10900StateType>({
  cpId: Or10900Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or10900Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  init()
  await acquMotoRadio()
  // 初期情報取得
  await getInitDataInfo()
})

/**************************************************
 * 関数
 **************************************************/
// 初期処理
function init() {
  // 確認ダイアログを初期化
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      isOpen: false,
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
    },
  })

  Or21815Logic.state.set({
    uniqueCpId: or21815_1.value.uniqueCpId,
    state: {
      isOpen: false,
      dialogTitle: t('label.caution'),
      firstBtnType: 'blank',
      secondBtnType: 'blank',
      thirdBtnType: 'normal1',
      thirdBtnLabel: t('btn.ok'),
    },
  })
}

//汎用コード取得取込モード
async function acquMotoRadio() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    //(汎用コードマスタ)取込モードラジオボタン入力取込
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_IMPORT_MODE_OA },
  ]
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  local.importModeOaRadioList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_IMPORT_MODE_OA
  )
}

/** 初期情報取得 */
async function getInitDataInfo() {
  const inputData: PlanTableImportInitSelectInEntity = {
    /** 事業者ID */
    svJigyoId: localOneway.or10900Oneway.svJigyoId,
    /** 利用者ID */
    userId: localOneway.or10900Oneway.userId,
    /** 種別ID */
    syubetsuId: localOneway.or10900Oneway.syubetsuId,
    /** 施設ID */
    shisetuId: localOneway.or10900Oneway.shisetuId,
    /** 期間管理フラグ */
    kikanFlg: localOneway.or10900Oneway.periodManageFlag,
  }
  const resData: PlanTableImportInitSelectOutEntity = await ScreenRepository.select(
    'planTableImportInitSelect',
    inputData
  )

  // データ情報設定
  if (localOneway.or10900Oneway.periodManageFlag === Or10900Const.DEFAULT.PERIOD_MANAGE_FLAG_1) {
    planPeriodInfoSet(resData)
  } else {
    historyInfoSet(resData)
  }
}

/**
 * 計画期間情報リスト設定
 *
 * @param resData - 検索結果
 */
const planPeriodInfoSet = (resData: PlanTableImportInitSelectOutEntity) => {
  const planPeriodInfoList = [] as PlanPeriodInfoType[]
  const histroyInfoList = [] as HistroyInfoType[]

  const dataInfo = resData.data
  if (dataInfo) {
    //(取込)期間一覧情報取得
    dataInfo.planPeriodInfoList.forEach((item) => {
      planPeriodInfoList.push({
        id: item.sc1Id,
        //期間ID
        sc1Id: item.sc1Id,
        //計画期間
        //.開始日＋” ～ ”＋終了日
        planPeriod: item.startYmd + SPACE_WAVE + item.endYmd,
        //期間内履歴数
        numberOfHistory: item.numberOfHistory,
      })
    })
    // 計画期間情報設定
    localOneway.planPeriodMo01334.items = planPeriodInfoList
    originData.planPeriodInfoList.items = planPeriodInfoList
    // 計画期間选中行
    selectedPlanPeriodItem.value.value = planPeriodInfoList[0].sc1Id

    //(ヘッダ)履歴一覧
    dataInfo.planPeriodHistoryInfoList.forEach((item) => {
      histroyInfoList.push({
        id: item.plan11Id,
        //履歴ID
        plan11Id: item.plan11Id,
        // 期間ID
        sc1Id: item.sc1Id,
        // 作成日
        createYmd: item.createYmd,
        // 支援センターの作成者
        centerShokuKnj: item.centerShokuKnj,
        // 作成者
        knj: item.knj,
        // 担当地域包括支援センター
        chiJigyoKnj: item.chiJigyoKnj,
        // 有効期間
        term: item.term,
      })
    })
    originData.historyInfo.items = histroyInfoList

    // 詳細一覧データを編集
    editDetailTable(resData)

    planPeriodRowClick(selectedPlanPeriodItem.value.value)
  }
}

/**
 * (ヘッダ)履歴一覧リスト設定
 *
 * @param resData - 検索結果
 */
const historyInfoSet = (resData: PlanTableImportInitSelectOutEntity) => {
  const histroyInfoList = [] as HistroyInfoType[]

  const dataInfo = resData.data
  if (dataInfo) {
    //(ヘッダ)履歴一覧
    dataInfo.planPeriodHistoryInfoList.forEach((item) => {
      histroyInfoList.push({
        id: item.plan11Id,
        //履歴ID
        plan11Id: item.plan11Id,
        // 期間ID
        sc1Id: item.sc1Id,
        // 作成日
        createYmd: item.createYmd,
        // 作成者氏名
        centerShokuKnj: item.centerShokuKnj,
        // 作成者
        knj: item.knj,
        /** 担当地域包括支援センター */
        chiJigyoKnj: item.chiJigyoKnj,
        // 有効期間
        term: item.term,
      })
    })
    originData.historyInfo.items = histroyInfoList
    localOneway.historyMo01334.items = histroyInfoList
    selectedHistoryItem.value.value = localOneway.historyMo01334.items[0].id
  }
  // 詳細一覧データを編集
  editDetailTable(resData)
}

/**
 * 詳細一覧データを編集
 *
 * @param resData - 検索結果
 */
const editDetailTable = (resData: PlanTableImportInitSelectOutEntity) => {
  const overwriteImportContentsInfoList = [] as OverwriteImportContentsInfoType[]
  const addImportContentsInfoList = [] as AddImportContentsInfoType[]
  const dataInfo = resData.data
  //計画書(2)リスト
  dataInfo.overwriteImportContentsInfoList.forEach((item) => {
    //詳細③一覧
    overwriteImportContentsInfoList.push({
      id: item.kadaiNo,
      /** 計画表ID */
      planTableId: item.planTableId,
      /** 課題番号 */
      kadaiNo: item.kadaiNo,
      /** 目標 */
      sogoMokuhyoKnj: item.sogoMokuhyoKnj,
      /** 介護保険サービスまたは地域支援事業 */
      hokenServiceKnj: item.hokenServiceKnj,
      /** サービス種別 */
      svShubetuKnj: item.svShubetuKnj,
      /** 事業所 */
      svJigyoKnj: item.svJigyoKnj,
      /** 期間 */
      kikanKnj: item.kikanKnj,
      /** 期間開始日 */
      kikanSYmd: item.kikanSYmd,
      /** 期間終了日 */
      kikanEYmd: item.kikanEYmd,
      /** 期間分隔 */
      dmyKikanSep: item.dmyKikanSep,
    })
  })

  dataInfo.addImportContentsInfoList.forEach((item) => {
    addImportContentsInfoList.push({
      id: item.kadaiNo,
      /** 計画表ID */
      planTableId: item.planTableId,
      /** 課題番号 */
      kadaiNo: item.kadaiNo,
      /** 目標 */
      sogoMokuhyoKnj: item.sogoMokuhyoKnj,
      /** 介護保険サービスまたは地域支援事業 */
      hokenServiceKnj: item.hokenServiceKnj,
      /** サービス種別 */
      svShubetuKnj: item.svShubetuKnj,
      /** 事業所 */
      svJigyoKnj: item.svJigyoKnj,
      /** 期間 */
      kikanKnj: item.kikanKnj,
      /** 期間開始日 */
      kikanSYmd: item.kikanSYmd,
      /** 期間終了日 */
      kikanEYmd: item.kikanEYmd,
      /** 期間分隔 */
      dmyKikanSep: item.dmyKikanSep,
    })
  })
  //上書取込内容情報リスト
  originData.overwriteImportContentsInfo.items = overwriteImportContentsInfoList
  //追加取込内容情報リスト
  originData.addImportContentsInfo.items = addImportContentsInfoList
}

/**
 * (取込)期間一覧行変更の時の処理
 *
 * @param selectPeriodId - (取込)期間一覧行データ
 */
function planPeriodRowClick(selectPeriodId: string) {
  // 選択行を変更した場合は選択行IDを変更
  if (selectedPlanPeriodItem.value.value !== selectPeriodId) {
    selectedPlanPeriodItem.value.value = selectPeriodId
  }
  //(ヘッダ)履歴一覧リスト設定
  historyInfoChange(selectPeriodId)
}

/**
 * (ヘッダ)履歴一覧リスト設定
 *
 * @param selectId - (ヘッダ)履歴一覧選択行ID
 */
const historyInfoChange = (selectId: string) => {
  let historyInfoList
  //履歴データがある場合
  if (originData.historyInfo.items) {
    //選択行IDと同じデータを取得
    historyInfoList = originData.historyInfo.items.filter(
      (item: { sc1Id: unknown }) => item.sc1Id === selectId
    )
    //既存のテーブルデータを削除
    localOneway.historyMo01334.items = historyInfoList
    local.overwriteImportContentsInfo.items = []
    local.addImportContentsInfo.items = []
    if (local.overwriteImportContentsInfo.items?.length > 0) {
      selectedDailyRoutinePlanInfoItem.value.value = local.overwriteImportContentsInfo.items[0]?.id
    } else {
      selectedDailyRoutinePlanInfoItem.value.value = ''
    }
    selectedDailyRoutinePlanInfoItem.value.values = []

    //履歴情報がある場合、履歴情報行選択処理
    if (historyInfoList && historyInfoList.length > 0) {
      historyRowClick(historyInfoList[0].plan11Id)
    }
  }
}

// 履歴情報行選択処理
const historyRowClick = (plan11Id: string) => {
  let overwriteImportContentsInfo = []
  let addImportContentsInfo = []
  selectedHistoryItem.value.value = plan11Id

  overwriteImportContentsInfo = originData.overwriteImportContentsInfo.items.filter(
    (planTableId: { planTableId: unknown }) => planTableId.planTableId === plan11Id
  )
  //選択行keyと同じデータを取得
  local.overwriteImportContentsInfo.items = overwriteImportContentsInfo

  if (local.overwriteImportContentsInfo.items?.length) {
    selectedDailyRoutinePlanInfoItem.value.value = local.overwriteImportContentsInfo.items[0]?.id
  } else {
    selectedDailyRoutinePlanInfoItem.value.value = ''
  }

  addImportContentsInfo = originData.addImportContentsInfo.items.filter(
    (planTableId: { planTableId: unknown }) => planTableId.planTableId === plan11Id
  )
  //選択行keyと同じデータを取得
  local.addImportContentsInfo.items = addImportContentsInfo
}

/**
 * 「×ボタン」押下
 * 「閉じボタン」押下
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  setState({ isOpen: false })
}

/**
 * AC008_「確定ボタン」押下
 * 「確定」ボタン押下
 */
async function confirm() {
  // 画面項目.取込モードは「0：上書」の場合
  const resultData: ReturnContentsInfoData[] = []
  if (local.importOaRadio.torikomiModel === Or10900Const.DEFAULT.TORIKOMIMODEL_0) {
    if (selectedDailyRoutinePlanInfoItem.value.value) {
      local.overwriteImportContentsInfo.items
        .filter((it) => it.id === selectedDailyRoutinePlanInfoItem.value.value)
        .forEach((item) => {
          const startDateTime = item.kikanSYmd
          const endDateTime = item.kikanEYmd
          const periodDescription = item.kikanKnj
          const separator = item.dmyKikanSep

          let kikanKnj
          if (
            localOneway.or10900Oneway.periodManagement === Or10900Const.DEFAULT.PERIOD_MANAGE_FLAG_1
          ) {
            kikanKnj = periodDescription
          } else if (
            localOneway.or10900Oneway.periodManagement ===
              Or10900Const.DEFAULT.PERIOD_MANAGE_FLAG_0 &&
            localOneway.or10900Oneway.periodCalendarImport &&
            Number(localOneway.or10900Oneway.periodCalendarImport) < 2
          ) {
            kikanKnj = `${startDateTime}\r\n${separator}\r\n${endDateTime}\r\n${periodDescription}`
          } else {
            kikanKnj = `${startDateTime}\r\n${periodDescription}`
          }
          resultData.push({
            torikomiModel: local.importOaRadio.torikomiModel,
            kadaiNo: item.kadaiNo,
            sogoMokuhyoKnj: item.sogoMokuhyoKnj,
            kikanKnj: kikanKnj,
          })
        })

      const dialogResult = await openConfirmDialog(t('message.i-cmn-10883'))
      switch (dialogResult) {
        case 'yes': {
          // 選択情報値戻り
          emit('update:modelValue', resultData)
          console.log('update:modelValue', resultData)
          close()
          break
        }
        case 'no':
          break
      }
    } else {
      Or21815Logic.state.set({
        uniqueCpId: or21815_1.value.uniqueCpId,
        state: {
          isOpen: true,
          dialogText: t('message.w-cmn-20791'),
        },
      })
    }
  }

  // 画面項目.取込モードは「1：追加」の場合
  if (local.importOaRadio.torikomiModel === Or10900Const.DEFAULT.TORIKOMIMODEL_1) {
    if (!selectedDailyRoutinePlanInfoItem.value.values.length) {
      Or21815Logic.state.set({
        uniqueCpId: or21815_1.value.uniqueCpId,
        state: {
          isOpen: true,
          dialogText: t('message.w-cmn-20791'),
        },
      })
    } else {
      if (selectedDailyRoutinePlanInfoItem.value.values.length) {
        local.overwriteImportContentsInfo.items
          .filter((it) => selectedDailyRoutinePlanInfoItem.value.values.includes(it.id))
          .forEach((item) => {
            const startDateTime = item.kikanSYmd
            const endDateTime = item.kikanEYmd
            const periodDescription = item.kikanKnj
            const separator = item.dmyKikanSep

            let kikanKnj
            if (
              localOneway.or10900Oneway.periodManagement ===
              Or10900Const.DEFAULT.PERIOD_MANAGE_FLAG_1
            ) {
              kikanKnj = periodDescription
            } else if (
              localOneway.or10900Oneway.periodManagement ===
                Or10900Const.DEFAULT.PERIOD_MANAGE_FLAG_0 &&
              localOneway.or10900Oneway.periodCalendarImport &&
              Number(localOneway.or10900Oneway.periodCalendarImport) < 2
            ) {
              kikanKnj = `${startDateTime}\r\n${separator}\r\n${endDateTime}\r\n${periodDescription}`
            } else {
              kikanKnj = `${startDateTime}\r\n${periodDescription}`
            }
            resultData.push({
              torikomiModel: local.importOaRadio.torikomiModel,
              kadaiNo: item.kadaiNo,
              sogoMokuhyoKnj: item.sogoMokuhyoKnj,
              kikanKnj: kikanKnj,
            })
          })
      }
      emit('update:modelValue', resultData)
      console.log('update:modelValue', resultData)

      close()
    }
  }
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no)
 */
async function openConfirmDialog(paramDialogText: string): Promise<'yes' | 'no'> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })
  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)

        let result = 'no' as 'yes' | 'no'

        if (event?.firstBtnClickFlg) {
          result = DIALOG_BTN.YES
        }
        if (event?.secondBtnClickFlg) {
          result = DIALOG_BTN.NO
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    mo00024.value.isOpen = true
    if (!newValue) {
      close()
    }
  }
)

//(汎用コードマスタ)取込元ラジオボタンを監視
watch(
  () => local.importOaRadio.torikomiModel,
  (newValue) => {
    if (local.overwriteImportContentsInfo.items?.length) {
      selectedDailyRoutinePlanInfoItem.value.value = local.overwriteImportContentsInfo.items[0]?.id
    } else {
      selectedDailyRoutinePlanInfoItem.value.value = ''
    }
    selectedDailyRoutinePlanInfoItem.value.values = []
    if (newValue === Or10900Const.DEFAULT.TORIKOMIMODEL_0) {
      // 上書
      localOneway.planTableInfoMo01334.showSelect = false
      localOneway.planTableInfoMo01334.selectStrategy = 'single'
      localOneway.planTableInfoMo01334.headers = planTableInfoMo01334Headers_1
    } else {
      localOneway.planTableInfoMo01334.showSelect = true
      localOneway.planTableInfoMo01334.selectStrategy = 'all'

      localOneway.planTableInfoMo01334.headers = planTableInfoMo01334Headers_2
    }
  }
)

/**
 * 計画期間選択一覧Table Mo01334のイベントを監視
 *
 * @description
 * 単一選択時
 */
watch(
  () => selectedPlanPeriodItem.value,
  (newVal) => {
    if (newVal) {
      planPeriodRowClick(selectedPlanPeriodItem.value.value)
    }
  }
)

/**
 * 計画期間選択一覧Table Mo01334のイベントを監視
 *
 * @description
 * 単一選択時
 */
watch(
  () => selectedHistoryItem.value,
  (newVal) => {
    if (newVal) {
      historyRowClick(selectedHistoryItem.value.value)

      selectedDailyRoutinePlanInfoItem.value.values = []
    }
  }
)
</script>
<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
    class="modal-dialog-or10900"
  >
    <template #cardItem>
      <c-v-sheet>
        <c-v-row
          no-gutters
          class="tabTop"
        >
          <c-v-col
            v-if="
              localOneway.or10900Oneway.periodManageFlag ==
              Or10900Const.DEFAULT.PERIOD_MANAGE_FLAG_1
            "
            class="table-header pr-2"
            col="4"
          >
            <!-- 計画期間選択一覧 -->
            <base-mo01334
              v-model="selectedPlanPeriodItem"
              class="list-wrapper"
              hide-default-footer
              :oneway-model-value="localOneway.planPeriodMo01334"
            >
              <template #[`item.numberOfHistory`]="{ item }">
                <div class="text-right">
                  <span>{{ item.numberOfHistory }}</span>
                </div>
              </template>
            </base-mo01334>
          </c-v-col>
          <c-v-col
            class="table-header"
            :cols="
              localOneway.or10900Oneway.periodManageFlag ===
              Or10900Const.DEFAULT.PERIOD_MANAGE_FLAG_1
                ? '8'
                : 'auto'
            "
          >
            <!--(ヘッダ)履歴一覧-->
            <base-mo01334
              v-model="selectedHistoryItem"
              class="list-wrapper"
              hide-default-footer
              :oneway-model-value="localOneway.historyMo01334"
            >
              <template #[`item.knj`]="{ item }">
                <div class="td-items">
                  <span>{{ item.centerShokuKnj }}</span>
                  <span>{{ item.knj }}</span>
                </div>
              </template>
              <template #[`item.term`]="{ item }">
                <div class="ellipsis-cell-1">
                  <span>{{ item.term }}</span>
                </div>
              </template>
            </base-mo01334>
          </c-v-col>
        </c-v-row>

        <c-v-row
          style="border-top: 1px solid rgb(var(--v-theme-black-100))"
          class="mt-2"
          no-gutters
        >
          <c-v-col>
            <div class="d-flex justify-end">
              <base-mo00039
                v-model="local.importOaRadio.torikomiModel"
                :oneway-model-value="localOneway.mo00039Oneway_oa"
              >
                <div>
                  <base-at-radio
                    v-for="item in local.importModeOaRadioList"
                    :key="item.value"
                    :name="item.label"
                    :radio-label="item.label"
                    :value="item.value"
                  />
                </div>
              </base-mo00039>
            </div>
          </c-v-col>
        </c-v-row>

        <!-- 追加取込内容情報リスト -->
        <c-v-row
          no-gutters
          class="d-flex justify-center"
        >
          <c-v-col
            cols="12"
            class="detail-list table-header mt-2 px-2"
          >
            <base-mo01334
              v-model="selectedDailyRoutinePlanInfoItem"
              class="list-wrapper"
              hide-default-footer
              :oneway-model-value="{
                ...localOneway.planTableInfoMo01334,
                items:
                  local.importOaRadio.torikomiModel === Or10900Const.DEFAULT.TORIKOMIMODEL_0
                    ? local.overwriteImportContentsInfo.items
                    : local.addImportContentsInfo.items,
              }"
            >
              <!-- 課題番号 -->
              <template #[`item.kadaiNo`]="{ item }">
                <div style="text-align: right">{{ item.kadaiNo }}</div>
              </template>
              <!-- 目標 -->
              <template #[`item.sogoMokuhyoKnj`]="{ item }">
                <span class="ellipsis-cell">{{ item.sogoMokuhyoKnj }}</span>
                <c-v-tooltip
                  activator="parent"
                  location="top"
                  :max-width="240"
                  :text="item.sogoMokuhyoKnj"
                />
              </template>
              <!-- 介護保険サービスまたは地域支援事業 -->
              <template #[`item.hokenServiceKnj`]="{ item }">
                <span class="ellipsis-cell">{{ item.hokenServiceKnj }}</span>
                <c-v-tooltip
                  activator="parent"
                  location="top"
                  :max-width="260"
                  :text="item.hokenServiceKnj"
                />
              </template>
              <!-- サービス種別 -->
              <template #[`item.svShubetuKnj`]="{ item }">
                <span class="ellipsis-cell">{{ item.svShubetuKnj }}</span>
                <c-v-tooltip
                  activator="parent"
                  location="top"
                  :max-width="280"
                  :text="item.svShubetuKnj"
                />
              </template>
              <!-- 事業所 -->
              <template #[`item.svJigyoKnj`]="{ item }">
                <span class="ellipsis-cell">{{ item.svJigyoKnj }}</span>
                <c-v-tooltip
                  activator="parent"
                  location="top"
                  :max-width="160"
                  :text="item.svJigyoKnj"
                />
              </template>
              <!-- 期間 -->
              <template #[`item.kikanKnj`]="{ item }">
                <div v-if="localOneway.or10900Oneway.periodManagement === '0'">
                  <div>{{ item.kikanSYmd }}</div>
                  <div
                    v-if="
                      localOneway.or10900Oneway.periodCalendarImport &&
                      Number(localOneway.or10900Oneway.periodCalendarImport) < 2
                    "
                  >
                    <div>{{ item.dmyKikanSep }}</div>
                    <div>{{ item.kikanEYmd }}</div>
                  </div>
                </div>
                <div>{{ item.kikanKnj }}</div>
                <c-v-tooltip
                  activator="parent"
                  location="top"
                  :max-width="160"
                >
                  <template #default>
                    <div class="tooltip-item">
                      <div v-if="localOneway.or10900Oneway.periodManagement === '0'">
                        <div>{{ item.kikanSYmd }}</div>
                        <div
                          v-if="
                            localOneway.or10900Oneway.periodCalendarImport &&
                            Number(localOneway.or10900Oneway.periodCalendarImport) < 2
                          "
                        >
                          <div>{{ item.dmyKikanSep }}</div>
                          <div>{{ item.kikanEYmd }}</div>
                        </div>
                      </div>
                      <div>{{ item.kikanKnj }}</div>
                    </div>
                  </template>
                </c-v-tooltip>
              </template>
            </base-mo01334>
          </c-v-col>
        </c-v-row>
      </c-v-sheet>
    </template>
    <!-- フッター -->
    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          @click="close"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <base-mo00609
          v-bind="localOneway.mo00609OneWay"
          class="mx-2"
          @click="confirm"
        >
          <!--ツールチップ表示："設定を確定します"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.confirm-btn')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>

  <!-- 上書ダイアログ -->
  <g-base-or21814 v-bind="or21814_1" />
  <g-base-or21815 v-bind="or21815_1" />
</template>
<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';
$spacing-root: 8px;
.ellipsis-cell {
  position: relative;
  display: -webkit-box;
  line-clamp: 5;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.ellipsis-cell-1 {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.td-items {
  display: flex;
  justify-content: flex-start;
  span {
    flex: 1;
  }
}
:deep(.detail-list .v-table__wrapper th:nth-last-child(6)) {
  color: blue !important;
}

.v-row {
  margin: -$spacing-root;
}

.tabTop {
  padding: $spacing-root;
}
.detail-list {
  margin-bottom: 8px;
  :deep(tbody tr) {
    height: 106px;
  }
}

:deep(.radio-group) {
  margin-top: 0 !important;
}
</style>
