import { Or32406Const } from './Or32406.constants'
import type { Or33456StateType } from './Or32406.type'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or00248Logic } from '~/components/base-components/organisms/Or00248/Or00248.logic'
import { Or30980Const } from '~/components/custom-components/organisms/Or30980/Or30980.constants'
import { Or30980Logic } from '~/components/custom-components/organisms/Or30980/Or30980.logic'
import { Gui00070Logic } from '~/components/custom-components/organisms/Gui00070/Gui00070.logic'
import { Gui00070Const } from '~/components/custom-components/organisms/Gui00070/Gui00070.constants'
import { Or00249Const } from '~/components/base-components/organisms/Or00249/Or00249.constants'
import { Or00249Logic } from '~/components/base-components/organisms/Or00249/Or00249.logic'
import { Or50148Const } from '~/components/custom-components/organisms/Or50148/Or50148.constants'
import { Or50148Logic } from '~/components/custom-components/organisms/Or50148/Or50148.logic'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or65700Const } from '~/components/custom-components/organisms/Or65700/Or65700.constants'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or00094Const } from '~/components/base-components/organisms/Or00094/Or00094.constants'
import { Or00094Logic } from '~/components/base-components/organisms/Or00094/Or00094.logic'

/**
 * Or32406:支援経過記録画面
 * GUI01258_支援経過記録
 *
 * @description
 * 処理ロジック
 *
 * <AUTHOR> NGUYEN NHUT THANH
 */
export namespace Or33456Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or32406Const.CP_ID(0),
      uniqueCpId,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [
        { cpId: Or00248Const.CP_ID(1) },
        { cpId: Or00249Const.CP_ID(0) },
        { cpId: Or30980Const.CP_ID(1) },
        { cpId: Gui00070Const.CP_ID(0) },
        { cpId: Or50148Const.CP_ID(0) },
        { cpId: Or11871Const.CP_ID },
        { cpId: Or21815Const.CP_ID(1) },
        { cpId: Or65700Const.CP_ID(0) },
        { cpId: Or41179Const.CP_ID(0) },
        { cpId: Or21814Const.CP_ID(0) },
        { cpId: Or00094Const.CP_ID(0) },
      ],
    })

    // 子コンポーネントのセットアップ
    Or00248Logic.initialize(childCpIds[Or00248Const.CP_ID(1)].uniqueCpId)
    Or00249Logic.initialize(childCpIds[Or00249Const.CP_ID(0)].uniqueCpId)
    Or30980Logic.initialize(childCpIds[Or30980Const.CP_ID(1)].uniqueCpId)
    Gui00070Logic.initialize(childCpIds[Gui00070Const.CP_ID(0)].uniqueCpId)
    Or50148Logic.initialize(childCpIds[Or50148Const.CP_ID(0)].uniqueCpId)
    Or11871Logic.initialize(childCpIds[Or11871Const.CP_ID].uniqueCpId)
    Or21815Logic.initialize(childCpIds[Or21815Const.CP_ID(1)].uniqueCpId)
    Or41179Logic.initialize(childCpIds[Or41179Const.CP_ID(0)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(0)].uniqueCpId)
    Or00094Logic.initialize(childCpIds[Or00094Const.CP_ID(0)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }

  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or33456StateType>(Or32406Const.CP_ID(0))
}
