/**
 * OrX0135:有機体:承認欄登録画面モーダル
 * GUI00617_承認欄登録画面
 *
 * @description
 * 静的データ
 *
 *  <AUTHOR>
 */
import { getSequencedCpId } from '~/utils/useScreenUtils'

export namespace OrX0135Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('OrX0135', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false
    /**
     * デフォルト承認欄の1行目の目的値
     */
    export const DEFAULT_TEXTKNJ = '上記サービス計画について説明を受け、内容に同意しました。'
    /**
     * デフォルト下線部分の1行目の目的値
     */
    export const DEFAULT_DAYKNJ = '同意年月日　　年　　月　　日　署名　　　　　　　　　　印'
    /**
     * 表示する行数: 1行
     */
    export const SHOW_ROW_1_VALUE = '1'
    /**
     * 表示する行数: 2行
     */
    export const SHOW_ROW_2_VALUE = '2'
    /**
     * 表示する行数: 3行
     */
    export const SHOW_ROW_3_VALUE = '3'
    /**
     * 表示する行数: 4行
     */
    export const SHOW_ROW_4_VALUE = '4'
    /**
     * 極小: 9
     */
    export const FONT_SIZE_9 = '9'
    /**
     * 小さい: 10
     */
    export const FONT_SIZE_10 = '10'
    /**
     * 普通: 11
     */
    export const FONT_SIZE_11 = '11'
    /**
     * 大きい: 12
     */
    export const FONT_SIZE_12 = '12'
    /**
     * px
     */
    export const WIDTH_PX = 'px'
    /**
     * 608px
     */
    export const WIDTH_608_PX = 608
    /**
     * 592px
     */
    export const WIDTH_592_PX = 592
    /**
     * 10px
     */
    export const WIDTH_10_PX = 10
    /**
     * 1190px
     */
    export const WIDTH_1190_PX = 1190
    /**
     * 1200px
     */
    export const WIDTH_1200_PX = 1200
    /**
     * 0px
     */
    export const WIDTH_0_PX = 0
    /**
     * 1208px
     */
    export const WIDTH_1208_PX = '1208px'
    /**
     * 文字数入力＝"38"
     */
    export const FONT_NUMBER_38 = '38'
    /**
     * 文字数入力＝"75"
     */
    export const FONT_NUMBER_75 = '75'
    /**
     * EMPTY
     */
    export const EMPTY = ''
    /**
     * "オフセット 6
     */
    export const OFFSET_6 = 6
  }
}
