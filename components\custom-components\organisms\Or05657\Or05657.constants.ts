import { getSequencedCpId } from '~/utils/useScreenUtils'
/**
 * Or05657:(見通し)注釈ダイアログ
 * GUI00916_見通し
 *
 * @description
 * 静的データ
 *
 * <AUTHOR> NGUYEN MANH HUNG
 */

export namespace Or05657Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or05657', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false
  }
}
