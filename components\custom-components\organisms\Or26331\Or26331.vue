<script setup lang="ts">
/**
 * Or26331：有機体：利用者選択
 * GUI01290_［印刷設定］画面
 *
 * @description
 * Or26331：有機体：利用者選択
 *
 * <AUTHOR> 朱征宇
 */
import { reactive, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or26331Const } from './Or26331.constants'
import { useScreenTwoWayBind } from '#imports'
import type { Or26331OnewayType, Or26331Type } from '~/types/cmn/business/components/Or26331Type'
import type { Mo00039OnewayType } from '@/types/business/components/Mo00039Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { CustomClass } from '~/types/CustomClassType'
import type { Mo00020OnewayType } from '~/types/business/components/Mo00020Type'

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or26331OnewayType
  uniqueCpId: string
}

const props = defineProps<Props>()
/**
 * Onewayバインド用の内部変数
 */
const localOneway = reactive({
  // 利用者選択ラベル
  mo01338OneWayPrinterUserSelect: {
    value: t('label.printer-user-select'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  // 利用者選択ラジオボタングループ
  mo00039OneWayUserSelectType: {
    ...({
      name: '',
      showItemLabel: false,
      inline: true,
    } as Mo00039OnewayType),
    ...props.onewayModelValue.mo00039OneWayUserSelectType,
  },
  // 基準日
  mo01338OneWayBaseDate: {
    value: t('label.base-date'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  // 基準日
  mo00020KijunbiOneWay: {
    showItemLabel: false,
    showSelectArrow: true,
    width: '132px',
    mo00009OnewayBack: {},
    mo00009OnewayForward: {},
  } as Mo00020OnewayType,
  // 履歴選択ラベル
  mo01338OneWayPrinterHistorySelect: {
    value: t('label.printer-history-select'),
    customClass: {
      labelStyle: 'display: none',
    } as CustomClass,
  } as Mo01338OnewayType,
  // 履歴選択-ラジオボタングループ
  mo00039OneWayHistorySelectType: {
    ...({
      name: '',
      showItemLabel: false,
      inline: true,
    } as Mo00039OnewayType),
    ...props.onewayModelValue.mo00039OneWayHistorySelectType,
  },
})

/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<Or26331Type>({
  cpId: Or26331Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

/**
 * 履歴選択ラジオボタンの選択項目の監視
 */
watch(
  () => props.onewayModelValue.mo00039OneWayHistorySelectType.items,
  (newValue) => {
    localOneway.mo00039OneWayHistorySelectType.items = newValue
  }
)

/**
 * 利用者選択ラジオボタンの選択項目の監視
 */
watch(
  () => props.onewayModelValue.mo00039OneWayUserSelectType.items,
  (newValue) => {
    localOneway.mo00039OneWayUserSelectType.items = newValue
  }
)
</script>

<template>
  <c-v-col
    v-if="refValue"
    cols="12"
    sm="4"
    class="pa-2"
  >
    <c-v-row class="or26331_row">
      <!-- 利用者選択ラベル -->
      <base-mo01338
        :oneway-model-value="localOneway.mo01338OneWayPrinterUserSelect"
        style="background-color: transparent"
      >
      </base-mo01338
    ></c-v-row>
    <c-v-row class="or26331_row">
      <!-- 利用者選択ラジオボタングループ -->
      <base-mo00039
        v-model="refValue!.mo00039OneWayUserSelectType"
        :oneway-model-value="localOneway.mo00039OneWayUserSelectType"
      >
      </base-mo00039>
    </c-v-row>
  </c-v-col>
  <c-v-col
    v-if="refValue && refValue!.mo00039OneWayUserSelectType === Or26331Const.DEFAULT.HUKUSUU"
    cols="12"
    sm="4"
    class="pa-2"
  >
    <c-v-row class="or26331_row">
      <!-- 基準日  -->
      <base-mo01338
        :oneway-model-value="localOneway.mo01338OneWayBaseDate"
        style="background-color: transparent"
      >
      </base-mo01338>
    </c-v-row>
    <c-v-row class="or26331_row">
      <base-mo00020
        v-model="refValue!.mo00020TypeKijunbi"
        :oneway-model-value="localOneway.mo00020KijunbiOneWay"
      />
    </c-v-row>
  </c-v-col>
  <c-v-col
    v-if="refValue && refValue!.mo00039OneWayUserSelectType === Or26331Const.DEFAULT.TANI"
    cols="12"
    sm="4"
    class="pa-2"
  >
    <c-v-row class="or26331_row">
      <!-- 履歴選択ラベル -->
      <base-mo01338
        :oneway-model-value="localOneway.mo01338OneWayPrinterHistorySelect"
        style="background-color: transparent"
      >
      </base-mo01338>
    </c-v-row>
    <c-v-row class="or26331_row">
      <!-- 履歴選択-ラジオボタングループ -->
      <base-mo00039
        v-model="refValue!.mo00039OneWayHistorySelectType"
        :oneway-model-value="localOneway.mo00039OneWayHistorySelectType"
      >
      </base-mo00039>
    </c-v-row>
  </c-v-col>
</template>

<style scoped lang="scss">
.or26331_row {
  margin: 0px !important;
}
</style>
