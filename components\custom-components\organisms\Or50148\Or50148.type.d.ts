import type { Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { Mo00046Type } from '~/types/business/components/Mo00046Type'
import type { Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo01272Type } from '~/types/business/components/Mo01272Type'
import type { Mo00040Type } from '~/types/business/components/Mo00040Type'
import type { Mo01376Type } from '~/types/business/components/Mo01376Type'

/**
 * Or50148:タイトル
 * GUI01258_支援経過記録
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR> NGUYEN NHUT THANH
 */
export interface Or50148StateType {
  /**
   * パラメータ
   */
  param: Param
}

/**
 * パラメータ
 */
export interface Param {
  /**
   * 実行フラグ
   */
  executeFlag: string
  /** ケアプラン方式フラグ */
  cpn_flg: string | null
  /** 視点 */
  view: string | null
  /** 事業者ID */
  sv_jigyo_id: string | null
  /** 職員ID */
  shoku_id: string | null
  /** システムコード */
  systemCode: string | null
  /** 機能ID */
  functionId: string | null
  /** 電子カルテ連携フラグ */
  electronicMedicalRecordCooperationFlag: string | null
  /** 処理期間開始日 */
  yymm_ymd_start: string | null
  /** 処理期間終了日 */
  yymm_ymd_end: string | null
  /** 利用者IDList */
  useridList: UseridList[]
  /** 支店区分 */
  shitenKbn: string | null
  /** システム略称 */
  systemAbbr: string | null
  /** 事業所コード */
  jigyoshoCd: string | null
  /** 支援経過記録様式 */
  shienKeikaKirokuYoshiki: string | null
  /** 適用事業所IDリスト */
  jigyoList: JigyoList[]
  /** 事業者グループ適用ID */
  jigyoGpId: string
}

/**
 * 適用事業所IDリスト
 */
export interface JigyoList {
  /** 事業者ID */
  jigyoId: string
}

/** 利用者IDList */
export interface UseridList {
  /** 利用者ID */
  userid: string | null
}

/**
 * 双方向バインドModelValue
 */
export interface Or50148TwoWayType {
  /**
   * 介護支援リスト(備考)
   */
  kaigo_shiyen_list: Kaigo_shiyen_list[]
  /**
   * 支援種別マスタリスト
   */
  shubetsu_list: Shubetsu_list[]
  /**
   * 処遇支援経過リスト
   */
  shogu_shiyen_list: Shogu_shiyen_list[]
  /**
   * 提供サービスマスタリスト
   */
  service_list: Service_list[]
  /**
   * 事業所情報リスト
   */
  tekiyoJigyoList: TekiyoJigyoList[]
  /**
   * 処遇支援経過リスト and 介護支援リスト(備考)
   */
  shiyen_list_mapping: TableDataList[]
}

/**
 * DataTableのデータ型
 */
export interface DataTable {
  /**
   * 介護支援リスト(備考)
   */
  kaigo_shiyen_list: Kaigo_shiyen_list[]
  /**
   * 支援種別マスタリスト
   */
  shubetsu_list: Shubetsu_list[]
  /**
   * 処遇支援経過リスト
   */
  shogu_shiyen_list: Shogu_shiyen_list[]
  /**
   * 提供サービスマスタリスト
   */
  service_list: Service_list[]
  /**
   * 事業所情報リスト
   */
  tekiyoJigyoList: TekiyoJigyoList[]
  /**
   * 処遇支援経過リスト and 介護支援リスト(備考)
   */
  shiyen_list_mapping: TableDataList[]
}

/**
 * 事業所情報リスト
 */
export interface TekiyoJigyoList {
  /** 法人ID */
  houjinId: string
  /** 施設ID */
  shisetuId: string
  /** サービス事業者ID */
  svJigyoId: string
  /** 事業所名 */
  jigyoNameKnj: string
  /** 適用フラグ */
  tekiyoFlg: string
  /** 表示順 */
  sort: string
}

/**
 * 処遇支援経過リスト
 */
export interface Shogu_shiyen_list {
  /** 記録日Dmy */
  dmyW01KirokuYmd: string
  /** 開始時間（時） */
  startHh: string
  /** 開始時間（分） */
  startMm: string
  /** 終了時間（時） */
  endHh: string
  /** 終了時間（分） */
  endMm: string
  /** ヘッダID */
  keikaId: string
  /** 法人ID */
  houjinId: string
  /** 施設ID */
  shisetuId: string
  /** 事業者ID */
  svJigyoId: string
  /** 利用者ID */
  userid: string
  /** 記録日 */
  kirokuYmd: string | null
  /** 開始時刻 */
  startTime: string
  /** 終了時刻 */
  endTime: string
  /** 所用時間 */
  jikan: string
  /** サービス提供種別CD */
  svTeikyoCd: string
  /** 内容 */
  memoKnj: string
  /** 担当者 */
  tantoName: string
  /** 記録者 */
  kirokuName: string
  /** 表示順 */
  seq: string
  /** 更新回数 */
  modifiedCnt: string
}

/**
 * 介護支援リスト
 */
export interface Kaigo_shiyen_list {
  /** ダミー記録日 */
  dmyYymmYmd: string
  /** 色計算 */
  computeColor: string
  /** 予備 */
  dmyYobi: string
  /** 備考区分 */
  bikoKbn: string
  /** 法人ID */
  houjinId: string
  /** 施設ID */
  shisetuId: string
  /** 記録日 */
  yymmYmd: string | null
  /** 利用者ID */
  userid: string
  /** レコード番号 */
  recNo: string
  /** 開始時間 */
  timeHh: string
  /** 開始分 */
  timeMm: string
  /** ケース種別 */
  caseCd: string
  /** ケース事項 */
  caseKnj: string
  /** 記入者 */
  staffid: string
  /** ｹｰｽ転記フラグ */
  caseFlg: string
  /** 申し送りフラグ */
  moushiokuriFlg: string
  /** 結果元履歴番号 */
  baseRecNo: string
  /** システム別フラグ */
  systemFlg: string
  /** 指示フラグ */
  shijiFlg: string
  /** 共有区分 */
  kyouyuBikoKbn: string
  /** 褥瘡フラグ */
  jyoFlg: string
  /** 事業者ID */
  svJigyoId: string
  /** ユニークID */
  uniqueId: string
  /** 結果元ユニークID */
  baseUniqueId: string
  /** 更新日時 */
  timeStmp: string
  /** 表示順 */
  seqNo: string
  /** 終了時間 */
  endHh: string
  /** 終了分 */
  endMm: string
  /** 所要時間 */
  totaltime: string
  /** タイトル */
  titleKnj: string
  /** 訪問チェックフラグ */
  houmonFlg: string
  /** 計画書（1）チェックフラグ */
  kkak1Flg: string
  /** 計画書（2）チェックフラグ */
  kkak2Flg: string
  /** 週間計画チェックフラグ */
  weekFlg: string
  /** 利用票チェックフラグ */
  riyoFlg: string
  /** 計画対象年月 */
  taishoYm: string | Mo00020Type
  /** 担当者 */
  tantoId: string
  /** CPSシステム内部ID */
  cpsKeika2Id: string
  /** 履歴変更フラグ */
  modifyFlg: string
  /** 記録者（文字列） */
  shokuKnj: string
  /** 地域支援事業所ID */
  chiJigyoId: string
  /** 大正年月1 */
  dmyW01TaishoYm: string
  /** 大正年月2 */
  dmyW02YymmYmd: string
  /** 種類CD */
  shuruiCd: string
  /** 項目名称 */
  koumokuKnj: string
  /** 更新回数 */
  modifiedCnt: string
}

/** 支援種別マスタリスト */
export interface Shubetsu_list {
  /** カウンター */
  shurui_cd: string
  /** 地域支援事業所ID */
  chi_jigyo_id: string
  /** 法人ID */
  houjin_id: string
  /** 施設ID */
  shisetu_id: string
  /** 事業者ID */
  sv_jigyo_id: string
  /** 種類名 */
  shurui_knj: string
}

/**
 * 提供サービスマスタリスト
 */
export interface Service_list {
  /** サービス提供種別CD */
  svTeikyoCd: string
  /** 内容 */
  naiyoKnj: string
  /** グループ */
  groupCd: string
  /** 表示順 */
  seq: string
}

/**
 * テーブルマッピングデータ
 */
export interface TableDataList {
  // [key: string]: string
  /**
   * ふりがな（姓）
   */
  nameKanaSei: string
  /** ユーザー名 */
  nameUser: string
  /** 記録日 */
  yymmYmd: Mo00020Type
  /** 開始時間・開始分  */
  fromTimeHhmm: Mo01272Type
  /** 終了時間・終了分 */
  toTimeHhmm: Mo01272Type
  /** 時間選択ボタン */
  timneControl: Mo01376Type
  /** タイトル */
  titleKnj: Mo00046Type
  /** ケース種別 */
  caseCd: Mo00040Type
  /** サービス提供種別CD */
  svTeikyoCd: Mo00040Type
  /** 項目名称 */
  koumokuKnj: Mo00046Type
  /** 内容 */
  memoKnj: Mo00046Type
  /** 記録者（文字列） */
  shokuKnj?: string
  /** 担当者 */
  tantoName: string
  /** 記録者 */
  kirokuName: string
  /** 訪問チェックフラグ */
  houmonFlg: Mo00018Type
  /** 計画書（1）チェックフラグ */
  kkak1Flg: Mo00018Type
  /** 計画書（2）チェックフラグ */
  kkak2Flg: Mo00018Type
  /** 週間計画チェックフラグ */
  weekFlg: Mo00018Type
  /** 利用票チェックフラグ */
  riyoFlg: Mo00018Type
  /** 計画対象年月 */
  taishoYm: Mo00020Type
  /** visible */
  visible: boolean
  /** 更新区分 */
  updateKbn: string
  /** 更新区分 */
  extraFields?: Record<string, string>
}
