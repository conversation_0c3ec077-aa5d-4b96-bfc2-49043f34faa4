<script setup lang="ts">
/**
 * GUI01310:有機体:印刷設定
 * GUI01310_印刷設定
 *
 * @description
 * 印刷設定
 *
 * <AUTHOR>
 */
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or56885Const } from '~/components/custom-components/organisms/Or56885/Or56885.constants'
import { Or56885Logic } from '~/components/custom-components/organisms/Or56885/Or56885.logic'
import type { Or56885Param } from '~/components/custom-components/organisms/Or56885/Or56885.type'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI01310'
// ルーティング
const routing = 'GUI01310/pinia'
// 画面物理名
const screenName = 'GUI01310'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or56885 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01310' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 子コンポーネントのユニークIDを設定する
// or56885.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI01310',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or56885Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or56885Const.CP_ID(1)]: or56885.value,
})

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  Or56885Logic.initialize(or56885.value.uniqueCpId)
}

// ダイアログ表示フラグ
const showDialogOr56885 = computed(() => {
  // Or56885のダイアログ開閉状態
  return Or56885Logic.state.get(or56885.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(Or56885)--期間管理フラグが「管理する」の場合
 *
 */
function onClickOr56885() {
  // Or56885のダイアログ開閉状態を更新する
  Or56885Logic.state.set({
    uniqueCpId: or56885.value.uniqueCpId,
    state: {
      isOpen: true,
      param: {
        prtNo: '1',
        svJigyoId: '1',
        shisetuId: '1',
        tantoId: '1',
        syubetsuId: '2',
        sectionName: 'インターライ方式ケアアセスメント表',
        userId: '41',
        recId: '2',
        assessmentId: '1',
        svJigyoKnj: '1',
        processYmd: '2025/07/02',
        parentUserIdSelectDataFlag: false,
        focusSettingInitial: ['や', 'ゆ', 'よ'],
        selectedUserCounter: '2'
      } as Or56885Param,
    },
  })
}
/**************************************************
 * コンポーネント固有処理
 **************************************************/
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr56885"
        >GUI01310_印刷設定</v-btn
      >
      <g-custom-or-56885
        v-if="showDialogOr56885"
        v-bind="or56885"
      />
    </c-v-col>
  </c-v-row>
</template>
