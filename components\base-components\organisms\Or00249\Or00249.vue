<script setup lang="ts">
/**
 * Or00249：有機体：（利用者基本）利用者選択
 *
 * @description
 * （利用者基本）利用者選択コンポーネント。
 */
import { onBeforeUnmount, onMounted, ref, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or00249Const } from './Or00249.constants'
import type { Or00249StateType, userListItemType } from './Or00249.type'
import { Or00249Logic } from './Or00249.logic'
import { useScreenOneWayBind } from '~/composables/useComponentVue'
import { useCommonProps } from '~/composables/useCommonProps'

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
const props = defineProps(useCommonProps())

/**************************************************
 * 変数定義
 **************************************************/
/** リストのスタイル定義（リアクティブ変数） */
const listStyles = ref({
  maxHeight: '350px',
  overflowY: 'auto',
  borderTopStyle: 'solid 0.5px rgb(237, 241, 247)',
  borderLeftStyle: 'solid 0.5px rgb(237, 241, 247)',
  borderRightStyle: 'solid 0.5px rgb(237, 241, 247)',
  borderBottomStyle: 'solid 0.5px rgb(237, 241, 247)',
})

/** ステート用変数 */
const orUserSelectState = ref<Or00249StateType>({
  /** 利用者配列 */
  userList: [] as userListItemType[],
  userListUpdateFlg: false,
})

/** HTMLCanvasElementインタフェース */
const listRef = ref<HTMLCanvasElement | null>(null)

/** アクティブフラグ配列（何番目の利用者がアクティブになっているか）） */
const activeFlgArray = ref(Array(1).fill(false))

/** 利用者一覧情報の最大のインデックス */
const maxIndexOfUserList = ref(-1)

/** リストアイテムの高さ */
const listItemHeight = ref(67.6)

// リストアイテムのdivコンポーネント
const listItemDiv = ref<HTMLDivElement | null>(null)

// CVListのID(スクロールの設定用)
const cVListId = ref('id-Or00249')

/**************************************************
 * Pinia
 **************************************************/
useScreenOneWayBind<Or00249StateType>({
  cpId: Or00249Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    userListUpdateFlg: (flg) => {
      if (flg === true) {
        // 自身のピニア領域の利用者一覧情報をローカル変数に設定
        const workUserList = Or00249Logic.state.get(props.uniqueCpId)?.userList

        maxIndexOfUserList.value = -1 // 利用者一覧情報の最大のインデックスを初期化

        if (workUserList !== undefined) {
          orUserSelectState.value.userList = workUserList
          maxIndexOfUserList.value = workUserList.length - 1
        }

        // CVListのIDを算出
        calcCVListId()

        nextTick()
          .then(() => {
            // スクロールの位置調整
            // （スクロールバーが表示されてから実行するため、nextTick後に実行）
            adjustmentScrollPosition()
          })
          .catch(() => {}) // asyncのESLintエラー回避

        // 一覧情報更新フラグをfalseに戻す
        Or00249Logic.state.set({
          uniqueCpId: props.uniqueCpId,
          state: { userList: orUserSelectState.value.userList, userListUpdateFlg: false },
        })
      }
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * コンポーネントがDOMにマウントされた後に実行されます。
 *
 * @remarks
 * - 画面リサイズでリストのスタイルを変更するイベントリスナーを追加します。
 */
onMounted(() => {
  window.addEventListener('resize', setListStyles)
  setListStyles()
})

/**
 * コンポーネントインスタンスがアンマウントされる直前に実行されます。
 *
 * @remarks
 * - 画面リサイズでリストのスタイルを変更するイベントリスナーを外します。
 */
onBeforeUnmount(() => {
  window.removeEventListener('resize', setListStyles)
})

/**
 * 画面リサイズでリストのスタイルを変更
 */
function setListStyles() {
  const listElement = listRef.value
  const screenHeight = window.innerHeight

  if (listElement?.getBoundingClientRect()) {
    const rect = listElement.getBoundingClientRect()
    const distanceToBottom = screenHeight - rect.top
    const maxVisibleHeight = distanceToBottom - 70

    listStyles.value = {
      maxHeight: `${maxVisibleHeight}px`,
      overflowY: 'auto',
      borderTopStyle: 'solid 0.5px rgb(237, 241, 247)',
      borderLeftStyle: 'solid 0.5px rgb(237, 241, 247)',
      borderRightStyle: 'solid 0.5px rgb(237, 241, 247)',
      borderBottomStyle: 'solid 0.5px rgb(237, 241, 247)',
    }
  }
}

/**
 * 利用者をクリックした際の処理
 *
 * @param index - クリックした利用者のインデックス
 */
function clickUser(index: number) {
  // 自身のTwoway領域の設定
  Or00249Logic.data.set({
    uniqueCpId: props.uniqueCpId,
    value: { selectUserIndex: index },
  })
}

/**
 * 何番目の利用者がアクティブか監視
 */
watch(
  () => Or00249Logic.data.get(props.uniqueCpId),
  (newValue) => {
    // アクティブフラグ配列を更新
    if (newValue?.selectUserIndex !== undefined) {
      if (activeFlgArray.value[newValue?.selectUserIndex] === undefined) {
        activeFlgArray.value = [] as boolean[]
        activeFlgArray.value = Array(orUserSelectState.value.userList.length).fill(false)
        activeFlgArray.value[newValue?.selectUserIndex] = true
      } else if (activeFlgArray.value[newValue?.selectUserIndex] === false) {
        activeFlgArray.value = [] as boolean[]
        activeFlgArray.value = Array(orUserSelectState.value.userList.length).fill(false)
        activeFlgArray.value[newValue?.selectUserIndex] = true
      }
    }
  }
)

/**
 * アラートバッジ1の表示
 *
 * @param ninteiJoutai - 認定状態
 */
function compAlertBadge1(ninteiJoutai: number) {
  const workDispValue = ref('') // 表示する値

  switch (ninteiJoutai) {
    case 0:
      workDispValue.value = ''
      break
    case 1: // 「申」を表示
      workDispValue.value = t('label.certification-badge-shin')
      break
    case 2: // 「認」を表示
      workDispValue.value = t('label.certification-badge-nin')
      break
    case 3: // 「未」を表示
      workDispValue.value = t('label.certification-badge-mi')
      break
    default:
    // 処理なし
  }

  return workDispValue.value
}

/**
 * アラートバッジ2の表示
 *
 * @param zengetuFlg - 前月退所フラグ
 */
function compAlertBadge2(zengetuFlg: number) {
  const workDispValue = ref('') // 表示する値

  if (zengetuFlg === 1) {
    // 「退」を表示
    workDispValue.value = t('label.zeigetutaisyo-badge-tai')
  }

  return workDispValue.value
}

/**
 * アラートバッジ3の表示
 *
 * @param riyoumaeFlg - 利用開始前フラグ
 */
function compAlertBadge3(riyoumaeFlg: number) {
  const workDispValue = ref('') // 表示する値

  if (riyoumaeFlg === 1) {
    // 「前」を表示
    workDispValue.value = t('label.riyoukaisi-mae-badge-zen')
  }

  return workDispValue.value
}

/**
 * スクロールの位置調整
 * - 50音ヘッドラインのフィルター切り替え後、
 * - 選択中の利用者が見える位置にスクロールバーを移動
 */
function adjustmentScrollPosition() {
  if (Or00249Logic.data.get(props.uniqueCpId)?.selectUserIndex !== undefined) {
    const workIndex = Or00249Logic.data.get(props.uniqueCpId)?.selectUserIndex
    const elementOr00249 = document.getElementById(cVListId.value)

    if (elementOr00249?.scrollTop !== undefined && workIndex !== undefined) {
      elementOr00249.scrollTop = workIndex * listItemHeight.value // 選択中のインデックス * リストアイテム1個分の高さ
    }
  }
}

/**
 * CVListのIDに一意の値を設定する。
 * 同時に複数の利用者一覧や職員一覧が表示されている場合を考慮し、
 * 意図した1つの一覧だけのスクロールの位置調整ができるようにするための対応。
 */
function calcCVListId() {
  cVListId.value = 'id-Or00249-' + getNowUnixTime() + '-' + getRandomNum()
}

/**
 * 現在のUnixタイム取得
 * 現在の年月日・時刻をUnix時間で文字列で返却します。
 * 13文字になる想定。
 */
function getNowUnixTime(): string {
  return Date.now().toString()
}

/**
 * ランダムな数値を7桁の文字列で返却します。
 */
function getRandomNum(): string {
  return ('0000000' + Math.floor(Math.random() * 10000000)).slice(-7)
}

onMounted(() => {
  // リストアイテムの高さを取得
  const resizeObserver = new ResizeObserver((entries) => {
    listItemHeight.value = entries[0].contentRect.height
  })
  if (listItemDiv.value) {
    resizeObserver.observe(listItemDiv.value)
  }
})
</script>

<template>
  <div
    ref="listRef"
    style="width: 240px"
  >
    <c-v-list
      :id="cVListId"
      name="userSelectorList"
      class="pl-0 pa-0"
      variant="outlined"
      :style="listStyles"
      elevation="0"
    >
      <c-v-list-item
        v-for="(user, i) in orUserSelectState.userList"
        :key="user.selfId.toString()"
        ref="listItemDiv"
        :value="user"
        active-class="user-active-style"
        color="key"
        class="pa-0 ma-0"
        :active="activeFlgArray[i]"
        :height="listItemHeight"
        @click="clickUser(i)"
      >
        <c-v-list-item-title class="">
          <c-v-col class="pa-0 pl-4">
            <c-v-row class="pa-2 pl-0 ma-auto">
              <!-- 利用者名の表示 -->
              <div class="user-name-style">
                {{ user.nameSei + ' ' + user.nameMei }}
              </div>
            </c-v-row>
            <c-v-row class="pa-0 ma-auto">
              <div class="alert-badge-outer">
                <div
                  class="pa-0 ma-0"
                  style="width: 24px"
                >
                  <!-- アラートバッジ1 認定状態の表示 -->
                  <div
                    v-if="user.ninteiJoutai !== 0"
                    class="alert-badge1"
                  >
                    <p style="text-align: center">{{ compAlertBadge1(user.ninteiJoutai) }}</p>
                  </div>
                </div>
                <div
                  class="pa-0 ma-0"
                  style="width: 24px"
                >
                  <!-- アラートバッジ2 前月退所の表示 -->
                  <div
                    v-if="user.zengetuFlg === 1"
                    class="alert-badge2"
                  >
                    <p style="text-align: center">{{ compAlertBadge2(user.zengetuFlg) }}</p>
                  </div>
                </div>
                <div
                  class="pa-0 ma-0"
                  style="width: 24px"
                >
                  <!-- アラートバッジ3 利用開始前の表示 -->
                  <div
                    v-if="user.riyoumaeFlg === 1"
                    class="alert-badge3"
                  >
                    <p style="text-align: center">{{ compAlertBadge3(user.riyoumaeFlg) }}</p>
                  </div>
                </div>
                <div
                  class="pa-0 ma-0"
                  style="width: 24px"
                >
                  <!-- アラートバッジ4 KBN3の表示 -->
                  <div
                    v-if="user.kbn3 === 1"
                    class="alert-badge4"
                  >
                    <p style="text-align: center">■</p>
                  </div>
                </div>
                <div
                  class="pa-0 ma-0"
                  style="width: 24px"
                >
                  <!-- アラートバッジ5 KBN4の表示 -->
                  <div
                    v-if="user.kbn4 === 1"
                    class="alert-badge5"
                  >
                    <p style="text-align: center">■</p>
                  </div>
                </div>
                <div
                  class="pa-0 ma-0"
                  style="width: 14px"
                ></div>
                <div class="pa-0 ma-0">
                  <!-- 利用者番号の表示 -->
                  <base-at-label
                    class="self-id-style pa-0 pl-2 pr-2"
                    :value="user.selfId"
                  />
                </div>
              </div>
            </c-v-row>
            <div style="height: 5px; width: 100%"></div>
          </c-v-col>
        </c-v-list-item-title>

        <!-- 区切り線を表示 -->
        <c-v-divider
          v-if="i < maxIndexOfUserList"
          :thickness="2"
        />
      </c-v-list-item>
    </c-v-list>
    <div v-if="orUserSelectState.userList.length === 0">
      <div
        style="height: 20px; width: 100px"
        class="pa-0 pl-2"
      >
        <base-at-label :value="'(' + orUserSelectState.userList.length + ' 名)'" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
/** 利用者名 */
.user-name-style {
  font-size: 12pt;
  text-align: left;
}

/** 利用者番号バッジ */
.self-id-style {
  background: rgb(var(--v-theme-black-100));
  padding-left: 2px;
  font-size: 10pt;
  border-radius: 4px;
}

/** 利用者の選択時の背景色 */
.user-active-style {
  background: rgb(var(--v-theme-background));
}

/** アラートバッジの外側 */
.alert-badge-outer {
  width: 100%;
  height: auto;

  display: flex;
  justify-content: left; /* 横方向 左ぞろえ */
  align-items: center; /* 縦方向 */
}

/** アラートバッジ1 認定状態 */
.alert-badge1 {
  background: rgb(var(--v-theme-yellow-400)); // 黄色
  font-size: 10pt;
  font-weight: bold;
  border-radius: 4px;
  width: 20px;
  user-select: none;
}

/** アラートバッジ2 前月退所の表示 */
.alert-badge2 {
  background: rgb(var(--v-theme-yellow-400)); // 黄色
  font-size: 10pt;
  font-weight: bold;
  border-radius: 4px;
  width: 20px;
  user-select: none;
}

/** アラートバッジ3 利用開始前の表示 */
.alert-badge3 {
  background: rgb(var(--v-theme-yellow-400)); // 黄色
  font-size: 10pt;
  font-weight: bold;
  border-radius: 4px;
  width: 20px;
  user-select: none;
}

/** アラートバッジ4  KBN3の表示 */
.alert-badge4 {
  background: rgb(var(--v-theme-yellow-400)); // 黄色
  font-size: 10pt;
  font-weight: bold;
  border-radius: 4px;
  width: 20px;
  user-select: none;
}

/** アラートバッジ5  KBN4の表示 */
.alert-badge5 {
  background: rgb(var(--v-theme-yellow-400)); // 黄色
  font-size: 10pt;
  font-weight: bold;
  border-radius: 4px;
  width: 20px;
  user-select: none;
}
</style>
