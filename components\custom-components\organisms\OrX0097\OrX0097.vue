<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or10494Const } from '../Or10494/Or10494.constants'
import { Or10494Logic } from '../Or10494/Or10494.logic'
import { Or27615Const } from '../Or27615/Or27615.constants'
import { Or27615Logic } from '../Or27615/Or27615.logic'
import type { Or27615Param } from '../Or27615/Or27615.type'
import { Or51990Const } from '../Or51990/Or51990.constants'
import { Or51990Logic } from '../Or51990/Or51990.logic'
import { OrX0097Const } from './OrX0097.constants'
import { OrX0097Logic } from './OrX0097.logic'
import type { OrX0097OnewayType, OrX0097Param, OrX0097StateType } from './OrX0097.type'
import type { Or51990OnewayType } from '~/types/cmn/business/components/Or51990Type'
import type { Mo01344OnewayType } from '~/types/business/components/Mo01344Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import {
  hasRegistAuth,
  useScreenOneWayBind,
  useScreenStore,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'

/**
 * OrX0097:課題立案ダイアログ
 *
 * @description
 * 課題立案ダイアログ
 *
 * <AUTHOR>
 */

/************************************************
 * Props
 ************************************************/

interface Props {
  uniqueCpId: string
  onewayModelValue: OrX0097OnewayType
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

// システム共有情報取得
const systemCommonsStore = useSystemCommonsStore()

const or10494 = ref({ uniqueCpId: Or10494Const.CP_ID(0) })
const or27615 = ref({ uniqueCpId: '' })
const or51990 = ref({ uniqueCpId: '' })
//Or21814_有機体:確認ダイアログ
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })

// ダイアログ
const mo00024Oneway = ref<Mo00024OnewayType>({
  width: '1180px',
  height: '781px',
  persistent: true,
  showCloseBtn: true,
  mo01344Oneway: {
    name: 'OrX0097',
    toolbarTitle: '',
    toolbarName: 'OrX0097ToolBar',
    toolbarTitleCenteredFlg: false,
    showCardActions: true,
    cardTextClass: 'pa-0',
  } as Mo01344OnewayType,
})

const mo00024 = ref<Mo00024Type>({
  isOpen: OrX0097Const.DEFAULT.IS_OPEN,
})

const local = reactive({
  // タブ
  mo00043: {
    id: '',
  } as Mo00043Type,
  kbnFlg: '',
  or51990: {
    svJigyoId: '',
    kinouKbn: '',
    youshikiKbn: '',
  } as Or51990OnewayType,
})
//閉じる
const isClose = ref(false)

const defaultOneway = reactive({
  // タブ
  mo00043OnewayType: {
    tabItems: [],
    minWidth: '58px',
  } as Mo00043OnewayType,
  // 閉じるコンポーネント
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 保存コンポーネント
  mo00609SaveOneway: {
    btnLabel: t('btn.save'),
    disabled: false,
  } as Mo00609OnewayType,
})

/**************************************************
 * ライフサイクルフック
 **************************************************/
// ダイアログ表示フラグ
const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or10494Const.CP_ID(0)]: or10494.value,
  [Or27615Const.CP_ID(0)]: or27615.value,
  [Or51990Const.CP_ID(1)]: or51990.value,
})

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  let isEditFlg = false
  switch (local.mo00043.id) {
    case OrX0097Const.DEFAULT.TAB_1:
      isEditFlg = useScreenStore().isEditByUniqueCpId(or51990.value.uniqueCpId)
      break
    case OrX0097Const.DEFAULT.TAB_2:
      isEditFlg = useScreenStore().isEditByUniqueCpId(or27615.value.uniqueCpId)
      break
    case OrX0097Const.DEFAULT.TAB_3:
      isEditFlg = useScreenStore().isEditByUniqueCpId(or10494.value.uniqueCpId)
      break
    case OrX0097Const.DEFAULT.TAB_4:
      isEditFlg = false // タブ4
      break
    case OrX0097Const.DEFAULT.TAB_5:
      isEditFlg = false // タブ5
      break
    default:
      isEditFlg = false
  }
  return isEditFlg
})

// 共通処理の編集権限チェック
const editFlg = ref(false)

//タブId
const tabId = ref('')

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<OrX0097StateType>({
  cpId: OrX0097Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? OrX0097Const.DEFAULT.IS_OPEN
    },
  },
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  editFlg.value = await hasRegistAuth()
  editFlg.value = true
  init()
})

const init = () => {
  defaultOneway.mo00043OnewayType.tabItems = [
    {
      id: OrX0097Const.DEFAULT.TAB_1,
      title: t('label.issues-planning-1-title'),
      tooltipText: t('label.issues-planning-1-title'),
      tooltipLocation: 'bottom',
    },
    {
      id: OrX0097Const.DEFAULT.TAB_2,
      title: t('label.issues-planning-2-title'),
      tooltipText: t('label.issues-planning-2-title'),
      tooltipLocation: 'bottom',
    },
    {
      id: OrX0097Const.DEFAULT.TAB_3,
      title: t('label.issues-planning-3-title'),
      tooltipText: t('label.issues-planning-3-title'),
      tooltipLocation: 'bottom',
    },
    {
      id: OrX0097Const.DEFAULT.TAB_4,
      title: t('label.issues-planning-4-title'),
      tooltipText: t('label.issues-planning-4-title'),
      tooltipLocation: 'bottom',
    },
    {
      id: OrX0097Const.DEFAULT.TAB_5,
      title: t('label.issues-planning-5-title'),
      tooltipText: t('label.issues-planning-5-title'),
      tooltipLocation: 'bottom',
    },
  ]

  //編集権限
  defaultOneway.mo00609SaveOneway.disabled = !editFlg.value

  // 共通情報.システム略称は"CPN"の場合
  // 区分フラグ＝１
  // ■共通情報.システム略称は"CPN"以外の場合
  // 区分フラグ＝２
  local.kbnFlg = systemCommonsStore.getSystemAbbreviation ?? '1'
  local.or51990 = { ...props.onewayModelValue }
}

/**
 * AC009 「保存ボタン」押下
 *
 * @param isChangeTab -切り替え時：true
 */
const save = (isChangeTab?: boolean) => {
  // 画面入力データ変更があるかどうかを判定する
  if (!isEdit.value) {
    showOr21814MsgOneBtn(t('message.i-cmn-21800'))
    return
  }
  switch (local.mo00043.id) {
    // タブ1
    case OrX0097Const.DEFAULT.TAB_1:
      // Or51990のダイアログ状態を更新する
      Or51990Logic.state.set({
        uniqueCpId: or51990.value.uniqueCpId,
        state: {
          param: {
            executeFlag: 'save',
            svJigyoId: local.or51990.svJigyoId,
            kinouKbn: local.or51990.kinouKbn,
            youshikiKbn: local.or51990.youshikiKbn,
            isClose: isClose.value,
            isChangeTab,
          } as OrX0097Param,
        },
      })
      break
    // タブ2
    case OrX0097Const.DEFAULT.TAB_2:
      // Or27615のダイアログ状態を更新する
      Or27615Logic.state.set({
        uniqueCpId: or27615.value.uniqueCpId,
        state: {
          param: {
            executeFlag: 'save',
            isClose: isClose.value,
          } as Or27615Param,
        },
      })
      break
    // タブ3
    case OrX0097Const.DEFAULT.TAB_3:
      // Or10494のダイアログ状態を更新する
      Or10494Logic.state.set({
        uniqueCpId: or10494.value.uniqueCpId,
        state: {
          param: {
            executeFlag: 'save',
            kbnFlg: local.kbnFlg,
            isClose: isClose.value,
            isChangeTab,
          } as OrX0097Param,
        },
      })
      break
    // タブ4
    case OrX0097Const.DEFAULT.TAB_4:
      break
    // タブ5
    case OrX0097Const.DEFAULT.TAB_5:
      break
    default:
      break
  }
}

/**
 * 画面最新情報を取得する
 */
const getTabsData = () => {
  switch (local.mo00043.id) {
    // タブ1
    case OrX0097Const.DEFAULT.TAB_1:
      // Or51990のダイアログ状態を更新する
      Or51990Logic.state.set({
        uniqueCpId: or51990.value.uniqueCpId,
        state: {
          param: {
            executeFlag: 'getData',
            svJigyoId: local.or51990.svJigyoId,
            kinouKbn: local.or51990.kinouKbn,
            youshikiKbn: local.or51990.youshikiKbn,
            isClose: isClose.value,
          } as OrX0097Param,
        },
      })
      break
    // タブ2
    case OrX0097Const.DEFAULT.TAB_2:
      // Or27615のダイアログ状態を更新する
      Or27615Logic.state.set({
        uniqueCpId: or27615.value.uniqueCpId,
        state: {
          param: {
            executeFlag: 'getData',
            editFlg: editFlg.value,
          } as Or27615Param,
        },
      })
      break
    // タブ3
    case OrX0097Const.DEFAULT.TAB_3:
      // Or10494のダイアログ状態を更新する
      Or10494Logic.state.set({
        uniqueCpId: or10494.value.uniqueCpId,
        state: {
          param: {
            executeFlag: 'getData',
            kbnFlg: local.kbnFlg,
          } as OrX0097Param,
        },
      })
      break
    // タブ4
    case OrX0097Const.DEFAULT.TAB_4:
      break
    // タブ5
    case OrX0097Const.DEFAULT.TAB_5:
      break
    default:
      break
  }
}

/**
 * AC003タコンテンツエリアタブ切替
 *
 * @param param - タブ選択ID
 */
const updateModelValue = async (param: Mo00043Type) => {
  if (isEdit.value) {
    const res = await showDialogOr21814Fun()
    if (!editFlg.value) {
      if (res === 'yes') {
        local.mo00043.id = param.id
      }
      return
    }
    switch (res) {
      case 'yes':
        // AC003(保存処理)を実行し
        tabId.value = param.id
        save(true)
        break
      case 'no':
        // 処理続き
        local.mo00043.id = param.id
        break
      case 'cancel':
        // 処理終了
        return
    }
  } else {
    local.mo00043.id = param.id
    getTabsData()
  }
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    mo00024.value.isOpen = true
    // 組織dialog自動クローズを手動判定に変更
    if (!newValue) {
      void close()
    }
  }
)

/**
 * タブ切り替え
 *
 * @description
 * タブ切り替え
 */

watch(
  () => local.mo00043.id,
  (newVal) => {
    mo00024Oneway.value.mo01344Oneway!.toolbarTitle =
      defaultOneway.mo00043OnewayType.tabItems.find((item) => item.id === newVal)?.title +
      t('label.master')
    getTabsData()
  }
)

/**
 * 権限変更
 *
 * @description
 * 権限変更
 */
watch(
  () => editFlg.value,
  (newVal) => {
    defaultOneway.mo00609SaveOneway.disabled = !newVal
  }
)

/**
 * 画面が閉じます
 */
/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
const close = async () => {
  if (!isEdit.value) {
    // 画面を閉じる。
    setState({ isOpen: false })
  } else {
    isClose.value = true
    const res = await showDialogOr21814Fun()
    if (!editFlg.value) {
      if (res === 'yes') {
        setState({ isOpen: false })
      }
      return
    }
    switch (res) {
      case 'yes':
        // AC003(保存処理)を実行し
        save()
        break
      case 'no':
        // 処理続き
        setState({ isOpen: false })
        break
      case 'cancel':
        // 処理終了
        return
    }
  }
}

/**
 * 共通処理の編集権限チェックを行う。
 */
const showDialogOr21814Fun = async () => {
  // TODO 共通処理の編集権限チェックを行う。
  if (!editFlg.value) {
    // 変更がある場合、確認ダイアログを表示する。(保存権限がない場合)
    return await showOr21814MsgTwoBtn(t('message.i-cmn-10006'))
  } else {
    // 変更がある場合、確認ダイアログを表示する。(保存権限がある場合)
    return await showOr21814MsgThreeBtn(t('message.i-cmn-10430'))
  }
}
/**
 * 閉じるボタン押下_保存権限がない場合の開閉
 *
 * @param errormsg - Message
 */
function showOr21814MsgTwoBtn(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: errormsg,
      firstBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.no'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = 'cancel'

        if (event?.secondBtnClickFlg) {
          result = 'yes'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })
        resolve(result)
      },
      { once: true }
    )
  })
}
/**
 * 閉じるボタン押下_保存権限がある場合の開閉
 *
 * @param errormsg - Message
 */
function showOr21814MsgThreeBtn(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト0
      dialogText: errormsg,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = 'cancel'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })
        resolve(result)
      },
      { once: true }
    )
  })
}
/**
 * 保存ボタン押下
 * 変更されている項目がないため、保存を行うことは出来ません。「改行」項目を入力変更してから、再度保存を行ってください。
 *
 * @param errormsg - Message
 */
function showOr21814MsgOneBtn(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: errormsg,
      firstBtnType: 'blank',
      thirdBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.ok'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 保存が完了したら切り替え
 */
watch(
  () => OrX0097Logic.event.get(props.uniqueCpId),
  (newVal) => {
    if (newVal?.isSave) {
      local.mo00043.id = tabId.value
      OrX0097Logic.event.set({
        uniqueCpId: props.uniqueCpId,
        state: {
          isSave: false,
        },
      })
    }
  }
)
</script>

<template>
  <!-- ダイアログ -->
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="mo00024Oneway"
  >
    <template #cardItem>
      <div class="d-flex flex-column h-100">
        <base-mo00043
          :model-value="local.mo00043"
          :oneway-model-value="defaultOneway.mo00043OnewayType"
          @update:model-value="updateModelValue"
        >
        </base-mo00043>
        <c-v-window
          v-model="local.mo00043.id"
          class="pa-2 flex-1"
        >
          <c-v-window-item :value="OrX0097Const.DEFAULT.TAB_1">
            <g-custom-or51990
              v-bind="or51990"
              :parent-unique-cp-id="props.uniqueCpId"
          /></c-v-window-item>
          <c-v-window-item :value="OrX0097Const.DEFAULT.TAB_2"
            ><g-custom-Or27615
              v-bind="or27615"
              :parent-unique-cp-id="props.uniqueCpId"
          /></c-v-window-item>
          <c-v-window-item
            :value="OrX0097Const.DEFAULT.TAB_3"
            class="h-100"
            ><g-custom-or10494
              v-bind="or10494"
              :parent-unique-cp-id="props.uniqueCpId"
          /></c-v-window-item>
          <c-v-window-item :value="OrX0097Const.DEFAULT.TAB_4"></c-v-window-item>
          <c-v-window-item :value="OrX0097Const.DEFAULT.TAB_5"></c-v-window-item>
        </c-v-window>
      </div>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="defaultOneway.mo00611CloseBtnOneWay"
          @click="close"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <!-- 保存ボタン Mo00611 -->
        <base-mo00609
          v-bind="defaultOneway.mo00609SaveOneway"
          class="ml-2"
          @click="save()"
        >
          <!--ツールチップ表示："保存します"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.save')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  />
</template>

<style scoped lang="scss">
.flex-1 {
  flex: 1;
}

:deep(.v-window__container) {
  height: 100%;
}
</style>
