<script setup lang="ts">
/**
 * Or10017:（（予防計画書）総合的な方針）ダイアログ
 * GUI01106_総合的な方針
 *
 * @description
 * （（予防計画書）総合的な方針）ダイアログ
 *
 * <AUTHOR> PHAM TIEN THANH
 */
import { useI18n } from 'vue-i18n'
import { Or10883Logic } from '../Or10883/Or10883.logic'
import { Or10883Const } from '../Or10883/Or10883.constants'
import { Or10017Const } from './Or10017.constants'
import type { Or10017StateType } from './Or10017.type'
import {
  reactive,
  watch,
  onMounted,
  computed,
  ref,
  useScreenOneWayBind,
  useScreenUtils,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Or10017OnewayType, Or10017Type } from '~/types/cmn/business/components/Or10017Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00046OnewayType, Mo00046Type } from '~/types/business/components/Mo00046Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type {
  Or10883OnewayType,
  Or10883TwowayType,
} from '~/types/cmn/business/components/Or10883Type'
import type { Mo01299OnewayType } from '~/types/business/components/Mo01299Type'
import type { At00014OnewayType } from '~/types/business/components/At00014Type'

useScreenUtils()
const { t } = useI18n()

const systemCommonsStore = useSystemCommonsStore()
/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or10017Type
  onewayModelValue: Or10017OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

const defaultOnewayModelValue: Or10017OnewayType = {
  dispFlg: '0',
  formatFlg: '0',
  hisFlg: '0',
  planId: '0',
  startPattern: '0',
  comprehensivePolicy: '',
}

const mo00046Oneway_readonly = ref(true)
const mo00046Oneway_disabled = ref(true)
const mo00046Oneway: Mo00046OnewayType = reactive({
  name: 'OrComprehensivePolicyInput',
  showItemLabel: false,
  readonly: mo00046Oneway_readonly,
  disabled: mo00046Oneway_disabled,
  counter: 4000,
  maxlength: '4000',
  hideDetails: true,
  class: 'mt-2',
})

// localOneway変数定義
const localOneway = reactive({
  Or10017: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  //［スケジュール形式は「A4横3ページ」
  mo00024Oneway: {
    width: '950px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or10017',
      toolbarTitle: t('label.comprehensive-policy'),
      toolbarName: 'Or10017ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  // スケジュールの形式が「A4横3ページ」ではない
  mo00024Oneway2: {
    width: '600px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or10017_2',
      toolbarTitle: t('label.comprehensive-policy'),
      toolbarName: 'Or10017ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  // 閉じる
  mo00611OneWay: {
    btnLabel: t('btn.close'),
    tooltipLocation: 'bottom',
    tooltipText: t('tooltip.screen-close'),
  } as Mo00611OnewayType,
  at00014OneWay: {
    itemLabel: t('label.comprehensive-policy-improvement-prevention'),
  } as At00014OnewayType,
  // アイコンボタンへの単方向モデルバリュー
  mo00009Oneway: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    disabled: false,
    density: 'compact',
  } as Mo00009OnewayType,
  //（目標とする生活（１年））入力支援ダイアログ
  or10883Oneway: {
    shokuinId: '',
    t1Cd: '40',
    t2Cd: '13',
    t3Cd: '0',
    title: t('label.comprehensive-policy-improvement-prevention'),
    inputContents: '',
    userId: systemCommonsStore.getUserId,
    historyTableName: 'kyc_tuc_plan1',
    historyColumnName: 'sogo_hoshin_knj',
  } as Or10883OnewayType,
})

// テキストエリアの内容
const mo00046ModelValue = ref<Mo00046Type>({
  value: '',
})

/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or10017Const.DEFAULT.IS_OPEN,
})
const mo01299OnewayModal = {
  anchorPoint: 'ss-2',
  title: t('label.comprehensive-assistance-policy-message'),
  visible: false,
} as Mo01299OnewayType
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or10017StateType>({
  cpId: Or10017Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or10017Const.DEFAULT.IS_OPEN
    },
  },
})

const or10883ModelValue = ref<Or10883TwowayType>({
  naiyo: '',
  confirmation: '',
})

onMounted(() => {
  init()
})

/**
 * 画面初期処理
 */
function init() {
  const startPattern = props.onewayModelValue.startPattern
  localOneway.mo00009Oneway.disabled = false

  if (startPattern === Or10017Const.START_PATTERN_IS_DUPLICATE) {
    mo00046Oneway_readonly.value = true
    mo00046Oneway_disabled.value = true
    localOneway.mo00009Oneway.disabled = true
  } else {
    mo00046Oneway_readonly.value = false
    mo00046Oneway_disabled.value = false
  }

  mo00046ModelValue.value = { value: localOneway.Or10017.comprehensivePolicy ?? '' }
}
/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    mo00024.value.isOpen = true
    if (!newValue) {
      close()
    }
  }
)

/**
 * 親画面設定
 *
 * @description
 * 親画面を設定する。
 */
function parentScreenSet() {
  // 画面.入力支援テキストエリア内容を親画面に反映する。
  localOneway.or10883Oneway.inputContents = mo00046ModelValue.value.value ?? ''

  // 画面.支援実施テキストエリア内容を親画面に反映する。
  const retData: Or10017Type = {
    comprehensivePolicy: mo00046ModelValue.value.value ?? '',
  }
  emit('update:modelValue', retData)
}

/**
 * ダイアログを閉じる処理
 *
 * @description
 */
function close() {
  // 画面.総合政策テキストエリアの内容を親画面に反映します。
  const retData: Or10017Type = {
    comprehensivePolicy: mo00046ModelValue.value.value ?? '',
  }
  emit('update:modelValue', retData)

  // 画面を閉じる。
  setState({ isOpen: false })
}

/**************************************************
 * イベントハンドラ
 **************************************************/
const or10883 = ref({ uniqueCpId: '' })
/**
 * ダイアログ表示フラグ
 * - Or10883のダイアログ開閉状態を取得
 */
const showDialogOr10883 = computed(() => {
  return Or10883Logic.state.get(or10883.value.uniqueCpId)?.isOpen ?? false
})

useSetupChildProps(props.uniqueCpId, {
  [Or10883Const.CP_ID(0)]: or10883.value,
})

/**
 * Or10883ダイアログを開く処理
 * - ボタン押下時に呼び出される
 * - 子コンポーネントのバインドデータを設定し、ダイアログを開く
 */
function Or10883OnClick() {
  // ダイアログを開く
  Or10883Logic.state.set({
    uniqueCpId: or10883.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}

/**
 * Or10883の更新イベントハンドラ
 *
 * @param value - 更新された値
 */
function handleOr10883Update(value: Or10883TwowayType) {
  or10883ModelValue.value = value
  if (value.naiyo && value.confirmation) {
    mo00046ModelValue.value.value = value.naiyo
  } else {
    mo00046ModelValue.value.value += value.naiyo ?? ''
  }
  parentScreenSet()
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="
      localOneway.Or10017.formatFlg === Or10017Const.FORMAT_A4_3_PAGES
        ? localOneway.mo00024Oneway
        : localOneway.mo00024Oneway2
    "
  >
    <template #cardItem>
      <c-v-row
        no-gutters
        class="container"
      >
        <c-v-col>
          <c-v-row no-gutters>
            <c-v-col>
              <!-- サブセクション -->
              <base-mo01299
                v-if="localOneway.Or10017.hisFlg === Or10017Const.HAS_HISTORICAL_DATA"
                :oneway-model-value="mo01299OnewayModal"
              >
                <template #content>
                  <c-v-row class="first-row">
                    <base-at-label :value="localOneway.at00014OneWay.itemLabel"></base-at-label>
                    <base-mo00009
                      v-if="
                        localOneway.Or10017.startPattern !== Or10017Const.START_PATTERN_IS_DUPLICATE
                      "
                      :oneway-model-value="localOneway.mo00009Oneway"
                      @click="Or10883OnClick"
                    />
                    <!-- Or10883ダイアログ -->
                    <g-custom-or-10883
                      v-if="showDialogOr10883"
                      v-model="or10883ModelValue"
                      v-bind="or10883"
                      :oneway-model-value="localOneway.or10883Oneway"
                      @update:model-value="handleOr10883Update"
                    />
                  </c-v-row>
                  <c-v-row id="returnValue_textArea">
                    <base-mo00046
                      v-model="mo00046ModelValue"
                      :oneway-model-value="mo00046Oneway"
                      @update:model-value="parentScreenSet"
                    ></base-mo00046>
                  </c-v-row>
                </template>
              </base-mo01299>
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>
    <!-- フッター -->
    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          class="mx-2"
          @click="close"
        >
          <!-- ツールチップ表示 -->
          <c-v-tooltip
            v-if="localOneway.mo00611OneWay.tooltipText"
            :text="localOneway.mo00611OneWay.tooltipText"
            :location="localOneway.mo00611OneWay.tooltipLocation"
            activator="parent"
          />
        </base-mo00611>
      </c-v-row>
    </template>
  </base-mo00024>
</template>

<style scoped lang="scss">
@use '~/styles/base.scss';
.col-padding {
  padding: 8px !important;
}

.table-header :deep(.v-table__wrapper th) {
  background-color: rgb(var(--v-theme-black-100)) !important;
  border: 1px rgb(var(--v-theme-black-200)) solid !important;
  font-size: 14px;
  font-weight: bold;
}

:deep(.no-disp .v-data-table-rows-no-data) {
  display: none;
}

.no-disp {
  margin-left: 120px !important;
  margin-right: 120px !important;
}

.width-580 {
  width: 580px;
}

:deep(.section-header) {
  max-width: none !important;
}

.container {
  height: 240px;
  margin: 8px 8px;
}

.first-row {
  justify-content: space-between;
  padding: 0 20px;
  margin-top: 20px;
}

.content-label {
  width: 100%;
}

:deep(.section) {
  display: block !important;
}

#returnValue_textArea div {
  width: 100%;
  padding-left: 5px;
  padding-right: 5px;
  margin: 15px;
}
:deep(.text-style .v-field) {
  border-radius: 4px 4px 4px 4px;
  resize: none;
  overflow: hidden;
  flex: 1;
}
</style>
