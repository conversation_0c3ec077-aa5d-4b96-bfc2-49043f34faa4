import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * Or10826:有機体:（日課表パターン）ダイアログ
 * GUI00990_日課表パターン（設定）
 *
 * @description
 * 静的データ
 *
 * <AUTHOR>
 */
export namespace Or10826Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or10826', seq)

  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false
    /**
     * マスタ区分リスト
     */
    export const MSTKBN_LIST = {
      // 2:日課パターン
      DAILY_PARTTEN: '2',
      // 3:週間パターン
      WEEK_PARTTEN: '3',
      // 4:月年パターン
      MONTHYEAR_PARTTEN: '4',
    }
    /**
     * CANCEL
     */
    export const DIALOG_RESULT_CANCEL = 'cancel'
    /**
     *YES
     */
    export const DIALOG_RESULT_YES = 'yes'
    /**
     *NO
     */
    export const DIALOG_RESULT_NO = 'no'
  }

  /**
   * タブID
   */
  export namespace TAB {
    /**
     * タイトル
     */
    export const TAB_ID_TITLE = 'title'
    /**
     * 設定
     */
    export const TAB_ID_SETTING = 'setting'
    /**
     * グループ
     */
    export const TAB_ID_GROUP = 'group'
  }
}
