/**
 * Or52057:有機体:印刷設定モーダル（画面/特殊コンポーネント）
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR>
 */
export interface Or52057StateType {
  /**
   * 開閉フラグ
   */
  isOpen?: boolean
  /**
   * パラメータ
   */
  param: Or52057Param
}

/**
 * パラメータ構造
 */
export interface Or52057Param {
  /** 施設ID：親画面.施設ID*/
  // shisetuId: string
  /** 事業者ID：親画面.事業者ID */
  // svJigyoId: string
  /** 担当者ID：親画面.担当者ID */
  // tantoId: string
  /** セクション名：親画面.セクション名 */
  // sectionName: string
  /** 利用者ID：親画面.利用者ID */
  // userId: string
  /** 親画面.アセスメントID */
  // assessmentId: string
  /**
   * 帳票番号
   */
  prtNo: string
  /**
   * 事業者ID
   */
  svJigyoId: string
  /**
   * 施設ID
   */
  shisetuId: string
  /**
   * 担当者ID
   */
  tantoId: string
  /**
   * 種別ID
   */
  syubetsuId: string
  /**
   * セクション名
   */
  sectionName: string
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 事業所名
   */
  svJigyoKnj: string
  /**
   * 処理年月日
   */
  processYmd: string
  /**
   * アセスメントID
   */
  assessmentId: string
  /**
   * 利用者情報リストにデータを選択フラゲ
   */
  parentUserIdSelectDataFlag: boolean
  /**
   * フォーカス設定用イニシャル
   */
  focusSettingInitial: string[]
  /**
   * 初期選択状態の担当者カウンタ値
   */
  selectedUserCounter: string
}

/**
 * メッセージのボタンの値の型
 */
export type Or52057MsgBtnType = 'yes' | 'no' | 'cancel'
