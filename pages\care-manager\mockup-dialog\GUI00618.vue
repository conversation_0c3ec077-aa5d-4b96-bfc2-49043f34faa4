<script setup lang="ts">
import { computed, definePageMeta, ref, useScreenStore, watch } from '#imports'
import { Or52916Logic } from '~/components/custom-components/organisms/Or52916/Or52916.logic'
import type { Or52916OnewayType, Or52916Type } from '~/types/cmn/business/components/Or52916Type'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI00618'
// ルーティング
const routing = 'GUI00618/pinia'
// 画面物理名
const screenName = 'GUI00618'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or52916 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00618' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  Or52916Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
or52916.value.uniqueCpId = pageComponent.uniqueCpId

/**************************************************
 * コンポーネント固有処理
 **************************************************/
const or52916Data: Or52916OnewayType = {
  svJigyoId: '',
  sectionNo: '',
  shoninFlg: '',
  sysRyaku: '',
}

const or52916ModelValue = ref<Or52916Type>({
  jigyoNumber: '',
  jigyoKnj: '',
  svJigyoId: '',
  houjinId: '',
  houjinKnj: '',
  shisetuId: '',
  svJigyoCd: '',
  shisetuKnj: '',
  svJigyoKnj: '',
  svKbn: '',
  jigyoRyakuKnj: '',
  houjinRyakuKnj: '',
  shisetsuRyakuKnj: '',
  ruakuKnj: '',
  dmyHoushiki: '',
  num1: '',
  cpnType: '',
  id: '',
  chohyoCd: '',
  dispKbn: '',
  chohyoNm: '',
  cmpModifiedCnt: '',
  text1Knj: '',
  text2Knj: '',
  day1Knj: '',
  day2Knj: '',
  text1Font: '',
  text2Font: '',
  text1Width: '',
  text2Width: '',
  day1Width: '',
  day2Width: '',
  reportInfoHoujinId: '',
  reportInfoShisetuId: '',
  reportInfoSvJigyoId: '',
  reportInfoId: '',
  reportInfoChohyoCd: '',
  orientType: '',
  text3Knj: '',
  day3Knj: '',
  text3Font: '',
  text3Width: '',
  day3Width: '',
  text4Knj: '',
  day4Knj: '',
  text4Font: '',
  text4Width: '',
  day4Width: '',
  reportInfoDispKbn: '',
  reportInfoCmsModifiedCnt: '',
})

/**
 *  ボタン押下時の処理
 *
 * @param str -帳票毎の保持
 *
 * @param str2 -セクション番号
 */
function or52916OnClick(str: string, str2: string) {
  // Or00586のダイアログ開閉状態を更新する
  or52916Data.shoninFlg = str
  or52916Data.sectionNo = str2
  Or52916Logic.state.set({
    uniqueCpId: or52916.value.uniqueCpId,
    state: { isOpen: true },
  })
}
// ダイアログ表示フラグ
const showDialogOr52916 = computed(() => {
  // Or00586のダイアログ開閉状態
  return Or52916Logic.state.get(or52916.value.uniqueCpId)?.isOpen ?? false
})

watch(or52916ModelValue, () => {
  console.log(or52916ModelValue.value)
})
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col cols="12">
      <v-btn
        variant="plain"
        @click="or52916OnClick('1', '3GKV00241P002')"
        >GUI00618_承認欄の複写画面---受領欄の複写 ---(①親画面.セクション番号(istr_prt.section) =
        "3GKV00241P002" OR "3GKV00242P002"の場合)(親画面.帳票毎の保持 = 1(保存単位：共有する)
        の場合、自事業者欄表示)
      </v-btn>
    </c-v-col>
    <c-v-col cols="12">
      <v-btn
        variant="plain"
        @click="or52916OnClick('0', '3GKV00241P002')"
        >GUI00618_承認欄の複写画面---受領欄の複写 ---(①親画面.セクション番号(istr_prt.section) =
        "3GKV00241P002" OR "3GKV00242P002"の場合)(親画面.帳票毎の保持 ≠ 1
        (保存単位：共有する)の場合、自事業者のみ欄非表示)
      </v-btn>
    </c-v-col>
    <c-v-col cols="12">
      <v-btn
        variant="plain"
        @click="or52916OnClick('1', '')"
        >GUI00618_承認欄の複写画面---承認欄の複写
        ---(②親画面.セクション番号(istr_prt.section)①以外の場合)(親画面.帳票毎の保持 =
        1(保存単位：共有する) の場合、自事業者欄表示)
      </v-btn>
    </c-v-col>
    <c-v-col cols="12">
      <v-btn
        variant="plain"
        @click="or52916OnClick('0', '')"
        >GUI00618_承認欄の複写画面---承認欄の複写
        ---(②親画面.セクション番号(istr_prt.section)①以外の場合)(親画面.帳票毎の保持 ≠ 1
        (保存単位：共有する)の場合、自事業者のみ欄非表示)
      </v-btn>
    </c-v-col>
  </c-v-row>
  <g-custom-or-52916
    v-if="showDialogOr52916"
    v-bind="or52916"
    v-model="or52916ModelValue"
    :oneway-model-value="or52916Data"
    :parent-unique-cp-id="pageComponent.uniqueCpId"
  />
</template>
