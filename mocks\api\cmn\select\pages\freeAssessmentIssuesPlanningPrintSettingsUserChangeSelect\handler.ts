import { HttpResponse } from 'msw'
import defaultData from './data/default.json'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { FreeAssessmentIssuesPlanningPrintSettingsUserChangeSelectInEntity } from '~/repositories/cmn/entities/FreeAssessmentIssuesPlanningPrintSettingsSelectEntity.ts'
/**
 * GUI00909_印刷設定
 *
 * @description
 * GUI00909_印刷設定履歴リストデータを返却する。
 * dataName："freeAssessmentIssuesPlanningPrintSettingsUserChangeSelect"
 */
export function handler(inEntity: FreeAssessmentIssuesPlanningPrintSettingsUserChangeSelectInEntity) {
  const responceJson: BaseResponseBody = {
    statusCode: 'success',
    data: {
      ...defaultData,
    },
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
