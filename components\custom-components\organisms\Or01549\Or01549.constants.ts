import { getSequencedCpId } from '#imports'
/**
 * Or01549_（予防基本）コンテンツエリア
 * GUI01067_基本情報
 *
 * @description
 * 静的データ
 *
 * <AUTHOR> HOANG SY TOAN
 */

export namespace Or01549Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or01549', seq)
  /**
   * 全
   */
  export const STR_ALL = '全'
  /**
   *計画管理フラグ：0管理しない
   */
  export const PLAN_TARGET_PERIOD_0 = '0'

  /**
   *計画管理フラグ：1管理する
   */
  export const PLAN_TARGET_PERIOD_1 = '1'

  /**
   *編集破棄ダイアログ
   */
  export const INFO_MESSAGE_TYPE_10430 = '10430'

  /**
   *削除確認ダイアログ
   */
  export const INFO_MESSAGE_TYPE_11326 = '11326'

  /**
   *二回目新規ダイアログ
   */
  export const INFO_MESSAGE_TYPE_11265 = '11265'
  /**
   *11263
   */
  export const INFO_MESSAGE_TYPE_11263 = '11263'
  /**
   *11262
   */
  export const INFO_MESSAGE_TYPE_11262 = '11262'
  /**
   *CANCEL
   */
  export const DIALOG_RESULT_CANCEL = 'cancel'
  /**
   *YES
   */
  export const DIALOG_RESULT_YES = 'yes'
  /**
   *NO
   */
  export const DIALOG_RESULT_NO = 'no'
  /**
   * LINK_AUTH
   */
  export const LINK_AUTH = '/care-manager/basic-info'
  /**
   * historyUpdateKbn: 削除(Delete)
   */
  export const HISTORY_UPDATE_KBN_DELETE = 'D'
  /**
   * historyUpdateKbn: 作成(Create)
   */
  export const HISTORY_UPDATE_KBN_CREATE = 'C'
  /**
   * historyUpdateKbn: 更新(Update)
   */
  export const HISTORY_UPDATE_KBN_UPDATE = 'U'
  /**
   * notificationLabel: 更新(Update)
   */
  export const NOTIIFICATION_REGISTER_TIME = '計画対象期間を固定文字'
}
