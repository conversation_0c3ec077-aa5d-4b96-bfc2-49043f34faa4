<script setup lang="ts">
/**
 * Or29224:状況の事実入力
 * GUI00915_状況の事実
 *
 * @description
 * 状況の事実入力
 *
 * <AUTHOR> NGUYEN MANH HUNG
 */
import { useI18n } from 'vue-i18n'
import { computed, nextTick, reactive, ref, watch } from 'vue'
import { Or29224Const } from './Or29224.constants'
import { useScreenTwoWayBind, useSetupChildProps, useScreenUtils } from '#imports'
import type { Or29224Type, TransmitParam } from '~/types/cmn/business/components/Or29224Type'
import { Or05658Const } from '../Or05658/Or05658.constants'
import type { Or05658Type } from '~/types/cmn/business/components/Or05658Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Or05656OnewayType } from '~/types/cmn/business/components/Or05656Type'
import type { Or10355OnewayType, Or10355Type } from '~/types/cmn/business/components/Or10355Type'
import { CustomClass } from '~/types/CustomClassType'
import type { DataTableType, JyokyoSyosaiType } from '../Or05656/Or05656.type'
import { Or10355Logic } from '../Or10355/Or10355.logic'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { Or10355Const } from '../Or10355/Or10355.constants'
import { UPDATE_KBN } from '~/constants/classification-constants'
import { Or05656Const } from '../Or05656/Or05656.constants'
import { Or05657Const } from '../Or05657/Or05657.constants'
import { Or05657Logic } from '../Or05657/Or05657.logic'
/**
 * useI18n
 */
const { t } = useI18n()
/**
 * Or05658用ref
 */
const or05658 = ref({ uniqueCpId: '' })
/**
 * 画面状態管理用操作変数
 */
const or10355 = ref({ uniqueCpId: '' })
/**
 * Or05656用ref
 */
const or05656 = ref({ uniqueCpId: '' })
/**
 * or05657
 */
const or05657 = ref({ uniqueCpId: '' })
const { setChildCpBinds } = useScreenUtils()
/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or29224Type
  uniqueCpId: string
  parentUniqueCpId: string
}

/**
 * Propsの定義
 */
const props = defineProps<Props>()

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**
 * DataTableTypeをOr05656OnewayType形式に変換する（逆変換）
 * @function transformFromDataTableType
 * @description DataTableType形式からフラットなデータ形式に変換する
 * @param {DataTableType} item 変換対象のDataTableType
 * @returns {JyokyoSyosaiType} 変換されたJyokyoSyosaiType
 */
function transformFromDataTableType(item: DataTableType): JyokyoSyosaiType {
  const { youinFlg, ...rest } = item

  const data = {
    ...rest,
    bikoKnj: item.bikoKnj?.value
      .replace(/\u2026/g, '')
      .replace(/\.\.\./g, '')
      .replace(/\r?\n/g, '')
      .trim(),
    joukyouFlg: item.joukyouFlg?.value,
    genzai1Flg: item.genzai1Flg?.value,
    youin1Flg: youinFlg?.values.includes(Or05656Const.VARIABLE.STRING_1)
      ? Or05656Const.VARIABLE.STRING_1
      : Or05656Const.VARIABLE.STRING_0,
    youin2Flg: youinFlg?.values.includes(Or05656Const.VARIABLE.STRING_2)
      ? Or05656Const.VARIABLE.STRING_2
      : Or05656Const.VARIABLE.STRING_0,
    youin3Flg: youinFlg?.values.includes(Or05656Const.VARIABLE.STRING_3)
      ? Or05656Const.VARIABLE.STRING_3
      : Or05656Const.VARIABLE.STRING_0,
    youin4Flg: youinFlg?.values.includes(Or05656Const.VARIABLE.STRING_4)
      ? Or05656Const.VARIABLE.STRING_4
      : Or05656Const.VARIABLE.STRING_0,
    youin5Flg: youinFlg?.values.includes(Or05656Const.VARIABLE.STRING_5)
      ? Or05656Const.VARIABLE.STRING_5
      : Or05656Const.VARIABLE.STRING_0,
    youin6Flg: youinFlg?.values.includes(Or05656Const.VARIABLE.STRING_6)
      ? Or05656Const.VARIABLE.STRING_6
      : Or05656Const.VARIABLE.STRING_0,
    updateKbn: item.updateKbn || '0',
    koumokuKnj: item.koumokuKnj?.value,
  }
  return data
}

/**
 * グループ化されたデータをフラットな配列に変換する（逆変換）
 * @function ungroupItems
 * @description 2次元配列のグループ化されたデータをフラットな1次元配列に変換する
 * @param {Or05656OnewayType[][]} groupedItems グループ化されたデータ配列
 * @returns {JyokyoSyosaiType[]} フラット化されたデータ配列
 */
function ungroupItems(groupedItems: Or05656OnewayType[][]): JyokyoSyosaiType[] {
  if (!groupedItems) return []

  const flatItems: JyokyoSyosaiType[] = []

  groupedItems.forEach((group) => {
    group.forEach((item) => {
      // 削除されていないアイテムのみを含める
      flatItems.push(transformFromDataTableType(item))
    })
  })

  // ソート順でソート
  return flatItems.sort((a, b) => (Number(a.sort) || 0) - (Number(b.sort) || 0))
}

/**
 * Or05656コンポーネントref
 */
const or05656Ref = ref<{
  init(): AsyncFunction
  createItem(): AsyncFunction
  deleteItem(): AsyncFunction
  moveUp(): AsyncFunction
  moveDown(): AsyncFunction
  selectedRowIndex: number
  totalItemCount: number
}>()
/**
 * システム共有情報取得
 */
const systemCommonsStore = useSystemCommonsStore()
/**
 * useScreenTwoWayBind
 */
const { refValue } = useScreenTwoWayBind<Or29224Type>({
  cpId: Or29224Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or05658Const.CP_ID(0)]: or05658.value,
  [Or10355Const.CP_ID(0)]: or10355.value,
  [Or05656Const.CP_ID(0)]: or05656.value,
  [Or05657Const.CP_ID(0)]: or05657.value,
})

/**
 * ローカルのOnewayオブジェクト
 */
const localOneway = reactive({
  mo00009OnewayAssessment: {
    btnIcon: 'edit_square',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
    tooltipText: t('tooltip.displays-the-assessment-import-screen'),
  } as Mo00009OnewayType,
  or28285Oneway: {
    btnIcon: 'edit_square',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
    tooltipText: t('tooltip.displays-the-notes-screen'),
  } as Mo00009OnewayType,
  /**
   * Or10030用の単方向バインド値
   */
  or10355OnewayModel: {
    shisetuId: '',
    menu2Knj: '',
    menu3Knj: '',
    valS: '',
    kikanFlg: '',
  } as Or10355OnewayType,
})
/** 表示頁ラベルの単方向モデル */
const mo01338OnewayAssessment = ref<Mo01338OnewayType>({
  value: t('label.assessment'),
  customClass: new CustomClass({
    outerClass: 'm-0 p-0',
    labelClass: 'm-0 p-0',
    itemClass: 'm-0 p-0',
  }),
})
/** 表示頁ラベルの単方向モデル */
const mo01338OnewayNotes = ref<Mo01338OnewayType>({
  value: t('label.notes'),
  customClass: new CustomClass({
    outerClass: 'm-0 p-0',
    labelClass: 'm-0 p-0',
    itemClass: 'm-0 p-0',
  }),
})
/**
 * フォーム値のローカル状態
 */
const local = reactive({
  transmitParam: {
    executeFlag: '',
    deleteBtnValue: '',
    kikanKanriFlg: '',
    houjinId: '',
    shisetuId: '',
    userId: '',
    svJigyoId: '',
    kijunbiYmd: '',
    sakuseiId: '',
    historyModifiedCnt: '',
    historyInfo: {},
  } as TransmitParam,
  or05658: {} as Or05658Type,
  /**
   * Or10355コンポーネントの型参照（出力項目付き）
   */
  or10355Value: {
    raiId: '',
  } as Or10355Type,
})

watch(
  () => refValue.value,
  async (newVal) => {
    if (newVal) {
      local.or05658 = newVal.rirekiObj

      setChildCpBinds(props.uniqueCpId, {
        Or05658: {
          twoWayValue: local.or05658,
        },
      })
      let data = initGroupedItems(newVal.jyokyoSyosaiList) as Or05656OnewayType[][]

      setChildCpBinds(props.uniqueCpId, {
        Or05656: {
          twoWayValue: data,
        },
      })
      await nextTick()
      or05656Ref.value?.init()
    }
  },
  {
    immediate: true,
    deep: true,
  }
)

/**
 * xxx
 */
const onAddItem = () => {
  or05656Ref.value?.createItem()
}
/**
 * xxx
 */
const onDeleteItem = () => {
  or05656Ref.value?.deleteItem()
}
/**
 * GUI00920_ｱｾｽﾒﾝﾄ履歴取込画面をポップアップで起動する。
 */
const onClickOr10355 = () => {
  localOneway.or10355OnewayModel = {
    shisetuId: systemCommonsStore.getShisetuId!,
    userId: systemCommonsStore.getUserId!,
    svJigyoId: systemCommonsStore.getSvJigyoId!,
  } as Or10355OnewayType
  Or10355Logic.state.set({
    uniqueCpId: or10355.value.uniqueCpId,
    state: { isOpen: true },
  })
}
/**
 * Or28285のダイアログ開閉状態を更新する
 */
const onClickOr05657 = () => {
  Or05657Logic.state.set({
    uniqueCpId: or05657.value.uniqueCpId,
    state: { isOpen: true },
  })
}
/**
 * or26184コンポーネントの状態
 */
const or26184 = ref({
  selectedIndex: computed(() => or05656Ref.value?.selectedRowIndex ?? 0),
  totalLine: computed(() => or05656Ref.value?.totalItemCount ?? 0),
})
/**
 * 上へ移動アイコン押下時の処理
 */
function moveUp() {
  or05656Ref.value?.moveUp()
}

/**
 * 下へ移動アイコン押下時の処理
 */
function moveDown() {
  or05656Ref.value?.moveDown()
}
/**
 *  ダイアログ表示フラグ
 */
const showDialogOr10355 = computed(() => {
  // Or10355のダイアログ開閉状態
  return Or10355Logic.state.get(or10355.value.uniqueCpId)?.isOpen ?? false
})
/**
 *  ダイアログ表示フラグ
 */
const showDialogOr05657 = computed(() => {
  // Or05657のダイアログ開閉状態
  return Or05657Logic.state.get(or05657.value.uniqueCpId)?.isOpen ?? false
})
/**
 * データをkoumoku1Idでグループ化してソートする
 * @function initGroupedItems
 * @description JyokyoSyosaiTypeの配列をkoumoku1Idでグループ化し、ソートして返す
 * @param {JyokyoSyosaiType[]} value 処理対象のデータ配列
 * @returns {Or05656OnewayType[][]} グループ化されたデータ配列
 */
function initGroupedItems(value: JyokyoSyosaiType[]): Or05656OnewayType[][] {
  if (!value) return []

  // koumoku1Idでグループ化する
  const groups: Record<string, { sort: number; items: DataTableType[] }> = {}

  value.forEach((item: JyokyoSyosaiType) => {
    // データを変換してDataTableType形式にする
    const koumokuItem = transformToDataTableType(item)

    const key = item.koumoku1Id ?? ''
    const sortVal = Number(item.sort) || 0

    if (!groups[key]) {
      groups[key] = { sort: sortVal, items: [] }
    }
    groups[key].items.push(koumokuItem as DataTableType)
  })
  // グループをソートし、各グループ内のアイテムもソートする
  let data = Object.values(groups)
    .sort((a, b) => a.sort - b.sort)
    .map((group) =>
      group.items.slice().sort((a, b) => (Number(a.sort) || 0) - (Number(b.sort) || 0))
    )
  return data as Or05656OnewayType[][]
}
/**
 * Or05656OnewayTypeをDataTableType形式に変換する
 * @function transformToDataTableType
 * @description JyokyoSyosaiTypeをDataTableType形式に変換する
 * @param {JyokyoSyosaiType} item 変換対象のJyokyoSyosaiType
 * @returns {DataTableType} 変換されたDataTableType
 */
function transformToDataTableType(item: JyokyoSyosaiType) {
  return {
    ...item,
    bikoKnj: { value: item.bikoKnj ?? '' },
    joukyouFlg: { value: item.joukyouFlg ?? '' },
    genzai1Flg: { value: item.genzai1Flg ?? '' },
    youinFlg: {
      values: [
        item.youin1Flg === Or05656Const.VARIABLE.STRING_1
          ? Or05656Const.VARIABLE.STRING_1
          : undefined,
        item.youin2Flg === Or05656Const.VARIABLE.STRING_1
          ? Or05656Const.VARIABLE.STRING_2
          : undefined,
        item.youin3Flg === Or05656Const.VARIABLE.STRING_1
          ? Or05656Const.VARIABLE.STRING_3
          : undefined,
        item.youin4Flg === Or05656Const.VARIABLE.STRING_1
          ? Or05656Const.VARIABLE.STRING_4
          : undefined,
        item.youin5Flg === Or05656Const.VARIABLE.STRING_1
          ? Or05656Const.VARIABLE.STRING_5
          : undefined,
        item.youin6Flg === Or05656Const.VARIABLE.STRING_1
          ? Or05656Const.VARIABLE.STRING_6
          : undefined,
      ].filter((value): value is string => value !== undefined),
    },
    updateKbn: UPDATE_KBN.NONE,
  }
}
defineExpose({
  ungroupItems,
  or05658: or05658.value.uniqueCpId,
  or05656: or05656.value.uniqueCpId,
})
</script>

<template>
  <c-v-row>
    <c-v-col
      class="mt-2"
      cols="12"
    >
      <g-custom-or-05658
        ref="or05658Ref"
        v-bind="or05658"
        :parent-unique-cp-id="props.uniqueCpId"
      />
    </c-v-col>
    <c-v-col
      cols="12"
      class="px-5"
    >
      <div class="d-flex justify-space-between align-center">
        <div>
          <!-- 行追加ボタン -->
          <g-custom-or-26168 @click="onAddItem" />

          <!-- 削除ボタン -->
          <g-custom-or-26171
            class="ml-2"
            @click="onDeleteItem"
          />
        </div>

        <div class="d-flex align-center">
          <div class="d-flex align-center">
            <base-mo01338 :oneway-model-value="mo01338OnewayAssessment" />
            <base-mo-00009
              :oneway-model-value="localOneway.mo00009OnewayAssessment"
              class="border-left"
              @click.stop="onClickOr10355"
            />
          </div>
          <base-mo01338
            :oneway-model-value="mo01338OnewayNotes"
            class="ml-4"
          />
          <base-mo-00009
            :oneway-model-value="localOneway.or28285Oneway"
            class="border-left mr-4"
            @click.stop="onClickOr05657"
          />
          <!-- 'データ-ページング -->
          <g-custom-or-26184
            v-model="or26184"
            @up="moveUp"
            @down="moveDown"
          />
        </div>
      </div>
    </c-v-col>
    <c-v-col
      cols="12"
      class="pt-0"
    >
      <g-custom-or-05656
        ref="or05656Ref"
        v-bind="or05656"
        :parent-unique-cp-id="props.uniqueCpId"
        style="padding-left: 8px; padding-right: 8px"
      />
    </c-v-col>
    <g-custom-or-10355
      v-if="showDialogOr10355"
      v-bind="or10355"
      v-model="local.or10355Value"
      :oneway-model-value="localOneway.or10355OnewayModel"
    />
    <!-- 注釈モーダル画面をポップアップで起動する。 -->
    <g-custom-or-05657
      v-if="showDialogOr05657"
      v-bind="or05657"
    />
  </c-v-row>
</template>

<style scoped lang="scss"></style>
