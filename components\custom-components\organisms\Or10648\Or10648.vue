<script setup lang="ts">
/**
 * Or10648:有機体:［事業所検索］画面
 * GUI01177 ［事業所検索］
 *
 * <AUTHOR> 靳先念
 */
import { onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'
import { Or10648Const } from './Or10648.constants'
import type { Or10648StateType, selectTableItem } from './Or10648.type'
import { useScreenOneWayBind } from '#imports'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Or10648InterType } from '~/types/cmn/business/components/Or10648Type'
import type { Mo00045Type, Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  JigyousyoKensakuSelectInEntity,
  JigyousyoKensakuSelectOutEntity,
} from '~/repositories/cmn/entities/JigyousyoKensakuSelect'

const { t } = useI18n()
/************************************************
 * Props
 ************************************************/
interface Props {
  onewayModelValue: Or10648InterType
  uniqueCpId: string
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/

const local = ref({
  or10648: {
    tableheader: [
      {
        title: t('label.plan-business-name'),
        key: 'jigyoNumber',
        sortable: true,
        width: 160,
      },
      {
        title: t('label.plan-business-abbreviation'),
        key: 'jigyoRyakuKnj',
        width: 160,
      },
      {
        title: t('label.plan-business-mnumber'),
        key: 'jigyoKnj',
        width: 460,
      },
    ],
    tableList: [] as selectTableItem[],
    tableAllList: [] as selectTableItem[],
    planName: { value: '' } as Mo00045Type,
    planAbbreviation: { value: '' } as Mo00045Type,
    planMnumber: { value: '' } as Mo00045Type,
    svJigyoId: '',
    jigyoKnj: '',
  },
})

const localOneway = reactive({
  or10648: {
    ...props.onewayModelValue,
  },
  title: t('label.search-condition'),
  // 「履歴選択」ダイアログ
  mo00024Oneway: {
    width: '800px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      toolbarTitle: t('label.career-search'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
    },
  },
  planBusinessName: {
    itemLabel: t('label.plan-business-name'),
    showItemLabel: true,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
    width: '180',
    disabled: false,
    maxLength: '256',
  } as Mo00045OnewayType,
  planBusinessAbbreviation: {
    itemLabel: t('label.plan-business-abbreviation'),
    showItemLabel: true,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
    width: '180',
    disabled: false,
    maxLength: '20',
  } as Mo00045OnewayType,
  planBusinessMnumber: {
    itemLabel: t('label.plan-business-mnumber'),
    showItemLabel: true,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
    width: '300',
    disabled: false,
    maxLength: '60',
  } as Mo00045OnewayType,
})

// 選択した行のindex
const selectedItemIndex = ref<number>(-1)

// 閉じるボタン設置
const mo00611Oneway: Mo00611OnewayType = {
  btnLabel: t('btn.close'),
  width: '90px',
}

// 確定ボタン設置
const mo00609Oneway: Mo00609OnewayType = {
  btnLabel: t('btn.confirm'),
  width: '90px',
}

// ダイアログ設置
const mo00024 = ref<Mo00024Type>({
  isOpen: Or10648Const.DEFAULT.IS_OPEN,
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or10648StateType>({
  cpId: Or10648Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or10648Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  // 初期情報取得
  await getInitDataInfo()
})

/** 初期情報取得 */
const getInitDataInfo = async () => {
  // 初期情報取得(IN)
  const inputData: JigyousyoKensakuSelectInEntity = {
    svtype: props.onewayModelValue.svtype,
  }
  // 事業所検索画面初期情を取得する。
  const res: JigyousyoKensakuSelectOutEntity = await ScreenRepository.select(
    'jigyousyoKensakuSelect',
    inputData
  )
  local.value.or10648.tableList = res.data.jigyoList
  local.value.or10648.tableAllList = res.data.jigyoList
  selectRow(0)
  onBlurEvent()
}

const onBlurEvent = () => {
  const tableData = cloneDeep(local.value.or10648.tableAllList)

  if (
    !local.value.or10648.planName &&
    !local.value.or10648.planAbbreviation &&
    !local.value.or10648.planMnumber
  ) {
    local.value.or10648.tableList = tableData
  } else {
    local.value.or10648.tableList = tableData.filter((item) => {
      const jigyoNumber = item.jigyoNumber
        .slice(0, local.value.or10648.planName.value.length)
        .toString()
        .toLowerCase()
        .includes(local.value.or10648.planName.value.toString().toLowerCase())
      const jigyoRyakuKnj = item.jigyoRyakuKnj
        .toString()
        .toLowerCase()
        .includes(local.value.or10648.planAbbreviation.value.toString().toLowerCase())
      const jigyoKnj = item.jigyoKnj
        .toString()
        .toLowerCase()
        .includes(local.value.or10648.planMnumber.value.toString().toLowerCase())
      return jigyoNumber && jigyoRyakuKnj && jigyoKnj
    })
    local.value.or10648.tableList.unshift({
      jigyoNumber: '',
      jigyoRyakuKnj: '',
      jigyoKnj: '（未選択）',
      svJigyoId: '0',
      svKindCd: '0',
      sort: '0',
    })
    selectRow(0)
  }
}

/**
 * 履歴情報選択行設定様式
 *
 * @param item - 履歴情報選択行
 */

/**
 * 「確定」ボタン押下
 */
const onConfirmBtn = () => {
  // 選択情報値戻り
  emit('update:modelValue', {
    svJigyoId: local.value.or10648.svJigyoId,
    jigyoKnj: local.value.or10648.jigyoKnj,
  })
  onClickCloseBtn()
}

/**
 * 閉じるボタン押下時
 */
const onClickCloseBtn = (): void => {
  setState({ isOpen: false })
}

/**
 * 行選択
 *
 * @param index - 選択した行のindex
 */
function selectRow(index: number) {
  selectedItemIndex.value = index
  local.value.or10648.svJigyoId = local.value.or10648.tableList[index].svJigyoId
  local.value.or10648.jigyoKnj = local.value.or10648.tableList[index].jigyoKnj
}

/**
 * 行選択
 *
 * @param index - 選択した行のindex
 */
const dbSelectRow = (index: number) => {
  selectedItemIndex.value = index
  local.value.or10648.svJigyoId = local.value.or10648.tableList[index].svJigyoId
  local.value.or10648.jigyoKnj = local.value.or10648.tableList[index].jigyoKnj
  onConfirmBtn()
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      onClickCloseBtn()
    }
  }
)

/**
 * planName change
 *
 */
watch(
  () => local.value.or10648.planName,
  () => {
    onBlurEvent()
  }
)
/**
 * planAbbreviation change
 *
 */
watch(
  () => local.value.or10648.planAbbreviation,
  () => {
    onBlurEvent()
  }
)
/**
 * planMnumber change
 *
 */
watch(
  () => local.value.or10648.planMnumber,
  () => {
    onBlurEvent()
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet>
        <!-- <c-v-row class="search-condition">{{ localOneway.title }}</c-v-row> -->
        <c-v-row class="search-condition-list">
          <c-v-col
            :cols="3"
            class="search-condition-item"
          >
            <base-Mo00045
              v-model="local.or10648.planName"
              :oneway-model-value="localOneway.planBusinessName"
            />
          </c-v-col>
          <c-v-col
            :cols="3"
            class="mr-2 search-condition-item"
            style="margin-left: -12px !important"
          >
            <base-Mo00045
              v-model="local.or10648.planAbbreviation"
              :oneway-model-value="localOneway.planBusinessAbbreviation"
            />
          </c-v-col>
          <c-v-col
            :cols="5"
            class="search-condition-item"
            style="margin-left: -20px !important"
          >
            <base-Mo00045
              v-model="local.or10648.planMnumber"
              :oneway-model-value="localOneway.planBusinessMnumber"
            />
          </c-v-col>
        </c-v-row>
        <c-v-row>
          <c-v-col cols="12">
            <c-v-data-table
              height="400px"
              :items="local.or10648.tableList"
              :headers="local.or10648.tableheader"
              hide-default-footer
              class="table-header overflow-y-auto table-area table-wrapper"
              :items-per-page="-1"
              hover
              fixed-header
            >
              <template #item="{ item, index }">
                <tr
                  :class="{ 'select-row': selectedItemIndex === index }"
                  @click="selectRow(index)"
                  @dblclick="dbSelectRow(index)"
                >
                  <td>
                    <span>{{ item.jigyoNumber }}</span>
                  </td>
                  <td style="padding-left: 0 !important; padding-right: 0 !important">
                    <span class="overflowText1">{{ item.jigyoRyakuKnj }}</span>
                    <c-v-tooltip
                      v-if="item.jigyoRyakuKnj"
                      activator="parent"
                      location="bottom"
                      :max-width="180"
                      :text="item.jigyoRyakuKnj"
                      open-delay="200"
                    />
                  </td>
                  <td>
                    <span class="overflowText">{{ item.jigyoKnj }}</span>
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :max-width="404"
                      :text="item.jigyoKnj"
                      open-delay="200"
                    />
                  </td>
                </tr>
              </template>
              <template #bottom />
            </c-v-data-table>
          </c-v-col>
        </c-v-row>
      </c-v-sheet>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="mo00611Oneway"
          @click="onClickCloseBtn"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <!-- 確定ボタン-->
        <base-mo00609
          class="mx-2"
          :oneway-model-value="mo00609Oneway"
          @click="onConfirmBtn"
        >
          <!--ツールチップ表示："設定を確定します。"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.confirm-btn')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';

.search-condition {
  font-weight: bolder;
  height: 36px;
  text-indent: 8px;
  line-height: 36px;
}

.search-condition-item {
  :deep(.v-sheet) {
    > .v-row {
      > .v-col {
        padding: 0px !important;
        .item-label {
          font-weight: bolder !important;
        }
      }
    }
  }
  margin-right: 0px;
}

// 選択した行のCSS
.select-row {
  background: rgb(var(--v-theme-blue-100)) !important;
}

/* 2行目以降のヘッダーのスタイル */
.table-header :deep(.v-table__wrapper tr:not(:first-child) th) {
  border-width: 0 1px 1px 0;
}

/* ヘッダーの1列目のスタイル */
.table-header :deep(.v-table__wrapper th:first-child) {
  border-left: 1px rgb(var(--v-theme-black-200)) solid; // ボーダー(左)
  width: 160px;
}

.table-header :deep(.v-table__wrapper th:nth-child(2)) {
  width: 180px;
}
.table-header :deep(.v-table__wrapper th:nth-child(3)) {
  width: 444px;
}

.overflowText {
  text-wrap: auto;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  line-clamp: 1;
  -webkit-line-clamp: 1;
}

.overflowText1 {
  display: inline-block;
  width: 160px;
  padding: 0 16px;
  text-wrap: auto;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  line-clamp: 1;
  -webkit-line-clamp: 1;
}
</style>
