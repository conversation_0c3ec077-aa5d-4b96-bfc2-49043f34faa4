import { Or27<PERSON>9<PERSON>og<PERSON> } from '../Or27539/Or27539.logic'
import { Or27539Const } from '../Or27539/Or27539.constants'
import { Or28528Logic } from '../Or28528/Or28528.logic'
import { Or28528Const } from '../Or28528/Or28528.constants'
import { Or28220Logic } from '../Or28220/Or28220.logic'
import { Or28220Const } from '../Or28220/Or28220.constants'
import { Or28221Logic } from '../Or28221/Or28221.logic'
import { Or28221Const } from '../Or28221/Or28221.constants'
import { OrX0114Const } from '../OrX0114/OrX0114.constants'
import { OrX0114Logic } from '../OrX0114/OrX0114.logic'
import { Or28153Const } from '../Or28153/Or28153.constants'
import { Or28153Logic } from '../Or28153/Or28153.logic'
import { Or01533Const } from './Or01533.constants'
import { useInitialize } from '~/composables/useComponentLogic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
/**
 * Or27653:モーダル:内容マスタ
 * GUI00935_内容マスタ
 *
 * @description
 * 処理ロジック initialize処理とpinia領域操作用の処理を提供する
 *
 * <AUTHOR>
 */
export namespace Or01533Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or01533Const.CP_ID(0),
      uniqueCpId,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [
        { cpId: Or27539Const.CP_ID(1) },
        { cpId: Or28153Const.CP_ID(1) },
        { cpId: Or28528Const.CP_ID(1) },
        { cpId: Or28220Const.CP_ID(1) },
        { cpId: Or28221Const.CP_ID(1) },
        { cpId: OrX0114Const.CP_ID(1) },
        { cpId: Or21813Const.CP_ID(1) },
        { cpId: Or21814Const.CP_ID(1) },
      ],

      // 編集フラグ不要
      // ※ 元々双方向領域を持たないため記載不要だが、サンプル用にナビゲーション制御領域で持つデータを分かりやすくするために設定
      editFlgNecessity: false,
    })
    Or27539Logic.initialize(childCpIds[Or27539Const.CP_ID(1)].uniqueCpId)
    Or28153Logic.initialize(childCpIds[Or28153Const.CP_ID(1)].uniqueCpId)
    Or28528Logic.initialize(childCpIds[Or28528Const.CP_ID(1)].uniqueCpId)
    Or28220Logic.initialize(childCpIds[Or28220Const.CP_ID(1)].uniqueCpId)
    Or28221Logic.initialize(childCpIds[Or28221Const.CP_ID(1)].uniqueCpId)
    OrX0114Logic.initialize(childCpIds[OrX0114Const.CP_ID(1)].uniqueCpId)
    Or21813Logic.initialize(childCpIds[Or21813Const.CP_ID(1)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(1)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }
}
