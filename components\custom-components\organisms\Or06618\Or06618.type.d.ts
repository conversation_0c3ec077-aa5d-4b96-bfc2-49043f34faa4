/**
 * Or06618:モーダル:(日課計画マスタ)コンテンツエリアタブ
 * GUI01056_日課計画マスタ
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR>
 */
export interface Or06618StateType {
  /**
   * 開閉フラグ
   */
  isOpen?: boolean
}
/**
 *
 * Or06618:モーダル:(日課計画マスタ)コンテンツエリアタブ
 * GUI01056_日課計画マスタ
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR>
 */
export interface Or06618Type {
  /** 初期取込 */
  initialUptake: string
  /** 初期取込分類3 */
  initialUptakeBunrui3: string
  /** 初期取込分類3更新回数 */
  initialUptakeModifiedCnt: string
  /** 時間 */
  time: string
  /** 時間分類3 */
  timeBunrui3: string
  /** 時間分類3更新回数 */
  timeModifiedCnt: string
  /** 文字サイズ */
  charSize: string
  /** 文字サイズ分類3 */
  charSizeBunrui3: string
  /** 文字サイズ分類3更新回数 */
  charSizeModifiedCnt: string
  /** 文字位置 */
  charPosition: string
  /** 文字位置分類3 */
  charPositionBunrui3: string
  /** 文字位置分類3更新回数 */
  charPositionModifiedCnt: string
  /** 文字色 */
  charColor: string
  /** 文字色分類3 */
  charColorBunrui3: string
  /** 文字色分類3更新回数 */
  charColorModifiedCnt: string
  /** 背景色 */
  backgroundColor: string
  /** 背景色分類3 */
  backgroundColorBunrui3: string
  /** 背景色分類3更新回数 */
  backgroundColorModifiedCnt: string
}
/**
 * Async Function
 */
type AsyncFunction = () => Promise<boolean>
