/**
 * Or00249：有機体：（利用者基本）利用者選択
 * TwoWayBind領域に保持するデータ構造
 */
export interface Or00249DataType {
  /**
   * クリックした利用者のインデックス
   */
  selectUserIndex: number
}

/**
 * Or00249：有機体：（利用者基本）利用者選択
 * OneWayBind領域に保持するデータ構造
 */
export interface Or00249StateType {
  /**
   * 利用者一覧情報の配列
   */
  userList: userListItemType[]

  /**
   * 一覧情報更新フラグ
   * 一覧情報(userList)が更新されたらtrue
   * ※userListがブランクに更新された場合、OnewayStateの仕様で値が
   *  連携されないため、ブランクへの更新も検知するためのフラグ。
   */
  userListUpdateFlg: boolean
}

/**
 * 利用者一覧情報のアイテムタイプ
 */
export interface userListItemType {
  /**
   * 利用者ID
   */
  userId: number

  /**
   * 写真パス
   */
  picturePath: string

  /**
   * ふりがな（姓）
   */
  nameKanaSei: string

  /**
   * ふりがな（名）
   */
  nameKanaMei: string

  /**
   * 氏名（姓）
   */
  nameSei: string

  /**
   * 氏名（名）
   */
  nameMei: string

  /**
   * 同名識別子
   */
  genericIdentifier: string

  /**
   * 性別
   */
  gender: number

  /**
   * 年齢
   */
  age: number

  /**
   * 要介護度
   */
  levelOfCareRequired: number

  /**
   * 利用者番号
   */
  selfId: string

  /**
   * 認定状態
   */
  ninteiJoutai: number

  /**
   * 前月退所
   */
  zengetuFlg: number

  /**
   * 利用開始前
   */
  riyoumaeFlg: number

  /**
   * KBN3
   */
  kbn3: number

  /**
   * KBN4
   */
  kbn4: number

  /**
   * 事業所情報一覧
   */
  jigyoInfoList: UserJigyoInfoType[]
}

/**
 * 利用者の事業所情報のタイプ
 */
export interface UserJigyoInfoType {
  /** 法人ID */
  houjinId: string
  /** 施設ID */
  shisetuId: string
  /** サービス事業所ID */
  svJigyoId: string
  /** サービス事業所CD */
  svJigyoCd: string
}
