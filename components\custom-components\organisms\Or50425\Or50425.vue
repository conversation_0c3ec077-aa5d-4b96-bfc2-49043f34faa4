<script setup lang="ts">
/**
 * Or50425:有機体:印刷設定
 * GUI01254_印刷設定
 *
 * @description
 * 実施モニタリング
 *
 * <AUTHOR>
 */ import { useI18n } from 'vue-i18n'
import { reactive, ref, onMounted, watch, computed } from 'vue'
import { OrX0135Logic } from '../OrX0135/OrX0135.logic'
import { OrX0135Const } from '../OrX0135/OrX0135.constants'
import { Or50425Const } from './Or50425.constants'
import type { Or50425StateType } from './Or50425.type'
import { useSetupChildProps, useScreenOneWayBind, useNuxtApp, dateUtils } from '#imports'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01338OnewayType } from '@/types/business/components/Mo01338Type'
import type { Mo01344OnewayType } from '@/types/business/components/Mo01344Type'
import type { Mo00039Items, Mo00039OnewayType } from '@/types/business/components/Mo00039Type'
import type { Mo00020Type, Mo00020OnewayType } from '@/types/business/components/Mo00020Type'
import type { Mo00018Type, Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { CustomClass } from '~/types/CustomClassType'
import type {
  UserEntity,
  PeriodHistoryEntity,
  PrtEntity,
  ImplementationMonitoringPrintSettingsInitUpdateInEntity,
  IAssessmentInterRAIPrintSettingsSelectOutEntity,
  ImplementationMonitoringPrintSettingsSubjectSelectInEntity,
  ImplementationMonitoringPrintSettingsSubjectSelectOutEntity,
  ImplementationMonitoringPrintSettingsHistorySelectInEntity,
  ImplementationMonitoringPrintSettingsHistorySelectOutEntity,
} from '~/repositories/cmn/entities/ImplementationMonitoringPrintSettingsInitUpdateEntity'
import type {
  SysIniInfoEntity,
  IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity,
  IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateOutEntity,
} from '~/repositories/cmn/entities/FreeAssessmentFacePrintSettingsPrtNoChangeUpdateEntity'
import type {
  IFreeAssessmentFacePrintSettingsUpdateInEntity,
  IFreeAssessmentFacePrintSettingsUpdateOutEntity,
} from '~/repositories/cmn/entities/FreeAssessmentFacePrintSettingsUpdateEntity'
import type {
  Mo01334OnewayType,
  Mo01334Type,
  Mo01334Items,
} from '~/types/business/components/Mo01334Type'
import { OrX0117Const } from '~/components/custom-components/organisms/OrX0117/OrX0117.constants'
import { OrX0117Logic } from '~/components/custom-components/organisms/OrX0117/OrX0117.logic'
import type { OrX0117History, OrX0117OnewayType } from '~/types/cmn/business/components/OrX0117Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { Or21814OnewayType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { OrX0128Const } from '~/components/custom-components/organisms/OrX0128/OrX0128.constants'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import { OrX0130Logic } from '~/components/custom-components/organisms/OrX0130/OrX0130.logic'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import type {
  OrX0128OnewayType,
  OrX0128Items,
  OrX0128Headers,
} from '~/types/cmn/business/components/OrX0128Type'
import { OrX0128Logic } from '~/components/custom-components/organisms/OrX0128/OrX0128.logic'
import type {
  Or50425Param,
  Or50425MsgBtnType,
} from '~/components/custom-components/organisms/Or50425/Or50425.type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { useReportUtils, reportOutputType } from '~/utils/useReportUtils'
import type {
  PrintSetEntity,
  PrintOptionEntity,
  PrintSubjectHistoryEntity,
  ChoPrtEntity,
  ICpnTucRaiAssReportSelectInEntity,
} from '~/repositories/cmn/entities/CpnTucSypAssSokatuReport'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { DebugLogPluginInterface } from '~/plugins/debug-log'
import { useCmnCom } from '@/utils/useCmnCom'
import { useCmnRouteCom } from '~/utils/useCmnRouteCom'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'

import type { Mo00045Type, Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Mo00038OnewayType, Mo00038Type } from '~/types/business/components/Mo00038Type'
import type { Mo00040OnewayType, Mo00040Type } from '~/types/business/components/Mo00040Type'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import { OrX0145Const } from '~/components/custom-components/organisms/OrX0145/OrX0145.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import type { Or21813StateType } from '~/components/base-components/organisms/Or21813/Or21813.type'
import type { OrX0135OnewayType } from '~/types/cmn/business/components/OrX0135Type'

const systemCommonsStore = useSystemCommonsStore()
const cmnRouteCom = useCmnRouteCom()
const { reportOutput } = useReportUtils()
const { convertDateToSeireki } = dateUtils()
const $log = useNuxtApp().$log as DebugLogPluginInterface

const { t } = useI18n()
/************************************************
 * Props
 ************************************************/
interface Props {
  uniqueCpId: string
}

const props = defineProps<Props>()

// 子コンポーネント用変数
const or21815 = ref({ uniqueCpId: '' })
const orX0117 = ref({ uniqueCpId: '' })
const orX0128 = ref({ uniqueCpId: '' })
const orX0130 = ref({ uniqueCpId: '' })
const or21814 = ref({ uniqueCpId: '' })
const or21813 = ref({ uniqueCpId: '' })
const orx0145 = ref({ uniqueCpId: '' })
const orX0135_1 = ref({ uniqueCpId: '' })

const localOneway = reactive({
  /**
   * 基本設定
   */
  mo01338OneWayBasicSettings: {
    value: t('label.basic-settings'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 帳票タイトル
   */
  mo00615OneWayTypeTitle: {
    itemLabel: t('label.print-settings-title'),
    showItemLabel: true,
  } as Mo00615OnewayType,
  /**
   * タイトル表示
   */
  mo01338OneWay: {
    value: Or50425Const.DEFAULT.STR.EMPTY,
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 日付印刷区分
   */
  mo00039OneWay: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  /**
   * 指定日
   */
  mo00020OneWay: {
    showItemLabel: false,
    showSelectArrow: false,
    mo00009OnewayBack: {},
    mo00009OnewayForward: {},
  } as Mo00020OnewayType,
  /**
   * 敬称を変更する
   */
  mo00018OneWayTitleOfHonorFlg: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.honorifics-change'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
   * 記入用シートを印刷する
   */
  mo00018OneWayEmptyFlg: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-entry-sheet'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
   * 承認欄を印刷する
   */
  mo00018OneWayApprovalPrintFlg: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.confirm-form-copying'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,

  /**
   * 承認欄の登録ボタン
   */
  mo00611OneWayApproval: {
    btnLabel: t('btn.confirm-form-registration‌'),
    width: '110px',
    minWidth: '110px',
    disabled: false,
  } as Mo00611OnewayType,
  /**
   * 印刷枠の高さを最小
   */
  mo00018OneWayPrintFrameSizeFlg: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-frame-height-minimum'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
   * 最小行入力
   */
  mo00038OneWayPrintFrameSize: {
    min: 1,
    max: 30,
    mo00045Oneway: {
      width: '64',
      maxLength: '2',
      isVerticalLabel: false,
      type: 'number',
      disabled: false,
      customClass: new CustomClass({ labelClass: '' }),
    } as Mo00045OnewayType,
  } as Mo00038OnewayType,
  /**
   * 行に設定する
   */
  mo00615OneWayPrintFrameSize: {
    itemLabel: t('label.line-set'),
    showItemLabel: true,
    isVerticalLabel: true,
    isRequired: false,
    hideDetails: 'auto',
    width: '64',
    disabled: false,
  } as Mo00615OnewayType,
  /**
   * 計画作成者を印刷する
   */
  mo00018OneWayPlanAuthorPrintFlg: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-plan-creater'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
   * モノクロ印刷モード
   */
  mo00018OneWayMonochromePrintFlg: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.mono-chrome-printing-mode'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
   * 項目毎に改ページ
   */
  mo00018OneWayPageChangeFlg: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.page-change-per-item'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
   * 今月の総括を印刷する
   */
  mo00018OneWaySummaryPrintFlg: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-monthly-summary'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
   * 日々の備考欄を印刷する
   */
  mo00018OneWayRemarksPrintFlg: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-daily-remarks'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /** 敬称 */
  mo00045OneWayTitleOfHonor: {
    itemLabel: '',
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
    width: '60',
    disabled: false,
  } as Mo00045OnewayType,
  /**
   * 計画作成日印刷区分
   */
  mo00039OneWayAssessmentType: {
    name: '',
    showItemLabel: false,
    inline: false,
    disabled: false,
  } as Mo00039OnewayType,
  /**
   * 印刷する要介護度
   */
  mo00615OneWayKaigodo: {
    itemLabel: t('label.print-nursing-care-required'),
    showItemLabel: true,
    isVerticalLabel: true,
    isRequired: false,
    hideDetails: 'auto',
    width: '64',
    disabled: false,
  } as Mo00615OnewayType,
  /**
   * 印刷する要介護度
   */
  mo00040OneWayKaigodo: {
    showItemLabel: false,
    items: [],
    itemTitle: 'label',
    itemValue: 'value',
    hideDetails: true,
    width: '280px',
  } as Mo00040OnewayType,

  /**
   * 印刷オプション
   */
  mo01338OneWayPrinterOption: {
    value: t('label.printer-option'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 利用者選択ラベル
   */
  mo01338OneWayPrinterUserSelect: {
    value: t('label.printer-user-select'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 履歴選択ラベル
   */
  mo01338OneWayPrinterHistorySelect: {
    value: t('label.printer-history-select'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 利用者選択
   */
  mo00039OneWayUserSelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  /**
   * 履歴選択
   */
  mo00039OneWayHistorySelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  /**
   * 基準日
   */
  mo00615OneWayType: {
    itemLabel: t('label.base-date'),
    showItemLabel: true,
  } as Mo00615OnewayType,
  /**
   * 基準日: 表用テキストフィールド
   */
  mo00020KijunbiOneWay: {
    showItemLabel: false,
    showSelectArrow: true,
    width: '132px',
    totalWidth: '220px',
    mo00009OnewayBack: {},
    mo00009OnewayForward: {},
  } as Mo00020OnewayType,
  /**
   * 担当ケアマネプルダウン
   */
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: true,
    multiple: false,
    selectedUserCounter: '2',
  } as OrX0145OnewayType,
  /**
   * 閉じるボタン
   */
  mo00611OneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  /**
   * PDFダウンロードボタン
   */
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
    disabled: false,
  } as Mo00609OnewayType,

  orX0135Oneway: {} as OrX0135OnewayType,
})

/**
 * 親画面の情報
 */
const local = {
  /**
   * 個人情報使用フラグ
   */
  kojinhogoUsedFlg: Or50425Const.DEFAULT.KOJINHOGO_USED_FLG,
  /**
   * 個人情報番号
   */
  sectionAddNo: Or50425Const.DEFAULT.SECTION_ADD_NO,
  /**
   * 親画面.事業所ID
   */
  svJigyoId: Or50425Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.施設ID
   */
  shisetuId: Or50425Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.担当者ID
   */
  tantoId: Or50425Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.種別ID
   */
  syubetsuId: Or50425Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.セクション名
   */
  sectionName: Or50425Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.利用者ID
   */
  userId: Or50425Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.事業所名
   */
  svJigyoKnj: Or50425Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.処理年月日
   */
  processYmd: Or50425Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.ヘッダID
   */
  kjisshi1Id: Or50425Const.DEFAULT.STR.EMPTY,
  /**
   * 選択された帳票のプロファイル
   */
  profile: Or50425Const.DEFAULT.STR.EMPTY,
  /**
   * 選択利用者ID
   */
  selectUserId: Or50425Const.DEFAULT.STR.EMPTY,
  /**
   * 出力帳票名一覧に選択行番号
   */
  index: Or50425Const.DEFAULT.STR.EMPTY,
  /**
   * システムINI情報
   */
  sysIniInfo: {} as SysIniInfoEntity,
  /**
   * 履歴一覧が0件選択の場合（※履歴リストが0件、複数件を含む）
   */
  historyNoSelect: false,
  /**
   * 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
   */
  userNoSelect: false,
  /**
   * 帳票番号
   */
  prtNo: Or50425Const.DEFAULT.STR.EMPTY,
  /**
   * 選択利用者リスト
   */
  userList: [] as UserEntity[],
  /**
   * 帳票ID
   */
  reportId: Or50425Const.DEFAULT.STR.EMPTY,
  /**
   * 履歴選択の明細
   */
  orX0128DetList: [] as OrX0128Items[],
  /**
   * 印刷設定情報リスト
   */
  prtList: [] as PrtEntity[],
  /** 画面.敬称 */
  titleOfHonor: {
    value: Or50425Const.DEFAULT.STR.EMPTY,
  } as Mo00045Type,
  /** 画面.最小行入力 */
  mo00038TypePrintFrameSize: {
    mo00045: {
      value: Or50425Const.DEFAULT.STR.EMPTY,
    } as Mo00045Type,
  } as Mo00038Type,
  mo00040TypeKaigodo: {
    modelValue: Or50425Const.DEFAULT.STR.EMPTY,
  } as Mo00040Type,
  orX0135Type: {},
}

/**
 * レスポンスパラメータ詳細
 */
const localData: IAssessmentInterRAIPrintSettingsSelectOutEntity = {
  data: {},
} as IAssessmentInterRAIPrintSettingsSelectOutEntity
/**************************************************
 * 変数定義
 **************************************************/
// ダイアログ
const mo00024Oneway = ref<Mo00024OnewayType>({
  width: '1300px',
  persistent: true,
  showCloseBtn: true,
  mo01344Oneway: {
    name: 'Or50425',
    toolbarTitle: t('label.print-set'),
    toolbarName: 'Or50425ToolBar',
    toolbarTitleCenteredFlg: false,
    showCardActions: true,
    cardTextClass: 'or50425_content',
  } as Mo01344OnewayType,
})

const mo00024 = ref<Mo00024Type>({
  isOpen: Or50425Const.DEFAULT.IS_OPEN,
})

/**
 * 出力帳票名一覧
 */
const mo01334Oneway = ref<Mo01334OnewayType>({
  headers: [
    {
      title: t('label.ledger'),
      key: 'ledgerName',
      sortable: false,
      minWidth: '100',
    },
  ],
  items: [],
  height: 655,
})

/**
 * 出力帳票名一覧
 */
const mo01334Type = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

/**
 * 日付印刷区分
 */
const mo00039Type = ref<string>('')

/**
 * 指定日
 */
const mo00020Type = ref<Mo00020Type>({
  value: convertDateToSeireki(undefined),
} as Mo00020Type)

/**
 * 敬称を変更するチェックボックス
 */
const mo00018TitleOfHonorFlg = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 記入用シートを印刷するチェックボックス
 */
const mo00018EmptyFlg = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 承認欄を印刷するチェックボックス
 */
const mo00018ApprovalPrintFlg = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 印刷枠の高さを最小チェックボックス
 */
const mo00018PrintFrameSizeFlg = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 計画作成者を印刷するチェックボックス
 */
const mo00018PlanAuthorPrintFlg = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * モノクロ印刷モードチェックボックス
 */
const mo00018MonochromePrintFlg = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 項目ごとに改ページするチェックボックス
 */
const mo00018PageChangeFlg = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 今月の総括を印刷するチェックボックス
 */
const mo00018SummaryPrintFlg = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 日々の備考欄を印刷するチェックボックス
 */
const mo00018RemarksPrintFlg = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 計画作成日印刷区分
 */
const mo00039OneWayAssessmentTypeType = ref<string>('')

/**
 * 印刷時に色をつける
 */
const mo00018TypeColor = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 利用者選択
 */
const mo00039OneWayUserSelectType = ref<string>('')

/**
 * 履歴選択
 */
const mo00039OneWayHistorySelectType = ref<string>('')

/**
 * 当ケアマネプルダウン選択値
 */
const orX0145Type = ref<OrX0145Type>({
  value: {} as TantoCmnShokuin,
} as OrX0145Type)

/**
 * 基準日
 */
const mo00020TypeKijunbi = ref<Mo00020Type>({
  value: convertDateToSeireki(undefined),
} as Mo00020Type)

/**
 * 指定日非表示/表示フラゲ
 */
const mo00020Flag = ref<boolean>(false)

/**
 * 計画作成日/表示フラゲ
 */
const mo00020CpnFlag = ref<boolean>(false)

const orX0128Headers_1 = [
  { title: t('label.process-ym'), key: 'syoriYm', width: '100px', sortable: false },
  { title: t('label.create-user-ymd'), key: 'planCreateYmd', width: '120px', sortable: false },
  { title: t('label.shoku_knj'), key: 'shokuinKnj', sortable: false },
] as OrX0128Headers[]
const orX0128Headers_2 = [
  { title: t('label.process-ym'), key: 'syoriYm', width: '100px', sortable: false },
  { title: t('label.create-user-ymd'), key: 'planCreateYmd', sortable: false },
] as OrX0128Headers[]

/** 共通情報.ケアプラン方式が"5:パッケージ"の場合 */
const isCarePlanStyle5 = computed(() => {
  return cmnRouteCom.getInitialSettingMaster()?.cpnFlg === Or50425Const.DEFAULT.CARE_PLAN_STYLE_5
    ? true
    : false
})

/**
 * 基準日フラゲ
 */
const kijunbiFlag = ref<boolean>(false)
/**
 * 履歴一覧セクションフラゲ
 */
const mo01334TypeHistoryFlag = ref<boolean>(true)

/**
 * 利用者列幅
 */
const userCols = ref<number>(5)

const orX0128OnewayModel = reactive<OrX0128OnewayType>({
  /**
   * 期間管理フラグ
   */
  kikanFlg: '1',
  /**
   * 単一複数フラグ(0:単一,1:複数)
   */
  singleFlg: OrX0128Const.DEFAULT.TANI,
  tableStyle: 'width:480px',
  headers: isCarePlanStyle5.value ? orX0128Headers_2 : orX0128Headers_1,
  items: [],
})

/**
 * 利用者
 */
const orX0130Oneway = reactive<OrX0130OnewayType>({
  selectMode: OrX0130Const.DEFAULT.TANI,
  tableStyle: 'width:210px',
  // 指定行選択
  userId: Or50425Const.DEFAULT.STR.EMPTY,
})

/**
 * 担当ケアマネ選択
 */
const tantoIconBtn = ref<boolean>(false)
/**
 * 印刷設定帳票出力
 */
const orX0117Oneway: OrX0117OnewayType = {
  type: Or50425Const.DEFAULT.TANI,
  historyList: [] as OrX0117History[],
} as OrX0117OnewayType

/**
 * 初期情報取得フラゲ
 */
const initFlag = ref<boolean>(false)

/**
 * 期間管理フラグ
 */
const kikanFlag = ref<string>(Or50425Const.DEFAULT.STR.EMPTY)

// ダイアログ表示フラグ
const showDialogOrX0135 = computed(() => {
  // OrX0135のダイアログ開閉状態
  return OrX0135Logic.state.get(orX0135_1.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or50425StateType>({
  cpId: Or50425Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or50425Const.DEFAULT.IS_OPEN
    },
    param: (value) => {
      if (value) {
        local.prtNo = value.prtNo
        local.svJigyoId = value.svJigyoId
        local.shisetuId = value.shisetuId
        local.tantoId = value.tantoId
        local.syubetsuId = value.syubetsuId
        local.sectionName = value.sectionName
        local.userId = value.userId
        local.svJigyoKnj = value.svJigyoKnj
        local.processYmd = value.processYmd
      }
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21815Const.CP_ID(0)]: or21815.value,
  [OrX0117Const.CP_ID(0)]: orX0117.value,
  [OrX0128Const.CP_ID(0)]: orX0128.value,
  [OrX0130Const.CP_ID(0)]: orX0130.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or21813Const.CP_ID(0)]: or21813.value,
  [OrX0145Const.CP_ID(0)]: orx0145.value,
  [OrX0135Const.CP_ID(1)]: orX0135_1.value,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 画面ID
const screenId = 'GUI00793'
// ルーティング
const routing = 'GUI00793/pinia'
// ケアマネ画面を初期化する
await useCmnCom().initialize({
  screenId,
  routing,
})

onMounted(async () => {
  // 汎用コード取得API実行
  await initCodes()

  // 画面ボタン活性非活性設定
  btnItemSetting()

  // 初期情報取得
  await init()
})

// ダイアログ表示フラグ
const showDialogOrX0117 = computed(() => {
  // Or50425のダイアログ開閉状態
  return OrX0117Logic.state.get(orX0117.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 汎用コード取得API実行
 */
const initCodes = async () => {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 計画作成日印刷区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_PLAN_DATE_PRINT_TYPE },
    // 日付印刷区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    // 単複数選択区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY },
    // 性別区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_GENDER_CATEGORY },
    // 印刷する要介護
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_PRINTED_CARE },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 印刷する要介護
  const printedCareCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_PRINTED_CARE
  )
  if (printedCareCodeTypes?.length > 0) {
    local.mo00040TypeKaigodo.modelValue = printedCareCodeTypes[0].value
    const list = []
    for (const item of printedCareCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        })
      }
    }
    localOneway.mo00040OneWayKaigodo.items = list
  }

  // 日付印刷区分
  const bizukePrintCategoryCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )
  if (bizukePrintCategoryCodeTypes?.length > 0) {
    mo00039Type.value = bizukePrintCategoryCodeTypes[0].value
    const list: Mo00039Items[] = []
    for (const item of bizukePrintCategoryCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    localOneway.mo00039OneWay.items = list
  }

  // 計画作成日印刷区分
  const assessmentKindCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_PLAN_DATE_PRINT_TYPE
  )
  if (assessmentKindCodeTypes?.length > 0) {
    const list: Mo00039Items[] = []
    for (const item of assessmentKindCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    localOneway.mo00039OneWayAssessmentType.items = list
  }

  // 利用者選択 TAN_MULTIPLE_SELECT_CATEGORY
  const tanMultipleSelectCategoryCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )
  if (tanMultipleSelectCategoryCodeTypes?.length > 0) {
    mo00039OneWayUserSelectType.value = tanMultipleSelectCategoryCodeTypes[0].value
    mo00039OneWayHistorySelectType.value = tanMultipleSelectCategoryCodeTypes[0].value
    const list: Mo00039Items[] = []
    for (const item of tanMultipleSelectCategoryCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    localOneway.mo00039OneWayHistorySelectType.items = list
    localOneway.mo00039OneWayUserSelectType.items = list
  }
}

/**
 * 初期プロジェクト設定
 */
const initSetting = () => {
  // 親画面.処理年月日が""の場合
  if (Or50425Const.DEFAULT.STR.EMPTY === local.processYmd) {
    // 担当ケアマネ選択
    tantoIconBtn.value = false
  } else {
    // 担当ケアマネ選択
    tantoIconBtn.value = true
  }
}

/**
 * 初期情報取得
 */
const init = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: ImplementationMonitoringPrintSettingsInitUpdateInEntity = {
    sysCd: systemCommonsStore.getSystemCode ?? '71101',
    sysRyaku: Or50425Const.DEFAULT.SYS_RYAKU,
    houjinId: systemCommonsStore.getHoujinId ?? '0',
    shisetuId: local.shisetuId,
    svJigyoId: local.svJigyoId,
    shokuId: systemCommonsStore.getStaffId ?? '0',
    userId: local.userId,
    tantoId: local.tantoId,
    sectionName: local.sectionName,
    index: Or50425Const.DEFAULT.INDEX,
    syubetsuId: local.syubetsuId,
    kojinhogoUsedFlg: Or50425Const.DEFAULT.KOJINHOGO_USED_FLG,
    sectionAddNo: Or50425Const.DEFAULT.SECTION_ADD_NO,
  } as ImplementationMonitoringPrintSettingsInitUpdateInEntity
  const resp: IAssessmentInterRAIPrintSettingsSelectOutEntity = await ScreenRepository.update(
    'implementationMonitoringPrintSettingsInitUpdate',
    inputData
  )
  if (resp?.data) {
    // レスポンスパラメータ詳細
    localData.data = { ...resp?.data }

    // 期間管理フラグ
    kikanFlag.value = localData.data.kikanFlg

    // 担当ケアマネ

    // 担当ケアマネ
    orX0145Type.value.value = {
      counter: Or50425Const.DEFAULT.STR.EMPTY,
      chkShokuId: Or50425Const.DEFAULT.STR.EMPTY,
      houjinId: Or50425Const.DEFAULT.STR.EMPTY,
      shisetuId: Or50425Const.DEFAULT.STR.EMPTY,
      svJigyoId: Or50425Const.DEFAULT.STR.EMPTY,
      shokuin1Kana: Or50425Const.DEFAULT.STR.EMPTY,
      shokuin2Kana: Or50425Const.DEFAULT.STR.EMPTY,
      shokuin1Knj: Or50425Const.DEFAULT.STR.EMPTY,
      shokuin2Knj: Or50425Const.DEFAULT.STR.EMPTY,
      sex: Or50425Const.DEFAULT.STR.EMPTY,
      birthdayYmd: Or50425Const.DEFAULT.STR.EMPTY,
      zip: Or50425Const.DEFAULT.STR.EMPTY,
      kencode: Or50425Const.DEFAULT.STR.EMPTY,
      citycode: Or50425Const.DEFAULT.STR.EMPTY,
      areacode: Or50425Const.DEFAULT.STR.EMPTY,
      addressKnj: Or50425Const.DEFAULT.STR.EMPTY,
      tel: Or50425Const.DEFAULT.STR.EMPTY,
      kaikeiId: Or50425Const.DEFAULT.STR.EMPTY,
      kyuyoKbn: Or50425Const.DEFAULT.STR.EMPTY,
      partKbn: Or50425Const.DEFAULT.STR.EMPTY,
      inYmd: Or50425Const.DEFAULT.STR.EMPTY,
      outYmd: Or50425Const.DEFAULT.STR.EMPTY,
      shozokuId: Or50425Const.DEFAULT.STR.EMPTY,
      shokushuId: Or50425Const.DEFAULT.STR.EMPTY,
      shokuId: Or50425Const.DEFAULT.STR.EMPTY,
      timeStmp: Or50425Const.DEFAULT.STR.EMPTY,
      delFlg: Or50425Const.DEFAULT.STR.EMPTY,
      shokuNumber: Or50425Const.DEFAULT.STR.EMPTY,
      caremanagerKbn: Or50425Const.DEFAULT.STR.EMPTY,
      shokuType1: Or50425Const.DEFAULT.STR.EMPTY,
      shokuType2: Or50425Const.DEFAULT.STR.EMPTY,
      kGroupid: Or50425Const.DEFAULT.STR.EMPTY,
      bmpPath: Or50425Const.DEFAULT.STR.EMPTY,
      bmpYmd: Or50425Const.DEFAULT.STR.EMPTY,
      hankoPath: Or50425Const.DEFAULT.STR.EMPTY,
      kojinPath: Or50425Const.DEFAULT.STR.EMPTY,
      keitaitel: Or50425Const.DEFAULT.STR.EMPTY,
      eMail: Or50425Const.DEFAULT.STR.EMPTY,
      senmonNo: Or50425Const.DEFAULT.STR.EMPTY,
      sgfFlg: Or50425Const.DEFAULT.STR.EMPTY,
      srvSekiKbn: Or50425Const.DEFAULT.STR.EMPTY,
      shokushuId2: Or50425Const.DEFAULT.STR.EMPTY,
      shokushuId3: Or50425Const.DEFAULT.STR.EMPTY,
      shokushuId4: Or50425Const.DEFAULT.STR.EMPTY,
      shokushuId5: Or50425Const.DEFAULT.STR.EMPTY,
      shikakuId1: Or50425Const.DEFAULT.STR.EMPTY,
      shikakuId2: Or50425Const.DEFAULT.STR.EMPTY,
      shikakuId3: Or50425Const.DEFAULT.STR.EMPTY,
      shikakuId4: Or50425Const.DEFAULT.STR.EMPTY,
      shikakuId5: Or50425Const.DEFAULT.STR.EMPTY,
      kyuseiFlg: Or50425Const.DEFAULT.STR.EMPTY,
      kyuseiKana: Or50425Const.DEFAULT.STR.EMPTY,
      kyuseiKnj: Or50425Const.DEFAULT.STR.EMPTY,
      sort: Or50425Const.DEFAULT.STR.EMPTY,
      selfNumber: Or50425Const.DEFAULT.STR.EMPTY,
      ichiranShokushuIdNm: Or50425Const.DEFAULT.STR.EMPTY,
      shokushuId2Nm: Or50425Const.DEFAULT.STR.EMPTY,
      shokushuId3Nm: Or50425Const.DEFAULT.STR.EMPTY,
      shokushuId4Nm: Or50425Const.DEFAULT.STR.EMPTY,
      shokushuId5Nm: Or50425Const.DEFAULT.STR.EMPTY,
      stopFlg: Or50425Const.DEFAULT.STR.EMPTY,
      shokuinKnj: local.tantoId,
      shokuinKana: Or50425Const.DEFAULT.STR.EMPTY,
      title: Or50425Const.DEFAULT.STR.EMPTY,
      value: Or50425Const.DEFAULT.STR.EMPTY,
    } as TantoCmnShokuin

    const prtList: Mo01334Items[] = []
    for (const item of resp.data.prtList) {
      if (item) {
        prtList.push({
          id: item.prtNo,
          mo01337OnewayLedgerName: {
            value: item.prtTitle,
            unit: Or50425Const.DEFAULT.STR.EMPTY,

            customClass: new CustomClass({
              outerClass: 'mr-2',
              outerStyle: 'background-color: rgba(0, 0, 0, 0);',
              labelClass: 'ma-0',
              itemClass: 'ml-0',
              itemStyle: 'width: 128px;',
            }),
          } as Mo01337OnewayType,
          prnDate: item.prnDate === Or50425Const.DEFAULT.STR.TRUE,
          selectable: true,
          profile: item.profile,
          index: item.index,
          prtNo: item.prtNo,
        } as Mo01334Items)

        if (prtList.length === Or50425Const.DEFAULT.NUMBER.ONE) {
          mo00018TypeColor.value.modelValue = item.param05 === Or50425Const.DEFAULT.STR.TRUE
        }
      }
    }
    mo01334Oneway.value.items = prtList
    initFlag.value = true
    // アセスメント履歴情報を取得する
    await getPrintSettingsHistoryList()
    outputLedgerName(local.prtNo)
  }

  initSetting()
}

/**
 * アセスメント履歴一覧データ
 *
 * @param historyList - アセスメント履歴リスト
 */
const getHistoryData = (historyList: PeriodHistoryEntity[]) => {
  // if (Or50425Const.DEFAULT.KIKAN_FLG_1 === kikanFlag.value) {
  // const tempList: string[] = [] as string[]
  const list: OrX0128Items[] = []
  for (const item of historyList) {
    if (item) {
      list.push({
        sel: item.sel,
        kjisshi1Id: item.kjisshi1Id,
        syoriYm: item.syoriYm,
        planCreateYmd: item.planCreateYmd,
        shokuId: item.shokuId,
        shokuinKnj: item.shokuinKnj,
        id: item.kjisshi1Id,
      } as OrX0128Items)
    }
  }
  orX0128OnewayModel.items = list
  // 親画面.ヘッダIDを対するレコードを選択状態にする
  orX0128OnewayModel.initSelectId = (
    list.findIndex((item) => item.kjisshi1Id === local.kjisshi1Id) + 1
  ).toString()
}

/**
 * 画面印刷設定内容を保存
 */
const save = async (): Promise<IFreeAssessmentFacePrintSettingsUpdateOutEntity> => {
  // バックエンドAPIから印刷設定情報保存
  const inputData: IFreeAssessmentFacePrintSettingsUpdateInEntity = {
    sysRyaku: Or50425Const.DEFAULT.SYS_RYAKU,
    sectionName: local.sectionName,
    gsyscd: systemCommonsStore.getSystemCode ?? '71101',
    shokuId: systemCommonsStore.getStaffId ?? '0',
    houjinId: systemCommonsStore.getHoujinId ?? '0',
    shisetuId: local.shisetuId,
    svJigyoId: local.svJigyoId,
    index: local.index,
    sysIniInfo: local.sysIniInfo,
    prtList: localData.data.prtList,
  } as IFreeAssessmentFacePrintSettingsUpdateInEntity

  const resp: IFreeAssessmentFacePrintSettingsUpdateOutEntity = await ScreenRepository.update(
    'freeAssessmentFacePrintSettingsUpdate',
    inputData
  )
  return resp
}

/**
 * GUI00617_承認欄登録画面をポップアップで起動する。
 */
const openGUI00617 = () => {
  // 承認欄情報：親画面.承認欄情報（0：共有する、1：帳票毎保持する）TODO
  // 事業所ID：親画面.事業所ID
  localOneway.orX0135Oneway.svJigyoId = '1'
  // セクション：印刷設定情報リスト.プロファイル
  // 法人ID：親画面.法人ID
  localOneway.orX0135Oneway.houjinId = '1'
  // 施設ID：親画面.施設ID
  localOneway.orX0135Oneway.shisetuId = '1'

  OrX0135Logic.state.set({
    uniqueCpId: orX0135_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「閉じるボタン」押下
 */
const close = async () => {
  await save()
  setState({
    isOpen: false,
    param: {} as Or50425Param,
  })
}

/**
 * 「PDFダウンロード」ボタン押下
 */
const pdfDownload = async () => {
  // 利用者選択方法が「単一」
  if (Or50425Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
    // 履歴選択が「単一」
    if (Or50425Const.DEFAULT.TANI === mo00039OneWayHistorySelectType.value) {
      orX0117Oneway.type = Or50425Const.DEFAULT.STR.ONE
      //記入用シートを印刷するチェック入れるの場合
      if (mo00018EmptyFlg.value.modelValue) {
        // 帳票側の処理を呼び出し、帳票レイアウトのみ印刷する
        const inputData: ICpnTucRaiAssReportSelectInEntity = {
          svJigyoKnj: local.svJigyoKnj,
          syscd: systemCommonsStore.getSystemCode ?? '71101',
          printSet: {
            shiTeiKubun: mo00039Type.value,
            shiTeiDate: mo00020Type.value.value ? mo00020Type.value.value.split('/').join('-') : '',
          } as PrintSetEntity,
          printOption: {
            emptyFlg: String(mo00018EmptyFlg.value.modelValue),
            kinyuAssType: mo00039OneWayAssessmentTypeType.value,
            colorFlg: String(mo00018TypeColor.value.modelValue),
          } as PrintOptionEntity,
          printSubjectHistoryList: [] as PrintSubjectHistoryEntity[],
        } as ICpnTucRaiAssReportSelectInEntity
        // 帳票出力
        await reportOutput(local.reportId, inputData, reportOutputType.DOWNLOAD)
        return
      }
      // 記入用シートを印刷するチェック外すの場合
      else {
        // 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
        if (local.userNoSelect) {
          // メッセージを表示
          const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
            // ダイアログタイトル
            dialogTitle: t('label.confirm'),
            // ダイアログテキスト
            dialogText: t('message.i-cmn-11393'),
            firstBtnType: 'normal1',
            firstBtnLabel: t('btn.yes'),
            secondBtnType: 'blank',
            thirdBtnType: 'blank',
          })
          // はい
          if (dialogResult === Or50425Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
            // 処理終了
            return
          }
        }

        // 履歴一覧が0件選択の場合（※履歴リストが0件、複数件を含む）
        if (local.historyNoSelect) {
          // メッセージを表示
          const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
            // ダイアログタイトル
            dialogTitle: t('label.confirm'),
            // ダイアログテキスト
            dialogText: t('message.i-cmn-11455'),
            firstBtnType: 'normal1',
            firstBtnLabel: t('btn.yes'),
            secondBtnType: 'blank',
            thirdBtnType: 'blank',
          })
          // はい
          if (dialogResult === Or50425Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
            // 処理終了
            return
          }
        }
      }

      // 履歴情報リストにデータを選択する場合
      if (!local.historyNoSelect) {
        // 印刷ダイアログ画面を開かずに、画面.印刷対象履歴リスト「利用者情報+履歴情報+出力帳票対象」を直接に利用して、帳票側の処理を呼び出す
        await reportOutputPdf()
        return
      }
    }
    // 履歴選択方法が「複数」
    else if (Or50425Const.DEFAULT.HUKUSUU === mo00039OneWayHistorySelectType.value) {
      // 履歴一覧が0件選択場合（※履歴リストが0件、複数件を含む）
      if (local.historyNoSelect) {
        // メッセージを表示
        const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
          // ダイアログタイトル
          dialogTitle: t('label.confirm'),
          // ダイアログテキスト
          dialogText: t('message.i-cmn-11455'),
          firstBtnType: 'normal1',
          firstBtnLabel: t('btn.yes'),
          secondBtnType: 'blank',
          thirdBtnType: 'blank',
        })
        // はい
        if (dialogResult === Or50425Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
          // 処理終了
          return
        }
      }
      // 履歴情報リストにデータを選択する場合
      else {
        // 印刷設定情報リストを作成
        createReportOutputData(local.prtNo)
      }
    }
  }

  // 利用者選択方法が「複数」の場合
  if (Or50425Const.DEFAULT.HUKUSUU === mo00039OneWayUserSelectType.value) {
    orX0117Oneway.type = Or50425Const.DEFAULT.STR.ONE
    // 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
    if (local.userNoSelect) {
      // メッセージを表示
      const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
        // ダイアログタイトル
        dialogTitle: t('label.confirm'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-11393'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      })
      // はい
      if (dialogResult === Or50425Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
        // 処理終了
        return
      }
    }
    // 利用者情報リストにデータを選択する場合
    else {
      // 印刷設定情報リストを作成
      await PrintSettingsSubjectSelect()
    }
  }

  // AC019-2と同じ => 画面の印刷設定情報を保存する
  await save()

  // 選択された帳票のプロファイルが””の場合
  if (local.profile === Or50425Const.DEFAULT.STR.EMPTY) {
    // メッセージを表示
    const dialogResult = await openErrorDialog(or21813.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト
      dialogText: t('message.e-cmn-40172'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
    // はい
    if (dialogResult === Or50425Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
      // 処理終了
      return
    }
  }

  // 「AC020-5-2 また AC020-5-3」で取得した印刷設定情報リスト＞0件の場合
  if (orX0117Oneway.historyList.length > 0) {
    // OrX0117のダイアログ開閉状態を更新する
    OrX0117Logic.state.set({
      uniqueCpId: orX0117.value.uniqueCpId,
      state: {
        isOpen: true,
      },
    })
  }
}

/**
 * 印刷ダイアログ画面を開
 */
const reportOutputPdf = async () => {
  try {
    // API処理失敗時のシステムエラーダイアログを非表示にする
    systemCommonsStore.setSystemErrorDialogType('hide')

    const reportData: ICpnTucRaiAssReportSelectInEntity = {
      svJigyoKnj: local.svJigyoKnj,
      syscd: systemCommonsStore.getSystemCode ?? '71101',
      printSet: {
        shiTeiKubun: mo00039Type.value,
        shiTeiDate: mo00020Type.value.value ? mo00020Type.value.value.split('/').join('-') : '',
      } as PrintSetEntity,
      printOption: {
        emptyFlg: String(mo00018EmptyFlg.value.modelValue),
        kinyuAssType: mo00039OneWayAssessmentTypeType.value,
        colorFlg: String(mo00018TypeColor.value.modelValue),
      } as PrintOptionEntity,
      printSubjectHistoryList: [] as PrintSubjectHistoryEntity[],
    } as ICpnTucRaiAssReportSelectInEntity

    const choPrtList: ChoPrtEntity[] = []
    for (const item of localData.data.prtList) {
      if (item) {
        // 全て帳票
        if (Or50425Const.DEFAULT.STR.ONE === local.prtNo) {
          choPrtList.push({
            shokuId: systemCommonsStore.getStaffId,
            sysRyaku: systemCommonsStore.getSystemAbbreviation,
            section: local.sectionName,
            prtNo: item.prtNo,
            choPro: item.profile,
            sectionName: item.defPrtTitle,
            dwobject: item.dwobject,
            prtOrient: item.prtOrient,
            prtSize: item.prtSize,
            listTitle: item.listTitle,
            prtTitle: item.prtTitle,
            mTop: item.mtop,
            mBottom: item.mbottom,
            mLeft: item.mleft,
            mRight: item.mright,
            ruler: item.ruler,
            prndate: item.prnDate,
            prnshoku: item.prnshoku,
            serialFlg: item.serialFlg,
            modFlg: item.modFlg,
            secFlg: item.secFlg,
            param01: item.param01,
            param02: item.param02,
            param03: item.param03,
            param04: item.param04,
            param05: item.param05,
            param06: item.param06,
            param07: item.param07,
            param08: item.param08,
            param09: item.param09,
            param10: item.param10,
            serialHeight: item.serialHeight,
            serialPagelen: item.serialPagelen,
            hsjId: systemCommonsStore.getHoujinId,
            param11: item.param11,
            param12: item.param12,
            param13: item.param13,
            param14: item.param14,
            param15: item.param15,
            param16: item.param16,
            param17: item.param17,
            param18: item.param18,
            param19: item.param19,
            param20: item.param20,
            param21: item.param21,
            param22: item.param22,
            param23: item.param23,
            param24: item.param24,
            param25: item.param25,
            param26: item.param26,
            param27: item.param28,
            param28: item.param28,
            param29: item.param29,
            param30: item.param30,
            param31: item.param31,
            param32: item.param32,
            param33: item.param33,
            param34: item.param34,
            param35: item.param35,
            param36: item.param36,
            param37: item.param37,
            param38: item.param38,
            param39: item.param39,
            param40: item.param40,
            param41: item.param41,
            param42: item.param42,
            param43: item.param43,
            param44: item.param44,
            param45: item.param45,
            param46: item.param46,
            param47: item.param47,
            param48: item.param48,
            param49: item.param49,
            param50: item.param50,
            houjinId: systemCommonsStore.getHoujinId,
            shisetuId: systemCommonsStore.getShisetuId,
            svJigyoId: systemCommonsStore.getSvJigyoId,
            zoomRate: item.zoomRate,
            modifiedCnt: item.modifiedCnt,
          } as ChoPrtEntity)
        }
        //  単一帳票
        else if (local.prtNo === item.prtNo) {
          choPrtList.push({
            shokuId: systemCommonsStore.getStaffId,
            sysRyaku: systemCommonsStore.getSystemAbbreviation,
            section: local.sectionName,
            prtNo: item.prtNo,
            choPro: item.profile,
            sectionName: item.defPrtTitle,
            dwobject: item.dwobject,
            prtOrient: item.prtOrient,
            prtSize: item.prtSize,
            listTitle: item.listTitle,
            prtTitle: item.prtTitle,
            mTop: item.mtop,
            mBottom: item.mbottom,
            mLeft: item.mleft,
            mRight: item.mright,
            ruler: item.ruler,
            prndate: item.prnDate,
            prnshoku: item.prnshoku,
            serialFlg: item.serialFlg,
            modFlg: item.modFlg,
            secFlg: item.secFlg,
            param01: item.param01,
            param02: item.param02,
            param03: item.param03,
            param04: item.param04,
            param05: item.param05,
            param06: item.param06,
            param07: item.param07,
            param08: item.param08,
            param09: item.param09,
            param10: item.param10,
            serialHeight: item.serialHeight,
            serialPagelen: item.serialPagelen,
            hsjId: systemCommonsStore.getHoujinId,
            param11: item.param11,
            param12: item.param12,
            param13: item.param13,
            param14: item.param14,
            param15: item.param15,
            param16: item.param16,
            param17: item.param17,
            param18: item.param18,
            param19: item.param19,
            param20: item.param20,
            param21: item.param21,
            param22: item.param22,
            param23: item.param23,
            param24: item.param24,
            param25: item.param25,
            param26: item.param26,
            param27: item.param28,
            param28: item.param28,
            param29: item.param29,
            param30: item.param30,
            param31: item.param31,
            param32: item.param32,
            param33: item.param33,
            param34: item.param34,
            param35: item.param35,
            param36: item.param36,
            param37: item.param37,
            param38: item.param38,
            param39: item.param39,
            param40: item.param40,
            param41: item.param41,
            param42: item.param42,
            param43: item.param43,
            param44: item.param44,
            param45: item.param45,
            param46: item.param46,
            param47: item.param47,
            param48: item.param48,
            param49: item.param49,
            param50: item.param50,
            houjinId: systemCommonsStore.getHoujinId,
            shisetuId: systemCommonsStore.getShisetuId,
            svJigyoId: systemCommonsStore.getSvJigyoId,
            zoomRate: item.zoomRate,
            modifiedCnt: item.modifiedCnt,
          } as ChoPrtEntity)
        }
      }
    }

    // TODO 印刷設定情報リストパラメータを作成
    reportData.printSubjectHistoryList.push({
      userId: local.userList.length > 0 ? local.userList[0].userId : Or50425Const.DEFAULT.STR.EMPTY,
      userName:
        local.userList.length > 0 ? local.userList[0].userName : Or50425Const.DEFAULT.STR.EMPTY,
      sc1Id: '1708',
      startYmd: '2022/10/01',
      endYmd: '2023/09/30',
      raiId: '63',
      assType:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].assType as string)
          : Or50425Const.DEFAULT.STR.EMPTY,
      assDateYmd:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].assDateYmd as string)
          : Or50425Const.DEFAULT.STR.EMPTY,
      assShokuId: '1',
      result: Or50425Const.DEFAULT.STR.EMPTY,
      choPrtList: choPrtList,
    } as PrintSubjectHistoryEntity)
    // 帳票出力
    await reportOutput(local.reportId, reportData, reportOutputType.DOWNLOAD)
  } catch (e) {
    $log.debug('帳票の出力に失敗しました。', local.reportId, reportOutputType.DOWNLOAD, e)
  } finally {
    // API処理失敗時のシステムエラーダイアログの表示タイプをデフォルトに戻す
    systemCommonsStore.setSystemErrorDialogType('details')
  }
}

/**
 * 印刷設定情報リストを作成(利用者選択が「複数」、利用者情報リストにデータを選択する場合)
 */
const PrintSettingsSubjectSelect = async () => {
  //TODO 印刷設定情報リストを作成する
  const inputData: ImplementationMonitoringPrintSettingsSubjectSelectInEntity = {
    svJigyoId: local.svJigyoId,
    processYm: mo00020TypeKijunbi.value.value ?? Or50425Const.DEFAULT.STR.EMPTY,
    userList: local.userList,
  } as ImplementationMonitoringPrintSettingsSubjectSelectInEntity
  const resp: ImplementationMonitoringPrintSettingsSubjectSelectOutEntity =
    await ScreenRepository.select('implementationMonitoringPrintSettingsSubjectSelect', inputData)

  const list: OrX0117History[] = []
  if (resp.data) {
    for (const data of resp.data.printSubjectHistoryList) {
      if (data) {
        // 利用者複数の場合
        if (Or50425Const.DEFAULT.HUKUSUU === mo00039OneWayUserSelectType.value) {
          // 印刷設定情報リストを作成
          const reportData: ICpnTucRaiAssReportSelectInEntity = {
            svJigyoKnj: local.svJigyoKnj,
            syscd: systemCommonsStore.getSystemCode ?? '71101',
            printSet: {
              shiTeiKubun: mo00039Type.value,
              shiTeiDate: mo00020Type.value.value
                ? mo00020Type.value.value.split('/').join('-')
                : '',
            } as PrintSetEntity,
            printOption: {
              emptyFlg: String(mo00018EmptyFlg.value.modelValue),
              kinyuAssType: mo00039OneWayAssessmentTypeType.value,
              colorFlg: String(mo00018TypeColor.value.modelValue),
            } as PrintOptionEntity,
            printSubjectHistoryList: [] as PrintSubjectHistoryEntity[],
          } as ICpnTucRaiAssReportSelectInEntity

          const choPrtList: ChoPrtEntity[] = []
          for (const item of localData.data.prtList) {
            if (item) {
              // 全て帳票
              if (Or50425Const.DEFAULT.STR.ONE === local.prtNo) {
                choPrtList.push({
                  shokuId: systemCommonsStore.getStaffId,
                  sysRyaku: systemCommonsStore.getSystemAbbreviation,
                  section: local.sectionName,
                  prtNo: item.prtNo,
                  choPro: item.profile,
                  sectionName: item.defPrtTitle,
                  dwobject: item.dwobject,
                  prtOrient: item.prtOrient,
                  prtSize: item.prtSize,
                  listTitle: item.listTitle,
                  prtTitle: item.prtTitle,
                  mTop: item.mtop,
                  mBottom: item.mbottom,
                  mLeft: item.mleft,
                  mRight: item.mright,
                  ruler: item.ruler,
                  prndate: item.prnDate,
                  prnshoku: item.prnshoku,
                  serialFlg: item.serialFlg,
                  modFlg: item.modFlg,
                  secFlg: item.secFlg,
                  param01: item.param01,
                  param02: item.param02,
                  param03: item.param03,
                  param04: item.param04,
                  param05: item.param05,
                  param06: item.param06,
                  param07: item.param07,
                  param08: item.param08,
                  param09: item.param09,
                  param10: item.param10,
                  serialHeight: item.serialHeight,
                  serialPagelen: item.serialPagelen,
                  hsjId: systemCommonsStore.getHoujinId,
                  param11: item.param11,
                  param12: item.param12,
                  param13: item.param13,
                  param14: item.param14,
                  param15: item.param15,
                  param16: item.param16,
                  param17: item.param17,
                  param18: item.param18,
                  param19: item.param19,
                  param20: item.param20,
                  param21: item.param21,
                  param22: item.param22,
                  param23: item.param23,
                  param24: item.param24,
                  param25: item.param25,
                  param26: item.param26,
                  param27: item.param28,
                  param28: item.param28,
                  param29: item.param29,
                  param30: item.param30,
                  param31: item.param31,
                  param32: item.param32,
                  param33: item.param33,
                  param34: item.param34,
                  param35: item.param35,
                  param36: item.param36,
                  param37: item.param37,
                  param38: item.param38,
                  param39: item.param39,
                  param40: item.param40,
                  param41: item.param41,
                  param42: item.param42,
                  param43: item.param43,
                  param44: item.param44,
                  param45: item.param45,
                  param46: item.param46,
                  param47: item.param47,
                  param48: item.param48,
                  param49: item.param49,
                  param50: item.param50,
                  houjinId: systemCommonsStore.getHoujinId,
                  shisetuId: systemCommonsStore.getShisetuId,
                  svJigyoId: systemCommonsStore.getSvJigyoId,
                  zoomRate: item.zoomRate,
                  modifiedCnt: item.modifiedCnt,
                } as ChoPrtEntity)
              }
              //  単一帳票
              else if (local.prtNo === item.prtNo) {
                choPrtList.push({
                  shokuId: systemCommonsStore.getStaffId,
                  sysRyaku: systemCommonsStore.getSystemAbbreviation,
                  section: local.sectionName,
                  prtNo: item.prtNo,
                  choPro: item.profile,
                  sectionName: item.defPrtTitle,
                  dwobject: item.dwobject,
                  prtOrient: item.prtOrient,
                  prtSize: item.prtSize,
                  listTitle: item.listTitle,
                  prtTitle: item.prtTitle,
                  mTop: item.mtop,
                  mBottom: item.mbottom,
                  mLeft: item.mleft,
                  mRight: item.mright,
                  ruler: item.ruler,
                  prndate: item.prnDate,
                  prnshoku: item.prnshoku,
                  serialFlg: item.serialFlg,
                  modFlg: item.modFlg,
                  secFlg: item.secFlg,
                  param01: item.param01,
                  param02: item.param02,
                  param03: item.param03,
                  param04: item.param04,
                  param05: item.param05,
                  param06: item.param06,
                  param07: item.param07,
                  param08: item.param08,
                  param09: item.param09,
                  param10: item.param10,
                  serialHeight: item.serialHeight,
                  serialPagelen: item.serialPagelen,
                  hsjId: systemCommonsStore.getHoujinId,
                  param11: item.param11,
                  param12: item.param12,
                  param13: item.param13,
                  param14: item.param14,
                  param15: item.param15,
                  param16: item.param16,
                  param17: item.param17,
                  param18: item.param18,
                  param19: item.param19,
                  param20: item.param20,
                  param21: item.param21,
                  param22: item.param22,
                  param23: item.param23,
                  param24: item.param24,
                  param25: item.param25,
                  param26: item.param26,
                  param27: item.param28,
                  param28: item.param28,
                  param29: item.param29,
                  param30: item.param30,
                  param31: item.param31,
                  param32: item.param32,
                  param33: item.param33,
                  param34: item.param34,
                  param35: item.param35,
                  param36: item.param36,
                  param37: item.param37,
                  param38: item.param38,
                  param39: item.param39,
                  param40: item.param40,
                  param41: item.param41,
                  param42: item.param42,
                  param43: item.param43,
                  param44: item.param44,
                  param45: item.param45,
                  param46: item.param46,
                  param47: item.param47,
                  param48: item.param48,
                  param49: item.param49,
                  param50: item.param50,
                  houjinId: systemCommonsStore.getHoujinId,
                  shisetuId: systemCommonsStore.getShisetuId,
                  svJigyoId: systemCommonsStore.getSvJigyoId,
                  zoomRate: item.zoomRate,
                  modifiedCnt: item.modifiedCnt,
                } as ChoPrtEntity)
              }
            }
          }
          reportData.printSubjectHistoryList.push({
            userId: data.userId,
            userName: data.userName,
            sc1Id: '',
            startYmd: '',
            endYmd: '',
            raiId: '',
            assType: '',
            assDateYmd: '',
            assShokuId: '',
            result: data.result,
            choPrtList: choPrtList,
          } as PrintSubjectHistoryEntity)
          list.push({
            reportId: local.reportId,
            outputType: reportOutputType.DOWNLOAD,
            reportData: reportData,
            userName: data.userName,
            historyDate: '',
            result: data.result,
          } as OrX0117History)
        }
      }
    }
  }
  orX0117Oneway.historyList = list
}

/**
 * 切替前の印刷設定を保存する
 *
 * @param selectId - 出力帳票ID
 */
const setBeforChangePrintData = (selectId: string) => {
  if (selectId) {
    for (const item of localData.data.prtList) {
      if (item) {
        if (selectId === item.prtNo) {
          // 日付表示有無
          item.prnDate = mo00039Type.value
          // 計画作成日印刷区分
          item.param16 = mo00039OneWayAssessmentTypeType.value
          // 敬称を変更するチェックボックス
          item.param03 = mo00018TitleOfHonorFlg.value.modelValue ? '1' : '0'
          // 敬称
          item.param04 = local.titleOfHonor.value
          // 承認欄を印刷するチェックボックス
          item.param13 = mo00018ApprovalPrintFlg.value.modelValue ? '1' : '0'
          // 印刷枠の高さを最小チェックボックス
          item.param14 = mo00018PrintFrameSizeFlg.value.modelValue ? '1' : '0'
          item.param15 = local.mo00038TypePrintFrameSize.mo00045.value
          // 印刷枠の高さを最小チェックボックスがチェックされている場合
          // if (mo00018PrintFrameSizeFlg.value.modelValue) {
          //   // 印刷枠の高さを最小の値
          //   item.param15 = local.mo00038TypePrintFrameSize.mo00045.value
          //     ? local.mo00038TypePrintFrameSize.mo00045.value
          //     : '0'
          // } else {
          //   // 印刷枠の高さを最小の値がチェックされていない場合
          //   // 印刷枠の高さを最小の値を空にする
          //   item.param15 = Or50425Const.DEFAULT.STR.EMPTY
          //   break
          // }
          // 計画作成者を印刷するチェックボックス
          item.param08 = mo00018PlanAuthorPrintFlg.value.modelValue ? '1' : '0'
          // モノクロ印刷モードチェックボックス
          item.param06 = mo00018MonochromePrintFlg.value.modelValue ? '1' : '0'
          // 項目ごとに改ページするチェックボックス
          item.param10 = mo00018PageChangeFlg.value.modelValue ? '1' : '0'
          // 今月の総括を印刷するチェックボックス
          item.param12 = mo00018SummaryPrintFlg.value.modelValue ? '1' : '0'
          // 日々の備考欄を印刷するチェックボックス
          item.param07 = mo00018RemarksPrintFlg.value.modelValue ? '1' : '0'
          // 日々の備考欄を印刷するチェックボックス
          item.param09 = local.mo00040TypeKaigodo.modelValue ?? ''
        }
      }
    }
  }
}

/**
 * 切替後の印刷設定を画面に設定する
 *
 * @param selectId - 出力帳票ID
 */
const setAfterChangePrintData = (selectId: string) => {
  if (selectId) {
    for (const item of localData.data.prtList) {
      if (item) {
        if (selectId === item.prtNo) {
          // 日付表示有無
          mo00039Type.value = item.prnDate
          // 計画作成日印刷区分
          mo00039OneWayAssessmentTypeType.value = item.param16
          // 敬称を変更するチェックボックス
          mo00018TitleOfHonorFlg.value.modelValue = item.param03 === '1' ? true : false
          // 敬称
          local.titleOfHonor.value = item.param04
          // 承認欄を印刷するチェックボックス
          mo00018ApprovalPrintFlg.value.modelValue = item.param13 === '1' ? true : false
          // 印刷枠の高さを最小チェックボックス
          mo00018PrintFrameSizeFlg.value.modelValue = item.param14 === '1' ? true : false
          // 印刷枠の高さを最小の値
          local.mo00038TypePrintFrameSize.mo00045.value = item.param15
            ? item.param15
            : Or50425Const.DEFAULT.STR.EMPTY
          // 計画作成者を印刷するチェックボックス
          mo00018PlanAuthorPrintFlg.value.modelValue = item.param08 === '1' ? true : false
          // モノクロ印刷モードチェックボックス
          mo00018MonochromePrintFlg.value.modelValue = item.param06 === '1' ? true : false
          // 項目ごとに改ページするチェックボックス
          mo00018PageChangeFlg.value.modelValue = item.param10 === '1' ? true : false
          // 今月の総括を印刷するチェックボックス
          mo00018SummaryPrintFlg.value.modelValue = item.param12 === '1' ? true : false
          // 日々の備考欄を印刷するチェックボックス
          mo00018RemarksPrintFlg.value.modelValue = item.param07 === '1' ? true : false
          // 日々の備考欄を印刷するチェックボックス
          local.mo00040TypeKaigodo.modelValue = item.param09
          break
        }
      }
    }
  }
}

/**
 * 「出力帳票名」選択
 *
 * @param selectId - 出力帳票ID
 */
const outputLedgerName = (selectId: string) => {
  if (!selectId) {
    selectId = Or50425Const.DEFAULT.SECTION_NO
  }
  let label = Or50425Const.DEFAULT.STR.EMPTY
  for (const item of mo01334Oneway.value.items) {
    if (item) {
      if (selectId === item.id && item.mo01337OnewayLedgerName) {
        const data = item.mo01337OnewayLedgerName as Mo01337OnewayType
        label = data.value
        mo01334Type.value.value = item.id

        // プロファイル
        local.profile = item.profile as string

        // 出力帳票名一覧に選択行番号
        local.index = item.index as string

        // 帳票番号
        local.prtNo = item.prtNo as string

        // 帳票ID
        setReportId(item.id)
        break
      }
    }
  }
  localOneway.mo01338OneWay.value = label

  // 画面PDFダウンロードボタン活性非活性設定
  pdfDownloadBtnSetting(selectId)

  // 帳票イニシャライズデータを取得する
  void getSectionInitializeData()
}

/**
 * 帳票ID設定
 *
 * @param prtNo - 帳票番号
 */
const setReportId = (prtNo: string) => {
  switch (prtNo) {
    case Or50425Const.DEFAULT.STR.ONE:
      local.reportId = Or50425Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.ALL
      break
    case Or50425Const.DEFAULT.STR.TWO:
      local.reportId = Or50425Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.A
      break
    case Or50425Const.DEFAULT.STR.THREE:
      local.reportId = Or50425Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.B
      break
    case Or50425Const.DEFAULT.STR.FOUR:
      local.reportId = Or50425Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.C
      break
    case Or50425Const.DEFAULT.STR.FIVE:
      local.reportId = Or50425Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.D
      break
    case Or50425Const.DEFAULT.STR.SIX:
      local.reportId = Or50425Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.E
      break
    case Or50425Const.DEFAULT.STR.SEVEN:
      local.reportId = Or50425Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.F
      break
    case Or50425Const.DEFAULT.STR.EIGHT:
      local.reportId = Or50425Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.G
      break
    case Or50425Const.DEFAULT.STR.NINE:
      local.reportId = Or50425Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.H
      break
    case Or50425Const.DEFAULT.STR.TEN:
      local.reportId = Or50425Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.I
      break
    case Or50425Const.DEFAULT.STR.ELEVEN:
      local.reportId = Or50425Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.J
      break
    case Or50425Const.DEFAULT.STR.TWELVE:
      local.reportId = Or50425Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.K
      break
    case Or50425Const.DEFAULT.STR.THIRTEEN:
      local.reportId = Or50425Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.L
      break
    case Or50425Const.DEFAULT.STR.FOURTEEN:
      local.reportId = Or50425Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.M
      break
    case Or50425Const.DEFAULT.STR.FIFTEEN:
      local.reportId = Or50425Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.N
      break
    case Or50425Const.DEFAULT.STR.SIXTEEN:
      local.reportId = Or50425Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.O
      break
    case Or50425Const.DEFAULT.STR.SEVENTEEN:
      local.reportId = Or50425Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.P
      break
    case Or50425Const.DEFAULT.STR.EIGHTEEN:
      local.reportId = Or50425Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.Q
      break
    case Or50425Const.DEFAULT.STR.NINETEEN:
      local.reportId = Or50425Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.R
      break
    case Or50425Const.DEFAULT.STR.TEENTY:
      local.reportId = Or50425Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.S
      break
    case Or50425Const.DEFAULT.STR.TWENTI_ONE:
      local.reportId = Or50425Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.T
      break
    case Or50425Const.DEFAULT.STR.TWENTI_TWO:
      local.reportId = Or50425Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.U
      break
    case Or50425Const.DEFAULT.STR.TWENTI_THREE:
      local.reportId = Or50425Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.V
      break
    default:
      local.reportId = Or50425Const.DEFAULT.STR.EMPTY
      break
  }
}

/**
 * 印刷設定情報リストを作成
 *
 * @param prtNo - 帳票番号
 */
const createReportOutputData = (prtNo: string) => {
  const list: OrX0117History[] = []
  for (const orX0128DetData of local.orX0128DetList) {
    const reportData: ICpnTucRaiAssReportSelectInEntity = {
      svJigyoKnj: local.svJigyoKnj,
      syscd: systemCommonsStore.getSystemCode ?? '71101',
      printSet: {
        shiTeiKubun: mo00039Type.value,
        shiTeiDate: mo00020Type.value.value ? mo00020Type.value.value.split('/').join('-') : '',
      } as PrintSetEntity,
      printOption: {
        emptyFlg: String(mo00018EmptyFlg.value.modelValue),
        kinyuAssType: mo00039OneWayAssessmentTypeType.value,
        colorFlg: String(mo00018TypeColor.value.modelValue),
      } as PrintOptionEntity,
      printSubjectHistoryList: [] as PrintSubjectHistoryEntity[],
    } as ICpnTucRaiAssReportSelectInEntity

    const choPrtList: ChoPrtEntity[] = []
    for (const item of localData.data.prtList) {
      if (item) {
        // 全て帳票
        if (Or50425Const.DEFAULT.STR.ONE === prtNo) {
          choPrtList.push({
            shokuId: systemCommonsStore.getStaffId,
            sysRyaku: systemCommonsStore.getSystemAbbreviation,
            section: local.sectionName,
            prtNo: item.prtNo,
            choPro: item.profile,
            sectionName: item.defPrtTitle,
            dwobject: item.dwobject,
            prtOrient: item.prtOrient,
            prtSize: item.prtSize,
            listTitle: item.listTitle,
            prtTitle: item.prtTitle,
            mTop: item.mtop,
            mBottom: item.mbottom,
            mLeft: item.mleft,
            mRight: item.mright,
            ruler: item.ruler,
            prndate: item.prnDate,
            prnshoku: item.prnshoku,
            serialFlg: item.serialFlg,
            modFlg: item.modFlg,
            secFlg: item.secFlg,
            param01: item.param01,
            param02: item.param02,
            param03: item.param03,
            param04: item.param04,
            param05: item.param05,
            param06: item.param06,
            param07: item.param07,
            param08: item.param08,
            param09: item.param09,
            param10: item.param10,
            serialHeight: item.serialHeight,
            serialPagelen: item.serialPagelen,
            hsjId: systemCommonsStore.getHoujinId,
            param11: item.param11,
            param12: item.param12,
            param13: item.param13,
            param14: item.param14,
            param15: item.param15,
            param16: item.param16,
            param17: item.param17,
            param18: item.param18,
            param19: item.param19,
            param20: item.param20,
            param21: item.param21,
            param22: item.param22,
            param23: item.param23,
            param24: item.param24,
            param25: item.param25,
            param26: item.param26,
            param27: item.param28,
            param28: item.param28,
            param29: item.param29,
            param30: item.param30,
            param31: item.param31,
            param32: item.param32,
            param33: item.param33,
            param34: item.param34,
            param35: item.param35,
            param36: item.param36,
            param37: item.param37,
            param38: item.param38,
            param39: item.param39,
            param40: item.param40,
            param41: item.param41,
            param42: item.param42,
            param43: item.param43,
            param44: item.param44,
            param45: item.param45,
            param46: item.param46,
            param47: item.param47,
            param48: item.param48,
            param49: item.param49,
            param50: item.param50,
            houjinId: systemCommonsStore.getHoujinId,
            shisetuId: systemCommonsStore.getShisetuId,
            svJigyoId: systemCommonsStore.getSvJigyoId,
            zoomRate: item.zoomRate,
            modifiedCnt: item.modifiedCnt,
          } as ChoPrtEntity)
        }
        //  単一帳票
        else if (prtNo === item.prtNo) {
          choPrtList.push({
            shokuId: systemCommonsStore.getStaffId,
            sysRyaku: systemCommonsStore.getSystemAbbreviation,
            section: local.sectionName,
            prtNo: item.prtNo,
            choPro: item.profile,
            sectionName: item.defPrtTitle,
            dwobject: item.dwobject,
            prtOrient: item.prtOrient,
            prtSize: item.prtSize,
            listTitle: item.listTitle,
            prtTitle: item.prtTitle,
            mTop: item.mtop,
            mBottom: item.mbottom,
            mLeft: item.mleft,
            mRight: item.mright,
            ruler: item.ruler,
            prndate: item.prnDate,
            prnshoku: item.prnshoku,
            serialFlg: item.serialFlg,
            modFlg: item.modFlg,
            secFlg: item.secFlg,
            param01: item.param01,
            param02: item.param02,
            param03: item.param03,
            param04: item.param04,
            param05: item.param05,
            param06: item.param06,
            param07: item.param07,
            param08: item.param08,
            param09: item.param09,
            param10: item.param10,
            serialHeight: item.serialHeight,
            serialPagelen: item.serialPagelen,
            hsjId: systemCommonsStore.getHoujinId,
            param11: item.param11,
            param12: item.param12,
            param13: item.param13,
            param14: item.param14,
            param15: item.param15,
            param16: item.param16,
            param17: item.param17,
            param18: item.param18,
            param19: item.param19,
            param20: item.param20,
            param21: item.param21,
            param22: item.param22,
            param23: item.param23,
            param24: item.param24,
            param25: item.param25,
            param26: item.param26,
            param27: item.param28,
            param28: item.param28,
            param29: item.param29,
            param30: item.param30,
            param31: item.param31,
            param32: item.param32,
            param33: item.param33,
            param34: item.param34,
            param35: item.param35,
            param36: item.param36,
            param37: item.param37,
            param38: item.param38,
            param39: item.param39,
            param40: item.param40,
            param41: item.param41,
            param42: item.param42,
            param43: item.param43,
            param44: item.param44,
            param45: item.param45,
            param46: item.param46,
            param47: item.param47,
            param48: item.param48,
            param49: item.param49,
            param50: item.param50,
            houjinId: systemCommonsStore.getHoujinId,
            shisetuId: systemCommonsStore.getShisetuId,
            svJigyoId: systemCommonsStore.getSvJigyoId,
            zoomRate: item.zoomRate,
            modifiedCnt: item.modifiedCnt,
          } as ChoPrtEntity)
        }
      }
    }

    // TODO 印刷設定情報リストパラメータを作成
    reportData.printSubjectHistoryList.push({
      userId: local.userList.length > 0 ? local.userList[0].userId : Or50425Const.DEFAULT.STR.EMPTY,
      userName:
        local.userList.length > 0 ? local.userList[0].userName : Or50425Const.DEFAULT.STR.EMPTY,
      sc1Id: '1708',
      startYmd: '2022/10/01',
      endYmd: '2023/09/30',
      raiId: '63',
      assType: orX0128DetData.assType as string,
      assDateYmd: orX0128DetData.assDateYmd as string,
      assShokuId: Or50425Const.DEFAULT.STR.EMPTY,
      result: Or50425Const.DEFAULT.STR.EMPTY,
      choPrtList: choPrtList,
    } as PrintSubjectHistoryEntity)
    list.push({
      reportId: local.reportId,
      outputType: reportOutputType.DOWNLOAD,
      reportData: reportData,
      userName:
        local.userList.length > 0 ? local.userList[0].userName : Or50425Const.DEFAULT.STR.EMPTY,
      historyDate: orX0128DetData.assDateYmd as string,
      result: Or50425Const.DEFAULT.STR.EMPTY,
    } as OrX0117History)
  }
  orX0117Oneway.historyList = list
}

/**
 * 画面PDFダウンロードボタン活性非活性設定
 *
 * @param selectId - 出力帳票ID
 */
const pdfDownloadBtnSetting = (selectId: string) => {
  // 記入用シートを印刷するチェックボックスがチェックオンの場合
  if (mo00018EmptyFlg.value.modelValue) {
    // 居宅版が選択の場合、アセスメント表（R）選択の場合
    if (
      mo00039OneWayAssessmentTypeType.value === Or50425Const.SURVEY_ASSESSMENT_KIND.HOME &&
      selectId === Or50425Const.DEFAULT.STR.NINETEEN
    ) {
      // PDFダウンロードボタンが非活性
      localOneway.mo00609Oneway.disabled = true
    }
    // 施設版が選択の場合、アセスメント表（Q,S,T）選択の場合
    else if (
      mo00039OneWayAssessmentTypeType.value === Or50425Const.SURVEY_ASSESSMENT_KIND.FACILITY &&
      (selectId === Or50425Const.DEFAULT.STR.NINETEEN ||
        selectId === Or50425Const.DEFAULT.STR.TEENTY ||
        selectId === Or50425Const.DEFAULT.STR.TWENTI_TWO)
    ) {
      // PDFダウンロードボタンが非活性
      localOneway.mo00609Oneway.disabled = true
    }
    // 高齢者住宅版が選択の場合、アセスメント表（Q,R,S,T）選択の場合
    else if (
      mo00039OneWayAssessmentTypeType.value ===
        Or50425Const.SURVEY_ASSESSMENT_KIND.SENIOR_HOUSING &&
      (selectId === Or50425Const.DEFAULT.STR.NINETEEN ||
        selectId === Or50425Const.DEFAULT.STR.TEENTY ||
        selectId === Or50425Const.DEFAULT.STR.TEENTY ||
        selectId === Or50425Const.DEFAULT.STR.TWENTI_TWO)
    ) {
      // PDFダウンロードボタンが非活性
      localOneway.mo00609Oneway.disabled = true
    } else {
      // PDFダウンロードボタンが活性
      localOneway.mo00609Oneway.disabled = false
    }
  }
  // 履歴･利用者共に単一の場合
  else if (
    mo00039OneWayUserSelectType.value === Or50425Const.DEFAULT.TANI &&
    mo00039OneWayHistorySelectType.value === Or50425Const.DEFAULT.TANI
  ) {
    // 居宅版が選択の場合、アセスメント表（R）選択の場合
    if (
      mo00039OneWayAssessmentTypeType.value === Or50425Const.SURVEY_ASSESSMENT_KIND.HOME &&
      selectId === Or50425Const.DEFAULT.STR.TEENTY
    ) {
      // PDFダウンロードボタンが非活性
      localOneway.mo00609Oneway.disabled = true
    }
    // 施設版が選択の場合、アセスメント表（Q,S,T）選択の場合
    else if (
      mo00039OneWayAssessmentTypeType.value === Or50425Const.SURVEY_ASSESSMENT_KIND.FACILITY &&
      (selectId === Or50425Const.DEFAULT.STR.NINETEEN ||
        selectId === Or50425Const.DEFAULT.STR.TEENTY ||
        selectId === Or50425Const.DEFAULT.STR.TWENTI_TWO)
    ) {
      // PDFダウンロードボタンが非活性
      localOneway.mo00609Oneway.disabled = true
    }
    // 高齢者住宅版が選択の場合、アセスメント表（Q,R,S,T）選択の場合
    else if (
      mo00039OneWayAssessmentTypeType.value ===
        Or50425Const.SURVEY_ASSESSMENT_KIND.SENIOR_HOUSING &&
      (selectId === Or50425Const.DEFAULT.STR.NINETEEN ||
        selectId === Or50425Const.DEFAULT.STR.TEENTY ||
        selectId === Or50425Const.DEFAULT.STR.TEENTY ||
        selectId === Or50425Const.DEFAULT.STR.TWENTI_TWO)
    ) {
      // PDFダウンロードボタンが非活性
      localOneway.mo00609Oneway.disabled = true
    } else {
      // PDFダウンロードボタンが活性
      localOneway.mo00609Oneway.disabled = false
    }
  }
  // 上記以外の場合
  else {
    // PDFダウンロードボタンが活性
    localOneway.mo00609Oneway.disabled = false
  }
}

/**
 * 帳票イニシャライズデータを取得する
 */
const getSectionInitializeData = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity = {
    profile: local.profile,
    gsyscd: systemCommonsStore.getSystemCode ?? '71101',
    shokuId: systemCommonsStore.getStaffId ?? '0',
    kojinhogoUsedFlg: Or50425Const.DEFAULT.KOJINHOGO_USED_FLG,
    sectionAddNo: Or50425Const.DEFAULT.SECTION_ADD_NO,
  } as IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity
  const resp: IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateOutEntity =
    await ScreenRepository.update('freeAssessmentFacePrintSettingsPrtNoChangeUpdate', inputData)
  if (resp.data) {
    local.sysIniInfo = resp.data.sysIniInfo
  }
}

/**
 * 印刷設定履歴リスト取得
 */
const getPrintSettingsHistoryList = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: ImplementationMonitoringPrintSettingsHistorySelectInEntity = {
    svJigyoId: local.svJigyoId,
    userId: local.selectUserId,
  } as ImplementationMonitoringPrintSettingsHistorySelectInEntity
  const resp: ImplementationMonitoringPrintSettingsHistorySelectOutEntity =
    await ScreenRepository.select('implementationMonitoringPrintSettingsHistorySelect', inputData)
  if (resp.data) {
    // アセスメント履歴一覧データ
    getHistoryData(resp.data.historyList)
  }
}

/**
 * 画面ボタン活性非活性設定
 */
const btnItemSetting = () => {
  // 利用者選択方法が「単一」の場合
  if (Or50425Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
    // 基準日を非表示にする
    // 履歴選択を活性表示にする
    kijunbiFlag.value = false

    // 履歴選択方法が「単一」の場合
    if (Or50425Const.DEFAULT.TANI === mo00039OneWayHistorySelectType.value) {
      // 記入用シートを印刷するチェックボックス表示
      localOneway.mo00018OneWayEmptyFlg.disabled = false
    }
    // 以外の場合
    else {
      // 記入用シートを印刷するを非活性表示にする
      localOneway.mo00018OneWayEmptyFlg.disabled = true
    }

    // 記入用シートを印刷するが「チェックオンの場合
    if (mo00018EmptyFlg.value.modelValue) {
      // localOneway.mo00039OneWayAssessmentType.disabled = false
    }
    // 以外の場合
    else {
      // localOneway.mo00039OneWayAssessmentType.disabled = true
    }
  }
  // 以外の場合
  else {
    // 基準日を活性表示にする
    // 履歴選択を非表示にする
    kijunbiFlag.value = true
    localOneway.mo00018OneWayEmptyFlg.disabled = true
    // localOneway.mo00039OneWayAssessmentType.disabled = true
  }

  // 履歴選択方法が「単一」の場合
  if (Or50425Const.DEFAULT.TANI === mo00039OneWayHistorySelectType.value) {
    // 利用者選択方法が「単一」の場合
    if (Or50425Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
      // 記入用シートを印刷するチェックボックス表示
      localOneway.mo00018OneWayEmptyFlg.disabled = false
    }
    // 以外の場合
    else {
      // 記入用シートを印刷するをチェックオフにする
      mo00018EmptyFlg.value.modelValue = false
      localOneway.mo00018OneWayEmptyFlg.disabled = true
    }
  }
  // 以外の場合
  else {
    // 記入用シートを印刷するをチェックオフにする
    mo00018EmptyFlg.value.modelValue = false
    // 記入用シートを印刷するを非活性表示にする
    localOneway.mo00018OneWayEmptyFlg.disabled = true

    // 記入用シート方式を非活性表示にする
    // localOneway.mo00039OneWayAssessmentType.disabled = true
  }

  // 履歴一覧セクション
  if (Or50425Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
    userCols.value = 5
    mo01334TypeHistoryFlag.value = true
  } else {
    userCols.value = 12
    mo01334TypeHistoryFlag.value = false
  }

  // 担当ケアマネ選択アイコンボタン非活性/活性設定
  const kkjTantoFlg: string =
    cmnRouteCom.getInitialSettingMaster()?.kkjTantoFlg ?? Or50425Const.DEFAULT.STR.EMPTY
  // 共通情報.担当ケアマネ設定フラグ > 0、且つ、共通情報.担当者IDが0以外の場合
  if (
    systemCommonsStore.getManagerId !== Or50425Const.DEFAULT.STR.ZERO &&
    parseInt(kkjTantoFlg) > Or50425Const.DEFAULT.NUMBER.ZERO
  ) {
    // 非活性
    localOneway.orX0145Oneway.disabled = true
  }
  // その他場合
  else {
    // 活性
    localOneway.orX0145Oneway.disabled = false
  }

  // 計画作成日セクション表示/非表示
  const cpnFlg: string =
    cmnRouteCom.getInitialSettingMaster()?.cpnFlg ?? Or50425Const.DEFAULT.STR.EMPTY
  if (cpnFlg === Or50425Const.DEFAULT.STR.FIVE) {
    // 共通情報.ケアプラン方式が"5:パッケージ"の場合
    mo00020CpnFlag.value = true
  } else {
    // 上記以外の場合
    mo00020CpnFlag.value = true
  }
  // 敬称/活性/非活性設定
  if (mo00018TitleOfHonorFlg.value.modelValue) {
    // 敬称を変更するチェックボックスがチェックオンの場合、活性。
    localOneway.mo00045OneWayTitleOfHonor.disabled = false
  } else {
    // 非活性
    localOneway.mo00045OneWayTitleOfHonor.disabled = true
  }

  // 承認欄を印刷するチェックボックス/活性/非活性設定
  if (mo01334Type.value.value === '1') {
    //  印刷帳票が"実施モニタリング表"の場合、活性。
    localOneway.mo00018OneWayApprovalPrintFlg.disabled = false
  } else {
    // 非活性
    localOneway.mo00018OneWayApprovalPrintFlg.disabled = true
    mo00018ApprovalPrintFlg.value.modelValue = false
  }

  // 計画作成者を印刷するチェックボックス/活性/非活性設定
  if (mo01334Type.value.value === '2') {
    //  印刷帳票が"実施モニタリング表_ケース一覧"の場合、非活性。
    localOneway.mo00018OneWayPlanAuthorPrintFlg.disabled = true
    mo00018PlanAuthorPrintFlg.value.modelValue = false
  } else {
    // 活性
    localOneway.mo00018OneWayPlanAuthorPrintFlg.disabled = false
  }

  // モノクロ印刷モードチェックボックス/活性/非活性設定
  if (mo01334Type.value.value === '1') {
    //  印刷帳票が"実施モニタリング表"の場合、活性。
    localOneway.mo00018OneWayMonochromePrintFlg.disabled = false
  } else {
    // 非活性
    localOneway.mo00018OneWayMonochromePrintFlg.disabled = true
    mo00018MonochromePrintFlg.value.modelValue = false
  }

  // 項目ごとに改ページするチェックボックス/活性/非活性設定
  if (mo01334Type.value.value === '2') {
    //  印刷帳票が"実施モニタリング表_ケース一覧"の場合、活性。
    localOneway.mo00018OneWayPageChangeFlg.disabled = false
  } else {
    // 非活性
    localOneway.mo00018OneWayPageChangeFlg.disabled = true
    mo00018PageChangeFlg.value.modelValue = false
  }

  // 今月の総括を印刷するチェックボックス/活性/非活性設定
  if (mo01334Type.value.value === '1') {
    //  印刷帳票が"実施モニタリング表"の場合、活性。
    // 日々の備考欄を印刷するチェックボックスがチェックオフの場合、活性。
    if (!mo00018RemarksPrintFlg.value.modelValue) {
      localOneway.mo00018OneWaySummaryPrintFlg.disabled = false
    } else {
      // 非活性
      localOneway.mo00018OneWaySummaryPrintFlg.disabled = true
      mo00018SummaryPrintFlg.value.modelValue = false
    }
  } else if (mo01334Type.value.value === '2') {
    // 印刷帳票が"実施モニタリング表_ケース一覧"の場合、チェックオンで、非活性。
    localOneway.mo00018OneWaySummaryPrintFlg.disabled = true
    mo00018SummaryPrintFlg.value.modelValue = true
  } else {
    // 印刷帳票が"実施モニタリング表_別紙"の場合、活性。
    localOneway.mo00018OneWaySummaryPrintFlg.disabled = false
  }

  // 日々の備考欄を印刷するチェックボックス/活性/非活性設定
  if (mo01334Type.value.value === '1') {
    //  印刷帳票が"実施モニタリング表"の場合、活性。
    // かつ今月の総括を印刷するチェックボックスがチェックオフの場合、活性。
    if (!mo00018SummaryPrintFlg.value.modelValue) {
      // 日々の備考欄を印刷するチェックボックス活性
      localOneway.mo00018OneWayRemarksPrintFlg.disabled = false
    } else {
      // 今月の総括を印刷するチェックボックスがチェックオンの場合、非活性。
      localOneway.mo00018OneWayRemarksPrintFlg.disabled = true
    }
  } else {
    // 上記以外の場合、チェックオンで、非活性。
    localOneway.mo00018OneWayRemarksPrintFlg.disabled = true
    mo00018RemarksPrintFlg.value.modelValue = true
  }

  // 印刷枠の高さを最小チェックボックス
  if (mo00018EmptyFlg.value.modelValue) {
    // 記入用シートを印刷するチェックボックスがチェックオンの場合、非活性。
    localOneway.mo00018OneWayPrintFrameSizeFlg.disabled = true
    if (localOneway.mo00038OneWayPrintFrameSize.mo00045Oneway) {
      localOneway.mo00038OneWayPrintFrameSize.mo00045Oneway.disabled = true
    }
  } else {
    // 活性
    localOneway.mo00018OneWayPrintFrameSizeFlg.disabled = false
    if (mo00018PrintFrameSizeFlg.value.modelValue) {
      // 印刷枠の高さを最小チェックボックスがチェックオンの場合、活性。
      if (localOneway.mo00038OneWayPrintFrameSize.mo00045Oneway) {
        localOneway.mo00038OneWayPrintFrameSize.mo00045Oneway.disabled = false
      }
    } else {
      // 非活性
      if (localOneway.mo00038OneWayPrintFrameSize.mo00045Oneway) {
        localOneway.mo00038OneWayPrintFrameSize.mo00045Oneway.disabled = true
      }
    }
  }
}
/**
 * 確認ダイアログ表示
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openConfirmDialog = async (
  uniqueCpId: string,
  state: Or21814OnewayType
): Promise<Or50425MsgBtnType> => {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result: Or50425MsgBtnType = Or50425Const.DEFAULT.MESSAGE_BTN_TYPE_YES

        if (event?.firstBtnClickFlg) {
          result = Or50425Const.DEFAULT.MESSAGE_BTN_TYPE_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or50425Const.DEFAULT.MESSAGE_BTN_TYPE_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = Or50425Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL
        }
        if (event?.closeBtnClickFlg) {
          result = Or50425Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * エラーダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openErrorDialog = async (
  uniqueCpId: string,
  state: Or21813StateType
): Promise<Or50425MsgBtnType> => {
  Or21813Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(uniqueCpId)

        let result = Or50425Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL as Or50425MsgBtnType

        if (event?.firstBtnClickFlg) {
          result = Or50425Const.DEFAULT.MESSAGE_BTN_TYPE_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or50425Const.DEFAULT.MESSAGE_BTN_TYPE_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = Or50425Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL
        }
        if (event?.closeBtnClickFlg) {
          result = Or50425Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL
        }

        // エラーダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 「出力帳票名」選択
 */
watch(
  () => mo01334Type.value.value,
  (newValue, oldValue) => {
    setBeforChangePrintData(oldValue)
    setAfterChangePrintData(newValue)
    outputLedgerName(newValue)
    btnItemSetting()
    // 日付印刷区分が2の場合
    if (Or50425Const.DEFAULT.STR.TWO === mo00039Type.value) {
      // 指定日を活性表示にする。
      mo00020Flag.value = true
    } else {
      // 指定日を非表示にする。
      mo00020Flag.value = false
    }
  }
)

/**
 * 「日付印刷区分」ラジオボタン押下
 */
watch(
  () => mo00039Type.value,
  (newValue) => {
    if (Or50425Const.DEFAULT.STR.TWO === newValue) {
      // 指定日を活性表示にする
      mo00020Flag.value = true
    } else {
      // 指定日を非表示にする
      mo00020Flag.value = false
    }
  }
)

/**
 * 敬称を変更するチェックボックス押下
 */
watch(
  () => mo00018TitleOfHonorFlg.value.modelValue,
  (newValue) => {
    if (newValue === true) {
      localOneway.mo00045OneWayTitleOfHonor.disabled = false
    } else {
      localOneway.mo00045OneWayTitleOfHonor.disabled = true
    }
  }
)

/**
 * 「利用者選択方法」ラジオボタン選択
 */
watch(
  () => mo00039OneWayUserSelectType.value,
  (newValue) => {
    // 画面ボタン活性非活性設定
    btnItemSetting()

    // 利用者選択方法が「単一」の場合
    if (Or50425Const.DEFAULT.TANI === newValue) {
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.TANI
      orX0130Oneway.tableStyle = 'width: 210px'

      if (OrX0130Logic.event.get(orX0130.value.uniqueCpId)) {
        if (
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList &&
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList.length > 0
        ) {
          local.userList = []
          for (const item of OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList) {
            if (item) {
              local.userList.push({
                userId: item.userId,
                userName: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
              } as UserEntity)
            }
          }
          local.userNoSelect = false
        } else {
          local.userList = []
          local.userNoSelect = true
        }
      } else {
        local.userList = []
        local.userNoSelect = true
      }
    } else {
      // 復元
      orX0117Oneway.type = Or50425Const.DEFAULT.STR.ONE
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.HUKUSUU
      orX0130Oneway.tableStyle = 'width: 430px'

      if (OrX0130Logic.event.get(orX0130.value.uniqueCpId)) {
        if (
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList &&
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList.length > 0
        ) {
          local.userList = []
          for (const item of OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList) {
            if (item) {
              local.userList.push({
                userId: item.userId,
                userName: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
              } as UserEntity)
            }
          }
          local.userNoSelect = false
        } else {
          local.userList = []
          local.userNoSelect = true
        }
      } else {
        local.userList = []
        local.userNoSelect = true
      }
    }

    // 利用者一覧明細に親画面.利用者IDが存在する場合
    if (local.userId) {
      // 利用者IDを対するレコードを選択状態にする
      orX0130Oneway.userId = local.userId
    }
    // 画面PDFダウンロードボタン活性非活性設定
    pdfDownloadBtnSetting(local.prtNo)
  }
)

/**
 * 「履歴選択方法」ラジオボタン押下
 */
watch(
  () => mo00039OneWayHistorySelectType.value,
  (newValue) => {
    // 画面ボタン活性非活性設定
    btnItemSetting()

    if (OrX0128Logic.event.get(orX0128.value.uniqueCpId)) {
      if (
        OrX0128Logic.event.get(orX0128.value.uniqueCpId)!.orX0128DetList &&
        OrX0128Logic.event.get(orX0128.value.uniqueCpId)!.orX0128DetList.length > 0
      ) {
        local.historyNoSelect = false
      } else {
        local.historyNoSelect = true
      }
    } else {
      local.historyNoSelect = true
    }

    // 履歴選択方法が「単一」の場合
    if (Or50425Const.DEFAULT.TANI === newValue) {
      orX0128OnewayModel.singleFlg = OrX0128Const.DEFAULT.TANI
    } else {
      orX0128OnewayModel.singleFlg = OrX0128Const.DEFAULT.HUKUSUU
      orX0117Oneway.type = Or50425Const.DEFAULT.STR.ZERO
    }
  }
)

/**
 * 記入用シート方式ラジオボタン監視
 */
watch(
  () => mo00018EmptyFlg.value.modelValue,
  () => {
    // 画面PDFダウンロードボタン活性非活性設定
    pdfDownloadBtnSetting(local.prtNo)

    // 画面ボタン活性非活性設定
    btnItemSetting()
  }
)

/**
 * 印刷枠の高さを最小チェックボックス監視
 */
watch(
  () => mo00018PrintFrameSizeFlg.value.modelValue,
  () => {
    // 画面ボタン活性非活性設定
    btnItemSetting()
  }
)

/**
 * 今月の総括を印刷するチェックボックス押下
 */
watch(
  () => mo00018SummaryPrintFlg.value.modelValue,
  () => {
    // 画面ボタン活性非活性設定
    btnItemSetting()
  }
)

/**
 * 日々の備考欄を印刷するチェックボックス押下
 */
watch(
  () => mo00018RemarksPrintFlg.value.modelValue,
  () => {
    // 画面ボタン活性非活性設定
    btnItemSetting()
  }
)

/**
 * 「履歴選択」の監視
 */
watch(
  () => OrX0128Logic.event.get(orX0128.value.uniqueCpId),
  (newValue) => {
    // 画面ボタン活性非活性設定
    btnItemSetting()
    if (newValue) {
      if (newValue.historyDetClickFlg) {
        local.orX0128DetList = newValue.orX0128DetList
        if (newValue.orX0128DetList.length > 0) {
          local.historyNoSelect = false

          for (const item of newValue.orX0128DetList) {
            if (item) {
              // 記入用シート方式ラジオボタン(デフォルト値の設定)
              if (!mo00039OneWayAssessmentTypeType.value) {
                mo00039OneWayAssessmentTypeType.value = item.assType as string
              }
            }
          }
        } else {
          local.historyNoSelect = true
        }
      } else {
        local.historyNoSelect = true
      }
    }
  }
)

/**
 * 「利用者選択」の監視
 */
watch(
  () => OrX0130Logic.event.get(orX0130.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.clickFlg) {
      if (newValue.userList.length > 0) {
        local.userNoSelect = false
        local.selectUserId = newValue.userList[0].id

        local.userList = []
        for (const item of newValue.userList) {
          if (item) {
            local.userList.push({
              userId: item.userId,
              userName: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
            } as UserEntity)
          }
        }

        // 利用者選択方法が「単一」の場合
        if (Or50425Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
          if (initFlag.value) {
            // アセスメント履歴情報を取得する
            await getPrintSettingsHistoryList()
          }
        }
        // 利用者選択方法が「複数」の場合
        else if (Or50425Const.DEFAULT.HUKUSUU === mo00039OneWayUserSelectType.value) {
          // 利用者一覧明細に前回選択された利用者が選択状態になる
          createReportOutputData(local.prtNo)
        }
      } else {
        local.userNoSelect = true
        local.userList = []
      }
    } else {
      local.userNoSelect = true
      local.userList = []
    }
  }
)

/**
 * 「担当ケアマネプルダウン」選択の監視
 */
watch(
  () => orX0145Type.value.value,
  (newValue) => {
    // TODO 担当ケアマネ有機体が未作成 「担当ケアマネプルダウン」選択変更がある場合
    console.log(newValue)
  }
)

/**
 * イベントリスナーの解除
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    if (!newValue) {
      await save()
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        class="or50425_row flex-nowrap"
        no-gutter
      >
        <c-v-col
          cols="12"
          sm="auto"
          class="or50425_table"
        >
          <base-mo-01334
            v-model="mo01334Type"
            :oneway-model-value="mo01334Oneway"
            class="list-wrapper"
            style="text-align: left"
          >
            <!-- 帳票 -->
            <template #[`item.ledgerName`]="{ item }">
              <!-- 分子：一覧専用ラベル（文字列型） -->
              <base-mo01337 :oneway-model-value="item.mo01337OnewayLedgerName" />
            </template>
            <!-- ページングを非表示 -->
            <template #bottom />
          </base-mo-01334>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="auto"
          class="content_center"
        >
          <c-v-row
            no-gutter
            class="printerOption customCol or50425_row"
          >
            <c-v-col
              cols="12"
              sm="12"
            >
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayBasicSettings"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
          <!-- 基本設定  -->
          <c-v-row
            no-gutter
            class="customCol or50425_row"
            style="width: 424px"
          >
            <c-v-col
              cols="12"
              sm="3"
            >
              <base-mo00615 :oneway-model-value="localOneway.mo00615OneWayTypeTitle"></base-mo00615>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="9"
            >
              <base-mo01338 :oneway-model-value="localOneway.mo01338OneWay"></base-mo01338>
            </c-v-col>
          </c-v-row>
          <c-v-divider></c-v-divider>
          <!-- 日付印刷セクション -->
          <c-v-row
            no-gutter
            class="customCol or50425_row pt-2"
          >
            <c-v-col
              cols="12"
              sm="6"
              class="pa-0"
            >
              <base-mo00039
                v-model="mo00039Type"
                :oneway-model-value="localOneway.mo00039OneWay"
              >
              </base-mo00039>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="4"
              class="pa-0 pr-2"
            >
              <base-mo00020
                v-if="mo00020Flag"
                v-model="mo00020Type"
                :oneway-model-value="localOneway.mo00020OneWay"
              >
              </base-mo00020>
            </c-v-col>
          </c-v-row>
          <!-- 計画作成日セクション -->
          <c-v-divider v-if="!isCarePlanStyle5"></c-v-divider>
          <c-v-row
            v-if="!isCarePlanStyle5"
            no-gutter
            class="customCol or50425_row pt-2"
          >
            <c-v-col
              cols="12"
              sm="6"
              class="pa-0"
            >
              <base-mo00039
                v-model="mo00039OneWayAssessmentTypeType"
                :oneway-model-value="localOneway.mo00039OneWayAssessmentType"
              >
              </base-mo00039>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="4"
              class="pa-0 pr-2"
            >
              <base-mo00020
                v-if="
                  mo00039OneWayAssessmentTypeType ===
                  Or50425Const.PLAN_CREATION_DATE_PRINT_KIND.PRINT
                "
                v-model="mo00020Type"
                :oneway-model-value="localOneway.mo00020OneWay"
              >
              </base-mo00020>
            </c-v-col>
          </c-v-row>
          <!-- 印刷オプション -->
          <c-v-row
            no-gutter
            class="printerOption customCol or50425_row"
          >
            <c-v-col
              cols="12"
              sm="12"
            >
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayPrinterOption"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
          <!-- 敬称を変更するチェックボックス -->
          <c-v-row
            no-gutter
            class="customCol or50425_row d-flex align-center pt-2"
          >
            <c-v-col
              cols="12"
              sm="auto"
              class="pa-0"
            >
              <base-mo00018
                v-model="mo00018TitleOfHonorFlg"
                :oneway-model-value="localOneway.mo00018OneWayTitleOfHonorFlg"
              >
              </base-mo00018>
            </c-v-col>
            <!-- 敬称 -->
            <c-v-col
              cols="12"
              sm="5"
              class="pa-0 d-flex align-center"
            >
              <span class="pr-2">(</span>
              <base-mo00045
                v-model="local.titleOfHonor"
                :oneway-model-value="localOneway.mo00045OneWayTitleOfHonor"
              >
              </base-mo00045>
              <span> )</span>
            </c-v-col>
          </c-v-row>
          <!-- 記入用シートを印刷するチェックボックス -->
          <c-v-row
            no-gutter
            class="customCol or50425_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <base-mo00018
                v-model="mo00018EmptyFlg"
                :oneway-model-value="localOneway.mo00018OneWayEmptyFlg"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>

          <!-- 承認欄を印刷するチェックボックス -->
          <c-v-row
            no-gutter
            class="customCol or50425_row"
          >
            <c-v-col
              cols="12"
              sm="6"
              class="pa-0"
            >
              <base-mo00018
                v-model="mo00018ApprovalPrintFlg"
                :oneway-model-value="localOneway.mo00018OneWayApprovalPrintFlg"
              >
              </base-mo00018>
            </c-v-col>

            <!-- 承認欄の登録ボタン -->
            <c-v-col
              cols="12"
              sm="6"
              class="pa-0"
            >
              <base-mo00611
                :oneway-model-value="localOneway.mo00611OneWayApproval"
                @click="openGUI00617"
              >
              </base-mo00611>
            </c-v-col>
          </c-v-row>

          <!-- 印刷枠の高さを最小チェックボックス -->
          <c-v-row
            no-gutter
            class="customCol or50425_row d-flex align-center"
          >
            <c-v-col
              cols="12"
              sm="auto"
              class="pa-0"
            >
              <base-mo00018
                v-model="mo00018PrintFrameSizeFlg"
                :oneway-model-value="localOneway.mo00018OneWayPrintFrameSizeFlg"
              >
              </base-mo00018>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="auto"
              class="pa-0"
            >
              <base-mo00038
                v-model="local.mo00038TypePrintFrameSize"
                :oneway-model-value="localOneway.mo00038OneWayPrintFrameSize"
                class="input-hide-border"
              />
            </c-v-col>

            <c-v-col
              cols="12"
              sm="3"
              class="pa-0 pl-3"
            >
              <base-mo00615
                :oneway-model-value="localOneway.mo00615OneWayPrintFrameSize"
                class="input-hide-border"
              />
            </c-v-col>
          </c-v-row>

          <!-- 計画作成者を印刷するチェックボックス -->
          <c-v-row
            no-gutter
            class="customCol or50425_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <base-mo00018
                v-model="mo00018PlanAuthorPrintFlg"
                :oneway-model-value="localOneway.mo00018OneWayPlanAuthorPrintFlg"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>

          <!-- モノクロ印刷モードチェックボックス -->
          <c-v-row
            no-gutter
            class="customCol or50425_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <base-mo00018
                v-model="mo00018MonochromePrintFlg"
                :oneway-model-value="localOneway.mo00018OneWayMonochromePrintFlg"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>

          <!-- 項目ごとに改ページするチェックボックス -->
          <c-v-row
            no-gutter
            class="customCol or50425_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <base-mo00018
                v-model="mo00018PageChangeFlg"
                :oneway-model-value="localOneway.mo00018OneWayPageChangeFlg"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>

          <!-- 今月の総括を印刷するチェックボックス -->
          <c-v-row
            no-gutter
            class="customCol or50425_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <base-mo00018
                v-model="mo00018SummaryPrintFlg"
                :oneway-model-value="localOneway.mo00018OneWaySummaryPrintFlg"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>

          <!-- 日々の備考欄を印刷するチェックボックス -->
          <c-v-row
            no-gutter
            class="customCol or50425_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <base-mo00018
                v-model="mo00018RemarksPrintFlg"
                :oneway-model-value="localOneway.mo00018OneWayRemarksPrintFlg"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>
          <!-- 印刷する要介護度 -->
          <!-- 共通情報.ケアプラン方式が"5:パッケージ"の場合、非表示。上記以外の場合、表示 -->
          <c-v-row
            v-if="!isCarePlanStyle5"
            no-gutter
            class="customCol or50425_row d-flex align-center pb-3"
          >
            <c-v-col
              cols="12"
              sm="auto"
              class="pa-0 pl-3"
            >
              <base-mo00615
                :oneway-model-value="localOneway.mo00615OneWayKaigodo"
                class="input-hide-border"
              >
              </base-mo00615>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="auto"
              class="pa-0 pr-2"
            >
              <base-mo00040
                v-model="local.mo00040TypeKaigodo"
                :oneway-model-value="localOneway.mo00040OneWayKaigodo"
              />
            </c-v-col>
          </c-v-row>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="auto"
          class="content_center_2"
          style="width: 619px"
        >
          <c-v-row
            class="or50425_row"
            no-gutter
            style="align-items: center; padding-bottom: 8px"
          >
            <c-v-col
              cols="12"
              sm="4"
              style="padding: 0px"
            >
              <c-v-row
                no-gutter
                class="or50425_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                >
                  <base-mo01338
                    :oneway-model-value="localOneway.mo01338OneWayPrinterUserSelect"
                    style="background-color: transparent"
                  >
                  </base-mo01338>
                </c-v-col>
              </c-v-row>
              <c-v-row
                no-gutter
                class="or50425_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  style="padding: 0px"
                >
                  <base-mo00039
                    v-model="mo00039OneWayUserSelectType"
                    :oneway-model-value="localOneway.mo00039OneWayUserSelectType"
                  >
                  </base-mo00039>
                </c-v-col>
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-if="kijunbiFlag"
              cols="12"
              sm="4"
              style="padding: 0px"
            >
              <c-v-row
                no-gutter
                class="or50425_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                >
                  <base-mo00615 :oneway-model-value="localOneway.mo00615OneWayType"> </base-mo00615>
                </c-v-col>
              </c-v-row>
              <c-v-row
                no-gutter
                class="or50425_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  style="padding: 0px"
                >
                  <base-mo00020
                    v-model="mo00020TypeKijunbi"
                    :oneway-model-value="localOneway.mo00020KijunbiOneWay"
                  />
                </c-v-col>
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-else
              cols="12"
              sm="4"
              style="padding: 0px"
            >
              <c-v-row
                no-gutter
                class="or50425_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                >
                  <base-mo01338
                    :oneway-model-value="localOneway.mo01338OneWayPrinterHistorySelect"
                    style="background-color: transparent"
                  >
                  </base-mo01338>
                </c-v-col>
              </c-v-row>
              <c-v-row
                no-gutter
                class="or50425_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  style="padding: 0px"
                >
                  <base-mo00039
                    v-model="mo00039OneWayHistorySelectType"
                    :oneway-model-value="localOneway.mo00039OneWayHistorySelectType"
                  >
                  </base-mo00039>
                </c-v-col>
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-if="tantoIconBtn"
              cols="12"
              sm="4"
              style="padding: 0px"
            >
              <!-- 担当ケアマネプルダウン -->
              <g-custom-or-x-0145
                v-bind="orx0145"
                v-model="orX0145Type"
                :oneway-model-value="localOneway.orX0145Oneway"
              ></g-custom-or-x-0145>
            </c-v-col>
          </c-v-row>
          <!-- <c-v-divider></c-v-divider> -->
          <c-v-row
            class="or50425_row flex-nowrap"
            no-gutter
          >
            <c-v-col :cols="userCols">
              <!-- 印刷設定画面利用者一覧 -->
              <g-custom-or-x-0130
                v-if="orX0130Oneway.selectMode && initFlag"
                v-bind="orX0130"
                :oneway-model-value="orX0130Oneway"
              >
              </g-custom-or-x-0130>
            </c-v-col>
            <c-v-col
              v-if="mo01334TypeHistoryFlag && initFlag"
              cols="auto"
              class="pr-2 overflow: auto"
              style="width: 448px; height: inherit"
            >
              <div style="width: 100%; height: 100%; overflow: auto">
                <!-- 計画期間＆履歴一覧 -->
                <g-custom-or-x-0128
                  v-if="orX0128OnewayModel.singleFlg"
                  v-bind="orX0128"
                  :oneway-model-value="orX0128OnewayModel"
                ></g-custom-or-x-0128>
              </div>
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          @click="close"
        >
        </base-mo00611>
        <!-- PDFダウンロードボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          class="mx-2"
          @click="pdfDownload()"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- メッセージ 警告 -->
  <g-base-or-21815 v-bind="or21815" />
  <!-- 印刷設定帳票出力状態リスト画面 -->
  <g-custom-or-x-0117
    v-if="showDialogOrX0117"
    v-bind="orX0117"
    :oneway-model-value="orX0117Oneway"
  ></g-custom-or-x-0117>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814 v-bind="or21814" />
  <!--GUI00617_承認欄登録画面-->
  <g-custom-or-x0135
    v-if="showDialogOrX0135"
    v-bind="orX0135_1"
    v-model="local.orX0135Type"
    :oneway-model-value="localOneway.orX0135Oneway"
    :parent-unique-cp-id="props.uniqueCpId"
  />
</template>
<style>
.or50425_content {
  padding: 0px !important;
}

.or50425_gokeiClass {
  label {
    color: #ffffff;
  }
}
</style>
<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';
:deep(.v-table__wrapper) {
  overflow-x: auto !important;
}

.or50425_table {
  padding: 0px !important;
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.or50425_row {
  margin: 0px !important;
}

.content_center {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.content_center,
.content_center_2 {
  padding: 0px !important;

  .label_left {
    padding-right: 0px;
  }

  .label_right {
    padding-left: 0px;
    padding-right: 0px;
  }

  .printerOption {
    background-color: #edf1f7;
  }

  :deep(.label-area-style) {
    display: none;
  }

  .customCol {
    margin-left: 0px;
    margin-right: 0px;
  }
}
</style>
