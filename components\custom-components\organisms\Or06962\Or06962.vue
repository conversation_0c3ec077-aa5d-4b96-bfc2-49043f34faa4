<script setup lang="ts">
/**
 * Or06962:有機体:アセスメント領域と課題（予防計画書）詳細リスト
 * GUI01093
 *
 * @description
 * アセスメント領域と課題（予防計画書）リストを表示するためのコンポーネント。
 *
 * <AUTHOR>
 */
import { ref, reactive, watch, nextTick, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or06962Const } from '../Or06962/Or06962.constants'
import { Or10883Logic } from '../Or10883/Or10883.logic'
import { Or10883Const } from '../Or10883/Or10883.constants'
import type { Or06962SelectTableDataItem, Or06962SelectType } from './Or06962.type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Or06962OnewayType, Or06962Type } from '~/types/cmn/business/components/Or06962Type'
import { SPACE_FORWARD_SLASH } from '~/constants/classification-constants'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import {
  useScreenEventStatus,
  useScreenStore,
  useScreenTwoWayBind,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import { Or21816Const } from '~/components/base-components/organisms/Or21816/Or21816.constants'
import { Or21816Logic } from '~/components/base-components/organisms/Or21816/Or21816.logic'
import type {
  Or10883OnewayType,
  Or10883TwowayType,
} from '~/types/cmn/business/components/Or10883Type'

const { t } = useI18n()
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
const screenStore = useScreenStore()
/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  onewayModelValue: Or06962OnewayType
}

const props = defineProps<Props>()
/**************************************************
 * Pinia
 **************************************************/
const { setEvent } = useScreenEventStatus<Or06962SelectType>({
  cpId: Or06962Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
})

const { refValue } = useScreenTwoWayBind<Or06962Type>({
  cpId: Or06962Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
})

const defaultOneway = {
  // データ-ページング
  mo00615Oneway: {
    itemLabel: 0 + SPACE_FORWARD_SLASH + 0,
    customClass: {
      outerClass: 'page-label-center',
    },
  } as Mo00615OnewayType,
  // 下アイコンボタン
  mo00009OnewayDown: {
    btnIcon: 'keyboard_arrow_down',
    variant: 'flat',
    color: 'transparent',
    rounded: '0',
    minWidth: '30px',
    minHeight: '30px',
  } as Mo00009OnewayType,
  // 上アイコンボタン
  mo00009OnewayUp: {
    btnIcon: 'keyboard_arrow_up',
    variant: 'flat',
    color: 'transparent',
    rounded: '0',
    minWidth: '30px',
    minHeight: '30px',
  } as Mo00009OnewayType,
  // 背景・原因
  mo00009OnewayBackgroundAndReasons: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 現在の状況
  mo00009OnewayCurrentCondition: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 本人・家族の意欲・意向
  mo00009OnewayDesireAndIntentionOfThePersonAndTheFamily: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
}

/** 単方向バインド用の内部変数 */
const localOneway = reactive({
  Or06962Oneway: props.onewayModelValue,
  mo00615Oneway: {
    ...defaultOneway.mo00615Oneway,
  } as Mo00615OnewayType,
  // 下アイコンボタン
  mo00009OnewayDown: {
    ...defaultOneway.mo00009OnewayDown,
  } as Mo00009OnewayType,
  // 上アイコンボタン
  mo00009OnewayUp: {
    ...defaultOneway.mo00009OnewayUp,
  } as Mo00009OnewayType,
  // 領域における課題(背景・原因)アイコンボタン
  mo00009OnewayBackgroundAndReasons: {
    ...defaultOneway.mo00009OnewayBackgroundAndReasons,
  } as Mo00009OnewayType,
  // 現在の状況アイコンボタン
  mo00009OnewayCurrentCondition: {
    ...defaultOneway.mo00009OnewayCurrentCondition,
  } as Mo00009OnewayType,
  // 本人・家族の意欲・意向アイコンボタン
  mo00009OnewayDesireAndIntentionOfThePersonAndTheFamily: {
    ...defaultOneway.mo00009OnewayDesireAndIntentionOfThePersonAndTheFamily,
  } as Mo00009OnewayType,
  // 詳細ボタン
  mo00611DetailBtnOneWay: {
    btnLabel: t('label.detail'),
  } as Mo00611OnewayType,
  // 入力支援【目標とする生活（１年）】画面
  or10883Oneway: {} as Or10883OnewayType,
})

const local = reactive({
  Or06962: {
    items: [] as Or06962SelectTableDataItem[],
    selectRowIndex: -1,
  } as Or06962Type,
  Or10883Type: {} as Or10883TwowayType,
})

// 選択した行のindex
const selectedItemIndex = ref<number>(-1)

// 選択した行のindex
const isDetail = ref<boolean>(props.onewayModelValue.isDetail)

const headers = ref([
  // 現在の状況
  { title: t('label.current-condition'), align: 'left', key: 'kadaiNo' },
  // 本人・家族の意欲・意向
  {
    title: t('label.desire-and-intention-of-the-person-and-the-family'),
    align: 'left',
    key: 'gutaitekiKnj',
  },
  // 背景・原因
  { title: t('label.background-and-reasons'), align: 'left', key: 'choukiKnj' },
])

const or21816_1 = ref({ uniqueCpId: '' })
const or10883_1 = ref({ uniqueCpId: '' })

const pageComponent = screenStore.screen().supplement.pageComponent

or10883_1.value.uniqueCpId = pageComponent.uniqueCpId

// ダイアログ表示フラグ
const showDialogOr10883 = computed(() => {
  // Or10883のダイアログ開閉状態
  return Or10883Logic.state.get(or10883_1.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21816Const.CP_ID(1)]: or21816_1.value,
  [Or10883Const.CP_ID(1)]: or10883_1.value,
})

// 有機体：行選択上下アイコンボタン
Or21816Logic.state.set({
  uniqueCpId: or21816_1.value.uniqueCpId,
  state: {
    tooltipTextUp: t('tooltip.care-plan2-select-up'),
    tooltipTextDown: t('tooltip.care-plan2-select-next'),
  },
})

/**
 * 行選択
 *
 * @param index - 選択した行のindex
 */
function onSelectRow(index: number) {
  selectedItemIndex.value = index
  local.Or06962.selectRowIndex = selectedItemIndex.value
  refValue.value!.selectRowIndex = local.Or06962.selectRowIndex
}

/**
 * 「詳細ボタン」押下
 */
function setDetail() {
  isDetail.value = !isDetail.value
}

/**
 * 目標とする生活（１年）入力支援アイコンボタン押下
 *
 * @param titlePosition - titlePosition
 */
function openDialog(titlePosition: string) {
  const selectedData = (local.Or06962.items as Or06962SelectTableDataItem[])[
    selectedItemIndex.value
  ]
  if (selectedData) {
    // 共通情報.利用者ID
    localOneway.or10883Oneway.userId = systemCommonsStore.getUserId ?? ''
    // 大分類CD:40
    localOneway.or10883Oneway.t1Cd = '40'
    // 中分類CD:選択行.画面.アセスメント領域のID +1
    localOneway.or10883Oneway.t2Cd = (Number(selectedData.assryoId) + 1).toString()
    // 小分類CD:1/2/3
    localOneway.or10883Oneway.t3Cd = titlePosition
    // タイトル
    // 初期設定マスタの情報.計画表様式は0:A3横1枚、2:A4横2枚の場合、
    if (
      localOneway.Or06962Oneway.itakuKkakPrtFlg === '0' ||
      localOneway.Or06962Oneway.itakuKkakPrtFlg === '2'
    ) {
      // "アセスメント領域と現在の状況：" + 選択行.画面.アセスメント領域
      localOneway.or10883Oneway.title =
        (titlePosition === '1'
          ? Or06962Const.JOKYO_START_WORD.JOKYO_START_WORD_1
          : titlePosition === '2'
            ? Or06962Const.IYOKUIKOU_START_WORD.IYOKUIKOU_START_WORD_1
            : Or06962Const.KADAI_START_WORD.KADAI_START_WORD_1) + setJokyo(selectedData.assryoId)
    }
    // 初期設定マスタの情報.計画表様式は1:A4横3枚のの場合
    else if (localOneway.Or06962Oneway.itakuKkakPrtFlg === '1') {
      // "現在の状況" + 選択行.画面.アセスメント領域
      localOneway.or10883Oneway.title =
        (titlePosition === '1'
          ? Or06962Const.JOKYO_START_WORD.JOKYO_START_WORD_2
          : titlePosition === '2'
            ? Or06962Const.IYOKUIKOU_START_WORD.IYOKUIKOU_START_WORD_1
            : Or06962Const.KADAI_START_WORD.KADAI_START_WORD_2) + setJokyo(selectedData.assryoId)
    }
    // テーブル名:"kyc_tuc_plan12"
    localOneway.or10883Oneway.tableName = Or06962Const.TABLE_NAME
    // カラム名
    localOneway.or10883Oneway.columnName =
      titlePosition === '1'
        ? Or06962Const.COLUMN_NAME_JOKYO
        : titlePosition === '2'
          ? Or06962Const.COLUMN_NAME_IYOKUIKOU
          : Or06962Const.COLUMN_NAME_KADAI
    // 入力値: 画面.アセスメント領域と課題一覧での選択行の「現在状況」/「本人・家族の意欲・意向」/「領域における課題(背景・原因)」
    localOneway.or10883Oneway.inputContents =
      titlePosition === '1'
        ? selectedData.jokyoKnj.value
        : titlePosition === '2'
          ? selectedData.iyokuikouKnj.value
          : selectedData.kadaiKnj.value

    // GUI01109 入力支援【目標とする生活（１年）】画面をポップアップで起動する
    Or10883Logic.state.set({
      uniqueCpId: or10883_1.value.uniqueCpId,
      state: { isOpen: true },
    })
  }
}

/**
 * アセスメント領域
 *
 * @param assryoId  - assryoId
 */
function setJokyo(assryoId: string) {
  let value = ''
  switch (assryoId) {
    // 1:"運動・移動について"
    case '1':
      value = Or06962Const.JOKYO.JOKYO_1
      break
    // 2:"日常生活（家庭生活）について"
    case '2':
      value = Or06962Const.JOKYO.JOKYO_2
      break
    // 3:"社会参加、対人関係・ｺﾐｭﾆｹｰｼｮﾝについて"
    case '3':
      value = Or06962Const.JOKYO.JOKYO_3
      break
    // 4:"健康管理について"
    case '4':
      value = Or06962Const.JOKYO.JOKYO_4
      break
    // 5:"その他の事項について"
    case '5':
      value = Or06962Const.JOKYO.JOKYO_5
      break
    default:
    // 処理なし
  }
  return value
}

/**
 * 行選択上下アイコンボタンを監視
 */
watch(
  () => Or21816Logic.event.get(or21816_1.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.upEventFlg) {
      // 「前の行選択」押下
      preLine()
      Or21816Logic.event.set({
        uniqueCpId: or21816_1.value.uniqueCpId,
        events: { upEventFlg: false },
      })
    }
    if (newValue.downEventFlg) {
      // 「次の行選択」押下
      nextLine()
      Or21816Logic.event.set({
        uniqueCpId: or21816_1.value.uniqueCpId,
        events: { downEventFlg: false },
      })
    }
  }
)

/**
 * 「次の行選択」押下
 */
function nextLine() {
  // 操作区分 = 3:削除の場合
  // 選択した行のフォーカスを一つ下の行を選択する（フォーカスチェンジ）すでに最終行の場合、なにもしない
  if (
    localOneway.Or06962Oneway.operaFlg !== '3' &&
    selectedItemIndex.value + 1 < (local.Or06962.items as Or06962SelectTableDataItem[]).length
  ) {
    selectedItemIndex.value++
    local.Or06962.selectRowIndex = selectedItemIndex.value
    refValue.value!.selectRowIndex = local.Or06962.selectRowIndex
  }
}

/**
 * 「前の行選択」押下
 */
function preLine() {
  // 操作区分 = 3:削除の場合
  // 選択した行のフォーカスを一つ上の行を選択する（フォーカスチェンジ）すでに最初行の場合、なにもしない
  if (localOneway.Or06962Oneway.operaFlg !== '3' && selectedItemIndex.value - 1 >= 0) {
    selectedItemIndex.value--
    local.Or06962.selectRowIndex = selectedItemIndex.value
    refValue.value!.selectRowIndex = local.Or06962.selectRowIndex
  }
}

/**
 * 選択されている項目
 *
 * @param selectCell - 選択されている項目
 *
 * @param index - index
 */
function handleFocus(selectCell: string, index: number) {
  onSelectRow(index)
  setEvent({
    selectCell: selectCell,
  })
}

/**
 * 値変更
 */
const changeTableData = () => {
  refValue.value = local.Or06962
}

onMounted(async () => {
  await nextTick()
  if (localOneway.Or06962Oneway.pagingFlg !== '0') {
    local.Or06962.items = refValue.value?.items ?? []
  }
  // デフォルトで1行目が選択されています
  if (local.Or06962.items.length > 0 && local.Or06962.selectRowIndex < 0) {
    onSelectRow(0)
  }
  // 行選択ラベル
  localOneway.mo00615Oneway.itemLabel =
    ((local.Or06962.items as Or06962SelectTableDataItem[]).length === 0 ? 0 : 1) +
    SPACE_FORWARD_SLASH +
    (local.Or06962.items as Or06962SelectTableDataItem[]).length
})

/**************************************************
 * ウォッチャー
 **************************************************/

/**
 * 表示用「アセスメント領域と課題」リストデータ再取得フラグの監視
 */
watch(
  () => refValue.value?.items,
  (newValue) => {
    if (localOneway.Or06962Oneway.pagingFlg !== '0') {
      local.Or06962.items = newValue ?? []
    } else {
      local.Or06962.items = []
    }
    // デフォルトで1行目が選択されています
    if (local.Or06962.items.length > 0 && local.Or06962.selectRowIndex < 0) {
      onSelectRow(0)
    }
    // 行選択ラベル
    localOneway.mo00615Oneway.itemLabel =
      ((local.Or06962.items as Or06962SelectTableDataItem[]).length === 0 ? 0 : 1) +
      SPACE_FORWARD_SLASH +
      (local.Or06962.items as Or06962SelectTableDataItem[]).length
  }
)

// データ-ページングの監視
watch(
  () => selectedItemIndex.value,
  (newValue) => {
    localOneway.mo00615Oneway.itemLabel =
      newValue +
      1 +
      SPACE_FORWARD_SLASH +
      (local.Or06962.items as Or06962SelectTableDataItem[]).length
  }
)

/**
 * アセスメント領域と課題一覧は展開の監視
 */
watch(
  () => props.onewayModelValue.isDetail,
  (newValue) => {
    isDetail.value = newValue
  }
)

/**
 * 入力支援【目標とする生活（１年）】は展開の監視
 */
watch(
  () => local.Or10883Type,
  (newValue) => {
    if (newValue) {
      const keyOfTab2Data = removeUnderscoreAndCapitalize(
        localOneway.or10883Oneway.columnName ?? ''
      ) as 'jokyoKnj' | 'iyokuikouKnj' | 'kadaiKnj'
      if (keyOfTab2Data) {
        local.Or06962.items[selectedItemIndex.value][keyOfTab2Data] = {
          value: newValue.naiyo ?? '',
        }
        refValue.value = local.Or06962
      }
    }
  }
)

/**
 *
 */
watch(
  () => localOneway.Or06962Oneway.pagingFlg,
  (newValue) => {
    if (newValue !== '0') {
      local.Or06962.items = refValue.value?.items ?? []
    } else {
      local.Or06962.items = []
    }
    // デフォルトで1行目が選択されています
    if (local.Or06962.items.length > 0 && local.Or06962.selectRowIndex < 0) {
      onSelectRow(0)
    }

    // 行選択ラベル
    localOneway.mo00615Oneway.itemLabel =
      ((local.Or06962.items as Or06962SelectTableDataItem[]).length === 0 ? 0 : 1) +
      SPACE_FORWARD_SLASH +
      (local.Or06962.items as Or06962SelectTableDataItem[]).length
  }
)

/**
 * 文字列中のアンダースコア `_` を削除し、アンダースコアの直後の文字を大文字に変換する（キャメルケース変換）
 *
 * @param input - アンダースコアを含む可能性のある入力文字列
 *
 * @returns 変換後のキャメルケース形式の文字列
 */
const removeUnderscoreAndCapitalize = (input: string): string => {
  // アンダースコアで分割して配列に変換
  return (
    input
      .split('_')
      // 空文字列を除去（先頭や連続するアンダースコアを処理）
      .filter((part) => part !== '')
      .map((part, index) => {
        if (index === 0) {
          // 最初の単語は小文字のまま（先頭のアンダースコアを無視）
          return part.toLowerCase()
        } else {
          // 2番目以降の単語は頭文字を大文字に変換
          return part.charAt(0).toUpperCase() + part.slice(1).toLowerCase()
        }
      })
      // 配列を結合して最終的な文字列を生成
      .join('')
  )
}
</script>

<template>
  <c-v-row
    v-if="!localOneway.Or06962Oneway.isCopyMode"
    class="ma-0 mt-2"
    :class="isDetail ? '' : 'width-large'"
  >
    <div class="flex-col">
      <div class="line3-flex-box">
        <div>
          <!-- 詳細ボタン Mo00611 -->
          <base-mo00611
            v-if="localOneway.Or06962Oneway.isDetail"
            v-bind="localOneway.mo00611DetailBtnOneWay"
            @click="setDetail"
          />
        </div>
      </div>
      <div class="line3-flex-box">
        <!--行選択ラベル-->
        <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway" />
        <!--行選択上下アイコンボタン-->
        <g-base-or21816 v-bind="or21816_1" />
      </div>
    </div>
  </c-v-row>
  <!-- アセスメント領域と課題 -->
  <c-v-row
    class="ma-0"
    :class="isDetail ? '' : 'width-large'"
  >
    <c-v-col
      cols="auto"
      class="pa-0 mt-2"
    >
      <c-v-data-table
        :headers="headers"
        class="table-wrapper overflow-x-auto border-none"
        hide-default-footer
        :items-per-page="-1"
        :items="local.Or06962.items"
        :max-height="600"
        fixed-header
        return-object
      >
        <!-- ヘッダ -->
        <template #headers>
          <tr>
            <!-- 現在の状況 -->
            <th
              rowspan="2"
              :colspan="isDetail ? '2' : '10'"
              :class="isDetail ? 'width-80' : 'width-320'"
            >
              <div class="thead-with-iconbtn">
                <div v-if="isDetail">
                  {{ t('label.current-condition-detail') }}
                </div>
                <div v-if="!isDetail && localOneway.Or06962Oneway.itakuKkakPrtFlg === '1'">
                  {{ t('label.current-condition') }}
                </div>
                <div v-if="!isDetail && localOneway.Or06962Oneway.itakuKkakPrtFlg !== '1'">
                  {{ t('label.current-condition-itakuKkakPrt') }}
                </div>
                <div
                  v-if="!isDetail && !localOneway.Or06962Oneway.isCopyMode"
                  class="divider-div"
                >
                  <c-v-divider
                    vertical
                    inset
                  />
                  <base-mo00009
                    :oneway-model-value="localOneway.mo00009OnewayCurrentCondition"
                    @click="openDialog('1')"
                  />
                </div>
              </div>
            </th>
            <!-- 本人・家族の意欲・意向 -->
            <th
              v-if="!isDetail"
              rowspan="2"
              class="width-180"
            >
              <div class="thead-with-iconbtn">
                <div>{{ t('label.desire-and-intention-of-the-person-and-the-family') }}</div>
                <div
                  v-if="!localOneway.Or06962Oneway.isCopyMode"
                  class="divider-div"
                >
                  <c-v-divider
                    vertical
                    inset
                  />
                  <base-mo00009
                    :oneway-model-value="
                      localOneway.mo00009OnewayDesireAndIntentionOfThePersonAndTheFamily
                    "
                    @click="openDialog('2')"
                  />
                </div>
              </div>
            </th>
            <!-- 背景・原因 -->
            <th
              rowspan="1"
              class="width-190"
            >
              <div class="thead-with-iconbtn">
                <div v-if="localOneway.Or06962Oneway.itakuKkakPrtFlg !== '1'">
                  {{ t('label.background-and-reasons-itakuKkakPrt') }}
                </div>
                <div v-if="localOneway.Or06962Oneway.itakuKkakPrtFlg === '1'">
                  {{ t('label.background-and-reasons') }}
                </div>
                <div
                  v-if="!localOneway.Or06962Oneway.isCopyMode"
                  class="divider-div"
                >
                  <c-v-divider
                    vertical
                    inset
                  />
                  <base-mo00009
                    :oneway-model-value="localOneway.mo00009OnewayBackgroundAndReasons"
                    @click="openDialog('3')"
                  />
                </div>
              </div>
            </th>
          </tr>
        </template>
        <!-- BODY -->
        <template #item="{ item, index }">
          <tr
            :class="{ 'select-row': selectedItemIndex === index }"
            @click="onSelectRow(index)"
          >
            <!-- 現在の状況 -->
            <td
              rowspan="2"
              :colspan="isDetail ? '2' : '3'"
              class="horizontal-table label-center"
              :class="isDetail ? '' : 'jokyo-text'"
            >
              <base-mo00615
                :oneway-model-value="
                  {
                    itemLabel: setJokyo(item.assryoId),
                  } as Mo00615OnewayType
                "
                :class="`jokyo-label`"
              />
            </td>
            <!-- 現在の状況テキストエリア -->
            <td
              v-if="!isDetail"
              rowspan="2"
              colspan="7"
              class="horizontal-table width-large border-left"
            >
              <base-mo01280
                v-model="item.jokyoKnj"
                max-length="4000"
                :readonly="localOneway.Or06962Oneway.isCopyMode"
                @click.stop="handleFocus(Or06962Const.COLUMN_NAME_JOKYO_KEY, index)"
                @change="changeTableData()"
              ></base-mo01280>
            </td>
            <!-- 本人・家族の意欲・意向 -->
            <td
              v-if="!isDetail"
              rowspan="2"
            >
              <base-mo01280
                v-model="item.iyokuikouKnj"
                max-length="4000"
                :readonly="localOneway.Or06962Oneway.isCopyMode"
                @click.stop="handleFocus(Or06962Const.COLUMN_NAME_IYOKUIKOU_KEY, index)"
                @change="changeTableData()"
              ></base-mo01280>
            </td>
            <!-- 領域における課題（背景・原因） -->
            <td rowspan="1">
              <base-mo00039
                v-if="item"
                :oneway-model-value="
                  {
                    showItemLabel: false,
                    hideDetails: true,
                  } as Mo00039OnewayType
                "
                :show-item-label="false"
                :class="`radio-btn`"
                @change="changeTableData()"
              >
                <base-at-radio-group
                  v-model="item.kadaiFlg"
                  :readonly="localOneway.Or06962Oneway.isCopyMode"
                  :inline="true"
                >
                  <base-at-radio
                    v-for="i in Or06962Const.RADIO_LIST"
                    :key="i.value"
                    v-model="item.kadaiFlg"
                    style="width: 75px"
                    class="mr-0 !important"
                    :name="i.label"
                    :radio-label="i.label"
                    :value="i.value"
                  />
                </base-at-radio-group>
              </base-mo00039>
            </td>
          </tr>
          <tr
            :class="{ 'select-row': selectedItemIndex === index }"
            @click="onSelectRow(index)"
          >
            <td>
              <base-mo01280
                v-model="item.kadaiKnj"
                max-length="4000"
                :readonly="localOneway.Or06962Oneway.isCopyMode"
                @click.stop="handleFocus(Or06962Const.COLUMN_NAME_KADAI_KEY, index)"
                @change="changeTableData()"
              ></base-mo01280>
            </td>
          </tr>
        </template>
      </c-v-data-table>
    </c-v-col>
  </c-v-row>
  <!--GUI01109 入力支援【目標とする生活（１年）】画面-->
  <g-custom-or-10883
    v-if="showDialogOr10883"
    v-bind="or10883_1"
    v-model="local.Or10883Type"
    :oneway-model-value="localOneway.or10883Oneway"
  />
</template>

<style scoped lang="scss">
@use '@/styles/base.scss';
// 選択した行のCSS
.select-row {
  background: #dbeefe;
}
.table-wrapper :deep(.v-table__wrapper th) {
  background-color: rgb(var(--v-theme-black-100)) !important;
  border-top: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-left: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-right: 1px rgb(var(--v-theme-black-200)) solid !important;
  bottom: 1px;
  position: relative;
  font-size: 14px;
  font-weight: bold;
  white-space: break-spaces;
}
.table-wrapper .v-table__wrapper td {
  border: 1px rgb(var(--v-theme-black-200)) solid;
  padding: 0;
  font-size: 14px;
}
:deep(.v-table--fixed-header > .v-table__wrapper > table > thead) {
  position: sticky;
  top: 0.5px !important;
  z-index: 2;
}
.thead-with-iconbtn {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.divider-div {
  display: flex;
}
.width-small {
  width: 35%;
}
.width-large {
  width: 100%;
}
// 最小幅: 40px
.width-40 {
  width: 40px;
}
// 最小幅: 50px
.width-50 {
  width: 50px;
}
// 最小幅: 80px
.width-80 {
  width: 80px;
}
// 最小幅: 180px
.width-180 {
  width: 180px;
}
// 最小幅: 190px
.width-190 {
  width: 190px;
}
// 最小幅: 320px
.width-320 {
  width: 320px;
}
:deep(.input-wrapper.full-height-cell) {
  height: 40px !important;
}
// col flex
.flex-col {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  flex-shrink: 0;
}
.line3-flex-box {
  display: flex;
  column-gap: 8px;
  align-items: center;
  flex-shrink: 0;
  flex-wrap: wrap;
}
.page-label-center {
  display: flex;
  align-items: center;
  background: transparent !important;
}
:deep(.table-wrapper .v-table__wrapper table) {
  table-layout: fixed;
}
:deep(.table-container) {
  :deep(div:nth-child(2) > .v-row) {
    margin: 0px !important;
    .flex-col {
      padding: 0px !important;
    }
  }
  :deep(div:nth-child(2) > div:nth-child(2)) {
    margin: 8px 0px !important;
  }
}
:deep(.table-checkbox > div) {
  display: flex;
  align-items: center;
  justify-content: center;
}
.inner-table {
  height: 100%;
  width: 100%;
  border-collapse: collapse;
  border: 1px rgb(var(--v-theme-black-200)) solid !important;
}
.horizontal-table {
  height: 100%;
  border-collapse: collapse;
}
.inner-table td {
  padding: 4px;
}
.border-bottom {
  border-bottom: 2px rgb(var(--v-theme-black-200)) solid !important;
}
.jokyo-text {
  border-right: 1px rgb(var(--v-theme-black-200)) solid !important;
}
.border-top {
  border-top: 1px rgb(var(--v-theme-black-200)) solid !important;
}
.border-left {
  border-left: 1px rgb(var(--v-theme-black-200)) solid !important;
}
:deep(.table-header td) {
  border-color: rgb(var(--v-theme-black-100)) !important;
  font-size: 14px;
  table-layout: fixed;
}
.table-header td {
  vertical-align: top;
}
.table-cell {
  align-content: center;
}
.jokyo-label {
  font-weight: normal;
  min-width: 80px;
  max-width: 80px;
  width: 80px;
  white-space: pre-wrap !important;
  word-wrap: break-word;
  background-color: transparent !important;
  align-content: center;
  margin: 0 16px;
}
.radio-btn {
  background-color: transparent !important;
  margin-top: 6px !important;
}
.label-center {
  display: table-cell;
  align-content: center;
}
.table-spacing {
  border-spacing: 0;
  height: 100%;
}
:deep(.v-radio-group .v-input__details) {
  display: none;
}
</style>
