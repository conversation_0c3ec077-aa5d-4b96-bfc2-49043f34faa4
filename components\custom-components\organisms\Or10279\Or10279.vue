<!-- eslint-disable no-undef -->
<script setup lang="ts">
/**
 * Or10279:有機体:アセスメント（インターライ）CSV出力
 * GUI00788_［アセスメント（インターライ）CSV出力］画面
 *
 * @description
 * アセスメント（インターライ）CSV出力
 *
 * <AUTHOR>
 */
import { onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or10279Const } from './Or10279.constants'
import type { ApplicableSvJigyoInfo, Or10279StateType } from './Or10279.type'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import { Or10280Const } from '~/components/custom-components/organisms/Or10280/Or10280.constants'
import { Or10280Logic } from '~/components/custom-components/organisms/Or10280/Or10280.logic'
import type { Or10280OnewayType } from '~/types/cmn/business/components/Or10280Type'
import { computed, dateUtils, useScreenOneWayBind, useSetupChildProps } from '#imports'
import type { Or10279OneWayType } from '~/types/cmn/business/components/Or10279Type'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00020Type, Mo00020OnewayType } from '~/types/business/components/Mo00020Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Mo01343OnewayType } from '~/types/business/components/Mo01343Type'
import type { Mo00039OnewayType } from '~/types/cmn/business/components/Mo00039Type'
import type { Mo01334OnewayType, Mo01334Type } from '~/types/business/components/Mo01334Type'
import type { CustomClass } from '~/types/CustomClassType'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  SvJigyoInfo,
  IAssessmentInterRAICsvOutputInitInEntity,
  IAssessmentInterRAICsvOutputInitOutEntity,
} from '~/repositories/cmn/entities/AssessmentInterRAICsvOutputInitEntity'
import type {
  IAssessmentInterRAICsvOutputListInEntity,
  IAssessmentInterRAICsvOutputListOutEntity,
} from '~/repositories/cmn/entities/AssessmentInterRAICsvOutputListEntity'
import type { IAssessmentInterRAICsvOutputSaveInEntity } from '~/repositories/cmn/entities/AssessmentInterRAICsvOutputSaveEntity'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { ResBodyStatusCode } from '~/constants/api-constants'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { useJigyoList } from '~/utils/useJigyoList'

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or10279OneWayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()
const or10280 = ref({ uniqueCpId: Or10280Const.CP_ID(1) })
// 事業所入力
const or41179 = ref({ uniqueCpId: Or41179Const.CP_ID(0) })

// ダイアログ表示フラグ
const showDialogOr10280 = computed(() => {
  // Or10280のダイアログ開閉状態
  return Or10280Logic.state.get(or10280.value.uniqueCpId)?.isOpen ?? false
})

// システム共有情報取得
const systemCommonsStore = useSystemCommonsStore()
const { jigyoListWatch } = useJigyoList()

// 共通情報
const commonInfoData = reactive({
  // 共通情報.適用事業所ＩＤリスト取得
  svJigyoIdList: [] as string[],
})

const localOneway = reactive({
  or10279: {
    ...props.onewayModelValue,
  },

  // 期間
  mo00615PeriodOnewayType: {
    itemLabel: t('label.period'),
    showItemLabel: true,
    showRequiredLabel: true,
    customClass: { outerClass: '' },
  } as Mo00615OnewayType,
  // 期間の入力支援
  mo00009PeriodInputSupportOnewayType: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,
  // 期間の入力支援用カレンダ
  mo01343Oneway: {} as Mo01343OnewayType,
  // 開始期間: 表用テキストフィールド
  mo00020FirstDateInputOneWay: {
    showItemLabel: false,
    showSelectArrow: true,
    width: '132px',
    totalWidth: '204px',
  } as Mo00020OnewayType,
  // 終了期間: 表用テキストフィールド
  mo00020LastDateInputOneWay: {
    showItemLabel: false,
    showSelectArrow: true,
    width: '132px',
    totalWidth: '204px',
  } as Mo00020OnewayType,
  // ～ラベル
  mo01338TildeOnewayType: {
    value: t('label.wave-dash'),
    customClass: {
      outerClass: 'waveDashOuter',
      labelClass: 'waveDashLabel',
      itemClass: 'waveDashItem',
    } as CustomClass,
  } as Mo01338OnewayType,
  // 集計ボタン
  mo00611SummaryOnewayType: {
    btnLabel: t('btn.summary'),
    tooltipText: t('tooltip.summary'),
  } as Mo00611OnewayType,
  // ファイル名ラベル
  mo00615FileNameOnewayType: {
    itemLabel: t('label.file-name'),
    showItemLabel: true,
    showRequiredLabel: false,
  } as Mo00615OnewayType,
  // ファイル名
  mo01338FileNameOnewayType: {
    value: '',
    customClass: { labelClass: '' },
  } as Mo01338OnewayType,
  // 区切り文字ラベル
  mo00615DelimiterOnewayType: {
    itemLabel: t('label.delimiter'),
    showItemLabel: true,
    showRequiredLabel: false,
    itemLabelFontWeight: 'bold',
    customClass: {
      outerClass: 'radio-label1',
    } as CustomClass,
  } as Mo00615OnewayType,
  // 区切り文字
  mo00039DelimiterOnewayType: {
    name: t('label.delimiter'),
    showItemLabel: false,
    inline: true,
    customClass: {
      outerClass: 'radio-item1 d-flex align-center',
    } as CustomClass,
  } as Mo00039OnewayType,
  // 利用者ラベル
  mo00615UserOnewayType: {
    itemLabel: t('label.user'),
    itemLabelFontWeight: 'bold',
    customClass: {
      outerClass: 'radio-label2',
    } as CustomClass,
  } as Mo00615OnewayType,
  //  利用者
  mo00039UserOnewayType: {
    name: t('label.user'),
    itemLabel: '',
    showItemLabel: false,
    inline: true,
    customClass: {
      outerClass: 'radio-item2 d-flex align-center',
    } as CustomClass,
  } as Mo00039OnewayType,
  // 件数ラベル
  mo00615ItemsLabelOnewayType: {
    itemLabel: t('label.items'),
    showItemLabel: true,
    showRequiredLabel: false,
    customClass: {
      itemStyle: 'padding: 0 !important',
    } as CustomClass,
  } as Mo00615OnewayType,
  // 件数
  mo00615ItemsOnewayType: {
    itemLabel: '',
    customClass: {
      outerClass: 'ml-2',
      itemStyle: 'padding: 0 !important',
    } as CustomClass,
  } as Mo00615OnewayType,
  // 分子：一覧
  mo01334Oneway: {
    headers: Or10279Const.DEFAULT.INIT_HEADERS,
    height: 438,
    items: [],
  } as Mo01334OnewayType,
  // 閉じるコンポーネント
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 確定コンポーネント
  mo00609ConfirmOneway: {
    btnLabel: t('btn.export'),
  } as Mo00609OnewayType,
  // CSV出力ダイアログ
  mo00024Oneway: {
    width: '1216px',
    height: '801px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or10279',
      toolbarTitle: t('label.assessment-interRAI-csv-output'),
      toolbarName: 'Or10279ToolBar',
      font: '24px',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
      cardTextClass: 'pa-2 pt-0',
    },
  } as Mo00024OnewayType,
})

const local = reactive({
  // 期間の入力支援用カレンダ
  mo01343: {
    value: '',
    mo00024: { isOpen: false },
  },
  // 開始期間
  firstDate: { value: '' } as Mo00020Type,
  // 終了期間
  lastDate: { value: '' } as Mo00020Type,
  // 区切り文字
  mo00039DelimiterModelValue: '0',
  // 利用者
  mo00039UserModelValue: '0',
  // 事業者ID
  svJigyoId: '',
  /** 選択行データ設定 */
  mo01334: {
    value: '',
    values: [],
  } as Mo01334Type,
})

const respData = reactive({
  // 事業所情報リスト
  svJigyoInfoList: [] as SvJigyoInfo[],
  // 適用事業所情報リスト
  applicableSvJigyoInfoList: [] as ApplicableSvJigyoInfo[],
})

const isLoading = ref(false)

/**************************************************
 * コンポーネント固有処理（Or10280のダイアログ）
 **************************************************/
const or10280Oneway = ref<Or10280OnewayType>({
  assessmentInterRaiCsvOutput: {
    csvOutputList: [],
  },
})

/**
 *  ボタン押下時の処理
 */
function or10280OnClick() {
  or10280Oneway.value.assessmentInterRaiCsvOutput.csvOutputList = []
  for (let i = 0; i < 10; i++) {
    or10280Oneway.value.assessmentInterRaiCsvOutput.csvOutputList.push({
      /** 事業所名 */
      officeName: t('label.office-title') + i,
      /** ファイル名 */
      fileName: t('label.file-name') + i,
      /** 結果 */
      result: t('label.result') + i,
    })
  }
  // Or10280のダイアログ開閉状態を更新する
  Or10280Logic.state.set({
    uniqueCpId: or10280.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**************************************************
 * Emit
 **************************************************/
/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or10279Const.DEFAULT.IS_OPEN,
})
// 期間設定用
const periodDate1 = ref('')
const periodDate2 = ref('')
// 区切り文字_リスト
const delimiterList = ref<CodeType[]>([])
// 利用者種別_リスト
const userKindList = ref<CodeType[]>([])
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or10279StateType>({
  cpId: Or10279Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or10279Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// ★事業所選択監視関数を実行
jigyoListWatch(or41179.value.uniqueCpId, callbackFuncJigyo)

/**
 * 事業所選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 *  @param newJigyoId - 変更後の事業者ID
 */
function callbackFuncJigyo(newJigyoId: string) {
  // CSV出力リストをクリア
  clearCsvOutputList()
  if (newJigyoId !== '') {
    const jigyoInfoList = Or41179Logic.state.get(or41179.value.uniqueCpId)?.jigyoInfoList
    const jigyoInfoData = jigyoInfoList?.find((jigyoInfo) => jigyoInfo.svJigyoId === newJigyoId)
    if (jigyoInfoData) {
      local.svJigyoId = jigyoInfoData.svJigyoId
      localOneway.mo01338FileNameOnewayType.value =
        'interRAI_' + jigyoInfoData.jigyoRyakuKnj + '_' + localOneway.or10279.baseDate
    }
  }
}

const { convertDateToSeireki } = dateUtils()
onMounted(() => {
  // 事業所選択リストを初期化
  Or41179Logic.state.set({
    uniqueCpId: or41179.value.uniqueCpId,
    state: {
      searchCriteria: {
        selfId: systemCommonsStore.getUserSelectSelfId(),
      },
    },
  })
  init()
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or41179Const.CP_ID(1)]: or41179.value,
})

function init() {
  // 汎用コードマスタデータを取得し初期化
  void initCodes()
  commonInfoData.svJigyoIdList = systemCommonsStore.getSvJigyoIdList.map((item) => {
    return item
  })
  // アセスメント（インターライ）CSV出力の初期情報を取得する
  void getInitData()
  const currentDate = new Date() // 現在の日付を取得する
  const currentYear = currentDate.getFullYear() // 現在の年を取得する
  const currentMonth = currentDate.getMonth() // 現在の月（0-11）を取得する
  const firstDate = new Date(currentYear, currentMonth, 1) // 現在の月の1日
  const lastDate = new Date(currentYear, currentMonth + 1, 0) // 現在の月の最後の日
  // 開始期間
  local.firstDate.value = convertDateToSeireki(firstDate)
  // 終了期間
  local.lastDate.value = convertDateToSeireki(lastDate)
  // 親画面.事業所ID対応する事業所の事業名（略称）を設定
  Or41179Logic.data.set({
    uniqueCpId: or41179.value.uniqueCpId,
    value: {
      modelValue: localOneway.or10279.officeId,
    },
  })

  // CSV出力リストをクリア
  clearCsvOutputList()
}

/**
 * アセスメント（インターライ）CSV出力の初期情報を取得
 */
const getInitData = async () => {
  const inputData: IAssessmentInterRAICsvOutputInitInEntity = {
    // 事業所IDリスト
    svJigyoIdList: commonInfoData.svJigyoIdList,
    // 職員ＩＤ
    shokuinId: localOneway.or10279.staffId,
    // 画面名
    kinouName: t('label.assessment-interRAI-csv-output'),
  }
  // バックエンドAPIから初期情報取得
  const assessmentInterRAICsvOutputResp: IAssessmentInterRAICsvOutputInitOutEntity =
    await ScreenRepository.select('assessmentInterRAICsvOutputInitSelect', inputData)
  if (
    ResBodyStatusCode.SUCCESS === assessmentInterRAICsvOutputResp.statusCode &&
    assessmentInterRAICsvOutputResp.data
  ) {
    respData.svJigyoInfoList = assessmentInterRAICsvOutputResp.data.svJigyoInfoList
    respData.applicableSvJigyoInfoList = []
    respData.svJigyoInfoList.forEach((item) => {
      const data = {
        // 事業所ID
        svJigyoId: item.svJigyoId,
        // 事業名
        jigyoKnj: item.jigyoKnj,
        // 事業名（略称）
        jigyoRyakuKnj: item.svJigyoId,
      } as ApplicableSvJigyoInfo
      respData.applicableSvJigyoInfoList.push(data)
    })
    // 区切り区分を設定
    local.mo00039DelimiterModelValue = assessmentInterRAICsvOutputResp.data.kugiri
  }
}

/**
 * CSV出力リストをクリア
 */
function clearCsvOutputList() {
  localOneway.mo01334Oneway.items = []
  localOneway.mo00615ItemsOnewayType.itemLabel =
    localOneway.mo01334Oneway.items.length + ' ' + t('label.item')
}

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 区切り文字
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DELIMITER },
    // 利用者種別
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_USER_KIND },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  // 区切り文字
  delimiterList.value = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_DELIMITER)
  // 利用者種別
  userKindList.value = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_USER_KIND)
}

/**
 * 「集計」ボタン押下
 */
function summary() {
  // 画面.開始期間日＞画面.終了期間日の場合
  if (local.firstDate.value > local.lastDate.value) {
    // 処理終了する
    return
  }
  isLoading.value = true
  // CSV出力リストをクリア
  clearCsvOutputList()
  void getCsvOutputInfo()
}

/**
 * CSV出力情報取得
 */
async function getCsvOutputInfo() {
  const inputData: IAssessmentInterRAICsvOutputListInEntity = {
    // 事業所ID
    svJigyoId: local.svJigyoId,
    // 期間開始日
    startYmd: local.firstDate.value,
    // 期間終了日
    endYmd: local.lastDate.value,
    // 利用者条件
    userConditions: local.mo00039UserModelValue,
    // 基準日
    asYmd: localOneway.or10279.baseDate,
  }
  const ret: IAssessmentInterRAICsvOutputListOutEntity = await ScreenRepository.select(
    'assessmentInterRAICsvOutputListSelect',
    inputData
  )
  // 取得したCSV出力データをCSV出力一覧に設定する。
  if (
    ResBodyStatusCode.SUCCESS === ret.statusCode &&
    ret.data.syukeiInfoList &&
    ret.data.syukeiInfoList.length > 0
  ) {
    const dataList = ret.data.syukeiInfoList
    // データ情報設定
    let count = 0
    localOneway.mo01334Oneway.items = dataList.map((item) => {
      return {
        id: ++count + '',
        ...item,
      }
    })
    localOneway.mo00615ItemsOnewayType.itemLabel =
      localOneway.mo01334Oneway.items.length + ' ' + t('label.item')
    local.mo01334.value = '1'
  }

  isLoading.value = false
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      close()
    }
  }
)

/**
 * 期間の入力支援をクリック処理
 *
 */
function openCalendar() {
  local.mo01343.mo00024.isOpen = true
}

/**
 * 期間取得
 *
 */
function getPeriod() {
  if (periodDate1.value <= periodDate2.value) {
    local.firstDate.value = periodDate1.value
    local.lastDate.value = periodDate2.value
  } else {
    local.firstDate.value = periodDate2.value
    local.lastDate.value = periodDate1.value
  }
  periodDate1.value = ''
  periodDate2.value = ''
}

/**
 * 期間の入力支援の値変更を監視
 *
 */
watch(
  () => local.mo01343.value,
  (newValue) => {
    // 期間を設定
    if (periodDate1.value === '') {
      periodDate1.value = newValue
      openCalendar()
    } else {
      periodDate2.value = newValue
      getPeriod()
    }
  }
)

/**
 * 開始期間の値変更を監視
 *
 */
watch(
  () => local.firstDate.value,
  () => {
    // CSV出力リストをクリア
    clearCsvOutputList()
  }
)

/**
 * 終了期間の値変更を監視
 *
 */
watch(
  () => local.lastDate.value,
  () => {
    // CSV出力リストをクリア
    clearCsvOutputList()
  }
)

/**
 *利用者の値変更を監視
 *
 */
watch(
  () => local.mo00039UserModelValue,
  () => {
    // CSV出力リストをクリア
    clearCsvOutputList()
  }
)

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  // api利用し、区切り文字情報を保存する。
  //更新データ作成
  const inputData: IAssessmentInterRAICsvOutputSaveInEntity = {
    // 職員ＩＤ
    shokuinId: localOneway.or10279.staffId,
    // 画面名
    kinouName: t('label.assessment-interRAI-csv-output'),
    // 区切り
    kugiri: local.mo00039DelimiterModelValue,
  }
  void ScreenRepository.update('assessmentInterRAICsvOutputSave', inputData)
  setState({ isOpen: false })
}

/**
 * CSV出力結果を表示
 *
 */
function showCsvOutputResult() {
  // CSV出力結果画面を開く
  or10280OnClick()
}

/**
 * 「出力」ボタン押下
 *
 */
function csvOutput() {
  // 画面.開始期間日＞画面.終了期間日の場合
  if (local.firstDate.value > local.lastDate.value) {
    // 処理終了する
    return
  }
  showCsvOutputResult()
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <v-overlay
        :model-value="isLoading"
        :persistent="false"
        class="align-center justify-center"
        ><v-progress-circular
          indeterminate
          color="primary"
        ></v-progress-circular
      ></v-overlay>
      <c-v-row class="ma-0">
        <c-v-col class="pa-0">
          <c-v-row no-gutters>
            <!-- 期間 -->
            <base-mo00615 :oneway-model-value="localOneway.mo00615PeriodOnewayType" />
            <!-- 期間の入力支援 -->
            <base-mo00009
              :oneway-model-value="localOneway.mo00009PeriodInputSupportOnewayType"
              @click="openCalendar"
            >
              <base-mo01343
                v-model="local.mo01343"
                :oneway-model-value="localOneway.mo01343Oneway"
              />
            </base-mo00009>
          </c-v-row>
          <c-v-row
            no-gutters
            class="d-flex align-center mb-2"
          >
            <!-- 開始期間 -->
            <base-mo00020
              v-model="local.firstDate"
              :oneway-model-value="localOneway.mo00020FirstDateInputOneWay"
            />
            <!-- ～ラベル -->
            <base-mo01338 :oneway-model-value="localOneway.mo01338TildeOnewayType" />
            <!-- 終了期間 -->
            <base-mo00020
              v-model="local.lastDate"
              :oneway-model-value="localOneway.mo00020LastDateInputOneWay"
            />
            <!-- 集計ボタン -->
            <base-mo00611
              :oneway-model-value="localOneway.mo00611SummaryOnewayType"
              @click.stop="summary"
            />
          </c-v-row>
          <c-v-divider class="mx--2" />
          <c-v-row
            no-gutters
            class="my-2"
          >
            <!-- ファイル名ラベル -->
            <base-mo00615 :oneway-model-value="localOneway.mo00615FileNameOnewayType" />
            <!-- ファイル名 -->
            <base-mo01338 :oneway-model-value="localOneway.mo01338FileNameOnewayType" />
          </c-v-row>
          <c-v-row
            no-gutters
            class="mb-2"
          >
            <!-- 区切り文字ラベル -->
            <base-mo00615 :oneway-model-value="localOneway.mo00615DelimiterOnewayType" />
            <!-- 区切り文字 -->
            <base-mo00039
              v-model="local.mo00039DelimiterModelValue"
              :oneway-model-value="localOneway.mo00039DelimiterOnewayType"
            >
              <base-at-radio
                v-for="(item, index) in delimiterList"
                :key="'or10279-' + index"
                :name="'or10279-radio-' + index"
                :radio-label="item.label"
                :value="item.value"
                style="width: 200px"
              />
            </base-mo00039>
          </c-v-row>
          <c-v-divider class="mx--2" />
          <c-v-row
            no-gutters
            class="d-flex align-center my-2"
          >
            <div style="width: auto">
              <!-- 事業所 -->
              <g-base-or-41179 v-bind="or41179" />
            </div>
            <!-- 利用者ラベル -->
            <base-mo00615 :oneway-model-value="localOneway.mo00615UserOnewayType" />
            <!-- 利用者 -->
            <base-mo00039
              v-model="local.mo00039UserModelValue"
              :oneway-model-value="localOneway.mo00039UserOnewayType"
            >
              <base-at-radio
                v-for="(item, index) in userKindList"
                :key="'or10279-' + index"
                :name="'or10279-radio-' + index"
                :radio-label="item.label"
                :value="item.value"
                style="width: 136px"
              />
            </base-mo00039>
          </c-v-row>
          <c-v-divider class="mx--2" />
          <c-v-row
            class="my-2"
            no-gutters
          >
            <c-v-spacer />
            <!-- 件数ラベル -->
            <base-mo00615 :oneway-model-value="localOneway.mo00615ItemsLabelOnewayType" />
            <!-- 件数 -->
            <base-mo00615 :oneway-model-value="localOneway.mo00615ItemsOnewayType" />
          </c-v-row>
          <c-v-row no-gutters>
            <c-v-col
              cols="12"
              class="table-header"
            >
              <base-mo01334
                :model-value="local.mo01334"
                class="list-wrapper"
                hide-default-footer
                :oneway-model-value="localOneway.mo01334Oneway"
              >
              </base-mo01334>
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="localOneway.mo00611CloseBtnOneWay"
          @click="close"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.close-btn')"
          ></c-v-tooltip>
        </base-mo00611>
        <!-- 出力ボタン Mo00609 -->
        <base-mo00609
          v-bind="localOneway.mo00609ConfirmOneway"
          class="ml-2"
          @click="csvOutput"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.output-btn')"
          ></c-v-tooltip>
        </base-mo00609>
        <g-custom-or-10280
          v-if="showDialogOr10280"
          v-bind="or10280"
          :oneway-model-value="or10280Oneway"
        />
      </c-v-row>
    </template>
  </base-mo00024>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';

.table-header {
  :deep(th) {
    padding: 0 8px !important;
  }
  :deep(td) {
    padding: 0 8px !important;
  }
  :deep(.v-data-table-rows-no-data) {
    text-align: left !important;
    td {
      padding-left: 1% !important;
    }
  }
}

.mx--2 {
  margin: 0 -8px;
}
:deep(.radio-group) {
  margin-top: 0 !important;
}

.radio-label1 {
  border: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  border-right: none;
  background: rgb(var(--v-theme-blue-50)) !important;
  width: 160px;
  height: 36px;
  padding: 8px;
}

.radio-item1 {
  border: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  width: 440px;
  height: 36px;
}

.radio-label2 {
  border: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  border-right: none;
  background: rgb(var(--v-theme-blue-50)) !important;
  width: 160px;
  height: 36px;
  padding: 8px;
}

.radio-item2 {
  border: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  width: 440px;
  height: 36px;
}

.waveDashOuter :deep(.v-col) {
  padding: 0 !important;
}

:deep(.waveDashLabel) {
  display: none;
}
</style>
