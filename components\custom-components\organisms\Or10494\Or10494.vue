<script setup lang="ts">
import { computed, nextTick, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or27616Const } from '../Or27616/Or27616.constants'
import { Or12336Const } from '../Or12336/Or12336.constants'
import type {
  IssuesPlanningCategoryMasterInitSelectInEntity,
  IssuesPlanningCategoryMasterInitSelectOutEntity,
} from '../../../../repositories/cmn/entities/IssuesPlanningCategoryMasterInitSelectEntity'
import { OrX0097Logic } from '../OrX0097/OrX0097.logic'
import type { OrX0097Param } from '../OrX0097/OrX0097.type'
import type { TableData } from '../Or12336/Or12336.type'
import { Or10494Const, type AsyncFunction } from './Or10494.constants'
import type { Or10494StateType } from './Or10494.type'
import { Or10494Logic } from './Or10494.logic'
import { useScreenOneWayBind, useScreenUtils, useSetupChildProps } from '#imports'
import type { Or12336Type } from '~/types/cmn/business/components/Or12336Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import type {
  IssuesPlanningCategoryMasterUpdateInEntity,
  IssuesPlanningCategoryMasterUpdateOutEntity,
} from '~/repositories/cmn/entities/IssuesPlanningCategoryMasterUpdateEntity'
import { UPDATE_KBN } from '~/constants/classification-constants'
import { ResBodyStatusCode } from '~/constants/api-constants'

/**
 * Or10494:有機体:課題立案区分マスタ
 * GUI00903_課題立案区分マスタ
 *
 * @description
 * 課題立案区分マスタ
 *
 * <AUTHOR>
 */

/************************************************
 * Props
 ************************************************/
interface Props {
  uniqueCpId: string
  parentUniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()
const { getChildCpBinds } = useScreenUtils()
const local = reactive({
  or12336: {
    delBtnDisabled: false,
    focusIndex: '',
    focusType: '',
    issuesPlanningCategoryMasterInfoList: [],
  } as Or12336Type,
  kbnFlg: '',
  focusIndex: '',
})

// 説明-区分番号
const descriptionCategoryNumber: string = t('label.category-number-input')
// 説明-全共通
const descriptionAllCommon: string = t('label.all-common')

//課題立案区分一覧および操作ボタン
const or27616 = ref({ uniqueCpId: Or27616Const.CP_ID(0) })

// 課題立案区分一覧
const or12336 = ref({ uniqueCpId: Or12336Const.CP_ID(0) })

// Or21813_有機体:エラーダイアログ
const or21813 = ref({ uniqueCpId: Or21813Const.CP_ID(0) })

//画面が閉じ
const isClose = ref(false)

//タブ切り替え
const isChangeTab = ref(false)

// Or12336 Ref
const or12336Ref = ref<{
  createRow(): AsyncFunction
  copyRow(): AsyncFunction
  deleteRow(): AsyncFunction
  init(): AsyncFunction
}>()

// Or27616 Ref
const or27616Ref = ref<{ delBtnDisable(disabled: boolean): AsyncFunction }>()

/**************************************************
 * コンポーネント固有処理
 **************************************************/
useSetupChildProps(props.uniqueCpId, {
  [Or27616Const.CP_ID(0)]: or27616.value,
  [Or12336Const.CP_ID(0)]: or12336.value,
})

/**
 * 保存ボタン押下_保存権限がある場合の開閉
 *
 * @param errormsg - Message
 */
function showOr21813Msg(errormsg: string) {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト0
      dialogText: errormsg,
      firstBtnType: 'normal1',
      firstBtnLabel: 'OK',
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })
  // エラーダイアログをオープン
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

// ダイアログ表示フラグ
const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 行追加ボタン
 */
function onAddItem() {
  or12336Ref.value?.createRow()
}
/**
 * 行複写ボタン
 */
function onCloneItem() {
  or12336Ref.value?.copyRow()
}
/**
 * 行削除ボタン
 */
function onDelete() {
  or12336Ref.value?.deleteRow()
}

useScreenOneWayBind<Or10494StateType>({
  cpId: Or10494Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    param: (value) => {
      if (!value?.executeFlag) {
        return
      }
      local.kbnFlg = value.kbnFlg
      switch (value?.executeFlag) {
        // 保存
        case 'save':
          isClose.value = value.isClose ?? false
          isChangeTab.value = value.isChangeTab ?? false
          void save()
          break
        // データ再取得
        case 'getData':
          void init()
          break
        default:
          break
      }
      // Or10494のダイアログ状態を更新する
      Or10494Logic.state.set({
        uniqueCpId: props.uniqueCpId,
        state: {
          param: {
            executeFlag: '',
            isClose: false,
          } as OrX0097Param,
        },
      })
    },
  },
})

/**
 * 行削除ボタン活性状態を監視
 *
 * @description
 * 活性状態を監視
 */
watch(
  () => local.or12336.delBtnDisabled,
  (newValue) => {
    or27616Ref.value?.delBtnDisable(!newValue)
  }
)
/**
 * Or21813のイベントを監視
 *
 * @description
 * またOr21813のボタン押下フラグをリセットする。
 */
watch(
  () => Or21813Logic.event.get(or21813.value.uniqueCpId),
  () => {
    local.or12336.focusIndex = local.focusIndex
  }
)

const init = async () => {
  // 課題立案区分マスタ情報取得(IN)
  const inputData: IssuesPlanningCategoryMasterInitSelectInEntity = {
    kbnFlg: local.kbnFlg,
  }
  // 課題立案区分マスタ初期情報取得
  const res: IssuesPlanningCategoryMasterInitSelectOutEntity = await ScreenRepository.select(
    'issuesPlanningCategoryMasterInitSelect',
    inputData
  )
  local.or12336.issuesPlanningCategoryMasterInfoList = res.data.issuesPlanningCategoryMasterInfoList

  await nextTick()
  or12336Ref.value?.init()
}

/**
 * 保存
 */
async function save() {
  await nextTick()
  local.focusIndex = ''
    local.or12336.focusIndex = ''
  const nameSet = new Set<string>()

    const childCpBindsData = getChildCpBinds(props.uniqueCpId, {
    // 一覧データ
    [Or12336Const.CP_ID(0)]: { cpPath: Or12336Const.CP_ID(0), twoWayFlg: true },
  })

  // 一覧データを取得
  const tableData = childCpBindsData[Or12336Const.CP_ID(0)].twoWayBind?.value as TableData[]
  for (let index = 0; index < tableData.length; index++) {
    const data = tableData[index]
    if (data.updateKbn === UPDATE_KBN.DELETE) {
      continue
    }
    //「区分番号」或いは「内容」に空白があるの場合
    if (data.kbnCd.value === '' || data.textKnj.value === '') {
      local.focusIndex = index + ''
      local.or12336.focusType = data.kbnCd
        ? Or12336Const.DEFAULT.TEXT_KNJ
        : Or12336Const.DEFAULT.KBN_CD
      showOr21813Msg(t('message.e-cmn-41710'))
      break
    }
    // 「区分番号」の入力値は1000以下の場合
    if (Number(data.kbnCd.value) < 1000) {
      local.focusIndex = index + ''
      local.or12336.focusType = Or12336Const.DEFAULT.KBN_CD
      showOr21813Msg(t('message.e-cmn-41711', [t('label.category-number')]))
      break
    }
    //「区分番号」は重複の場合
    if (nameSet.has(data.kbnCd.value)) {
      local.focusIndex = index + ''
      local.or12336.focusType = Or12336Const.DEFAULT.KBN_CD
      showOr21813Msg(t('message.e-cmn-41713', [t('label.category-number')]))
      break
    }
    nameSet.add(data.kbnCd.value)
  }
  // 変更がある場合、処理継続
  if (local.focusIndex) {
    return
  }

  const param: IssuesPlanningCategoryMasterUpdateInEntity = {
    issuesPlanningCategoryMasterInfoList: tableData.map((item) => {
      return {
        cf1Id: item.cf1Id,
        modifiedCnt: item.modifiedCnt,
        kbnFlg:item.kbnFlg,
        kbnCd: item.kbnCd.value,
        textKnj: item.textKnj.value,
        updateKbn: item.updateKbn,
      }
    }),
  }
  // 情報保存
  const res: IssuesPlanningCategoryMasterUpdateOutEntity = await ScreenRepository.update(
    'issuesPlanningCategoryMasterUpdate',
    param
  )

  if (isClose.value) {
    OrX0097Logic.state.set({
      uniqueCpId: props.parentUniqueCpId,
      state: {
        isOpen: false,
      },
    })
  }
  if (res.statusCode === ResBodyStatusCode.SUCCESS && !isChangeTab.value) {
    await init()
  }
  if (isChangeTab.value) {
    OrX0097Logic.event.set({
      uniqueCpId:props.parentUniqueCpId,
      state:{
        isSave:true
      }
    })
  }
}

</script>

<template>
  <div class="h-100 d-flex flex-column">
    <!-- 課題立案区分一覧および操作ボタン -->
    <div class="pb-2">
      <g-custom-or-27616
        ref="or27616Ref"
        v-bind="or27616"
        @on-add-item="onAddItem"
        @on-clone-item="onCloneItem"
        @on-delete="onDelete"
      />
    </div>
    <!-- 課題立案区分一覧 -->
    <div class="flex-1-1">
      <g-custom-or-12336
        ref="or12336Ref"
        v-bind="or12336"
        :model-value="local.or12336"
        :parent-unique-cp-id="props.uniqueCpId"
        @update:model-value="local.or12336.delBtnDisabled = $event.delBtnDisabled"
      />
    </div>
    <div>
      <!-- 説明:※区分番号は1000以上の値を入力してください。  -->
      <div class="font-weight-bold font-size-text font-red">
        {{ descriptionCategoryNumber }}
      </div>
      <!-- 説明:※全共通  -->
      <div class="font-size-text">
        {{ descriptionAllCommon }}
      </div>
    </div>
  </div>

  <!-- Or21813:有機体:エラーダイアログ -->
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  />
</template>

<style scoped lang="scss">
.font-size-text {
  font-size: 12px;
}
.font-red {
  color: red;
}
</style>
