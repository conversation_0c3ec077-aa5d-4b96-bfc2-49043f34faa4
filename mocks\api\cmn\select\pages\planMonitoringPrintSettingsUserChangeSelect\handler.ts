import { HttpResponse } from 'msw'
import defaultData from './data/default.json'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { PlanMonitoringPrintSettingsUserChangeSelectInEntity } from '~/repositories/cmn/entities/PlanMonitoringPrintSettingsUpdateEntity.ts'
/**
 * GUI01232_印刷設定
 *
 * @description
 * GUI01232_印刷設定履歴リストデータを返却する。
 * dataName："planMonitoringPrintSettingsUserChangeSelect"
 */
export function handler(inEntity: PlanMonitoringPrintSettingsUserChangeSelectInEntity) {
  const responceJson: BaseResponseBody = {
    statusCode: 'success',
    data: {
      ...defaultData,
    },
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
