<script setup lang="ts">
/**
 * Or01086：有機体：(日課計画マスタ)予防計画書からの初期取込入力
 */
import { reactive, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or01086Const } from './Or01086.constants'
import type { Mo00039OnewayType } from '~/types/cmn/business/components/Mo00039Type'
import { useScreenTwoWayBind } from '#imports'
import type { Or01086Type, Or01086OneWayType } from '~/types/cmn/business/components/Or01086Type'

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  onewayModelValue: Or01086OneWayType
}
const props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/
const localOneway = reactive({
  or01086OneWay: {
    ...props.onewayModelValue,
  } as Or01086OneWayType,
  mo00039Oneway: {
    // デフォルト値の設定
    name: Or01086Const.CP_ID(0),
    itemLabel: t('label.level-of-care-required'),
    showItemLabel: false,
    hideDetails: true,
  } as Mo00039OnewayType,
})
/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<Or01086Type>({
  cpId: Or01086Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
watch(
  () => props.onewayModelValue.radioItemsList,
  () => {
    localOneway.or01086OneWay.radioItemsList = props.onewayModelValue.radioItemsList
  }
)
</script>

<template>
  <c-v-row
    no-gutters
    class="text-center"
  >
    <base-mo00039
      v-if="refValue"
      v-model="refValue!.initialImport"
      :oneway-model-value="localOneway.mo00039Oneway"
    >
      <base-at-radio
        v-for="item in localOneway.or01086OneWay.radioItemsList"
        :key="item.value"
        v-model="refValue!.initialImport"
        style="width: 140px"
        :name="item.label"
        :radio-label="item.label"
        :value="item.value"
      />
    </base-mo00039>
  </c-v-row>
</template>

<style scoped lang="scss">
.text-center {
  align-items: baseline;
}
</style>
