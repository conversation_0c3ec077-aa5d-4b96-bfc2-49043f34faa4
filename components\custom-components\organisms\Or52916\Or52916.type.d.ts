/**
 * Or52916:有機体:承認欄の複写画面
 * GUI00618_承認欄の複写画面
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR>
 */
export interface Or52916StateType {
  /**
   * 開閉フラグ
   */
  isOpen?: boolean
}

/**
 * 承認欄一覧情報
 */
export interface ApprovalColumnTableDataItem {
  /**
   * 承認欄text
   */
  textRenderList: {
    /**
     * 左の入力内容
     */
    leftText: string
    /**
     * 右の入力内容
     */
    rightText: string
    /**
     * fontsize
     */
    fontsize: string
    /**
     * 左の入力With
     */
    leftWidth: string
    /**
     * 右の入力Width
     */
    rightWidth: string
    /**
     * 左の入力Flg
     */
    leftFlg: boolean
    /**
     * 右の入力Flg
     */
    rightFlg: boolean
  }[]
  /**
   * 事業所番号
   */
  jigyoNumber: string

  /**
   * 事業名
   */
  jigyoKnj: string

  /**
   * サービス事業者ID
   */
  svJigyoId: string

  /**
   * 法人ID
   */
  houjinId: string

  /**
   * 法人名
   */
  houjinKnj: string

  /**
   * 施設ID
   */
  shisetuId: string

  /**
   * サービス事業者コード
   */
  svJigyoCd: string

  /**
   * 施設名
   */
  shisetuKnj: string

  /**
   * サービス事業所名
   */
  svJigyoKnj: string

  /**
   * サービス区分
   */
  svKbn: string

  /**
   * 事業名（略称）
   */
  jigyoRyakuKnj: string

  /**
   * 法人名（略称）
   */
  houjinRyakuKnj: string

  /**
   * 施設名（略称）
   */
  shisetsuRyakuKnj: string

  /**
   * 略称
   */
  ruakuKnj: string

  /**
   * ダミー方式
   */
  dmyHoushiki: string

  /**
   * NUM1
   */
  num1: string

  /**
   * ケアプラン方式
   */
  cpnType: string

  /**
   * ID
   */
  id: string

  /**
   * 帳票コード
   */
  chohyoCd: string

  /**
   * 表示行数
   */
  dispKbn: string

  /**
   * 帳票リスト名
   */
  chohyoNm: string

  /**
   * 更新回数
   */
  cmpModifiedCnt: string

  /**
   * 承認欄1行目
   */
  text1Knj: string

  /**
   * 承認欄2行目
   */
  text2Knj: string

  /**
   * 下線部分1行目
   */
  day1Knj: string

  /**
   * 下線部分2行目
   */
  day2Knj: string

  /**
   * 1行目文字サイズ
   */
  text1Font: string

  /**
   * 2行目文字サイズ
   */
  text2Font: string

  /**
   * 承認欄1行目幅
   */
  text1Width: string

  /**
   * 承認欄2行目幅
   */
  text2Width: string

  /**
   * 下線部分1行目幅
   */
  day1Width: string

  /**
   * 下線部分2行目幅
   */
  day2Width: string

  /**
   * 法人ID
   */
  reportInfoHoujinId: string

  /**
   * 施設ID
   */
  reportInfoShisetuId: string

  /**
   * サービス事業者ID
   */
  reportInfoSvJigyoId: string

  /**
   * ID
   */
  reportInfoId: string

  /**
   * 帳票コード
   */
  reportInfoChohyoCd: string

  /**
   * 承認欄方向
   */
  orientType: string

  /**
   * 承認欄3行目
   */
  text3Knj: string

  /**
   * 下線部分3行目
   */
  day3Knj: string

  /**
   * 3行目文字サイズ
   */
  text3Font: string

  /**
   * 承認欄3行目幅
   */
  text3Width: string

  /**
   * 下線部分3行目幅
   */
  day3Width: string

  /**
   * 承認欄4行目
   */
  text4Knj: string

  /**
   * 下線部分4行目
   */
  day4Knj: string

  /**
   * 4行目文字サイズ
   */
  text4Font: string

  /**
   * 承認欄4行目幅
   */
  text4Width: string

  /**
   * 下線部分4行目幅
   */
  day4Width: string

  /**
   * 表示行数
   */
  reportInfoDispKbn: string

  /**
   * 更新回数
   */
  reportInfoCmsModifiedCnt: string
}
