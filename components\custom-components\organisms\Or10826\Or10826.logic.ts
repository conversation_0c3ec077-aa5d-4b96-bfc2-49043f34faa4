import { Or01533Const } from '../Or01533/Or01533.constants'
import { Or01533Logic } from '../Or01533/Or01533.logic'
import { OrX0053Const } from '../OrX0053/OrX0053.constants'
import { OrX0053Logic } from '../OrX0053/OrX0053.logic'
import { OrX0054Const } from '../OrX0054/OrX0054.constants'
import { OrX0054Logic } from '../OrX0054/OrX0054.logic'
import { Or10826Const } from './Or10826.constants'
import type { Or10826StateType } from './Or10826.type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'

/**
 * Or27653:モーダル:内容マスタ
 * GUI00935_内容マスタ
 *
 * @description
 * 処理ロジック initialize処理とpinia領域操作用の処理を提供する
 *
 * <AUTHOR>
 */
export namespace Or10826Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or10826Const.CP_ID(0),
      uniqueCpId,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [
        { cpId: Or01533Const.CP_ID(1) },
        { cpId: OrX0053Const.CP_ID(1) },
        { cpId: OrX0054Const.CP_ID(1) },
        { cpId: Or21813Const.CP_ID(1) },
        { cpId: Or21814Const.CP_ID(1) },
      ],

      // 編集フラグ不要
      // ※ 元々双方向領域を持たないため記載不要だが、サンプル用にナビゲーション制御領域で持つデータを分かりやすくするために設定
      editFlgNecessity: false,
    })
    Or01533Logic.initialize(childCpIds[Or01533Const.CP_ID(1)].uniqueCpId)
    OrX0053Logic.initialize(childCpIds[OrX0053Const.CP_ID(1)].uniqueCpId)
    OrX0054Logic.initialize(childCpIds[OrX0054Const.CP_ID(1)].uniqueCpId)
    Or21813Logic.initialize(childCpIds[Or21813Const.CP_ID(1)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(1)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }
  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or10826StateType>(Or10826Const.CP_ID(0))
}
