/**
 * Or10913:(要因取込)ダイアログ
 * GUI00918_［要因取込］画面
 *
 * @description
 * 静的データ
 *
 * <AUTHOR> DAM XUAN HIEU
 */
import { getSequencedCpId } from '~/utils/useScreenUtils'

export namespace Or10913Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or10913', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false
    /**
     * アセスメント種別の選択コード区分（selectCodeKbn）
     */
    export const SELECT_CD_KBN_ASSESSMENT_KIND = 357
  }

  export namespace VARIABLE {
    /**
     * 文字列1
     */
    export const string_1 = '1'
    /**
     * 文字列2
     */
    export const string_2 = '2'
    /**
     * 文字列3
     */
    export const string_3 = '3'
    /**
     * 文字列4
     */
    export const string_4 = '4'
    /**
     * 文字列5
     */
    export const string_5 = '5'
    /**
     * 文字列6
     */
    export const string_6 = '6'

    /**
     * 検討用紙
     */
    export const studyForm = 'studyForm'
    /**
     * サマリー表
     */
    export const summaryTable = 'summaryTable'
    /**
     * 検討表
     */
    export const considerationTable = 'considerationTable'

    /**
     * アセスメントセクション
     */
    export const asSection = '「課題整理総括要因取込」'
    /**
     * アセスメントキー
     */
    export const asKey = '「初期表示」'
  }
}
