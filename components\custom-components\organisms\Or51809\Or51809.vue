<script setup lang="ts">
import { onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or24551Const } from '../Or24551/Or24551.constants'
import type { CodeType } from '../Or28326/Or28326.type'
import type { Or51809RadioType, Or51809StateType } from './Or51809.type'
import { Or51809Const } from './Or51809.constants'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { useScreenOneWayBind } from '#imports'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type {
  Or51809OnewayType,
  MoveUseIdListInfoData,
  UserListInfoData,
} from '~/types/cmn/business/components/Or51809Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import { useSetupChildProps, useSystemCommonsStore } from '#build/imports'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CustomClass } from '~/types/CustomClassType'
import type { At00014OnewayType } from '~/types/business/components/At00014Type'
import type { Mo00037OnewayType } from '~/types/business/components/Mo00037Type'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo01352OnewayType } from '~/types/business/components/Mo01352Type'
import type {
  Mo01334Items,
  Mo01334OnewayType,
  Mo01334Type,
} from '~/types/business/components/Mo01334Type'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import type {
  ServiceUseSlipAnnexedTableDataMoveSelectInEntity,
  ServiceUseSlipAnnexedTableDataMoveSelectOutEntity,
} from '~/repositories/cmn/entities/ServiceUseSlipAnnexedTableDataMoveSelectEntity'
import type {
  ServiceUseSlipAnnexedTableDataMoveUpdateInEntity,
  ServiceUseSlipAnnexedTableDataMoveUpdateOutEntity,
} from '~/repositories/cmn/entities/ServiceUseSlipAnnexedTableDataMoveUpdateEntity'

import { OrX0145Const } from '~/components/custom-components/organisms/OrX0145/OrX0145.constants'
import { hasRegistAuth } from '~/utils/useCmnAuthz'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
/**
 * Or51809:有機体:サービス利用票・別表データ移動
 * GUI00613_サービス利用票・別表データ移動
 *
 * @description
 * サービス利用票・別表データ移動
 *
 * <AUTHOR>
 */
const { t } = useI18n()
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()

/************************************************
 * Props
 ************************************************/
interface Props {
  onewayModelValue: Or51809OnewayType
  uniqueCpId: string
}
const props = defineProps<Props>()

const defaultOnewayModelValue: Or51809OnewayType = {
  /** 親画面.移行元事業者ID */
  originSvJigyoId: '',
  /** 親画面.移動先年月*/
  moveYm: '',
  /** 親画面.担当ケアマネID*/
  tantoId: '',
  // 親画面.事業所ID
  svJigyoId: '',
}

const localOneway = reactive({
  or51809Oneway: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // 「計画表取込」ダイアログ
  mo00024Oneway: {
    width: '980px',
    persistent: true,
    showCloseBtn: true,
    disabledCloseBtnEvent: false,
    mo01344Oneway: {
      toolbarTitle: t('label.service-use-slip-annexed-table-data-move'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
    },
  },
  // 年月日
  mo00020Oneway: {
    itemLabel: '',
    showItemLabel: false,
    hideDetails: true,
    maxlength: '10',
    disabled: false,
    width: '144',
    isRequired: true,
    showSelectArrow: true,
    mo00009OnewayBack: {} as Mo00009OnewayType,
    mo00009OnewayForward: {} as Mo00009OnewayType,
  } as Mo00020OnewayType,
  mo00020Type: {
    value: '',
    mo01343: {
      value: '',
      mo00024: {
        isOpen: false,
      } as Mo00024Type,
    },
  } as Mo00020Type,
  //ボタンを閉じますラベル
  mo00611OneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  //確定ボタンラベル
  mo00609OneWay: {
    btnLabel: t('btn.confirm'),
  } as Mo00609OnewayType,
  //(汎用コードマスタ)取込元
  mo00039Oneway_oa: {
    // デフォルト値の設定
    name: 'importModeOa',
    showItemLabel: false,
    hideDetails: true,
  } as Mo00039OnewayType,
  // 担当者ラベル
  mo00615Oneway: {
    itemLabel: t('label.earrings'),
  } as Mo00615OnewayType,
  mo00009Oneway: {
    btnIcon: 'open_in_new',
  } as Mo00009OnewayType,
  mo01334Oneway: {
    headers: [
      {
        title: t('label.bussiness-name'),
        key: 'jigyoNumber',
        minWidth: '140px',
        sortable: false,
      },
    ],
    height: 583,
    items: [
      {
        id: '',
        /** サービス事業者ID */
        svJigyoId: '',
        /** 事業所番号 */
        jigyoNumber: '',
        /** 事業名（略称） */
        jigyoRyakuKnj: '',
      },
    ],
  } as Mo01334OnewayType,
  /**
   * 担当ケアマネプルダウン
   */
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: false,
    multiple: false,
    // TODO 前の画面担当ケアマネのインデックス取得
    selectedUserCounter: '',
  } as OrX0145OnewayType,
})

const local = reactive({
  // ローカルで保持する共通情報
  commonInfo: {
    // 共通情報.ログイン情報.職員ID
    staffId: systemCommonsStore.getLoginInfo?.staffId,
    // 共通情報.システムコード
    systemCode: systemCommonsStore.getSystemCode,
  },
  // 年月日
  moveYm: {
    value: '',
  },
  // 担当ケアマネID
  tantoId: localOneway.or51809Oneway.tantoId,
  // 移行元事業者ID
  originSvJigyoId: localOneway.or51809Oneway.originSvJigyoId,
  // 性別リスト
  sexRadioList: [] as Or51809RadioType[],
  // 担当ケアマネ一覧リスト
  userListBAK: [] as UserListInfoData[],
})
const or21814_1 = ref({ uniqueCpId: '' }) // 確認ダイアログ（はい、いいえ、キャンセル）
const or21814_2 = ref({ uniqueCpId: '' }) // 確認ダイアログ（はい、いいえ）
const or21814_3 = ref({ uniqueCpId: '' })
const or21813_1 = ref({ uniqueCpId: '' })
const or21815_1 = ref({ uniqueCpId: '' })
const or24551 = ref({ uniqueCpId: '' })
// const gui00048 = ref({ uniqueCpId: '' })
const orx0145 = ref({ uniqueCpId: '' })

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or21814Const.CP_ID(2)]: or21814_2.value,
  [Or21814Const.CP_ID(3)]: or21814_3.value,
  [Or21813Const.CP_ID(1)]: or21813_1.value,
  [Or21815Const.CP_ID(1)]: or21815_1.value,
  [Or24551Const.CP_ID(1)]: or24551.value,
  [OrX0145Const.CP_ID(0)]: orx0145.value,
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or51809StateType>({
  cpId: Or51809Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or51809Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or51809Const.DEFAULT.IS_OPEN,
  emitType: 'blank',
})

/**
 * 当ケアマネプルダウン選択値
 */
const orX0145Type = ref<OrX0145Type>({
  value: {} as TantoCmnShokuin,
} as OrX0145Type)

const Mo01338Oneway_1 = ref<Mo01338OnewayType>({
  value: t('label.user-select'),

  customClass: new CustomClass({
    outerClass: 'mr-0',
  }),
})

const Mo00037Oneway_1 = ref<Mo00037OnewayType>({
  class: 'must-badge',
  label: t('label.required'),
})

const At00014Oneway = ref<At00014OnewayType>({
  value: t('label.process-ym'),
})

const Mo01338Oneway_2 = ref<Mo01338OnewayType>({
  value: '',
  customClass: new CustomClass({
    outerClass: 'ma-0',
  }),
})

const Mo01338Oneway_3 = ref<Mo01338OnewayType>({
  value: t('label.move-select'),
  customClass: new CustomClass({
    outerClass: 'mr-0',
  }),
})

const Mo01338Oneway_4 = ref<Mo01338OnewayType>({
  value: t('label.move-select-sub-label'),
})

const mo01352Oneway = ref<Mo01352OnewayType>({
  textFieldwidth: '136px',
  disabled: false,
  width: '200px',
})

const mo01338Style = {
  customClass: new CustomClass({}),
}

// 共通処理の編集権限チェック
const editFlg = ref<boolean>(true)

/**
 * 利用者列幅
 */
// const userCols = ref<number>(4)

/**
 * 移動利用者ID一覧リスト
 */
const mo01334TypeUser = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

const mo01334TypeOriginSvJigyoId = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

/**
 * 利用者一覧(複数)
 */
const mo01334OnewayUserHukusuu = ref<Mo01334OnewayType>({
  headers: [
    // 氏名
    {
      title: t('label.name'),
      key: 'nameKnj',
      sortable: false,
      minWidth: '166',
    },
    // 年齢
    {
      title: t('label.age'),
      key: 'birthdayYmd',
      sortable: false,
      minWidth: '42',
    },
    // 性別
    {
      title: t('label.gender'),
      key: 'sex',
      sortable: false,
      minWidth: '42',
    },
  ],
  items: [],
  height: 445,
  mandatory: false,
  showSelect: true,
  selectStrategy: 'all',
})

/**
 * 利用者エンティティ
 */
interface UserEntity {
  /** 氏名（姓） */
  name1Knj: string
  /** 氏名（名） */
  name2Knj: string
  /** 生年月日 */
  birthdayYmd: string
  /** 性別 */
  sex: string
  /** サービス提供年月（変更日） */
  yymmD: string
  /** 利用者ID */
  id: string
}

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  editFlg.value = await hasRegistAuth()

  init()

  await getFormatStyleRadio()
  // 初期情報取得
  await getInitDataInfo()
})

/**************************************************
 * 関数
 **************************************************/
// 初期処理
function init() {
  console.log('systemCommonsStore.getSystemDate', systemCommonsStore.getSystemDate)
  const getSystemDate = systemCommonsStore.getSystemDate
  local.moveYm.value = getSystemDate ? getSystemDate.substring(0, 7) : ''

  // 確認ダイアログを初期化
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      isOpen: false,
      dialogTitle: t('label.confirm'),
      firstBtnType: 'blank',
      secondBtnType: 'blank',
      thirdBtnType: 'normal1',
      thirdBtnLabel: t('btn.ok'),
    },
  })

  // 警告ダイアログを初期化
  Or21815Logic.state.set({
    uniqueCpId: or21815_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      isOpen: false,
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
    },
  })

  // エラーダイアログを初期化
  Or21813Logic.state.set({
    uniqueCpId: or21813_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      isOpen: false,
      dialogTitle: t('label.error'),
      firstBtnType: 'blank',
      secondBtnType: 'blank',
      thirdBtnType: 'normal1',
      thirdBtnLabel: t('btn.ok'),
    },
  })
}

/**
 * 性別の変更
 *
 * @param gender - value値
 */
const genderValueToLabel = (gender: string) => {
  let genderLabel = '不明'
  for (const item of local.sexRadioList) {
    const data = { ...item } as CodeType
    if (gender === data.value) {
      genderLabel = data.label
    }
  }
  return genderLabel
}

/**
 * 年齢の計算
 *
 * @param userBirthdate - value値
 */
function calculateAge(userBirthdate: string) {
  const currentDate = new Date()
  const birthdate = new Date(userBirthdate)
  let age = currentDate.getFullYear() - birthdate.getFullYear()
  const monthDiff = currentDate.getMonth() - birthdate.getMonth()

  if (monthDiff < 0 || (monthDiff === 0 && currentDate.getDate() < birthdate.getDate())) {
    age--
  }

  return age
}

/**
 * 一時データの構築
 *
 * @param userDataSourceList -items
 */
const createTempData = (userDataSourceList: UserEntity[]) => {
  // 利用者
  const list: Mo01334Items[] = []
  for (const item of userDataSourceList) {
    let color = ''
    switch (genderValueToLabel(item.sex)) {
      case '女':
        color = 'red'
        break
      case '男':
        color = 'blue'
        break
    }

    list.push({
      id: item.id,
      mo01337OnewayNameKnj: {
        value: `${item.name1Knj} ${item.name2Knj}`,
        unit: '',
      },
      mo01337OnewaybirthdayYmd: {
        value: calculateAge(item.birthdayYmd),
        unit: '',
      },
      mo01337Onewaysex: {
        value: genderValueToLabel(item.sex),
        unit: '',
        valueFontColor: color,
      },
    } as Mo01334Items)
  }

  mo01334OnewayUserHukusuu.value.items = list
}

//汎用コード取得取込モード
async function getFormatStyleRadio() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    //(汎用コードマスタ)取込モードラジオボタン入力取込
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SEVICE_USER_GENDERE_CODE },
  ]
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  local.sexRadioList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_SEVICE_USER_GENDERE_CODE
  )
}

/**
 * 初期情報取得
 *
 */
async function getInitDataInfo() {
  const inputData: ServiceUseSlipAnnexedTableDataMoveSelectInEntity = {
    /** 親画面.移行元事業者ID */
    originSvJigyoId: localOneway.or51809Oneway.originSvJigyoId,
    /** 親画面.移動先年月*/
    moveYm: local.moveYm.value,
    /** 親画面.担当ケアマネID*/
    tantoId: local.tantoId,
  }

  const resData: ServiceUseSlipAnnexedTableDataMoveSelectOutEntity = await ScreenRepository.select(
    'serviceUseSlipAnnexedTableDataMoveSelect',
    inputData
  )

  const dataInfo = resData.data
  console.log('初期情報取得', dataInfo)
  let items = []
  if (dataInfo) {
    items = dataInfo.officeNameList.map((item) => {
      return { ...item, id: item.svJigyoId }
    })
    localOneway.mo01334Oneway.items = items

    createTempData(dataInfo.userList)
    // 担当ケアマネ一覧リスト
    local.userListBAK = dataInfo.userList

    Mo01338Oneway_2.value.value = `${dataInfo.tantoList[0].shokuin1Knj} ${dataInfo.tantoList[0].shokuin2Knj}`
    mo01334TypeUser.value.values = [dataInfo.userList[0].id]
  }
}

/** サービス利用票・別表データ移動		情報保存 */
async function save() {
  try {
    const moveUseIdList = [] as MoveUseIdListInfoData[]
    mo01334TypeUser.value.values.forEach((item) => {
      moveUseIdList.push({
        moveUseId: item,
      })
    })
    const payload: ServiceUseSlipAnnexedTableDataMoveUpdateInEntity = {
      /** 親画面.移行元事業者ID */
      originSvJigyoId: localOneway.or51809Oneway.originSvJigyoId,
      /** 移動サービス事業者ID */
      moveServiceSvJigyoId: mo01334TypeOriginSvJigyoId.value.value ?? '',
      /** 処理年月 */
      shoriYmd: local.moveYm.value,
      /** 移動利用者ID一覧リスト */
      moveUseIdList: moveUseIdList,
      /** 利用者一覧リスト */
      userList: local.userListBAK,
    }
    const resData: ServiceUseSlipAnnexedTableDataMoveUpdateOutEntity =
      await ScreenRepository.update('serviceUseSlipAnnexedTableDataMoveUpdate', payload, 'dd')
    /****************************************
     * 保存成功の場合
     ****************************************/
    if (resData.statusCode === 'success') {
      Or21814Logic.state.set({
        uniqueCpId: or21814_1.value.uniqueCpId,
        state: {
          isOpen: true,
          dialogText: t('message.i-cmn-10527'),
        },
      })
      // 画面情報再取得
      await getInitDataInfo()
    }
  } catch (e: unknown) {
    console.log(e)
  }
}

/**
 * AC002_「×ボタン」押下
 * AC011_「閉じボタン」押下
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  setState({ isOpen: false })
}

/**
 * 処理年月
 *
 * @param dateString - 画面.処理年月
 */
function isDateLessThanOrEqualToMarch2000(dateString: string) {
  // 2000/03
  const targetDate = new Date(2000, 2, 1)
  const inputDate = new Date(dateString)
  return inputDate <= targetDate
}

/**
 * AC012_「確定ボタン」押下
 * 「確定」ボタン押下
 */
async function confirm() {
  //AC012-2 画面.処理年月=""または≦ '2000/03'の場合
  if (isDateLessThanOrEqualToMarch2000(local.moveYm.value)) {
    Or21814Logic.state.set({
      uniqueCpId: or21814_1.value.uniqueCpId,
      state: {
        isOpen: true,
        dialogText: t('message.i-cmn-10524'),
      },
    })
    return
  }
  //AC012-3 親画面.移行元事業者ID > 0 且つ 移動サービス事業者ID  > 0以外の場合

  if (
    !(
      Number(localOneway.or51809Oneway.originSvJigyoId) > 0 &&
      Number(mo01334TypeOriginSvJigyoId.value.value) > 0
    )
  ) {
    Or21814Logic.state.set({
      uniqueCpId: or21814_1.value.uniqueCpId,
      state: {
        isOpen: true,
        dialogText: t('message.i-cmn-10525'),
      },
    })
    return
  }

  // AC012-4 親画面.移行元事業者ID = 移動サービス事業者ID の場合
  if (localOneway.or51809Oneway.originSvJigyoId === mo01334TypeOriginSvJigyoId.value.value) {
    Or21814Logic.state.set({
      uniqueCpId: or21814_1.value.uniqueCpId,
      state: {
        isOpen: true,
        dialogText: t('message.i-cmn-10526'),
      },
    })
    return
  }

  // AC012-5 利用者一覧のチェックボックスが選択されていない場合
  if (mo01334TypeUser.value.values.length < 1) {
    Or21814Logic.state.set({
      uniqueCpId: or21814_1.value.uniqueCpId,
      state: {
        isOpen: true,
        dialogText: t('message.i-cmn-11323'),
      },
    })
    return
  }
  // 保存処理を呼出し
  const dialogResult = await openConfirmDialogWarning(t('message.w-cmn-20051'))
  switch (dialogResult) {
    case 'yes': {
      void save()
      break
    }
    case 'no': {
      break
    }
  }
}



/**
 * 警告ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no)
 */
async function openConfirmDialogWarning(paramDialogText: string): Promise<'yes' | 'no'> {
  // 警告ダイアログを開く
  Or21815Logic.state.set({
    uniqueCpId: or21815_1.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })

  // 警告ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815_1.value.uniqueCpId)

        let result = 'no' as 'yes' | 'no'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }

        // 警告ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: or21815_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    mo00024.value.isOpen = true
    if (!newValue) {
      close()
    }
  }
)

/**
 * 担当者名アップデート
 *
 * @param newVal - アップデート用データ
 */
const handleUpdateName = async (newVal: OrX0145Type) => {
  const val = newVal.value
  if (val && !Array.isArray(val) && 'chkShokuId' in val) {
    local.tantoId = val.chkShokuId
  } else if (val === undefined) {
    local.tantoId = ''
  }
  await getInitDataInfo()
}


/**
 * 担当者変更アイコンを監視
 *
 * @description
 * 担当ケアマネ検索画面から返却した「職員ID」が変更する場合
 */
watch(
  () => local.tantoId,
  async (newValue) => {
    if (newValue) {
      await getInitDataInfo()
    }
  },
  { deep: true }
)

/**
 * 「処理年月テキストボックス」変更を監視
 *
 * @description
 * 入力した日付正確の場合
 */
watch(
  () => local.moveYm,
  async (newValue) => {
    if (newValue) {
      await getInitDataInfo()
    }
  },
  { deep: true }
)

</script>
<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet>
        <c-v-row no-gutters>
          <!-- 利用者選択（複数選択可 -->
          <c-v-col class="leftWrap pa-2 mr-2" >
            <!-- 利用者選択（複数選択可） -->
            <c-v-row no-gutters> <base-mo01338 :oneway-model-value="Mo01338Oneway_1" /></c-v-row>
            <c-v-row no-gutters class="d-flex align-center">
              <custom-at00014 :oneway-model-value="At00014Oneway" />
              <base-mo00037 :oneway-model-value="Mo00037Oneway_1" />
            </c-v-row>
            <c-v-row no-gutters >
              <!-- 処理年月 -->
              <base-mo01352
                v-model="local.moveYm"
                class="dateRow "
                :oneway-model-value="mo01352Oneway"
              />
            </c-v-row>
            <c-v-row no-gutters class="pt-2">
              <!-- 担当ケアマネプルダウン -->
              <c-v-col cols="auto">
                <g-custom-or-x-0145
                  v-bind="orx0145"
                  v-model="orX0145Type"
                  :oneway-model-value="localOneway.orX0145Oneway"
                  @update:model-value="handleUpdateName"
                ></g-custom-or-x-0145>
              </c-v-col>
            </c-v-row>

            <!-- 利用者一覧 -->
            <c-v-row no-gutters class="myTabel">
              <c-v-col
                cols="12"
                class="table-header"
              >
                <base-mo-01334
                  v-model="mo01334TypeUser"
                  :oneway-model-value="mo01334OnewayUserHukusuu"
                  class="list-wrapper"
                >
                  <!-- 氏名 -->
                  <template #[`item.nameKnj`]="{ item }">
                    <!-- 分子：一覧専用ラベル（文字列型） -->
                    <base-mo01337 :oneway-model-value="item.mo01337OnewayNameKnj" />
                  </template>
                  <!-- 年齢 -->
                  <template #[`item.birthdayYmd`]="{ item }">
                    <!-- 分子：一覧専用ラベル（文字列型） -->
                    <base-mo01337 :oneway-model-value="item.mo01337OnewaybirthdayYmd" />
                  </template>
                  <!-- 性別 -->
                  <template #[`item.sex`]="{ item }">
                    <!-- 分子：一覧専用ラベル（文字列型） -->
                    <base-mo01337
                      v-if="'女' === item.mo01337Onewaysex.value"
                      :oneway-model-value="item.mo01337Onewaysex"
                    />
                    <base-mo01337
                      v-else
                      :oneway-model-value="item.mo01337Onewaysex"
                    />
                  </template>
                  <!-- ページングを非表示 -->
                  <template #bottom />
                  <template #tfoot />
                </base-mo-01334>
              </c-v-col>

              <!-- 対象者人数 -->
              <c-v-col
                cols="12"
                class="d-flex justify-space-between align-center bottom"
                al
              >
                <base-mo01338 :oneway-model-value="{ value: `対象者人数`, ...mo01338Style }" />
                <base-mo01338
                  :oneway-model-value="{
                    value: `${local.userListBAK.length}${t('label.first-name')}`,
                    ...mo01338Style,
                  }"
                />
              </c-v-col>

              <!-- 選択人数 -->
              <c-v-col
                cols="12"
                class="d-flex justify-space-between bottom"
              >
                <base-mo01338 :oneway-model-value="{ value: `選択人数`, ...mo01338Style }" />
                <base-mo01338
                  :oneway-model-value="{
                    value: `${mo01334TypeUser.values.length}${t('label.first-name')}`,
                    ...mo01338Style,
                  }"
                />
              </c-v-col>
            </c-v-row>

            <!-- ダイアログ -->
          </c-v-col>
          <!-- 移動先選択 -->
          <c-v-col class="rightWrap table-header">
            <c-v-row no-gutters>
              <base-mo01338 :oneway-model-value="Mo01338Oneway_3" />
            </c-v-row>
            <c-v-row no-gutters>
              <base-mo01338 :oneway-model-value="Mo01338Oneway_4" />
            </c-v-row>
            <base-mo01334
              v-model="mo01334TypeOriginSvJigyoId"
              class="list-wrapper"
              hide-default-footer
              :oneway-model-value="localOneway.mo01334Oneway"
            >
              <!-- 利用者名 -->
              <template #[`item.jigyoNumber`]="{ item }">
                <!-- 分子：一覧専用ラベル（文字列型） -->
                <span>{{ item.jigyoNumber }}</span>
                <span>:</span>
                <span>{{ item.jigyoRyakuKnj }}</span>
              </template>
            </base-mo01334>
          </c-v-col>
        </c-v-row>
      </c-v-sheet>
    </template>
    <!-- フッター -->
    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          @click="close"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <base-mo00609
          v-bind="localOneway.mo00609OneWay"
          class="mx-2"
          @click="confirm"
        >
          <!--ツールチップ表示："表示されているデータを保存します"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.save')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <g-base-or21814 v-bind="or21814_1" />
  <g-base-or21814 v-bind="or21814_2" />
  <g-base-or21814 v-bind="or21814_3" />
  <g-base-or21813 v-bind="or21813_1" />
  <g-base-or21815 v-bind="or21815_1" />
</template>
<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';
@use '@/styles/base.scss';
//テーブルスタイル

.mo01338OOuter {
  background-color: rgb(var(--v-theme-background));
}

.leftWrap,
.rightWrap {
  border: 1px solid rgb(var(--v-theme-black-100));
}
:deep(.v-table__wrapper) {
  overflow-x: auto !important;
}

.dateRow {
  display: flex;
  align-items: center;
}
.myTabel {
  margin: 8px 0;
  border: 1px solid rgb(var(--v-theme-black-100));
  :deep(.v-col-12) {
    padding: 0 !important;
  }
  .bottom {
    border-top: 1px solid rgb(var(--v-theme-black-100));
  }
}
.must-badge {
  @extend .caption;
  color: rgb(var(--v-theme-orange-400));
}
</style>
