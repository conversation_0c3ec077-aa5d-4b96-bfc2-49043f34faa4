import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * Or10600:静的データ
 * GUI03248_月間・年間表パターン画面(タイトル)
 *
 * @description
 * 静的データ
 *
 * <AUTHOR>
 */
export namespace Or10600Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or10600', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false
    /**
     * マスタ区分リスト
     */
    export const MSTKBN_LIST = {
      // 2:日課パターン
      DAILY_PARTTEN: '2',
      // 3:週間パターン
      WEEK_PARTTEN: '3',
      // 4:月年パターン
      MONTHYEAR_PARTTEN: '4',
    }
    /**
     *YES
     */
    export const DIALOG_RESULT_YES = 'yes'
  }
  /**
   * タブID
   */
  export namespace TAB {
    /**
     * パターンタイトル画面のタブID
     */
    export const TAB_ID_TITLE = 'title'

    /**
     * パターン設定画面のタブID
     */
    export const TAB_ID_SET = 'settings'
    /**
     * パターングループ画面のタブID
     */
    export const TAB_ID_GROUP = 'group'
  }
}
