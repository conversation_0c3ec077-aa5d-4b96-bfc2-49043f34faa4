<script setup lang="ts">
/**
 * Or15183：有機体：（確定版）本人／家族の意向について与入院前の介護サービスの利用状況について
 *
 * <AUTHOR>
 */
import { computed, reactive, ref, watch, type Ref } from 'vue'
import { useI18n } from 'vue-i18n'
import type { CodeType } from '../Or28326/Or28326.type'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { TeX0012Logic } from '../../template/TeX0012/TeX0012.logic'
import { Or15183Const } from './Or15183.constants'
import type { Or15183OneWayType, Or15183ValuesType } from './Or15183.Type'
import {
  useScreenOneWayBind,
  useScreenTwoWayBind,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { CustomClass } from '~/types/CustomClassType'
import type { Mo00046OnewayType } from '~/types/business/components/Mo00046Type'
import type { Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import type { Or51775OnewayType, Or51775Type } from '~/types/cmn/business/components/Or51775Type'
import type { TeX0012Type } from '~/types/cmn/business/components/TeX0012Type'
/**************************************************
 * Props
 **************************************************/
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
interface Props {
  /** uniqueCpId */
  uniqueCpId: string
  parentUniqueCpId: string
}
const props = defineProps<Props>()
/**************************************************
 * Pinia
 **************************************************/
const { t } = useI18n()
const or51775 = ref({ uniqueCpId: '' }) // Or51775：有機体：入力支援［ケアマネ］モーダル
const { refValue } = useScreenTwoWayBind<Or15183ValuesType>({
  cpId: Or15183Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
}) as { refValue: Ref<Or15183ValuesType> }
useScreenOneWayBind<Or15183OneWayType>({
  cpId: Or15183Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    codeList: (value) => {
      localOneway.codeListOneway = value as Record<string, CodeType[]>
    },
  },
})
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or51775Const.CP_ID(1)]: or51775.value,
})
/**************************************************
 * 変数定義
 **************************************************/
const local = reactive({
  commonInfo: {} as TeX0012Type,
  or51775: { modelValue: '' } as Or51775Type,
  or51775Value: '',
})
const localOneway = reactive({
  codeListOneway: {} as Record<string, CodeType[]>,
  // GUI00937 共通入力支援画面
  or51775Oneway: {
    screenId: Or15183Const.DEFAULT.GUI,
    bunruiId: '-', // 分類ID TBD
    t2Cd: '',
    t3Cd: '',
    tableName: 'cpn_tuc_hosp_info_teikyou_data',
    assessmentMethod: Or15183Const.DEFAULT.ASSESS_MENT_METHOD, // アセスメント方式 TBD
    userId: systemCommonsStore.getUserId ?? '',
  } as Or51775OnewayType,
  mo00046Oneway: {
    showItemLabel: false,
    autoGrow: false,
    noResize: true,
    maxlength: '4000',
    rows: Or15183Const.DEFAULT.VALUE_1,
    maxRows: Or15183Const.DEFAULT.VALUE_1,
    width: '1000px',
  } as Mo00046OnewayType,
  mo00045Oneway: {
    maxlength: '44',
    width: '625px',
    isVerticalLabel: false,
    showItemLabel: false,
  } as Mo00045OnewayType,
  mo00018Oneway1: {
    name: t('label.family_intentions_before_admission_label'),
    hideDetails: true,
    showItemLabel: false,
    itemLabel: '',
    checkboxLabel: t('label.family_intentions_before_admission_label'),
  } as Mo00018OnewayType,
  mo00009Oneway: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  mo01338Oneway: {
    valueFontWeight: 'blod',
    value: t('label.patient_family_intentions_label'),
    customClass: { itemStyle: 'font-size:18px; !import' } as CustomClass,
  } as Mo01338OnewayType,
  mo01338Oneway1: {
    valueFontWeight: 'blod',
    value: t('label.care_services_utilization_before_admission_label'),
    customClass: { itemStyle: 'font-size:18px; !import' } as CustomClass,
  } as Mo01338OnewayType,

  serviceRiyoDataList: [
    {
      label: t('label.care_services_utilization_home_service_plan'),
      key: 'serviceRiyoKbn',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.care_services_utilization_other'),
      key: 'serviceRiyoKbn2',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
})

/**
 * 入力支援アイコンボタンクリック
 *
 */
const handleUserInfo = () => {
  // GUI00937 共通入力支援画面をポップアップで起動する
  local.or51775Value = 'userInfo'
  localOneway.or51775Oneway.title = t('label.hobbies_interests_label') + t('label.hobbies_icon')
  localOneway.or51775Oneway.t2Cd = Or15183Const.DEFAULT.VALUE_1
  localOneway.or51775Oneway.t3Cd = Or15183Const.DEFAULT.VALUE_7
  localOneway.or51775Oneway.columnName = 'user_info'
  localOneway.or51775Oneway.inputContents =
    t('label.hobbies_interests_label') + t('label.hobbies_icon')
  local.or51775.modelValue =
    (refValue.value.or15183Values[local.or51775Value] as unknown as Mo00045Type).value ?? ''
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 入力支援アイコンボタンクリック
 *
 */
const handleUserSeikatureki = () => {
  // GUI00937 共通入力支援画面をポップアップで起動する
  local.or51775Value = 'userSeikatureki'
  localOneway.or51775Oneway.title = t('label.life_history_label')
  localOneway.or51775Oneway.t2Cd = Or15183Const.DEFAULT.VALUE_1
  localOneway.or51775Oneway.t3Cd = Or15183Const.DEFAULT.VALUE_8
  localOneway.or51775Oneway.columnName = 'user_seikatureki'
  localOneway.or51775Oneway.inputContents = t('label.life_history_label')
  local.or51775.modelValue =
    (refValue.value.or15183Values[local.or51775Value] as unknown as Mo00045Type).value ?? ''
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 入力支援アイコンボタンクリック
 *
 */
const handleUserIkouKnj = () => {
  // GUI00937 共通入力支援画面をポップアップで起動する
  local.or51775Value = 'userIkouKnj'
  localOneway.or51775Oneway.title = t('label.intentions_before_admission_plan_reference')
  localOneway.or51775Oneway.t2Cd = Or15183Const.DEFAULT.VALUE_1
  localOneway.or51775Oneway.t3Cd = Or15183Const.DEFAULT.VALUE_9
  localOneway.or51775Oneway.columnName = 'user_ikou_knj'
  localOneway.or51775Oneway.inputContents = t('label.intentions_before_admission_plan_reference')
  local.or51775.modelValue =
    (refValue.value.or15183Values[local.or51775Value] as unknown as Mo00045Type).value ?? ''
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 入力支援アイコンボタンクリック
 *
 */
const handleKazokuIkouKnj = () => {
  // GUI00937 共通入力支援画面をポップアップで起動する
  local.or51775Value = 'kazokuIkouKnj'
  localOneway.or51775Oneway.title = t('label.family_intentions_before_admission_plan_reference')
  localOneway.or51775Oneway.t2Cd = Or15183Const.DEFAULT.VALUE_1
  localOneway.or51775Oneway.t3Cd = Or15183Const.DEFAULT.VALUE_10
  localOneway.or51775Oneway.columnName = 'kazoku_ikou_knj'
  localOneway.or51775Oneway.inputContents = t(
    'label.family_intentions_before_admission_plan_reference'
  )
  local.or51775.modelValue =
    (refValue.value.or15183Values[local.or51775Value] as unknown as Mo00045Type).value ?? ''
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 入力支援アイコンボタンクリック
 *
 */
const handleServiceRiyoMemoKnj = () => {
  // GUI00937 共通入力支援画面をポップアップで起動する
  local.or51775Value = 'serviceRiyoMemoKnj'
  localOneway.or51775Oneway.title = t('label.care_services_utilization_other_memo')
  localOneway.or51775Oneway.t2Cd = Or15183Const.DEFAULT.VALUE_1
  localOneway.or51775Oneway.t3Cd = Or15183Const.DEFAULT.VALUE_16
  localOneway.or51775Oneway.columnName = 'service_riyo_memo_knj'
  localOneway.or51775Oneway.inputContents = t('label.care_services_utilization_other_memo')
  local.or51775.modelValue =
    (refValue.value.or15183Values[local.or51775Value] as unknown as Mo00045Type).value ?? ''
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 共通入力支援画面の確定ボタン押す後の処理
 *
 * @param data - 入力支援情報
 */
const handleOr51775Confirm = (data: Or51775ConfirmType) => {
  const setOrAppendValue = (str: string, data: Or51775ConfirmType) => {
    if (data.type === Or15183Const.DEFAULT.ADD_END_TEXT_IMPORT_TYPE) {
      // 本文末に追加の場合
      return `${str}${data.value}`
    } else if (data.type === Or15183Const.DEFAULT.OVERWRITE_TEXT_IMPORT_TYPE) {
      // 本文上書の場合
      return data.value
    } else {
      return ''
    }
  }
  ;(refValue.value.or15183Values[local.or51775Value] as unknown as Mo00045Type).value =
    setOrAppendValue(
      (refValue.value.or15183Values[local.or51775Value] as unknown as Mo00045Type).value ?? '',
      data
    )
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: false },
  })
}

/**
 * 入力支援ポップアップの表示状態を返すComputed
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 画面メニューのイベントを監視
 */
watch(
  () => TeX0012Logic.data.get(props.parentUniqueCpId),
  (newValue) => {
    if (newValue?.teikyouId) {
      local.commonInfo = newValue
      localOneway.or51775Oneway.userId = newValue.userId ?? ''
    }
  },
  { deep: true }
)
</script>

<template>
  <div v-if="refValue.or15183Values">
    <c-v-row class="title">
      <c-v-col>
        <base-mo01338 :oneway-model-value="localOneway.mo01338Oneway"></base-mo01338>
      </c-v-col>
    </c-v-row>
    <!-- 本人の趣味・興味・関心領域等 -->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.hobbies_interests_label') }}<br />
        {{ t('label.hobbies_icon') }}<br />
        <div class="d-flex">
          <c-v-divider
            vertical
            inset
          />
          <base-mo00009
            :oneway-model-value="localOneway.mo00009Oneway"
            @click="handleUserInfo"
          />
        </div>
      </c-v-col>
      <c-v-col
        cols="10"
        class="data-cell"
      >
        <base-mo00046
          v-model="refValue.or15183Values.userInfo"
          class="w-60"
          :oneway-model-value="localOneway.mo00046Oneway"
        />
      </c-v-col>
    </c-v-row>
    <!-- 本人の生活歴 -->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.life_history_label') }}
        <div class="d-flex">
          <c-v-divider
            vertical
            inset
          />
          <base-mo00009
            :oneway-model-value="localOneway.mo00009Oneway"
            @click="handleUserSeikatureki"
          />
        </div>
      </c-v-col>
      <c-v-col
        cols="10"
        class="data-cell"
      >
        <base-mo00046
          v-model="refValue.or15183Values.userSeikatureki"
          class="w-60"
          :oneway-model-value="localOneway.mo00046Oneway"
        />
      </c-v-col>
    </c-v-row>
    <!-- 入院前の本人の生活に対する意向ラベル -->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.family_intentions_before_admission') }}<br />
        {{ t('label.intentions_before_admission_label') }}
        <div class="d-flex">
          <c-v-divider
            vertical
            inset
          />
          <base-mo00009
            :oneway-model-value="localOneway.mo00009Oneway"
            @click="handleUserIkouKnj"
          />
        </div>
      </c-v-col>
      <c-v-col
        cols="10"
        class="data-cell"
      >
        <base-mo00046
          v-model="refValue.or15183Values.userIkouKnj"
          class="w-60"
          :oneway-model-value="localOneway.mo00046Oneway"
        />
        <base-mo00018
          v-model="refValue.or15183Values.userIkouCksFlg"
          :oneway-model-value="localOneway.mo00018Oneway1"
        />
      </c-v-col>
    </c-v-row>
    <!-- 入院前の家族の生活に対する意向ラベル -->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.intentions_before_admission') }}<br />
        {{ t('label.intentions_before_admission_label') }}
        <div class="d-flex">
          <c-v-divider
            vertical
            inset
          />
          <base-mo00009
            :oneway-model-value="localOneway.mo00009Oneway"
            @click="handleKazokuIkouKnj"
          />
        </div>
      </c-v-col>
      <c-v-col
        cols="10"
        class="data-cell"
      >
        <base-mo00046
          v-model="refValue.or15183Values.kazokuIkouKnj"
          class="w-60"
          :oneway-model-value="localOneway.mo00046Oneway"
        />
        <base-mo00018
          v-model="refValue.or15183Values.kazokuIkouCksFlg"
          :oneway-model-value="localOneway.mo00018Oneway1"
        />
      </c-v-col>
    </c-v-row>
    <!-- 4.入院前の介護サービスの利用状況について -->
    <c-v-row class="title">
      <c-v-col>
        <base-mo01338 :oneway-model-value="localOneway.mo01338Oneway1"></base-mo01338>
      </c-v-col>
    </c-v-row>
    <!-- 入院前の介護サービスの利用状況 -->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.care_services_utilization') }}<br />
        {{ t('label.care_services_utilization_label') }}
      </c-v-col>
      <c-v-col
        cols="10"
        class="data-cell"
      >
        <div class="d-flex align-center w-40 flex-wrap">
          <base-mo00018
            v-for="(item, index) in localOneway.serviceRiyoDataList"
            :key="index"
            v-model="refValue.or15183Values[item.key]"
            :oneway-model-value="{
              name: item.label,
              hideDetails: true,
              showItemLabel: false,
              itemLabel: '',
              checkboxLabel: item.label,
            }"
          />
          <div class="d-flex align-center">
            <div class="d-flex align-center">
              <c-v-divider
                class="ml-2"
                vertical
                inset
              />
              <base-mo00009
                :oneway-model-value="localOneway.mo00009Oneway"
                @click="handleServiceRiyoMemoKnj"
              />
            </div>
            <base-mo00045
              v-model="refValue.or15183Values.serviceRiyoMemoKnj"
              :oneway-model-value="localOneway.mo00045Oneway"
            />
          </div>
        </div>
      </c-v-col>
    </c-v-row>
    <!-- Or51775: 有機体: GUI00937 入力支援［ケアマネ］をポップアップで起動する。 -->
    <g-custom-or51775
      v-if="showDialogOr51775"
      v-bind="or51775"
      v-model="local.or51775"
      :oneway-model-value="localOneway.or51775Oneway"
      @confirm="handleOr51775Confirm"
    />
  </div>
</template>

<style scoped lang="scss">
.row {
  display: flex;
  align-items: center;
  border-bottom: 1px gainsboro solid;
  border-left: 1px gainsboro solid;
  min-height: 62px;
}

.header-cell {
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 12px !important;
}

.header-title-cell {
  background-color: transparent;
  border-right: 1px gainsboro solid;
  display: grid;
  align-items: center;
}

.data-cell {
  border-left: 1px gainsboro solid;
  border-right: 1px gainsboro solid;
  background: #fff;
  padding: 10px 12px;
  width: 100%;
  min-height: 62px;
  display: grid;
  align-items: center;
}

:deep(.v-input__control) {
  background-color: rgb(var(--v-theme-surface));
}
:deep(.v-selection-control-group--inline) {
  align-items: center;
}
.flex-20 {
  flex: 0 0 20%;
  max-width: 20%;
}
.flex-80 {
  flex: 0 0 80%;
  max-width: 80%;
}
.flex-7 {
  flex: 0 0 7.333333%;
  max-width: 7.3333333333%;
}
.flex-56 {
  flex: 0 0 56%;
  max-width: 56%;
}
.w-40 {
  width: 40%;
}
.title {
  margin-top: 12px;
  background-color: #fff;
  border-left: 1px gainsboro solid;
  border-right: 1px gainsboro solid;
  border-bottom: 1px gainsboro solid;
}
.w-60 {
  width: 60%;
}
.requiredText {
  :deep(.item-label) {
    margin-left: 10px;
    font-size: 18px !important;
    color: red !important;
  }
}
</style>
