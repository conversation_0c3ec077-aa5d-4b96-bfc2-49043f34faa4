<script setup lang="ts">
/**
 * GUI00875:有機体:印刷設定
 * GUI00875_印刷設定
 *
 * @description
 * 印刷設定
 *
 * <AUTHOR>
 */
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or51767Const } from '~/components/custom-components/organisms/Or51767/Or51767.constants'
import { Or51767Logic } from '~/components/custom-components/organisms/Or51767/Or51767.logic'
import type { Or51767Param } from '~/components/custom-components/organisms/Or51767/Or51767.type'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI00875'
// ルーティング
const routing = 'GUI00875/pinia'
// 画面物理名
const screenName = 'GUI00875'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or51767 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00875' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 子コンポーネントのユニークIDを設定する
// or51767.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI00875',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or51767Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or51767Const.CP_ID(1)]: or51767.value,
})

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  Or51767Logic.initialize(or51767.value.uniqueCpId)
}

// ダイアログ表示フラグ
const showDialogOr51767 = computed(() => {
  // Or51767のダイアログ開閉状態
  return Or51767Logic.state.get(or51767.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(Or51767)--期間管理フラグが「管理する」の場合
 *
 */
function onClickOr51767_1() {
  // Or51767のダイアログ開閉状態を更新する
  Or51767Logic.state.set({
    uniqueCpId: or51767.value.uniqueCpId,
    state: {
      isOpen: true,
      param: {
        shokuId: '1',
        prtNo: '1',
        svJigyoId: '1',
        shisetuId: '1',
        tantoId: '1',
        syubetsuId: '2',
        sectionName: 'インターライ方式ケアアセスメント表',
        userId: '3',
        assessmentId: '2',
        svJigyoKnj: '1',
        processYmd: '2025/07/02',
        parentUserIdSelectDataFlag: false,
        focusSettingInitial: ['あ', 'い', 'う', 'え', 'お'],
        selectedUserCounter: '2',
      } as Or51767Param,
    },
  })
}

/**************************************************
 * コンポーネント固有処理
 **************************************************/
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr51767_1"
        >GUI00875_印刷設定</v-btn
      >
      <g-custom-or-51767
        v-if="showDialogOr51767"
        v-bind="or51767"
      />
    </c-v-col>
  </c-v-row>
</template>
