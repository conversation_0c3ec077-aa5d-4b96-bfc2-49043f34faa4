import { Or51732Const } from '../Or51732/Or51732.constants'
import { Or51732Logic } from '../Or51732/Or51732.logic'
import { Or26678Logic } from '../Or26678/Or26678.logic'
import { Or26678Const } from '../Or26678/Or26678.constants'
import { Or51734Const } from './Or51734.constants'
import type { Or51734EventType, Or51734StateType } from './Or51734.type'
import { useInitialize, useOneWayBindAccessor } from '#imports'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'

/**
 * Or51734:有機体:(文字列入力支援) 実施モニタリング記号マスタ
 * GUI01250_実施モニタリング記号マスタ
 *
 * @description
 * initialize処理とpinia領域操作用の処理を提供する
 *
 * <AUTHOR>
 */
export namespace Or51734Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or51734Const.CP_ID(0),
      uniqueCpId,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [
        { cpId: Or26678Const.CP_ID(0) },
        { cpId: Or51732Const.CP_ID(0) },
        { cpId: Or21814Const.CP_ID(1) },
        { cpId: Or21814Const.CP_ID(2) },
        { cpId: Or21814Const.CP_ID(3) },
      ],
    })
    // 子コンポーネントのセットアップ
    Or26678Logic.initialize(childCpIds[Or26678Const.CP_ID(0)].uniqueCpId)
    Or51732Logic.initialize(childCpIds.Or51732.uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(1)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(2)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(3)].uniqueCpId)
    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }
  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or51734StateType>(Or51734Const.CP_ID(0))

  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const event = useOneWayBindAccessor<Or51734EventType>(Or51734Const.CP_ID(0))
}
