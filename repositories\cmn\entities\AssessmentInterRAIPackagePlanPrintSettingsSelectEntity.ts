import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'

/**
 * 帳票INIデータオブジェクトエンティティ
 */
export interface IniDataObjectEntity {
  /**
   * 氏名伏字印刷
   */
  prtName: string
  /**
   * 文書番号印刷
   */
  prtBng: string
  /**
   * 個人情報印刷
   */
  prtKojin: string
}

/**
 * 利用者絞込エンティティ
 */
export interface UserSelEntity {
  /**
   * 利用者ID
   */
  userId: string
}

/**
 * アセスメント履歴エンティティ
 */
export interface AssessmentHistoryEntity {
  /**
   * 期間ID
   */
  sc1Id: string
  /**
   * 計画期間
   */
  planPeriod: string
  /**
   * 計画期間リスト
   */
  planPeriodList: PlanPeriodEntity[]
}

/**
 * 計画期間エンティティ
 */
export interface PlanPeriodEntity {
  /**
   * 履歴ID
   */
  id: string
  /**
   * 選択
   */
  sel: string
  /**
   * 開始日
   */
  startYmd: string
  /**
   * 終了日
   */
  endYmd: string
  /**
   * アセスメントID
   */
  raiId: string
  /**
   * 利用者ID
   */
  userid: string
  /**
   * 調査アセスメント種別
   */
  assType: string
  /**
   * 調査アセスメント種別名
   */
  assTypeKnj: string
  /**
   * 調査日
   */
  assDateYmd: string
  /**
   * 調査者
   */
  assShokuId: string
  /**
   * 調査者名
   */
  fullName: string
}

/**
 * 帳票名称エンティティ
 */
export interface PrtEntity {
  /**
   * 選択
   */
  clicked: string
  /**
   * タイトル
   */
  title: string
  /**
   * インデックス
   */
  index: string
  /**
   * セクション番号
   */
  sectionNo: string
}

/**
 * 出力帳票一覧明細エンティティ
 */
export interface PrtEntity {
  /**
   * インデックス
   */
  index: string

  /**
   * 帳票名
   */
  defPrtTitle: string

  /**
   * 帳票タイトル
   */
  prtTitle: string

  /**
   * セクション番号
   */
  sectionNo: string

  /**
   * 帳票番号
   */
  prtNo: string

  /**
   * プロファイル
   */
  profile: string

  /**
   * 日付表示有無
   */
  prnDate: string

  /**
   * 職員表示有無
   */
  prnshoku: string

  /**
   * オブジェクト名
   */
  dwobject: string

  /**
   * 用紙向き
   */
  prtOrient: string

  /**
   * 用紙サイズ
   */
  prtSize: string

  /**
   * 帳票リスト名
   */
  listTitle: string

  /**
   * 上余白
   */
  mtop: string

  /**
   * 下余白
   */
  mbottom: string

  /**
   * 左余白
   */
  mleft: string

  /**
   * 右余白
   */
  mright: string

  /**
   * ルーラ表示有無
   */
  ruler: string

  /**
   * シリアルフラグ
   */
  serialFlg: string

  /**
   * モードフラグ
   */
  modFlg: string

  /**
   * セクションフラグ
   */
  secFlg: string

  /**
   * 高さ
   */
  serialHeight: string

  /**
   * 印刷行数
   */
  serialPagelen: string

  /**
   * 表示内拡大率
   */
  zoomRate: string

  /**
   * パラメータ01
   */
  param01: string

  /**
   * パラメータ02
   */
  param02: string

  /**
   * パラメータ03
   */
  param03: string

  /**
   * パラメータ04
   */
  param04: string

  /**
   * パラメータ05
   */
  param05: string

  /**
   * パラメータ06
   */
  param06: string

  /**
   * パラメータ07
   */
  param07: string

  /**
   * パラメータ08
   */
  param08: string

  /**
   * パラメータ09
   */
  param09: string

  /**
   * パラメータ10
   */
  param10: string

  /**
   * パラメータ11
   */
  param11: string

  /**
   * パラメータ12
   */
  param12: string

  /**
   * パラメータ13
   */
  param13: string

  /**
   * パラメータ14
   */
  param14: string

  /**
   * パラメータ15
   */
  param15: string

  /**
   * パラメータ16
   */
  param16: string

  /**
   * パラメータ17
   */
  param17: string

  /**
   * パラメータ18
   */
  param18: string

  /**
   * パラメータ19
   */
  param19: string

  /**
   * パラメータ20
   */
  param20: string

  /**
   * パラメータ21
   */
  param21: string

  /**
   * パラメータ22
   */
  param22: string

  /**
   * パラメータ23
   */
  param23: string

  /**
   * パラメータ24
   */
  param24: string

  /**
   * パラメータ25
   */
  param25: string

  /**
   * パラメータ26
   */
  param26: string

  /**
   * パラメータ27
   */
  param27: string

  /**
   * パラメータ28
   */
  param28: string

  /**
   * パラメータ29
   */
  param29: string

  /**
   * パラメータ30
   */
  param30: string

  /**
   * パラメータ31
   */
  param31: string

  /**
   * パラメータ32
   */
  param32: string

  /**
   * パラメータ33
   */
  param33: string

  /**
   * パラメータ34
   */
  param34: string

  /**
   * パラメータ35
   */
  param35: string

  /**
   * パラメータ36
   */
  param36: string

  /**
   * パラメータ37
   */
  param37: string

  /**
   * パラメータ38
   */
  param38: string

  /**
   * パラメータ39
   */
  param39: string

  /**
   * パラメータ40
   */
  param40: string

  /**
   * パラメータ41
   */
  param41: string

  /**
   * パラメータ42
   */
  param42: string

  /**
   * パラメータ43
   */
  param43: string

  /**
   * パラメータ44
   */
  param44: string

  /**
   * パラメータ45
   */
  param45: string

  /**
   * パラメータ46
   */
  param46: string

  /**
   * パラメータ47
   */
  param47: string

  /**
   * パラメータ48
   */
  param48: string

  /**
   * パラメータ49
   */
  param49: string

  /**
   * パラメータ50
   */
  param50: string

  /**
   * 更新回数
   */
  modifiedCnt: string
}
// ---------------------------------------------
/**
 * GUI00828_印刷設定初期情報取得入力エンティティ
 */
export interface AssessmentInterRAIPackagePlanPrintSettingsSelectInEntity extends InWebEntity {
  /** 共通情報.システムコード */
  sysCd: string
  /** 共通情報.システム略称 */
  sysRyaku: string
  /** 共通情報.法人ID */
  houjinId: string
  /** 親画面.施設ID */
  shisetuId: string
  /** 親画面.事業者ID */
  svJigyoId: string
  /** 共通情報.適用事業所IDリスト */
  svJigyoIds: string[]
  /** 共通情報.職員ID */
  shokuId: string
  /** 種別ID：共通情報.種別ID */
  syubetsuId: string
  /** 親画面.利用者ID */
  userId: string
  /** 親画面.担当者ID */
  tantoId: string
  /** 親画面.セクション名 */
  sectionName: string
  /** インデックス_0 */
  index: string
  /** 個人情報使用フラグ_0※0：不使用、1：使用 */
  kojinhogoUsedFlg: string
  /** 個人情報番号_0※0：主に日誌以外、1：主に日誌系 */
  sectionAddNo: string
}

/**
 * GUI00828_印刷設定初期情報取得出力エンティティ
 */
export interface AssessmentInterRAIPackagePlanPrintSettingsSelectOutEntity extends OutWebEntity {
  /**
   * data
   */
  data: {
    /** 印刷設定情報リスト*/
    prtList: PrtListData[]
    /** システムINI情報*/
    sysIniInfo: SysIniInfoData
    /** 担当ケアマネ*/
    tantoKnj: string
    /** 期間管理フラグ*/
    kikanFlg: string
    /** アセスメント履歴リスト*/
    periodHistoryList: PeriodHistoryListData[]
  }
}

/**
 * GUI00828_印刷設定履歴リスト取得入力エンティティ
 */
export interface AssessmentInterRAIPackagePlanPrintSettingsHistorySelectInEntity
  extends InWebEntity {
  /** 親画面.事業者ID */
  svJigyoId: string
  /** 利用者一覧.選択行.利用者ID */
  userId: string
  /** 計画期間管理フラグ */
  kikanFlg: string
}

/**
 * GUI00828_印刷設定履歴リスト取得出力エンティティ
 */
export interface AssessmentInterRAIPackagePlanPrintSettingsHistorySelectOutEntity
  extends OutWebEntity {
  /**
   * data
   */
  data: {
    /** アセスメント履歴リスト*/
    periodHistoryList: PeriodHistoryListData[]
  }
}
/**
 * 印刷設定情報リスト
 */
export interface PrtListData {
  /** インデックス*/
  index: string
  /** 帳票名*/
  defPrtTitle: string
  /** 帳票タイトル*/
  prtTitle: string
  /** セクション番号*/
  sectionNo: string
  /** 帳票番号*/
  prtNo: string
  /** プロファイル*/
  profile: string
  /** 日付表示有無*/
  prnDate: string
  /** 職員表示有無*/
  prnshoku: string
  /** オブジェクト名*/
  dwobject: string
  /** 用紙向き*/
  prtOrient: string
  /** 用紙サイズ*/
  prtSize: string
  /** 帳票リスト名*/
  listTitle: string
  /** 上余白*/
  mtop: string
  /** 下余白*/
  mbottom: string
  /** 左余白*/
  mleft: string
  /** 右余白*/
  mright: string
  /** ルーラ表示有無*/
  ruler: string
  /** シリアルフラグ*/
  serialFlg: string
  /** モードフラグ*/
  modFlg: string
  /** セクションフラグ*/
  secFlg: string
  /** 高さ*/
  serialHeight: string
  /** 印刷行数*/
  serialPagelen: string
  /** 表示内拡大率*/
  zoomRate: string
  /** パラメータ01*/
  param01: string
  /** パラメータ02*/
  param02: string
  /** パラメータ03*/
  param03: string
  /** パラメータ04*/
  param04: string
  /** パラメータ05*/
  param05: string
  /** パラメータ06*/
  param06: string
  /** パラメータ07*/
  param07: string
  /** パラメータ08*/
  param08: string
  /** パラメータ09*/
  param09: string
  /** パラメータ10*/
  param10: string
  /** パラメータ11*/
  param11: string
  /** パラメータ12*/
  param12: string
  /** パラメータ13*/
  param13: string
  /** パラメータ14*/
  param14: string
  /** パラメータ15*/
  param15: string
  /** パラメータ16*/
  param16: string
  /** パラメータ17*/
  param17: string
  /** パラメータ18*/
  param18: string
  /** パラメータ19*/
  param19: string
  /** パラメータ20*/
  param20: string
  /** パラメータ21*/
  param21: string
  /** パラメータ22*/
  param22: string
  /** パラメータ23*/
  param23: string
  /** パラメータ24*/
  param24: string
  /** パラメータ25*/
  param25: string
  /** パラメータ26*/
  param26: string
  /** パラメータ27*/
  param27: string
  /** パラメータ28*/
  param28: string
  /** パラメータ29*/
  param29: string
  /** パラメータ30*/
  param30: string
  /** パラメータ31*/
  param31: string
  /** パラメータ32*/
  param32: string
  /** パラメータ33*/
  param33: string
  /** パラメータ34*/
  param34: string
  /** パラメータ35*/
  param35: string
  /** パラメータ36*/
  param36: string
  /** パラメータ37*/
  param37: string
  /** パラメータ38*/
  param38: string
  /** パラメータ39*/
  param39: string
  /** パラメータ40*/
  param40: string
  /** パラメータ41*/
  param41: string
  /** パラメータ42*/
  param42: string
  /** パラメータ43*/
  param43: string
  /** パラメータ44*/
  param44: string
  /** パラメータ45*/
  param45: string
  /** パラメータ46*/
  param46: string
  /** パラメータ47*/
  param47: string
  /** パラメータ48*/
  param48: string
  /** パラメータ49*/
  param49: string
  /** パラメータ50*/
  param50: string
  /** 更新回数*/
  modifiedCnt: string
}

/**
 * システムINI情報
 */
export interface SysIniInfoData {
  /** 氏名伏字設定フラグ */
  amikakeFlg: string
  /** 氏名伏字更新回数 */
  amikakeModifiedCnt: string
  /** 文章管理設定フラグ */
  iso9001Flg: string
  /** 文章管理更新回数 */
  iso9001ModifiedCnt: string
  /** 個人情報設定フラグ */
  kojinhogoFlg: string
  /** 個人情報更新回数 */
  kojinhogoModifiedCnt: string
}

/**
 * アセスメント履歴リスト
 */
export interface PeriodHistoryListData {
  /** 期間ID*/
  sc1Id: string
  /** 開始日*/
  startYmd: string
  /** 終了日*/
  endYmd: string
  /** 履歴情報リスト*/
  // historyList: HistoryListData[]
  /** 選択*/
  sel: string
  /** アセスメントID*/
  raiId: string
  /** 利用者ID*/
  userid: string
  /** 作成日*/
  createYmd: string
  /** 作成者*/
  shokuinKnj: string
  /** ケース番号*/
  caseNo: string
  /** 改訂*/
  kaitei: string
}

/**
 * 履歴情報リスト
 */
export interface HistoryListData {
  /** 選択*/
  sel: string
  /** アセスメントID*/
  raiId: string
  /** 利用者ID*/
  userid: string
  /** 作成日*/
  createYmd: string
  /** 作成者*/
  shokuinKnj: string
  /** ケース番号*/
  caseNo: string
  /** 改訂*/
  kaitei: string
}

/**
 * 印刷設定情報保入力エンティティ
 */
export interface IPrintSettingsUpdateInEntity extends InWebEntity {
  /**
   * システムコード
   */
  sysCd: string
  /**
   * システム略称
   */
  sysRyaku: string
  /**
   * 機能名
   */
  kinounameKnj: string
  /**
   * 法人ID
   */
  houjinId: string
  /**
   * 施設ID
   */
  shisetuId: string
  /**
   * 事業者ID
   */
  svJigyoId: string
  /**
   * 職員ID
   */
  shokuId: string
  /**
   * プロファイル
   */
  choPro: string
  /**
   * 出力帳票一覧明細
   */
  choPrtList: PrtEntity[]
}

/**
 * 印刷設定情報保存出力エンティティ
 */
export interface IPrintSettingsUpdateOutEntity extends OutWebEntity {
  /**
   * data
   */
  data: {
    status: string
  }
}

/**
 * 印刷設定明細取得入力エンティティ
 */
export interface IPrintSettingsDetailSelectInEntity extends InWebEntity {
  /**
   * システムコード
   */
  sysCd: string
  /**
   * システム略称
   */
  sysRyaku: string
  /**
   * 機能名
   */
  kinounameKnj: string
  /**
   * 法人ID
   */
  houjinId: string
  /**
   * 施設ID
   */
  shisetuId: string
  /**
   * 事業者ID
   */
  svJigyoId: string
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 職員ID
   */
  shokuId: string
  /**
   * 担当者ID
   */
  tantoId: string
}

/**
 * 印刷設定明細取得出力エンティティ
 */
export interface IPrintSettingsDetailSelectOutEntity extends OutWebEntity {
  /**
   * data
   */
  data: {
    /**
     * 出力帳票印刷情報リスト
     */
    choPrtList: PrtEntity[]
  }
}
/**
 * 印刷設定対象一覧情報取得入力エンティティ
 */
export interface IAssessmentInterRAIPackagePlanPrintSettingsSubjectSelectInEntity
  extends InWebEntity {
  /**
   *帳票番号
   */
  prtNo: string
  /**
   *事業者ID
   */
  svJigyoId: string
  /**
   *基準日
   */
  kijunbi: string
  /**
   *選択履歴のデータのみ印刷
   */
  onlySelectHistoryPrt: string
  /**
   *利用者リスト
   */
  userList: UserlistData[]
  /**
   *履歴リスト
   */
  rirekiList: RirekiListData[]
}

/**
 *利用者リスト
 */
export interface UserlistData {
  /**
   *利用者ID
   */
  userId: string
  /**
   *利用者名
   */
  userName: string
}
/**
 *履歴リスト
 */
export interface RirekiListData {
  /**
   *アセスメント履歴ID
   */
  assId: string
  /**
   *作成日
   */
  createYmd: string
  /**
   *作成者
   */
  shokuId: string
  /**
   *ケース番号
   */
  caseNo: string
}

/**
 * 印刷設定対象一覧情報取得出力エンティティ
 */
export interface IAssessmentInterRAIPackagePlanPrintSettingsSubjectSelectOutEntity
  extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /**
     * 印刷設定情報リスト
     */
    printSubjectHistoryList: PrintSubjectHistoryListData[]
  }
}

/**
 *印刷対象履歴リスト
 */
export interface PrintSubjectHistoryListData {
  /**
   *利用者ID
   */
  userId: string
  /**
   *利用者名
   */
  userName: string
  /**
   *初回作成日
   */
  shokaiYmd: string
  /**
   *ケース番号
   */
  caseNo: string
  /**
   *アセスメント履歴ID[3]
   */
  assId: string[]
  /**
   *作成日[3]
   */
  createYmd: string[]
  /**
   *障害者老人日常生活自立度[3]
   */
  adl1: string[]
  /**
   * 認知症老人日常背かつ自立度[3]
   */
  adl2: string[]
  /**
   *身体測定日[3]
   */
  shintaiYmd: string[]
  /**
   *結果
   */
  result: string
}

/**
 * 帳票データ
 */
export interface PrintOptionEntity {
  /** 画面.記入用シートを印刷する */
  emptyFlg: string
  /** 画面.選択履歴のデータのみを印刷する */
  selectedHistoryPrintFlg: string
  /** 画面.履歴情報を印刷する（基準日、作成者） */
  historyPrintFlg: string
  /** 画面.選択項目を囲む色 */
  color: string
  /** 画面.説明文を印刷する */
  descriptionPrintFlg: string
}

/**
 * json
 */
export interface AssessmentSheetRegionReportInEntity {
  /** 0：画面.履歴選択複数、1：画面.利用者選択複数、2：以外の場合 */
  type: string
  /** 「AC020-5-2 また AC020-5-3」で取得した印刷設定情報リスト */
  historyList: AssessmentSheetRegionReportHistoryList
}

/**
 * 「AC020-5-2 また AC020-5-3」で取得した印刷設定情報リスト
 */
export interface AssessmentSheetRegionReportHistoryList {
  /** 補足説明を参照 */
  reportId: string
  /** 出力タイプ */
  outputType: string
  /** 利用者名 */
  userName: string
  /** 結果 */
  result: string
  /** 帳票データ */
  reportData: AssessmentSheetRegionReportReportData
}

/**
 * 帳票データ
 */
export interface AssessmentSheetRegionReportReportData {
  /** 親画面.事業所名 */
  svJigyoKnj: string
  /** 印刷設定 */
  printConfigure: PrintConfigure
  /** 印刷オプション */
  printOption: PrintOption
  /** 印刷対象履歴リスト */
  printHistoryList: PrintHistoryList[]
}

/**
 * 印刷設定
 */
export interface PrintConfigure {
  /** 画面.指定日印刷区分 */
  shiTeiKubun: string
  /** 画面.指定日 */
  shiTeiDate: string
}

/**
 * 印刷オプション
 */
export interface PrintOption {
  /** 画面.記入用シートを印刷する */
  emptyFlg: string
  /** 画面.選択履歴のデータのみを印刷する */
  selectedHistoryPrintFlg: string
  /** 画面.履歴情報を印刷する（基準日、作成者） */
  historyPrintFlg: string
  /** 画面.選択項目を囲む色 */
  color: string
  /** 画面.説明文を印刷する */
  descriptionPrintFlg: string
}

/**
 * 印刷対象履歴リスト
 */
export interface PrintHistoryList {
  /** 印刷対象履歴リスト.利用者ID */
  userId: string
  /** 印刷対象履歴リスト.利用者名 */
  userName: string
  /** 印刷対象履歴リスト.初回作成日 */
  shokaiYmd: string
  /** 印刷対象履歴リスト.ケース番号 */
  caseNo: string
  /** 印刷対象履歴リスト.アセスメント履歴ID[3] */
  assId: string[]
  /** 印刷対象履歴リスト.作成日[3] */
  createYmd: string[]
  /** 印刷対象履歴リスト.障害者老人日常生活自立度[3] */
  adl1: string[]
  /** 印刷対象履歴リスト.認知症老人日常背かつ自立度[3] */
  adl2: string[]
  /** 印刷対象履歴リスト.身体測定日[3] */
  shintaiYmd: string[]
  /** 印刷対象履歴リスト.結果 */
  result: string
  /** 出力帳票印刷情報リスト */
  outputLedgerPrintList: PrtEntity[]
}
