import { Or10929Const } from './Or10929.constants'
import type { Or10929StateType } from './Or10929.type'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'

/**
 * Or10929:有機体:［履歴選択］画面 アセスメント(インターライ)
 * GUI00792_［履歴選択］画面 アセスメント(インターライ)
 *
 * @description
 * initialize処理とpinia領域操作用の処理を提供する
 *
 * <AUTHOR>
 */
export namespace Or10929Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or10929Const.CP_ID(0),
      uniqueCpId,
      initTwoWayValue: {
        items: [],
      },
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [],
    })

    // 子コンポーネントのセットアップ

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }

  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or10929StateType>(Or10929Const.CP_ID(0))
}
