<script setup lang="ts">
/**
 * Or28401:有機体:（日課表）印刷設定モーダル
 * GUI01001_印刷設定POP画面
 *
 * @description
 * GUI01001_印刷設定POP画面の処理
 *
 * <AUTHOR>
 */
import { onMounted, reactive, ref, watch, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or18615Const } from '../Or18615/Or18615.constants'
import { Or18615Logic } from '../Or18615/Or18615.logic'
import { Or20313Const } from '../Or20313/Or20313.constants'
import { Or20313Logic } from '../Or20313/Or20313.logic'
import { Or26326Const } from '../Or26326/Or26326.constants'
import { Or26326Logic } from '../Or26326/Or26326.logic'
import { Or26328Const } from '../Or26328/Or26328.constants'
import { Or26328Logic } from '../Or26328/Or26328.logic'
import { Or26331Const } from '../Or26331/Or26331.constants'
import { Or26331Logic } from '../Or26331/Or26331.logic'
import { Or28780Const } from '../Or28780/Or28780.constants'
import { Or28780Logic } from '../Or28780/Or28780.logic'
import { OrX0130Const } from '../OrX0130/OrX0130.constants'
import { OrX0130Logic } from '../OrX0130/OrX0130.logic'
import { OrX0143Const } from '../OrX0143/OrX0143.constants'
import { OrX0143Logic } from '../OrX0143/OrX0143.logic'
import type { OrX0143TableData } from '../OrX0143/OrX0143.type'
import type { Or28401ScreenType } from './Or28401.type'
import { Or28401Const } from './Or28401.constants'
import {
  useScreenOneWayBind,
  useSetupChildProps,
  useScreenUtils,
  usePrint,
  type HistoryInfo,
} from '#imports'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Or28401StateType } from '~/types/cmn/business/components/Or28401Type'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type {
  DailyTaskTablePrintSettingsInitUpdateInEntity,
  DailyTaskTablePrintSettingsInitUpdateOutEntity,
  KikanRirekiData,
} from '~/repositories/cmn/entities/DailyTaskTablePrintSettingsInitUpdateEntity'
import type { Mo01334Items, Mo01334OnewayType } from '~/types/business/components/Mo01334Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { Mo00039OnewayType } from '@/types/business/components/Mo00039Type'
import type { Or18615OnewayType, Or18615Type } from '~/types/cmn/business/components/Or18615Type'
import type { Or26326Type } from '~/types/cmn/business/components/Or26326Type'
import type { Or28780Type } from '~/types/cmn/business/components/Or28780Type'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Or26331OnewayType, Or26331Type } from '~/types/cmn/business/components/Or26331Type'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import type { OrX0143OnewayType } from '~/types/cmn/business/components/OrX0143Type'
import type { Or26328OnewayType, Or26328Type } from '~/types/cmn/business/components/Or26328Type'
import type { IPrintInfo, PrintOnewayEntity } from '~/repositories/cmn/entities/PrintSelectEntity'
import type { DailyScheduleImageInitMasterInfo } from '~/repositories/cmn/entities/DailyScheduleImageInitSelectEntity'
import type { PrintUserChangeSelectInEntity } from '~/repositories/cmn/entities/PrintUserChangeSelectEntity'
import type { DailyTaskTablePrintSettingsUserChangeSelectOutEntity } from '~/repositories/cmn/entities/DailyTaskTablePrintSettingsUserChangeSelectEntity'
import type { PrintCloseUpdateEntity } from '~/repositories/cmn/entities/PrintCloseUpdateEntity'
import type {
  DailyTaskTablePrintSubjectSelectInEntity,
  DailyTaskTablePrintSettingsSubjectSelectOutEntity,
} from '~/repositories/cmn/entities/DailyTaskTablePrintSubjectSelectEntity'
import type { PrtHistoryInfo } from '~/repositories/cmn/entities/PrintSubjectSelectEntity'
import { hasPrintAuth } from '~/utils/useCmnAuthz'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { OrX0117Const } from '~/components/custom-components/organisms/OrX0117/OrX0117.constants'
import { OrX0117Logic } from '~/components/custom-components/organisms/OrX0117/OrX0117.logic'
import type { OrX0117History, OrX0117OnewayType } from '~/types/cmn/business/components/OrX0117Type'
import { reportOutputType } from '~/utils/useReportUtils'

const { t } = useI18n()
const { setChildCpBinds } = useScreenUtils()

// 印刷共通処理
const printCom = usePrint()
/************************************************
 * Props
 ************************************************/
interface Props {
  onewayModelValue: PrintOnewayEntity<DailyScheduleImageInitMasterInfo>
  uniqueCpId: string
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
// 利用者列幅
const userCols = ref<number>(6)
const localOneway = reactive({
  or28401Oneway: {
    ...props.onewayModelValue,
  } as PrintOnewayEntity<DailyScheduleImageInitMasterInfo>,
  or26326Oneway: {
    headers: [
      {
        title: t('label.report'),
        key: 'prtTitle',
        sortable: false,
        minWidth: '100',
      },
    ],
    items: [],
    height: 655,
  } as Mo01334OnewayType,
  or26328Oneway: {
    maxLength: '128',
  } as Or26328OnewayType,
  // 日付印刷
  mo00039OneWay: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  //印刷オプション
  or18615OneWay: {
    mo00018OneWayChangeTitle: {
      isVerticalLabel: false,
    } as Mo00018OnewayType,
    mo00045OnewayTextInput: {
      isVerticalLabel: false,
      maxLength: '2',
    } as Mo00045OnewayType,
  } as Or18615OnewayType,
  //利用者選択方法セクション
  or26331OneWay: {
    mo00039OneWayHistorySelectType: {} as Mo00039OnewayType,
    mo00039OneWayUserSelectType: {} as Mo00039OnewayType,
  } as Or26331OnewayType,
  mo00024Oneway: {
    maxWidth: '1420px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or28401',
      toolbarTitle: t('label.print-set'),
      toolbarName: 'Or28401ToolBar',
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
    disabledCloseBtnEvent: true,
  } as Mo00024OnewayType,
  orX0130Oneway: {
    selectMode: Or28401Const.DEFAULT.TANI,
    tableStyle: 'width:270px',
  } as OrX0130OnewayType,
  orX0143Oneway: {
    // 期間管理フラグ
    kikanFlg: '1',
    // 単一複数フラグ(0:単一,1:複数)
    singleFlg: Or28401Const.DEFAULT.TANI,
    tableStyle: 'width:335px',
    kikanRirekiTitleList: [
      // 作成日
      { title: 'create-date', width: '93', key: 'createYmd' },
      // 作成者
      { title: 'author', width: '171', key: 'shokuKnj' },
    ],
    // 履歴情報
    rirekiList: [] as OrX0143TableData[],
  } as OrX0143OnewayType,
  // 記入用シートを印刷するチェックボックス
  mo00018OneWayPrintTheForm: {
    checkboxLabel: t('label.printer-entry-sheet'),
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  param05OneWay: {
    checkboxLabel: t('label.history-info-print-base-author-no'),
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  param07OneWay: {
    checkboxLabel: t('label.mono-chrome-printing-mode'),
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  param08OneWay: {
    checkboxLabel: t('label.on-demand-other-services-printing-mode'),
    isVerticalLabel: false,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // 閉じるボタン設置
  mo00611Oneway: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,

  // PDFダウンロードボタン設置
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
    //共通処理の印刷権限チェックを行う
    disabled: false,
  } as Mo00609OnewayType,
  orX0117Oneway: {
    reportId: '',
    type: '1',
    outputType: reportOutputType.DOWNLOAD,
    historyList: [] as OrX0117History[],
    replaceKey: 'printHistoryList',
  } as OrX0117OnewayType,
})

const local = reactive({
  mo00024: {
    isOpen: Or28401Const.DEFAULT.IS_OPEN,
  },
  or28401: {} as Or28401ScreenType,
  //出力帳票名一覧
  or26326Type: {
    mo01334Type: {},
  } as Or26326Type,
  or26328Type: { titleInput: {} } as Or26328Type,
  or28780Type: { mo00020Type: {} } as Or28780Type,
  or18615Type: {
    mo00018TypeChangeTitle: {} as Mo00018Type,
    mo00045Type: {},
  } as Or18615Type,
  or26331Type: { mo00020TypeKijunbi: { value: props.onewayModelValue.sysYmd } } as Or26331Type,
  mo00018TypePrintTheForm: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypeParam05: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypeParam07: {
    modelValue: false,
  } as Mo00018Type,
  mo00018TypeParam08: {
    modelValue: false,
  } as Mo00018Type,
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or28401StateType>({
  cpId: Or28401Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      local.mo00024.isOpen = value ?? Or28401Const.DEFAULT.IS_OPEN
    },
  },
})
const or20313_1 = ref({ uniqueCpId: '' })
const or21813_1 = ref({ uniqueCpId: '' })
const or21814_1 = ref({ uniqueCpId: '' })
const or18615_1 = ref({ uniqueCpId: '' })
const or26326_1 = ref({ uniqueCpId: '' })
const or26328_1 = ref({ uniqueCpId: '' })
const or26331_1 = ref({ uniqueCpId: '' })
const or28780_1 = ref({ uniqueCpId: '' })
const orX0117_1 = ref({ uniqueCpId: '' })
const orX0130_1 = ref({ uniqueCpId: '' })
const orX0143_1 = ref({ uniqueCpId: '' })

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or20313Const.CP_ID(1)]: or20313_1.value,
  [Or21813Const.CP_ID(1)]: or21813_1.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or18615Const.CP_ID(1)]: or18615_1.value,
  [Or26326Const.CP_ID(1)]: or26326_1.value,
  [Or26328Const.CP_ID(1)]: or26328_1.value,
  [Or26331Const.CP_ID(1)]: or26331_1.value,
  [Or28780Const.CP_ID(1)]: or28780_1.value,
  [OrX0117Const.CP_ID(1)]: orX0117_1.value,
  [OrX0130Const.CP_ID(1)]: orX0130_1.value,
  [OrX0143Const.CP_ID(1)]: orX0143_1.value,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  await initCodes()
  // 初期情報取得
  await getInitDataInfo()
})

/**
 * 担当ケアマネ選択の監視
 */
watch(
  () => Or20313Logic.event.get(or20313_1.value.uniqueCpId),
  (newValue) => {
    if (newValue?.filterFlg && newValue.filterParams) {
      // 画面.担当ケアマネIDに戻り値.担当ケアマネIDを設定する
      local.or28401.tantoId = newValue.filterParams.tantoId
      //利用者一覧情報を再表示する。TODO
      // 子コンポーネントのflgをリセットする
      Or20313Logic.event.set({
        uniqueCpId: or20313_1.value.uniqueCpId,
        events: {
          filterFlg: false,
          filterParams: undefined,
        },
      })
    }
  }
)

/**
 * 利用者選択ラジオボタン選択の監視
 */
watch(
  () => Or26331Logic.data.get(or26331_1.value.uniqueCpId)?.mo00039OneWayUserSelectType,
  (newValue) => {
    if (newValue) {
      local.or26331Type.mo00039OneWayUserSelectType = newValue
      if (newValue === Or28401Const.DEFAULT.TANI) {
        // 選択モート
        localOneway.orX0130Oneway.selectMode = Or28401Const.DEFAULT.TANI
        userCols.value = 6
        //画面.記入用シートを印刷するを活性にする。
        localOneway.mo00018OneWayPrintTheForm.disabled = false
      } else if (newValue === Or28401Const.DEFAULT.HUKUSUU) {
        // 選択モート
        localOneway.orX0130Oneway.selectMode = OrX0130Const.DEFAULT.HUKUSUU
        // 画面.記入用シートを印刷するをチェックオフ、非活性にする
        local.mo00018TypePrintTheForm.modelValue = false
        localOneway.mo00018OneWayPrintTheForm.disabled = true
        userCols.value = 11
      }
    }
  }
)

/**
 * 履歴選択ラジオボタン選択の監視
 */
watch(
  () => Or26331Logic.data.get(or26331_1.value.uniqueCpId)?.mo00039OneWayHistorySelectType,
  (newValue) => {
    if (newValue) {
      // 履歴選択方法が「単一」の場合
      if (newValue === Or28401Const.DEFAULT.TANI) {
        // 単一複数フラグ
        localOneway.orX0143Oneway.singleFlg = OrX0143Const.DEFAULT.TANI
        //画面.記入用シートを印刷するを活性にする。
        localOneway.mo00018OneWayPrintTheForm.disabled = false
      } else if (newValue === Or28401Const.DEFAULT.HUKUSUU) {
        // 単一複数フラグ
        localOneway.orX0143Oneway.singleFlg = OrX0143Const.DEFAULT.HUKUSUU
        // 画面.記入用シートを印刷するをチェックオフ、非活性にする
        local.mo00018TypePrintTheForm.modelValue = false
        localOneway.mo00018OneWayPrintTheForm.disabled = true
      }
    }
  }
)

/**
 * 「利用者選択」の監視
 */
watch(
  () => OrX0130Logic.event.get(orX0130_1.value.uniqueCpId),
  async (newValue, oldValue) => {
    if (
      !oldValue?.userList?.at(0)?.userId ||
      newValue?.userList?.at(0)?.userId === oldValue?.userList?.at(0)?.userId
    ) {
      return
    }
    if (newValue?.clickFlg && localOneway.orX0130Oneway.selectMode === Or28401Const.DEFAULT.TANI) {
      // 利用者選択
      // 利用者選択方法が「単一」の場合
      await getHistoricalInfoList(newValue.userList[0].userId)
    }
  }
)

/**
 * 「出力帳票名」選択
 */
watch(
  () => Or26326Logic.data.get(or26326_1.value.uniqueCpId)?.mo01334Type.value,
  (newValue, oldValue) => {
    if (oldValue) {
      setInputValue(oldValue)
    }
    if (newValue && oldValue !== newValue) {
      local.or26326Type.mo01334Type.value = newValue
      setPrintData()
    }
  }
)

/**
 * 印刷設定情報設定
 */
const setPrintData = () => {
  const selectedRow = local.or28401.prtList.find(
    (x) => x.prtNo === local.or26326Type.mo01334Type.value
  )
  //印刷設定情報リスト.帳票名
  local.or26326Type.mo01334Type.value = selectedRow?.prtNo ?? ''
  //印刷設定情報リストから選択行.帳票タイトル
  local.or26328Type.titleInput.value = selectedRow?.prtTitle ?? ''
  //印刷設定情報リストから選択行.日付表示有無
  local.or28780Type.mo00039Type = selectedRow?.prnDate ?? ''
  //システム日付
  local.or28780Type.mo00020Type.value = props.onewayModelValue.sysYmd
  //印刷設定情報リストから選択行.パラメータ03
  local.or18615Type.mo00018TypeChangeTitle.modelValue = selectedRow?.param03 === '1'
  //印刷設定情報リストから選択行.パラメータ04
  local.or18615Type.mo00045Type.value = selectedRow?.param04 ?? ''
  //印刷設定情報リストから選択行.パラメータ05
  local.mo00018TypeParam05.modelValue = selectedRow?.param05 === '1'
  //印刷設定情報リストから選択行.パラメータ07
  local.mo00018TypeParam07.modelValue = selectedRow?.param07 !== 'OFF'
  //印刷設定情報リストから選択行.パラメータ08
  local.mo00018TypeParam08.modelValue = selectedRow?.param08 === '1'
  //「単一」を選択
  local.or26331Type.mo00039OneWayUserSelectType = Or28401Const.DEFAULT.TANI
  local.or26331Type.mo00039OneWayHistorySelectType = Or28401Const.DEFAULT.TANI
  setChildCpBinds(props.uniqueCpId, {
    [Or26326Const.CP_ID(1)]: {
      twoWayValue: {
        mo01334Type: local.or26326Type.mo01334Type,
      },
    },
    [Or26328Const.CP_ID(1)]: {
      twoWayValue: {
        titleInput: local.or26328Type.titleInput,
      },
    },
    [Or28780Const.CP_ID(1)]: {
      twoWayValue: {
        mo00039Type: local.or28780Type.mo00039Type,
        mo00020Type: local.or28780Type.mo00020Type,
      },
    },
    [Or18615Const.CP_ID(1)]: {
      twoWayValue: {
        mo00018TypeChangeTitle: local.or18615Type.mo00018TypeChangeTitle,
        mo00045Type: local.or18615Type.mo00045Type,
      },
    },
    [Or26331Const.CP_ID(1)]: {
      twoWayValue: {
        mo00039OneWayUserSelectType: local.or26331Type.mo00039OneWayUserSelectType,
        mo00020TypeKijunbi: local.or26331Type.mo00020TypeKijunbi,
        mo00039OneWayHistorySelectType: local.or26331Type.mo00039OneWayHistorySelectType,
      },
    },
  })
}

// ダイアログ表示フラグ
const showDialogOrx0117 = computed(() => {
  // OrX0117のダイアログ開閉状態
  return OrX0117Logic.state.get(orX0117_1.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({
    selectCodeKbnList: [
      {
        mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY,
        addBlank: false,
        targetDate: props.onewayModelValue.sysYmd,
      },
      {
        mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY,
        addBlank: false,
        targetDate: props.onewayModelValue.sysYmd,
      },
    ],
  })

  // コード取得
  // 日付印刷区分
  localOneway.mo00039OneWay.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )
  //単複数選択区分
  localOneway.or26331OneWay.mo00039OneWayUserSelectType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )
  //単複数選択区分
  localOneway.or26331OneWay.mo00039OneWayHistorySelectType.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )
}

/**
 * 初期情報取得
 */
const getInitDataInfo = async () => {
  const inputData: DailyTaskTablePrintSettingsInitUpdateInEntity = {
    // セクション名
    sectionName: localOneway.or28401Oneway.sectionName,
    // 職員ID
    shokuId: localOneway.or28401Oneway.shokuId,
    // 法人ID
    houjinId: localOneway.or28401Oneway.houjinId,
    //施設ID
    shisetuId: localOneway.or28401Oneway.shisetuId,
    // 事業者ID
    svJigyoId: localOneway.or28401Oneway.svJigyoId,
    // システムコード
    gsyscd: localOneway.or28401Oneway.systemCode,
    // 利用者ID
    userId: localOneway.or28401Oneway.userId,
  }
  // 印刷設定画面初期情報を取得する。
  const ret: DailyTaskTablePrintSettingsInitUpdateOutEntity = await printCom.doPrintGet<
    DailyTaskTablePrintSettingsInitUpdateInEntity,
    IPrintInfo,
    KikanRirekiData,
    DailyTaskTablePrintSettingsInitUpdateOutEntity
  >(inputData, 'dailyTaskTablePrintSettingsInitUpdate')
  // 印刷設定情報リスト、期間履歴情報リスト、期間管理フラグを取得
  printCom.doGetPrintData<
    DailyTaskTablePrintSettingsInitUpdateOutEntity,
    IPrintInfo,
    KikanRirekiData
  >(ret, local.or28401)
  localOneway.or26326Oneway.items = local.or28401.prtList.map((item) => {
    return {
      id: item.prtNo,
      mo01337OnewayReport: {
        value: item.defPrtTitle,
        unit: '',
      } as Mo01337OnewayType,
      ...item,
    } as Mo01334Items
  })
  //印刷設定情報に一件目の印刷設定情報リストを設定する。
  local.or26326Type.mo01334Type.value = ret.data.prtList.at(0)?.prtNo ?? ''
  //履歴一覧情報を表示する。
  localOneway.orX0143Oneway.rirekiList = ret.data.kikanRirekiList
  //画面.履歴一覧に親画面.履歴ID対応するレコードを選択する
  localOneway.orX0143Oneway.rirekiId = props.onewayModelValue.rirekiId
  setPrintData()
}

/**
 * 履歴情報を取得する
 *
 * @param userId - 利用者ID
 */
async function getHistoricalInfoList(userId: string) {
  localOneway.orX0143Oneway.rirekiList = []
  // バックエンドAPIから初期情報取得
  const kikanRirekiList = await printCom.doUserClick<
    PrintUserChangeSelectInEntity,
    KikanRirekiData,
    DailyTaskTablePrintSettingsUserChangeSelectOutEntity
  >(
    {
      // 事業所ＩＤ:親画面.事業所ＩＤ
      svJigyoId: props.onewayModelValue.svJigyoId,
      // 利用者ID:画面.利用者一覧に選択行の利用者ID
      userId: userId,
    },
    'dailyTaskTablePrintSettingsUserChangeSelect'
  )
  //履歴一覧情報を表示する。
  localOneway.orX0143Oneway.rirekiList = kikanRirekiList
}

/**
 * エラーダイアログの開閉
 *
 * @param text - メッセージ
 *
 * @param btn - ボタンラベル
 *
 * @param isError - エラーかどうか
 *
 * @returns ダイアログの選択結果（yes）
 */
const showMessageBox = async (text: string, btn: string, isError = true) => {
  if (isError) {
    return await openErrorDialog(text, btn)
  } else {
    return await openInfoDialog(text)
  }
}

/**
 * ダイアログの開閉
 *
 * @param text - メッセージ
 *
 * @param btn - ボタンラベル
 */
function openErrorDialog(text: string, btn: string) {
  Or21813Logic.state.set({
    uniqueCpId: or21813_1.value.uniqueCpId,
    state: {
      dialogTitle: t('label.error'),
      dialogText: t(text),
      firstBtnType: 'normal1',
      firstBtnLabel: t(btn) ?? t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813_1.value.uniqueCpId)

        // 確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(event?.firstBtnClickFlg ? Or28401Const.YES : undefined)
      },
      { once: true }
    )
  })
}

/**
 * ダイアログの開閉
 *
 * @param text - メッセージ
 */
function openInfoDialog(text: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      dialogTitle: t('label.error'),
      dialogText: t(text),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(event?.firstBtnClickFlg ? Or28401Const.YES : undefined)
      },
      { once: true }
    )
  })
}

/**
 * 「PDFダウンロード」ボタン押下
 */
async function print() {
  //利用者一覧
  const userList = OrX0130Logic.event.get(orX0130_1.value.uniqueCpId)?.userList
  //履歴一覧
  const rirekiList = OrX0143Logic.event.get(orX0143_1.value.uniqueCpId)?.orX0143DetList
  //業務チェックを行う。
  if (
    !printCom.doCheckBeforePrint(
      local.or26328Type.titleInput.value,
      local.mo00018TypePrintTheForm.modelValue,
      userList?.length ?? 0,
      rirekiList?.length ?? 0,
      showMessageBox
    )
  ) {
    return
  }
  const resSubjectData = await printCom.doRetryRirekiData<
    DailyTaskTablePrintSubjectSelectInEntity,
    PrtHistoryInfo,
    DailyTaskTablePrintSettingsSubjectSelectOutEntity
  >(
    {
      // 利用者リスト：画面.利用者一覧で選択した利用者リスト
      userList: userList!.map((x) => {
        return {
          userId: x.userId,
          userName: x.name1Knj + ' ' + x.name2Knj,
        }
      }),
      // 事業所ID：親画面.事業所ID
      svJigyoId: props.onewayModelValue.svJigyoId,
      // 基準日：画面.基準日
      kijunbiYmd: Or26331Logic.data.get(or26331_1.value.uniqueCpId)?.mo00020TypeKijunbi.value ?? '',
      kikanFlg: local.or28401.kikanFlag,
    },
    localOneway.orX0130Oneway.selectMode,
    'dailyTaskTablePrintSettingsSubjectSelect'
  )
  //画面の印刷設定情報を保存する。
  await save()
  const selectedPrtData = local.or28401.prtList.find(
    (x) => x.prtNo === local.or26326Type.mo01334Type.value
  )
  // 利用者選択が「単一」、且つ、履歴選択が「単一」の場合
  if (selectedPrtData) {
    if (
      localOneway.orX0130Oneway.selectMode === Or28401Const.DEFAULT.TANI &&
      localOneway.orX0143Oneway.singleFlg === Or28401Const.DEFAULT.TANI
    ) {
      // 業務共通化処理の設定
      void printCom.doReportOutput(
        selectedPrtData,
        {
          userId: userList![0].selfId,
          rirekiId: rirekiList![0].rirekiId,
        },
        {
          // 初期設定マスタの情報
          initMasterObj: props.onewayModelValue.initMasterObj,
          // 事業者名
          jigyoKnj: props.onewayModelValue.jigyoKnj,
        },
        {
          // 指定日
          // 印刷設定情報リスト選択行.日付表示有無が2の場合、画面の指定日
          selectDate:
            selectedPrtData.prnDate === '2'
              ? (Or28780Logic.data.get(or28780_1.value.uniqueCpId)?.mo00020Type.value ?? '')
              : '',
          // 記入用シートを印刷するフラグ
          emptyFlg: local.mo00018TypePrintTheForm.modelValue ? '1' : '0',
        },
        // システム日付
        props.onewayModelValue.sysYmd
      )
    } else {
      const historyData: HistoryInfo[] = []
      if (localOneway.orX0130Oneway.selectMode === Or28401Const.DEFAULT.TANI) {
        rirekiList!.forEach((x) => {
          historyData.push({
            userId: userList![0].selfId,
            rirekiId: x.rirekiId,
          })
        })
      } else {
        resSubjectData.forEach((x) => {
          historyData.push({
            userId: x.userId,
            rirekiId: x.rirekiId,
          })
        })
      }

      // 画面.印刷対象履歴リストを作成する
      printCom.doHukusuuSetting(
        selectedPrtData,
        historyData,
        localOneway.orX0117Oneway,
        localOneway.orX0130Oneway.selectMode,
        userList!,
        {
          // 初期設定マスタの情報
          initMasterObj: props.onewayModelValue.initMasterObj,
          // 事業者名
          jigyoKnj: props.onewayModelValue.jigyoKnj,
        },
        {
          // 指定日
          // 印刷設定情報リスト選択行.日付表示有無が2の場合、画面の指定日
          selectDate:
            selectedPrtData.prnDate === '2'
              ? (Or28780Logic.data.get(or28780_1.value.uniqueCpId)?.mo00020Type.value ?? '')
              : '',
          // 記入用シートを印刷するフラグ
          emptyFlg: local.mo00018TypePrintTheForm.modelValue ? '1' : '0',
        },
        // システム日付
        props.onewayModelValue.sysYmd,
        resSubjectData,
        rirekiList!
      )
      // PDFダウンロードを行う
      OrX0117Logic.state.set({
        uniqueCpId: orX0117_1.value.uniqueCpId,
        state: {
          isOpen: true,
        },
      })
    }
  }
}

/**
 * 「タイトルテキストフィールド」変更
 */
const handleChangeTitle = async () => {
  await checkTitleInput()
}

/**
 * 帳票タイトルが入力していない場合
 */
async function checkTitleInput() {
  const titleInput = Or26328Logic.data.get(or26328_1.value.uniqueCpId)?.titleInput
  if (titleInput?.value) return
  await openErrorDialog('message.e-cmn-20845', 'btn.ok')
  // 変更前の帳票タイトルに戻す。
  Or26328Logic.data.set({
    uniqueCpId: or26328_1.value.uniqueCpId,
    value: local.or26328Type,
  })
}

/**
 * 印刷設定情報に選択した印刷設定情報リストを設定する。
 *
 * @param oldValue - 前回選択印刷設定情報
 */
const setInputValue = (oldValue?: string) => {
  const printData = local.or28401.prtList.find(
    (x) => x.prtNo === (oldValue ?? local.or26326Type.mo01334Type.value)
  )
  if (printData) {
    //帳票タイトル
    printData.prtTitle = Or26328Logic.data.get(or26328_1.value.uniqueCpId)?.titleInput.value ?? ''
    //日付表示有無
    printData.prnDate = Or28780Logic.data.get(or28780_1.value.uniqueCpId)?.mo00039Type ?? ''
    //パラメータ03
    printData.param03 = Or18615Logic.data.get(or18615_1.value.uniqueCpId)?.mo00018TypeChangeTitle
      .modelValue
      ? '1'
      : '0'
    //パラメータ04
    printData.param04 = Or18615Logic.data.get(or18615_1.value.uniqueCpId)?.mo00045Type.value ?? ''
    //パラメータ05
    printData.param05 = local.mo00018TypeParam05.modelValue ? '1' : '0'
    //パラメータ07
    printData.param07 = local.mo00018TypeParam07.modelValue ? 'ON' : 'OFF'
    //パラメータ08
    printData.param08 = local.mo00018TypeParam08.modelValue ? '1' : '0'
  }
}

/**
 * 画面印刷設定内容を保存
 */
const save = async () => {
  setInputValue()
  const inputData: PrintCloseUpdateEntity<IPrintInfo> = {
    // セクション名:親画面.セクション名
    sectionName: props.onewayModelValue.sectionName,
    // システムコード：親画面.システムコード
    gsyscd: props.onewayModelValue.systemCode,
    // 職員ID：親画面.職員ID
    shokuId: props.onewayModelValue.shokuId,
    // 法人ID：親画面.法人ID
    houjinId: props.onewayModelValue.houjinId,
    // 施設ID：親画面.施設ID
    shisetuId: props.onewayModelValue.shisetuId,
    // 事業者ID：親画面.事業者ID
    svJigyoId: props.onewayModelValue.svJigyoId,
    // 印刷設定情報リスト：印刷設定情報リスト
    prtList: local.or28401.prtList,
  }
  await printCom.doPrintUpdate(inputData, 'dailyTaskTablePrintSettingsInfoUpdate', showMessageBox)
}

/**
 * 閉じるボタン押下時
 */
const close = async (): Promise<void> => {
  setInputValue()
  // 画面の印刷設定情報を保存する
  await printCom.doPrintClose(
    Or26328Logic.data.get(or26328_1.value.uniqueCpId)!.titleInput,
    {
      // セクション名:親画面.セクション名
      sectionName: props.onewayModelValue.sectionName,
      // システムコード：親画面.システムコード
      gsyscd: props.onewayModelValue.systemCode,
      // 職員ID：親画面.職員ID
      shokuId: props.onewayModelValue.shokuId,
      // 法人ID：親画面.法人ID
      houjinId: props.onewayModelValue.houjinId,
      // 施設ID：親画面.施設ID
      shisetuId: props.onewayModelValue.shisetuId,
      // 事業者ID：親画面.事業者ID
      svJigyoId: props.onewayModelValue.svJigyoId,
      // 印刷設定情報リスト：印刷設定情報リスト
      prtList: local.or28401.prtList,
    },
    'dailyTaskTablePrintSettingsInfoUpdate',
    localOneway.or26326Oneway.items,
    local.or26326Type.mo01334Type.value,
    showMessageBox,
    () => setState({ isOpen: false })
  )
}

/**
 * 閉じる処理
 *
 * @param newValue - ダイアログ
 */
async function closeBtnClick(newValue: Mo00024Type) {
  if (newValue.emitType === 'closeBtnClick') {
    await close()
  }
}
</script>

<template>
  <base-mo00024
    v-model="local.mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
    @update:model-value="closeBtnClick"
  >
    <template #cardItem>
      <c-v-row
        no-gutter
        class="or28401_screen"
      >
        <!-- 帳票 -->
        <g-custom-or-26326
          v-bind="or26326_1"
          :oneway-model-value="localOneway.or26326Oneway"
        />
        <c-v-col
          cols="12"
          sm="4"
          class="pa-0 pt-2 content_center"
        >
          <!-- タイトル -->
          <g-custom-or-26328
            v-bind="or26328_1"
            :oneway-model-value="localOneway.or26328Oneway"
            @update:model-value="handleChangeTitle"
          />
          <c-v-divider class="my-0" />
          <!-- 日付印刷 -->
          <g-custom-or-28780
            v-bind="or28780_1"
            :oneway-model-value="localOneway.mo00039OneWay"
          />
          <c-v-divider class="my-0" />
          <!-- 印刷オプション -->
          <g-custom-or-18615
            v-bind="or18615_1"
            :oneway-model-value="localOneway.or18615OneWay"
          >
            <template #optionPrintItems>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-2"
              >
                <!--記入用シートを印刷するチェックボックス-->
                <base-mo00018
                  v-model="local.mo00018TypePrintTheForm"
                  :oneway-model-value="localOneway.mo00018OneWayPrintTheForm"
                />
              </c-v-col>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-2"
              >
                <!--履歴情報を印刷する（作成者, No.）-->
                <base-mo00018
                  v-model="local.mo00018TypeParam05"
                  :oneway-model-value="localOneway.param05OneWay"
                />
              </c-v-col>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-2"
              >
                <!--モノクロ印刷モード-->
                <base-mo00018
                  v-model="local.mo00018TypeParam07"
                  :oneway-model-value="localOneway.param07OneWay"
                />
              </c-v-col>
              <c-v-col
                cols="12"
                sm="12"
                class="pa-2"
              >
                <!--「随時実施する等その他のサービス」を印刷する-->
                <base-mo00018
                  v-model="local.mo00018TypeParam08"
                  :oneway-model-value="localOneway.param08OneWay"
                />
              </c-v-col>
            </template>
          </g-custom-or-18615>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="6"
          class="pa-2"
        >
          <c-v-row
            no-gutter
            class="or28401_row align-center"
          >
            <!-- 利用者選択 -->
            <g-custom-or-26331
              v-bind="or26331_1"
              :oneway-model-value="localOneway.or26331OneWay"
            />
            <c-v-col
              cols="12"
              sm="12"
              class="pa-2 d-flex align-center"
            >
              <!--担当ケアマネ選択-->
              <g-custom-or20313
                v-bind="or20313_1"
                :oneway-model-value="props.onewayModelValue"
              />
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="or28401_row grid-width"
          >
            <c-v-col
              :cols="userCols"
              style="overflow-x: auto"
            >
              <!-- 印刷設定画面利用者一覧 -->
              <g-custom-or-x-0130
                v-if="localOneway.orX0130Oneway.selectMode"
                v-bind="orX0130_1"
                :oneway-model-value="localOneway.orX0130Oneway"
              />
            </c-v-col>
            <c-v-col
              v-if="local.or26331Type.mo00039OneWayUserSelectType === Or28401Const.DEFAULT.TANI"
              cols="6"
              sm="6"
              class="pl-2 pr-0"
            >
              <!-- 計画期間＆履歴一覧 -->
              <g-custom-or-x-0143
                v-bind="orX0143_1"
                :oneway-model-value="localOneway.orX0143Oneway"
              />
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611Oneway"
          class="mx-2"
          @click="close()"
        ></base-mo00611>
        <!-- 印刷ボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          class="mr-2"
          @click="print"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- 印刷設定帳票出力状態リスト画面 -->
  <g-custom-or-x-0117
    v-if="showDialogOrx0117"
    v-bind="orX0117_1"
    :oneway-model-value="localOneway.orX0117Oneway"
  />
  <g-base-or21813 v-bind="or21813_1" />
  <!-- 確認ダイアログ -->
  <g-base-or21814 v-bind="or21814_1" />
</template>

<style scoped lang="scss">
.or28401_screen {
  margin: -8px !important;
}

.or28401_row {
  margin: 0px !important;
}

.content_center {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  padding: 0px !important;

  .label_left {
    padding-right: 0px;
  }

  .label_right {
    padding-left: 0px;
    padding-right: 0px;
  }

  .printerOption {
    background-color: rgb(var(--v-theme-black-100));
  }

  :deep(.label-area-style) {
    display: none;
  }

  .customCol {
    margin-left: 0px;
    margin-right: 0px;
  }
}

.grid-width {
  min-width: 694px;
}
</style>
