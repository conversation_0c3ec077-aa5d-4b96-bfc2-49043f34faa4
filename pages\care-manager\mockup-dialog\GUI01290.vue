<script setup lang="ts">
import {
  computed,
  definePageMeta,
  reactive,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import { Or30594Const } from '~/components/custom-components/organisms/Or30594/Or30594.constants'
import { Or30594Logic } from '~/components/custom-components/organisms/Or30594/Or30594.logic'
import type { InitMedMasterObj } from '~/components/custom-components/organisms/Or30594/Or30594.type'
import type { userList } from '~/repositories/cmn/entities/PrintSelectEntity'
import type { Or30594OnewayType } from '~/types/cmn/business/components/Or30594Type'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})
const systemCommonsStore = useSystemCommonsStore()

/**************************************************
 * POP画面ポップアップ
 * GUI01290_［印刷設定］画面
 * KMD朱征宇 2025/06/04 ADD START
 **************************************************/
// 画面ID
const screenId = 'GUI01290'
// ルーティング
const routing = 'GUI01290/pinia'
// 画面物理名
const screenName = 'GUI01290'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const localOneway = reactive({
  or30594Oneway: {
    houjinId: '1',
    shisetuId: '1',
    svJigyoId: '4',
    jigyoKnj: '1',
    jigyoCd: '000001',
    svJigyoIdList: systemCommonsStore.getSvJigyoIdList,
    tantoShokuId: '0',
    shokuId: '0',
    userId: '1',
    startDate: systemCommonsStore.getStartDate,
    endDate: systemCommonsStore.getEndDate,
    sysYmd: systemCommonsStore.getSystemDate,
    rirekiId: '1',
    userList: [] as userList[],
    selectLedgerNumber: '1',
    gojuuOnRowNo: '1',
    gojuuOnKana: ['全'],
    initMasterObj: { kikanFlg: '1' },
    systemCode: '1',
    sectionName: '[3GK]利用料請求書',
  } as Or30594OnewayType<InitMedMasterObj>,
})
/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01290' },
})

/**************************************************
 * Props
 **************************************************/
const or30594 = ref({ uniqueCpId: '' })
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 自身のPinia領域をセットアップ
const { childCpIds } = useInitialize({
  cpId: 'GUI01290',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or30594Const.CP_ID(0) }],
})
Or30594Logic.initialize(childCpIds.Or30594.uniqueCpId)
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or30594Const.CP_ID(0)]: or30594.value,
})

/**************************************************
 * Props
 **************************************************/

// ダイアログ表示フラグ
const showDialogOr30594 = computed(() => {
  // Or00100のダイアログ開閉状態
  return Or30594Logic.state.get(or30594.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * 初期データ取得
 */
async function initData() {}

/***
 * ボタン押下時の処理
 */
function or30594OnClick() {
  Or30594Logic.state.set({
    uniqueCpId: or30594.value.uniqueCpId,
    state: { isOpen: true },
  })
}

await initData()
/**************************************************
 * ［印刷設定］画面
 * KMD朱征宇 2025/06/04 ADD START
 **************************************************/
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="or30594OnClick()"
        >GUI01290_［印刷設定］画面
      </v-btn>
      <g-custom-or-30594
        v-if="showDialogOr30594"
        v-bind="or30594"
        :oneway-model-value="localOneway.or30594Oneway"
        :unique-cp-id="or30594.uniqueCpId"
      />
    </c-v-col>
  </c-v-row>
</template>
