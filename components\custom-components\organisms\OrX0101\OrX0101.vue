<script setup lang="ts">
import { computed, nextTick, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or27777Const } from '../Or27777/Or27777.constants'
import { Or27778Const } from '../Or27778/Or27778.constants'
import { Or10659Logic } from '../Or10659/Or10659.logic'
import type { TableData } from '../Or27778/Or27778.type'
import { OrX0101Const, type AsyncFunction } from './OrX0101.constants'
import type { OrX0101StateType } from './OrX0101.type'
import { useScreenOneWayBind, useScreenUtils, useSetupChildProps } from '#imports'
import type { Or27778OnewayType, Or27778Type } from '~/types/cmn/business/components/Or27778Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { UPDATE_KBN } from '~/constants/classification-constants'
import type {
  OpinionMasterSelectInEntity,
  OpinionMasterSelectOutEntity,
} from '~/repositories/cmn/entities/opinionMasterSelectEntity'
import type {
  OpinionMasterUpdateInEntity,
  OpinionMasterUpdateOutEntity,
} from '~/repositories/cmn/entities/opinionMasterUpdateEntity'
import { ResBodyStatusCode } from '~/constants/api-constants'

/**
 * OrX0101:有機体:意見マスタ
 * GUI01224_意見マスタ
 *
 * @description
 * 意見マスタ
 *
 * <AUTHOR>
 */

/************************************************
 * Props
 ************************************************/
interface Props {
  uniqueCpId: string
  parentUniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/
const { getChildCpBinds } = useScreenUtils()
const { t } = useI18n()
const local = reactive({
  or27778: {
    delBtnDisabled: false,
    focusIndex: '',
    focusType: '',
    opinionMasterInfoList: [],
  } as Or27778Type,
  kbnFlg: '',
  focusIndex: '',
})

const or27778Oneway = ref({
  kbnFlg: '',
} as Or27778OnewayType)

//意見一覧および操作ボタン
const or27777 = ref({ uniqueCpId: Or27777Const.CP_ID(0) })

// 意見一覧
const or27778 = ref({ uniqueCpId: Or27778Const.CP_ID(0) })

// Or21813_有機体:エラーダイアログ
const or21813 = ref({ uniqueCpId: Or21813Const.CP_ID(0) })

//タブ切り替え
const isChangeTab = ref(false)

const isClose = ref(false)
// Or27778 Ref
const or27778Ref = ref<{
  createRow(): AsyncFunction
  copyRow(): AsyncFunction
  deleteRow(): AsyncFunction
  init(): AsyncFunction
}>()

// Or27777 Ref
const or27777Ref = ref<{ delBtnDisable(disabled: boolean): AsyncFunction }>()

/**************************************************
 * コンポーネント固有処理
 **************************************************/
useSetupChildProps(props.uniqueCpId, {
  [Or27777Const.CP_ID(0)]: or27777.value,
  [Or27778Const.CP_ID(0)]: or27778.value,
})

/**
 * 保存ボタン押下_保存権限がある場合の開閉
 *
 * @param errormsg - Message
 */
function showOr21813Msg(errormsg: string) {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト0
      dialogText: errormsg,
      firstBtnType: 'normal1',
      firstBtnLabel: 'OK',
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })
  // エラーダイアログをオープン
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

// ダイアログ表示フラグ
const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 行追加ボタン
 */
function onAddItem() {
  or27778Ref.value?.createRow()
}
/**
 * 行複写ボタン
 */
function onCloneItem() {
  or27778Ref.value?.copyRow()
}
/**
 * 行削除ボタン
 */
function onDelete() {
  or27778Ref.value?.deleteRow()
}

useScreenOneWayBind<OrX0101StateType>({
  cpId: OrX0101Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    param: (value) => {
      local.kbnFlg = value!.sys3ryaku
      or27778Oneway.value.kbnFlg = value!.sys3ryaku
      switch (value?.executeFlag) {
        // 保存
        case 'save':
          isClose.value = value.isClose ?? false
          isChangeTab.value = value.isChangeTab ?? false
          void save()
          break
        // データ再取得
        case 'getData':
          void init()
          break
        default:
          break
      }
    },
  },
})

/**
 * Or21813のイベントを監視
 *
 * @description
 * またOr21813のボタン押下フラグをリセットする。
 */
watch(
  () => Or21813Logic.event.get(or21813.value.uniqueCpId),
  () => {
    local.or27778.focusIndex = local.focusIndex
  }
)

/**
 * 行削除ボタン活性状態を監視
 *
 * @description
 * 活性状態を監視
 */
watch(
  () => local.or27778.delBtnDisabled,
  (newValue) => {
    or27777Ref.value?.delBtnDisable(!newValue)
  }
)

const init = async () => {
  // 意見マスタ情報取得(IN)
  const inputData: OpinionMasterSelectInEntity = {
    kbnFlg: local.kbnFlg,
    cf1Flg: Or27778Const.DEFAULT.CF1_FLG,
  }
  // 意見マスタ初期情報取得
  const res: OpinionMasterSelectOutEntity = await ScreenRepository.select(
    'opinionMasterSelect',
    inputData
  )
  local.or27778.opinionMasterInfoList = res.data.opinionMasterInfoList
  await nextTick()
  or27778Ref.value?.init()
}

/**
 * 保存
 */
async function save() {
  await nextTick()
  local.focusIndex = ''
  local.or27778.focusIndex = ''
  const nameSet = new Set<string>()
   const childCpBindsData = getChildCpBinds(props.uniqueCpId, {
    // 一覧データ
    [Or27778Const.CP_ID(0)]: { cpPath: Or27778Const.CP_ID(0), twoWayFlg: true },
  })

  // 一覧データを取得
  const tableData = childCpBindsData[Or27778Const.CP_ID(0)].twoWayBind?.value as TableData[]
  for (let index = 0; index < tableData.length; index++) {
    const data = tableData[index]
    if (data.updateKbn === UPDATE_KBN.DELETE) {
      continue
    }
    //「区分番号」或いは「内容」に空白があるの場合
    if (data.kbnCd.value === '' || data.textKnj.value === '') {
      local.focusIndex = index + ''
      local.or27778.focusType = data.kbnCd
        ? Or27778Const.DEFAULT.TEXT_KNJ
        : Or27778Const.DEFAULT.KBN_CD
      showOr21813Msg(t('message.e-cmn-41710'))
      break
    }
    // 「区分番号」の入力値は1000以下の場合
    if (Number(data.kbnCd) < 1000) {
      local.focusIndex = index + ''
      local.or27778.focusType = Or27778Const.DEFAULT.KBN_CD
      showOr21813Msg(t('message.e-cmn-41711', [t('label.category-number')]))
      break
    }
    //「区分番号」は重複の場合
    if (nameSet.has(data.kbnCd.value)) {
      local.focusIndex = index + ''
      local.or27778.focusType = Or27778Const.DEFAULT.KBN_CD
      showOr21813Msg(t('message.e-cmn-41713', [t('label.category-number')]))
      break
    }
    nameSet.add(data.kbnCd.value)
  }
  // 変更がある場合、処理継続
  if (local.focusIndex) {
    return
  }
  const param: OpinionMasterUpdateInEntity = {
    opinionMasterInfoList: tableData.map((item) => {
      return {
        cf1Id: item.cf1Id,
        kbnFlg: item.kbnFlg,
        kbnCd: item.kbnCd.value,
        textKnj: item.textKnj.value,
        modifiedCnt: item.modifiedCnt,
        changeF: item.changeF,
        cf1Flg: item.cf1Flg,
        updateKbn: item.updateKbn,
      }
    }),
  }

  console.log(local.or27778.opinionMasterInfoList,
    'local.or27778.opinionMasterInfoList:OrX0101.vue:save()');

  // 情報保存
  const res: OpinionMasterUpdateOutEntity = await ScreenRepository.update(
    'opinionMasterUpdate',
    param
  )
  if (isClose.value) {
    Or10659Logic.state.set({
      uniqueCpId: props.parentUniqueCpId,
      state: {
        isOpen: false,
      },
    })
  }
  if (res.statusCode === ResBodyStatusCode.SUCCESS && !isChangeTab.value) {
    await init()
  }
  if (isChangeTab.value) {
    Or10659Logic.event.set({
      uniqueCpId: props.parentUniqueCpId,
      state: {
        isSave: true,
      },
    })
  }
}
</script>

<template>
  <div class="pa-2">
    <!-- 意見一覧および操作ボタン -->
    <div class="pb-2">
      <g-custom-or-27777
        ref="or27777Ref"
        v-bind="or27777"
        @on-add-item="onAddItem"
        @on-clone-item="onCloneItem"
        @on-delete="onDelete"
      />
    </div>
    <!-- 意見一覧 -->
    <g-custom-or-27778
      ref="or27778Ref"
      v-bind="or27778"
      :model-value="local.or27778"
      :oneway-model-value="or27778Oneway"
      :parent-unique-cp-id="props.uniqueCpId"
      @update:model-value="local.or27778.delBtnDisabled = $event.delBtnDisabled"
    />
  </div>
  <!-- Or21813:有機体:エラーダイアログ -->
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  />
</template>

<style scoped lang="scss"></style>
