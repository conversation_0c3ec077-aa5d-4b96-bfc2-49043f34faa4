import { HttpResponse, type ResponseResolver } from 'msw'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { InEntity } from '~/repositories/AbstructWebRepository'
import AssessmentMasterUpdate from '~/mocks/api/cmn/update/pages/assessmentMasterUpdate/handler'
import MonthlyYearlyPatternSettingUpdate from '~/mocks/api/cmn/update/pages/monthlyYearlyPatternSettingUpdate/handler'
import MonthlyYearlyTableUpdate from '~/mocks/api/cmn/update/pages/monthlyYearlyTableUpdate/handler'
import CognitiveFunctionMentalActionHandycapUpdate from '~/mocks/api/cmn/update/pages/assessmentHome63Update/handler'
import ImplementationPlan1Update from '~/mocks/api/cmn/update/pages/implementationPlan1Update/handler'
import ImplementationPlan2Update from '~/mocks/api/cmn/update/pages/implementationPlan2Update/handler'
import AssessmentComprehensiveLetterSizeUpdate from '~/mocks/api/cmn/update/pages/assessmentComprehensiveLetterSizeUpdate/handler'
import ImplementationPlanInfoUpdate from '~/mocks/api/cmn/update/pages/implementationPlanInfoUpdate/handler'
import IssuesAnalysisPrintSettingsUpdate from '~/mocks/api/cmn/update/pages/issuesAnalysisPrintSettingsUpdate/handler'
import ImplementationMonitoringMasterUpdate from '~/mocks/api/cmn/update/pages/implementationMonitoringMasterUpdate/handler'
import AssessmentDuplicateUpdate from '~/mocks/api/cmn/update/pages/assessmentDuplicateUpdate/handler'
import AssessmentInterRAIScreenDUpdate from '~/mocks/api/cmn/update/pages/assessmentInterRAIDUpdate/handler'
import AssessmentInterRAIScreenVUpdate from '~/mocks/api/cmn/update/pages/assessmentInterRAIScreenVUpdate/handler'
import AssessmentHomeTab4Update from '~/mocks/api/cmn/update/pages/assessmentHome4Update/handler'
import FreeAssessmentFacePrintSettingsUpdate from '~/mocks/api/cmn/update/pages/freeAssessmentFacePrintSettingsUpdate/handler'
import FreeAssessmentCheckPrintSettingsUpdate from '~/mocks/api/cmn/update/pages/freeAssessmentCheckPrintSettingsUpdate/handler'
import AssessmentComprehensivePsychologyUpdate from '~/mocks/api/cmn/update/pages/assessmentComprehensivePsychologyUpdate/handler'
import WeekPlanPatternSettingUpdate from '~/mocks/api/cmn/update/pages/weekPlanPatternSettingUpdate/handler'
import symbolsMeaningUpdate from '~/mocks/api/cmn/update/pages/symbolsMeaningUpdate/handler'
import consentFieldEditUpdate from '~/mocks/api/cmn/update/pages/consentFieldEditUpdate/handler'
import AssessmentComprehensivePrintSettingsInitialUpdate from '~/mocks/api/cmn/update/pages/assessmentComprehensivePrintSettingsInitialUpdate/handler'
import AssessmentInterRAIJUpdate from '~/mocks/api/cmn/update/pages/assessmentInterRAIJUpdate/handler'
import MonitoringConfigureMasterUpdate from '~/mocks/api/cmn/update/pages/monitoringConfigureMasterUpdate/handler'
import difficultyMasterUpdate from '~/mocks/api/cmn/update/pages/difficultyMasterUpdate/handler'
import considerInitialInfoDataUpdate from '~/mocks/api/cmn/update/pages/considerInitialInfoDataUpdate/handler'
import issueOrganizeSummaryMasterUpdate from '~/mocks/api/cmn/update/pages/issueOrganizeSummaryMasterUpdate/handler'
import IncentivesItemsPrintSettingsInitUpdate from '~/mocks/api/cmn/update/pages/incentivesItemsPrintSettingsInitUpdate/handler'
import supportElapsedRecordUpdate from '~/mocks/api/cmn/update/pages/supportElapsedRecordUpdate/handler'
import inhibitoryFactorsMasterUpdate from '~/mocks/api/cmn/update/pages/inhibitoryFactorsMasterUpdate/handler'
import assessmentSummaryImportSettingUpdate from '~/mocks/api/cmn/update/pages/assessmentSummaryImportSettingUpdate/handler'
import Or00251OfficeGroupChange from '@/mocks/api/cmn/update/pages/header/officeGroupChange/handler'
import IssuesPlanningCategoryMasterUpdate from '@/mocks/api/cmn/update/pages/IssuesPlanningCategoryMasterUpdate/handler'
import issuesPlanningStyleTitleMasterUpdate from '@/mocks/api/cmn/update/pages/issuesPlanningStyleTitleMasterUpdate/handler'
import ExecutionConfirmationMasterUpdate from '@/mocks/api/cmn/update/pages/executionConfirmationMasterUpdate/handler'
import DailyPlanPatternTitleUpdate from '@/mocks/api/cmn/update/pages/dailyPlanPatternTitleUpdate/handler'
import ConfirmationMethodMasterUpdate from '@/mocks/api/cmn/update/pages/confirmationMethodMasterUpdate/handler'
import SatisfactionLevelMasterUpdate from '@/mocks/api/cmn/update/pages/satisfactionLevelMasterUpdate/handler'
import CorrespondenceMasterUpdate from '@/mocks/api/cmn/update/pages/correspondenceMasterUpdate/handler'
import DailyTaskTableMasterUpdate from '@/mocks/api/cmn/update/pages/dailyTaskTableMasterUpdate/handler'
import GaibuItakuryouUpdate from '@/mocks/api/cmn/update/pages/gaibuItakuryouUpdate/handler'
import EvaluationTablePrintSettingsInitUpdate from '@/mocks/api/cmn/update/pages/evaluationTablePrintSettingsInitUpdate/handler'
import CheckItemsUpdate from '@/mocks/api/cmn/update/pages/checkItemsUpdate/handler'
import specialNoteMattersUpdate from '@/mocks/api/cmn/update/pages/specialNoteMattersUpdate/handler'
import UseSlipOtherDtlMaintenanceInfoUpdate from '@/mocks/api/cmn/update/pages/useSlipOtherDtlMaintenanceInfoUpdate/handler'
import CmPreventionSentenceMasterUpdate from '@/mocks/api/cmn/update/pages/cmPreventionSentenceMasterUpdate/handler'
import KigoImiUpdate from '@/mocks/api/cmn/update/pages/kigoImiUpdate/handler'
import OpinionMasterUpdate from '@/mocks/api/cmn/update/pages/opinionMasterUpdate/handler'
import WeekTblPatternInfoUpdate from '@/mocks/api/cmn/update/pages/weekTblPatternInfoUpdate/handler'
import InfoCollectionInitTenUpdate from '@/mocks/api/cmn/update/pages/infoCollectionInitTenUpdate/handler'
import assessmentComprehensiveMealUpdate from '@/mocks/api/cmn/update/pages/assessmentComprehensiveMealUpdate/handler'
import AssessmentComprehensiveBaseUpdate from '@/mocks/api/cmn/update/pages/assessmentComprehensiveBaseUpdate/handler'
import preventionEvaluationTableTitleMasterUpdate from '@/mocks/api/cmn/update/pages/preventionEvaluationTableTitleMasterUpdate/handler'
import MonitoringMasterInfoUpdate from '@/mocks/api/cmn/update/pages/monitoringMasterInfoUpdate/handler'
import duplicateAssessmentUpdate from '@/mocks/api/cmn/update/pages/duplicateAssessmentUpdate/handler'
import PriorityOrderUpdate from '@/mocks/api/cmn/update/pages/priorityOrderUpdate/handler'
import InputSupportGoalAndLife1YearScreenUpdate from '@/mocks/api/cmn/update/pages/inputSupportGoalAndLife1YearScreenUpdate/handler'
import MaskMastUpdate from '@/mocks/api/cmn/update/pages/maskMastUpdate/handler'
import ImplementationPlan3Update from '~/mocks/api/cmn/update/pages/implementationPlan3Update/handler'
import SituationMasterUpdate from '~/mocks/api/cmn/update/pages/situationMasterUpdate/handler'
import PlanTransferExecutionProcessUpdate from '~/mocks/api/cmn/update/pages/planTransferExecutionProcessUpdate/handler'
import implementationMonitoringRegistInfoUpdate from '~/mocks/api/cmn/update/pages/implementationMonitoringRegistInfoUpdate/handler'
import evaluationTableMasterInfoUpdate from '~/mocks/api/cmn/update/pages/evaluationTableMasterInfoUpdate/handler'
import KentouhyoInfoUpdate from '~/mocks/api/cmn/update/pages/kentouhyoInfoUpdate/handler'
import IntegratedPlanMasterUpdate from '~/mocks/api/cmn/update/pages/integratedPlanMasterUpdate/handler'
import InquiryContentsInfoUpdate from '~/mocks/api/cmn/update/pages/inquiryContentsInfoUpdate/handler'
import supportElapsedRecordMasterUpdate from '~/mocks/api/cmn/update/pages/supportElapsedRecordMasterUpdate/handler'
import WelfareUnitInfoUpdate from '~/mocks/api/cmn/update/pages/welfareUnitInfoUpdate/handler'
import careProvisionMasterUpdate from '~/mocks/api/cmn/update/pages/careProvisionMasterUpdate/handler'
import FreeAssessmentSheetDisplaySettingsUpdate from '~/mocks/api/cmn/update/pages/freeAssessmentSheetDisplaySettingsUpdate/handler'
import supportElapsedKindMasterUpdateService from '~/mocks/api/cmn/update/pages/supportElapsedKindMasterUpdateService/handler'
import HospitalizationTimeInfoOfferSetingMasterUpdate from '~/mocks/api/cmn/update/pages/hospitalizationTimeInfoOfferSetingMasterUpdate/handler'
import offerKindMasterUpdateService from '~/mocks/api/cmn/update/pages/offerKindMasterUpdateService/handler'
import freeAssessmentOutputItemSettingsMasterUpdate from '~/mocks/api/cmn/update/pages/freeAssessmentOutputItemSettingsMasterUpdate/handler'
import riyouBeppyo1KatuCalcUpdateService from '~/mocks/api/cmn/update/pages/riyouBeppyo1KatuCalcUpdateService/handler'
import CertificationSurveySpecialMatterUpdate from '~/mocks/api/cmn/update/pages/certificationSurveySpecialMatterUpdate/handler'
import AffectedAreaImportSettingUpdate from '~/mocks/api/cmn/update/pages/affectedAreaImportSettingUpdate/handler'
import IssuesAnalysisMasterUpdate from '~/mocks/api/cmn/update/pages/issuesAnalysisMasterUpdate/handler'
import InterestAndConcernUpdate from '~/mocks/api/cmn/update/pages/interestAndConcernUpdate/handler'
import IssuesAnalysisUpdate from '~/mocks/api/cmn/update/pages/issuesAnalysisUpdate/handler'
import CorrespondingTablePrintSettingsInitUpdate from '~/mocks/api/cmn/update/pages/correspondingTablePrintSettingsInitUpdate/handler'
import weekTableImageInfoUpdate from '~/mocks/api/cmn/update/pages/weekTableImageInfoUpdate/handler'
import DailyRoutinePlanUpdate from '~/mocks/api/cmn/update/pages/dailyRoutinePlanUpdate/handler'
import welfareEquipmentLendingUnitCntBundleSettingsInfoUpdate from '~/mocks/api/cmn/update/pages/welfareEquipmentLendingUnitCntBundleSettingsInfoUpdate/handler'
import frameWidthModifiedMasterUpdate from '~/mocks/api/cmn/update/pages/frameWidthModifiedMasterUpdate/handler'
import freeAssessmentFaceSheetUpdate from '@/mocks/api/cmn/update/pages/freeAssessmentFaceSheetUpdate/handler'
import freeAssessmentFaceSheetInitialUpdate from '@/mocks/api/cmn/update/pages/freeAssessmentFaceSheetInitialUpdate/handler'
import freeAssessmentFaceSheetHistoryChangeUpdate from '@/mocks/api/cmn/update/pages/freeAssessmentFaceSheetHistoryChangeUpdate/handler'
import freeAssessmentFaceSheetNewUpdate from '@/mocks/api/cmn/update/pages/freeAssessmentFaceSheetNewUpdate/handler'
import freeAssessmentFaceSheetPlanningPeriodChangeUpdate from '@/mocks/api/cmn/update/pages/freeAssessmentFaceSheetPlanningPeriodChangeUpdate/handler'

import MeetingMinutesDetailPkgUpdate from '@/mocks/api/cmn/update/pages/meetingMinutesDetailPkgUpdate/handler'
import MeetingMinutesDetailPtn1Update from '@/mocks/api/cmn/update/pages/meetingMinutesDetailPtn1Update/handler'
import ServiceUseSlipAnnexedTableDataMoveUpdate from '@/mocks/api/cmn/update/pages/serviceUseSlipAnnexedTableDataMoveUpdate/handler'
import MeetingMinutesDetailPtn2Update from '@/mocks/api/cmn/update/pages/meetingMinutesDetailPtn2Update/handler'
import PreventionEvaluationTableSettingsMasterUpdate from '~/mocks/api/cmn/update/pages/preventionEvaluationTableSettingsMasterUpdate/handler'
import KaigiRokuMstUpdate from '@/mocks/api/cmn/update/pages/kaigiRokuMstUpdate/handler'
import BasicSurvey1InfoUpdate from '@/mocks/api/cmn/update/pages/basicSurvey1InfoUpdate/handler'
import BasicSurvey2InfoUpdate from '@/mocks/api/cmn/update/pages/basicSurvey2InfoUpdate/handler'
import BasicSurvey3InitialInfoUpdate from '@/mocks/api/cmn/update/pages/basicSurvey3InitialInfoUpdate/handler'
import BasicSurvey4Update from '@/mocks/api/cmn/update/pages/basicSurvey4Update/handler'
import BasicSurvey5InfoUpdate from '@/mocks/api/cmn/update/pages/basicSurvey5InfoUpdate/handler'
import SpecialnoteMatterInfoUpdate from '@/mocks/api/cmn/update/pages/specialnoteMatterInfoUpdate/handler'
import KibohudanTorokuKakute from '@/mocks/api/cmn/update/pages/kibohudanTorokuKakute/handler'
import Cp2MasterUpdate from '@/mocks/api/cmn/update/pages/cp2MasterUpdate/handler'
import offerOfficeUpdate from '~/mocks/api/cmn/update/pages/offerOfficeUpdate/handler'
import SDCareMasterEasyRegistUpdate from '@/mocks/api/cmn/update/pages/SDCareMasterEasyRegistUpdate/handler'
import SealFieldDataUpdate from '~/mocks/api/cmn/update/pages/sealFieldDataUpdate/handler'
import PrintSettingsInfoUpdateGUI00944 from '@/mocks/api/cmn/update/pages/printSettingsInfoUpdateGUI00944/handler'
import ReceiptSectionUpdate from '@/mocks/api/cmn/update/pages/receiptSectionUpdate/handler'
import YokodasiServiceUnitReconfigureExecutionProcessUpdate from '~/mocks/api/cmn/update/pages/yokodasiServiceUnitReconfigureExecutionProcessUpdate/handler'
import PrintSettingsInfoUpdateGUI01132 from '@/mocks/api/cmn/update/pages/printSettingsInfoUpdateGUI01132/handler'
import CaseListInfoUpdate from '@/mocks/api/cmn/update/pages/caseListInfoUpdate/handler'
import PrintSettingsInfoUpdateGUI00938 from '@/mocks/api/cmn/update/pages/printSettingsInfoUpdateGUI00938/handler'
import PrintSettingsInfoUpdateGUI01139 from '@/mocks/api/cmn/update/pages/printSettingsInfoUpdateGUI01139/handler'
import PrintSettingsInfoUpdateGUI01266 from '@/mocks/api/cmn/update/pages/printSettingsInfoUpdateGUI01266/handler'
import PreventionBasicDetailInfoUpdate from '@/mocks/api/cmn/update/pages/preventionBasicDetailInfoUpdate/handler'
import freeAssessmentLedgerTitleSettingsMasterUpdate from '@/mocks/api/cmn/update/pages/freeAssessmentLedgerTitleSettingsMasterUpdate/handler'
import GUI00027Confirm from '@/mocks/api/cmn/update/pages/dch/common/GUI00027/confirm/handler'
import IndividualDuplicateHistoryDataUpdate from '@/mocks/api/cmn/update/pages/individualDuplicateHistoryDataUpdate/handler'
import FreeAssessmentFacePrintSettingsPrtNoChangeUpdate from '@/mocks/api/cmn/update/pages/freeAssessmentFacePrintSettingsPrtNoChangeUpdate/handler'
import SpecialInstructionsPeriodUpdate from '@/mocks/api/cmn/update/pages/specialInstructionsPeriodUpdate/handler'
import PrintSettingsInfoUpdateGUI01085 from '@/mocks/api/cmn/update/pages/printSettingsInfoUpdateGUI01085/handler'
import PrintSettingsInfoUpdateGUI011264 from '@/mocks/api/cmn/update/pages/printSettingsInfoUpdateGUI011264/handler'
import SougouJigyoServiceUnitReconfigureExecutionProcessUpdate from '@/mocks/api/cmn/update/pages/sougouJigyoServiceUnitReconfigureExecutionProcessUpdate/handler'
import AttendingPhysicianStatementUpdate from '@/mocks/api/cmn/update/pages/attendingPhysicianStatementUpdate/handler'
import PrintSettingsInfoUpdateGUI1131 from '@/mocks/api/cmn/update/pages/printSettingsInfoUpdateGUI1131/handler'
import ShortTermLeavingDateRegistUpdate from '@/mocks/api/cmn/update/pages/shortTermLeavingDateRegistUpdate/handler'
import printSettingsInfoComUpdate from '@/mocks/api/cmn/update/pages/printSettingsInfoComUpdate/handler'
import planDuplicateInsuranceiInvalidDuplicateUpdate from '~/mocks/api/cmn/update/pages/planDuplicateInsuranceiInvalidDuplicateUpdate/handler'
import planDuplicateUseSlipDuplicateUpdate from '~/mocks/api/cmn/update/pages/planDuplicateUseSlipDuplicateUpdate/handler'
import planDuplicateCarePlanEtcBundleCheckUpdate from '~/mocks/api/cmn/update/pages/planDuplicateCarePlanEtcBundleCheckUpdate/handler'
import planDuplicateCarePlanBundleUpdate from '~/mocks/api/cmn/update/pages/planDuplicateCarePlanBundleUpdate/handler'
import planDuplicateCarePlanIndividualCheckUpdate from '~/mocks/api/cmn/update/pages/planDuplicateCarePlanIndividualCheckUpdate/handler'
import ComprehensivePlanUpdate from '@/mocks/api/cmn/update/pages/comprehensivePlanUpdate/handler'
import useSlipInfoUpdate from '@/mocks/api/cmn/update/pages/useSlipInfoUpdate/handler'
import SavePrintSettingInfoSDCareUpdate from '@/mocks/api/cmn/update/pages/savePrintSettingInfoSDCareUpdate/handler'
import SavePrintSettingInfoAndSDCareDownloadUpdate from '@/mocks/api/cmn/update/pages/savePrintSettingInfoAndSDCareDownloadUpdate/handler'
import PrintSettingsInfoUpdateGUI01034 from '@/mocks/api/cmn/update/pages/printSettingsInfoUpdateGUI01034/handler'
import AttendingPhysicianStatementPrintSettingsInitUpdate from '@/mocks/api/cmn/update/pages/attendingPhysicianStatementPrintSettingsInitUpdate/handler'
import HospitalizationTimeInfoOfferUpdate from '@/mocks/api/cmn/update/pages/hospitalizationTimeInfoOfferUpdate/handler'
import AttendingPhysicianStatementPrintSettingsInfoUpdate from '@/mocks/api/cmn/update/pages/attendingPhysicianStatementPrintSettingsInfoUpdate/handler'
import FreeAssessmentFacePrintSettingsInitUpdate from '@/mocks/api/cmn/update/pages/freeAssessmentFacePrintSettingsInitUpdate/handler'
import FreeAssessmentFacePrintSettingsMemoUpdate from '@/mocks/api/cmn/update/pages/freeAssessmentFacePrintSettingsMemoUpdate/handler'
import PrintSettingsInfoUpdateGUI01119 from '@/mocks/api/cmn/update/pages/printSettingsInfoUpdateGUI01119/handler'
import PrintSettingsInfoUpdateGUI01035 from '@/mocks/api/cmn/update/pages/printSettingsInfoUpdateGUI01035/handler'
import WeeklyPlanMasterInfoUpdate from '@/mocks/api/cmn/update/pages/weeklyPlanMasterInfoUpdate/handler'
import printSettingsInfoUpdateGUI01211 from '@/mocks/api/cmn/update/pages/printSettingsInfoUpdateGUI01211/handler'
import DailyTaskTablePrintSettingsInitUpdate from '@/mocks/api/cmn/update/pages/dailyTaskTablePrintSettingsInitUpdate/handler'
import Plan1PrintSettingsInitUpdate from '@/mocks/api/cmn/update/pages/plan1PrintSettingsInitUpdate/handler'
import evaluationTableSettingsMasterUpdate from '@/mocks/api/cmn/update/pages/evaluationTableSettingsMasterUpdate/handler'
import issuesPlanningStyleSettingsMasterUpdate from '@/mocks/api/cmn/update/pages/issuesPlanningStyleSettingsMasterUpdate/handler'
import KaigiBasyoMstUpdate from '@/mocks/api/cmn/update/pages/kaigiBasyoMstUpdate/handler'
import useSlipDailyRateUsePeriodInfoUpdate from '@/mocks/api/cmn/update/pages/useSlipDailyRateUsePeriodInfoUpdate/handler'
import DailyRoutinePlanPrintSettingsInitUpdate from '@/mocks/api/cmn/update/pages/dailyRoutinePlanPrintSettingsInitUpdate/handler'
import ImplementationPlan1PrintSettingsInitUpdate from '@/mocks/api/cmn/update/pages/implementationPlan1PrintSettingsInitUpdate/handler'
import simUpdate from '@/mocks/api/cmn/update/pages/simUpdate/handler'
import simDelete from '@/mocks/api/cmn/update/pages/simDelete/handler'
import ImplementationPlan1PrintSettingsInfoUpdate from '~/mocks/api/cmn/update/pages/implementationPlan1PrintSettingsInfoUpdate/handler'
import AssessmentInterRAIPrintSettingsUpdate from '~/mocks/api/cmn/update/pages/assessmentInterRAIPrintSettingsUpdate/handler'
import IssuesConsiderPrintSettingsInitialUpdate from '~/mocks/api/cmn/update/pages/issuesConsiderPrintSettingsInitialUpdate/handler'
import ImplementationMonitoringPrintSettingsInitUpdate from '~/mocks/api/cmn/update/pages/implementationMonitoringPrintSettingsInitUpdate/handler'
import PlanMonitoringPrintSettingsUpdate from '~/mocks/api/cmn/update/pages/planMonitoringPrintSettingsUpdate/handler'
import PreventionEvaluationPrintSettingsUpdate from '~/mocks/api/cmn/update/pages/preventionEvaluationPrintSettingsUpdate/handler'
import IssueOrganizeSummaryPrintSettingsUpdate from '~/mocks/api/cmn/update/pages/issueOrganizeSummaryPrintSettingsUpdate/handler'
import ConsiderTableInterRAIPrintSettingsInitUpdate from '~/mocks/api/cmn/update/pages/considerTableInterRAIPrintSettingsInitUpdate/handler'
import LeavingInfoRecordPrintSettingsInitialUpdate from '~/mocks/api/cmn/update/pages/leavingInfoRecordPrintSettingsInitialUpdate/handler'
import DailyRoutinePlanPrintSettingsInfoUpdate from '~/mocks/api/cmn/update/pages/dailyRoutinePlanPrintSettingsInfoUpdate/handler'
import PreventionPlanPrintUserSettingsSubjectUpdate from '@/mocks/api/cmn/update/pages/preventionPlanPrintUserSettingsSubjectUpdate/handler'
import PreventionPlanPrintSettingsInitUpdate from '@/mocks/api/cmn/update/pages/preventionPlanPrintSettingsInitUpdate/handler'
import AssessmentInterRAIDuplicateUpdate from '@/mocks/api/cmn/update/pages/assessmentInterRAIDuplicateUpdate/handler'
import ImplementationPlan2PrintSettingsInitUpdate from '@/mocks/api/cmn/update/pages/implementationPlan2PrintSettingsInfoUpdate/handler'
import ExaminationIssueInfoNewSelect from '@/mocks/api/cmn/update/pages/examinationIssueInfoNewSelect/handler'
import ExaminationIssueInfoSelect from '@/mocks/api/cmn/update/pages/examinationIssueInfoSelect/handler'
import ExaminationIssueInfoUpdate from '@/mocks/api/cmn/update/pages/examinationIssueInfoUpdate/handler'
import TucPlanUpdate from '@/mocks/api/cmn/update/pages/tucPlanUpdate/handler'
import ImplementPlanTwoMasterUpdate from '@/mocks/api/cmn/update/pages/implementPlanTwoMasterUpdate/handler'
import PreventionPlanPrintSettingsInfoUpdate from '@/mocks/api/cmn/update/pages/preventionPlanPrintSettingsInfoUpdate/handler'
import AssessmentComprehensiveMedicalCareUpdate from '@/mocks/api/cmn/update/pages/assessmentComprehensiveMedicalCareUpdate/handler'
import OrganizingIssuesUpdate from '@/mocks/api/cmn/update/pages/organizingIssuesUpdate/handler'
import BaseCheckListUpdate from '@/mocks/api/cmn/update/pages/baseCheckListUpdate/handler'

const post: ResponseResolver = async ({ request }) => {
  const requestBody = (await request.json()) as InEntity
  const business = requestBody.data.business
  const dataName = business.dataName
  let filePath = ''
  let handlerParam

  if (dataName) {
    filePath = ['./pages', dataName].join('/')
    handlerParam = business
  } else {
    const screenDefId = business.definitionJson.definition.screenDefId
    const routing = business.definitionJson.definition.routing
    const screenName = business.definitionJson.definition.screenPhysicalName
    handlerParam = business.definitionJson

    filePath = ['./pages', routing, screenName, screenDefId].join('/')
  }

  const handler = _handler(filePath)
  if (handler) {
    return handler(handlerParam)
  } else {
    const responseJson: BaseResponseBody = {
      statusCode: 'success',
      data: {
        definitionJson: business.definitionJson,
      },
    }
    return HttpResponse.json(responseJson, { status: 200 })
  }
}

function _handler(path: string): Function | undefined {
  try {
    if (path === './pages/assessmentMasterUpdate') {
      return AssessmentMasterUpdate.handler
    } else if (path === './pages/monthlyYearlyPatternSettingUpdate') {
      //GUI00940_月間・年間表パターン設定 の保存更新
      return MonthlyYearlyPatternSettingUpdate.handler
    } else if (path === './pages/monthlyYearlyTableUpdate') {
      //GUI00939_月間・年間表 の保存更新
      return MonthlyYearlyTableUpdate.handler
    } else if (path === './pages/assessmentHome63Update') {
      return CognitiveFunctionMentalActionHandycapUpdate.handler
    } else if (path === './pages/implementationPlan1Update') {
      // GUI00946_実施計画～① の保存更新
      return ImplementationPlan1Update.handler
    } else if (path === './pages/implementationPlan2Update') {
      // GUI00955_実施計画～② の保存更新
      return ImplementationPlan2Update.handler
    } else if (path === './pages/implementationPlanInfoUpdate') {
      // GUI00963_実施計画③マスタの保存更新
      return ImplementationPlanInfoUpdate.handler
    } else if (path === './pages/assessmentDuplicateUpdate') {
      return AssessmentDuplicateUpdate.handler
    } else if (path === './pages/assessmentInterRAIDUpdate') {
      return AssessmentInterRAIScreenDUpdate.handler
    } else if (path === './pages/assessmentHome4Update') {
      return AssessmentHomeTab4Update.handler
    } else if (path === './pages/assessmentInterRAIScreenVUpdate') {
      return AssessmentInterRAIScreenVUpdate.handler
    } else if (path === './pages/checkItemsUpdate') {
      return CheckItemsUpdate.handler
    } else if (path === './pages/assessmentComprehensivePsychologyUpdate') {
      return AssessmentComprehensivePsychologyUpdate.handler
    } else if (path === './pages/weekPlanPatternSettingUpdate') {
      return WeekPlanPatternSettingUpdate.handler
    } else if (path === './pages/freeAssessmentLedgerTitleSettingsMasterUpdate') {
      return freeAssessmentLedgerTitleSettingsMasterUpdate.handler
    } else if (path === './pages/symbolsMeaningUpdate') {
      return symbolsMeaningUpdate.handler
    } else if (path === './pages/consentFieldEditUpdate') {
      return consentFieldEditUpdate.handler
    } else if (path === './pages/specialNoteMattersUpdate') {
      return specialNoteMattersUpdate.handler
    } else if (path === './pages/assessmentInterRAIJUpdate') {
      return AssessmentInterRAIJUpdate.handler
    } else if (path === './pages/difficultyMasterUpdate') {
      return difficultyMasterUpdate.handler
    } else if (path === './pages/considerInitialInfoDataUpdate') {
      return considerInitialInfoDataUpdate.handler
    } else if (path === './pages/useSlipOtherDtlMaintenanceInfoUpdate') {
      return UseSlipOtherDtlMaintenanceInfoUpdate.handler
    } else if (path === './pages/issueOrganizeSummaryMasterUpdate') {
      return issueOrganizeSummaryMasterUpdate.handler
    } else if (path === './pages/supportElapsedRecordUpdate') {
      return supportElapsedRecordUpdate.handler
    } else if (path === './pages/inhibitoryFactorsMasterUpdate') {
      return inhibitoryFactorsMasterUpdate.handler
    } else if (path === './pages/header/officeGroupChange') {
      return Or00251OfficeGroupChange.handler
    } else if (path === './pages/issuesPlanningCategoryMasterUpdate') {
      return IssuesPlanningCategoryMasterUpdate.handler
    } else if (path === './pages/executionConfirmationMasterUpdate') {
      return ExecutionConfirmationMasterUpdate.handler
    } else if (path === './pages/preventionEvaluationTableTitleMasterUpdate') {
      return preventionEvaluationTableTitleMasterUpdate.handler
    } else if (path === './pages/issuesPlanningStyleTitleMasterUpdate') {
      return issuesPlanningStyleTitleMasterUpdate.handler
    } else if (path === './pages/dailyPlanPatternTitleUpdate') {
      return DailyPlanPatternTitleUpdate.handler
    } else if (path === './pages/dailyTaskTableMasterUpdate') {
      return DailyTaskTableMasterUpdate.handler
    } else if (path === './pages/gaibuItakuryouUpdate') {
      return GaibuItakuryouUpdate.handler
    } else if (path === './pages/cmPreventionSentenceMasterUpdate') {
      return CmPreventionSentenceMasterUpdate.handler
    } else if (path === './pages/kigoImiUpdate') {
      return KigoImiUpdate.handler
    } else if (path === './pages/opinionMasterUpdate') {
      return OpinionMasterUpdate.handler
    } else if (path === './pages/weekTblPatternInfoUpdate') {
      return WeekTblPatternInfoUpdate.handler
    } else if (path === './pages/issuesAnalysisPrintSettingsUpdate') {
      return IssuesAnalysisPrintSettingsUpdate.handler
    } else if (path === './pages/infoCollectionInitTenUpdate') {
      return InfoCollectionInitTenUpdate.handler
    } else if (path === './pages/assessmentComprehensiveMealUpdate') {
      return assessmentComprehensiveMealUpdate.handler
    } else if (path === './pages/assessmentComprehensiveBaseUpdate') {
      return AssessmentComprehensiveBaseUpdate.handler
    } else if (path === './pages/monitoringMasterInfoUpdate') {
      return MonitoringMasterInfoUpdate.handler
    } else if (path === './pages/inputSupportGoalAndLife1YearScreenUpdate') {
      return InputSupportGoalAndLife1YearScreenUpdate.handler
    } else if (path === './pages/maskMastUpdate') {
      return MaskMastUpdate.handler
    } else if (path === './pages/implementationMonitoringRegistInfoUpdate') {
      return implementationMonitoringRegistInfoUpdate.handler
    } else if (path === './pages/implementationPlan3Update') {
      // GUI00964_実施計画～③ の保存更新
      return ImplementationPlan3Update.handler
    } else if (path === './pages/situationMasterUpdate') {
      return SituationMasterUpdate.handler
    } else if (path === './pages/assessmentSummaryImportSettingUpdate') {
      return assessmentSummaryImportSettingUpdate.handler
    } else if (path === './pages/planTransferExecutionProcessUpdate') {
      return PlanTransferExecutionProcessUpdate.handler
    } else if (path === './pages/evaluationTableMasterInfoUpdate') {
      return evaluationTableMasterInfoUpdate.handler
    } else if (path === './pages/kentouhyoInfoUpdate') {
      return KentouhyoInfoUpdate.handler
    } else if (path === './pages/priorityOrderUpdate') {
      return PriorityOrderUpdate.handler
    } else if (path === './pages/integratedPlanMasterUpdate') {
      return IntegratedPlanMasterUpdate.handler
    } else if (path === './pages/freeAssessmentCheckPrintSettingsUpdate') {
      return FreeAssessmentCheckPrintSettingsUpdate.handler
    } else if (path === './pages/inquiryContentsInfoUpdate') {
      return InquiryContentsInfoUpdate.handler
    } else if (path === './pages/supportElapsedRecordMasterUpdate') {
      return supportElapsedRecordMasterUpdate.handler
    } else if (path === './pages/welfareUnitInfoUpdate') {
      return WelfareUnitInfoUpdate.handler
    } else if (path === './pages/freeAssessmentSheetDisplaySettingsUpdate') {
      return FreeAssessmentSheetDisplaySettingsUpdate.handler
    } else if (path === './pages/implementationMonitoringMasterUpdate') {
      return ImplementationMonitoringMasterUpdate.handler
    } else if (path === './pages/supportElapsedKindMasterUpdateService') {
      return supportElapsedKindMasterUpdateService.handler
    } else if (path === './pages/hospitalizationTimeInfoOfferSetingMasterUpdate') {
      return HospitalizationTimeInfoOfferSetingMasterUpdate.handler
    } else if (path === './pages/offerKindMasterUpdateService') {
      return offerKindMasterUpdateService.handler
    } else if (path === './pages/evaluationTablePrintSettingsInitUpdate') {
      return EvaluationTablePrintSettingsInitUpdate.handler
    } else if (path === './pages/monitoringConfigureMasterUpdate') {
      return MonitoringConfigureMasterUpdate.handler
    } else if (path === './pages/freeAssessmentOutputItemSettingsMasterUpdate') {
      return freeAssessmentOutputItemSettingsMasterUpdate.handler
    } else if (path === './pages/careProvisionMasterUpdate') {
      return careProvisionMasterUpdate.handler
    } else if (path === './pages/certificationSurveySpecialMatterUpdate') {
      return CertificationSurveySpecialMatterUpdate.handler
    } else if (path === './pages/affectedAreaImportSettingUpdate') {
      return AffectedAreaImportSettingUpdate.handler
    } else if (path === './pages/issuesAnalysisMasterUpdate') {
      return IssuesAnalysisMasterUpdate.handler
    } else if (path === './pages/interestAndConcernUpdate') {
      return InterestAndConcernUpdate.handler
    } else if (path === './pages/issuesAnalysisUpdate') {
      return IssuesAnalysisUpdate.handler
    } else if (path === './pages/assessmentComprehensivePrintSettingsInitialUpdate') {
      return AssessmentComprehensivePrintSettingsInitialUpdate.handler
    } else if (path === './pages/incentivesItemsPrintSettingsInitUpdate') {
      return IncentivesItemsPrintSettingsInitUpdate.handler
    } else if (path === './pages/weekTableImageInfoUpdate') {
      return weekTableImageInfoUpdate.handler
    } else if (path === './pages/duplicateAssessmentUpdate') {
      return duplicateAssessmentUpdate.handler
    } else if (path === './pages/dailyRoutinePlanUpdate') {
      return DailyRoutinePlanUpdate.handler
    } else if (path === './pages/welfareEquipmentLendingUnitCntBundleSettingsInfoUpdate') {
      return welfareEquipmentLendingUnitCntBundleSettingsInfoUpdate.handler
    } else if (path === './pages/cpnMucMygWidthUpdate') {
      return frameWidthModifiedMasterUpdate.handler
    } else if (path === './pages/freeAssessmentFaceSheetUpdate') {
      return freeAssessmentFaceSheetUpdate.handler
    } else if (path === './pages/freeAssessmentFaceSheetInitialUpdate') {
      return freeAssessmentFaceSheetInitialUpdate.handler
    } else if (path === './pages/freeAssessmentFaceSheetHistoryChangeUpdate') {
      return freeAssessmentFaceSheetHistoryChangeUpdate.handler
    } else if (path === './pages/freeAssessmentFaceSheetNewUpdate') {
      return freeAssessmentFaceSheetNewUpdate.handler
    } else if (path === './pages/freeAssessmentFaceSheetPlanningPeriodChangeUpdate') {
      return freeAssessmentFaceSheetPlanningPeriodChangeUpdate.handler
    } else if (path === './pages/riyouBeppyo1KatuCalcUpdateService') {
      return riyouBeppyo1KatuCalcUpdateService.handler
    } else if (path === './pages/meetingMinutesDetailPkgUpdate') {
      return MeetingMinutesDetailPkgUpdate.handler
    } else if (path === './pages/meetingMinutesDetailPtn1Update') {
      return MeetingMinutesDetailPtn1Update.handler
    } else if (path === './pages/serviceUseSlipAnnexedTableDataMoveUpdate') {
      return ServiceUseSlipAnnexedTableDataMoveUpdate.handler
    } else if (path === './pages/freeAssessmentFacePrintSettingsUpdate') {
      return FreeAssessmentFacePrintSettingsUpdate.handler
    } else if (path === './pages/confirmationMethodMasterUpdate') {
      return ConfirmationMethodMasterUpdate.handler
    } else if (path === './pages/correspondenceMasterUpdate') {
      return CorrespondenceMasterUpdate.handler
    } else if (path === './pages/satisfactionLevelMasterUpdate') {
      return SatisfactionLevelMasterUpdate.handler
    } else if (path === './pages/meetingMinutesDetailPtn2Update') {
      return MeetingMinutesDetailPtn2Update.handler
    } else if (path === './pages/preventionEvaluationTableSettingsMasterUpdate') {
      return PreventionEvaluationTableSettingsMasterUpdate.handler
    } else if (path === './pages/kaigiRokuMstUpdate') {
      return KaigiRokuMstUpdate.handler
    } else if (path === './pages/basicSurvey1InfoUpdate') {
      return BasicSurvey1InfoUpdate.handler
    } else if (path === './pages/basicSurvey2InfoUpdate') {
      return BasicSurvey2InfoUpdate.handler
    } else if (path === './pages/basicSurvey3InitialInfoUpdate') {
      return BasicSurvey3InitialInfoUpdate.handler
    } else if (path === './pages/basicSurvey4Update') {
      return BasicSurvey4Update.handler
    } else if (path === './pages/basicSurvey5InfoUpdate') {
      return BasicSurvey5InfoUpdate.handler
    } else if (path === './pages/specialnoteMatterInfoUpdate') {
      return SpecialnoteMatterInfoUpdate.handler
    } else if (path === './pages/kibohudanTorokuKakute') {
      return KibohudanTorokuKakute.handler
    } else if (path === './pages/cp2MasterUpdate') {
      return Cp2MasterUpdate.handler
    } else if (path === './pages/offerOfficeUpdate') {
      return offerOfficeUpdate.handler
    } else if (path === './pages/SDCareMasterEasyRegistUpdate') {
      return SDCareMasterEasyRegistUpdate.handler
    } else if (path === './pages/sealFieldDataUpdate') {
      return SealFieldDataUpdate.handler
    } else if (path === './pages/printSettingsInfoUpdateGUI00944') {
      return PrintSettingsInfoUpdateGUI00944.handler
    } else if (path === './pages/receiptSectionUpdate') {
      return ReceiptSectionUpdate.handler
    } else if (path === './pages/yokodasiServiceUnitReconfigureExecutionProcessUpdate') {
      return YokodasiServiceUnitReconfigureExecutionProcessUpdate.handler
    } else if (path === './pages/printSettingsInfoUpdateGUI01132') {
      return PrintSettingsInfoUpdateGUI01132.handler
    } else if (path === './pages/correspondingTablePrintSettingsInitUpdate') {
      return CorrespondingTablePrintSettingsInitUpdate.handler
    } else if (path === './pages/caseListInfoUpdate') {
      return CaseListInfoUpdate.handler
    } else if (path === './pages/assessmentComprehensiveLetterSizeUpdate') {
      return AssessmentComprehensiveLetterSizeUpdate.handler
    } else if (path === './pages/printSettingsInfoUpdateGUI00938') {
      return PrintSettingsInfoUpdateGUI00938.handler
    } else if (path === './pages/printSettingsInfoUpdateGUI01139') {
      return PrintSettingsInfoUpdateGUI01139.handler
    } else if (path === './pages/printSettingsInfoUpdateGUI01266') {
      return PrintSettingsInfoUpdateGUI01266.handler
    } else if (path === './pages/preventionBasicDetailInfoUpdate') {
      return PreventionBasicDetailInfoUpdate.handler
    } else if (path === './pages/dch/common/GUI00027/confirm') {
      return GUI00027Confirm.handler
    } else if (path === './pages/individualDuplicateHistoryDataUpdate') {
      return IndividualDuplicateHistoryDataUpdate.handler
    } else if (path === './pages/freeAssessmentFacePrintSettingsPrtNoChangeUpdate') {
      return FreeAssessmentFacePrintSettingsPrtNoChangeUpdate.handler
    } else if (path === './pages/specialInstructionsPeriodUpdate') {
      return SpecialInstructionsPeriodUpdate.handler
    } else if (path === './pages/printSettingsInfoUpdateGUI01085') {
      return PrintSettingsInfoUpdateGUI01085.handler
    } else if (path === './pages/printSettingsInfoUpdateGUI011264') {
      return PrintSettingsInfoUpdateGUI011264.handler
    } else if (path === './pages/sougouJigyoServiceUnitReconfigureExecutionProcessUpdate') {
      return SougouJigyoServiceUnitReconfigureExecutionProcessUpdate.handler
    } else if (path === './pages/attendingPhysicianStatementUpdate') {
      return AttendingPhysicianStatementUpdate.handler
    } else if (path === './pages/printSettingsInfoUpdateGUI1131') {
      return PrintSettingsInfoUpdateGUI1131.handler
    } else if (path === './pages/shortTermLeavingDateRegistUpdate') {
      return ShortTermLeavingDateRegistUpdate.handler
    } else if (path === './pages/printSettingsInfoComUpdate') {
      return printSettingsInfoComUpdate.handler
    } else if (path === './pages/planDuplicateInsuranceiInvalidDuplicateUpdate') {
      return planDuplicateInsuranceiInvalidDuplicateUpdate.handler
    } else if (path === './pages/planDuplicateUseSlipDuplicateUpdate') {
      return planDuplicateUseSlipDuplicateUpdate.handler
    } else if (path === './pages/planDuplicateCarePlanEtcBundleCheckUpdate') {
      return planDuplicateCarePlanEtcBundleCheckUpdate.handler
    } else if (path === './pages/planDuplicateCarePlanBundleUpdate') {
      return planDuplicateCarePlanBundleUpdate.handler
    } else if (path === './pages/planDuplicateCarePlanIndividualCheckUpdate') {
      return planDuplicateCarePlanIndividualCheckUpdate.handler
    } else if (path === './pages/R4sTucSok1InfoUpdate') {
      return ComprehensivePlanUpdate.handler
    } else if (path === './pages/useSlipInfoUpdate') {
      return useSlipInfoUpdate.handler
    } else if (path === './pages/savePrintSettingInfoSDCareUpdate') {
      return SavePrintSettingInfoSDCareUpdate.handler
    } else if (path === './pages/savePrintSettingInfoAndSDCareDownloadUpdate') {
      return SavePrintSettingInfoAndSDCareDownloadUpdate.handler
    } else if (path === './pages/printSettingsInfoUpdateGUI01034') {
      return PrintSettingsInfoUpdateGUI01034.handler
    } else if (path === './pages/attendingPhysicianStatementPrintSettingsInitUpdate') {
      return AttendingPhysicianStatementPrintSettingsInitUpdate.handler
    } else if (path === './pages/hospitalizationTimeInfoOfferUpdate') {
      return HospitalizationTimeInfoOfferUpdate.handler
    } else if (path === './pages/attendingPhysicianStatementPrintSettingsInfoUpdate') {
      return AttendingPhysicianStatementPrintSettingsInfoUpdate.handler
    } else if (path === './pages/freeAssessmentFacePrintSettingsInitUpdate') {
      return FreeAssessmentFacePrintSettingsInitUpdate.handler
    } else if (path === './pages/freeAssessmentFacePrintSettingsMemoUpdate') {
      return FreeAssessmentFacePrintSettingsMemoUpdate.handler
    } else if (path === './pages/printSettingsInfoUpdateGUI01119') {
      return PrintSettingsInfoUpdateGUI01119.handler
    } else if (path === './pages/printSettingsInfoUpdateGUI01035') {
      return PrintSettingsInfoUpdateGUI01035.handler
    } else if (path === './pages/weeklyPlanMasterInfoUpdate') {
      return WeeklyPlanMasterInfoUpdate.handler
    } else if (path === './pages/printSettingsInfoUpdateGUI01211') {
      return printSettingsInfoUpdateGUI01211.handler
    } else if (path === './pages/dailyTaskTablePrintSettingsInitUpdate') {
      return DailyTaskTablePrintSettingsInitUpdate.handler
    } else if (path === './pages/plan1PrintSettingsInitUpdate') {
      return Plan1PrintSettingsInitUpdate.handler
    } else if (path === './pages/evaluationTableSettingsMasterUpdate') {
      return evaluationTableSettingsMasterUpdate.handler
    } else if (path === './pages/issuesPlanningStyleSettingsMasterUpdate') {
      return issuesPlanningStyleSettingsMasterUpdate.handler
    } else if (path === './pages/kaigiBasyoMstUpdate') {
      return KaigiBasyoMstUpdate.handler
    } else if (path === './pages/useSlipDailyRateUsePeriodInfoUpdate') {
      return useSlipDailyRateUsePeriodInfoUpdate.handler
    } else if (path === './pages/dailyRoutinePlanPrintSettingsInitUpdate') {
      return DailyRoutinePlanPrintSettingsInitUpdate.handler
    } else if (path === './pages/implementationPlan1PrintSettingsInitUpdate') {
      return ImplementationPlan1PrintSettingsInitUpdate.handler
    } else if (path === './pages/simUpdate') {
      return simUpdate.handler
    } else if (path === './pages/simDelete') {
      return simDelete.handler
    } else if (path === './pages/implementationPlan1PrintSettingsInfoUpdate') {
      return ImplementationPlan1PrintSettingsInfoUpdate.handler
    } else if (path === './pages/assessmentInterRAIPrintSettingsUpdate') {
      return AssessmentInterRAIPrintSettingsUpdate.handler
    } else if (path === './pages/implementationMonitoringPrintSettingsInitUpdate') {
      return ImplementationMonitoringPrintSettingsInitUpdate.handler
    } else if (path === './pages/planMonitoringPrintSettingsUpdate') {
      return PlanMonitoringPrintSettingsUpdate.handler
    } else if (path === './pages/issuesConsiderPrintSettingsInitialUpdate') {
      return IssuesConsiderPrintSettingsInitialUpdate.handler
    } else if (path === './pages/PreventionEvaluationPrintSettingsUpdate') {
      return PreventionEvaluationPrintSettingsUpdate.handler
    } else if (path === './pages/issueOrganizeSummaryPrintSettingsUpdate') {
      return IssueOrganizeSummaryPrintSettingsUpdate.handler
    } else if (path === './pages/considerTableInterRAIPrintSettingsInitUpdate') {
      return ConsiderTableInterRAIPrintSettingsInitUpdate.handler
    } else if (path === './pages/leavingInfoRecordPrintSettingsInitialUpdate') {
      return LeavingInfoRecordPrintSettingsInitialUpdate.handler
    } else if (path === './pages/dailyRoutinePlanPrintSettingsInfoUpdate') {
      return DailyRoutinePlanPrintSettingsInfoUpdate.handler
    } else if (path === './pages/preventionPlanPrintUserSettingsSubjectUpdate') {
      return PreventionPlanPrintUserSettingsSubjectUpdate.handler
    } else if (path === './pages/preventionPlanPrintSettingsInitUpdate') {
      return PreventionPlanPrintSettingsInitUpdate.handler
    } else if (path === './pages/preventionPlanPrintSettingsInfoUpdate') {
      return PreventionPlanPrintSettingsInfoUpdate.handler
    } else if (path === './pages/assessmentInterRAIDuplicateUpdate') {
      return AssessmentInterRAIDuplicateUpdate.handler
    } else if (path === './pages/implementationPlan2PrintSettingsInitUpdate') {
      return ImplementationPlan2PrintSettingsInitUpdate.handler
    } else if (path === './pages/examinationIssueInfoNewSelect') {
      return ExaminationIssueInfoNewSelect.handler
    } else if (path === './pages/examinationIssueInfoSelect') {
      return ExaminationIssueInfoSelect.handler
    } else if (path === './pages/examinationIssueInfoUpdate') {
      return ExaminationIssueInfoUpdate.handler
    } else if (path === './pages/tucPlanUpdate') {
      return TucPlanUpdate.handler
    } else if (path === './pages/implementPlanTwoMasterUpdate') {
      return ImplementPlanTwoMasterUpdate.handler
    } else if (path === './pages/assessmentComprehensiveMedicalCareUpdate') {
      return AssessmentComprehensiveMedicalCareUpdate.handler
    } else if (path === './pages/organizingIssuesUpdate') {
      return OrganizingIssuesUpdate.handler
    } else if (path === './pages/baseCheckListUpdate') {
      return BaseCheckListUpdate.handler
    }
  } catch (error) {
    console.error('Error:', error)
  }

  return undefined
}

export default { post }
