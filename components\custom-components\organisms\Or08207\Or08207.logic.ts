/**
 * Or08207_基本情報画面入力フォーム
 * GUI01067_基本情報
 *
 * @description
 * 処理ロジック initialize処理とpinia領域操作用の処理を提供する
 *
 * <AUTHOR> HOANG SY TOAN
 */

import type { BasicInfo, Or08207StateType } from '../Or08207/Or08207.type'
import { Or27349Const } from '../Or27349/Or27349.constants'
import { Or27349Logic } from '../Or27349/Or27349.logic'
import { Or10883Logic } from '../Or10883/Or10883.logic'
import { Or10883Const } from '../Or10883/Or10883.constants'
import { Or08207Const } from './Or08207.constants'
import { useInitialize, useOneWayBindAccessor } from '#imports'

export namespace Or08207Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or08207Const.CP_ID(1),
      uniqueCpId,
      initTwoWayValue: {
        kihonList: [] as BasicInfo[],
      },
      childCps: [{ cpId: Or27349Const.CP_ID(1) }, { cpId: Or10883Const.CP_ID(1) }],
    })

    Or27349Logic.initialize(childCpIds[Or27349Const.CP_ID(1)].uniqueCpId)
    Or10883Logic.initialize(childCpIds[Or10883Const.CP_ID(1)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }

  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or08207StateType>(Or08207Const.CP_ID(0))
}
