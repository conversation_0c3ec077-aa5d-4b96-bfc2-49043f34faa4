import type { Mo00045Type } from '~/types/business/components/Mo00045Type'

/**
 * OrX0135:有機体:承認欄登録画面モーダル
 * GUI00617_承認欄登録画面
 */

/**
 *
 * 単方向バインドのデータ構造
 */
export interface OrX0135OnewayType {
  /**
   * ★ｻｰﾋﾞｽ事業者ID
   */
  svJigyoId: string
  /**
   * ★法人ID
   */
  houjinId: string
  /**
   * ★施設ID
   */
  shisetuId: string
  /**
   * 帳票コード
   */
  chohyoCd: string
}

/**
 * 双方向バインドModelValue
 */
export interface OrX0135Type {
  /**
   * key
   */
  [key: string]: Mo00045Type | string
  /**
   * 承認欄1行目下線無
   */
  text1Knj: Mo00045Type
  /**
   * 承認欄1行目下線有
   */
  day1Knj: Mo00045Type
  /**
   * 承認欄2行目下線無
   */
  text2Knj: Mo00045Type
  /**
   * 承認欄2行目下線有
   */
  day2Knj: Mo00045Type
  /**
   * 承認欄3行目下線無
   */
  text3Knj: Mo00045Type
  /**
   * 承認欄3行目下線有
   */
  day3Knj: Mo00045Type
  /**
   * 承認欄4行目下線無
   */
  text4Knj: Mo00045Type
  /**
   * 承認欄4行目下線有
   */
  day4Knj: Mo00045Type
  /**
   * 表示行数
   */
  dispKbn: string
  /**
   * 1行目文字サイズ
   */
  text1Font: string
  /**
   * 下線部分1行目幅
   */
  text1Width: string
  /**
   * 下線部分2行目幅
   */
  day1Width: string
  /**
   * 2行目文字サイズ
   */
  text2Font: string
  /**
   * 承認欄2行目幅
   */
  text2Width: string
  /**
   * 下線部分2行目幅
   */
  day2Width: string
  /**
   * 3行目文字サイズ
   */
  text3Font: string
  /**
   * 承認欄3行目幅
   */
  text3Width: string
  /**
   * 下線部分3行目幅
   */
  day3Width: string
  /**
   * 4行目文字サイズ
   */
  text4Font: string
  /**
   * 承認欄4行目幅
   */
  text4Width: string
  /**
   * 下線部分4行目幅
   */
  day4Width: string
  /**
   * 更新回数
   */
  modifiedCnt: string
  /**
   * 帳票コード
   */
  chohyoCd: string
}
