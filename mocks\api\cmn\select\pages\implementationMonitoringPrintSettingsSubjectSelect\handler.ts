import { HttpResponse } from 'msw'
import defaultData from './data/default.json'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { ImplementationMonitoringPrintSettingsSubjectSelectInEntity } from '~/repositories/cmn/entities/ImplementationMonitoringPrintSettingsInitUpdateEntity.ts'
/**
 * GUI01254_印刷設定
 *
 * @description
 * GUI01254_印刷設定対象一覧情報データを返却する。
 * dataName："implementationMonitoringPrintSettingsSubjectSelect"
 */
export function handler(inEntity: ImplementationMonitoringPrintSettingsSubjectSelectInEntity) {
  const responceJson: BaseResponseBody = {
    statusCode: 'success',
    data: {
      ...defaultData,
    },
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
