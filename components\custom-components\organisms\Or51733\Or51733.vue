<script setup lang="ts">
/**
 * Or51733:実施モニタリング記号 一覧  コンポーネント
 * GUI01250_実施モニタリング記号マスタ
 *
 * @description
 * 実施モニタリング記号 一覧  コンポーネント
 *
 * <AUTHOR>
 */
import {
  computed,
  reactive,
  ref,
  watch,
  nextTick,
  type ComponentPublicInstance,
  onUnmounted,
} from 'vue'
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'
import type { TableData } from './Or51733.type'
import { Or51733Const } from './Or51733.constants'
import { useScreenStore } from '#imports'
import type { Or51733OnewayType, Or51733Type } from '~/types/cmn/business/components/Or51733Type'
import { UPDATE_KBN } from '~/constants/classification-constants'
import { useScreenTwoWayBind, useSetupChildProps } from '~/composables/useComponentVue'
import { useValidation } from '~/utils/useValidation'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { ResizableGridBinding } from '~/plugins/resizableGrid.client'
import type {
  Mo01354Headers,
  Mo01354OnewayType,
  Mo01354Type,
} from '~/components/base-components/molecules/Mo01354/Mo01354Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import { CustomClass } from '~/types/CustomClassType'

const { t } = useI18n()
const validation = useValidation()
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or51733OnewayType
  modelValue: Or51733Type
  uniqueCpId: string
  parentUniqueCpId: string
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/

const defaultOnewayModelValue: Or51733OnewayType = {
  maskMastInfoList: [],
}

const defaultModelValue: Or51733Type = {
  delBtnDisabled: true,
  focusIndex: '',
  focusType: '',
  maskMastInfoList: [],
}
// ターブルヘッダー情報
const headers = [
  {
    title: t('label.category-number'),
    key: 'kbnCd',
    sortable: true,
    required: true,
  },
  {
    title: t('label.content'),
    key: 'textKnj',
    sortable: false,
    required: true,
  },
] as Mo01354Headers[]

const localOneWay = reactive({
  Or51733: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  mo00045Oneway_1: {
    maxLength: '4',
    min: 0,
    max: 9999,
    isEditCamma: false,
    isRequired: true,
    showItemLabel: false,
    rules: [
      validation.required,
      validation.numeric,
      validation.minValue(1000),
      validation.maxValue(9999),
    ],
    customClass: new CustomClass({
      outerClass: 'h-100',
      itemClass: 'h-100',
    }),
  } as Mo00045OnewayType,
  mo00045Oneway_2: {
    maxLength: '2',
    isRequired: true,
    showItemLabel: false,
    rules: [validation.required],
    customClass: new CustomClass({
      outerClass: 'h-100',
      itemClass: 'h-100',
    }),
  } as Mo00045OnewayType,
  mo01354Oneway: {
    columnMinWidth: {
      columnWidths: [168, 200],
    } as ResizableGridBinding,
    headers: headers,
    height: '392px',
    rowHeight: '32',
  } as Mo01354OnewayType,
})

const local = reactive({
  Or51733: {
    ...defaultModelValue,
    ...props.modelValue,
  },
})

const or1354Data = ref<Mo01354Type>({
  values: {
    selectedRowId: '',
    selectedRowIds: [],
    items: [],
  },
})

const or21814 = ref({ uniqueCpId: '' })

// ダイアログ表示フラグ
const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

// 元のテーブルデータ
const orgTableData = ref<TableData[]>([])

// テーブルデータ
const tableDataFilter = computed(() => {
  const titleList = refValue.value
  return titleList!.filter((i: TableData) => i.updateKbn !== UPDATE_KBN.DELETE)
})

const itemRefs = new Map<string, HTMLElement>()
const textRefs = new Map<string, HTMLElement>()

const setItemRef = (el: Element | ComponentPublicInstance | null, id: string, type: boolean) => {
  const elHtml: HTMLElement = el as HTMLElement
  if (el) {
    if (type) {
      itemRefs.set(id, elHtml)
    } else {
      textRefs.set(id, elHtml)
    }
  }
}

/**************************************************
 * Pinia
 **************************************************/

const { refValue } = useScreenTwoWayBind<TableData[]>({
  cpId: Or51733Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
refValue.value = []

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(0)]: or21814.value,
})
/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**
 * 実施モニタリング記号マスタ情報取得
 *
 */
function init() {
  // 戻り値はテーブルデータとして処理されます
  const tmpArr = local.Or51733.maskMastInfoList.map((item) => {
    return {
      ...item,
      kbnCd: { value: item.kbnCd },
      textKnj: { value: item.textKnj },
    }
  })

  // 元のテーブルデータの設定
  orgTableData.value = cloneDeep(tmpArr)

  refValue.value = cloneDeep(tmpArr)

  if (tmpArr.length > 0) {
    or1354Data.value.values.selectedRowId = tmpArr[0].id
  }

  // refValueを更新する
  setRefValue()
}

/**
 * 「新規」押下
 */
async function createRow() {
  // 実施モニタリング記号マスタのタイトル一覧の最終に新しい行を追加する。
  const data = {
    //テーブルINDEX(行固有ID)
    id: refValue.value?.length
      ? Math.max(...refValue.value.map((i) => parseInt(i.id))) + 1 + ''
      : '1',
    // 区分番号：空白
    kbnCd: { value: '' },
    // 内容：空白
    textKnj: { value: '' },
    // 入力ID
    cf1Id: '',
    // 入力区分
    cf1Flg: Or51733Const.DEFAULT.CF1FLG,
    // 更新回数
    modifiedCnt: '0',
    // 更新区分
    updateKbn: UPDATE_KBN.CREATE,
    // 区分フラグ
    kbnFlg: '',
    // 変更フラグ
    changeF: '0',
  }
  refValue.value!.push(data)
  or1354Data.value.values.selectedRowId = data.id
  await nextTick()
  setFocus(data.id, true)
}

/**
 * 行削除ボタン押下
 */
function deleteRow() {
  if (or1354Data.value.values.selectedRowId) {
    // 確認ダイアログを開く
    // メッセージを表示
    // 確認ダイアログのpiniaを設定
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: {
        // ダイアログタイトル
        dialogTitle: t('label.confirm-dialog-title-info'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-10764'),
        // 第1ボタンタイプ
        firstBtnType: 'normal1',
        // 第1ボタンラベル
        firstBtnLabel: t('btn.yes'),
        // 第2ボタンタイプ
        secondBtnType: 'normal3',
        // 第2ボタンラベル
        secondBtnLabel: t('btn.no'),
        // 第3ボタンタイプ
        thirdBtnType: 'blank',
        iconColor: 'rgb(var(--v-theme-blue-700))',
        iconBackgroundColor: 'rgb(var(--v-theme-blue-200))',
      },
    })
    // 確認ダイアログ表示
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: { isOpen: true },
    })
  }
}

/**
 * 行複写ボタン押下
 */
async function copyRow() {
  if (or1354Data.value.values.selectedRowId) {
    let index = 0
    if (refValue.value) {
      let data = null
      for (let i = 0; i < refValue.value.length; i++) {
        if (refValue.value[i].id === or1354Data.value.values.selectedRowId) {
          index = i
          // タイトル一覧の最終に新しい行を追加する。
          data = {
            // テーブルINDEX(行固有ID)
            id: refValue.value?.length
              ? Math.max(...refValue.value.map((i) => parseInt(i.id))) + 1 + ''
              : '1',
            // 区分番号：空白
            kbnCd: { value: refValue.value[i].kbnCd.value },
            // 内容：選択された行
            textKnj: { value: refValue.value[i].textKnj.value },
            // 入力ID
            cf1Id: '',
            // 入力区分
            cf1Flg: Or51733Const.DEFAULT.CF1FLG,
            // 更新回数
            modifiedCnt: '0',
            // 更新区分
            updateKbn: UPDATE_KBN.CREATE,
            // 区分フラグ
            kbnFlg: refValue.value[i].kbnFlg,
            // 変更フラグ
            changeF: '0',
          }
          break
        }
      }
      if (data !== null) {
        refValue.value.splice(++index, 0, data)
        await nextTick()
        or1354Data.value.values.selectedRowId = data.id
        setFocus(data.id, true)
      }
    }
  }
}

/**
 * フォーカス設定
 *
 * @param id - 行id
 *
 * @param type - true: 番号入力/false: 内容入力
 */
const setFocus = (id: string, type: boolean) => {
  const inputElement = type
    ? itemRefs.get(id)!.querySelector(`#input-${id}-or51733 input`)!
    : textRefs.get(id)!.querySelector(`#text-${id}-or51733 input`)!
  const inputHtmlElement = inputElement as HTMLElement
  inputHtmlElement.focus()
}

/**
 *  refValueを更新する
 */
function setRefValue() {
  useScreenStore().setCpTwoWay({
    cpId: Or51733Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: refValue.value,
    isInit: true,
  })
}

/**
 * データ変更処理
 *
 * @param tableData - 行情報
 */
const dataChange = (tableData: TableData) => {
  tableData.updateKbn = UPDATE_KBN.UPDATE
}

/**************************************************
 * ワッチャー
 **************************************************/
/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.firstBtnClickFlg) {
      if (or1354Data.value.values.selectedRowId) {
        let rowId = ''
        refValue.value!.forEach((item: TableData, index: number) => {
          if (item.id === or1354Data.value.values.selectedRowId) {
            // 更新前後配列長さ
            const newLength = tableDataFilter.value.length ?? 0
            if (newLength > 1) {
              // 削除するデータは配列の最後にあります
              if (item.id === tableDataFilter.value[newLength - 1].id) {
                rowId = tableDataFilter.value[newLength - 2].id
              } else {
                const idx = tableDataFilter.value.findIndex((sItem) => item.id === sItem.id)
                // 削除するデータは配列の最後にありません
                rowId = tableDataFilter.value[idx + 1].id
              }
            }
            // 新規行の場合
            if (item.updateKbn === UPDATE_KBN.CREATE) {
              // 当該行を廃棄する
              refValue.value?.splice(index, 1)
            } else {
              // 既存行の場合
              item.updateKbn = UPDATE_KBN.DELETE
            }
          }
        })
        // 一行だけ
        if (or1354Data.value.values.items.length === 1) {
          or1354Data.value.values.selectedRowId = ''
          return
        }
        // 選択行再設定
        or1354Data.value.values.selectedRowId = rowId
        await nextTick()
        // フォーカス
        setFocus(rowId, true)
      }
    } else {
      return
    }
  }
)

/**
 * 実施モニタリング記号一覧データ変更監視
 */
watch(
  () => props.modelValue.maskMastInfoList,
  (newValue) => {
    if (!newValue) return
    local.Or51733.maskMastInfoList = cloneDeep(newValue)
  },
  { deep: true }
)

/**
 * エラー行のフォーカス位置に戻す
 */
watch(
  () => props.modelValue.focusIndex,
  async (newValue) => {
    if (!newValue) return

    const focusIndex = newValue
    const row = refValue.value![Number(focusIndex)]
    or1354Data.value.values.selectedRowId = row.id
    await nextTick()
    if (props.modelValue.focusType === Or51733Const.DEFAULT.KBN_CD) {
        setFocus(row.id, true)
        or1354Data.value.values.selectedRowId = row.id
      } else {
        setFocus(row.id, false)
        or1354Data.value.values.selectedRowId = row.id
      }
  },
  { deep: true }
)

/**
 * エラー行のフォーカス位置に戻す
 */
watch(
  () => tableDataFilter.value,
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    or1354Data.value.values.items = newValue
  }
)

/**
 * 行選択
 */
watch(
  () => or1354Data.value.values.selectedRowId,
  (newValue) => {
    emit('update:modelValue', newValue)
  }
)
/**
 * コンポーネントがアンマウントされる際に実行されるクリーンアップ操作
 */
onUnmounted(() => {
  itemRefs.clear()
  textRefs.clear()
})

/**
 * コンポーネントのメソッドを親コンポーネントに公開する
 */
defineExpose({
  createRow,
  copyRow,
  deleteRow,
  init,
})
</script>

<template>
  <div class="flex-1-1">
    <!-- 実施モニタリング記号マスタ一覧 -->
    <!-- ヘッダ Part -->
    <div class="table-header">
      <base-mo-01354
        v-model="or1354Data"
        :oneway-model-value="localOneWay.mo01354Oneway"
        class="list-wrapper"
      >
        <!-- 区分番号 -->
        <template #[`item.kbnCd`]="{ item }">
          <div
            :ref="(el) => setItemRef(el, item.id, true)"
            class="h-100"
          >
            <!-- 分子：表用数値専用テキストフィールド -->
            <base-mo00045
              :id="`input-${item.id}-or51733`"
              v-model="item.kbnCd"
              class="background-transparent text-right"
              :oneway-model-value="localOneWay.mo00045Oneway_1"
              @update:model-value="dataChange(item)"
            />
          </div>
        </template>
        <!-- 内容 -->
        <template #[`item.textKnj`]="{ item }">
          <div
            :ref="(el) => setItemRef(el, item.id, false)"
            class="h-100"
          >
            <!-- 分子：表用数値専用テキストフィールド -->
            <base-mo00045
              :id="`text-${item.id}-or51733`"
              v-model="item.textKnj"
              class="background-transparent"
              :oneway-model-value="localOneWay.mo00045Oneway_2"
              @update:model-value="dataChange(item)"
            />
          </div>
        </template>
        <!-- ページングを非表示 -->
        <template #bottom />
      </base-mo-01354>
    </div>
    <!-- 説明:※区分番号は1000以上の値を入力してください。  -->
    <div class="font-weight-bold caption pt-4 font-red">
      {{ t('label.category-number-input') }}
    </div>
    <!-- 説明:※全共通  -->
    <div class="caption pt-2">
      {{ t('label.all-common') }}
    </div>
  </div>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  >
  </g-base-or21814>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table.scss';
.font-red {
  color: red;
}

:deep(.v-table__wrapper) {
  width: max-content !important;
}
.background-transparent {
  background-color: transparent !important;
}
// '番号'右寄せ
:deep(.text-right .v-field__input) {
  text-align: right;
}

.number-p-t {
  padding-top: 16px;
}

:deep(.v-messages__message) {
  text-align: left;
}
</style>
