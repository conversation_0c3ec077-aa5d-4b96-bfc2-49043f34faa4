/**
 * OrX0135:有機体:承認欄登録画面モーダル
 * GUI00617_承認欄登録画面
 *
 * @description
 * GUI00617_承認欄登録画面
 *
 *  <AUTHOR>
 */
import type { InWebEntity } from '~/repositories/AbstructWebRepository'

/**
 * 承認欄登録入力エンティティ
 */
export interface ApprovalColumnRegistrationUpdateInEntity extends InWebEntity {
  /**
   * ★ｻｰﾋﾞｽ事業者ID
   */
  svJigyoId: string
  /**
   * ★法人ID
   */
  houjinId: string
  /**
   * ★施設ID
   */
  shisetuId: string
  /**
   * 承認欄1行目
   */
  text1Knj: string
  /**
   * 承認欄2行目
   */
  text2Knj: string
  /**
   * 下線部分1行目
   */
  day1Knj: string
  /**
   * 下線部分2行目
   */
  day2Knj: string
  /**
   * 1行目文字サイズ
   */
  text1Font: string
  /**
   * 2行目文字サイズ
   */
  text2Font: string
  /**
   * 承認欄1行目幅
   */
  text1Width: string
  /**
   * 承認欄2行目幅
   */
  text2Width: string
  /**
   *下線部分1行目幅
   */
  day1Width: string
  /**
   * 下線部分2行目幅
   */
  day2Width: string
  /**
   * 承認欄3行目
   */
  text3Knj: string
  /**
   * 承認欄4行目
   */
  text4Knj: string
  /**
   * 下線部分3行目
   */
  day3Knj: string
  /**
   * 下線部分4行目
   */
  day4Knj: string
  /**
   * 3行目文字サイズ
   */
  text3Font: string
  /**
   * 4行目文字サイズ
   */
  text4Font: string
  /**
   * 承認欄3行目幅
   */
  text3Width: string
  /**
   * 承認欄4行目幅
   */
  text4Width: string
  /**
   *下線部分3行目幅
   */
  day3Width: string
  /**
   * 下線部分4行目幅
   */
  day4Width: string
  /**
   * 表示行数
   */
  dispKbn: string
  /**
   * 更新回数
   */
  modifiedCnt: string
  /**
   * 帳票コード
   */
  chohyoCd: string
}
