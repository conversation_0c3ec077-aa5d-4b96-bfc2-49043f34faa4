<script setup lang="ts">
/**
 * Or27670:（支援経過記録）支援経過確認一覧印刷設定モーダル
 * GUI01266_印刷設定
 *
 * @description
 * （支援経過記録）支援経過確認一覧印刷設定モーダル
 *
 * <AUTHOR> HOANG SY TOAN
 */
import { computed, onMounted, reactive, ref, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or27670Const } from './Or27670.constants'
import type { Or27670TwoWayData } from './Or27670.type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Or27670OnewayType } from '~/types/cmn/business/components/Or27670Type'
import {
  useScreenOneWayBind,
  useScreenStore,
  useScreenTwoWayBind,
  useSetupChildProps,
  useReportUtils,
} from '#imports'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  Mo01334Items,
  Mo01334OnewayType,
  Mo01334Type,
} from '~/types/business/components/Mo01334Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { CustomClass } from '~/types/CustomClassType'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { CmnMCdKbnId } from '~/constants/cmn/system-code-constants'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import type {
  PrintSettingScreenInitialInfoSelectInEntity,
  PrintSettingScreenInitialInfoSelectOutEntity,
} from '~/repositories/cmn/entities/PrintSettingsScreenInitialInfoSelectGUI01266Entity'

import type {
  LedgerInitializeDataSelectInEntity,
  LedgerInitializeDataSelectOutEntity,
} from '~/repositories/cmn/entities/LedgerInitializeDataSelectGUI01266Entity'
import type {
  PrintSettingsInfoUpdateGUI01266InEntity,
  choPrtList,
} from '~/repositories/cmn/entities/PrintSettingsInfoUpdateGUI01266Entity'
import type { NursingCareSupportDailyListReportInEntity } from '~/repositories/cmn/entities/NursingCareSupportDailyListReportEntity'
import { reportOutputType } from '~/utils/useReportUtils'

const { t } = useI18n()

// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or27670OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

// 帳票出力ユーティリティを取得
const { reportOutput } = useReportUtils()

const or21815 = ref({ uniqueCpId: Or27670Const.EMPTY })

// プロファイル
const choPro = ref(Or27670Const.EMPTY)

// 帳票ID
const reportId = ref('NursingCareSupportDailyList')

// 帳票イニシャライズデータを取得する
const reportInitData = ref({
  // 氏名伏字印刷
  prtName: Or27670Const.EMPTY,
  // 文書番号印刷
  prtBng: Or27670Const.EMPTY,
  // 個人情報印刷
  prtKojin: Or27670Const.EMPTY,
})

const localOneway = reactive({
  Or27670: {
    ...props.onewayModelValue,
  },
  mo00024Oneway: {
    width: '750px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or27670',
      toolbarTitle: t('label.print-set'),
      toolbarName: 'Or27670ToolBar',
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.close-btn'),
  } as Mo00611OnewayType,
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
    tooltipText: t('tooltip.confirm-print-btn'),
  } as Mo00609OnewayType,
  mo01338OneWayTitle: {
    value: t('label.print-settings-title'),
    customClass: {
      outerClass: Or27670Const.EMPTY,
      outerStyle: Or27670Const.EMPTY,
      labelClass: Or27670Const.EMPTY,
      labelStyle: 'display: none',
      itemClass: Or27670Const.EMPTY,
      itemStyle: Or27670Const.EMPTY,
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00039OneWay: {
    name: Or27670Const.EMPTY,
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  mo00020OneWay: {
    showItemLabel: false,
    width: '132px',
  } as Mo00020OnewayType,
  mo00045OnewayTitleInput: {
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
  } as Mo00045OnewayType,
})

const mo00024 = ref<Mo00024Type>({
  isOpen: Or27670Const.DEFAULT.IS_OPEN,
})

const local = reactive({
  textInput: {
    value: Or27670Const.EMPTY,
  } as Mo00045Type,
  titleInput: {
    value: Or27670Const.EMPTY,
  } as Mo00045Type,
  mo00039Type: Or27670Const.EMPTY,
  mo00020Type: {
    value: Or27670Const.EMPTY,
  } as Mo00020Type,
  mo00020TypePrintDateCreation: {
    value: Or27670Const.EMPTY,
  } as Mo00020Type,
})

/**
 * 出力帳票名一覧
 */
const mo01334OnewayReport = ref<Mo01334OnewayType>({
  headers: [
    {
      title: t('label.report'),
      key: 'prtTitle',
      sortable: false,
      minWidth: '100',
    },
  ],
  items: [],
  height: 200,
})

/**
 * 出力帳票名一覧
 */
const mo01334TypeReport = ref<Mo01334Type>({
  value: Or27670Const.EMPTY,
  values: [],
} as Mo01334Type)

/**************************************************
 * Pinia
 **************************************************/
/**
 * Piniaストアの設定
 * - ダイアログの開閉状態を管理
 * - uniqueCpIdを使用して一意の状態を識別
 */
const { setState } = useScreenOneWayBind({
  cpId: Or27670Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value?: boolean) => {
      mo00024.value.isOpen = value ?? Or27670Const.DEFAULT.IS_OPEN
    },
  },
})

const isEdit = computed(() => {
  return useScreenStore().getCpNavControl(props.uniqueCpId)
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21815Const.CP_ID(0)]: or21815.value,
})

const { refValue } = useScreenTwoWayBind<Or27670TwoWayData>({
  cpId: Or27670Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
})

onMounted(async () => {
  local.mo00020Type.value = systemCommonsStore.getSystemDate!

  await initCodes()
  await printSettingsScreenInitialInfoSelect()
})

/**
 * 汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 回数区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CREATED_DATE_PRINT_TYPE },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_PRINTED_CARE_LEVEL_3 },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  // 回数区分
  localOneway.mo00039OneWay.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )
}

/**
 * API No. 991
 * AC001-2: 印刷設定画面初期情報を取得する
 */
async function printSettingsScreenInitialInfoSelect() {
  const inputData: PrintSettingScreenInitialInfoSelectInEntity = {
    // システムコード
    sysCd: localOneway.Or27670.sysCd ?? Or27670Const.EMPTY,
    // システム略称
    sysRyaku: localOneway.Or27670.sysRyaku ?? Or27670Const.EMPTY,
    // 機能名
    kinounameKnj: Or27670Const.KINOU_NAME_KNJ,
    // 計画期間管理フラグ
    // 'TRUE：管理する、FALSE：管理しない
    kikanFlg: localOneway.Or27670.kikanFlg ?? Or27670Const.EMPTY,
    // 法人ID
    houjinId: localOneway.Or27670.houjinId ?? Or27670Const.EMPTY,
    // 施設ID
    shisetuId: localOneway.Or27670.shisetuId ?? Or27670Const.EMPTY,
    // 事業者ID
    svJigyoId: localOneway.Or27670.svJigyoId ?? Or27670Const.EMPTY,
    // 利用者ID
    userId: localOneway.Or27670.userId ?? Or27670Const.EMPTY,
    // 職員ID
    shokuId: localOneway.Or27670.shokuId ?? Or27670Const.EMPTY,
    // セクション名
    sectionName: localOneway.Or27670.sectionName ?? Or27670Const.EMPTY,
    // 選択帳票番号
    choIndex: localOneway.Or27670.choIndex ?? Or27670Const.EMPTY,
    /**
     * ケアプラン方式
     * '0:未作成、1:包括的、2:居宅GL、3:MDS-HC2.0、4:MDS2.1、5:新型養護老人ホームパッケージプラン
     */
    cpnFlg: localOneway.Or27670.cpnFlg ?? Or27670Const.EMPTY,
    /**
     * 計画書書式
     * '1:旧書式、2:新書式
     */
    shosikiFlg: localOneway.Or27670.shosikiFlg ?? Or27670Const.EMPTY,
    // 個人情報表示フラグ
    kojinhogoFlg: Or27670Const.ZERO,
    // 個人情報表示値
    sectionAddNo: Or27670Const.ZERO,
  }

  // バックエンドAPIから初期情報取得
  const ret: PrintSettingScreenInitialInfoSelectOutEntity = await ScreenRepository.select(
    'printSettingsScreenInitialInfoSelectGUI01266',
    inputData
  )

  // AC001-5.1: 画面.出力帳票一覧明細に引継情報.選択帳票インデックスに対するレコードを選択状態にする
  const mo01334OnewayList: Mo01334Items[] = []
  ret.data.choPrtList.forEach((item, idx) => {
    if (item) {
      mo01334OnewayList.push({
        id: String(idx),
        mo01337OnewayReport: {
          value: item.prtTitle,
          unit: Or27670Const.EMPTY,
        } as Mo01337OnewayType,
        ...item,
      } as Mo01334Items)
    }
  })

  mo01334OnewayReport.value.items = mo01334OnewayList

  if (localOneway.Or27670.selectLedgerNumber) {
    const idx = ret.data.choPrtList.findIndex(
      (item) => item.prtNo === localOneway.Or27670.selectLedgerNumber
    )
    mo01334TypeReport.value.value = idx !== -1 ? String(idx) : Or27670Const.ZERO
  } else {
    mo01334TypeReport.value.value = Or27670Const.ZERO
  }

  refValue.value = { choPrtList: ret.data.choPrtList }

  useScreenStore().setCpTwoWay({
    cpId: Or27670Const.CP_ID(1),
    uniqueCpId: props.uniqueCpId,
    value: refValue.value,
    isInit: true,
  })

  reportInitData.value = ret.data.iniDataObject
}

/**
 * API No.993
 * 帳票イニシャライズデータを取得する。
 */
async function getReportInfoDataList() {
  const inputData: LedgerInitializeDataSelectInEntity = {
    sysCd: localOneway.Or27670.sysCd ?? Or27670Const.EMPTY,
    kinounameKnj: Or27670Const.KINOU_NAME_KNJ,
    shokuId: localOneway.Or27670.shokuId ?? Or27670Const.EMPTY,
    sectionKnj: choPro.value,
    kojinhogoFlg: Or27670Const.ZERO,
    sectionAddNo: Or27670Const.ZERO,
  }

  // バックエンドAPIから初期情報取得
  const ret: LedgerInitializeDataSelectOutEntity = await ScreenRepository.select(
    'ledgerInitializeDataSelectGUI01266',
    inputData
  )
  reportInitData.value = ret.data.iniDataObject
}

/**
 * AC002: 印刷設定情報を保存する
 * API: No.992 : printSettingsInfoUpdateGUI01266
 *
 * @param choPrtList - 出力帳票印刷情報リスト
 */
async function printSettingsInfoUpdate(choPrtList: choPrtList[]) {
  const inputData: PrintSettingsInfoUpdateGUI01266InEntity = {
    sysCd: localOneway.Or27670.sysCd ?? Or27670Const.ONE,
    sysRyaku: localOneway.Or27670.sysRyaku ?? Or27670Const.ONE,
    kinounameKnj: Or27670Const.KINOU_NAME_KNJ,
    houjinId: localOneway.Or27670.houjinId ?? Or27670Const.ONE,
    shisetuId: localOneway.Or27670.shisetuId ?? Or27670Const.ONE,
    svJigyoId: localOneway.Or27670.svJigyoId ?? Or27670Const.ONE,
    shokuId: localOneway.Or27670.shokuId ?? Or27670Const.ONE,
    choPro: choPro.value,
    kojinhogoFlg: Or27670Const.ZERO,
    sectionAddNo: Or27670Const.ZERO,
    iniDataObject: { ...reportInitData.value },
    choPrtList: choPrtList,
  }

  // バックエンドAPIから初期情報取得
  await ScreenRepository.update('printSettingsInfoUpdateGUI01266', inputData)
}

/**
 * 警告ダイアログの開閉
 *
 * @returns ダイアログの選択結果（yes）
 */
async function showOr21815MsgOneBtn() {
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.caution'),
      // ダイアログテキスト
      iconName: 'warning',
      dialogText: t('message.w-cmn-20845'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815.value.uniqueCpId)

        let result = Or27670Const.YES

        if (event?.firstBtnClickFlg) {
          result = Or27670Const.YES
        }

        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: or21815.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 帳票タイトルが入力していない場合
 */
async function checkTitleInput() {
  if (local.titleInput.value) return
  const dialogResult = await showOr21815MsgOneBtn()
  switch (dialogResult) {
    case Or27670Const.YES: {
      let label = Or27670Const.EMPTY
      for (const item of mo01334OnewayReport.value.items) {
        if (item) {
          if (mo01334TypeReport.value.value === item.id && item.mo01337OnewayReport) {
            const data = item.mo01337OnewayReport as Mo01337OnewayType
            label = data.value
          }
        }
      }
      local.titleInput.value = label
      // タイトルを自動入力した場合、refValueを更新
      updateRefValueFromCurrentRow()
      break
    }
  }
  return
}

// 選択中の行のrefValueを更新する関数を追加
function updateRefValueFromCurrentRow() {
  const updatedItems = mo01334OnewayReport.value.items.map(
    ({ id, mo01337OnewayReport, ...rest }) => {
      if (id === mo01334TypeReport.value.value) {
        return {
          ...rest,
          prtTitle: local.titleInput.value,
          prndate: local.mo00039Type,
          param04: local.textInput.value,
        }
      }
      return { ...rest }
    }
  ) as choPrtList[]
  refValue.value = { choPrtList: updatedItems }
}

/**
 * 変更データを取得する
 *
 * @returns 出力帳票印刷情報リスト
 */
async function getDataTable() {
  const choPrtList = mo01334OnewayReport.value.items.map(({ id, mo01337OnewayReport, ...rest }) => {
    if (id === mo01334TypeReport.value.value) {
      return {
        ...rest,
        prtTitle: local.titleInput.value,
        prndate: local.mo00039Type,
        param04: local.textInput.value,
      }
    }
    return {
      ...rest,
    }
  }) as choPrtList[]

  refValue.value = { choPrtList: choPrtList }

  await nextTick()

  return choPrtList
}

/**
 * 閉じる
 */
async function close() {
  await checkTitleInput()

  const choPrtList = await getDataTable()

  if (isEdit.value) await printSettingsInfoUpdate(choPrtList)

  setState({ isOpen: false })
}

/**
 * 帳票データを作成する
 *
 * @returns 帳票データ
 */
function createReportData(): NursingCareSupportDailyListReportInEntity {
  return {
    // /** 計画書様式 1 */
    // cks_flg: localOneway.Or27670.shosikiFlg || Or27670Const.EMPTY,
    // /** タイトル */
    // title: local.titleInput.value || Or27670Const.EMPTY,
    // /** 印刷設定 */
    // printSet: {
    //   /** 指定日印刷区分 */
    //   shiTeiKubun: local.mo00039Type || Or27670Const.EMPTY,
    //   /** 指定日 */
    //   shiTeiDate: local.mo00020Type.value || Or27670Const.EMPTY,
    // },
    // /**
    //  *  印刷対象履歴
    //  */
    // printSubjectHistoryList: [
    //   {
    //     /** 利用者ID */
    //     userId: localOneway.Or27670.userId || Or27670Const.EMPTY,
    //   },
    // ],
    // /**
    //  * 印刷オプション
    //  */
    // printOption: {
    //   /** 事業者名 */
    //   jigyoshaKnj: localOneway.Or27670.jigyoshaKnj || Or27670Const.EMPTY,
    //   /** 開催日 */
    //   createYmd: systemCommonsStore.getSystemDate ?? Or27670Const.EMPTY,
    // },
    // /**
    //  * 事業所情報
    //  */
    // jigyoInfo: {
    //   /** 事業者ID */
    //   svJigyoId: systemCommonsStore.getSvJigyoId ?? Or27670Const.EMPTY,
    // },
    cks_flg: '1',
    title: '介護支援経過確認一覧',
    printSet: {
      shiTeiKubun: '2',
      shiTeiDate: '2025/07/18',
    },
    printSubjectHistoryList: [
      {
        userId: '1',
      },
    ],
    printOption: {
      jigyoshaKnj: '事業者名サンプル',
      createYmd: '2015/01/13',
    },
    jigyoInfo: {
      svJigyoId: '1',
    },
  }
}

/**
 * 印刷
 */
async function PDFDownload() {
  // TODO AC009-1 「印刷」ボタン押下
  await checkTitleInput()
  const choPrtList = await getDataTable()
  if (isEdit.value) await printSettingsInfoUpdate(choPrtList)

  try {
    // API処理失敗時のシステムエラーダイアログを非表示にする
    systemCommonsStore.setSystemErrorDialogType('hide')

    // 帳票データを作成
    const reportData = createReportData()

    // 帳票出力
    await reportOutput(reportId.value, reportData, reportOutputType.DOWNLOAD)
  } finally {
    // API処理失敗時のシステムエラーダイアログの表示タイプをデフォルトに戻す
    systemCommonsStore.setSystemErrorDialogType('details')
  }
  setState({ isOpen: false })
}

/**
 * （ダイアログ）の開閉状態を監視
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    mo00024.value.isOpen = true
    if (!newValue) {
      await close()
    }
  }
)

/**
 * 「出力帳票名」選択
 */
watch(
  () => mo01334TypeReport.value.value,
  async (newValue, oldValue) => {
    if (oldValue) {
      for (const item of mo01334OnewayReport.value.items) {
        if (item) {
          if (oldValue === item.id) {
            // 印刷オプションセクションの初期化を行う :
            // ①帳票タイトル :  出力帳票一覧明細に選択される行.帳票タイトル
            if (local.titleInput.value) item.prtTitle = local.titleInput.value
            // 印刷オプションセクションの初期化を行う :
            // ②日付印刷区分 : 出力帳票一覧明細に選択される行.日付表示有無
            item.prndate = local.mo00039Type
            // 印刷オプションセクションの初期化を行う
            // ②敬称 = 出力帳票一覧明細に選択される行.パラメータ04
            item.param04 = local.textInput.value
          }
        }
      }
    }

    for (const item of mo01334OnewayReport.value.items) {
      if (item) {
        if (newValue === item.id) {
          // 印刷オプションセクションの初期化を行う :
          // ①帳票タイトル :  出力帳票一覧明細に選択される行.帳票タイトル
          local.titleInput.value = item?.prtTitle as string
          // 印刷オプションセクションの初期化を行う :
          // ②日付印刷区分 : 出力帳票一覧明細に選択される行.日付表示有無
          local.mo00039Type = item?.prndate as string
          // 印刷オプションセクションの初期化を行う
          // ②敬称 = 出力帳票一覧明細に選択される行.パラメータ04
          local.textInput.value = item?.param04 as string

          choPro.value = item?.choPro as string

          await getReportInfoDataList()
        }
      }
    }
  }
)

/**
 * ラジオボタンの選択状態を追跡する
 */
watch(
  () => local.mo00039Type,
  async () => {
    await checkTitleInput()
    updateRefValueFromCurrentRow()
  }
)

/**
 * タイトル入力の変更を追跡する
 */
watch(
  () => local.titleInput.value,
  () => {
    updateRefValueFromCurrentRow()
  }
)

/**
 *  テーブルの変更を追跡する
 */
watch(
  () => mo01334OnewayReport.value.items,
  () => {
    const choPrtList = [
      ...mo01334OnewayReport.value.items.map(({ id, mo01337OnewayReport, ...rest }) => ({
        ...rest,
      })),
    ] as choPrtList[]
    refValue.value = { choPrtList: choPrtList }
  },
  { deep: true }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        no-gutter
        class="or27670_screen"
      >
        <c-v-col
          cols="12"
          sm="4"
          class="pa-0 pt-2 px-2 or27670_border_right"
        >
          <base-mo-01334
            v-model="mo01334TypeReport"
            :oneway-model-value="mo01334OnewayReport"
            class="list-wrapper"
          >
            <!-- 帳票 -->
            <template #[`item.prtTitle`]="{ item }">
              <base-mo01337 :oneway-model-value="item.mo01337OnewayReport" />
            </template>
            <template #bottom />
          </base-mo-01334>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="8"
          class="pa-0 pt-2 content_center"
        >
          <c-v-row
            no-gutter
            class="or27670_row flex-center"
          >
            <c-v-col
              cols="12"
              sm="3"
              class="pa-0"
            >
              <base-mo01338 :oneway-model-value="localOneway.mo01338OneWayTitle"></base-mo01338>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="9"
              class="pa-0"
            >
              <base-mo00045
                v-model="local.titleInput"
                :oneway-model-value="localOneway.mo00045OnewayTitleInput"
                @keyup.enter="checkTitleInput"
              />
            </c-v-col>
          </c-v-row>
          <c-v-divider class="my-0"></c-v-divider>
          <c-v-row
            no-gutter
            class="customCol or27670_row"
          >
            <c-v-col
              cols="12"
              sm="7"
              class="pa-0"
            >
              <!-- 印刷日付選択ラジオボタングループ -->
              <base-mo00039
                v-model="local.mo00039Type"
                :oneway-model-value="localOneway.mo00039OneWay"
              >
              </base-mo00039>
            </c-v-col>
            <c-v-col
              id="test"
              cols="12"
              sm="5"
              class="pa-0"
            >
              <!-- 印刷日付ラベル -->
              <base-mo00020
                v-if="local.mo00039Type === Or27670Const.TWO"
                v-model="local.mo00020Type"
                :oneway-model-value="localOneway.mo00020OneWay"
                @mousedown="checkTitleInput"
              >
              </base-mo00020>
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>

    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          @click="close()"
        ></base-mo00611>
        <!-- 印刷ボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          class="mx-2"
          @click="PDFDownload()"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <g-base-or21815 v-bind="or21815"> </g-base-or21815>
</template>
<style scoped lang="scss">
@use '@/styles/base.scss';
@use '@/styles/cmn/dialog-base.scss';
@use '@/styles/cmn/dialog-data-table-list.scss';
.or27670_screen {
  margin: -8px !important;
  padding: 8px !important;
}
.or27670_border_right {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  padding: 0px !important;
}
.or27670_row {
  margin: 0px !important;
}
.content_center {
  padding: 0px !important;

  :deep(.label-area-style) {
    display: none;
  }
  .customCol {
    margin-left: 0px;
    margin-right: 0px;
  }
}
.flex-center {
  display: flex;
  align-items: center;
}
.user_class {
  margin-left: -12px;
}

:deep(.v-data-table__td .v-col) {
  padding: 0px !important;
}

:deep(.selected-row) {
  background-color: rgb(var(--v-theme-blue-100)) !important;
  border: 2px solid rgb(var(--v-theme-blue-600)) !important;
  box-sizing: border-box;
}
</style>
