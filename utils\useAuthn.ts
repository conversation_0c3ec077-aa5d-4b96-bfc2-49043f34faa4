import { nextTick, useN<PERSON><PERSON><PERSON><PERSON>, useRouter } from '#imports'
import { useCookieData } from '@/utils/useCookieData'
import { RepositoryError } from '~/repositories/RepositoryError'
import { AuthnRepository } from '~/repositories/business/core/authn/AuthnRepository'
import type { ILoginOutEntity } from '~/repositories/business/core/authn/entities/LoginEntity'
import type { IAuthnCheckOutEntity } from '~/repositories/business/core/authn/entities/AuthnCheckEntity'
import type { IAuthnUserProfileOutEntity } from '~/repositories/business/core/authn/entities/AuthnUserProfile'
import type { DebugLogPluginInterface } from '~/plugins/debug-log'
import { LOCAL_STORAGE_ROUTE_COMMONS, LOCAL_STORAGE_SYSTEM_COMMONS } from '~/constants/constants'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { OutWebEntity } from '~/types/common/api/OutWebEntity'
import type { AuthzOtherCaremanagerType } from '~/types/business/stores/SystemCommonsType'

/**
 * ログインタイプ定数
 */
export namespace LoginType {
  /**
   * システム運用者
   */
  export const SYSTEM_OPERATOR = '1'
  /**
   * システム管理者
   */
  export const SYSTEM_ADMIN = '2'
  /**
   * 一般ユーザー
   */
  export const GENERAL_USER = '3'
}
/**
 * システム運用者
 *
 * @remarks 個別にエクスポートしないとNuxtが自動生成するimport.mjsでSyntaxErrorになるため必要
 */
export const SYSTEM_OPERATOR = LoginType.SYSTEM_OPERATOR
/** システム管理者 */
export const SYSTEM_ADMIN = LoginType.SYSTEM_ADMIN
/** 一般ユーザー */
export const GENERAL_USER = LoginType.GENERAL_USER

/** トークン種別 */
export namespace TokenType {
  /** Web */
  export const WEB = '1'
  /** API */
  export const API = '2'
  /** モバイル */
  export const MOBILE = '3'
  /** アクティベーション */
  export const ACTIVATION = '4'
  /** システム管理者パスワード再設定 */
  export const SYSTEM_ADMIN_PASSWORD_RESET = '5'
}
/** Web */
export const WEB = TokenType.WEB
/** API */
export const API = TokenType.API
/** モバイル */
export const MOBILE = TokenType.MOBILE
/** アクティベーション */
export const ACTIVATION = TokenType.ACTIVATION
/** システム管理者パスワード再設定 */
export const SYSTEM_ADMIN_PASSWORD_RESET = TokenType.SYSTEM_ADMIN_PASSWORD_RESET

const isLoggedIn = async (): Promise<boolean> => {
  let result = false

  const cookie = useCookieData().get({ key: 'auth-token' })
  if (cookie.value) {
    result = await tokenCheck(cookie.value)
  } else {
    result = false
  }

  return result
}

const tokenCheck = async (authToken: string): Promise<boolean> => {
  let result = true

  let res: IAuthnCheckOutEntity
  try {
    // const repository = new AuthnRepository()
    res = await AuthnRepository.validAuthToken({ authToken, userId: '' })
    result = res.data?.validToken ?? false
  } catch (e: unknown) {
    const $log = useNuxtApp().$log as DebugLogPluginInterface
    $log.debug(e)
    result = false
  }

  return result
}

interface LoginResult {
  isError: boolean
  message?: string
  errorCode?: string
}

/**
 * ログイン処理
 *
 * @param userId - ログインID
 *
 * @param password - パスワード
 *
 * @param dbNumber - DB番号
 *
 * @param loginType - ログインタイプ
 *
 * @param tokenType - トークン種別
 *
 * @returns Promise
 */
const login =
  (userId: string, password: string, dbNumber: string, loginType: string, tokenType: string) =>
  async (): Promise<LoginResult | undefined> => {
    let res: ILoginOutEntity
    let ret: LoginResult | undefined = undefined
    try {
      res = await AuthnRepository.login({ userId, password, dbNumber, loginType, tokenType })
    } catch (e: unknown) {
      if (e instanceof RepositoryError) {
        return {
          isError: true,
          message: e.detail.info?.message,
          errorCode: e.detail.info?.messageCode,
        }
      } else {
        return { isError: true, message: (e as Error).message }
      }
    }

    // パスワード有効期限切れ且つログイン可設定の場合、レスポンスステータスコードは200で返却されるため
    // エラーコードをチェックしてコンポーネントに返す値を生成する
    if (res!.info?.messageCode === 'e.com.9019') {
      ret = { isError: false, message: res!.info.message, errorCode: res!.info.messageCode }
    }

    // クッキー登録 認証トークン
    useCookieData().set({
      key: 'auth-token',
      value: res!.data?.authToken,
    })

    // クッキー登録 DB番号
    useCookieData().set({
      key: 'db-number',
      value: dbNumber,
    })

    // クッキーへの反映待機
    await nextTick()

    const systemCommonsStore = useSystemCommonsStore()

    systemCommonsStore.$reset()
    localStorage.removeItem(LOCAL_STORAGE_SYSTEM_COMMONS)
    localStorage.removeItem(LOCAL_STORAGE_ROUTE_COMMONS)

    // ログイン処理
    systemCommonsStore.setCurrentUser({
      authToken: res!.data?.authToken,
      loginId: userId,
      chkShokuId: res!.data?.chkShokuId,
      shokuinKnj: res!.data?.shokuinKnj,
    })

    // ケアマネジャーマスタ情報の取得
    const caremanagerMaster = await ScreenRepository.select<
      OutWebEntity<{ settings: AuthzOtherCaremanagerType[] }>
    >('settingsCmnSelect', {})

    // ケアマネジャーマスタ情報の設定
    if (caremanagerMaster.data) {
      systemCommonsStore.setSubsystemSettings('cmn', caremanagerMaster.data.settings)
    }

    return ret
  }

/**
 * ログアウト処理
 */
const logout = async () => {
  const systemCommonsStore = useSystemCommonsStore()
  try {
    const currentUser = systemCommonsStore.getCurrentUser
    await AuthnRepository.logout({
      token: currentUser.authToken ?? '',
      userId: currentUser.loginId ?? '',
    })
  } catch (e: unknown) {
    if (e instanceof RepositoryError) {
      return { isError: true, message: e.detail.info?.message }
    } else {
      return { isError: true, message: (e as Error).message }
    }
  }

  // クッキーの削除
  useCookieData().set({ key: 'auth-token', value: null })
  useCookieData().set({ key: 'db-number', value: null })
  // ログイン情報の削除
  systemCommonsStore.resetCurrentUser()
  await useRouter().push('/login')

  systemCommonsStore.$reset()
  localStorage.removeItem(LOCAL_STORAGE_SYSTEM_COMMONS)
  localStorage.removeItem(LOCAL_STORAGE_ROUTE_COMMONS)
}

/**
 * ユーザ情報取得
 *
 * @param loginId  - ログインID
 *
 * @returns ユーザ情報
 */
const fetchUserProfile = async (loginId: string): Promise<IAuthnUserProfileOutEntity> => {
  const res = await AuthnRepository.fetchUserProfile({
    loginId,
  })

  return res
}

/**
 * useAuthn
 */
export const useAuthn = () => {
  return {
    isLoggedIn,
    login: (
      userId: string,
      password: string,
      dbNumber: string,
      loginType: string,
      tokenType: string
    ) => login(userId, password, dbNumber, loginType, tokenType),
    logout,
    fetchUserProfile,
  }
}
