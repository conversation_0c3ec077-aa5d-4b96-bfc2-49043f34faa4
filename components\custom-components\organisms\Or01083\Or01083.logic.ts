import  { Or01083Const } from './Or01083.constants'
import type { Or01083Type } from '~/types/cmn/business/components/Or01083Type'
import { useInitialize, useTwoWayBindAccessor } from '~/composables/useComponentLogic'

/**
 * Or01083：有機体：(日課計画マスタ)文字サイズ入力
 * 処理ロジック
 *
 * @description
 * initialize処理とpinia領域操作用の処理を提供する
 */
export namespace Or01083Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or01083Const.CP_ID(0),
      uniqueCpId,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [],
    })
    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }

  /**
   * TwoWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const data = useTwoWayBindAccessor<Or01083Type>(Or01083Const.CP_ID(0))
}
