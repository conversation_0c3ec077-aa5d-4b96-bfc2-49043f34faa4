import { Or10889Const } from './Or10889.constants'
import type { Or10889StateType } from './Or10889.type'
import { Or27751Const } from '~/components/custom-components/organisms/Or27751/Or27751.constants'
import { Or27751Logic } from '~/components/custom-components/organisms/Or27751/Or27751.logic'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'

/**
 * Or10889:有機体:薬剤検索マスタ
 * GUI00670_薬剤検索マスタ
 *
 * 処理ロジック
 *
 * @description
 * initialize処理とpinia領域操作用の処理を提供する
 */
export namespace Or10889Logic {
  /**
   * initialize
   *
   * @description
   * コンポーネントの初期化処理。
   * 共通関数を呼びpiniaの領域を初期化する。
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or10889Const.CP_ID(0),
      uniqueCpId,
      childCps: [
        { cpId: Or27751Const.CP_ID(1) },
      ],
      editFlgNecessity: false,
    })

    // 子コンポーネントのセットアップ
    Or27751Logic.initialize(childCpIds[Or27751Const.CP_ID(1)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }
  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or10889StateType>(Or10889Const.CP_ID(0))
}
