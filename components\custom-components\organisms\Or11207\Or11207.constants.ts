import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * Or11207：有機体：概況調査
 * 静的データ
 */
export namespace Or11207Const {
  /**
   * コンポーネントID
   *
   * @param seq  - 連番
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or11207', seq)
  /**
   * 計画期間ID
   */
  export const sc1Id_0 = '0'
  /**
   * 前回認定結果2
   */
  export const oldNinteiDo_2 = '2'
  /**
   * 前回認定結果3
   */
  export const oldNinteiDo_3 = '3'
  /**
   * 調査票改訂フラグ4
   */
  export const dmyCho_4 = '4'
  /**
   * 調査票改訂フラグ5
   */
  export const dmyCho_5 = '5'
   /**
    * date
    */
  export const ymd_20150401 = 20150401
   /**
    * date
    */
  export const ymd_20180401 = 20180401
   /**
    * checkbox select
    */
  export const checkBox_on = '1'
   /**
    * 処理モード:”1”
    */
  export const mode_1 = '1'
   /**
    * 会議録
    */
  export const ID42020001 = '42020001'
}
