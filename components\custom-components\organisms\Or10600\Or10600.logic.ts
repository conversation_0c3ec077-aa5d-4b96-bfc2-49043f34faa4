import { Or01533Const } from '../Or01533/Or01533.constants'
import { Or01533Logic } from '../Or01533/Or01533.logic'
import { Or10600Const } from './Or10600.constants'
import type { Or10600StateType } from './Or10600.type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { OrX0053Const } from '~/components/custom-components/organisms/OrX0053/OrX0053.constants'
import { OrX0053Logic } from '~/components/custom-components/organisms/OrX0053/OrX0053.logic'
import { OrX0054Const } from '~/components/custom-components/organisms/OrX0054/OrX0054.constants'
import { OrX0054Logic } from '~/components/custom-components/organisms/OrX0054/OrX0054.logic'

/**
 * Or27523:処理ロジック
 * GUI03248_月間・年間表パターン画面(タイトル)
 *
 * @description
 * 処理ロジック
 *
 * <AUTHOR>
 */
export namespace Or10600Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or10600Const.CP_ID(0),
      uniqueCpId,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [
        { cpId: Or01533Const.CP_ID(0) },
        { cpId: OrX0053Const.CP_ID(0) },
        { cpId: OrX0054Const.CP_ID(0) },
        { cpId: Or21813Const.CP_ID(0) },
        { cpId: Or21814Const.CP_ID(0) },
      ],
    })

    // 子コンポーネントのセットアップ
    Or01533Logic.initialize(childCpIds[Or01533Const.CP_ID(0)].uniqueCpId)
    OrX0053Logic.initialize(childCpIds[OrX0053Const.CP_ID(0)].uniqueCpId)
    OrX0054Logic.initialize(childCpIds[OrX0054Const.CP_ID(0)].uniqueCpId)
    Or21813Logic.initialize(childCpIds[Or21813Const.CP_ID(0)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(0)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }
  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or10600StateType>(Or10600Const.CP_ID(0))
}
