<script setup lang="ts">
/**
 * Or05059:(見通し)意向
 * GUI00916_見通し
 *
 * @description
 * (見通し)意向
 *
 * <AUTHOR> NGUYEN MANH HUNG
 */
import { useI18n } from 'vue-i18n'
import { ref, reactive, computed, watch } from 'vue'
import { Or05059Const } from './Or05059.constants'
import { useScreenTwoWayBind, useSetupChildProps, useSystemCommonsStore } from '#imports'
import type { Or05059Type } from '~/types/cmn/business/components/Or05059Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Or51775OnewayType, Or51775Type } from '~/types/cmn/business/components/Or51775Type'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import { Or51775Const } from '../Or51775/Or51775.constants'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
/**
 * システム共有情報ストア
 */
const systemCommonsStore = useSystemCommonsStore()
/**
 * useI18n
 */
const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or05059Type
  uniqueCpId: string
  parentUniqueCpId: string
}

/**
 * Propsの定義
 */
const props = defineProps<Props>()

/**
 * 子コンポーネントのプロパティを保持する変数
 * - uniqueCpId: 子コンポーネントの一意のID
 */
const or51775 = ref({ uniqueCpId: Or51775Const.CP_ID(0) })

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])
/**************************************************
 * Pinia
 **************************************************/
/**
 * useScreenTwoWayBind
 */
const { refValue } = useScreenTwoWayBind<Or05059Type>({
  cpId: Or05059Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or51775Const.CP_ID(0)]: or51775.value,
})

/**
 * ダイアログ表示フラグ
 * - Or51775のダイアログ開閉状態を取得
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

/**
 * ローカルのOnewayオブジェクト
 */
const localOneway = reactive({
  memoInputIconBtn: {
    btnIcon: 'edit_square',
    name: 'serveyLedgerImportBtn',
    density: 'compact',
  } as Mo00009OnewayType,

  mo01338OnewayObstacleFactors: {
    value: t('label.user-family-life-intention-label'),
  } as Mo01338OnewayType,

  mo00045Oneway: {
    hideDetails: true,
    class: 'index',
    customClass: new CustomClass({
      outerClass: 'mx-0',
    }),
    width: '290px',
    showItemLabel: false,
    maxLength: '4000',
    autoGrow: false,
    rows: 3,
    maxRows: '3',
  } as Mo00045OnewayType,

  or51775Oneway: {
    title: Or05059Const.VARIABLE.INTENTION,
    screenId: '',
    bunruiId: '2',
    t1Cd: '750',
    t2Cd: '2',
    t3Cd: '0',
    tableName: 'cpn_tuc_kss1',
    columnName: 'ikou_knj',
    assessmentMethod: systemCommonsStore.getAuthzOtherCaremanager?.assessmentMethod ?? '',
    inputContents: '',
    userId: systemCommonsStore.getUserId ?? '',
    mode: '',
  } as Or51775OnewayType,
  mo00009OneWay: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,
})
const defaultModelValue = ref<Or05059Type>({ value: '' } as Mo00045Type)
/**
 * フォーム値のローカル状態
 */
const local = reactive({
  Or05059: { ...defaultModelValue.value, ...props.modelValue },
  Or51775: { modelValue: '' } as Or51775Type,
})

/**
 * Or28285のダイアログ開閉状態を更新する
 */
const onClickOr51775 = () => {
  localOneway.or51775Oneway.inputContents = refValue.value?.value ?? ''
  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}

const onConfirmOr51775 = (data: Or51775ConfirmType) => {
  // タイプによって処理を分ける
  if (data.type === Or05059Const.VARIABLE.STRING_1) {
    // タイプが1の場合：完全に置き換え
    if (refValue.value) {
      refValue.value.value = data.value
    }
  } else {
    // タイプが1以外の場合：既存の値に追加
    if (refValue.value) {
      refValue.value.value = refValue.value.value + data.value
    }
  }
}

const onChangeMo00045 = () => {
  if (refValue.value) {
    refValue.value.value = formatForDisplay(refValue.value.value, 19, 3)
  }
  emit('update:modelValue', local.Or05059)
}
function formatForDisplay(text: string, numPerLine: number, numberLine: number): string {
  if (!text || !text.length) return ''

  const cleaned = text.replace(/\r?\n/g, '').trim()
  const lines: string[] = []
  let line = '',
    i = 0

  while (i < cleaned.length && lines.length < numberLine) {
    line += cleaned[i]
    if (line.length === numPerLine) {
      lines.push(line)
      line = ''
    }
    i++
  }

  if (line) lines.push(line)

  if (i < cleaned.length && lines.length === numberLine) {
    lines[numberLine - 1] = lines[numberLine - 1].slice(0, numPerLine - 1) + '…'
  }

  return lines.join('\n')
}
</script>

<template>
  <div>
    <c-v-row>
      <!-- 左列 -->
      <c-v-col
        cols="auto"
        class="obstacle-factors-box"
      >
        <div class="d-flex align-center">
          <base-mo01338
            :oneway-model-value="localOneway.mo01338OnewayObstacleFactors"
            class="bg-transparent"
          />
          <base-mo-00009
            :oneway-model-value="localOneway.memoInputIconBtn"
            class="border-left"
            @click.stop="onClickOr51775"
          />
        </div>
      </c-v-col>
      <!-- 中央列 -->
      <c-v-col
        cols="auto"
        class="d-flex flex-column"
        style="padding-top: 8px !important; padding-bottom: 8px !important"
      >
        <base-mo00046
          v-model="refValue"
          :oneway-model-value="localOneway.mo00045Oneway"
          class="mb-2"
          @change="onChangeMo00045"
        />
      </c-v-col>
      <!-- 右列 -->
      <c-v-col
        cols="auto"
        class="d-flex align-center px-0"
      >
      </c-v-col>
    </c-v-row>
    <!-- ダイアログ -->
    <!-- Or51775ダイアログ -->
    <g-custom-or-51775
      v-if="showDialogOr51775"
      v-bind="or51775"
      v-model="local.Or51775"
      :oneway-model-value="localOneway.or51775Oneway"
      @confirm="onConfirmOr51775"
    />
  </div>
</template>

<style scoped lang="scss">
.bg-transparent {
  background-color: transparent !important;
}
.obstacle-factors-box {
  margin-left: 14px;
  margin-top: 6px;
  height: 97px;
  background: rgb(var(--v-theme-black-100));
  border: 1px solid rgba(var(--v-theme-black-300));
  white-space: pre-line;
}
.table-box {
  border: 1px solid rgb(var(--v-theme-black-100));
  overflow: hidden;
  margin-bottom: 8px;
}
.row-flex {
  display: flex;
  border-bottom: 1px solid rgb(var(--v-theme-black-100));
  &:last-child {
    border-bottom: none;
  }
}
.cell-flex {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  text-align: center;
  padding: 8px;
  border-right: 1px solid rgb(var(--v-theme-black-100));
  height: 45px;
  &:last-child {
    border-right: none;
  }
}

.border-left {
  border-left: 1px solid rgba(var(--v-theme-black-300));
  border-radius: 0 !important;
  align-self: center;
}
</style>
