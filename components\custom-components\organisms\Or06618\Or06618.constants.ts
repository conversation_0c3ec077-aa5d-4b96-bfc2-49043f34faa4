import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * Or06618:モーダル:(日課計画マスタ)コンテンツエリアタブ
 * GUI01056_日課計画マスタ
 *
 * @description
 * 静的データ
 *
 * <AUTHOR>
 */
export namespace Or06618Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or06618', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false
    /**
     *CANCEL
     */
    export const DIALOG_RESULT_CANCEL = 'cancel'
    /**
     *YES
     */
    export const DIALOG_RESULT_YES = 'yes'
    /**
     *NO
     */
    export const DIALOG_RESULT_NO = 'no'

    /**
     * 初期取込
     */
    export const INITIAL_UPTAKE = '2'
    /**
     * 時間
     */
    export const TIME = '4'
    /**
     * 文字サイズ
     */
    export const CHAR_SIZE = '3'
    /**
     * 文字位置
     */
    export const CHAR_POSITION = '8'
    /**
     * 文字色
     */
    export const CHAR_COLOR = '6'
    /**
     * 背景色
     */
    export const BACK_GROUND_COLOR = '7'
  }
}
