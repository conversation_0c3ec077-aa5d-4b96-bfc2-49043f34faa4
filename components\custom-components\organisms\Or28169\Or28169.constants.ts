import { getSequencedCpId } from '#imports'

/**
 * Or28169Const：有機体：履歴選択
 * GUI01080_基本チェックリスト
 *
 * @description
 * 静的データ
 *
 * <AUTHOR> NGUYEN VAN PHONG
 */
export namespace Or28169Const {
  /**
   * コンポーネントID
   *
   * @param seq  - 連番
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or28169', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）項目名表示有無
     */
    export const SHOW_REQUIRED_LABEL = true
    /**
     * （初期値）必須有無
     */
    export const IS_REQUIRED = false
  }

  /**
   * 履歴変更区分: 選択先の計画書ID
   */
  export const UPDATE_CATEGORY_SELECT = '0'
  /**
   * 履歴変更区分: 選択している計画書IDの前
   */
  export const UPDATE_CATEGORY_PREVIOUS = '1'
  /**
   * 履歴変更区分: 選択している計画書IDの次
   */
  export const UPDATE_CATEGORY_NEXT = '2'
  /**
   * 履歴
   */
  export const UPDATE_CATEGORY_ACTION_WHEN_CREATE = '3'
}
