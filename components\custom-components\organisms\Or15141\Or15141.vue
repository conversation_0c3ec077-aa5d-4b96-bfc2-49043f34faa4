<script setup lang="ts">
/**
 * Or15141：有機体：（確定版）今後の生活展望について与カンファレンス等について(ケアマネジャーからの希望)
 *
 * <AUTHOR>
 */
import { computed, reactive, ref, watch, type Ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import type { Or55476OneWayType, RelatedPersonSelectResInfo } from '../Or55476/Or55476.type'
import { Or55476Const } from '../Or55476/Or55476.constants'
import { Or55476Logic } from '../Or55476/Or55476.logic'
import { TeX0012Logic } from '../../template/TeX0012/TeX0012.logic'
import { Or15141Const } from './Or15141.constants'
import type { CodeType, Or15141OneWayType, Or15141ValuesType } from './Or15141.Type'
import {
  useScreenOneWayBind,
  useScreenTwoWayBind,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo00038OnewayType } from '~/types/business/components/Mo00038Type'
import type { Mo00040OnewayType } from '~/types/business/components/Mo00040Type'
import type { Mo00046OnewayType } from '~/types/business/components/Mo00046Type'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'
import type { Or51775OnewayType, Or51775Type } from '~/types/cmn/business/components/Or51775Type'
import type { KinshipInfo } from '~/repositories/cmn/entities/HospitalizationTimeInfoOfferPeriodSelectServiceEntity'
import type { TeX0012Type } from '~/types/cmn/business/components/TeX0012Type'
/**************************************************
 * Props
 **************************************************/
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
interface Props {
  /** uniqueCpId */
  uniqueCpId: string
  parentUniqueCpId: string
}
const props = defineProps<Props>()
/**************************************************
 * Pinia
 **************************************************/
const { t } = useI18n()
const or51775 = ref({ uniqueCpId: '' }) // Or51775：有機体：入力支援［ケアマネ］モーダル
const or55476 = ref({ uniqueCpId: '' })
const { refValue } = useScreenTwoWayBind<Or15141ValuesType>({
  cpId: Or15141Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
}) as { refValue: Ref<Or15141ValuesType> }
useScreenOneWayBind<Or15141OneWayType>({
  cpId: Or15141Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    codeList: (value) => {
      localOneway.codeListOneway = value!
    },
  },
})
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or51775Const.CP_ID(1)]: or51775.value,
  [Or55476Const.CP_ID(1)]: or55476.value,
})
/**************************************************
 * 変数定義
 **************************************************/

const localOneway = reactive({
  codeListOneway: {} as Record<string, CodeType[]>,
  // GUI00937 共通入力支援画面
  or51775Oneway: {
    screenId: Or15141Const.DEFAULT.GUI,
    bunruiId: '-', // 分類ID TBD
    t2Cd: '',
    t3Cd: '',
    tableName: 'cpn_tuc_hosp_info_teikyou_data',
    assessmentMethod: Or15141Const.DEFAULT.ASSESS_MENT_METHOD, // アセスメント方式 TBD
    userId: systemCommonsStore.getUserId ?? '',
  } as Or51775OnewayType,
  or55476Oneway: {
    userId: Or15141Const.DEFAULT.VALUE_1,
    telCellFlg: Or15141Const.DEFAULT.VALUE_1,
    createYmd: systemCommonsStore.getSystemDate ?? '',
  } as Or55476OneWayType,
  mo00046Oneway: {
    showItemLabel: false,
    autoGrow: false,
    noResize: true,
    maxlength: '4000',
    rows: '2',
    maxRows: '2',
    width:'1000px'
  } as Mo00046OnewayType,
  mo00045Oneway1: {
    maxlength: '41',
    width: '585px',
    isVerticalLabel: false,
    showItemLabel: false,
  } as Mo00045OnewayType,
  mo00045Oneway2: {
    maxlength: '70',
    width: '560px',
    isVerticalLabel: false,
    showItemLabel: false,
  } as Mo00045OnewayType,
  mo00045Oneway3: {
    maxlength: '41',
    width: '338px',
    isVerticalLabel: false,
    showItemLabel: false,
  } as Mo00045OnewayType,
  mo00045Oneway4: {
    maxlength: '80',
    width: '648px',
    isVerticalLabel: false,
    showItemLabel: false,
  } as Mo00045OnewayType,
  mo00009Oneway: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  mo01338Oneway: {
    valueFontWeight: 'blod',
    value: t('label.future_life_prospects_label'),
    customClass: { itemStyle: 'font-size:18px; !import' } as CustomClass,
  } as Mo01338OnewayType,
  mo01338Oneway1: {
    valueFontWeight: 'blod',
    value: t('label.conference_label'),
    customClass: { itemStyle: 'font-size:18px; !import' } as CustomClass,
  } as Mo01338OnewayType,
  mo00039Oneway: {
    showItemLabel: false,
    inline: true,
    customClass: { outerClass: 'd-flex align-center' } as CustomClass,
  } as Mo00039OnewayType,

  // 文字数入力
  mo00038Oneway1: {
    mo00045Oneway: {
      appendLabel: t('label.primary_caregiver_years_label'),
      showItemLabel: false,
      maxLength: '3',
      width: '100px',
      customClass: new CustomClass({
        outerClass: 'ml-2',
      }),
    } as Mo00045OnewayType,
    min: 0,
    max: 999,
    disalbeSpinBtnDefaultProcessing: false,
  } as Mo00038OnewayType,
  mo00040Oneway1: {
    itemTitle: 'zcode',
    itemValue: 'zokugaraKnj',
    showItemLabel: false,
    itemLabelFontWeight: 'bold',
    width: '150px',
    hideDetails: true,
    items: [],
  } as Mo00040OnewayType,
  mo00018Oneway: {
    name: t('label.specific_requests'),
    hideDetails: true,
    showItemLabel: false,
    itemLabel: '',
    checkboxLabel: t('label.specific_requests'),
  } as Mo00018OnewayType,
  mo00030Oneway: {
    mo00045Oneway: {
      itemLabel: t('label.household_members_count_left_bracket_label'),
      appendLabel: t('label.household_members_count_right_bracket_label'),
      showItemLabel: true,
      isVerticalLabel: false,
      maxLength: '20',
      width: '305px',
      customClass: new CustomClass({
        labelClass: 'd-flex align-center mr-2',
        labelStyle: 'padding:0 !important',
      }),
    },
    mode: Or15141Const.DEFAULT.VALUE_1,
  },
  careCapabilityDataList: [
    {
      label: t('label.care_capability_solo'),
      key: 'kazokuKaigo1Umu',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.care_capability_elderly_household'),
      key: 'kazokuKaigo3Umu',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.care_capability_with_children'),
      key: 'kazokuKaigo7Umu',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.care_capability_daytime_solo'),
      key: 'kazokuKaigo2Umu',
      showItemLabel: true,
      itemLabel: '*',
    },
    {
      label: t('label.care_capability_other'),
      key: 'kazokuKaigo6Umu',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
  careDataList: [
    {
      label: t('label.care_capability_available'),
      key: 'kazokuKaigo8Umu',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.care_capability_unavailable'),
      key: 'kazokuKaigo9Umu',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.care_capability_no_support'),
      key: 'kazokuKaigo4Umu',
      showItemLabel: false,
      itemLabel: '',
    },
  ],
})

const local = reactive({
  commonInfo: {} as TeX0012Type,
  or51775: { modelValue: '' } as Or51775Type,
  or51775Value: '',
})

/**
 * 入力支援アイコンボタンクリック
 *
 * @param or51775Value -or51775Value
 *
 * @param title -title
 *
 * @param t2Cd -t2Cd
 *
 * @param t3Cd -t3Cd
 *
 * @param columnName -columnName
 *
 * @param inputContents -inputContents
 */
const handleOr51775 = (
  or51775Value: string,
  title: string,
  t2Cd: string,
  t3Cd: string,
  columnName: string,
  inputContents: string
) => {
  // GUI00937 共通入力支援画面をポップアップで起動する
  local.or51775Value = or51775Value
  localOneway.or51775Oneway.title = title
  localOneway.or51775Oneway.t2Cd = t2Cd
  localOneway.or51775Oneway.t3Cd = t3Cd
  localOneway.or51775Oneway.columnName = columnName
  localOneway.or51775Oneway.inputContents = inputContents
  local.or51775.modelValue =
    (refValue.value.or15141Values[local.or51775Value] as unknown as Mo00045Type).value ?? ''
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}
/**
 * 入力支援ポップアップの表示状態を返すComputed
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr55476 = computed(() => {
  // Or55476のダイアログ開閉状態
  return Or55476Logic.state.get(or55476.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理
 *
 */
function onClickOr55476() {
  Or55476Logic.state.set({
    uniqueCpId: or55476.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const handleOr55476 = (info: RelatedPersonSelectResInfo) => {
  const data = (localOneway.codeListOneway.kinshipInfoList as KinshipInfo[]).find(
    (item) => item.id === info.id
  )
  refValue.value.or15141Values.taiingoKaigoMainName.value = info.nameKnj
  refValue.value.or15141Values.taiingoKaigoMainZcode.modelValue = info.zcode
  handleInit()
  refValue.value.or15141Values.taiingoKaigoMainAge.mo00045.value =
    data && !info.age ? (data.age ?? '') : (info.age ?? '')
}

/**
 * 共通入力支援画面の確定ボタン押す後の処理
 *
 * @param data - 入力支援情報
 */
const handleOr51775Confirm = (data: Or51775ConfirmType) => {
  const setOrAppendValue = (str: string, data: Or51775ConfirmType) => {
    if (data.type === Or15141Const.DEFAULT.ADD_END_TEXT_IMPORT_TYPE) {
      // 本文末に追加の場合
      return `${str}${data.value}`
    } else if (data.type === Or15141Const.DEFAULT.OVERWRITE_TEXT_IMPORT_TYPE) {
      // 本文上書の場合
      return data.value
    } else {
      return ''
    }
  }
  ;(refValue.value.or15141Values[local.or51775Value] as unknown as Mo00045Type).value =
    setOrAppendValue(
      (refValue.value.or15141Values[local.or51775Value] as unknown as Mo00045Type).value ?? '',
      data
    )
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: false },
  })
}

const handleInit = () => {
  if (
    !localOneway.codeListOneway.relationshipMasterInfo.find(
      (item) => item.zcode === refValue.value.or15141Values.taiingoKaigoMainZcode?.modelValue
    )
  ) {
    refValue.value.or15141Values.taiingoKaigoMainZcode.modelValue = undefined
  }
}

/**
 * mo00039の値変更を監視
 *
 */
const handleMo00039 = () => {
  refValue.value.or15141Values.kazokuKaigo8Umu.modelValue = true
}

/**
 * mo00018の値変更を監視
 *
 * @param mo00018 - mo00018
 *
 * @param key - key
 */
const handleMo00018 = (mo00018: Mo00018Type, key: string) => {
  if (key === 'kazokuKaigo8Umu' && !mo00018.modelValue) {
    refValue.value.or15141Values.kazokuKaigo8Kbn = '-1'
  }
}

watch(
  () => localOneway.codeListOneway,
  (newVal) => {
    localOneway.mo00040Oneway1.items = newVal.relationshipMasterInfo
  }
)
/**
 * 画面メニューのイベントを監視
 */
watch(
  () => TeX0012Logic.data.get(props.parentUniqueCpId),
  (newValue) => {
    if (newValue?.teikyouId) {
      local.commonInfo = newValue
      localOneway.or55476Oneway.createYmd = newValue.createYmd
      localOneway.or51775Oneway.userId = newValue.userId ?? ''
    }
  },
  { deep: true }
)
</script>

<template>
  <div v-if="refValue.or15141Values">
    <c-v-row class="title">
      <c-v-col>
        <base-mo01338 :oneway-model-value="localOneway.mo01338Oneway"></base-mo01338>
      </c-v-col>
    </c-v-row>
    <!-- 在宅生活に必要な要件 -->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.requirements_for_home_life_label') }}
        <div class="d-flex">
          <c-v-divider
            vertical
            inset
          />
          <base-mo00009
            :oneway-model-value="localOneway.mo00009Oneway"
            @click="
              handleOr51775(
                'zaitakuYokenKnj',
                t('label.requirements_for_home_life_label'),
                Or15141Const.DEFAULT.VALUE_1,
                Or15141Const.DEFAULT.VALUE_11,
                'zaitaku_yoken_knj',
                t('label.requirements_for_home_life_label')
              )
            "
          />
        </div>
      </c-v-col>
      <c-v-col
        cols="10"
        class="data-cell"
      >
        <base-mo00046
          v-model="refValue.or15141Values.zaitakuYokenKnj"
          class="w-60"
          :oneway-model-value="localOneway.mo00046Oneway"
        />
      </c-v-col>
    </c-v-row>

    <!--退院後の世帯状況-->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.household_status_after_discharge_label') }}
      </c-v-col>
      <c-v-col
        cols="10"
        class="data-cell d-flex"
      >
        <div
          v-for="(item, index) in localOneway.careCapabilityDataList"
          :key="index"
          class="d-flex align-center my-1 mr-2"
        >
          <base-mo00018
            v-model="refValue.or15141Values[item.key]"
            :oneway-model-value="{
              name: item.label,
              hideDetails: true,
              showItemLabel: item.showItemLabel,
              itemLabel: item.itemLabel,
              checkboxLabel: item.label,
              isVerticalLabel: false,
              customClass: { outerClass: 'requiredText' } as CustomClass,
            }"
          />
          <base-mo00030
            v-if="item.key === 'kazokuKaigo7Umu'"
            v-model="refValue.or15141Values.kazokuKaigo7Ninzu"
            class="ml-2"
            :oneway-model-value="localOneway.mo00030Oneway"
          />
          <div
            v-if="item.key === 'kazokuKaigo6Umu'"
            class="d-flex align-center ml-2"
          >
            <c-v-divider
              class="ml-2"
              vertical
              inset
            />
            <base-mo00009
              :oneway-model-value="localOneway.mo00009Oneway"
              @click="
                handleOr51775(
                  'kazokuKaigoMemoKnj',
                  t('label.household_status_after_discharge_label'),
                  Or15141Const.DEFAULT.VALUE_1,
                  Or15141Const.DEFAULT.VALUE_17,
                  'kazoku_kaigo_memo_knj',
                  t('label.household_status_after_discharge_label')
                )
              "
            />
          </div>
          <base-mo00045
            v-if="item.key === 'kazokuKaigo6Umu'"
            v-model="refValue.or15141Values.kazokuKaigoMemoKnj"
            :oneway-model-value="localOneway.mo00045Oneway1"
          />
        </div>
      </c-v-col>
    </c-v-row>
    <!--世帯に対する配慮-->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.considerations_for_household_label') }}
      </c-v-col>
      <c-v-col
        cols="10"
        class="data-cell d-flex align-center"
      >
        <base-mo00039
          v-model="refValue.or15141Values.setaiHairyo"
          :oneway-model-value="localOneway.mo00039Oneway"
        >
          <div
            v-for="(item, index) in localOneway.codeListOneway.CONFIRMATION_INFO_HOUSEHOLD"
            :key="'radio' + '_' + index"
            class="d-flex w-60"
          >
            <base-at-radio
              :name="item.label"
              :radio-label="item.label"
              :value="item.value"
            />
            <div
              v-if="index === localOneway.codeListOneway.CONFIRMATION_INFO_HOUSEHOLD.length - 1"
              class="d-flex align-center"
            >
              <div class="d-flex align-center">
                <c-v-divider
                  class="ml-2"
                  vertical
                  inset
                />
                <base-mo00009
                  :oneway-model-value="localOneway.mo00009Oneway"
                  @click="
                    handleOr51775(
                      'setaiHairyoMemoKnj',
                      t('label.considerations_for_household_label'),
                      Or15141Const.DEFAULT.VALUE_1,
                      Or15141Const.DEFAULT.VALUE_18,
                      'setai_hairyo_memo_knj',
                      t('label.considerations_for_household_label')
                    )
                  "
                />
              </div>
              <base-mo00045
                v-model="refValue.or15141Values.setaiHairyoMemoKnj"
                :oneway-model-value="localOneway.mo00045Oneway2"
              />
            </div>
          </div>
        </base-mo00039>
      </c-v-col>
    </c-v-row>
    <!--退院後の主介護者-->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.primary_caregiver_after_discharge_label') }}
        <div class="d-flex">
          <c-v-divider
            vertical
            inset
          />
          <base-mo00009
            v-if="local.commonInfo.kinshipAuthorityFlag === Or15141Const.DEFAULT.VALUE_0"
            :oneway-model-value="localOneway.mo00009Oneway"
            @click="onClickOr55476"
          />
        </div>
      </c-v-col>
      <c-v-col
        cols="4"
        class="data-cell"
      >
        <base-mo00039
          v-model="refValue.or15141Values.taiingoKaigoMain"
          :oneway-model-value="localOneway.mo00039Oneway"
        >
          <base-at-radio
            v-for="(item, index) in localOneway.codeListOneway
              .CONFIRMATION_INFO_PRIMARY_CAREGIVER_AFTER_DISCHARGE"
            :key="index"
            :name="'radio' + '-' + index"
            :radio-label="item.label"
            :value="item.value"
          />
        </base-mo00039>
      </c-v-col>
      <c-v-col cols="6">
        <c-v-row>
          <c-v-col
            cols="2"
            class="header-title-cell"
            style="border-bottom: 1px gainsboro solid; border-right: 0"
          >
            {{ t('label.name') }}
          </c-v-col>
          <c-v-col
            cols="10"
            class="data-cell"
            style="border-bottom: 1px gainsboro solid"
          >
            <base-mo00045
              v-model="refValue.or15141Values.taiingoKaigoMainName"
              :oneway-model-value="localOneway.mo00045Oneway3"
            />
          </c-v-col>
        </c-v-row>
        <c-v-row>
          <c-v-col
            cols="2"
            class="header-title-cell"
            style="border-right: 0"
          >
            {{ t('label.primary_caregiver_name_relationship_age_label') }}
          </c-v-col>
          <c-v-col
            cols="10"
            class="data-cell d-flex"
          >
            <base-mo00040
              v-model="refValue.or15141Values.taiingoKaigoMainZcode"
              :oneway-model-value="localOneway.mo00040Oneway1"
            />
            <base-mo00038
              v-model="refValue.or15141Values.taiingoKaigoMainAge"
              class="mlr-10"
              :oneway-model-value="localOneway.mo00038Oneway1"
            ></base-mo00038>
          </c-v-col>
        </c-v-row>
      </c-v-col>
    </c-v-row>
    <!--介護力*-->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.care_capability_label') }}
      </c-v-col>
      <c-v-col
        cols="10"
        class="data-cell d-flex"
      >
        <div
          v-for="(item, index) in localOneway.careDataList"
          :key="index"
          class="d-flex mr-6"
        >
          <base-mo00018
            v-model="refValue.or15141Values[item.key]"
            :oneway-model-value="{
              name: item.label,
              hideDetails: true,
              showItemLabel: item.showItemLabel,
              itemLabel: item.itemLabel,
              checkboxLabel: item.label,
              isVerticalLabel: false,
              customClass: { outerClass: 'requiredText' } as CustomClass,
            }"
            @update:model-value="
              (mo00018: Mo00018Type) => {
                handleMo00018(mo00018, item.key)
              }
            "
          />
          <div
            v-if="item.key === 'kazokuKaigo8Umu'"
            class="d-flex align-center ml-2"
          >
            (
            <base-mo00039
              v-model="refValue.or15141Values.kazokuKaigo8Kbn"
              :oneway-model-value="localOneway.mo00039Oneway"
              @click="handleMo00039"
            >
              <base-at-radio
                v-for="(itm, idx) in localOneway.codeListOneway
                  .CONFIRMATION_INFO_NURSING_CARE_PROSPECT"
                :key="idx"
                :name="'radio' + '-' + idx"
                :radio-label="itm.label"
                :value="itm.value"
              /> </base-mo00039
            >)
          </div>
        </div>
      </c-v-col>
    </c-v-row>
    <!--家族や同居者等による虐待の疑い-->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.abuse_suspicion_label') }}<br />
        {{ t('label.abuse_suspicion') }}
      </c-v-col>
      <c-v-col
        cols="10"
        class="data-cell d-flex align-center"
      >
        <base-mo00039
          v-model="refValue.or15141Values.gyakutaiUmu"
          :oneway-model-value="localOneway.mo00039Oneway"
        >
          <div
            v-for="(item, index) in localOneway.codeListOneway.M_CD_KBN_ID_OMITTED"
            :key="'radio' + '_' + index"
            class="d-flex align-center w-60"
          >
            <base-at-radio
              :name="item.label"
              :radio-label="item.label"
              :value="item.value"
            />
            <div
              v-if="item.value === '1'"
              class="d-flex align-center"
            >
              <div class="d-flex align-center">
                <c-v-divider
                  class="ml-2"
                  vertical
                  inset
                />
                <base-mo00009
                  :oneway-model-value="localOneway.mo00009Oneway"
                  @click="
                    handleOr51775(
                      'gyakutaiUmuMemoKnj',
                      t('label.abuse_suspicion_label'),
                      Or15141Const.DEFAULT.VALUE_1,
                      Or15141Const.DEFAULT.VALUE_19,
                      'gyakutai_umu_memo_knj',
                      t('label.abuse_suspicion_label')
                    )
                  "
                />
              </div>
              <base-mo00045
                v-model="refValue.or15141Values.gyakutaiUmuMemoKnj"
                :oneway-model-value="localOneway.mo00045Oneway4"
              />
            </div>
          </div>
        </base-mo00039>
      </c-v-col>
    </c-v-row>
    <!-- 特記事項 -->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.special_notes') }}
        <div class="d-flex">
          <c-v-divider
            vertical
            inset
          />
          <base-mo00009
            :oneway-model-value="localOneway.mo00009Oneway"
            @click="
              handleOr51775(
                'tokkiKnj',
                t('label.special_notes'),
                Or15141Const.DEFAULT.VALUE_1,
                Or15141Const.DEFAULT.VALUE_13,
                'tokki_knj',
                t('label.special_notes')
              )
            "
          />
        </div>
      </c-v-col>
      <c-v-col
        cols="10"
        class="data-cell"
      >
        <base-mo00046
          v-model="refValue.or15141Values.tokkiKnj"
          style="width: 80%"
          :oneway-model-value="localOneway.mo00046Oneway"
        />
      </c-v-col>
    </c-v-row>
    <!-- ケアマネジャーからの希望 -->
    <c-v-row class="title">
      <c-v-col>
        <base-mo01338 :oneway-model-value="localOneway.mo01338Oneway1"></base-mo01338>
      </c-v-col>
    </c-v-row>
    <!-- 「院内の多職種カンファレンス」への参加 -->
    <c-v-row class="row">
      <c-v-col
        cols="3"
        class="header-cell"
      >
        {{ t('label.participation_in_multidisciplinary_conference_label') }}
      </c-v-col>
      <c-v-col
        cols="9"
        class="data-cell"
      >
        <base-mo00018
          v-model="refValue.or15141Values.hospConfSanka"
          :oneway-model-value="localOneway.mo00018Oneway"
        />
      </c-v-col>
    </c-v-row>
    <!-- 「退院前カンファレンス」への参加 -->
    <c-v-row class="row">
      <c-v-col
        cols="3"
        class="header-cell"
      >
        {{ t('label.participation_in_pre_discharge_conference_label') }}
      </c-v-col>
      <c-v-col
        cols="9"
        class="data-cell d-flex"
      >
        <base-mo00018
          v-model="refValue.or15141Values.leavConfSanka"
          :oneway-model-value="localOneway.mo00018Oneway"
        />
        <div class="d-flex align-center ml-2">
          {{ t('label.specific_requests_label') }}
          <c-v-divider
            class="ml-2"
            vertical
            inset
          />
          <base-mo00009
            :oneway-model-value="localOneway.mo00009Oneway"
            @click="
              handleOr51775(
                'leavConfMemoKnj',
                t('label.specific_requests_label'),
                Or15141Const.DEFAULT.VALUE_1,
                Or15141Const.DEFAULT.VALUE_14,
                'leav_conf_memo_knj',
                t('label.pre_discharge_conference_memo')
              )
            "
          />
        </div>
        <base-mo00045
          v-model="refValue.or15141Values.leavConfMemoKnj"
          :oneway-model-value="localOneway.mo00045Oneway1"
        />
      </c-v-col>
    </c-v-row>
    <!-- 「退院前訪問指導」を実施する場合の同行 -->
    <c-v-row class="row">
      <c-v-col
        cols="3"
        class="header-cell"
      >
        {{ t('label.accompanying_pre_discharge_visit_guidance_label') }}
      </c-v-col>
      <c-v-col
        cols="9"
        class="data-cell"
      >
        <base-mo00018
          v-model="refValue.or15141Values.leavHoumonDoukou"
          :oneway-model-value="localOneway.mo00018Oneway"
        />
      </c-v-col>
    </c-v-row>
    <!-- Or51775: 有機体: GUI00937 入力支援［ケアマネ］をポップアップで起動する。 -->
    <g-custom-or51775
      v-if="showDialogOr51775"
      v-bind="or51775"
      v-model="local.or51775"
      :oneway-model-value="localOneway.or51775Oneway"
      @confirm="handleOr51775Confirm"
    />
    <g-custom-or-55476
      v-if="showDialogOr55476"
      v-bind="or55476"
      :oneway-model-value="localOneway.or55476Oneway"
      @update:model-value="handleOr55476"
    />
  </div>
</template>

<style scoped lang="scss">
.row {
  display: flex;
  border-bottom: 1px gainsboro solid;
  border-left: 1px gainsboro solid;
  border-right: 0;
  min-height: 62px;
}

.header-cell {
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 12px !important;
}

.header-title-cell {
  background-color: transparent;
  border-right: 1px gainsboro solid;
  display: grid;
  align-items: center;
}

.data-cell {
  border-left: 1px gainsboro solid;
  border-right: 1px gainsboro solid;
  background: #fff;
  padding: 10px 12px;
  width: 100%;
  min-height: 62px;
  display: grid;
  align-items: center;
  flex-wrap: wrap;
}

:deep(.v-input__control) {
  background-color: rgb(var(--v-theme-surface));
}
:deep(.v-selection-control-group--inline) {
  align-items: center;
}
.w-60 {
  width: 60%;
}

.mlr-10 {
  margin: 0 10%;
}
.title {
  margin-top: 12px;
  background-color: #fff;
  border-left: 1px gainsboro solid;
  border-right: 1px gainsboro solid;
  border-bottom: 1px gainsboro solid;
}
.requiredText {
  :deep(.item-label) {
    margin-left: 10px;
    font-size: 18px !important;
  }
}
</style>
