<script setup lang="ts">
/**
 * Or06680:有機体:入退所利用情報
 * GUI01308_ 入退所利用情報画面
 *
 * @description
 * 入退所利用情報画面
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { reactive, ref, onMounted, watch } from 'vue'
import type { Or06680StateType } from './Or06680.type'
import { Or06680Const } from './Or06680.constants'
import { useScreenOneWayBind } from '#imports'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Or06680OnewayType, Or06680Type } from '~/types/cmn/business/components/Or06680Type'
import type {
  AdmissionLeavingUseInfoInEntity,
  AdmissionLeavingUseInfoOutEntity,
} from '~/repositories/cmn/entities/AdmissionLeavingUseInfoSelectEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  Mo01334Headers,
  Mo01334Items,
  Mo01334OnewayType,
} from '~/types/business/components/Mo01334Type'

const { t } = useI18n()
/************************************************
 * Props
 ************************************************/
interface Props {
  modelValue: Or06680Type
  onewayModelValue: Or06680OnewayType
  uniqueCpId: string
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/

const localOneway = reactive({
  or06680Oneway: {
    ...props.onewayModelValue,
  },
  // 「入退所利用情報」ダイアログ
  mo00024Oneway: {
    width: '550px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      toolbarTitle: t('label.admission-leaving-usageInfo'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
    },
  },
  // 入退所利用情報情報データテーブルのヘッダー
  mo01334Oneway: {
    height: '294px',
    headers: [
      // 利用開始日
      {
        title: t('label.utilization-start-date'), // ヘッダーに表示される名称
        key: 'enterYmd',
        sortable: false,
      },
      // 利用終了日
      {
        title: t('label.utilization-end-date'), // ヘッダーに表示される名称
        key: 'leaveYmd',
        sortable: false,
      },
      // 終了予定日
      {
        title: t('label.planned-end-date'), // ヘッダーに表示される名称
        key: 'leaveexYmd',
        sortable: false,
      },
      // 提供事業所
      {
        title: t('label.providing-facility'), // ヘッダーに表示される名称
        key: 'jigyoRyakuKnj',
        width:'180px',
        sortable: false,
      },
    ] as Mo01334Headers[],
    showPaginationBottomFlg: false,
    showPaginationTopFlg: false,
    density: 'default',
    items: [],
  } as Mo01334OnewayType,
})

const local = reactive({
  // 入退所利用情報情報
  mo01334: {
    value: '0',
    values: [],
  },
  or06680: {
    ...props.modelValue,
  },
})

// 閉じるボタン設置
const mo00611Oneway: Mo00611OnewayType = {
  btnLabel: t('btn.close'),
  width: '90px',
  tooltipText: t('tooltip.screen-close'),
}

// 確定ボタン設置
const mo00609Oneway: Mo00609OnewayType = {
  btnLabel: t('btn.confirm'),
  width: '90px',
  tooltipText: t('tooltip.confirm-btn'),
}

// ダイアログ設置
const mo00024 = ref<Mo00024Type>({
  isOpen: Or06680Const.DEFAULT.IS_OPEN,
})

// 入退所利用情報情報選択行データ設定
const historySelectedItem = ref<Mo01334Items | undefined>()

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or06680StateType>({
  cpId: Or06680Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or06680Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  // 初期情報取得
  await getInitDataInfo()
})

/** 初期情報取得 */
const getInitDataInfo = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: AdmissionLeavingUseInfoInEntity = {
    ...local.or06680,
  }
  const resData: AdmissionLeavingUseInfoOutEntity = await ScreenRepository.select(
    'admissionLeavingUseInfoSelect',
    inputData
  )
  // データ情報設定
  localOneway.mo01334Oneway.items = resData.data.admissionLeavingUseInfoList
    .map((item, index) => {
      return {
        id: index.toString(),
        ...item,
      }
    })
    .sort((a, b) => {
      const dateA = new Date(a.enterYmd).getTime()
      const dateB = new Date(b.enterYmd).getTime()
      if (dateA > dateB) return -1
      if (dateA < dateB) return 1
      return parseInt(b.id, 10) - parseInt(a.id, 10)
    })
  const arrTime: Mo01334Items[] = []
  localOneway.mo01334Oneway.items.forEach((item) => {
    if (
      new Date(localOneway.or06680Oneway.recordYmd).getTime() >=
      new Date(item.enterYmd as string).getTime()
    ) {
      arrTime.push(item)
    }
  })
  local.mo01334.value = arrTime[0]?.id || localOneway.mo01334Oneway.items[0].id
}

/**
 * 入退所利用情報選択行
 *
 * @param item - 入退所利用情報選択行
 */
const clickHistorySelectRow = (item: Mo01334Items) => {
  historySelectedItem.value = localOneway.mo01334Oneway.items.find((itm) => itm.id === item.value)
}

/**
 * 「確定」ボタン押下
 */
const onConfirmBtn = () => {
  // 入退所利用データがない場合、操作なし
  if (!historySelectedItem.value) return
  // 選択情報値戻り
  emit('update:modelValue', historySelectedItem.value)
  onClickCloseBtn()
}

/**
 * 閉じるボタン押下時
 */
const onClickCloseBtn = (): void => {
  setState({ isOpen: false })
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      onClickCloseBtn()
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet>
        <c-v-row no-gutters>
          <c-v-col
            cols="12"
            class="table-header"
          >
            <!-- 入退所利用情報一覧 -->
            <base-mo01334
              v-model="local.mo01334"
              :oneway-model-value="localOneway.mo01334Oneway"
              hide-default-footer
              class="list-wrapper "
              @update:model-value="clickHistorySelectRow"
            >
            </base-mo01334>
          </c-v-col>
        </c-v-row>
      </c-v-sheet>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="mo00611Oneway"
          class="mr-2"
          @click="onClickCloseBtn"
        >
        </base-mo00611>
        <!-- 確定ボタン-->
        <base-mo00609
          class="mr-2"
          :oneway-model-value="mo00609Oneway"
          @click="onConfirmBtn"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';
:deep(td) {
  height: 32px !important;
}
</style>
