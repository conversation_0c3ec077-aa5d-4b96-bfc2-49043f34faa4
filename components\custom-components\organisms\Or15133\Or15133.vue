<script setup lang="ts">
/**
 * Or15133：有機体：（確定版）家族構成／連絡先について
 *
 * <AUTHOR>
 */
import { computed, reactive, ref, watch, type Ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import { Or51775Const } from '../Or51775/Or51775.constants'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { Or55476Logic } from '../Or55476/Or55476.logic'
import type { Or55476OneWayType, RelatedPersonSelectResInfo } from '../Or55476/Or55476.type'
import { Or55476Const } from '../Or55476/Or55476.constants'
import { TeX0012Logic } from '../../template/TeX0012/TeX0012.logic'
import type { CodeType, Or15133OneWayType, Or15133ValuesType } from './Or15133.Type'
import { Or15133Const } from './Or15133.constants'
import {
  useScreenOneWayBind,
  useScreenTwoWayBind,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo00038OnewayType } from '~/types/business/components/Mo00038Type'
import type { Mo00040OnewayType } from '~/types/business/components/Mo00040Type'
import type { Or51775OnewayType, Or51775Type } from '~/types/cmn/business/components/Or51775Type'
import type { KinshipInfo } from '~/repositories/cmn/entities/HospitalizationTimeInfoOfferPeriodSelectServiceEntity'
import type { TeX0012Type } from '~/types/cmn/business/components/TeX0012Type'

/**************************************************
 * Props
 **************************************************/
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
interface Props {
  /** uniqueCpId */
  uniqueCpId: string
  parentUniqueCpId: string
}

const props = defineProps<Props>()
/**************************************************
 * Pinia
 **************************************************/
const { t } = useI18n()
const or51775 = ref({ uniqueCpId: '' }) // Or51775：有機体：入力支援［ケアマネ］モーダル
const or55476 = ref({ uniqueCpId: '' })
const { refValue } = useScreenTwoWayBind<Or15133ValuesType>({
  cpId: Or15133Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
}) as { refValue: Ref<Or15133ValuesType> }
useScreenOneWayBind<Or15133OneWayType>({
  cpId: Or15133Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    codeList: (value) => {
      localOneway.codeListOneway = value as Record<string, CodeType[]>
    },
  },
})
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or51775Const.CP_ID(1)]: or51775.value,
  [Or55476Const.CP_ID(1)]: or55476.value,
})
/**************************************************
 * 変数定義
 **************************************************/
const local = reactive({
  commonInfo: {} as TeX0012Type,
  or51775: { modelValue: '' } as Or51775Type,
  or51775Value: '',
  or55476Value: '',
})

const localOneway = reactive({
  codeListOneway: {} as Record<string, CodeType[]>,
  // GUI00937 共通入力支援画面
  or51775Oneway: {
    screenId: Or15133Const.DEFAULT.GUI,
    bunruiId: '-', // 分類ID TBD
    t2Cd: '',
    t3Cd: '',
    tableName: 'cpn_tuc_hosp_info_teikyou_data',
    assessmentMethod: Or15133Const.DEFAULT.ASSESS_MENT_METHOD, // 共通情報.アセスメント方式
    userId: systemCommonsStore.getUserId ?? '',
  } as Or51775OnewayType,
  or55476Oneway: {
    userId: Or15133Const.DEFAULT.VALUE_1,
    telCellFlg: Or15133Const.DEFAULT.VALUE_1,
    createYmd: systemCommonsStore.getSystemDate ?? '',
  } as Or55476OneWayType,
  mo00045Oneway: {
    maxlength: '36',
    width: '328px',
    isVerticalLabel: false,
    showItemLabel: false,
  } as Mo00045OnewayType,
  mo00045Oneway1: {
    maxlength: '41',
    width: '585px',
    isVerticalLabel: false,
    showItemLabel: false,
  } as Mo00045OnewayType,
  mo00045Oneway2: {
    maxlength: '41',
    width: '120px',
    isVerticalLabel: false,
    showItemLabel: false,
  } as Mo00045OnewayType,
  mo00009Oneway: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  mo01338Oneway: {
    valueFontWeight: 'blod',
    value: t('label.family_structure_contact_label'),
    customClass: { itemStyle: 'font-size:18px; !import' } as CustomClass,
  } as Mo01338OnewayType,
  mo00039Oneway: {
    itemLabel: '',
    showItemLabel: false,
    inline: true,
    customClass: { outerClass: 'd-flex align-center' } as CustomClass,
  } as Mo00039OnewayType,

  // 文字数入力
  mo00038Oneway1: {
    mo00045Oneway: {
      appendLabel: t('label.primary_caregiver_years_label'),
      showItemLabel: false,
      maxLength: '3',
      width: '100px',
      customClass: new CustomClass({
        outerClass: 'ml-2',
      }),
    } as Mo00045OnewayType,
    min: 0,
    max: 999,
    disalbeSpinBtnDefaultProcessing: false,
  } as Mo00038OnewayType,
  mo00040Oneway1: {
    itemTitle: 'zcode',
    itemValue: 'zokugaraKnj',
    showItemLabel: false,
    itemLabelFontWeight: 'bold',
    width: '150px',
    hideDetails: true,
    items: [],
  } as Mo00040OnewayType,

  mo00030Oneway: {
    mo00045Oneway: {
      maxlength: '14',
      width: '120px',
      isVerticalLabel: false,
      showItemLabel: false,
    },
    mode: Or15133Const.DEFAULT.VALUE_1,
  },
  householdStructureDataList: [
    {
      label: t('label.household_structure_single'),
      key: 'setaiKousei1',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.household_structure_elderly'),
      key: 'setaiKousei2',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.household_structure_with_children'),
      key: 'setaiKousei3',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.household_structure_other'),
      key: 'setaiKousei4',
      showItemLabel: false,
      itemLabel: '',
    },
    {
      label: t('label.household_structure_daytime_single'),
      key: 'setaiKousei5',
      showItemLabel: true,
      itemLabel: '*',
    },
  ],
})

/**
 * 入力支援アイコンボタンクリック
 *
 */
const handleSetaiKouseiMemoKnj = () => {
  // GUI00937 共通入力支援画面をポップアップで起動する
  localOneway.or51775Oneway.title = t('label.kazoku_memo_knj')
  localOneway.or51775Oneway.t2Cd = Or15133Const.DEFAULT.VALUE_1
  localOneway.or51775Oneway.t3Cd = Or15133Const.DEFAULT.VALUE_6
  localOneway.or51775Oneway.columnName = 'house_tokki_knj'
  localOneway.or51775Oneway.inputContents = t('label.household_structure_other_memo')
  local.or51775Value = 'setaiKouseiMemoKnj'
  local.or51775.modelValue = refValue.value.or15133Values.setaiKouseiMemoKnj.value ?? ''
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  ボタン押下時の処理
 *
 * @param key -key
 */
function onClickOr55476(key: string) {
  local.or55476Value = key
  Or55476Logic.state.set({
    uniqueCpId: or55476.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const handleOr55476 = (info: RelatedPersonSelectResInfo) => {
  const data = (localOneway.codeListOneway.kinshipInfoList as KinshipInfo[]).find(
    (item) => item.id === info.id
  )
  switch (local.or55476Value) {
    case 'kaigoMainKakutei':
      refValue.value.or15133Values.kaigoMainKakutei.value = info.nameKnj
      refValue.value.or15133Values.kaigoMainZcode.modelValue = info.zcode
      handleInit()
      refValue.value.or15133Values.kaigoMainAge.mo00045.value = info.age ?? ''
      refValue.value.or15133Values.kaigoMainTel.value = info.tel ?? ''
      if (data) {
        refValue.value.or15133Values.kaigoMainKousei =
          data.kankei2Kbn === Or15133Const.DEFAULT.VALUE_1
            ? Or15133Const.DEFAULT.VALUE_1
            : Or15133Const.DEFAULT.VALUE_0
        if (!info.age) {
          refValue.value.or15133Values.kaigoMainAge.mo00045.value = data.age ?? ''
        }
      } else {
        refValue.value.or15133Values.kaigoMainKousei = Or15133Const.DEFAULT.VALUE_1
      }
      break
    case 'keyPerson':
      refValue.value.or15133Values.keyPerson.value = info.nameKnj
      refValue.value.or15133Values.keyPersonZcode.modelValue = info.zcode
      handleInit()
      refValue.value.or15133Values.keyPersonAge.mo00045.value = info.age ?? ''
      refValue.value.or15133Values.keyPersonTel.value = info.tel ?? ''
      if (data) {
        refValue.value.or15133Values.keyPersonRenrakuTel.mo00045!.value = data.other1Tel ?? ''
        if (!info.age) {
          refValue.value.or15133Values.keyPersonAge.mo00045.value = data.age ?? ''
        }
      }
      break
  }
}

/**
 * 共通入力支援画面の確定ボタン押す後の処理
 *
 * @param data - 入力支援情報
 */
const handleOr51775Confirm = (data: Or51775ConfirmType) => {
  const setOrAppendValue = (str: string, data: Or51775ConfirmType) => {
    if (data.type === Or15133Const.DEFAULT.ADD_END_TEXT_IMPORT_TYPE) {
      // 本文末に追加の場合
      return `${str}${data.value}`
    } else if (data.type === Or15133Const.DEFAULT.OVERWRITE_TEXT_IMPORT_TYPE) {
      // 本文上書の場合
      return data.value
    } else {
      return ''
    }
  }
  ;(refValue.value.or15133Values[local.or51775Value] as unknown as Mo00045Type).value =
    setOrAppendValue(
      (refValue.value.or15133Values[local.or51775Value] as unknown as Mo00045Type).value ?? '',
      data
    )
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: false },
  })
}

const handleInit = () => {
  if (
    !localOneway.codeListOneway.relationshipMasterInfo.find(
      (item) => item.zcode === refValue.value.or15133Values.kaigoMainZcode?.modelValue
    )
  ) {
    refValue.value.or15133Values.kaigoMainZcode.modelValue = undefined
  }
  if (
    !localOneway.codeListOneway.relationshipMasterInfo.find(
      (item) => item.zcode === refValue.value.or15133Values.keyPersonZcode?.modelValue
    )
  ) {
    refValue.value.or15133Values.keyPersonZcode.modelValue = undefined
  }
}

/**
 * 入力支援ポップアップの表示状態を返すComputed
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr55476 = computed(() => {
  // Or55476のダイアログ開閉状態
  return Or55476Logic.state.get(or55476.value.uniqueCpId)?.isOpen ?? false
})
watch(
  () => localOneway.codeListOneway,
  (newVal) => {
    localOneway.mo00040Oneway1.items = newVal.relationshipMasterInfo
  }
)

/**
 * 画面メニューのイベントを監視
 */
watch(
  () => TeX0012Logic.data.get(props.parentUniqueCpId),
  (newValue) => {
    if (newValue?.teikyouId) {
      local.commonInfo = newValue
      localOneway.or55476Oneway.createYmd = newValue.createYmd
      localOneway.or51775Oneway.userId = newValue.userId ?? ''
    }
  },
  { deep: true }
)
</script>

<template>
  <div v-if="refValue.or15133Values">
    <c-v-row class="title">
      <c-v-col>
        <base-mo01338 :oneway-model-value="localOneway.mo01338Oneway"></base-mo01338>
      </c-v-col>
    </c-v-row>
    <!-- 世帯構成 -->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.household_structure_label') }}
      </c-v-col>
      <c-v-col
        cols="10"
        class="data-cell"
        style="border-bottom: 0"
      >
        <div class="d-flex flex-wrap w-86">
          <div
            v-for="(item, index) in localOneway.householdStructureDataList"
            :key="index"
            class="d-flex align-center mr-4"
            :class="{ item: index === localOneway.householdStructureDataList.length - 1 }"
          >
            <base-mo00018
              v-model="refValue.or15133Values[item.key]"
              :class="{ itemWidth: index === localOneway.householdStructureDataList.length - 1 }"
              :oneway-model-value="{
                name: item.label,
                hideDetails: true,
                showItemLabel: item.showItemLabel,
                itemLabel: item.itemLabel,
                checkboxLabel: item.label,
                isVerticalLabel: false,
                customClass: { outerClass: 'requiredText' } as CustomClass,
              }"
            />
            <div
              v-if="item.key === 'setaiKousei4'"
              class="d-flex align-center"
            >
              <div class="d-flex align-center">
                <c-v-divider
                  class="ml-2"
                  vertical
                  inset
                />
                <base-mo00009
                  :oneway-model-value="localOneway.mo00009Oneway"
                  @click="handleSetaiKouseiMemoKnj"
                />
              </div>
              <base-mo00045
                v-model="refValue.or15133Values.setaiKouseiMemoKnj"
                :oneway-model-value="localOneway.mo00045Oneway"
              />
            </div>
          </div>
        </div>
      </c-v-col>
    </c-v-row>

    <!--主介護者氏名-->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.primary_caregiver_name_label') }}
        <div class="d-flex">
          <c-v-divider
            vertical
            inset
          />
          <base-mo00009
            v-if="local.commonInfo.kinshipAuthorityFlag === Or15133Const.DEFAULT.VALUE_0"
            :oneway-model-value="localOneway.mo00009Oneway"
            @click="onClickOr55476('kaigoMainKakutei')"
          />
        </div>
      </c-v-col>
      <c-v-col cols="10">
        <c-v-row>
          <c-v-col
            cols="2"
            class="header-title-cell"
          >
            {{ t('label.name') }}
          </c-v-col>
          <c-v-col
            cols="10"
            class="data-cell"
          >
            <base-mo00045
              v-model="refValue.or15133Values.kaigoMainKakutei"
              :oneway-model-value="localOneway.mo00045Oneway1"
            />
          </c-v-col>
        </c-v-row>
        <c-v-row>
          <c-v-col
            cols="2"
            class="header-title-cell"
          >
            {{ t('label.primary_caregiver_name_relationship_age_label') }}
          </c-v-col>
          <c-v-col
            cols="10"
            class="data-cell d-flex"
          >
            <base-mo00040
              v-model="refValue.or15133Values.kaigoMainZcode"
              :oneway-model-value="localOneway.mo00040Oneway1"
            />
            <base-mo00038
              v-model="refValue.or15133Values.kaigoMainAge"
              class="mlr-10"
              :oneway-model-value="localOneway.mo00038Oneway1"
            ></base-mo00038>
          </c-v-col>
        </c-v-row>
        <c-v-row>
          <c-v-col
            cols="2"
            class="header-title-cell"
            style="border-bottom: 0"
          >
            {{ t('label.cohabitation_separation_label') }}
          </c-v-col>
          <c-v-col
            cols="4"
            class="data-cell"
            style="border-bottom: 0"
          >
            <base-mo00039
              v-model="refValue.or15133Values.kaigoMainKousei"
              :oneway-model-value="localOneway.mo00039Oneway"
            >
              <base-at-radio
                v-for="(item, index) in localOneway.codeListOneway
                  .CONFIRMATION_INFO_PRIMARY_CAREGIVER_COHABITATION_SEPARATION"
                :key="index"
                :name="'radio' + '-' + index"
                :radio-label="item.label"
                :value="item.value"
              />
            </base-mo00039>
          </c-v-col>
          <c-v-col
            cols="1"
            class="header-title-cell"
            style="border-left: 0; border-bottom: 0"
          >
            {{ t('label.tel-label') }}
          </c-v-col>
          <c-v-col
            cols="5"
            class="data-cell"
            style="border-bottom: 0"
          >
            <base-mo00045
              v-model="refValue.or15133Values.kaigoMainTel"
              :oneway-model-value="localOneway.mo00045Oneway2"
            />
          </c-v-col>
        </c-v-row>
      </c-v-col>
    </c-v-row>
    <!--キーパーソン-->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.key_person_label') }}
        <div class="d-flex">
          <c-v-divider
            vertical
            inset
          />
          <base-mo00009
            v-if="local.commonInfo.kinshipAuthorityFlag === Or15133Const.DEFAULT.VALUE_0"
            :oneway-model-value="localOneway.mo00009Oneway"
            @click="onClickOr55476('keyPerson')"
          />
        </div>
      </c-v-col>
      <c-v-col cols="10">
        <c-v-row>
          <c-v-col
            cols="2"
            class="header-title-cell"
          >
            {{ t('label.name') }}
          </c-v-col>
          <c-v-col
            cols="10"
            class="data-cell"
          >
            <base-mo00045
              v-model="refValue.or15133Values.keyPerson"
              :oneway-model-value="localOneway.mo00045Oneway1"
            />
          </c-v-col>
        </c-v-row>
        <c-v-row>
          <c-v-col
            cols="2"
            class="header-title-cell"
          >
            {{ t('label.primary_caregiver_name_relationship_age_label') }}
          </c-v-col>
          <c-v-col
            cols="10"
            class="data-cell d-flex"
          >
            <base-mo00040
              v-model="refValue.or15133Values.keyPersonZcode"
              :oneway-model-value="localOneway.mo00040Oneway1"
            />
            <base-mo00038
              v-model="refValue.or15133Values.keyPersonAge"
              class="mlr-10"
              :oneway-model-value="localOneway.mo00038Oneway1"
            ></base-mo00038>
          </c-v-col>
        </c-v-row>
        <c-v-row>
          <c-v-col
            cols="2"
            class="header-title-cell"
            style="border-bottom: 0"
          >
            {{ t('label.key_person_contact_tel_label') }}
          </c-v-col>
          <c-v-col
            cols="4"
            class="data-cell"
            style="border-bottom: 0"
          >
            <base-mo00030
              v-model="refValue.or15133Values.keyPersonRenrakuTel"
              :oneway-model-value="localOneway.mo00030Oneway"
            />
          </c-v-col>
          <c-v-col
            cols="1"
            class="header-title-cell"
            style="border-left: 0; border-bottom: 0"
          >
            {{ t('label.tel-label') }}
          </c-v-col>
          <c-v-col
            cols="5"
            class="data-cell"
            style="border-bottom: 0"
          >
            <base-mo00045
              v-model="refValue.or15133Values.keyPersonTel"
              :oneway-model-value="localOneway.mo00045Oneway2"
            />
          </c-v-col>
        </c-v-row>
      </c-v-col>
    </c-v-row>
    <!-- Or51775: 有機体: GUI00937 入力支援［ケアマネ］をポップアップで起動する。 -->
    <g-custom-or51775
      v-if="showDialogOr51775"
      v-bind="or51775"
      v-model="local.or51775"
      :oneway-model-value="localOneway.or51775Oneway"
      @confirm="handleOr51775Confirm"
    />
    <g-custom-or-55476
      v-if="showDialogOr55476"
      v-bind="or55476"
      :oneway-model-value="localOneway.or55476Oneway"
      @update:model-value="handleOr55476"
    />
  </div>
</template>

<style scoped lang="scss">
.row {
  display: flex;
  align-items: center;
  border-bottom: 1px gainsboro solid;
  border-left: 1px gainsboro solid;
  min-height: 62px;
}

.header-cell {
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 12px !important;
}

.header-title-cell {
  background-color: transparent;
  border-left: 1px gainsboro solid;
  border-bottom: 1px gainsboro solid;
  display: grid;
  align-items: center;
}

.data-cell {
  border-left: 1px gainsboro solid;
  border-right: 1px gainsboro solid;
  border-bottom: 1px gainsboro solid;
  background: #fff;
  padding: 10px 12px;
  width: 100%;
  min-height: 62px;
  display: grid;
  align-items: center;
}

:deep(.v-input__control) {
  background-color: rgb(var(--v-theme-surface));
}
:deep(.v-selection-control-group--inline) {
  align-items: center;
}
.mlr-10 {
  margin: 0 10%;
}
.w-86 {
  width: 86%;
}
.title {
  margin-top: 12px;
  background-color: #fff;
  border-left: 1px gainsboro solid;
  border-right: 1px gainsboro solid;
  border-bottom: 1px gainsboro solid;
}
.requiredText {
  :deep(.item-label) {
    margin-left: 2px;
    font-size: 18px !important;
  }
}
.item {
  order: 1;
  width: 100%;
  margin-right: 52% !important;
}
.itemWidth {
  width: 177px;
}
.item::before {
  content: '';
  order: -1 !important; /* 在第二排每个元素前插入一个伪元素，设置 order 为 -1 */
  flex-basis: 100% !important; /* 占满一行 */
}
</style>
