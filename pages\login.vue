<!-- ログイン画面 -->
<script setup lang="ts">
import { reactive, ref, watch, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import type { VForm } from 'vuetify/lib/components/index.mjs'
import { definePageMeta, useRoute, useNuxt<PERSON><PERSON>, useRouter, useSystemCommonsStore } from '#imports'
import { useAuthn, LoginType, TokenType } from '@/utils/useAuthn'
import { useValidation } from '@/utils/useValidation'
import type { OrLoginIdOnewayType, OrLoginIdType } from '~/types/business/components/OrLoginIdType'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type {
  OrLoginPasswordOnewayType,
  OrLoginPasswordType,
} from '~/types/business/components/OrLoginPasswordType'
import type { DebugLogPluginInterface } from '~/plugins/debug-log'
import type { Mo00040Type, Mo00040OnewayType } from '~/types/business/components/Mo00040Type'

import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'

import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo01266OnewayType } from '~/types/business/components/Mo01266Type'
import type { Mo00612OnewayType } from '~/types/business/components/Mo00612Type'
import type { Mo00606OnewayType } from '~/types/business/components/Mo00606Type'
import type { Mo00031OnewayType } from '~/types/business/components/Mo00031Type'

import { LoginIdInquiryRepository } from '~/repositories/business/core/account/loginid-inquiry/LoginIdInquiryRepository'

import { DatabaseNumberListRepository } from '~/repositories/business/core/database-control/database-number-list/DatabaseNumberListRepository'

import type { InWebEntity } from '~/types/common/api/InWebEntity'
import type {
  ILoginidInquiryInEntity,
  ILoginidInquiryOutEntity,
} from '~/repositories/business/core/account/loginid-inquiry/entities/LoginidInquiryEntity'

import { PasswordInquiryRepository } from '~/repositories/business/core/account/password-inquiry/PasswordInquiryRepository'
import type {
  IPasswordInquiryInEntity,
  IPasswordInquiryOutEntity,
} from '~/repositories/business/core/account/password-inquiry/entities/PasswordInquiryEntity'

import type {
  IDatabaseNumberListOutEntity,
  IDatabaseNumberListItem,
} from '~/repositories/business/core/database-control/database-number-list/entities/DatabaseNumberListEntity'

const systemCommonsStore = useSystemCommonsStore()

definePageMeta({
  layout: 'login-layout',
})

const { login } = useAuthn()
const { required, email, alphaDash, min, max } = useValidation()
const { t } = useI18n()

const MODE_NORMAL = 'NORMAL' // 通常モード
const MODE_ADMIN = 'ADMIN' // システム管理者モード
type ModeType = 'NORMAL' | 'ADMIN'

/**************************************************
 * 変数定義
 **************************************************/

const LoginForm = reactive({
  orLoginId: {
    mo00045: {
      value: '',
    } as Mo00045Type,
  } as OrLoginIdType,
  OrLoginPassword: {
    mo00045: {
      value: '',
    } as Mo00045Type,
  } as OrLoginPasswordType,
  mo00040DbNumber: {
    modelValue: '', // DB番号 プルダウンの選択値
  } as Mo00040Type,
})

const LoginFormOneway = reactive({
  orLoginIdOneway: {
    mo00045Oneway: {
      name: 'txtLoginId',
      rules: [required],
    } as Mo00045OnewayType,
  } as OrLoginIdOnewayType,
  orLoginPasswordOneway: {
    mo00045Oneway: {
      name: 'txtPassword',
      rules: [required],
    } as Mo00045OnewayType,
  } as OrLoginPasswordOnewayType,
  mo00609Oneway: {
    name: 'btnLogin',
    btnLabel: t('label.login'),
    block: true,
    appendIcon: 'login',
    size: 'large',
    minHeight: '44px',
    minWidth: '410px',
  } as Mo00609OnewayType,
  mo00040OnewayDbNumber: {
    itemLabel: t('label.db-number'),
    showItemLabel: true,
    isRequired: false,
    hideDetails: true,
    width: '410px',
    items: [] as { value: string; title: string }[], // DB番号 プルダウンの選択肢
  } as Mo00040OnewayType,
})

// 優先度1リンクボタン モード変更ボタン
const mo01266OnewayMode = ref<Mo01266OnewayType>({
  btnLabel: '→' + t('message.to-system-admin-login'),
  width: '250px',
  disabled: false,
  tooltipText: '',
})

// 優先度1リンクボタン ログインIDを忘れた場合ボタン
const mo01266OnewayForgotId = ref<Mo01266OnewayType>({
  btnLabel: t('label.forgot-login-id'),
  width: '200px',
  disabled: false,
  tooltipText: '',
})

// 優先度1リンクボタン パスワードを忘れた場合ボタン
const mo01266OnewayForgotPass = ref<Mo01266OnewayType>({
  btnLabel: t('label.forgot-password'),
  width: '200px',
  disabled: false,
  tooltipText: '',
})

/** ダイアログ ログインIDを忘れた場合 双方向バインド */
const mo00024ForgotId = ref<Mo00024Type>({
  isOpen: false,
})

/** ダイアログ ログインIDを忘れた場合 単方向バインド */
const mo00024OnewayForgotId = ref<Mo00024OnewayType>({
  width: '400px',
  persistent: true,
  showCloseBtn: true,
  disabledCloseBtnEvent: true, // バツボタン押下でダイアログを閉じない（emitTypeを監視）
  mo01344Oneway: {
    name: 'ForgotIdDialog',
    toolbarColor: 'white',
    toolbarTitle: t('label.forgot-your-login-id'),
    toolbarName: 'ForgotIdDialogToolBar',
    showCardActions: true,
    toolbarTitleCenteredFlg: false, // ダイアログのタイトルを左寄せ
  },
})

/** 入力フォーム ログインIDを忘れた場合 */
const inputFormForgotId = ref<VForm>()

/** ダイアログ パスワードを忘れた場合 双方向バインド */
const mo00024ForgotPass = ref<Mo00024Type>({
  isOpen: false,
})

/** ダイアログ パスワードを忘れた場合 単方向バインド */
const mo00024OnewayForgotPass = ref<Mo00024OnewayType>({
  width: '400px',
  persistent: true,
  showCloseBtn: true,
  disabledCloseBtnEvent: true, // バツボタン押下でダイアログを閉じない（emitTypeを監視）
  mo01344Oneway: {
    name: 'ForgotPassDialog',
    toolbarColor: 'white',
    toolbarTitle: t('label.forgot-your-password'),
    toolbarName: 'ForgotPassDialogToolBar',
    showCardActions: true,
    toolbarTitleCenteredFlg: false, // ダイアログのタイトルを左寄せ
  },
})

/** 入力フォーム パスワードを忘れた場合 */
const inputFormForgotPass = ref<VForm>()

/** ダイアログ 編集内容の確認 双方向バインド */
const mo00024ConfirmationEdits = ref<Mo00024Type>({
  isOpen: false,
})

/** ダイアログ 編集内容の確認 単方向バインド */
const mo00024OnewayConfirmationEdits = ref<Mo00024OnewayType>({
  width: '320px',
  persistent: true,
  showCloseBtn: false, // バツボタンは非表示
  mo01344Oneway: {
    name: 'ConfirmationEditsDialog',
    toolbarColor: 'white',
    toolbarTitle: t('label.confirmation-of-edits'),
    toolbarName: 'ConfirmationEditsDialogToolBar',
    showCardActions: true,
    toolbarTitleCenteredFlg: false, // ダイアログのタイトルを左寄せ
  },
})

/** ダイアログ メールを送信しました 双方向バインド */
const mo00024MailSent = ref<Mo00024Type>({
  isOpen: false,
})

/** ダイアログ メールを送信しました 単方向バインド */
const mo00024OnewayMailSent = ref<Mo00024OnewayType>({
  width: '320px',
  persistent: true,
  showCloseBtn: false, // バツボタンは非表示
  mo01344Oneway: {
    name: 'MailSentDialog',
    toolbarColor: 'white',
    toolbarTitle: t('message.email-sent'),
    toolbarName: 'MailSentDialogToolBar',
    showCardActions: true,
    toolbarTitleCenteredFlg: false, // ダイアログのタイトルを左寄せ
  },
})

/** キャンセルボタン */
const mo00612OnewayCancel = ref<Mo00612OnewayType>({
  btnLabel: t('btn.cancel'),
  width: '100px',
})

/** 送信ボタン */
const mo00609OnewaySend = ref<Mo00609OnewayType>({
  btnLabel: t('btn.send'),
  width: '100px',
})

/** 編集を続けるボタン */
const mo00612OnewayContinueEditing = ref<Mo00612OnewayType>({
  btnLabel: t('btn.continue-editing'),
  width: '120px',
})

/** 破棄ボタン（優先度1破壊ボタン） */
const mo00606OnewayDiscard = ref<Mo00606OnewayType>({
  btnLabel: t('btn.discard'),
  width: '100px',
})

/** OKボタン */
const mo00609OnewayOk = ref<Mo00609OnewayType>({
  btnLabel: t('btn.ok'),
  width: '90px',
})

// メールアドレス テキストフィールド  双方向バインド
const mo00045MailAddress = ref<Mo00045Type>({
  value: '',
})
// メールアドレス テキストフィールド  単方向バインド
const mo00045OnewayMailAddress = ref<Mo00045OnewayType>({
  name: 'MailAddressTextField',
  itemLabel: t('label.mail-address'),
  hideDetails: 'auto',
  showItemLabel: true,
  width: '350px',
  maxLength: '100',
  isRequired: true,
  validateOn: 'blur lazy', // バリデーションチェック実行設定
  rules: [required, email], // 必須、Eメールアドレス形式
})

// システム管理者のログインID テキストフィールド  双方向バインド
const mo00045AdminLoginId = ref<Mo00045Type>({
  value: '',
})
// システム管理者のログインID テキストフィールド  単方向バインド
const mo00045OnewayAdminLoginId = ref<Mo00045OnewayType>({
  name: 'AdminLoginIdTextField',
  itemLabel: t('label.system-admin-login-id'),
  hideDetails: 'auto',
  showItemLabel: true,
  width: '350px',
  maxLength: '50',
  isRequired: true,
  validateOn: 'blur lazy', // バリデーションチェック実行設定
  rules: [required, alphaDash, min(4), max(20)], // 必須、半角英字（記号「-」、「_」含む）、4文字以上、20文字以下
})

// ビックリマークアイコン 単方向バインド
const mo00031OnewayExclamationMark = ref<Mo00031OnewayType>({
  name: 'Mo00031OnewayExclamationMark',
  icon: 'error fill', // ビックリマークアイコン（"fill"で塗りつぶし表示）
  class: 'ma-0 pa-0',
  size: '24',
  color: 'rgb(var(--v-theme-red-700))',
})

// 現在のモード NORMAL or ADMIN
const nowMode = ref(MODE_NORMAL as ModeType)

// システム管理者用の文言の表示設定
const systemAdminDisplay = ref('none')

// エラーメッセージ表示フラグ ログインIDを忘れた場合ダイアログ（trueなら表示）
const errMsgFlgForgotId = ref(false)
// エラーメッセージ表示フラグ パスワードを忘れた場合ダイアログ（trueなら表示）
const errMsgFlgForgotPass = ref(false)

// 黒文字メッセージ メールを送信しましたダイアログ用
const msgMailSentBlack = ref('')
// 赤文字メッセージ メールを送信しましたダイアログ用
const msgMailSentRed = ref('')

/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(async () => {
  // DB番号プルダウンの選択肢を設定
  await setDbNumberChoices()
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * ログイン処理
 */
const inputForm = ref<VForm>()
const doLogin = async () => {
  // Validationエラーを消去する
  inputForm.value!.resetValidation()

  const { valid } = await inputForm.value!.validate()
  if (!valid) {
    // Validationエラーの場合はAPI呼び出ししないhttps://typescript-eslint.io/rules/no-unsafe-member-access
    return
  }

  const workDbNumber = ref('')
  if (LoginForm.mo00040DbNumber.modelValue !== undefined) {
    workDbNumber.value = LoginForm.mo00040DbNumber.modelValue
  }

  // ログイン リクエスト
  const result = await login(
    LoginForm.orLoginId.mo00045.value,
    LoginForm.OrLoginPassword.mo00045.value,
    workDbNumber.value,
    nowMode.value === MODE_NORMAL ? LoginType.GENERAL_USER : LoginType.SYSTEM_ADMIN,
    TokenType.WEB
  )()

  if (result?.isError) {
    if (result.errorCode === 'e.com.9009') {
      // ログインID、パスワードが不正である場合、システムエラーダイアログにメッセージを表示
      systemCommonsStore.setShowSystemErrorDialog(true)
      systemCommonsStore.setSystemErrorDialogMsg(result.message)
    } else if (result.errorCode === 'e.com.9019') {
      // パスワード有効期限切れ且つログイン不可設定の場合、パスワード変更ダイアログを表示
      systemCommonsStore.setShowSystemErrorDialog(true)
      systemCommonsStore.setSystemErrorDialogMsg(result.message)
    } else if (result.errorCode === 'e.com.9020') {
      // アカウントロックによるエラーの場合、システムエラーダイアログにメッセージを表示
      systemCommonsStore.setShowSystemErrorDialog(true)
      systemCommonsStore.setSystemErrorDialogMsg(result.message)
    }
  } else if (result?.errorCode === 'e.com.9019') {
    // パスワード有効期限切れ且つログイン可設定の場合、システムエラーダイアログにメッセージを表示
    systemCommonsStore.setShowSystemErrorDialog(true)
    systemCommonsStore.setSystemErrorDialogMsg(result.message)

    watch(
      () => systemCommonsStore.getShowSystemErrorDialog,
      async () => {
        await transition()
      }
    )
  } else {
    await transition()
  }
}

async function transition() {
  // middleware でログインページにリダイレクトした場合は redirectFrom に元のページが入っている
  const to = useRoute().redirectedFrom?.path ?? '/'
  await useRouter().push(to)
}

/**
 * モード変更クリック
 * - 「システム管理者用ログイン画面へ」又は
 * -「通常のログイン画面へ」をクリック時の処理。
 */
function clickModeCange() {
  if (nowMode.value === MODE_NORMAL) {
    // システム管理者モードに変更
    nowMode.value = MODE_ADMIN
    mo01266OnewayMode.value.btnLabel = '→' + t('message.to-normal-login')
    mo01266OnewayMode.value.width = '185px'
    systemAdminDisplay.value = 'block' // システム管理者用の文言を表示
  } else {
    // 通常モードに変更
    nowMode.value = MODE_NORMAL
    mo01266OnewayMode.value.btnLabel = '→' + t('message.to-system-admin-login')
    mo01266OnewayMode.value.width = '250px'
    systemAdminDisplay.value = 'none' // システム管理者用の文言を非表示
  }
}

/**
 * ログインIDを忘れた場合をクリック
 */
function clickForgotId() {
  mo00024ForgotId.value.isOpen = true
}

/**
 * パスワードを忘れた場合をクリック
 */
function clickForgotPass() {
  mo00024ForgotPass.value.isOpen = true
}

/**
 * ログインIDを忘れた場合・パスワードを忘れた場合ダイアログのキャンセルボタンクリック
 *
 * @param dialogName - 対象のダイアログ名
 */
function clickCancel(dialogName: string) {
  if (dialogName === 'ForgotId') {
    // ログインIDを忘れた場合ダイアログから呼び出された場合
    if (mo00045MailAddress.value.value !== '') {
      // 入力がある場合、編集内容の確認ダイアログを表示
      mo00024ConfirmationEdits.value.isOpen = true
    } else {
      // 入力が無ければダイアログを閉じる
      resetAllDialog()
    }
  } else if (dialogName === 'ForgotPass') {
    // パスワードを忘れた場合ダイアログから呼び出された場合
    if (mo00045MailAddress.value.value !== '' || mo00045AdminLoginId.value.value !== '') {
      // 入力がある場合、編集内容の確認ダイアログを表示
      mo00024ConfirmationEdits.value.isOpen = true
    } else {
      // 入力が無ければダイアログを閉じる
      resetAllDialog()
    }
  }
}

/**
 * 編集を続けるボタンをクリック
 */
function clickContinueEditing() {
  // 編集内容の確認ダイアログを閉じる
  mo00024ConfirmationEdits.value.isOpen = false
}

/**
 * 破棄ボタンをクリック
 */
function clickDiscard() {
  // ダイアログを閉じる
  resetAllDialog()
}

/**
 * OKボタンをクリック
 */
function clickOk() {
  // ダイアログを閉じる
  resetAllDialog()
}

/**
 * ログインIDを忘れた場合ダイアログの送信ボタンクリック
 */
const sendLoginidInquiry = async () => {
  // Validationエラーを消去する
  inputFormForgotId.value!.resetValidation()

  const { valid } = await inputFormForgotId.value!.validate()
  if (!valid) {
    // Validationエラーの場合はAPI呼び出ししない

    return
  }

  let resultErrorCode = ''
  let res: ILoginidInquiryOutEntity

  // リクエストのインプット
  const inEntity = {
    mailAddress: mo00045MailAddress.value.value,
  } as ILoginidInquiryInEntity

  try {
    res = await LoginIdInquiryRepository.loginidInquiry(inEntity) // リクエスト送信

    resultErrorCode = res.data?.errorCode ?? ''
  } catch (e: unknown) {
    const $log = useNuxtApp().$log as DebugLogPluginInterface
    $log.debug(e)
    resultErrorCode = 'error'
  }

  if (resultErrorCode !== '') {
    /** レスポンスがエラーの場合 */
    // エラーメッセージを表示
    errMsgFlgForgotId.value = true
  } else {
    /** レスポンスが正常の場合 */
    // メールを送信しましたダイアログのメッセージを設定
    msgMailSentBlack.value = t('message.email-check-login-id') // 黒文字メッセージ
    msgMailSentRed.value = '' // 赤文字メッセージ

    // ログインIDを忘れた場合ダイアログを非表示
    mo00024ForgotId.value.isOpen = false
    // メールを送信しましたダイアログを表示
    mo00024MailSent.value.isOpen = true
  }
}

/**
 * パスワードを忘れた場合ダイアログの送信ボタンクリック
 */
const sendPasswordInquiry = async () => {
  // Validationエラーを消去する
  inputFormForgotPass.value!.resetValidation()

  const { valid } = await inputFormForgotPass.value!.validate()
  if (!valid) {
    // Validationエラーの場合はAPI呼び出ししない

    return
  }

  let resultErrorCode = ''
  let res: IPasswordInquiryOutEntity

  // リクエストのインプット
  const inEntity = {
    systemAdminLoginid: mo00045AdminLoginId.value.value, // システム管理者のログインID
    mailAddress: mo00045MailAddress.value.value, // メールアドレス
  } as IPasswordInquiryInEntity

  try {
    res = await PasswordInquiryRepository.PasswordInquiry(inEntity) // リクエスト送信

    resultErrorCode = res.data?.errorCode ?? ''
  } catch (e: unknown) {
    const $log = useNuxtApp().$log as DebugLogPluginInterface
    $log.debug(e)
    resultErrorCode = 'error'
  }

  if (resultErrorCode !== '') {
    /** レスポンスがエラーの場合 */
    // エラーメッセージを表示
    errMsgFlgForgotPass.value = true
  } else {
    /** レスポンスが正常の場合 */
    // メールを送信しましたダイアログのメッセージを設定
    msgMailSentBlack.value = t('message.check-email-reset-password') // 黒文字メッセージ
    msgMailSentRed.value = t('message.password-reset-url-1-hour') // 赤文字メッセージ

    // パスワードを忘れた場合ダイアログを非表示
    mo00024ForgotPass.value.isOpen = false
    // メールを送信しましたダイアログを表示
    mo00024MailSent.value.isOpen = true
  }
}

/**
 * 全ダイアログをリセット
 * - 全てのダイアログを閉じて、入力値を初期化。
 */
function resetAllDialog() {
  /** ダイアログを閉じる */
  // ログインIDを忘れた場合
  mo00024ForgotId.value.isOpen = false
  // パスワードを忘れた場合
  mo00024ForgotPass.value.isOpen = false
  // 編集内容の確認
  mo00024ConfirmationEdits.value.isOpen = false
  // メールを送信しました
  mo00024MailSent.value.isOpen = false

  /** 入力値の初期化 */
  // メールアドレス
  mo00045MailAddress.value.value = ''
  // システム管理者のログインID
  mo00045AdminLoginId.value.value = ''

  /** エラーメッセージを非表示 */
  errMsgFlgForgotId.value = false //  ログインIDを忘れた場合ダイアログのエラーメッセージ表示フラグ
  errMsgFlgForgotPass.value = false //  パスワードを忘れた場合ダイアログのエラーメッセージ表示フラグ

  /** メッセージを初期化 */
  msgMailSentBlack.value = '' // 黒文字メッセージ メールを送信しましたダイアログ用
  msgMailSentRed.value = '' // 赤文字メッセージ メールを送信しましたダイアログ用
}

// ログインIDを忘れた場合ダイアログの、バツボタンクリックを監視
watch(
  () => mo00024ForgotId.value.emitType,
  (newValue) => {
    // バツボタン押下時
    if (newValue === 'closeBtnClick') {
      // キャンセルボタン押下時と同じ動作を実行
      clickCancel('ForgotId')

      // emitTypeを初期化
      mo00024ForgotId.value.emitType = 'blank'
    }
  }
)

// パスワードを忘れた場合ダイアログの、バツボタンクリックを監視
watch(
  () => mo00024ForgotPass.value.emitType,
  (newValue) => {
    // バツボタン押下時
    if (newValue === 'closeBtnClick') {
      // キャンセルボタン押下時と同じ動作を実行
      clickCancel('ForgotPass')

      // emitTypeを初期化
      mo00024ForgotPass.value.emitType = 'blank'
    }
  }
)

/**
 * DB番号プルダウンの選択肢を設定
 */
async function setDbNumberChoices() {
  let res: IDatabaseNumberListOutEntity
  const DbNumberList = ref([] as IDatabaseNumberListItem[])

  // リクエストのインプット
  const inEntity = {} as InWebEntity

  try {
    // リクエスト送信
    res = await DatabaseNumberListRepository.databaseNumberList(inEntity)

    if (res.data !== undefined) {
      DbNumberList.value = res.data

      // プルダウンの選択肢を設定
      LoginFormOneway.mo00040OnewayDbNumber.items = DbNumberList.value

      // プルダウンの選択値の初期値を設定
      if (DbNumberList.value.length > 0) {
        LoginForm.mo00040DbNumber.modelValue = DbNumberList.value[0].dbNumber
      } else {
        LoginForm.mo00040DbNumber.modelValue = ''
      }

      // プルダウンの活性・非活性を設定
      LoginFormOneway.mo00040OnewayDbNumber.disabled = false // 一旦初期化
      if (DbNumberList.value.length === 1) {
        LoginFormOneway.mo00040OnewayDbNumber.disabled = true // 非活性に設定
      }
    } else {
      LoginFormOneway.mo00040OnewayDbNumber.items = [] as IDatabaseNumberListItem[] // プルダウンの選択肢を設定
      LoginForm.mo00040DbNumber.modelValue = '' // プルダウンの選択値の初期値を設定
      LoginFormOneway.mo00040OnewayDbNumber.disabled = true // プルダウンを非活性に設定
    }
  } catch (e: unknown) {
    // DB番号リストを取得できなかった場合
    const $log = useNuxtApp().$log as DebugLogPluginInterface
    $log.debug(e)

    LoginFormOneway.mo00040OnewayDbNumber.items = [] as IDatabaseNumberListItem[] // プルダウンの選択肢を設定
    LoginForm.mo00040DbNumber.modelValue = '' // プルダウンの選択値の初期値を設定
    LoginFormOneway.mo00040OnewayDbNumber.disabled = true // プルダウンを非活性に設定
  }
}

/**
 * プルダウンの選択肢の表示設定
 *
 * @param item - 選択肢のアイテム
 */
function mo00040ItemProps(item: IDatabaseNumberListItem) {
  return {
    title: item.dbNumber,
    subtitle: item.logicalName,
    value: item.dbNumber,
  }
}
</script>

<template>
  <div class="pa-0 ma-0 bg">
    <c-v-row class="ma-0 pa-0 justify-end">
      <!-- 優先度1リンクボタン モード変更 -->
      <base-mo01266
        :oneway-model-value="mo01266OnewayMode"
        prepend-icon=""
        @click="clickModeCange()"
      />
    </c-v-row>

    <c-v-row class="form-row ma-0 pa-0">
      <c-v-col class="ma-0 pa-0">
        <!-- ログイン -->
        <c-v-row class="d-flex justify-center mt-1">
          <c-v-form
            ref="inputForm"
            @submit.prevent="doLogin"
          >
            <!-- 画像 -->
            <base-at-img
              name="imgLogo"
              src="/logo/carebase_logo.png"
              height="105px"
              width="480px"
              alt="Care Base"
            />

            <!-- システム管理者用ログイン画面の文言表示 -->
            <div class="system-admin-login pa-2 ma-0">
              {{ t('label.system-admin-login') }}
            </div>

            <c-v-sheet class="content-login-area px-0 rounded-lg">
              <!-- ログインID -->
              <g-base-or-login-id
                v-model="LoginForm.orLoginId"
                :oneway-model-value="LoginFormOneway.orLoginIdOneway"
              />

              <!--パスワード -->
              <g-base-or-login-password
                v-model="LoginForm.OrLoginPassword"
                :oneway-model-value="LoginFormOneway.orLoginPasswordOneway"
                class="pt-2"
              />

              <!-- DB番号 プルダウン -->
              <c-v-col class="px-5 pt-2">
                <base-mo00040
                  v-model="LoginForm.mo00040DbNumber"
                  :oneway-model-value="LoginFormOneway.mo00040OnewayDbNumber"
                  :item-props="mo00040ItemProps"
                />
              </c-v-col>

              <!-- ログインボタン アイコンをマテリアルにするなら、"mdi mdi-login" -->
              <c-v-col class="px-5 pt-4">
                <base-mo00609
                  :oneway-model-value="LoginFormOneway.mo00609Oneway"
                  type="submit"
                />
              </c-v-col>

              <template v-if="nowMode === MODE_ADMIN">
                <!-- システム管理者用 -->
                <c-v-col class="pt-2">
                  <c-v-row>
                    <c-v-col class="pt-2">
                      <!-- 優先度1リンクボタン ログインIDを忘れた場合 -->
                      <base-mo01266
                        :oneway-model-value="mo01266OnewayForgotId"
                        prepend-icon=""
                        @click="clickForgotId()"
                      />
                    </c-v-col>

                    <c-v-col class="pt-2 justify-end">
                      <!-- 優先度1リンクボタン パスワードを忘れた場合 -->
                      <base-mo01266
                        :oneway-model-value="mo01266OnewayForgotPass"
                        prepend-icon=""
                        @click="clickForgotPass()"
                      />
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </template>
            </c-v-sheet>
          </c-v-form>
        </c-v-row>
      </c-v-col>
    </c-v-row>
  </div>

  <!-- ログインIDを忘れた場合 ダイアログ START -->
  <base-mo00024
    v-if="nowMode === MODE_ADMIN"
    v-model="mo00024ForgotId"
    :oneway-model-value="mo00024OnewayForgotId"
  >
    <template #cardItem>
      <c-v-form
        ref="inputFormForgotId"
        class="pa-0 ma-0"
        style="height: 100%; width: 100%"
      >
        <c-v-sheet class="dialog-sheet-style">
          <p>{{ t('message.contact-email-address-send') }}</p>
          <p>{{ t('message.receive-email-admin-login-id') }}</p>

          <!-- メールアドレス テキストフィールド -->
          <base-mo-00045
            v-model="mo00045MailAddress"
            :oneway-model-value="mo00045OnewayMailAddress"
            class="pt-2 pb-2"
          />

          <!-- エラーメッセージ -->
          <div
            v-if="errMsgFlgForgotId === true"
            class="div-red-frame pa-2"
          >
            <c-v-row no-gutters>
              <c-v-col cols="1">
                <!-- ビックリマークアイコン -->
                <div class="exclamation-mark-icon">
                  <base-mo00031 :oneway-model-value="mo00031OnewayExclamationMark" />
                </div>
              </c-v-col>
              <c-v-col cols="11">
                <div
                  class="pl-4 pr-4 pb-1 ma-0"
                  style="color: rgb(var(--v-theme-red-700))"
                >
                  {{ t('message.error-in-email-address') }}
                </div>
              </c-v-col>
            </c-v-row>
          </div>
        </c-v-sheet>
      </c-v-form>
    </template>
    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <c-v-col cols="auto">
          <!-- キャンセルボタン -->
          <base-mo00612
            :oneway-model-value="mo00612OnewayCancel"
            @click="clickCancel('ForgotId')"
          />
        </c-v-col>

        <c-v-col
          cols="auto"
          class="pl-2 pr-2"
        >
          <!-- 送信ボタン -->
          <base-mo00609
            :oneway-model-value="mo00609OnewaySend"
            @click="sendLoginidInquiry()"
          />
        </c-v-col>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- ログインIDを忘れた場合 ダイアログ END -->

  <!-- パスワードを忘れた場合 ダイアログ START -->
  <base-mo00024
    v-if="nowMode === MODE_ADMIN"
    v-model="mo00024ForgotPass"
    :oneway-model-value="mo00024OnewayForgotPass"
  >
    <template #cardItem>
      <c-v-form
        ref="inputFormForgotPass"
        class="pa-0 ma-0"
        style="height: 100%; width: 100%"
      >
        <c-v-sheet class="dialog-sheet-style">
          <p>{{ t('message.login-id-and-contact-email-address-send') }}</p>
          <p>{{ t('message.email-with-url-password') }}</p>
          <p>{{ t('message.follow-email-reset-password') }}</p>

          <!-- システム管理者のログインID テキストフィールド -->
          <base-mo-00045
            v-model="mo00045AdminLoginId"
            :oneway-model-value="mo00045OnewayAdminLoginId"
            class="pt-2 pb-2"
          />

          <!-- メールアドレス テキストフィールド -->
          <base-mo-00045
            v-model="mo00045MailAddress"
            :oneway-model-value="mo00045OnewayMailAddress"
            class="pb-2"
          />

          <!-- エラーメッセージ -->
          <div
            v-if="errMsgFlgForgotPass === true"
            class="div-red-frame pa-2"
          >
            <c-v-row no-gutters>
              <c-v-col cols="1">
                <!-- ビックリマークアイコン -->
                <div class="exclamation-mark-icon">
                  <base-mo00031 :oneway-model-value="mo00031OnewayExclamationMark" />
                </div>
              </c-v-col>
              <c-v-col cols="11">
                <div
                  class="pl-4 pr-4 pb-1 ma-0"
                  style="color: rgb(var(--v-theme-red-700))"
                >
                  {{ t('message.error-in-login-id-email-address') }}
                </div>
              </c-v-col>
            </c-v-row>
          </div>
        </c-v-sheet>
      </c-v-form>
    </template>
    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <c-v-col cols="auto">
          <!-- キャンセルボタン -->
          <base-mo00612
            :oneway-model-value="mo00612OnewayCancel"
            @click="clickCancel('ForgotPass')"
          />
        </c-v-col>
        <c-v-col
          cols="auto"
          class="pl-2 pr-2"
        >
          <!-- 送信ボタン -->
          <base-mo00609
            :oneway-model-value="mo00609OnewaySend"
            @click="sendPasswordInquiry()"
          />
        </c-v-col>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- パスワードを忘れた場合 ダイアログ END -->

  <!-- 編集内容の確認 ダイアログ START -->
  <base-mo00024
    v-if="nowMode === MODE_ADMIN"
    v-model="mo00024ConfirmationEdits"
    :oneway-model-value="mo00024OnewayConfirmationEdits"
  >
    <template #cardItem>
      <c-v-sheet class="dialog-sheet-style">
        <p>{{ t('message.back-confirm') }}</p>
      </c-v-sheet>
    </template>
    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <c-v-col cols="auto">
          <!-- 編集を続けるボタン -->
          <base-mo00612
            :oneway-model-value="mo00612OnewayContinueEditing"
            @click="clickContinueEditing()"
          />
        </c-v-col>
        <c-v-col
          cols="auto"
          class="pl-2 pr-2"
        >
          <!-- 破棄ボタン -->
          <base-mo00606
            :oneway-model-value="mo00606OnewayDiscard"
            @click="clickDiscard()"
          />
        </c-v-col>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- 編集内容の確認 ダイアログ END -->

  <!-- メールを送信しました ダイアログ START -->
  <base-mo00024
    v-if="nowMode === MODE_ADMIN"
    v-model="mo00024MailSent"
    :oneway-model-value="mo00024OnewayMailSent"
  >
    <template #cardItem>
      <c-v-sheet class="dialog-sheet-style">
        <p>{{ msgMailSentBlack }}</p>
        <p style="color: rgb(var(--v-theme-red-700))">{{ msgMailSentRed }}</p>
      </c-v-sheet>
    </template>
    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <c-v-col
          cols="auto"
          class="pl-2 pr-2"
        >
          <!-- OKボタン -->
          <base-mo00609
            :oneway-model-value="mo00609OnewayOk"
            @click="clickOk()"
          />
        </c-v-col>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- メールを送信しました ダイアログ END -->
</template>
<style scoped lang="scss">
/* ログインエリアの背景色 */
.content-login-area {
  min-width: 450px;
  max-width: 450px;
  margin: 12px 0 24px 0;
  padding: 16px;
  background-color: #fff;
  box-shadow: 0.5px 1px 3px 1px rgba(0, 0, 0, 0.54) !important;
}

/**
  ヘッダー部分の枠線指定
 */
.notice-header {
  border: 1px solid #78909c;
  border-bottom: 10;
  max-width: 100%;
}

// システム管理者用ログイン画面の表示
.system-admin-login {
  background-color: transparent;
  color: rgb(var(--v-theme-red-700));
  font-size: 24px;
  text-align: center;
  font-weight: bold;
  display: v-bind('systemAdminDisplay');
}

// フォーム行の背景
.form-row {
  width: 100%;
  height: 100vh;
  align-items: center;
  justify-content: center;
}

.dialog-sheet-style {
  padding-top: 10px;
  padding-bottom: 10px;
  padding-left: 15px;
  padding-right: 15px;
}

// 赤枠
.div-red-frame {
  border-radius: 4px;
  border: 1px solid rgb(var(--v-theme-red-700));
  width: 350px;
  height: auto;
}
// ビックリマークアイコン
.exclamation-mark-icon {
  width: 36px;
  height: 36px;
  border-radius: 10%;
  align-items: center;
  display: flex;
  justify-content: center;
  // background-color: rgb(var(--v-theme-red-700));
  background-color: transparent;
}

// 全体の背景 アニメーション
.bg {
  width: 100%;
  height: 100vh;
  align-items: center;
  justify-content: center;
  background-size: 300% 300%;
  background-image: linear-gradient(-45deg, #e8f5e9 0%, #e8f5e9 25%, #e8f5e9 51%, #00856f 100%);
  -webkit-animation: AnimateBG 20s ease infinite;
  animation: AnimateBG 20s ease infinite;
}

@-webkit-keyframes AnimateBG {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

@keyframes AnimateBG {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}
</style>
