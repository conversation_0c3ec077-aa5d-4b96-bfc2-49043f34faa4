<script setup lang="ts">
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or51734Logic } from '~/components/custom-components/organisms/Or51734/Or51734.logic'
import { Or51734Const } from '~/components/custom-components/organisms/Or51734/Or51734.constants'
import type { Or51734OneWayType } from '~/components/custom-components/organisms/Or51734/Or51734.type'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI01250'
// ルーティング
const routing = 'GUI01250/pinia'
// 画面物理名
const screenName = 'GUI01250'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or51734 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01250' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI01250',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or51734Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or51734Const.CP_ID(1)]: or51734.value,
})

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  Or51734Logic.initialize(or51734.value.uniqueCpId)
}

const or51734OneWayType = ref<Or51734OneWayType>({
  svJigyoId: '2',
  shisetuId: '',
  kbnFlg: '1',
})

/**
 *  ボタン押下時の処理
 *
 */
function onClickOr51734() {
  Or51734Logic.state.set({
    uniqueCpId: or51734.value.uniqueCpId,
    state: { isOpen: true },
  })
}

// ダイアログ表示フラグ
const showDialogOr51734 = computed(() => {
  // Or51734のダイアログ開閉状態
  return Or51734Logic.state.get(or51734.value.uniqueCpId)?.isOpen ?? false
})
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr51734"
        >GUI01250_実施モニタリング記号マスタ
      </v-btn>
      <!-- POP画面ポップアップ CD 2025/04/30 ADD START-->
      <g-custom-or-51734
        v-if="showDialogOr51734"
        v-bind="or51734"
        :oneway-model-value="or51734OneWayType"
      />
    </c-v-col>
  </c-v-row>

  <!-- POP画面ポップアップ CD 2025/04/30 ADD END-->
</template>
