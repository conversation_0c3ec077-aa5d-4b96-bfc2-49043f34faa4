<script setup lang="ts">
/**
 * Or05657:(見通し)注釈ダイアログ
 * GUI00916_見通し
 *
 * @description
 * (見通し)注釈ダイアログ
 *
 * <AUTHOR> NGUYEN MANH HUNG
 */
/**
 * ================================
 * インポート
 * ================================
 */
// 必要なモジュールや型をインポート
import { reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or05657Const } from './Or05657.constants'
import type { Or05657StateType } from './Or05657.type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import { useScreenOneWayBind } from '#imports'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
/**
 * ================================
 * Props
 * ================================
 */
/**
 * 親コンポーネントから受け取るプロパティ
 */
interface Props {
  uniqueCpId: string
  parentUniqueCpId: string
}
/**
 * プロパティを定義
 */
const props = defineProps<Props>()

/**
 * ================================
 * 定数・Reactive・Ref
 * ================================
 */
/** i18n翻訳関数 */
const { t } = useI18n()

/** 単方向バインドのローカルデータ */
const localOneway = reactive({
  mo00024Oneway: {
    width: '800px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or05657',
      toolbarTitle: t('label.notes'),
      toolbarName: 'Or05657ToolBar',
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as unknown as Mo00024OnewayType,
  mo00611OneWay: {
    btnLabel: t('btn.close'),
    tooltipLocation: 'bottom',
    tooltipText: t('tooltip.screen-close'),
  } as Mo00611OnewayType,
})
/** ダイアログの表示状態管理 */
const mo00024 = ref<Mo00024Type>({
  isOpen: Or05657Const.DEFAULT.IS_OPEN,
})
/** ※１ラベル */
const mo01338OnewayNote1 = ref<Mo01338OnewayType>({
  value: t('label.notes_*1'),
})
/** ※２ラベル */
const mo01338OnewayNote2 = ref<Mo01338OnewayType>({
  value: t('label.notes_*2'),
})
/** ※３ラベル */
const mo01338OnewayNote3 = ref<Mo01338OnewayType>({
  value: t('label.notes_*3'),
})
/** ※４ラベル */
const mo01338OnewayNote4 = ref<Mo01338OnewayType>({
  value: t('label.notes_*4'),
})
/**
 * ================================
 * Piniaストアのインポート
 * ================================
 */
const { setState } = useScreenOneWayBind<Or05657StateType>({
  cpId: Or05657Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    /**
     * ダイアログの開閉状態を更新
     *
     * @param value - ダイアログの開閉状態
     */
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or05657Const.DEFAULT.IS_OPEN
    },
  },
})

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 * ダイアログが閉じられた場合にフラグを更新する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      close()
    }
  }
)

/**
 * ダイアログを閉じる処理
 * ダイアログを閉じ、OneWayBind領域のフラグを更新する
 */
function close() {
  setState({ isOpen: false })
}
</script>

<template>
  <!-- 基本ダイアログコンポーネント -->
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <!-- タブ：提供 -->
      <c-v-row
        no-gutters
        class="mt-2 mb-10"
      >
        <c-v-col
          cols="12 "
          class="note-text"
        >
          <!-- 入力方法選択のラベル -->
          <pre> <base-mo-01338 :oneway-model-value="mo01338OnewayNote1" /></pre>
        </c-v-col>
        <c-v-col cols="12 ">
          <!-- 入力方法選択のラベル -->
          <base-mo-01338 :oneway-model-value="mo01338OnewayNote2" />
        </c-v-col>
        <c-v-col cols="12 ">
          <!-- 入力方法選択のラベル -->
          <base-mo-01338 :oneway-model-value="mo01338OnewayNote3" />
        </c-v-col>
        <c-v-col cols="12 ">
          <!-- 入力方法選択のラベル -->
          <base-mo-01338 :oneway-model-value="mo01338OnewayNote4" />
        </c-v-col>
      </c-v-row>
    </template>
    <!-- フッター -->
    <template #cardActionRight>
      <!-- フッターアクションバー -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          @click="close"
        >
          <!-- ツールチップ表示 -->
          <c-v-tooltip
            v-if="localOneway.mo00611OneWay.tooltipText"
            :text="localOneway.mo00611OneWay.tooltipText"
            :location="localOneway.mo00611OneWay.tooltipLocation"
            activator="parent"
          />
        </base-mo00611>
      </c-v-row>
    </template>
    <!-- 情報ダイアログ -->
  </base-mo00024>
</template>

<style slot="scoped">
.note-text {
  white-space: pre-wrap; /* giữ nguyên xuống dòng \n và khoảng trắng đầu dòng */
  word-break: break-word;
  font-family: inherit;
  line-height: 1.6;
}
</style>
