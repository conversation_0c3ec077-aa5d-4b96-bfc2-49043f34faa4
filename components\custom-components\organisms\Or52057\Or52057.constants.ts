import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * Or52057:有機体:印刷設定モーダル（画面/特殊コンポーネント）
 * GUI00828_印刷設定
 *
 * @description
 * 印刷設定
 *
 * <AUTHOR>
 */
export namespace Or52057Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or52057', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false
    /**
     * セクション番号
     */
    export const SECTION_NO = 'ALL'
    /**
     * 個人情報表示フラグ
     */
    export const KOJINHOGO_USED_FLG = '0'
    /**
     * 個人情報表示値
     */
    export const SECTION_ADD_NO = '0'
    /**
     * システム略称
     */
    export const SYS_RYAKU = '3GK'
    /**
     * インデックス
     */
    export const INDEX = '0'
    /**
     * 単一
     */
    export const TANI = '0'
    /**
     * 複数
     */
    export const HUKUSUU = '1'
    /**
     * 期間管理する
     */
    export const KIKAN_FLG_1 = '1'
    /**
     * 期間管理しない
     */
    export const KIKAN_FLG_0 = '0'
    /**
     * メッセージボタンのタイプ - yes
     */
    export const MESSAGE_BTN_TYPE_YES = 'yes'
    /**
     * メッセージボタンのタイプ - no
     */
    export const MESSAGE_BTN_TYPE_NO = 'no'
    /**
     * メッセージボタンのタイプ - cancel
     */
    export const MESSAGE_BTN_TYPE_CANCEL = 'cancel'
    /**
     * 帳票ID: PDFダウンロード
     */
    export namespace PDF_DOWNLOAD_REPORT_ID {
      /**
       * Aタブ
       */
      export const A = 'AssessmentSheetRegionAReport'
      /**
       * Bタブ
       */
      export const B = 'AssessmentSheetRegionBReport'
      /**
       * Cタブ
       */
      export const C = 'AssessmentSheetRegionCReport'
      /**
       * Dタブ
       */
      export const D = 'AssessmentSheetRegionDReport'
      /**
       * 全てタブ
       */
      export const E = 'PackageAssessmentAllReport'
      /**
       * Fタブ
       */
      export const F = 'AssessmentSummaryTableReport'
    }
    /**
     * 印刷時に色をつける
     */
    export namespace TYPE_COLOR {
      /**
       * ON
       */
      export const ON ='1'
      /**
       * OFF
       */
      export const OFF = '2'
    }
    /**
     * 固定文字セット
     */
    export namespace STR {
      /**
       * 空文字
       */
      export const EMPTY  = ''
      /**
       * 区切り文字-コロン
       */
      export const SPLIT_COLON = '：'
      /**
       * 区切り文字- チルダ
       */
      export const SPLIT_TILDE = ' ～ '
      /**
       * true
       */
      export const TRUE = 'true'
      /**
       * ZERO
       */
      export const ZERO = '0'
      /**
       * ONE
       */
      export const ONE = '1'
      /**
       * TWO
       */
      export const TWO = '2'
      /**
       * THREE
       */
      export const THREE = '3'
      /**
       * FOUR
       */
      export const FOUR = '4'
      /**
       * FIVE
       */
      export const FIVE = '5'
      /**
       * SIX
       */
      export const SIX = '6'
      /**
       * SEVEN
       */
      export const SEVEN = '7'
      /**
       * EIGHT
       */
      export const EIGHT = '8'
      /**
       * NINE
       */
      export const NINE = '9'
      /**
       * TEN
       */
      export const TEN = '10'
      /**
       * ELEVEN
       */
      export const ELEVEN = '11'
      /**
       * TWELVE
       */
      export const TWELVE = '12'
      /**
       * THIRTEEN
       */
      export const THIRTEEN = '13'
      /**
       * FOURTEEN
       */
      export const FOURTEEN = '14'
      /**
       * FIFTEEN
       */
      export const FIFTEEN = '15'
      /**
       * SIXTEEN
       */
      export const SIXTEEN = '16'
      /**
       * SEVENTEEN
       */
      export const SEVENTEEN = '17'
      /**
       * EIGHTEEN
       */
      export const EIGHTEEN = '18'
      /**
       * NINETEEN
       */
      export const NINETEEN = '19'
      /**
       * TEENTY
       */
      export const TEENTY = '20'
      /**
       * TWENTI_ONE
       */
      export const TWENTI_ONE = '21'
      /**
       * TWENTI_TWO
       */
      export const TWENTI_TWO = '22'
      /**
       * TWENTI_THREE
       */
      export const TWENTI_THREE = '23'
    }
    /**
     * 数値定数
     */
    export namespace NUMBER  {
      /**
       * ZERO
       */
      export const ZERO = 0
      /**
       * 一
       */
      export const ONE = 1
    }
  }
  /**
   * 調査アセスメント種別
   */
  export namespace SURVEY_ASSESSMENT_KIND {
    /**
     * 居宅版
     */
    export const HOME = '1'
    /**
     * 施設版
     */
    export const FACILITY = '2'
    /**
     * 高齢者住宅版
     */
    export const SENIOR_HOUSING = '3'
  }
}
