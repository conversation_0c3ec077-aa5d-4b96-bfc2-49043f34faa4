<script setup lang="ts">
/**
 * Or52057:有機体:印刷設定
 * GUI00793_印刷設定
 *
 * @description
 * 印刷設定
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { reactive, ref, onMounted, watch, computed } from 'vue'
import { Or52057Const } from './Or52057.constants'
import type { Or52057StateType } from './Or52057.type'
import { useSetupChildProps, useScreenOneWayBind, useNuxtApp, dateUtils } from '#imports'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01338OnewayType } from '@/types/business/components/Mo01338Type'
import type { Mo01344OnewayType } from '@/types/business/components/Mo01344Type'
import type { Mo00039Items, Mo00039OnewayType } from '@/types/business/components/Mo00039Type'
import type { Mo00020Type, Mo00020OnewayType } from '@/types/business/components/Mo00020Type'
import type { Mo00018Type, Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
// import type {
//   UserEntity,
//   PeriodHistoryEntity,
//   PrtEntity,
//   IAssessmentInterRAIPrintSettingsSelectInEntity,
//   AssessmentInterRAIPackagePlanPrintSettingsSelectOutEntity,
//   IAssessmentInterRAIPrintSettingsSubjectSelectInEntity,
//   IAssessmentInterRAIPrintSettingsSubjectSelectOutEntity,
//   IAssessmentInterRAIPrintSettingsHistorySelectInEntity,
//   IAssessmentInterRAIPrintSettingsHistorySelectOutEntity,
// } from '~/repositories/cmn/entities/AssessmentInterRAIPrintSettingsSelectEntity'
import type {
  PeriodHistoryListData,
  RirekiListData,
  UserlistData,
  PrintOption,
  PrintHistoryList,
  PrintConfigure,
  PrtEntity,
  AssessmentSheetRegionReportReportData,
  AssessmentInterRAIPackagePlanPrintSettingsSelectInEntity,
  AssessmentInterRAIPackagePlanPrintSettingsSelectOutEntity,
  AssessmentInterRAIPackagePlanPrintSettingsHistorySelectInEntity,
  AssessmentInterRAIPackagePlanPrintSettingsHistorySelectOutEntity,
  IAssessmentInterRAIPackagePlanPrintSettingsSubjectSelectInEntity,
  IAssessmentInterRAIPackagePlanPrintSettingsSubjectSelectOutEntity,
} from '~/repositories/cmn/entities/AssessmentInterRAIPackagePlanPrintSettingsSelectEntity'

import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo00040OnewayType, Mo00040Type } from '~/types/business/components/Mo00040Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'

import type {
  SysIniInfoEntity,
  IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity,
  IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateOutEntity,
} from '~/repositories/cmn/entities/FreeAssessmentFacePrintSettingsPrtNoChangeUpdateEntity'
import type {
  IFreeAssessmentFacePrintSettingsUpdateInEntity,
  IFreeAssessmentFacePrintSettingsUpdateOutEntity,
} from '~/repositories/cmn/entities/FreeAssessmentFacePrintSettingsUpdateEntity'
import type {
  Mo01334OnewayType,
  Mo01334Type,
  Mo01334Items,
} from '~/types/business/components/Mo01334Type'
import { OrX0117Const } from '~/components/custom-components/organisms/OrX0117/OrX0117.constants'
import { OrX0117Logic } from '~/components/custom-components/organisms/OrX0117/OrX0117.logic'
import type { OrX0117History, OrX0117OnewayType } from '~/types/cmn/business/components/OrX0117Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { CustomClass } from '~/types/CustomClassType'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { OrX0128Const } from '~/components/custom-components/organisms/OrX0128/OrX0128.constants'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import { OrX0130Logic } from '~/components/custom-components/organisms/OrX0130/OrX0130.logic'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import type {
  OrX0128OnewayType,
  OrX0128Items,
  OrX0128Headers,
} from '~/types/cmn/business/components/OrX0128Type'
import { OrX0128Logic } from '~/components/custom-components/organisms/OrX0128/OrX0128.logic'
import type {
  Or52057MsgBtnType,
  Or52057Param,
} from '~/components/custom-components/organisms/Or52057/Or52057.type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Or21814OnewayType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { useReportUtils, reportOutputType } from '~/utils/useReportUtils'
import type {
  PrintSetEntity,
  PrintOptionEntity,
  PrintSubjectHistoryEntity,
  ChoPrtEntity,
  ICpnTucRaiAssReportSelectInEntity,
} from '~/repositories/cmn/entities/CpnTucRaiAssReportSelectEntity'
import type { DebugLogPluginInterface } from '~/plugins/debug-log'
import { useCmnCom } from '@/utils/useCmnCom'
import { useCmnRouteCom } from '~/utils/useCmnRouteCom'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import type { Or21813StateType } from '~/components/base-components/organisms/Or21813/Or21813.type'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import { OrX0145Const } from '~/components/custom-components/organisms/OrX0145/OrX0145.constants'
const systemCommonsStore = useSystemCommonsStore()
const cmnRouteCom = useCmnRouteCom()
const { reportOutput } = useReportUtils()
const { convertDateToSeireki } = dateUtils()
const $log = useNuxtApp().$log as DebugLogPluginInterface

const { t } = useI18n()
/************************************************
 * Props
 ************************************************/
interface Props {
  uniqueCpId: string
}

const props = defineProps<Props>()

// 子コンポーネント用変数
const orX0117 = ref({ uniqueCpId: '' })
const orX0128 = ref({ uniqueCpId: '' })
const orX0130 = ref({ uniqueCpId: '' })
const or21814 = ref({ uniqueCpId: '' })
const or21813 = ref({ uniqueCpId: '' })
const orx0145 = ref({ uniqueCpId: '' })

const localOneway = reactive({
  /**
   * 基本設定
   */
  mo01338OneWayBasicSettings: {
    value: t('label.basic-settings'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 帳票タイトル
   */
  mo00615OneWayTypeTitle: {
    itemLabel: t('label.print-settings-title'),
    showItemLabel: true,
  } as Mo00615OnewayType,
  /**
   * タイトル表示
   */
  mo01338OneWay: {
    value: Or52057Const.DEFAULT.STR.EMPTY,
    customClass: {
      outerClass: '',
      outerStyle: 'background: transparent',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: 'background: transparent',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 日付印刷区分
   */
  mo00039OneWay: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  /**
   * 指定日
   */
  mo00020OneWay: {
    showItemLabel: false,
    showSelectArrow: false,
    mo00009OnewayBack: {},
    mo00009OnewayForward: {},
  } as Mo00020OnewayType,
  /**
   * 記入用シートを印刷する
   */
  mo00018OneWay: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-entry-sheet'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
   * 選択履歴のデータのみを印刷する
   */
  mo00018OneWaySelectionHistory: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.selected-history-printing'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
   * 履歴情報を印刷する（基準日、作成者）
   */
  mo00018OneWayHistoryInfo: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.history-info-print-base-date-author'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
  /**
   * アセスメントタイプ
   */
  mo00039OneWayAssessmentType: {
    name: '',
    showItemLabel: false,
    inline: false,
    disabled: true,
  } as Mo00039OnewayType,
  /**
   * 説明文を印刷するチェックボックス
   */
  mo00018OneWayTypePrintCheck: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-description'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  // /**
  //  * 印刷時に色をつける
  //  */
  // mo00018OneWayColor: {
  //   name: '',
  //   itemLabel: '',
  //   checkboxLabel: t('label.printer-with-color'),
  //   isVerticalLabel: true,
  //   showItemLabel: true,
  //   indeterminate: false,
  //   hideDetails: 'auto',
  //   disabled: false,
  // } as Mo00018OnewayType,
  /**
   * 印刷オプション
   */
  mo01338OneWayPrinterOption: {
    value: t('label.printer-option'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 利用者選択ラベル
   */
  mo01338OneWayPrinterUserSelect: {
    value: t('label.printer-user-select'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 履歴選択ラベル
   */
  mo01338OneWayPrinterHistorySelect: {
    value: t('label.printer-history-select'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 利用者選択
   */
  mo00039OneWayUserSelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  /**
   * 履歴選択
   */
  mo00039OneWayHistorySelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  /**
   * 基準日
   */
  mo00615OneWayType: {
    itemLabel: t('label.base-date'),
    showItemLabel: true,
  } as Mo00615OnewayType,
  /**
   * 基準日: 表用テキストフィールド
   */
  mo00020KijunbiOneWay: {
    showItemLabel: false,
    showSelectArrow: true,
    width: '132px',
    totalWidth: '220px',
    mo00009OnewayBack: {},
    mo00009OnewayForward: {},
  } as Mo00020OnewayType,
  /**
   * 担当ケアマネプルダウン
   */
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: true,
    multiple: false,
    selectedUserCounter: Or52057Const.DEFAULT.STR.EMPTY,
  } as OrX0145OnewayType,
  /**
   * 閉じるボタン
   */
  mo00611OneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  /**
   * PDFダウンロードボタン
   */
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
    disabled: false,
  } as Mo00609OnewayType,
  /** ※まとめを印刷する場合はチェックを入れてください。\r\n(H21改訂版のみ) */
  mo00615Oneway: {
    itemLabel: t('label.print-check-h21-revision-label'),
    showItemLabel: true,
    showRequiredLabel: false,
  } as Mo00615OnewayType,
  /** 選択項目を囲む色ラベル */
  mo00615OnewaySelectColor: {
    itemLabel: t('label.select-color'),
    showItemLabel: true,
    showRequiredLabel: false,
  } as Mo00615OnewayType,
  mo00040OneWay: {
    showItemLabel: false,
    width: '90',
    items: [],
    itemTitle: 'label',
    itemValue: 'value',
  } as Mo00040OnewayType,
})

/**
 * 親画面の情報
 */
const local = {
  /**
   * 個人情報使用フラグ
   */
  kojinhogoUsedFlg: Or52057Const.DEFAULT.KOJINHOGO_USED_FLG,
  /**
   * 個人情報番号
   */
  sectionAddNo: Or52057Const.DEFAULT.SECTION_ADD_NO,
  /**
   * 親画面.事業所ID
   */
  svJigyoId: Or52057Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.施設ID
   */
  shisetuId: Or52057Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.担当者ID
   */
  tantoId: Or52057Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.種別ID
   */
  syubetsuId: Or52057Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.セクション名
   */
  sectionName: Or52057Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.利用者ID
   */
  userId: Or52057Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.事業所名
   */
  svJigyoKnj: Or52057Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.処理年月日
   */
  processYmd: Or52057Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.アセスメントID
   */
  assessmentId: Or52057Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.フォーカス設定用イニシャル
   */
  focusSettingInitial: [] as string[],
  /**
   * 親画面.初期選択状態の担当者カウンタ値
   */
  selectedUserCounter: Or52057Const.DEFAULT.STR.EMPTY,
  /**
   * 選択された帳票のプロファイル
   */
  profile: Or52057Const.DEFAULT.STR.EMPTY,
  /**
   * 選択利用者ID
   */
  selectUserId: Or52057Const.DEFAULT.STR.EMPTY,
  /**
   * 出力帳票名一覧に選択行番号
   */
  index: Or52057Const.DEFAULT.STR.EMPTY,
  /**
   * システムINI情報
   */
  sysIniInfo: {} as SysIniInfoEntity,
  /**
   * 履歴一覧が0件選択の場合（※履歴リストが0件、複数件を含む）
   */
  historyNoSelect: false,
  /**
   * 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
   */
  userNoSelect: false,
  /**
   * 帳票番号
   */
  prtNo: Or52057Const.DEFAULT.STR.EMPTY,
  /**
   * 選択利用者リスト
   */
  userList: [] as UserlistData[],
  /**
   * 帳票ID
   */
  reportId: Or52057Const.DEFAULT.STR.EMPTY,
  /**
   * 履歴選択の明細
   */
  orX0128DetList: [] as OrX0128Items[],
  /**
   * 印刷設定情報リスト
   */
  prtList: [] as PrtEntity[],
  mo00040Type: {
    modelValue: '',
  } as Mo00040Type,
}

/**
 * レスポンスパラメータ詳細
 */
const localData: AssessmentInterRAIPackagePlanPrintSettingsSelectOutEntity = {
  data: {},
} as AssessmentInterRAIPackagePlanPrintSettingsSelectOutEntity
/**************************************************
 * 変数定義
 **************************************************/
// ダイアログ
const mo00024Oneway = ref<Mo00024OnewayType>({
  width: '1300px',
  persistent: true,
  showCloseBtn: true,
  mo01344Oneway: {
    name: 'Or52057',
    toolbarTitle: t('label.print-set'),
    toolbarName: 'Or52057ToolBar',
    toolbarTitleCenteredFlg: false,
    showCardActions: true,
    cardTextClass: 'or52057_content',
  } as Mo01344OnewayType,
})

const mo00024 = ref<Mo00024Type>({
  isOpen: Or52057Const.DEFAULT.IS_OPEN,
})

/**
 * 出力帳票名一覧
 */
const mo01334Oneway = ref<Mo01334OnewayType>({
  headers: [
    {
      title: t('label.ledger'),
      key: 'ledgerName',
      sortable: false,
      minWidth: '100',
    },
  ],
  items: [],
  height: 655,
})

/**
 * 出力帳票名一覧
 */
const mo01334Type = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

/**
 * 日付印刷区分
 */
const mo00039Type = ref<string>('')

/**
 * 指定日
 */
const mo00020Type = ref<Mo00020Type>({
  value: convertDateToSeireki(undefined),
} as Mo00020Type)

/**
 * 選択履歴のデータのみを印刷する
 */
const mo00018Type = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 選択履歴のデータのみを印刷する
 */
const mo00018TypeSelectionHistory = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 履歴情報を印刷する（基準日、作成者）
 */
const mo00018TypeSelectionHistoryInfo = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 説明文を印刷するチェックボックス
 */
const mo00018TypePrintCheck = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * アセスメントタイプ
 */
const mo00039OneWayAssessmentTypeType = ref<string>('')

/**
 * 印刷時に色をつける
 */
const mo00018TypeColor = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 利用者選択
 */
const mo00039OneWayUserSelectType = ref<string>('')

/**
 * 履歴選択
 */
const mo00039OneWayHistorySelectType = ref<string>('')

/**
 * 当ケアマネプルダウン選択値
 */
const orX0145Type = ref<OrX0145Type>({
  value: {} as TantoCmnShokuin,
} as OrX0145Type)

/**
 * 基準日
 */
const mo00020TypeKijunbi = ref<Mo00020Type>({
  value: convertDateToSeireki(undefined),
} as Mo00020Type)

/**
 * 指定日非表示/表示フラゲ
 */
const mo00020Flag = ref<boolean>(false)
/**
 * 基準日フラゲ
 */
const kijunbiFlag = ref<boolean>(false)
/**
 * 履歴一覧セクションフラゲ
 */
const mo01334TypeHistoryFlag = ref<boolean>(true)

/**
 * 利用者列幅
 */
const userCols = ref<number>(4)

const orX0128OnewayModel = reactive<OrX0128OnewayType>({
  /**
   * 期間管理フラグ
   */
  kikanFlg: '1',
  /**
   * 単一複数フラグ(0:単一,1:複数)
   */
  singleFlg: OrX0128Const.DEFAULT.TANI,
  tableStyle: 'width:515px',
  headers: [
    // 作成日
    {
      title: t('label.create-date'),
      key: 'createYmd',
      sortable: false,
      minWidth: '100',
    },
    // 作成者
    {
      title: t('label.author'),
      key: 'shokuinKnj',
      sortable: false,
      minWidth: '140',
    },
    // ケース番号
    {
      title: t('label.history-selection-case-number'),
      key: 'caseNo',
      sortable: false,
      minWidth: '150',
    },
    // 改訂
    {
      title: t('label.history-selection-case-number'),
      key: 'kaitei',
      sortable: false,
      minWidth: '150',
    },
  ] as OrX0128Headers[],
  items: [],
})

/**
 * 利用者
 */
const orX0130Oneway = reactive<OrX0130OnewayType>({
  /**
   * 選択モート
   */
  selectMode: OrX0130Const.DEFAULT.TANI,
  /**
   * テーブルのスタイル
   */
  tableStyle: 'width:210px',
  /**
   * 指定行選択
   */
  userId: Or52057Const.DEFAULT.STR.EMPTY,
  /**
   * 複数選択時、50音の選択数を表示するか
   */
  showKanaSelectionCount: true,
  /**
   * フォーカス設定用イニシャル
   */
  focusSettingInitial: [] as string[],
})

/**
 * 担当ケアマネ選択
 */
const tantoIconBtn = ref<boolean>(false)
/**
 * 印刷設定帳票出力
 */
const orX0117Oneway: OrX0117OnewayType = {
  type: Or52057Const.DEFAULT.TANI,
  historyList: [] as OrX0117History[],
} as OrX0117OnewayType

/**
 * 初期情報取得フラゲ
 */
const initFlag = ref<boolean>(false)

/**
 * 期間管理フラグ
 */
const kikanFlag = ref<string>(Or52057Const.DEFAULT.STR.EMPTY)
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or52057StateType>({
  cpId: Or52057Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or52057Const.DEFAULT.IS_OPEN
    },
    param: (value) => {
      if (value) {
        local.prtNo = value.prtNo
        local.svJigyoId = value.svJigyoId
        local.shisetuId = value.shisetuId
        local.tantoId = value.tantoId
        local.syubetsuId = value.syubetsuId
        local.sectionName = value.sectionName
        local.userId = value.userId
        local.svJigyoKnj = value.svJigyoKnj
        local.processYmd = value.processYmd
        local.assessmentId = value.assessmentId
        local.focusSettingInitial = value.focusSettingInitial
        local.selectedUserCounter = value.selectedUserCounter
      }
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0117Const.CP_ID(0)]: orX0117.value,
  [OrX0128Const.CP_ID(0)]: orX0128.value,
  [OrX0130Const.CP_ID(0)]: orX0130.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or21813Const.CP_ID(0)]: or21813.value,
  [OrX0145Const.CP_ID(0)]: orx0145.value,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 画面ID
const screenId = 'GUI00793'
// ルーティング
const routing = 'GUI00793/pinia'
// ケアマネ画面を初期化する
await useCmnCom().initialize({
  screenId,
  routing,
})

onMounted(async () => {
  // 汎用コード取得API実行
  await initCodes()

  // 画面ボタン活性非活性設定
  btnItemSetting()

  // 初期情報取得
  await init()

  // 初期選択データ設定
  selectRowDataSetting()
})

// ダイアログ表示フラグ
const showDialogOrX0117 = computed(() => {
  // Or52057のダイアログ開閉状態
  return OrX0117Logic.state.get(orX0117.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 初期選択データ設定
 */
const selectRowDataSetting = () => {
  // フォーカス設定用イニシャル設定
  orX0130Oneway.focusSettingInitial = local.focusSettingInitial
  // 初期選択状態の担当者カウンタ値設定
  localOneway.orX0145Oneway.selectedUserCounter = local.selectedUserCounter
  // 利用者一覧明細に親画面.利用者IDが存在する場合
  if (local.userId) {
    // 利用者IDを対するレコードを選択状態にする
    orX0130Oneway.userId = local.userId
  }
  // 利用者一覧明細に親画面.利用者IDが存在しない場合
  else {
    orX0130Oneway.userId = Or52057Const.DEFAULT.STR.EMPTY
  }
}

/**
 * 汎用コード取得API実行
 */
const initCodes = async () => {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // アセスメント種別コード
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND },
    // 日付印刷区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    // 単複数選択区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY },
    // 性別区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_GENDER_CATEGORY },
    // 選択項目を囲む色リスト
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SELECT_HIFHLIGHT_COL_LIST },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  // colorList
  const colorList: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_SELECT_HIFHLIGHT_COL_LIST
  )
  if (colorList?.length > 0) {
    local.mo00040Type.modelValue = colorList[0].value
    const list = []
    for (const item of colorList) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        })
      }
    }
    localOneway.mo00040OneWay.items = list
  }

  // 日付印刷区分
  const bizukePrintCategoryCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )
  if (bizukePrintCategoryCodeTypes?.length > 0) {
    mo00039Type.value = bizukePrintCategoryCodeTypes[0].value
    const list: Mo00039Items[] = []
    for (const item of bizukePrintCategoryCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    localOneway.mo00039OneWay.items = list
  }

  // アセスメントタイプ
  const assessmentKindCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND
  )
  if (assessmentKindCodeTypes?.length > 0) {
    const list: Mo00039Items[] = []
    for (const item of assessmentKindCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    localOneway.mo00039OneWayAssessmentType.items = list
  }

  // 利用者選択 TAN_MULTIPLE_SELECT_CATEGORY
  const tanMultipleSelectCategoryCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )
  if (tanMultipleSelectCategoryCodeTypes?.length > 0) {
    mo00039OneWayUserSelectType.value = tanMultipleSelectCategoryCodeTypes[0].value
    mo00039OneWayHistorySelectType.value = tanMultipleSelectCategoryCodeTypes[0].value
    const list: Mo00039Items[] = []
    for (const item of tanMultipleSelectCategoryCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    localOneway.mo00039OneWayHistorySelectType.items = list
    localOneway.mo00039OneWayUserSelectType.items = list
  }
}

/**
 * 初期プロジェクト設定
 */
const initSetting = () => {
  // 親画面.処理年月日が""の場合
  if (Or52057Const.DEFAULT.STR.EMPTY === local.processYmd) {
    // 担当ケアマネ選択
    tantoIconBtn.value = false
  } else {
    // 担当ケアマネ選択
    tantoIconBtn.value = true
  }
}

/**
 * 初期情報取得
 */
const init = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: AssessmentInterRAIPackagePlanPrintSettingsSelectInEntity = {
    sysCd: systemCommonsStore.getSystemCode,
    sysRyaku: Or52057Const.DEFAULT.SYS_RYAKU,
    houjinId: systemCommonsStore.getHoujinId,
    shisetuId: local.shisetuId,
    svJigyoId: local.svJigyoId,
    shokuId: systemCommonsStore.getStaffId,
    userId: local.userId,
    tantoId: local.tantoId,
    sectionName: local.sectionName,
    index: Or52057Const.DEFAULT.INDEX,
    syubetsuId: local.syubetsuId,
    kojinhogoUsedFlg: Or52057Const.DEFAULT.KOJINHOGO_USED_FLG,
    sectionAddNo: Or52057Const.DEFAULT.SECTION_ADD_NO,
  } as AssessmentInterRAIPackagePlanPrintSettingsSelectInEntity
  const resp: AssessmentInterRAIPackagePlanPrintSettingsSelectOutEntity =
    await ScreenRepository.select('assessmentInterRAIPackagePlanPrintSettingsSelect', inputData)
  if (resp?.data) {
    // レスポンスパラメータ詳細
    localData.data = { ...resp?.data }

    // 期間管理フラグ
    kikanFlag.value = localData.data.kikanFlg

    const prtList: Mo01334Items[] = []
    for (const item of resp.data.prtList) {
      if (item) {
        prtList.push({
          id: item.prtNo,
          mo01337OnewayLedgerName: {
            value: item.prtTitle,
            unit: Or52057Const.DEFAULT.STR.EMPTY,
          } as Mo01337OnewayType,
          prnDate: item.prnDate === Or52057Const.DEFAULT.STR.TRUE,
          selectable: true,
          profile: item.profile,
          index: item.index,
          prtNo: item.prtNo,
        } as Mo01334Items)

        if (prtList.length === Or52057Const.DEFAULT.NUMBER.ONE) {
          mo00018TypeColor.value.modelValue = item.param05 === Or52057Const.DEFAULT.STR.TRUE
        }
      }
    }
    mo01334Oneway.value.items = prtList
    initFlag.value = true
    // アセスメント履歴情報を取得する
    await getPrintSettingsHistoryList()
    outputLedgerName(local.prtNo)
  }

  initSetting()
}

/**
 * アセスメント履歴一覧データ
 *
 * @param periodHistoryList - アセスメント履歴リスト
 */
const getHistoryData = (periodHistoryList: PeriodHistoryListData[]) => {
  if (Or52057Const.DEFAULT.KIKAN_FLG_1 === kikanFlag.value) {
    const tempList: string[] = [] as string[]
    let list: OrX0128Items[] = []
    for (const item of periodHistoryList) {
      if (item) {
        const planPeriod =
          t('label.plan-period') +
          Or52057Const.DEFAULT.STR.SPLIT_COLON +
          item.startYmd +
          Or52057Const.DEFAULT.STR.SPLIT_TILDE +
          item.endYmd
        if (!tempList.includes(planPeriod)) {
          const historyList: OrX0128Items[] = []
          for (const data of periodHistoryList) {
            const dataPlanPeriod =
              t('label.plan-period') +
              Or52057Const.DEFAULT.STR.SPLIT_COLON +
              data.startYmd +
              Or52057Const.DEFAULT.STR.SPLIT_TILDE +
              data.endYmd
            if (planPeriod === dataPlanPeriod) {
              historyList.push({
                sel: data.sel,
                raiId: data.raiId,
                createYmd: data.createYmd,
                shokuKnj: data.shokuinKnj,
                caseNo: data.caseNo,
                kaiteiKnj: data.kaitei,
                shokuinKnj: data.shokuinKnj,
                id: data.raiId,
                sc1Id: data.sc1Id,
                startYmd: data.startYmd,
                endYmd: data.endYmd,
              } as OrX0128Items)
            }
          }
          if (historyList.length > 0) {
            list.push({
              sc1Id: item.sc1Id,
              startYmd: item.startYmd,
              endYmd: item.endYmd,
              isPeriodManagementMergedRow: true,
              planPeriod:
                t('label.plan-period') +
                Or52057Const.DEFAULT.STR.SPLIT_COLON +
                item.startYmd +
                Or52057Const.DEFAULT.STR.SPLIT_TILDE +
                item.endYmd,
              id: Or52057Const.DEFAULT.STR.EMPTY,
            } as OrX0128Items)
            list = list.concat(historyList)
            tempList.push(planPeriod)
          }
        }
      }
    }
    list.forEach((item, index) => {
      item.id = String(++index)
    })
    orX0128OnewayModel.items = list
    // 画面.利用者一覧明細に親画面.利用者IDが存在する場合
    if (local.assessmentId) {
      // 親画面.アセスメントIDを対するレコードを選択状態にする
      orX0128OnewayModel.initSelectId = (
        list.findIndex((item) => item.raiId === local.assessmentId) + 1
      ).toString()
    }
  } else {
    const list: OrX0128Items[] = []
    for (const data of periodHistoryList) {
      if (data) {
        list.push({
          sel: data.sel,
          raiId: data.raiId,
          createYmd: data.createYmd,
          shokuKnj: data.shokuinKnj,
          caseNo: data.caseNo,
          kaiteiKnj: data.kaitei,
          shokuinKnj: data.shokuinKnj,
          id: data.raiId,
          sc1Id: data.sc1Id,
          startYmd: data.startYmd,
          endYmd: data.endYmd,
        } as OrX0128Items)
      }
    }
    orX0128OnewayModel.items = list
    // 履歴一覧明細に親画面.履歴IDが存在する場合
    if (local.assessmentId) {
      // 画面.履歴一覧明細に親画面.履歴IDを対するレコードを選択状態にする
      orX0128OnewayModel.initSelectId = (
        list.findIndex((item) => item.raiId === local.assessmentId) + 1
      ).toString()
    }
  }
}

// /**
//  * アセスメント
//  *
//  * @param assessmentType - アセスメントタイプ
//  */
// const getAssessmentTypeName = (assessmentType: string): string => {
//   let assessmentTypeName = Or52057Const.DEFAULT.STR.EMPTY
//   if (
//     localOneway.mo00039OneWayAssessmentType.items &&
//     localOneway.mo00039OneWayAssessmentType.items?.length > 0
//   ) {
//     for (const item of localOneway.mo00039OneWayAssessmentType.items) {
//       if (item) {
//         if (item.value === assessmentType) {
//           assessmentTypeName = item.label
//         }
//       }
//     }
//   }
//   return assessmentTypeName
// }

/**
 * 画面印刷設定内容を保存
 */
const save = async (): Promise<IFreeAssessmentFacePrintSettingsUpdateOutEntity> => {
  // バックエンドAPIから印刷設定情報保存
  const inputData: IFreeAssessmentFacePrintSettingsUpdateInEntity = {
    sysRyaku: Or52057Const.DEFAULT.SYS_RYAKU,
    sectionName: local.sectionName,
    gsyscd: systemCommonsStore.getSystemCode,
    shokuId: systemCommonsStore.getStaffId,
    houjinId: systemCommonsStore.getHoujinId,
    shisetuId: local.shisetuId,
    svJigyoId: local.svJigyoId,
    index: local.index,
    sysIniInfo: local.sysIniInfo,
    prtList: localData.data.prtList,
  } as IFreeAssessmentFacePrintSettingsUpdateInEntity

  const resp: IFreeAssessmentFacePrintSettingsUpdateOutEntity = await ScreenRepository.update(
    'freeAssessmentFacePrintSettingsUpdate',
    inputData
  )
  return resp
}

/**
 * 「閉じるボタン」押下
 */
const close = async () => {
  await save()
  setState({
    isOpen: false,
    param: {} as Or52057Param,
  })
}

/**
 * 「PDFダウンロード」ボタン押下
 */
const pdfDownload = async () => {
  // 利用者選択方法が「単一」
  if (Or52057Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
    // 履歴選択が「単一」
    if (Or52057Const.DEFAULT.TANI === mo00039OneWayHistorySelectType.value) {
      orX0117Oneway.type = Or52057Const.DEFAULT.STR.ONE
      //記入用シートを印刷するチェック入れるの場合
      if (mo00018Type.value.modelValue) {
        // 帳票側の処理を呼び出し、帳票レイアウトのみ印刷する
        const inputData: ICpnTucRaiAssReportSelectInEntity = {
          svJigyoKnj: local.svJigyoKnj,
          syscd: systemCommonsStore.getSystemCode,
          printSet: {
            shiTeiKubun: mo00039Type.value,
            shiTeiDate: mo00020Type.value.value ? mo00020Type.value.value.split('/').join('-') : '',
          } as PrintSetEntity,
          printOption: {
            emptyFlg: String(mo00018Type.value.modelValue),
            kinyuAssType: mo00039OneWayAssessmentTypeType.value,
            colorFlg: String(mo00018TypeColor.value.modelValue),
          } as PrintOptionEntity,
          printSubjectHistoryList: [] as PrintSubjectHistoryEntity[],
        } as ICpnTucRaiAssReportSelectInEntity
        // 帳票出力
        await reportOutput(local.reportId, inputData, reportOutputType.DOWNLOAD)
        return
      }
      // 記入用シートを印刷するチェック外すの場合
      else {
        // 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
        if (local.userNoSelect) {
          // メッセージを表示
          const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
            // ダイアログタイトル
            dialogTitle: t('label.confirm'),
            // ダイアログテキスト
            dialogText: t('message.i-cmn-11393'),
            firstBtnType: 'normal1',
            firstBtnLabel: t('btn.yes'),
            secondBtnType: 'blank',
            thirdBtnType: 'blank',
          })
          // はい
          if (dialogResult === Or52057Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
            // 処理終了
            return
          }
        }

        // 履歴一覧が0件選択の場合（※履歴リストが0件、複数件を含む）
        if (local.historyNoSelect) {
          // メッセージを表示
          const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
            // ダイアログタイトル
            dialogTitle: t('label.confirm'),
            // ダイアログテキスト
            dialogText: t('message.i-cmn-11455'),
            firstBtnType: 'normal1',
            firstBtnLabel: t('btn.yes'),
            secondBtnType: 'blank',
            thirdBtnType: 'blank',
          })
          // はい
          if (dialogResult === Or52057Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
            // 処理終了
            return
          }
        }
      }

      // 履歴情報リストにデータを選択する場合
      if (!local.historyNoSelect) {
        // 印刷ダイアログ画面を開かずに、画面.印刷対象履歴リスト「利用者情報+履歴情報+出力帳票対象」を直接に利用して、帳票側の処理を呼び出す
        await reportOutputPdf()
        return
      }
    }
    // 履歴選択方法が「複数」
    else if (Or52057Const.DEFAULT.HUKUSUU === mo00039OneWayHistorySelectType.value) {
      // 履歴一覧が0件選択場合（※履歴リストが0件、複数件を含む）
      if (local.historyNoSelect) {
        // メッセージを表示
        const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
          // ダイアログタイトル
          dialogTitle: t('label.confirm'),
          // ダイアログテキスト
          dialogText: t('message.i-cmn-11455'),
          firstBtnType: 'normal1',
          firstBtnLabel: t('btn.yes'),
          secondBtnType: 'blank',
          thirdBtnType: 'blank',
        })
        // はい
        if (dialogResult === Or52057Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
          // 処理終了
          return
        }
      }
      // 履歴情報リストにデータを選択する場合
      else {
        // 印刷設定情報リストを作成
        createReportOutputData(local.prtNo)
      }
    }
  }

  // 利用者選択方法が「複数」の場合
  if (Or52057Const.DEFAULT.HUKUSUU === mo00039OneWayUserSelectType.value) {
    orX0117Oneway.type = Or52057Const.DEFAULT.STR.ONE
    // 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
    if (local.userNoSelect) {
      // メッセージを表示
      const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
        // ダイアログタイトル
        dialogTitle: t('label.confirm'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-11393'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      })
      // はい
      if (dialogResult === Or52057Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
        // 処理終了
        return
      }
    }
    // 利用者情報リストにデータを選択する場合
    else {
      // 印刷設定情報リストを作成
      await PrintSettingsSubjectSelect()
    }
  }

  // AC019-2と同じ => 画面の印刷設定情報を保存する
  await save()

  // 選択された帳票のプロファイルが””の場合
  if (local.profile === Or52057Const.DEFAULT.STR.EMPTY) {
    // メッセージを表示
    const dialogResult = await openErrorDialog(or21813.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト
      dialogText: t('message.e-cmn-40172'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
    // はい
    if (dialogResult === Or52057Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
      // 処理終了
      return
    }
  }

  // 「AC020-5-2 また AC020-5-3」で取得した印刷設定情報リスト＞0件の場合
  if (orX0117Oneway.historyList.length > 0) {
    // OrX0117のダイアログ開閉状態を更新する
    OrX0117Logic.state.set({
      uniqueCpId: orX0117.value.uniqueCpId,
      state: {
        isOpen: true,
      },
    })
  }
}

/**
 * 印刷ダイアログ画面を開
 */
const reportOutputPdf = async () => {
  try {
    // API処理失敗時のシステムエラーダイアログを非表示にする
    systemCommonsStore.setSystemErrorDialogType('hide')

    const reportData: ICpnTucRaiAssReportSelectInEntity = {
      svJigyoKnj: local.svJigyoKnj,
      syscd: systemCommonsStore.getSystemCode,
      printSet: {
        shiTeiKubun: mo00039Type.value,
        shiTeiDate: mo00020Type.value.value ? mo00020Type.value.value.split('/').join('-') : '',
      } as PrintSetEntity,
      printOption: {
        emptyFlg: String(mo00018Type.value.modelValue),
        kinyuAssType: mo00039OneWayAssessmentTypeType.value,
        colorFlg: String(mo00018TypeColor.value.modelValue),
      } as PrintOptionEntity,
      printSubjectHistoryList: [] as PrintSubjectHistoryEntity[],
    } as ICpnTucRaiAssReportSelectInEntity

    const choPrtList: ChoPrtEntity[] = []
    for (const item of localData.data.prtList) {
      if (item) {
        // 全て帳票
        if (Or52057Const.DEFAULT.STR.ONE === local.prtNo) {
          choPrtList.push({
            shokuId: systemCommonsStore.getStaffId,
            sysRyaku: systemCommonsStore.getSystemAbbreviation,
            section: local.sectionName,
            prtNo: item.prtNo,
            choPro: item.profile,
            sectionName: item.defPrtTitle,
            dwobject: item.dwobject,
            prtOrient: item.prtOrient,
            prtSize: item.prtSize,
            listTitle: item.listTitle,
            prtTitle: item.prtTitle,
            mTop: item.mtop,
            mBottom: item.mbottom,
            mLeft: item.mleft,
            mRight: item.mright,
            ruler: item.ruler,
            prndate: item.prnDate,
            prnshoku: item.prnshoku,
            serialFlg: item.serialFlg,
            modFlg: item.modFlg,
            secFlg: item.secFlg,
            param01: item.param01,
            param02: item.param02,
            param03: item.param03,
            param04: item.param04,
            param05: item.param05,
            param06: item.param06,
            param07: item.param07,
            param08: item.param08,
            param09: item.param09,
            param10: item.param10,
            serialHeight: item.serialHeight,
            serialPagelen: item.serialPagelen,
            hsjId: systemCommonsStore.getHoujinId,
            param11: item.param11,
            param12: item.param12,
            param13: item.param13,
            param14: item.param14,
            param15: item.param15,
            param16: item.param16,
            param17: item.param17,
            param18: item.param18,
            param19: item.param19,
            param20: item.param20,
            param21: item.param21,
            param22: item.param22,
            param23: item.param23,
            param24: item.param24,
            param25: item.param25,
            param26: item.param26,
            param27: item.param28,
            param28: item.param28,
            param29: item.param29,
            param30: item.param30,
            param31: item.param31,
            param32: item.param32,
            param33: item.param33,
            param34: item.param34,
            param35: item.param35,
            param36: item.param36,
            param37: item.param37,
            param38: item.param38,
            param39: item.param39,
            param40: item.param40,
            param41: item.param41,
            param42: item.param42,
            param43: item.param43,
            param44: item.param44,
            param45: item.param45,
            param46: item.param46,
            param47: item.param47,
            param48: item.param48,
            param49: item.param49,
            param50: item.param50,
            houjinId: systemCommonsStore.getHoujinId,
            shisetuId: systemCommonsStore.getShisetuId,
            svJigyoId: systemCommonsStore.getSvJigyoId,
            zoomRate: item.zoomRate,
            modifiedCnt: item.modifiedCnt,
          } as ChoPrtEntity)
        }
        //  単一帳票
        else if (local.prtNo === item.prtNo) {
          choPrtList.push({
            shokuId: systemCommonsStore.getStaffId,
            sysRyaku: systemCommonsStore.getSystemAbbreviation,
            section: local.sectionName,
            prtNo: item.prtNo,
            choPro: item.profile,
            sectionName: item.defPrtTitle,
            dwobject: item.dwobject,
            prtOrient: item.prtOrient,
            prtSize: item.prtSize,
            listTitle: item.listTitle,
            prtTitle: item.prtTitle,
            mTop: item.mtop,
            mBottom: item.mbottom,
            mLeft: item.mleft,
            mRight: item.mright,
            ruler: item.ruler,
            prndate: item.prnDate,
            prnshoku: item.prnshoku,
            serialFlg: item.serialFlg,
            modFlg: item.modFlg,
            secFlg: item.secFlg,
            param01: item.param01,
            param02: item.param02,
            param03: item.param03,
            param04: item.param04,
            param05: item.param05,
            param06: item.param06,
            param07: item.param07,
            param08: item.param08,
            param09: item.param09,
            param10: item.param10,
            serialHeight: item.serialHeight,
            serialPagelen: item.serialPagelen,
            hsjId: systemCommonsStore.getHoujinId,
            param11: item.param11,
            param12: item.param12,
            param13: item.param13,
            param14: item.param14,
            param15: item.param15,
            param16: item.param16,
            param17: item.param17,
            param18: item.param18,
            param19: item.param19,
            param20: item.param20,
            param21: item.param21,
            param22: item.param22,
            param23: item.param23,
            param24: item.param24,
            param25: item.param25,
            param26: item.param26,
            param27: item.param28,
            param28: item.param28,
            param29: item.param29,
            param30: item.param30,
            param31: item.param31,
            param32: item.param32,
            param33: item.param33,
            param34: item.param34,
            param35: item.param35,
            param36: item.param36,
            param37: item.param37,
            param38: item.param38,
            param39: item.param39,
            param40: item.param40,
            param41: item.param41,
            param42: item.param42,
            param43: item.param43,
            param44: item.param44,
            param45: item.param45,
            param46: item.param46,
            param47: item.param47,
            param48: item.param48,
            param49: item.param49,
            param50: item.param50,
            houjinId: systemCommonsStore.getHoujinId,
            shisetuId: systemCommonsStore.getShisetuId,
            svJigyoId: systemCommonsStore.getSvJigyoId,
            zoomRate: item.zoomRate,
            modifiedCnt: item.modifiedCnt,
          } as ChoPrtEntity)
        }
      }
    }

    // 印刷設定情報リストパラメータを作成
    reportData.printSubjectHistoryList.push({
      userId: local.userList.length > 0 ? local.userList[0].userId : Or52057Const.DEFAULT.STR.EMPTY,
      userName:
        local.userList.length > 0 ? local.userList[0].userName : Or52057Const.DEFAULT.STR.EMPTY,
      sc1Id:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].sc1Id as string)
          : Or52057Const.DEFAULT.STR.EMPTY,
      startYmd:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].startYmd as string)
          : Or52057Const.DEFAULT.STR.EMPTY,
      endYmd:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].endYmd as string)
          : Or52057Const.DEFAULT.STR.EMPTY,
      raiId:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].raiId as string)
          : Or52057Const.DEFAULT.STR.EMPTY,
      assType:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].assType as string)
          : Or52057Const.DEFAULT.STR.EMPTY,
      assDateYmd:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].assDateYmd as string)
          : Or52057Const.DEFAULT.STR.EMPTY,
      assShokuId:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].assShokuId as string)
          : Or52057Const.DEFAULT.STR.EMPTY,
      result: Or52057Const.DEFAULT.STR.EMPTY,
      choPrtList: choPrtList,
    } as PrintSubjectHistoryEntity)
    // 帳票出力
    await reportOutput(local.reportId, reportData, reportOutputType.DOWNLOAD)
  } catch (e) {
    $log.debug('帳票の出力に失敗しました。', local.reportId, reportOutputType.DOWNLOAD, e)
  } finally {
    // API処理失敗時のシステムエラーダイアログの表示タイプをデフォルトに戻す
    systemCommonsStore.setSystemErrorDialogType('details')
  }
}

/**
 * 印刷設定情報リストを作成(利用者選択が「複数」、利用者情報リストにデータを選択する場合)
 */
const PrintSettingsSubjectSelect = async () => {
  // 印刷設定情報リストを作成する
  const inputData: IAssessmentInterRAIPackagePlanPrintSettingsSubjectSelectInEntity = {
    prtNo: local.prtNo,
    svJigyoId: local.svJigyoId,
    kijunbi: mo00020TypeKijunbi.value.value ?? Or52057Const.DEFAULT.STR.EMPTY,
    onlySelectHistoryPrt: '',
    userList: local.userList,
    rirekiList: []
  } as IAssessmentInterRAIPackagePlanPrintSettingsSubjectSelectInEntity
  const resp: IAssessmentInterRAIPackagePlanPrintSettingsSubjectSelectOutEntity =
    await ScreenRepository.select(
      'assessmentInterRAIPackagePlanPrintSettingsSubjectSelect',
      inputData
    )

  const list: OrX0117History[] = []
  if (resp.data) {
    for (const data of resp.data.printSubjectHistoryList) {
      if (data) {
        // 利用者複数の場合
        if (Or52057Const.DEFAULT.HUKUSUU === mo00039OneWayUserSelectType.value) {
          // 印刷設定情報リストを作成
          const reportData: ICpnTucRaiAssReportSelectInEntity = {
            svJigyoKnj: local.svJigyoKnj,
            syscd: systemCommonsStore.getSystemCode,
            printSet: {
              shiTeiKubun: mo00039Type.value,
              shiTeiDate: mo00020Type.value.value
                ? mo00020Type.value.value.split('/').join('-')
                : '',
            } as PrintSetEntity,
            printOption: {
              emptyFlg: String(mo00018Type.value.modelValue),
              kinyuAssType: mo00039OneWayAssessmentTypeType.value,
              colorFlg: String(mo00018TypeColor.value.modelValue),
            } as PrintOptionEntity,
            printSubjectHistoryList: [] as PrintSubjectHistoryEntity[],
          } as ICpnTucRaiAssReportSelectInEntity

          const choPrtList: ChoPrtEntity[] = []
          for (const item of localData.data.prtList) {
            if (item) {
              // 全て帳票
              if (Or52057Const.DEFAULT.STR.ONE === local.prtNo) {
                choPrtList.push({
                  shokuId: systemCommonsStore.getStaffId,
                  sysRyaku: systemCommonsStore.getSystemAbbreviation,
                  section: local.sectionName,
                  prtNo: item.prtNo,
                  choPro: item.profile,
                  sectionName: item.defPrtTitle,
                  dwobject: item.dwobject,
                  prtOrient: item.prtOrient,
                  prtSize: item.prtSize,
                  listTitle: item.listTitle,
                  prtTitle: item.prtTitle,
                  mTop: item.mtop,
                  mBottom: item.mbottom,
                  mLeft: item.mleft,
                  mRight: item.mright,
                  ruler: item.ruler,
                  prndate: item.prnDate,
                  prnshoku: item.prnshoku,
                  serialFlg: item.serialFlg,
                  modFlg: item.modFlg,
                  secFlg: item.secFlg,
                  param01: item.param01,
                  param02: item.param02,
                  param03: item.param03,
                  param04: item.param04,
                  param05: item.param05,
                  param06: item.param06,
                  param07: item.param07,
                  param08: item.param08,
                  param09: item.param09,
                  param10: item.param10,
                  serialHeight: item.serialHeight,
                  serialPagelen: item.serialPagelen,
                  hsjId: systemCommonsStore.getHoujinId,
                  param11: item.param11,
                  param12: item.param12,
                  param13: item.param13,
                  param14: item.param14,
                  param15: item.param15,
                  param16: item.param16,
                  param17: item.param17,
                  param18: item.param18,
                  param19: item.param19,
                  param20: item.param20,
                  param21: item.param21,
                  param22: item.param22,
                  param23: item.param23,
                  param24: item.param24,
                  param25: item.param25,
                  param26: item.param26,
                  param27: item.param28,
                  param28: item.param28,
                  param29: item.param29,
                  param30: item.param30,
                  param31: item.param31,
                  param32: item.param32,
                  param33: item.param33,
                  param34: item.param34,
                  param35: item.param35,
                  param36: item.param36,
                  param37: item.param37,
                  param38: item.param38,
                  param39: item.param39,
                  param40: item.param40,
                  param41: item.param41,
                  param42: item.param42,
                  param43: item.param43,
                  param44: item.param44,
                  param45: item.param45,
                  param46: item.param46,
                  param47: item.param47,
                  param48: item.param48,
                  param49: item.param49,
                  param50: item.param50,
                  houjinId: systemCommonsStore.getHoujinId,
                  shisetuId: systemCommonsStore.getShisetuId,
                  svJigyoId: systemCommonsStore.getSvJigyoId,
                  zoomRate: item.zoomRate,
                  modifiedCnt: item.modifiedCnt,
                } as ChoPrtEntity)
              }
              //  単一帳票
              else if (local.prtNo === item.prtNo) {
                choPrtList.push({
                  shokuId: systemCommonsStore.getStaffId,
                  sysRyaku: systemCommonsStore.getSystemAbbreviation,
                  section: local.sectionName,
                  prtNo: item.prtNo,
                  choPro: item.profile,
                  sectionName: item.defPrtTitle,
                  dwobject: item.dwobject,
                  prtOrient: item.prtOrient,
                  prtSize: item.prtSize,
                  listTitle: item.listTitle,
                  prtTitle: item.prtTitle,
                  mTop: item.mtop,
                  mBottom: item.mbottom,
                  mLeft: item.mleft,
                  mRight: item.mright,
                  ruler: item.ruler,
                  prndate: item.prnDate,
                  prnshoku: item.prnshoku,
                  serialFlg: item.serialFlg,
                  modFlg: item.modFlg,
                  secFlg: item.secFlg,
                  param01: item.param01,
                  param02: item.param02,
                  param03: item.param03,
                  param04: item.param04,
                  param05: item.param05,
                  param06: item.param06,
                  param07: item.param07,
                  param08: item.param08,
                  param09: item.param09,
                  param10: item.param10,
                  serialHeight: item.serialHeight,
                  serialPagelen: item.serialPagelen,
                  hsjId: systemCommonsStore.getHoujinId,
                  param11: item.param11,
                  param12: item.param12,
                  param13: item.param13,
                  param14: item.param14,
                  param15: item.param15,
                  param16: item.param16,
                  param17: item.param17,
                  param18: item.param18,
                  param19: item.param19,
                  param20: item.param20,
                  param21: item.param21,
                  param22: item.param22,
                  param23: item.param23,
                  param24: item.param24,
                  param25: item.param25,
                  param26: item.param26,
                  param27: item.param28,
                  param28: item.param28,
                  param29: item.param29,
                  param30: item.param30,
                  param31: item.param31,
                  param32: item.param32,
                  param33: item.param33,
                  param34: item.param34,
                  param35: item.param35,
                  param36: item.param36,
                  param37: item.param37,
                  param38: item.param38,
                  param39: item.param39,
                  param40: item.param40,
                  param41: item.param41,
                  param42: item.param42,
                  param43: item.param43,
                  param44: item.param44,
                  param45: item.param45,
                  param46: item.param46,
                  param47: item.param47,
                  param48: item.param48,
                  param49: item.param49,
                  param50: item.param50,
                  houjinId: systemCommonsStore.getHoujinId,
                  shisetuId: systemCommonsStore.getShisetuId,
                  svJigyoId: systemCommonsStore.getSvJigyoId,
                  zoomRate: item.zoomRate,
                  modifiedCnt: item.modifiedCnt,
                } as ChoPrtEntity)
              }
            }
          }
          reportData.printSubjectHistoryList.push({
            userId: data.userId,
            userName: data.userName,
            sc1Id: '',
            startYmd: '',
            endYmd: '',
            raiId: '',
            assType: '',
            assDateYmd: '',
            assShokuId: '',
            result: data.result,
            choPrtList: choPrtList,
          } as PrintSubjectHistoryEntity)
          list.push({
            reportId: local.reportId,
            outputType: reportOutputType.DOWNLOAD,
            reportData: reportData,
            userName: data.userName,
            historyDate: '',
            result: data.result,
          } as OrX0117History)
        }
      }
    }
  }
  orX0117Oneway.historyList = list
}

/**
 * 切替前の印刷設定を保存する
 *
 * @param selectId - 出力帳票ID
 */
const setBeforChangePrintData = (selectId: string) => {
  if (selectId) {
    for (const item of localData.data.prtList) {
      if (item) {
        if (selectId === item.prtNo) {
          // 日付表示有無
          item.prnDate = mo00039Type.value
          break
        }
      }
    }
  }
}

/**
 * 切替後の印刷設定を画面に設定する
 *
 * @param selectId - 出力帳票ID
 */
const setAfterChangePrintData = (selectId: string) => {
  if (selectId) {
    for (const item of localData.data.prtList) {
      if (item) {
        if (selectId === item.prtNo) {
          // 日付表示有無
          mo00039Type.value = item.prnDate
          break
        }
      }
    }
  }
}

/**
 * 「出力帳票名」選択
 *
 * @param selectId - 出力帳票ID
 */
const outputLedgerName = (selectId: string) => {
  if (!selectId) {
    selectId = Or52057Const.DEFAULT.SECTION_NO
  }
  let label = Or52057Const.DEFAULT.STR.EMPTY
  for (const item of mo01334Oneway.value.items) {
    if (item) {
      if (selectId === item.id && item.mo01337OnewayLedgerName) {
        const data = item.mo01337OnewayLedgerName as Mo01337OnewayType
        label = data.value
        mo01334Type.value.value = item.id

        // プロファイル
        local.profile = item.profile as string

        // 出力帳票名一覧に選択行番号
        local.index = item.index as string

        // 帳票番号
        local.prtNo = item.prtNo as string

        // 帳票ID
        setReportId(item.id)
        break
      }
    }
  }
  localOneway.mo01338OneWay.value = label

  // 画面PDFダウンロードボタン活性非活性設定
  pdfDownloadBtnSetting(selectId)

  // 帳票イニシャライズデータを取得する
  void getSectionInitializeData()
}

/**
 * 帳票ID設定
 *
 * @param prtNo - 帳票番号
 */
const setReportId = (prtNo: string) => {
  switch (prtNo) {
    case Or52057Const.DEFAULT.STR.ONE:
      local.reportId = Or52057Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.A
      break
    case Or52057Const.DEFAULT.STR.TWO:
      local.reportId = Or52057Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.B
      break
    case Or52057Const.DEFAULT.STR.THREE:
      local.reportId = Or52057Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.C
      break
    case Or52057Const.DEFAULT.STR.FOUR:
      local.reportId = Or52057Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.D
      break
    case Or52057Const.DEFAULT.STR.FIVE:
      local.reportId = Or52057Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.E
      break
    case Or52057Const.DEFAULT.STR.SIX:
      local.reportId = Or52057Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.E
      break
    default:
      local.reportId = Or52057Const.DEFAULT.STR.EMPTY
      break
  }
}

/**
 * 印刷設定情報リストを作成
 *
 * @param prtNo - 帳票番号
 */
const createReportOutputData = (prtNo: string) => {
  const list: OrX0117History[] = []
  for (const orX0128DetData of local.orX0128DetList) {
    const reportData: ICpnTucRaiAssReportSelectInEntity = {
      svJigyoKnj: local.svJigyoKnj,
      syscd: systemCommonsStore.getSystemCode,
      printSet: {
        shiTeiKubun: mo00039Type.value,
        shiTeiDate: mo00020Type.value.value ? mo00020Type.value.value.split('/').join('-') : '',
      } as PrintSetEntity,
      printOption: {
        emptyFlg: String(mo00018Type.value.modelValue),
        kinyuAssType: mo00039OneWayAssessmentTypeType.value,
        colorFlg: String(mo00018TypeColor.value.modelValue),
      } as PrintOptionEntity,
      printSubjectHistoryList: [] as PrintSubjectHistoryEntity[],
    } as ICpnTucRaiAssReportSelectInEntity

    const choPrtList: ChoPrtEntity[] = []
    for (const item of localData.data.prtList) {
      if (item) {
        // 全て帳票
        if (Or52057Const.DEFAULT.STR.ONE === prtNo) {
          choPrtList.push({
            shokuId: systemCommonsStore.getStaffId,
            sysRyaku: systemCommonsStore.getSystemAbbreviation,
            section: local.sectionName,
            prtNo: item.prtNo,
            choPro: item.profile,
            sectionName: item.defPrtTitle,
            dwobject: item.dwobject,
            prtOrient: item.prtOrient,
            prtSize: item.prtSize,
            listTitle: item.listTitle,
            prtTitle: item.prtTitle,
            mTop: item.mtop,
            mBottom: item.mbottom,
            mLeft: item.mleft,
            mRight: item.mright,
            ruler: item.ruler,
            prndate: item.prnDate,
            prnshoku: item.prnshoku,
            serialFlg: item.serialFlg,
            modFlg: item.modFlg,
            secFlg: item.secFlg,
            param01: item.param01,
            param02: item.param02,
            param03: item.param03,
            param04: item.param04,
            param05: item.param05,
            param06: item.param06,
            param07: item.param07,
            param08: item.param08,
            param09: item.param09,
            param10: item.param10,
            serialHeight: item.serialHeight,
            serialPagelen: item.serialPagelen,
            hsjId: systemCommonsStore.getHoujinId,
            param11: item.param11,
            param12: item.param12,
            param13: item.param13,
            param14: item.param14,
            param15: item.param15,
            param16: item.param16,
            param17: item.param17,
            param18: item.param18,
            param19: item.param19,
            param20: item.param20,
            param21: item.param21,
            param22: item.param22,
            param23: item.param23,
            param24: item.param24,
            param25: item.param25,
            param26: item.param26,
            param27: item.param28,
            param28: item.param28,
            param29: item.param29,
            param30: item.param30,
            param31: item.param31,
            param32: item.param32,
            param33: item.param33,
            param34: item.param34,
            param35: item.param35,
            param36: item.param36,
            param37: item.param37,
            param38: item.param38,
            param39: item.param39,
            param40: item.param40,
            param41: item.param41,
            param42: item.param42,
            param43: item.param43,
            param44: item.param44,
            param45: item.param45,
            param46: item.param46,
            param47: item.param47,
            param48: item.param48,
            param49: item.param49,
            param50: item.param50,
            houjinId: systemCommonsStore.getHoujinId,
            shisetuId: systemCommonsStore.getShisetuId,
            svJigyoId: systemCommonsStore.getSvJigyoId,
            zoomRate: item.zoomRate,
            modifiedCnt: item.modifiedCnt,
          } as ChoPrtEntity)
        }
        //  単一帳票
        else if (prtNo === item.prtNo) {
          choPrtList.push({
            shokuId: systemCommonsStore.getStaffId,
            sysRyaku: systemCommonsStore.getSystemAbbreviation,
            section: local.sectionName,
            prtNo: item.prtNo,
            choPro: item.profile,
            sectionName: item.defPrtTitle,
            dwobject: item.dwobject,
            prtOrient: item.prtOrient,
            prtSize: item.prtSize,
            listTitle: item.listTitle,
            prtTitle: item.prtTitle,
            mTop: item.mtop,
            mBottom: item.mbottom,
            mLeft: item.mleft,
            mRight: item.mright,
            ruler: item.ruler,
            prndate: item.prnDate,
            prnshoku: item.prnshoku,
            serialFlg: item.serialFlg,
            modFlg: item.modFlg,
            secFlg: item.secFlg,
            param01: item.param01,
            param02: item.param02,
            param03: item.param03,
            param04: item.param04,
            param05: item.param05,
            param06: item.param06,
            param07: item.param07,
            param08: item.param08,
            param09: item.param09,
            param10: item.param10,
            serialHeight: item.serialHeight,
            serialPagelen: item.serialPagelen,
            hsjId: systemCommonsStore.getHoujinId,
            param11: item.param11,
            param12: item.param12,
            param13: item.param13,
            param14: item.param14,
            param15: item.param15,
            param16: item.param16,
            param17: item.param17,
            param18: item.param18,
            param19: item.param19,
            param20: item.param20,
            param21: item.param21,
            param22: item.param22,
            param23: item.param23,
            param24: item.param24,
            param25: item.param25,
            param26: item.param26,
            param27: item.param28,
            param28: item.param28,
            param29: item.param29,
            param30: item.param30,
            param31: item.param31,
            param32: item.param32,
            param33: item.param33,
            param34: item.param34,
            param35: item.param35,
            param36: item.param36,
            param37: item.param37,
            param38: item.param38,
            param39: item.param39,
            param40: item.param40,
            param41: item.param41,
            param42: item.param42,
            param43: item.param43,
            param44: item.param44,
            param45: item.param45,
            param46: item.param46,
            param47: item.param47,
            param48: item.param48,
            param49: item.param49,
            param50: item.param50,
            houjinId: systemCommonsStore.getHoujinId,
            shisetuId: systemCommonsStore.getShisetuId,
            svJigyoId: systemCommonsStore.getSvJigyoId,
            zoomRate: item.zoomRate,
            modifiedCnt: item.modifiedCnt,
          } as ChoPrtEntity)
        }
      }
    }

    // 印刷設定情報リストパラメータを作成
    reportData.printSubjectHistoryList.push({
      userId: local.userList.length > 0 ? local.userList[0].userId : Or52057Const.DEFAULT.STR.EMPTY,
      userName:
        local.userList.length > 0 ? local.userList[0].userName : Or52057Const.DEFAULT.STR.EMPTY,
      sc1Id: orX0128DetData.sc1Id as string,
      startYmd: orX0128DetData.startYmd as string,
      endYmd: orX0128DetData.endYmd as string,
      raiId: orX0128DetData.raiId as string,
      assType: orX0128DetData.assType as string,
      assDateYmd: orX0128DetData.assDateYmd as string,
      assShokuId: orX0128DetData.assShokuId as string,
      result: Or52057Const.DEFAULT.STR.EMPTY,
      choPrtList: choPrtList,
    } as PrintSubjectHistoryEntity)
    list.push({
      reportId: local.reportId,
      outputType: reportOutputType.DOWNLOAD,
      reportData: reportData,
      userName:
        local.userList.length > 0 ? local.userList[0].userName : Or52057Const.DEFAULT.STR.EMPTY,
      historyDate: orX0128DetData.assDateYmd as string,
      result: Or52057Const.DEFAULT.STR.EMPTY,
    } as OrX0117History)
  }
  orX0117Oneway.historyList = list
}

/**
 * 画面PDFダウンロードボタン活性非活性設定
 *
 * @param selectId - 出力帳票ID
 */
const pdfDownloadBtnSetting = (selectId: string) => {
  // 記入用シートを印刷するチェックボックスがチェックオンの場合
  if (mo00018Type.value.modelValue) {
    // 居宅版が選択の場合、アセスメント表（R）選択の場合
    if (
      mo00039OneWayAssessmentTypeType.value === Or52057Const.SURVEY_ASSESSMENT_KIND.HOME &&
      selectId === Or52057Const.DEFAULT.STR.NINETEEN
    ) {
      // PDFダウンロードボタンが非活性
      localOneway.mo00609Oneway.disabled = true
    }
    // 施設版が選択の場合、アセスメント表（Q,S,T）選択の場合
    else if (
      mo00039OneWayAssessmentTypeType.value === Or52057Const.SURVEY_ASSESSMENT_KIND.FACILITY &&
      (selectId === Or52057Const.DEFAULT.STR.NINETEEN ||
        selectId === Or52057Const.DEFAULT.STR.TEENTY ||
        selectId === Or52057Const.DEFAULT.STR.TWENTI_TWO)
    ) {
      // PDFダウンロードボタンが非活性
      localOneway.mo00609Oneway.disabled = true
    }
    // 高齢者住宅版が選択の場合、アセスメント表（Q,R,S,T）選択の場合
    else if (
      mo00039OneWayAssessmentTypeType.value ===
        Or52057Const.SURVEY_ASSESSMENT_KIND.SENIOR_HOUSING &&
      (selectId === Or52057Const.DEFAULT.STR.NINETEEN ||
        selectId === Or52057Const.DEFAULT.STR.TEENTY ||
        selectId === Or52057Const.DEFAULT.STR.TEENTY ||
        selectId === Or52057Const.DEFAULT.STR.TWENTI_TWO)
    ) {
      // PDFダウンロードボタンが非活性
      localOneway.mo00609Oneway.disabled = true
    } else {
      // PDFダウンロードボタンが活性
      localOneway.mo00609Oneway.disabled = false
    }
  }
  // 履歴･利用者共に単一の場合
  else if (
    mo00039OneWayUserSelectType.value === Or52057Const.DEFAULT.TANI &&
    mo00039OneWayHistorySelectType.value === Or52057Const.DEFAULT.TANI
  ) {
    // 居宅版が選択の場合、アセスメント表（R）選択の場合
    if (
      mo00039OneWayAssessmentTypeType.value === Or52057Const.SURVEY_ASSESSMENT_KIND.HOME &&
      selectId === Or52057Const.DEFAULT.STR.TEENTY
    ) {
      // PDFダウンロードボタンが非活性
      localOneway.mo00609Oneway.disabled = true
    }
    // 施設版が選択の場合、アセスメント表（Q,S,T）選択の場合
    else if (
      mo00039OneWayAssessmentTypeType.value === Or52057Const.SURVEY_ASSESSMENT_KIND.FACILITY &&
      (selectId === Or52057Const.DEFAULT.STR.NINETEEN ||
        selectId === Or52057Const.DEFAULT.STR.TEENTY ||
        selectId === Or52057Const.DEFAULT.STR.TWENTI_TWO)
    ) {
      // PDFダウンロードボタンが非活性
      localOneway.mo00609Oneway.disabled = true
    }
    // 高齢者住宅版が選択の場合、アセスメント表（Q,R,S,T）選択の場合
    else if (
      mo00039OneWayAssessmentTypeType.value ===
        Or52057Const.SURVEY_ASSESSMENT_KIND.SENIOR_HOUSING &&
      (selectId === Or52057Const.DEFAULT.STR.NINETEEN ||
        selectId === Or52057Const.DEFAULT.STR.TEENTY ||
        selectId === Or52057Const.DEFAULT.STR.TEENTY ||
        selectId === Or52057Const.DEFAULT.STR.TWENTI_TWO)
    ) {
      // PDFダウンロードボタンが非活性
      localOneway.mo00609Oneway.disabled = true
    } else {
      // PDFダウンロードボタンが活性
      localOneway.mo00609Oneway.disabled = false
    }
  }
  // 上記以外の場合
  else {
    // PDFダウンロードボタンが活性
    localOneway.mo00609Oneway.disabled = false
  }
}

/**
 * 帳票イニシャライズデータを取得する
 */
const getSectionInitializeData = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity = {
    profile: local.profile,
    gsyscd: systemCommonsStore.getSystemCode,
    shokuId: systemCommonsStore.getStaffId,
    kojinhogoUsedFlg: Or52057Const.DEFAULT.KOJINHOGO_USED_FLG,
    sectionAddNo: Or52057Const.DEFAULT.SECTION_ADD_NO,
  } as IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity
  const resp: IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateOutEntity =
    await ScreenRepository.update('freeAssessmentFacePrintSettingsPrtNoChangeUpdate', inputData)
  if (resp.data) {
    local.sysIniInfo = resp.data.sysIniInfo
  }
}

/**
 * 印刷設定履歴リスト取得
 */
const getPrintSettingsHistoryList = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: AssessmentInterRAIPackagePlanPrintSettingsHistorySelectInEntity = {
    svJigyoId: local.svJigyoId,
    userId: local.selectUserId,
    kikanFlg: kikanFlag.value,
  } as AssessmentInterRAIPackagePlanPrintSettingsHistorySelectInEntity
  const resp: AssessmentInterRAIPackagePlanPrintSettingsHistorySelectOutEntity =
    await ScreenRepository.select(
      'assessmentInterRAIPackagePlanPrintSettingsHistorySelect',
      inputData
    )
  if (resp.data) {
    // アセスメント履歴一覧データ
    getHistoryData(resp.data.periodHistoryList)
  }
}

/**
 * 「印刷時に色をつける」クリック
 */
const printColorCheckBoxCliick = () => {
  // 画面.帳票番号は1のパラメータ05＝画面.印刷時に色をつける（2：Off、1：On）
  for (const item of localData.data.prtList) {
    if (item) {
      if (item.prtNo === Or52057Const.DEFAULT.STR.ONE) {
        // On
        if (mo00018TypeColor.value) {
          item.param05 = Or52057Const.DEFAULT.TYPE_COLOR.ON
        }
        // Off
        else {
          item.param05 = Or52057Const.DEFAULT.TYPE_COLOR.OFF
        }
        break
      }
    }
  }
}

/**
 * 画面ボタン活性非活性設定
 */
const btnItemSetting = () => {
  // 利用者選択方法が「単一」の場合
  if (Or52057Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
    // 基準日を非表示にする
    // 履歴選択を活性表示にする
    kijunbiFlag.value = false

    // 履歴選択方法が「単一」の場合
    if (Or52057Const.DEFAULT.TANI === mo00039OneWayHistorySelectType.value) {
      // 記入用シートを印刷するチェックボックス表示
      localOneway.mo00018OneWay.disabled = false
    }
    // 以外の場合
    else {
      // チェックオフとなって
      mo00018Type.value.modelValue = false
      // 記入用シートを印刷するを非活性表示にする
      localOneway.mo00018OneWay.disabled = true
    }

    // 記入用シートを印刷するが「チェックオンの場合
    if (mo00018Type.value.modelValue) {
      localOneway.mo00039OneWayAssessmentType.disabled = false
    }
    // 以外の場合
    else {
      localOneway.mo00039OneWayAssessmentType.disabled = true
    }
  }
  // 以外の場合
  else {
    // 基準日を活性表示にする
    // 履歴選択を非表示にする
    kijunbiFlag.value = true

    // チェックオフとなって
    mo00018Type.value.modelValue = false
    // 記入用シートを印刷するを非活性表示にする
    localOneway.mo00018OneWay.disabled = true
    localOneway.mo00039OneWayAssessmentType.disabled = true
  }

  // 履歴選択方法が「単一」の場合
  if (Or52057Const.DEFAULT.TANI === mo00039OneWayHistorySelectType.value) {
    // 利用者選択方法が「単一」の場合
    if (Or52057Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
      // 記入用シートを印刷するチェックボックス表示
      localOneway.mo00018OneWay.disabled = false
    }
    // 以外の場合
    else {
      // チェックオフとなって
      mo00018Type.value.modelValue = false
      // 記入用シートを印刷するを非活性表示にする
      localOneway.mo00018OneWay.disabled = true
    }
  }
  // 以外の場合
  else {
    // チェックオフとなって
    mo00018Type.value.modelValue = false
    // 記入用シートを印刷するを非活性表示にする
    localOneway.mo00018OneWay.disabled = true

    // 記入用シートを印刷するをチェックオフにする
    mo00018Type.value.modelValue = false
    // 記入用シート方式を非活性表示にする
    localOneway.mo00039OneWayAssessmentType.disabled = true
  }

  // 履歴一覧セクション
  if (Or52057Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
    userCols.value = 4
    mo01334TypeHistoryFlag.value = true
  } else {
    userCols.value = 12
    mo01334TypeHistoryFlag.value = false
  }


  // 履歴情報を印刷する（基準日、作成者）
  if (Or52057Const.DEFAULT.STR.ONE === mo01334Type.value.value) {
    localOneway.mo00018OneWayHistoryInfo.disabled = true
  } else {
    localOneway.mo00018OneWayHistoryInfo.disabled = false
  }
  // 選択項目を囲む色
  if (Or52057Const.DEFAULT.STR.FIVE === mo01334Type.value.value) {
    localOneway.mo00018OneWaySelectionHistory.disabled = true
    localOneway.mo00040OneWay.readonly = true
  } else {
    localOneway.mo00018OneWaySelectionHistory.disabled = false
    localOneway.mo00040OneWay.readonly = false
  }
  // 説明文を印刷するチェックボックス
  if (
    Or52057Const.DEFAULT.STR.FIVE === mo01334Type.value.value ||
    Or52057Const.DEFAULT.STR.SIX === mo01334Type.value.value
  ) {
    localOneway.mo00018OneWayTypePrintCheck.disabled = true
  } else {
    localOneway.mo00018OneWayTypePrintCheck.disabled = false
  }

  // 担当ケアマネ選択アイコンボタン非活性/活性設定
  const kkjTantoFlg: string =
    cmnRouteCom.getInitialSettingMaster()?.kkjTantoFlg ?? Or52057Const.DEFAULT.STR.EMPTY
  // 共通情報.担当ケアマネ設定フラグ > 0、且つ、共通情報.担当者IDが0以外の場合
  if (
    systemCommonsStore.getManagerId !== Or52057Const.DEFAULT.STR.ZERO &&
    parseInt(kkjTantoFlg) > Or52057Const.DEFAULT.NUMBER.ZERO
  ) {
    // 非活性
    localOneway.orX0145Oneway.disabled = true
  }
  // その他場合
  else {
    // 活性
    localOneway.orX0145Oneway.disabled = false
  }
}

/**
 * 担当ケアマネプルダウン
 *
 * @param result - 戻り値
 */
const orx0145UpdateModelValue = (result: OrX0145Type) => {
  if (result) {
    if (result.value) {
      if (!Array.isArray(result.value) && 'chkShokuId' in result.value) {
        // TODO API疎通時に確認
        orX0130Oneway.tantouCareManager = result.value.chkShokuId
      }
    }
  }
}

/**
 * 確認ダイアログ表示
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openConfirmDialog = async (
  uniqueCpId: string,
  state: Or21814OnewayType
): Promise<Or52057MsgBtnType> => {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result: Or52057MsgBtnType = Or52057Const.DEFAULT.MESSAGE_BTN_TYPE_YES

        if (event?.firstBtnClickFlg) {
          result = Or52057Const.DEFAULT.MESSAGE_BTN_TYPE_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or52057Const.DEFAULT.MESSAGE_BTN_TYPE_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = Or52057Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL
        }
        if (event?.closeBtnClickFlg) {
          result = Or52057Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * エラーダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openErrorDialog = async (
  uniqueCpId: string,
  state: Or21813StateType
): Promise<Or52057MsgBtnType> => {
  Or21813Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(uniqueCpId)

        let result = Or52057Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL as Or52057MsgBtnType

        if (event?.firstBtnClickFlg) {
          result = Or52057Const.DEFAULT.MESSAGE_BTN_TYPE_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or52057Const.DEFAULT.MESSAGE_BTN_TYPE_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = Or52057Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL
        }
        if (event?.closeBtnClickFlg) {
          result = Or52057Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL
        }

        // エラーダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 「出力帳票名」選択
 */
watch(
  () => mo01334Type.value.value,
  (newValue, oldValue) => {
    setBeforChangePrintData(oldValue)
    setAfterChangePrintData(newValue)
    outputLedgerName(newValue)
   btnItemSetting()
    // 日付印刷区分が2の場合
    if (Or52057Const.DEFAULT.STR.TWO === mo00039Type.value) {
      // 指定日を活性表示にする。
      mo00020Flag.value = true
    } else {
      // 指定日を非表示にする。
      mo00020Flag.value = false
    }
  }
)

/**
 * 「日付印刷区分」ラジオボタン押下
 */
watch(
  () => mo00039Type.value,
  (newValue) => {
    if (Or52057Const.DEFAULT.STR.TWO === newValue) {
      // 指定日を活性表示にする
      mo00020Flag.value = true
    } else {
      // 指定日を非表示にする
      mo00020Flag.value = false
    }
  }
)

/**
 * 「利用者選択方法」ラジオボタン選択
 */
watch(
  () => mo00039OneWayUserSelectType.value,
  (newValue) => {
    // 画面ボタン活性非活性設定
    btnItemSetting()

    // 利用者選択方法が「単一」の場合
    if (Or52057Const.DEFAULT.TANI === newValue) {
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.TANI
      orX0130Oneway.tableStyle = 'width: 210px'

      if (OrX0130Logic.event.get(orX0130.value.uniqueCpId)) {
        if (
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList &&
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList.length > 0
        ) {
          local.userList = []
          for (const item of OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList) {
            if (item) {
              local.userList.push({
                userId: item.userId,
                userName: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
              } as UserlistData)
            }
          }
          local.userNoSelect = false
        } else {
          local.userList = []
          local.userNoSelect = true
        }
      } else {
        local.userList = []
        local.userNoSelect = true
      }

      // 利用者一覧明細に親画面.利用者IDが存在する場合
      if (local.userId) {
        // 利用者IDを対するレコードを選択状態にする
        orX0130Oneway.userId = local.userId
      }
    } else {
      // 復元
      orX0117Oneway.type = Or52057Const.DEFAULT.STR.ONE
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.HUKUSUU
      orX0130Oneway.tableStyle = 'width: 430px'

      if (OrX0130Logic.event.get(orX0130.value.uniqueCpId)) {
        if (
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList &&
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList.length > 0
        ) {
          local.userList = []
          for (const item of OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList) {
            if (item) {
              local.userList.push({
                userId: item.userId,
                userName: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
              } as UserlistData)
            }
          }
          local.userNoSelect = false
        } else {
          local.userList = []
          local.userNoSelect = true
        }
      } else {
        local.userList = []
        local.userNoSelect = true
      }
    }
    // 画面PDFダウンロードボタン活性非活性設定
    pdfDownloadBtnSetting(local.prtNo)
  }
)

/**
 * 「履歴選択方法」ラジオボタン押下
 */
watch(
  () => mo00039OneWayHistorySelectType.value,
  (newValue) => {
    // 画面ボタン活性非活性設定
    btnItemSetting()

    if (OrX0128Logic.event.get(orX0128.value.uniqueCpId)) {
      if (
        OrX0128Logic.event.get(orX0128.value.uniqueCpId)!.orX0128DetList &&
        OrX0128Logic.event.get(orX0128.value.uniqueCpId)!.orX0128DetList.length > 0
      ) {
        local.historyNoSelect = false
      } else {
        local.historyNoSelect = true
      }
    } else {
      local.historyNoSelect = true
    }

    // 履歴選択方法が「単一」の場合
    if (Or52057Const.DEFAULT.TANI === newValue) {
      orX0128OnewayModel.singleFlg = OrX0128Const.DEFAULT.TANI
    } else {
      orX0128OnewayModel.singleFlg = OrX0128Const.DEFAULT.HUKUSUU
      orX0117Oneway.type = Or52057Const.DEFAULT.STR.ZERO
    }
  }
)

/**
 * 記入用シート方式ラジオボタン監視
 */
watch(
  () => mo00018Type.value.modelValue,
  () => {
    // 画面PDFダウンロードボタン活性非活性設定
    pdfDownloadBtnSetting(local.prtNo)

    // 画面ボタン活性非活性設定
    btnItemSetting()
  }
)

/**
 * 「履歴選択」の監視
 */
watch(
  () => OrX0128Logic.event.get(orX0128.value.uniqueCpId),
  (newValue) => {
    // 画面ボタン活性非活性設定
    btnItemSetting()
    if (newValue) {
      if (newValue.historyDetClickFlg) {
        local.orX0128DetList = newValue.orX0128DetList
        if (newValue.orX0128DetList.length > 0) {
          local.historyNoSelect = false

          for (const item of newValue.orX0128DetList) {
            if (item) {
              // 記入用シート方式ラジオボタン(デフォルト値の設定)
              if (!mo00039OneWayAssessmentTypeType.value) {
                mo00039OneWayAssessmentTypeType.value = item.assType as string
              }
            }
          }
        } else {
          local.historyNoSelect = true
        }
      } else {
        local.historyNoSelect = true
      }
    }
  }
)

/**
 * 「利用者選択」の監視
 */
watch(
  () => OrX0130Logic.event.get(orX0130.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.clickFlg) {
      if (newValue.userList.length > 0) {
        local.userNoSelect = false
        local.selectUserId = newValue.userList[0].userId

        local.userList = []
        for (const item of newValue.userList) {
          if (item) {
            local.userList.push({
              userId: item.userId,
              userName: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
            } as UserlistData)
          }
        }

        // 利用者選択方法が「単一」の場合
        if (Or52057Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
          if (initFlag.value) {
            // アセスメント履歴情報を取得する
            await getPrintSettingsHistoryList()
          }
        }
        // 利用者選択方法が「複数」の場合
        else if (Or52057Const.DEFAULT.HUKUSUU === mo00039OneWayUserSelectType.value) {
          // 利用者一覧明細に前回選択された利用者が選択状態になる
          createReportOutputData(local.prtNo)
        }
      } else {
        local.userNoSelect = true
        local.userList = []
      }
    } else {
      local.userNoSelect = true
      local.userList = []
    }
  }
)

/**
 * イベントリスナーの解除
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    if (!newValue) {
      await save()
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        class="or52057_row"
        no-gutter
      >
        <c-v-col
          cols="12"
          sm="2"
          class="table-header"
        >
          <base-mo-01334
            v-model="mo01334Type"
            :oneway-model-value="mo01334Oneway"
            class="list-wrapper"
          >
            <!-- 帳票 -->
            <template #[`item.ledgerName`]="{ item }">
              <!-- 分子：一覧専用ラベル（文字列型） -->
              <base-mo01337 :oneway-model-value="item.mo01337OnewayLedgerName" />
            </template>
            <!-- ページングを非表示 -->
            <template #bottom />
          </base-mo-01334>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="3"
          class="content_center"
        >
          <c-v-row
            no-gutter
            class="printerOption customCol or52057_row"
          >
            <c-v-col
              cols="12"
              sm="12"
            >
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayBasicSettings"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>

          <!-- 記入用シートを印刷する -->
          <c-v-row
            no-gutter
            class="customCol or52057_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="label_left"
            >
              <base-mo00615 :oneway-model-value="localOneway.mo00615OneWayTypeTitle"></base-mo00615>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="customCol or52057_row"
            style="padding-top: 0px; padding-left: 24px; background-color: #fafafa"
          >
            <c-v-col
              cols="12"
              sm="12"
            >
              <base-mo01338 :oneway-model-value="localOneway.mo01338OneWay"></base-mo01338>
            </c-v-col>
          </c-v-row>
          <c-v-divider></c-v-divider>

          <c-v-row
            no-gutter
            class="customCol or52057_row"
          >
            <c-v-col
              cols="12"
              sm="7"
              style="padding-left: 0px; padding-right: 0px"
            >
              <base-mo00039
                v-model="mo00039Type"
                :oneway-model-value="localOneway.mo00039OneWay"
              >
              </base-mo00039>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="5"
              style="padding-left: 0px; padding-right: 8px"
            >
              <base-mo00020
                v-if="mo00020Flag"
                v-model="mo00020Type"
                :oneway-model-value="localOneway.mo00020OneWay"
              >
              </base-mo00020>
            </c-v-col>
          </c-v-row>

          <c-v-row
            no-gutter
            class="printerOption customCol or52057_row"
          >
            <c-v-col
              cols="12"
              sm="12"
            >
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayPrinterOption"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="customCol or52057_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              style="padding-bottom: 0px; padding-left: 0px"
            >
              <base-mo00018
                v-model="mo00018Type"
                :oneway-model-value="localOneway.mo00018OneWay"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>

          <c-v-row
            no-gutter
            class="customCol or52057_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              style="padding-bottom: 0px; padding-left: 32px"
            >
              <base-mo00615 :oneway-model-value="localOneway.mo00615Oneway"> </base-mo00615>
            </c-v-col>
          </c-v-row>

          <!-- 選択履歴のデータのみを印刷する -->
          <c-v-row
            no-gutter
            class="customCol or52057_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              style="padding-bottom: 0px; padding-left: 0px"
            >
              <base-mo00018
                v-model="mo00018TypeSelectionHistory"
                :oneway-model-value="localOneway.mo00018OneWaySelectionHistory"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>
          <!-- 履歴情報を印刷する（基準日、作成者） -->
          <c-v-row
            no-gutter
            class="customCol or52057_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              style="padding-bottom: 0px; padding-left: 0px"
            >
              <base-mo00018
                v-model="mo00018TypeSelectionHistoryInfo"
                :oneway-model-value="localOneway.mo00018OneWayHistoryInfo"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>
          <!-- 選択項目を囲む色 -->
          <c-v-row
            no-gutter
            class="customCol or52057_row"
          >
            <c-v-col
              cols="6"
              sm="6"
              style="padding-bottom: 0px; padding-left: 32px"
            >
              <base-mo00615 :oneway-model-value="localOneway.mo00615OnewaySelectColor">
              </base-mo00615>
            </c-v-col>
            <c-v-col
              cols="6"
              sm="6"
              style="padding-top: 0px; padding-bottom: 0px"
            >
              <base-mo00040
                v-model="local.mo00040Type"
                :oneway-model-value="localOneway.mo00040OneWay"
              ></base-mo00040>
            </c-v-col>
          </c-v-row>

          <!-- 説明文を印刷するチェックボックス -->
          <c-v-row
            no-gutter
            class="customCol or52057_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              style="padding-top: 0px; padding-left: 0px"
            >
              <base-mo00018
                v-model="mo00018TypePrintCheck"
                :oneway-model-value="localOneway.mo00018OneWayTypePrintCheck"
                @click="printColorCheckBoxCliick"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>
          <!-- 説明文を印刷するチェックボックス -->
          <c-v-row
            no-gutter
            class="customCol or52057_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              style="padding-top: 0px; padding-left: 0px"
            >
              <base-mo00018
                v-model="mo00018TypePrintCheck"
                :oneway-model-value="localOneway.mo00018OneWayTypePrintCheck"
                @click="printColorCheckBoxCliick"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="7"
          class="content_center"
        >
          <c-v-row
            class="or52057_row"
            no-gutter
            style="align-items: center; height: 90px"
          >
            <c-v-col
              cols="12"
              sm="4"
              style="padding: 0px"
            >
              <c-v-row
                no-gutter
                class="or52057_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  class="or52057-pd-8"
                >
                  <base-mo01338
                    :oneway-model-value="localOneway.mo01338OneWayPrinterUserSelect"
                    style="background-color: transparent"
                  >
                  </base-mo01338>
                </c-v-col>
              </c-v-row>
              <c-v-row
                no-gutter
                class="or52057_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  style="padding: 0px"
                >
                  <base-mo00039
                    v-model="mo00039OneWayUserSelectType"
                    :oneway-model-value="localOneway.mo00039OneWayUserSelectType"
                  >
                  </base-mo00039>
                </c-v-col>
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-if="kijunbiFlag"
              cols="12"
              sm="4"
              style="padding: 0px"
            >
              <c-v-row
                no-gutter
                class="or52057_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  class="or52057-pd-8"
                >
                  <base-mo00615 :oneway-model-value="localOneway.mo00615OneWayType"> </base-mo00615>
                </c-v-col>
              </c-v-row>
              <c-v-row
                no-gutter
                class="or52057_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  style="padding: 0px"
                >
                  <base-mo00020
                    v-model="mo00020TypeKijunbi"
                    :oneway-model-value="localOneway.mo00020KijunbiOneWay"
                  />
                </c-v-col>
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-else
              cols="12"
              sm="4"
              style="padding: 0px"
            >
              <c-v-row
                no-gutter
                class="or52057_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  class="or52057-pd-8"
                >
                  <base-mo01338
                    :oneway-model-value="localOneway.mo01338OneWayPrinterHistorySelect"
                    style="background-color: transparent"
                  >
                  </base-mo01338>
                </c-v-col>
              </c-v-row>
              <c-v-row
                no-gutter
                class="or52057_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  style="padding: 0px"
                >
                  <base-mo00039
                    v-model="mo00039OneWayHistorySelectType"
                    :oneway-model-value="localOneway.mo00039OneWayHistorySelectType"
                  >
                  </base-mo00039>
                </c-v-col>
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-if="tantoIconBtn"
              cols="12"
              sm="4"
              class="or52057-pd-8"
            >
              <!-- 担当ケアマネプルダウン -->
              <g-custom-or-x-0145
                v-bind="orx0145"
                v-model="orX0145Type"
                :oneway-model-value="localOneway.orX0145Oneway"
                @update:model-value="orx0145UpdateModelValue"
              ></g-custom-or-x-0145>
            </c-v-col>
          </c-v-row>
          <c-v-divider></c-v-divider>
          <c-v-row
            class="or52057_row"
            no-gutter
          >
            <c-v-col
              :cols="userCols"
              class="or52057-pd-8"
            >
              <!-- 印刷設定画面利用者一覧 -->
              <g-custom-or-x-0130
                v-if="orX0130Oneway.selectMode && initFlag"
                v-bind="orX0130"
                :oneway-model-value="orX0130Oneway"
              >
              </g-custom-or-x-0130>
            </c-v-col>
            <c-v-col
              v-if="mo01334TypeHistoryFlag && initFlag"
              cols="8"
              style="overflow-x: hidden"
              class="or52057-pd-8"
            >
              <div style="width: 100%; height: 100%; overflow: auto">
                <!-- 計画期間＆履歴一覧 -->
                <g-custom-or-x-0128
                  v-if="orX0128OnewayModel.singleFlg"
                  v-bind="orX0128"
                  :oneway-model-value="orX0128OnewayModel"
                  style="width: 515px"
                ></g-custom-or-x-0128>
              </div>
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          @click="close"
        >
        </base-mo00611>
        <!-- PDFダウンロードボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          class="mx-2"
          @click="pdfDownload()"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- 印刷設定帳票出力状態リスト画面 -->
  <g-custom-or-x-0117
    v-if="showDialogOrX0117"
    v-bind="orX0117"
    :oneway-model-value="orX0117Oneway"
  ></g-custom-or-x-0117>
  <!-- Or21813:有機体:エラーダイアログ -->
  <g-base-or21814 v-bind="or21813" />
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814 v-bind="or21814" />
</template>
<style>
.or52057_content {
  padding: 0px !important;
}

.or52057_gokeiClass {
  label {
    color: #ffffff;
  }
}
</style>
<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table.scss';

.or52057_row {
  margin: 0px !important;
}

.table-header {
  padding: 8px;
}

.content_center {
  border-left: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  padding: 0px !important;

  .label_left {
    padding-right: 0px;
  }

  .label_right {
    padding-left: 0px;
    padding-right: 0px;
  }

  .printerOption {
    background-color: #edf1f7;
  }

  :deep(.label-area-style) {
    display: none;
  }

  .customCol {
    margin-left: 0px;
    margin-right: 0px;
  }

  .or52057-pd-8 {
    padding: 8px;
  }
}
</style>
