import { Or10443Const } from './Or10443.constants'
import type { Or10443StateType } from './Or10443.type'
import { Or00094Const } from '~/components/base-components/organisms/Or00094/Or00094.constants'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'
import { Or00094Logic } from '~/components/base-components/organisms/Or00094/Or00094.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or10148Const } from '~/components/custom-components/organisms/Or10148/Or10148.constants'
import { Or10148Logic } from '~/components/custom-components/organisms/Or10148/Or10148.logic'
import { OrX0128Const } from '~/components/custom-components/organisms/OrX0128/OrX0128.constants'
import { OrX0128Logic } from '~/components/custom-components/organisms/OrX0128/OrX0128.logic'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import { OrX0130Logic } from '~/components/custom-components/organisms/OrX0130/OrX0130.logic'
import { OrX0117Const } from '~/components/custom-components/organisms/OrX0117/OrX0117.constants'
import { OrX0117Logic } from '~/components/custom-components/organisms/OrX0117/OrX0117.logic'
import { OrX0145Const } from '~/components/custom-components/organisms/OrX0145/OrX0145.constants'
import { OrX0145Logic } from '~/components/custom-components/organisms/OrX0145/OrX0145.logic'

/**
 * Or10443:処理ロジック
 * GUI01079_印刷設定
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR> DAO VAN DUONG
 */
export namespace Or10443Logic {
  /**
   * initialize
   *
   * @description
   * コンポーネントの初期化処理。
   * 共通関数を呼びpiniaの領域を初期化する。
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or10443Const.CP_ID(0),
      uniqueCpId,
      initTwoWayValue: {
        choPrtList: [],
      },
      childCps: [
        { cpId: Or00094Const.CP_ID(0) },
        { cpId: Or21813Const.CP_ID(0) },
        { cpId: Or21815Const.CP_ID(0) },
        { cpId: Or10148Const.CP_ID(0) },
        { cpId: OrX0128Const.CP_ID(0) },
        { cpId: OrX0130Const.CP_ID(0) },
        { cpId: OrX0117Const.CP_ID(0) },
        { cpId: OrX0145Const.CP_ID(0) },
      ],
      editFlgNecessity: false,
    })

    Or00094Logic.initialize(childCpIds[Or00094Const.CP_ID(0)].uniqueCpId)
    Or21813Logic.initialize(childCpIds[Or21813Const.CP_ID(0)].uniqueCpId)
    Or21815Logic.initialize(childCpIds[Or21815Const.CP_ID(0)].uniqueCpId)
    Or10148Logic.initialize(childCpIds[Or10148Const.CP_ID(0)].uniqueCpId)
    OrX0128Logic.initialize(childCpIds[OrX0128Const.CP_ID(0)].uniqueCpId)
    OrX0130Logic.initialize(childCpIds[OrX0130Const.CP_ID(0)].uniqueCpId)
    OrX0117Logic.initialize(childCpIds[OrX0117Const.CP_ID(0)].uniqueCpId)
    OrX0145Logic.initialize(childCpIds[OrX0145Const.CP_ID(0)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }

  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or10443StateType>(Or10443Const.CP_ID(0))
}
