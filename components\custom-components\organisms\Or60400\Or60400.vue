<script setup lang="ts">
/**
 * Or60400:有機体:週間計画一括取込
 * GUI00614_週間計画一括取込
 *
 * @description
 * GUI00614_週間計画一括取込
 *
 * <AUTHOR>
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or26261Const } from '../Or26261/Or26261.constants'
import { Or26823Const } from '../Or26823/Or26823.constants'
import { Or26823Logic } from '../Or26823/Or26823.logic'
import { Or60400Const } from './Or60400.constants'
import type { Or60400StateType, WeeklyPlanTableDataItem } from './Or60400.type'
import { useScreenOneWayBind, useSetupChildProps } from '~/composables/useComponentVue'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'

import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import type {
  Mo01334Headers,
  Mo01334OnewayType,
  Mo01334Type,
} from '~/types/business/components/Mo01334Type'
import type { Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo01352OnewayType, Mo01352Type } from '~/types/business/components/Mo01352Type'
import type { Or60400OnewayType } from '~/types/cmn/business/components/Or60400Type'
import { Or00094Const } from '~/components/base-components/organisms/Or00094/Or00094.constants'
import { Or00094Logic } from '~/components/base-components/organisms/Or00094/Or00094.logic'
import type {
  weekPlanBundleImportInfoSelectInEntity,
  weekPlanBundleImportInfoSelectOutEntity,
  weekPlanBundleImportInfoUpdateInEntity,
} from '~/repositories/cmn/entities/weekPlanBundleImportInfoEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { ResBodyStatusCode } from '~/constants/api-constants'
import { hasRegistAuth, useSystemCommonsStore } from '#imports'
import { convHiraganaToHalfKanaMap } from '~/constants/KanaMap'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import type { Or26823OnewayType, Or26823Type } from '~/types/cmn/business/components/Or26823Type'

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or60400OnewayType
  uniqueCpId: string
}

const props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/
const or21814 = ref({ uniqueCpId: '' })
const or21813 = ref({ uniqueCpId: '' })
const or00094 = ref({ uniqueCpId: '' })
// 週間計画取込状況画面
const or26823 = ref({ uniqueCpId: '' })
const orx0145 = ref({ uniqueCpId: '' })

const editFlg = ref(false)

// 表データのバックアップ
let weeklyPlanTableDataBak: WeeklyPlanTableDataItem[] = []
// 既定のヘッダー
const headersBak = [
  // フリガナラベル
  {
    title: t('label.frigana'),
    key: 'nameKana',
    minWidth: '130px',
    sortable: true,
    children: [
      {
        key: 'nameKnj',
        title: t('label.full_name'),
        sortable: true,
      },
    ],
  },
  {
    title: t('label.sex'),
    key: 'sex',
    minWidth: '90px',
    sortable: true,
  },
  // 電話番号
  {
    title: t('label.phone-number'),
    key: 'tel',
    children: [
      {
        key: 'keitaitel',
        title: t('label.mobile-phone-number'),
        sortable: true,
      },
    ],
    minWidth: '140px',
    sortable: true,
  },
  // 要介護度
  {
    title: t('label.level-of-care-required'),
    key: 'yokaiKnj',
    minWidth: '120px',
    sortable: true,
  },
  // 認定期間
  {
    title: t('label.certification-eriod'),
    key: 'certificationPeriod ',
    minWidth: '200px',
    sortable: false,
    children: [
      {
        title: t('label.start-date'),
        key: 'startYmd',
        minWidth: '120px',
        sortable: true,
      },
      {
        title: t('label.end-date'),
        key: 'endYmd',
        minWidth: '120px',
        sortable: true,
      },
    ],
  },
  //週間計画
  {
    title: t('label.weekly-plan'),
    key: 'tougaiYm',
    minWidth: '135px',
    sortable: true,
  },
]

const localOneway = reactive({
  or60400: {
    ...props.onewayModelValue,
  },
  // ダイアログ
  mo00024Oneway: {
    width: '1300px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      toolbarTitle: t('label.weekly-plan-import'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
    },
  } as Mo00024OnewayType,
  // 閉じるボタン設置
  mo00609CloseBtnOneway: {
    btnLabel: t('btn.close'),
    width: '90px',
  } as Mo00609OnewayType,
  // 取込ボタン設置
  mo00609ImportBtnOneway: {
    btnLabel: t('btn.import'),
    width: '90px',
  } as Mo00609OnewayType,
  category1Title: t('label.week-plan-set'),
  category2Title: t('label.import-month-year'),
  // 処理年月を指定するcheckbox
  processingDmyCheckbox: {
    showItemLabel: false,
    checkboxLabel: t('label.year-monthly-sel'),
  } as Mo00018OnewayType,
  mo01352Oneway: {
    showItemLabel: false,
    showSelectArrow: true,
  } as Mo01352OnewayType,
  // 処理年月を優先するcheckbox
  prioritizeProcessingDmyCheckbox: {
    showItemLabel: false,
    checkboxLabel: t('label.prioritize-process-month-year'),
  } as Mo00018OnewayType,
  // 短期入所サービスの取込Radio
  shortTermImportResidentialmo00039Oneway: {
    showItemLabel: true,
    itemLabel: t('label.short-term-import-residential-month-year'),
    items: [
      {
        value: '1',
        label: t('label.jigyousho-service-plan'),
      },
      {
        value: '2',
        label: t('label.jigyousho-service'),
      },
      {
        value: '3',
        label: t('label.do-not-import'),
      },
    ],
    customClass: { labelClass: 'ml-2' },
  } as Mo00039OnewayType,
  // 取込を許可する利用票の状況Radio
  importAllowedUseSlipMo00039Oneway: {
    showItemLabel: true,
    itemLabel: t('label.import-permission-form'),
    items: [
      {
        value: '1',
        label: t('label.uncreated'),
      },
      {
        value: '2',
        label: t('label.up-to-in-progress'),
      },
      {
        value: '3',
        label: t('label.up-to-scheduled'),
      },
    ],
    customClass: { labelClass: 'ml-2' },
  } as Mo00039OnewayType,
  // 福祉用具貸与設定単位数Radio
  welfareEquipmentMo00039Oneway: {
    showItemLabel: true,
    itemLabel: t('label.welfare-equipment-loan-unit-setting'),
    items: [
      {
        value: '1',
        label: t('label.prioritize-the-service-unit-count-from-the-source'),
      },
      {
        value: '2',
        label: t('label.welfare-equipment-master-setting'),
      },
    ],
    customClass: { labelClass: 'ml-2' },
  } as Mo00039OnewayType,
  // 取込期間開始年月
  // 週間計画TableList
  weeklyPlanListMo01334: {
    // 週間計画データテーブルのヘッダー
    headers: headersBak,
    height: 395,
    showSelect: true,
    selectStrategy: 'all',
    mandatory: false,
    items: [],
  } as Mo01334OnewayType,
  inChargeBtnOneway: {
    btnIcon: 'edit_square',
    name: 'inChargeBtn',
    density: 'compact',
  },
  /**
   * 担当ケアマネプルダウン
   */
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: false,
    multiple: false,
    selectedUserCounter: '0',
  } as OrX0145OnewayType,
  or26823Data: {} as Or26823OnewayType,
})

const local = reactive({
  // 表の双方向の値
  formListValues: {
    processingDmyCheckbox: { modelValue: true },
    processingDmy: { value: '' } as Mo01352Type,
    prioritizeProcessingDmyCheckbox: { modelValue: false },
    shortTermImportResidential: '1',
    importAllowedUseSlip: '1',
    welfareEquipment: '1',
    importStartDmy: { value: '' } as Mo01352Type,
    importEndDmy: { value: '' } as Mo01352Type,
  },
  //  週間計画取込状況の返回值
  Or26823: { usingSlipList: [] } as Or26823Type,
})

const mo00024 = ref<Mo00024Type>({
  isOpen: Or60400Const.DEFAULT.IS_OPEN,
})

// 週間計画情報選択行データ設定
const selectedItem = ref<Mo01334Type>({
  value: '',
  values: [],
})

// 初期排他
let isInit = true

//現在選択されている五十音の文字
let currentOr00094Value = [Or60400Const.DEFAULT.STR_ALL]

/**
 * 当ケアマネプルダウン選択値
 */
const orX0145Type = ref<OrX0145Type>({
  value: {} as TantoCmnShokuin,
} as OrX0145Type)

const plansColums = Array.from({ length: 24 }, (_, index) => {
  const planNumber = index + 1
  const key = `plan${planNumber}`
  const title = t('label.use-slip') + `${planNumber}\n`
  return { key, title, minWidth: '130px', width: '130px', sortable: true, dmyValue: '' }
})

/**************************************************
 * 算出プロパティ
 **************************************************/
// ダイアログ表示フラグ
const showDialogOr21814 = computed(() => {
  // Or10486のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr21813 = computed(() => {
  // Or10486のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr26823 = computed(() => {
  // Or26823のダイアログ開閉状態
  return Or26823Logic.state.get(or26823.value.uniqueCpId)?.isOpen ?? false
})

// 計算を表しているかどうか
const showFlg = computed(() => {
  return local.formListValues.processingDmyCheckbox.modelValue
})

// 表の集計欄computed
const headcountsummaryComputed = computed(() => {
  return {
    totalCount:
      t('label.target-count') +
      `：${localOneway.weeklyPlanListMo01334.items.length}` +
      t('label.unit-person'),
    selectedCount:
      t('label.selected-count') + `：${selectedItem.value.values.length}` + t('label.unit-person'),
  }
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(1)]: or21814.value,
  [Or21813Const.CP_ID(1)]: or21813.value,
  [Or26261Const.CP_ID(1)]: or21813.value,
  [Or00094Const.CP_ID(1)]: or00094.value,
  [Or26823Const.CP_ID(1)]: or26823.value,
})

// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or60400StateType>({
  cpId: Or60400Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or60400Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/

// 50音ボタンを初期化
Or00094Logic.state.set({
  uniqueCpId: or00094.value.uniqueCpId,
  state: {
    dispSettingBtnDisplayFlg: true,
    focusSettingFlg: true,
    focusSettingInitial: [Or60400Const.DEFAULT.STR_ALL],
  },
})

orX0145Type.value.value = {
  counter: '',
  chkShokuId: localOneway.or60400.chkShokuId ?? '',
  houjinId: '',
  shisetuId: '',
  svJigyoId: '',
  shokuin1Kana: '',
  shokuin2Kana: '',
  shokuin1Knj: '',
  shokuin2Knj: '',
  value: '',
} as TantoCmnShokuin

onMounted(async () => {
  // 処理年月を初期化
  if (!localOneway.or60400.objYm) {
    // ・親画面.対象年月＝NULL（空）の場合、システム日付の年月を表示
    const temDate = systemCommonsStore.getSystemDate
      ? systemCommonsStore.getSystemDate.replace(/\/\d{2}$/, '')
      : ''

    local.formListValues.processingDmy = { value: temDate }
    localOneway.or60400.objYm = temDate
    local.formListValues.importStartDmy = { value: temDate }
    local.formListValues.importEndDmy = { value: temDate }
  } else {
    // ・親画面.対象年月<>NULL（空）の場合、親画面.対象年月を表示
    local.formListValues.processingDmy = { value: localOneway.or60400.objYm }
    local.formListValues.importStartDmy = { value: localOneway.or60400.objYm }
    local.formListValues.importEndDmy = { value: localOneway.or60400.objYm }
  }

  localOneway.orX0145Oneway.selectedUserCounter = localOneway.or60400.chkShokuId

  editFlg.value = await hasRegistAuth('')
  editFlg.value = true
})

/**
 * 初期情報取得
 *
 * @param intoParams -入力エンティティ
 */
const getDataInfo = async (intoParams?: Partial<weekPlanBundleImportInfoSelectInEntity>) => {
  //初期情報取得(IN)

  // バックエンドAPIから初期情報取得
  const inputData: weekPlanBundleImportInfoSelectInEntity = {
    svJigyoId: localOneway.or60400.svJigyoId,
    selWeek: local.formListValues.processingDmy.value,
    objYm: localOneway.or60400.objYm,
    tantoId: (orX0145Type.value.value as TantoCmnShokuin).chkShokuId ?? '',
    selWeekFlag: Or60400Const.DEFAULT.PROCESSINGDMY_0,
    shoriYmFlag: Or60400Const.DEFAULT.IMPORTDMY_0,
    ibSelWeekYm: local.formListValues.processingDmyCheckbox.modelValue
      ? Or60400Const.DEFAULT.PROCESSINGDMY_1
      : Or60400Const.DEFAULT.SELECTSTATUS_0,
    shoriYm1: '',
    shoriYm2: '',
    shoriYm3: '',
    shoriYm4: '',
    shoriYm5: '',
    shoriYm6: '',
    shoriYm7: '',
    shoriYm8: '',
    shoriYm9: '',
    shoriYm10: '',
    shoriYm11: '',
    shoriYm12: '',
    shoriYm13: '',
    shoriYm14: '',
    shoriYm15: '',
    shoriYm16: '',
    shoriYm17: '',
    shoriYm18: '',
    shoriYm19: '',
    shoriYm20: '',
    shoriYm21: '',
    shoriYm22: '',
    shoriYm23: '',
    shoriYm24: '',
    screenNm: localOneway.or60400.screenNm,
    shokuinId: localOneway.or60400.shokuinId,
    sysCd: localOneway.or60400.sysCd,
    gojuuOnRowNo: '',
    gojuuOnKana: '',
    userList: localOneway.weeklyPlanListMo01334.items.map((item) => {
      return {
        userId: item.id ?? '',
      }
    }),
    ...intoParams,
  }
  plansColums.forEach((item, index) => {
    index++
    inputData[Or60400Const.DEFAULT.SHORIYM + index] = item?.dmyValue ?? ''
  })

  const resData: weekPlanBundleImportInfoSelectOutEntity = await ScreenRepository.select(
    'weekPlanBundleImportInfoSelect',
    inputData
  )
  if (resData.statusCode === ResBodyStatusCode.SUCCESS) {
    // データ情報設定
    const { userList, copyTnki, selSyori, fygTani, syoriYm } = resData.data
    weeklyPlanTableDataBak = userList?.map((item) => {
      return {
        ...item,
        id: item.userId,
        nameKana: item.name1Kana + item.name2Kana,
        nameKnj: item.name1Knj + item.name2Knj,
        sex: item.sex,
      }
    })
    // 50音母音に設定がある場合
    calcAppliedFilterUserList(currentOr00094Value)

    local.formListValues.prioritizeProcessingDmyCheckbox.modelValue = !!syoriYm
    local.formListValues.shortTermImportResidential = copyTnki
    local.formListValues.importAllowedUseSlip = selSyori
    local.formListValues.welfareEquipment = fygTani
    selectedItem.value.value = userList[0].userId
  }
}

/**
 * 動的列計算関数
 *
 * @param start -開始日
 *
 * @param end -終了日
 */
const generatePlanColumns = (start: string, end: string) => {
  // 入力された文字列を Date オブジェクトに変換する Date 对象
  const startDate = new Date(start + '/01')
  const endDate = new Date(end + '/01')
  // 総月数の計算
  const totalMonths =
    (endDate.getFullYear() - startDate.getFullYear()) * 12 +
    (endDate.getMonth() - startDate.getMonth()) +
    1
  // planColumns 配列の充填
  for (let i = 0; i < 24; i++) {
    const currentMonth = new Date(startDate)
    currentMonth.setMonth(startDate.getMonth() + i)
    const year = currentMonth.getFullYear()
    const month = String(currentMonth.getMonth() + 1).padStart(2, '0')
    if (i >= totalMonths) {
      plansColums[i] = {
        ...plansColums[i],
        title: t('label.use-slip') + `${i + 1}`,
        dmyValue: '',
      }
    } else {
      plansColums[i] = {
        ...plansColums[i],
        title: t('label.use-slip') + `${i + 1}\n ` + `${year}/${month}` + t('label.unit-record'),
        dmyValue: `${year}/${month}`,
      }
    }
  }
  localOneway.weeklyPlanListMo01334.headers = [...headersBak, ...plansColums] as Mo01334Headers[]
}

/**
 * 区間計算
 *
 * @param dmy -開始日
 *
 * @param type -終了日
 */
const dateRangeComputed = (dmy: string, type: string) => {
  const startDate =
    type === '1'
      ? new Date(dmy + '/01')
      : new Date(local.formListValues.importStartDmy.value + '/01')
  const endDate =
    type === '2' ? new Date(dmy + '/01') : new Date(local.formListValues.importEndDmy.value + '/01')

  if (startDate > endDate) {
    if (type === '1') {
      local.formListValues.importEndDmy.value = dmy
    } else {
      local.formListValues.importStartDmy.value = dmy
    }
    generatePlanColumns(dmy, dmy)
  } else {
    // 総月数の計算
    const totalMonths =
      (endDate.getFullYear() - startDate.getFullYear()) * 12 +
      (endDate.getMonth() - startDate.getMonth()) +
      1
    if (totalMonths > 24) {
      if (type === '1') {
        // start が小さくなって、期間が24ヶ月を超える場合、end を調整する
        const newEndDate = new Date(startDate)
        // 1ヶ月を引くのは、月が0から始まるためです
        newEndDate.setMonth(startDate.getMonth() + 24 - 1)
        const endTem = `${newEndDate.getFullYear()}/${String(newEndDate.getMonth() + 1).padStart(2, '0')}`
        local.formListValues.importEndDmy.value = endTem
        generatePlanColumns(dmy, endTem)
      } else {
        // end が大きくなって、期間が24ヶ月を超える場合、start を調整する
        const newStartDate = new Date(endDate)
        newStartDate.setMonth(endDate.getMonth() - 24 + 1)
        const startTem = `${newStartDate.getFullYear()}/${String(newStartDate.getMonth() + 1).padStart(2, '0')}`
        local.formListValues.importStartDmy.value = startTem
        generatePlanColumns(startTem, dmy)
      }
    } else {
      generatePlanColumns(
        `${startDate.getFullYear()}/${String(startDate.getMonth() + 1).padStart(2, '0')}`,
        `${endDate.getFullYear()}/${String(endDate.getMonth() + 1).padStart(2, '0')}`
      )
    }
  }
  setTimeout(() => (dateChangeType = '0'), 0)
}

/**
 * 閉じるボタン押下時
 */
const onClickCloseBtn = () => {
  void onSave()
  setState({ isOpen: false })
}

/**
 * 取込ボタン押下時
 */
const onImportBtn = () => {
  // 画面.週間計画一括取込一覧明細の件数が0件の場合
  if (!localOneway.weeklyPlanListMo01334.items.length) {
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: {
        // ダイアログタイトル
        dialogTitle: t('label.caution'),
        // ダイアログテキスト
        iconName: 'info',
        dialogText: t('message.i-cmn-10310'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
        isOpen: true,
      },
    })
    return
  }
  // 画面.週間計画一括取込一覧明細.選択チェックボックスのチェック（選択）されている件数が0件の場合
  if (!selectedItem.value.values.length) {
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: {
        // ダイアログタイトル
        dialogTitle: t('label.caution'),
        // ダイアログテキスト
        iconName: 'info',
        dialogText: t('message.i-cmn-11299'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
        isOpen: true,
      },
    })
    return
  }
  localOneway.or26823Data = {
    // 利用者ID配列[1]
    userId: selectedItem.value.values,
    // 対象年月：画面.処理年月
    subjectYymm: local.formListValues.processingDmy.value,
    // 開始年月：画面.取込期間開始年月
    startYymm: local.formListValues.importStartDmy.value,
    // 終了年月：画面.取込期間終了年月
    endYymm: local.formListValues.importEndDmy.value,
    // 呼び出し元判定
    userCheck: '1',
    // 取り込みの区分
    importKbn: '0',
    // 短期入所サービスの複写：画面.短期入所サービスの取込ラジオボタン
    shortTermImportResidential: local.formListValues.shortTermImportResidential,
    // 作成を許可する利用票状況：画面.取込を許可する利用票の状況ラジオボタン
    importAllowedUseSlip: local.formListValues.importAllowedUseSlip,
    // 福祉用具単位数の設定：画面.福祉用具貸与 設定単位数ラジオボタン
    equipmentUnit: local.formListValues.welfareEquipment,
    // 処理年月を優先する：画面.処理年月を優先するチェックボックス
    processYymmPri: local.formListValues.prioritizeProcessingDmyCheckbox.modelValue ? '1' : '0',
    // 処理年月を指定する：画面.処理年月を指定するチェックボックス
    processYymmDes: local.formListValues.processingDmyCheckbox.modelValue ? '1' : '0',
  } as unknown as Or26823OnewayType
  Or26823Logic.state.set({
    uniqueCpId: or26823.value.uniqueCpId,
    state: { isOpen: true },
  })
  // void getDataInfo({
  //   selWeekFlag: Or60400Const.DEFAULT.PROCESSINGDMY_1,
  //   shoriYmFlag: Or60400Const.DEFAULT.IMPORTDMY_1,
  // })
}

const onSave = async () => {
  const inputData: weekPlanBundleImportInfoUpdateInEntity = {
    shokuinId: localOneway.or60400.shokuinId,
    screenNm: localOneway.or60400.screenNm,
    sysCd: localOneway.or60400.sysCd,
    copyTnkiParam: local.formListValues.shortTermImportResidential,
    selSyoriParam: local.formListValues.importAllowedUseSlip,
    fygTaniParam: local.formListValues.welfareEquipment,
    syoriYmParam: local.formListValues.prioritizeProcessingDmyCheckbox.modelValue ? '1' : '0',
  }
  await ScreenRepository.update('weekPlanBundleImportSettingsUpdate', inputData)
}

/**
 * 50音ヘッドラインのフィルター適用後の利用者一覧を算出し、ローカル変数に設定します。
 * また、システム共有領域．利用者選択領域の更新も行います。
 *
 * @param or00094Select - 50音
 */
const calcAppliedFilterUserList = (or00094Select: string[]) => {
  // システム共有領域．利用者選択領域から、50音ヘッドラインの選択値を取得
  let workFilterInitials: string[] = []
  const workGetUserSelectFilterInitials = or00094Select
  if (workGetUserSelectFilterInitials) {
    workFilterInitials = workGetUserSelectFilterInitials // readonlyを外すためにasが必要
  }

  if (!workFilterInitials.length) {
    // 50音ヘッドラインのフィルターが設定値なしの場合、
    // 利用者一覧は全量を設定する
    if (weeklyPlanTableDataBak?.length) {
      localOneway.weeklyPlanListMo01334.items = weeklyPlanTableDataBak
    }
  } else if (workFilterInitials.length && workFilterInitials[0] === Or60400Const.DEFAULT.STR_ALL) {
    // 50音ヘッドラインで「全」が選択された場合
    localOneway.weeklyPlanListMo01334.items = weeklyPlanTableDataBak
  } else if (
    workFilterInitials.length &&
    workFilterInitials[0] === Or60400Const.DEFAULT.STR_OTHER
  ) {
    // 50音ヘッドラインで「他」が選択された場合
    const workUserList = weeklyPlanTableDataBak.filter((workUser) => {
      // 読み仮名の1文字目がひらがなではなければtrue
      const regex = /^[ぁ-ん]+$/u
      if (!regex.test(workUser.name1Kana.charAt(0))) {
        return true
      }
    })
    // ヒットしたユーザーを"50音ヘッドラインのフィルター適用後の利用者一覧"に追加
    localOneway.weeklyPlanListMo01334.items = workUserList
  } else {
    // 50音ヘッドラインのフィルターにヒットする利用者を設定する

    if (weeklyPlanTableDataBak?.length) {
      const temArr: WeeklyPlanTableDataItem[] = []
      workFilterInitials.forEach((initialValue) => {
        const workUserList: WeeklyPlanTableDataItem | undefined = weeklyPlanTableDataBak.find(
          (workUser) => yomiHitCheck(workUser.name1Kana, initialValue)
        )
        // ヒットしたユーザーを"50音ヘッドラインのフィルター適用後の利用者一覧"に追加
        if (workUserList) {
          temArr.push(workUserList)
        }
      })

      localOneway.weeklyPlanListMo01334.items = temArr
    }
  }
}

/**
 * 読み仮名ヒットチェック
 * 対象ワードの1文字目と、検索文字の値が一致するか比較します。
 * 比較の際は、濁点・半濁点を無視するために半角カタカナで比較します。
 * true : 一致する
 * false : 一致しない
 *
 * @param targetWord - 対象ワード
 *
 * @param searchChar - 検索文字（50音ヘッドラインの1文字）
 */
const yomiHitCheck = (targetWord: string, searchChar: string) => {
  // 対象ワードの1文字目の半角カタカナ
  // const targetWordKana = Or60400Const.DEFAULT.KANAMAP?.[targetWord.charAt(0)]
  // 検索文字の半角カタカナ
  const searchCharKana = convHiraganaToHalfKanaMap?.[searchChar.charAt(0)]
  // 濁点・半濁点を考慮して、1文字目を対象に比較
  return targetWord.charAt(0).startsWith(searchCharKana.charAt(0))
}

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 *  Mo00024（ダイアログ）の開閉状態を監視
 *
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      void onClickCloseBtn()
    }
  }
)

/**
 * 五十音を変更した場合の処理
 */
watch(
  () => Or00094Logic.event.get(or00094.value.uniqueCpId),
  (newValue) => {
    if (!newValue || isInit) return
    if (newValue?.charBtnClickFlg) {
      // 50音ヘッドラインの文字ボタン押下イベントを検知
      const selectValueArray = Or00094Logic.data.get(or00094.value.uniqueCpId)?.selectValueArray
      if (!selectValueArray) return
      currentOr00094Value = selectValueArray
      // システム共有領域．利用者選択領域に50音ヘッドラインの選択値を設定
      // 50音ヘッドラインのフィルター適用後の利用者一覧を算出
      calcAppliedFilterUserList(selectValueArray)
    }
  }
)

/**
 * 「処理年月」変更を監視
 */
watch(
  () => local.formListValues.processingDmy.value,
  async (newVal) => {
    if (!newVal) return
    if (isInit) {
      dateRangeComputed(newVal, '1')
      // 初期情報取得
      await getDataInfo()
      setTimeout(() => (isInit = false), 0)
    } else {
      void getDataInfo({
        selWeekFlag: Or60400Const.DEFAULT.PROCESSINGDMY_1,
        shoriYmFlag: Or60400Const.DEFAULT.IMPORTDMY_0,
      })
    }
  }
)

let dateChangeType = '0'
/**
 * 取込期間開始日を監視
 */
watch(
  () => local.formListValues.importStartDmy.value,
  (newVal, oldVal) => {
    if (!newVal || isInit || dateChangeType !== '0') return
    console.log(newVal, 'left')
    dateChangeType = '1'
    // 画面.取込期間開始年月ー1（１つ前の月）が「2009/05」より前の場合
    if (newVal < Or60400Const.DEFAULT.YYYYMM_2009_05) {
      Or21813Logic.state.set({
        uniqueCpId: or21813.value.uniqueCpId,
        state: {
          // ダイアログタイトル
          dialogTitle: t('label.caution'),
          // ダイアログテキスト
          iconName: 'warning',
          dialogText: t('message.e-cmn-41743'),
          firstBtnType: 'normal1',
          firstBtnLabel: t('btn.yes'),
          secondBtnType: 'blank',
          thirdBtnType: 'blank',
          isOpen: true,
        },
      })

      local.formListValues.importStartDmy.value = oldVal
      setTimeout(() => (dateChangeType = '0'), 0)
      return
    }
    dateRangeComputed(newVal, '1')
    void getDataInfo({
      selWeekFlag: Or60400Const.DEFAULT.PROCESSINGDMY_1,
      shoriYmFlag: Or60400Const.DEFAULT.IMPORTDMY_1,
    })
  }
)

/**
 * 取込期間終了日を監視を監視
 */
watch(
  () => local.formListValues.importEndDmy.value,
  (newVal, oldVal) => {
    if (!newVal || isInit || dateChangeType !== '0') return
    dateChangeType = '2'
    if (newVal < Or60400Const.DEFAULT.YYYYMM_2009_05) {
      Or21813Logic.state.set({
        uniqueCpId: or21813.value.uniqueCpId,
        state: {
          // ダイアログタイトル
          dialogTitle: t('label.caution'),
          // ダイアログテキスト
          iconName: 'error',
          dialogText: t('message.e-cmn-41743'),
          firstBtnType: 'normal1',
          firstBtnLabel: t('btn.yes'),
          secondBtnType: 'blank',
          thirdBtnType: 'blank',
          isOpen: true,
        },
      })
      local.formListValues.importEndDmy.value = oldVal
      setTimeout(() => (dateChangeType = '0'), 0)
      return
    }
    console.log(newVal, 'right')

    dateRangeComputed(newVal, '2')
    void getDataInfo({
      selWeekFlag: Or60400Const.DEFAULT.PROCESSINGDMY_1,
      shoriYmFlag: Or60400Const.DEFAULT.IMPORTDMY_1,
    })
  }
)

watch(
  () => orX0145Type.value,
  (newVal) => {
    if (!newVal || isInit) return
    console.log(newVal, '11')
    void getDataInfo({
      selWeekFlag: Or60400Const.DEFAULT.PROCESSINGDMY_1,
      shoriYmFlag: Or60400Const.DEFAULT.IMPORTDMY_1,
    })
  }
)

/**************************************************
 * ウォッチャー
 **************************************************/
</script>

<template>
  <div>
    <base-mo00024
      v-model="mo00024"
      :oneway-model-value="localOneway.mo00024Oneway"
    >
      <template #cardItem>
        <c-v-sheet>
          <c-v-row no-gutters>
            <c-v-col cols="6">
              <c-v-row
                no-gutters
                class="body-text-m text-bold ml-2"
              >
                {{ localOneway.category1Title }}
              </c-v-row>
              <c-v-row no-gutters>
                <c-v-col
                  cols="5"
                  class="height-72"
                >
                  <base-mo00018
                    v-model="local.formListValues.processingDmyCheckbox"
                    :oneway-model-value="localOneway.processingDmyCheckbox"
                  />
                  <base-mo00018
                    v-show="showFlg"
                    v-model="local.formListValues.prioritizeProcessingDmyCheckbox"
                    :oneway-model-value="localOneway.prioritizeProcessingDmyCheckbox"
                  />
                </c-v-col>
                <c-v-col cols="auto">
                  <base-mo01352
                    v-show="showFlg"
                    v-model="local.formListValues.processingDmy"
                    :oneway-model-value="localOneway.mo01352Oneway"
                  />
                </c-v-col>
              </c-v-row>
              <c-v-row no-gutters>
                <base-mo00039
                  v-model="local.formListValues.shortTermImportResidential"
                  :oneway-model-value="localOneway.shortTermImportResidentialmo00039Oneway"
                />
              </c-v-row>
              <c-v-row no-gutters>
                <base-mo00039
                  v-model="local.formListValues.importAllowedUseSlip"
                  :oneway-model-value="localOneway.importAllowedUseSlipMo00039Oneway"
                />
              </c-v-row>
              <c-v-row no-gutters>
                <base-mo00039
                  v-model="local.formListValues.welfareEquipment"
                  :oneway-model-value="localOneway.welfareEquipmentMo00039Oneway"
                />
              </c-v-row>
            </c-v-col>
            <c-v-col cols="6">
              <c-v-row
                no-gutters
                class="body-text-m text-bold"
              >
                {{ localOneway.category2Title }}
              </c-v-row>
              <c-v-row no-gutters>
                <c-v-col
                  cols="auto"
                  class="d-flex align-center"
                >
                  <span class="height-line">{{ t('label.import-range') }}</span>
                  <base-mo01352
                    v-model="local.formListValues.importStartDmy"
                    :oneway-model-value="localOneway.mo01352Oneway"
                  />
                  <span>~</span>
                  <base-mo01352
                    v-model="local.formListValues.importEndDmy"
                    :oneway-model-value="localOneway.mo01352Oneway"
                  />
                </c-v-col>
              </c-v-row>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutters
            class="mb-2"
          >
            <!-- <c-v-col cols="auto">
              {{ t('label.earrings') }}
            </c-v-col>
            <c-v-col cols="auto">
              <base-mo00009
                class="m00009Container"
                :oneway-model-value="localOneway.inChargeBtnOneway"
                @click="inChargeBtnClick"
              />
            </c-v-col>
            <c-v-col cols="auto">
              {{
                local.or26261Type.shokuin.shokuin1Knj + ' ' + local.or26261Type.shokuin.shokuin2Knj
              }}
            </c-v-col> -->
            <!-- 担当ケアマネプルダウン -->
            <g-custom-or-x-0145
              v-bind="orx0145"
              v-model="orX0145Type"
              :oneway-model-value="localOneway.orX0145Oneway"
            />
          </c-v-row>
          <c-v-row
            no-gutters
            class="tableContainer"
          >
            <c-v-col
              cols="auto"
              class="left_content"
            >
              <g-base-or-00094 v-bind="or00094" />
            </c-v-col>
            <c-v-col
              cols="auto"
              class="table-width table-header fixed-column"
            >
              <base-mo01334
                v-model="selectedItem"
                class="list-wrapper"
                :oneway-model-value="localOneway.weeklyPlanListMo01334"
              >
                <template #[`item.sex`]="{ item }">
                  <div class="text-right">{{ item.sex }}</div>
                </template>
                <template #[`item.nameKnj`]="{ item }">
                  <div>
                    <div>{{ item.nameKana }}</div>
                    <div>{{ item.nameKnj }}</div>
                  </div>
                </template>
                <template #[`item.tel`]="{ item }">
                  <div>
                    <div>{{ item.tel }}</div>
                    <div>{{ item.keitaitel }}</div>
                  </div>
                </template>
                <template
                  v-for="(subuItem, subIndex) in plansColums"
                  :key="subIndex"
                  #[`item.${subuItem!.key}`]="{ item }"
                >
                  <div
                    v-if="item[subuItem!.key] === '1'"
                    class="text-center text_style1 text-bg-yellow"
                  >
                    {{ t('label.saku') }}
                  </div>
                  <div
                    v-else-if="item[subuItem!.key] === '2'"
                    class="text-center text_style2"
                  >
                    {{ t('label.yoyo') }}
                  </div>
                  <div
                    v-else
                    class="text-center text_style3"
                  >
                    {{ t('label.fruit') }}
                  </div>
                </template>
                <template #bottom>
                  <c-v-row no-gutters>
                    <c-v-col
                      cols="3"
                      class="ml-4 mt-2"
                    >
                      <div class="body-text-s">{{ headcountsummaryComputed.totalCount }}</div>
                      <div class="body-text-s">{{ headcountsummaryComputed.selectedCount }}</div>
                    </c-v-col>
                    <c-v-col
                      cols="6"
                      class="d-flex align-center"
                    >
                      <div class="d-flex mr-2">
                        <div class="text_style1">{{ t('label.saku') + ':' }}</div>
                        <div class="body-text-m">{{ t('label.creating') }}</div>
                      </div>
                      <div class="d-flex mr-2">
                        <div class="text_style2">{{ t('label.yoyo') + ':' }}</div>
                        <div class="body-text-m">{{ t('label.schedule-ok') }}</div>
                      </div>
                      <div class="d-flex">
                        <div class="text_style3">{{ t('label.fruit') + ':' }}</div>
                        <div class="body-text-m">{{ t('label.achievements-ok') }}</div>
                      </div>
                    </c-v-col>
                  </c-v-row>
                </template>
              </base-mo01334>
            </c-v-col>
          </c-v-row>
        </c-v-sheet>
      </template>

      <template #cardActionRight>
        <!-- ダイアログフッター -->
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00609CloseBtnOneway"
          class="mx-2"
          @click="onClickCloseBtn"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>

        <!-- 取込ボタン-->
        <base-mo00609
          :oneway-model-value="{ ...localOneway.mo00609ImportBtnOneway, disabled: !editFlg }"
          @click="onImportBtn"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="t('tooltip.weekly-plan-import')"
            open-delay="200"
          />
        </base-mo00609>
      </template>
    </base-mo00024>
  </div>
  <!-- 確認ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  />
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  />
  <!-- 週間計画取込状況画面 -->
  <g-custom-or-26823
    v-if="showDialogOr26823"
    v-bind="or26823"
    v-model="local.Or26823"
    :oneway-model-value="localOneway.or26823Data"
  />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';
.height-38 {
  height: 38px;
  line-height: 38px;
}
.text-bold {
  font-weight: bold;
}
.height-72 {
  height: 72px;
}
.height-line {
  line-height: 36px;
}
.left_content {
  z-index: 200;
}
.tableContainer {
  overflow-y: hidden;
  height: 450px;
  .table-width {
    width: calc(100% - 40px);
    overflow: auto;
    z-index: 100;
  }
  .icon_hover {
    opacity: 0;
  }
  .customTitle:hover {
    .icon_hover {
      opacity: 1;
    }
  }
}
.text_style1 {
  color: rgb(var(--v-theme-blue-600));
  font-weight: bold;
  font-size: 16px;
}
.text_style2 {
  color: rgb(var(--v-theme-green-600));
  font-weight: bold;
  font-size: 16px;
}
.text_style3 {
  color: rgb(var(--v-theme-orange-600));
  font-weight: bold;
  font-size: 16px;
}
.fixed-column :deep(tr:nth-child(1) th:nth-child(2)) {
  border-right: none !important;
  box-shadow: unset;
}
.fixed-column :deep(tr:nth-child(1) th:nth-child(3)) {
  vertical-align: top;
  padding-top: 7px;
}

.fixed-column :deep(tr:nth-child(2) th:nth-child(1)) {
  border-right: none !important;
}
.fixed-column :deep(tr td:has(.text-bg-yellow)) {
  background-color: rgb(var(--v-theme-yellow-200));
}
// // データがない場合、チェックボックス列が意図せず崩れないようにする。
.fixed-column :deep(tr:nth-child(1) th:nth-child(1) > div) {
  width: 38px !important;
}
/* 列固定 */
// １列目 th
.fixed-column :deep(tr:nth-child(1) th:nth-child(1)) {
  position: sticky !important;
  left: 0 !important;
  z-index: 100 !important;
}

/* 列固定 */
// 1列目 td
.fixed-column :deep(td:nth-child(1)) {
  position: sticky !important;
  left: 0;
  background: white;
}

/* 列固定 */
// 2列目 th
.fixed-column :deep(tr:nth-child(1) th:nth-child(2)) {
  position: sticky !important;
  left: 56px !important;
  z-index: 100 !important;
}
.fixed-column :deep(tr:nth-child(2) th:nth-child(1)) {
  position: sticky !important;
  left: 56px !important;
  z-index: 100 !important;
}
.fixed-column :deep(tr:nth-child(1) th:nth-child(3)) {
  position: sticky !important;
  left: 186px !important;
  z-index: 100 !important;
}

/* 列固定 */
// 2列目 td
.fixed-column :deep(td:nth-child(2)) {
  position: sticky !important;
  left: 56px !important;
  background: white;
}
.fixed-column :deep(td:nth-child(3)) {
  position: sticky !important;
  left: 186px !important;
  background: white;
}

/* 列固定 */
// 3列目 th
.fixed-column :deep(tr:nth-child(1) th:nth-child(4)),
:deep(tr:nth-child(2) th:nth-child(2)) {
  position: sticky !important;
  left: 276px !important;
  z-index: 100 !important;
}

/* 列固定 */
// 3列目 td
.fixed-column :deep(td:nth-child(4)) {
  position: sticky !important;
  left: 276px !important;
  background: white;
}
/* 列固定 */
// 4列目 th
.fixed-column :deep(tr:nth-child(1) th:nth-child(5)) {
  position: sticky !important;
  left: 416px !important;
  z-index: 100 !important;
}

/* 列固定 */
// 4列目 td
.fixed-column :deep(td:nth-child(5)) {
  position: sticky !important;
  left: 416px;
  background: white;
}
/* 列固定 */
// 5列目 th
.fixed-column :deep(tr:nth-child(1) th:nth-child(6)),
:deep(tr:nth-child(2) th:nth-child(3)) {
  position: sticky !important;
  left: 536px !important;
  z-index: 100 !important;
}
.fixed-column :deep(tr:nth-child(2) th:nth-child(4)) {
  position: sticky !important;
  left: 656px !important;
  z-index: 100 !important;
}

/* 列固定 */
// 5列目 td
.fixed-column :deep(td:nth-child(6)) {
  position: sticky !important;
  left: 536px;
  background: white;
}
.fixed-column :deep(td:nth-child(7)) {
  position: sticky !important;
  left: 656px;
  background: white;
}

/* 列固定 */
// 6列目 th
.fixed-column :deep(tr:nth-child(1) th:nth-child(7)) {
  position: sticky !important;
  left: 776px !important;
  z-index: 100 !important;
  border-right: 3px solid rgb(var(--v-theme-black-300));
}

/* 列固定 */
// 6列目 td
.fixed-column :deep(td:nth-child(8)) {
  position: sticky !important;
  left: 776px;
  background: white;
  border-right: 3px solid rgb(var(--v-theme-black-300));
}

.fixed-column :deep(.selected-row td) {
  background: rgb(var(--v-theme-blue-100)) !important; // 背景色
}
/* 選択行のスタイル */
.fixed-column :deep(td .selected-row) {
  background-color: rgb(var(--v-theme-blue-100)) !important; // 背景色
}
// 五十音ヘッドラインstyle修正
:deep(.parent-container) {
  height: 34.5px !important;
}
:deep(.parent-btn) {
  height: 34.5px !important;
  line-height: 34.5px;
}
:deep(.v-table__wrapper) {
  position: relative;
}
</style>
