import { Or10320Const } from './Or10320.constants'
import type { Or10320StateType } from './Or10320.type'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'

/**
 * Or10320:有機体:モーダル（アセスメント（インターライ）マスタ画面モーダル））
 * 処理ロジック
 *
 * @description
 * initialize処理とpinia領域操作用の処理を提供する
 */
export namespace Or10320Logic {
  /**
   * initialize
   *
   * @description
   * コンポーネントの初期化処理。
   * 共通関数を呼びpiniaの領域を初期化する。
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or10320Const.CP_ID(0),
      uniqueCpId,
      childCps: [
        { cpId: Or21814Const.CP_ID(1) },
      ],
      editFlgNecessity: false,
    })

    // 子コンポーネントのセットアップ
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(1)].uniqueCpId) // 確認ダイアログ

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }
  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or10320StateType>(Or10320Const.CP_ID(0))
}
