<script setup lang="ts">
/**
 * Or15108：有機体：（確定版）入院基本情報 ８．お薬についてセクション
 *
 * <AUTHOR>
 */
import { computed, reactive, ref, type Ref } from 'vue'
import { useI18n } from 'vue-i18n'
import type { CodeType } from '../Or28326/Or28326.type'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { Or15108Const } from './Or15108.constants'
import type { Or15108ValuesType, Or15108OneWayType } from './Or15108.type'
import {
  useCommonProps,
  useScreenOneWayBind,
  useScreenTwoWayBind,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo00046OnewayType } from '~/types/business/components/Mo00046Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { CustomClass } from '~/types/CustomClassType'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Or51775OnewayType, Or51775Type } from '~/types/cmn/business/components/Or51775Type'
/**************************************************
 * Props
 **************************************************/
const systemCommonsStore = useSystemCommonsStore()
const props = defineProps(useCommonProps())
/**************************************************
 * Pinia
 **************************************************/
const { t } = useI18n()

const { refValue } = useScreenTwoWayBind<Or15108ValuesType>({
  cpId: Or15108Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
}) as { refValue: Ref<Or15108ValuesType> }
useScreenOneWayBind<Or15108OneWayType>({
  cpId: Or15108Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    codeList: (value) => {
      localOneway.codeListOneway = value as Record<string, CodeType[]>
    },
  },
})
/**************************************************
 * 変数定義
 **************************************************/

const localOneway = reactive({
  codeListOneway: {} as Record<string, CodeType[]>,
  // GUI00937 共通入力支援画面
  or51775: {
    screenId: 'GUI01300',
    bunruiId: '-', // 分類ID TBD
    t2Cd: '',
    t3Cd: '',
    tableName: 'cpn_tuc_hosp_info_teikyou_data',
    assessmentMethod: '共通情報.アセスメント方式', // アセスメント方式 TBD
    userId: systemCommonsStore.getUserId ?? '',
  } as Or51775OnewayType,
  mo00045Oneway: {
    maxlength: '20',
    width: '168px',
    isVerticalLabel: false,
    showItemLabel: false,
  } as Mo00045OnewayType,
  mo00046Oneway: {
    autoGrow: false,
    rows: '2',
    maxRows: '3',
    width: '1000px',
    maxlength: '4000',
    noResize: true,
  } as Mo00046OnewayType,
  mo00009Oneway: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  mo00039Oneway: {
    showItemLabel: false,
    inline: true,
    customClass: { outerClass: 'd-flex align-center' } as CustomClass,
  } as Mo00039OnewayType,
  //８．お薬についてセクション
  medicationTitle: {
    valueFontWeight: 'blod',
    value: t('label.medication_title'),
    customClass: {
      itemStyle: 'font-size:18px; !import',
    } as CustomClass,
  } as Mo01338OnewayType,
  medicationSpecialNotes: {
    itemLabelFontWeight: '400',
    itemLabel: t('label.medication_special_notes'),
    customClass: { outerClass: ' d-flex  background-transparent' } as CustomClass,
  } as Mo01338OnewayType,
})

/**
 * 共通入力支援画面の確定ボタン押す後の処理
 *
 * @param data - 入力支援情報
 */
const handleOr51775Confirm = (data: Or51775ConfirmType) => {
  const setOrAppendValue = (str: string, data: Or51775ConfirmType) => {
    if (data.type === Or15108Const.DEFAULT.ADD_END_TEXT_IMPORT_TYPE) {
      // 本文末に追加の場合
      return `${str}${data.value}`
    } else if (data.type === Or15108Const.DEFAULT.OVERWRITE_TEXT_IMPORT_TYPE) {
      // 本文上書の場合
      return data.value
    } else {
      return ''
    }
  }
  ;(refValue.value.or15108Values[local.or51775Value] as unknown as Mo00045Type).value =
    setOrAppendValue(
      (refValue.value.or15108Values[local.or51775Value] as unknown as Mo00045Type).value ?? '',
      data
    )
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: false },
  })
}
/**
 * 入力支援アイコンボタンクリック
 *
 * @param column
 *
 * @param title
 *
 * @param t2Cd
 *
 * @param t3Cd
 *
 * @param columnName
 */
const handPropUp = (
  column: string,
  title: string,
  t2Cd: string,
  t3Cd: string,
  columnName: string
) => {
  // GUI00937 共通入力支援画面をポップアップで起動する
  local.or51775Value = column
  localOneway.or51775.title = title
  localOneway.or51775.t2Cd = t2Cd
  localOneway.or51775.t3Cd = t3Cd
  localOneway.or51775.columnName = columnName
  localOneway.or51775.inputContents = title
  local.or51775.modelValue =
    (refValue.value.or15108Values[local.or51775Value] as unknown as Mo00045Type).value ?? ''
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const or51775 = ref({ uniqueCpId: '' }) // Or51775：有機体：入力支援［ケアマネ］モーダル

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or51775Const.CP_ID(1)]: or51775.value,
})
const local = reactive({
  or51775: { modelValue: '' } as Or51775Type,
  or51775Value: '',
  mo01343: {
    value: '',
    endValue: '',
    mo00024: { isOpen: false },
  },
  or15108: {},
})
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})
</script>

<template>
  <div v-if="refValue.or15108Values">
    <c-v-row class="title">
      <c-v-col>
        <base-mo01338 :oneway-model-value="localOneway.medicationTitle"></base-mo01338>
      </c-v-col>
    </c-v-row>

    <!-- 内服薬 -->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        <!--  <base-mo01338> -->
        {{ t('label.oral_medication') }}
      </c-v-col>
      <c-v-col
        cols="10"
        class="data-cell"
      >
        <div class="d-flex align-center">
          <!--  <base-mo00039> -->
          <base-mo00039
            v-model="refValue.or15108Values.drugUmu"
            :oneway-model-value="localOneway.mo00039Oneway"
          >
            <base-at-radio
              v-for="(item, index) in localOneway.codeListOneway.M_CD_KBN_ID_OMITTED"
              :key="'radio' + '_' + index"
              :name="item.label"
              :radio-label="item.label"
              :value="item.value"
            />
          </base-mo00039>

          <div class="d-flex align-center left">
            <c-v-divider
              class="ml-2"
              vertical
              inset
            />
            <base-mo00009
              :oneway-model-value="localOneway.mo00009Oneway"
              @click="
                handPropUp(
                  'drugMemoKnj',
                  t('label.oral_medication'),
                  Or15108Const.DEFAULT.T2_CD_2,
                  Or15108Const.DEFAULT.T3_CD_12,
                  'drug_memo_knj'
                )
              "
            />
          </div>
          <base-mo00045
            v-model="refValue.or15108Values.drugMemoKnj"
            :oneway-model-value="localOneway.mo00045Oneway"
          />
        </div>
      </c-v-col>
    </c-v-row>
    <!-- 居宅療養管理指導 -->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        <!--  <base-mo01338> -->
        {{ t('label.home_healthcare_management') }}
      </c-v-col>
      <c-v-col
        cols="10"
        class="data-cell"
      >
        <div class="d-flex align-center">
          <!--  <base-mo00039> -->
          <base-mo00039
            v-model="refValue.or15108Values.ryouyouKanriUmu"
            :oneway-model-value="localOneway.mo00039Oneway"
          >
            <base-at-radio
              v-for="(item, index) in localOneway.codeListOneway.M_CD_KBN_ID_OMITTED"
              :key="'radio' + '_' + index"
              :name="item.label"
              :radio-label="item.label"
              :value="item.value"
            />
          </base-mo00039>

          <div class="d-flex align-center left2">
            {{ t('label.job-type') }}
            <c-v-divider
              class="ml-2"
              vertical
              inset
            />
            <base-mo00009
              :oneway-model-value="localOneway.mo00009Oneway"
              @click="
                handPropUp(
                  'ryouyouKanriMemoKnj',
                  t('label.job-type'),
                  Or15108Const.DEFAULT.T2_CD_2,
                  Or15108Const.DEFAULT.T3_CD_13,
                  'ryouyou_kanri_memo_knj'
                )
              "
            />
          </div>
          <base-mo00045
            v-model="refValue.or15108Values.ryouyouKanriMemoKnj"
            :oneway-model-value="localOneway.mo00045Oneway"
          />
        </div>
      </c-v-col>
    </c-v-row>

    <!-- 薬剤管理-->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        <!--  <base-mo01338> -->
        {{ t('label.medication_management') }}
      </c-v-col>
      <c-v-col
        cols="10"
        class="data-cell"
      >
        <div class="d-flex align-center">
          <!--  <base-mo00039> -->
          <base-mo00039
            v-model="refValue.or15108Values.drugKanriKbn"
            :oneway-model-value="localOneway.mo00039Oneway"
          >
            <base-at-radio
              v-for="(item, index) in localOneway.codeListOneway.MEDICINE_MANAGEMENT"
              :key="'radio' + '_' + index"
              class="mr-7"
              :name="item.label"
              :radio-label="item.label"
              :value="item.value"
            />
          </base-mo00039>

          <div class="d-flex align-center left">
            {{ t('label.medication_management_manager_label') }}
            <c-v-divider
              class="ml-2"
              vertical
              inset
            />
            <base-mo00009
              :oneway-model-value="localOneway.mo00009Oneway"
              @click="
                handPropUp(
                  'drugKanriKanrisya',
                  t('label.medication_management_manager_label'),
                  Or15108Const.DEFAULT.T2_CD_2,
                  Or15108Const.DEFAULT.T3_CD_14,
                  'drug_kanri_kanrisya'
                )
              "
            />
          </div>
          <base-mo00045
            v-model="refValue.or15108Values.drugKanriKanrisya"
            :oneway-model-value="localOneway.mo00045Oneway"
          />
          <div class="d-flex align-center left">
            {{ t('label.medication_management_method_label') }}
            <c-v-divider
              class="ml-2"
              vertical
              inset
            />
            <base-mo00009
              :oneway-model-value="localOneway.mo00009Oneway"
              @click="
                handPropUp(
                  'drugKanriHouhou',
                  t('label.medication_management_method_label'),
                  Or15108Const.DEFAULT.T2_CD_2,
                  Or15108Const.DEFAULT.T3_CD_15,
                  'drug_kanri_houhou'
                )
              "
            />
          </div>
          <base-mo00045
            v-model="refValue.or15108Values.drugKanriHouhou"
            :oneway-model-value="localOneway.mo00045Oneway"
          />
        </div>
      </c-v-col>
    </c-v-row>

    <!-- 服薬状況-->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        <!--  <base-mo01338> -->
        {{ t('label.medication_adherence') }}
        <div class="d-flex">
          <c-v-divider
            vertical
            inset
          />
        </div>
      </c-v-col>
      <c-v-col
        cols="10"
        class="data-cell"
      >
        <!--  <base-mo00039> -->
        <div class="d-flex align-center">
          <!--  <base-mo00039> -->
          <base-mo00039
            v-model="refValue.or15108Values.drugJyoukyou"
            :oneway-model-value="localOneway.mo00039Oneway"
          >
            <base-at-radio
              v-for="(item, index) in localOneway.codeListOneway.TAKING_MEDICALTION_SITUATION"
              :key="'radio' + '_' + index"
              class="widthFix"
              :name="item.label"
              :radio-label="item.label"
              :value="item.value"
            />
          </base-mo00039>
        </div>
      </c-v-col>
    </c-v-row>
    <!-- お薬に関する特記事項-->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        <base-mo01338 :oneway-model-value="localOneway.medicationSpecialNotes"></base-mo01338>
        <div class="d-flex align-center">
          <c-v-divider
            vertical
            inset
          />
          <base-mo00009
            :oneway-model-value="localOneway.mo00009Oneway"
            @click="
              handPropUp(
                'drugTokkiKnj',
                t('label.medication_adherence'),
                Or15108Const.DEFAULT.T2_CD_2,
                Or15108Const.DEFAULT.T3_CD_16,
                'drug_tokki_knj'
              )
            "
          />
        </div>
      </c-v-col>
      <c-v-col
        cols="10"
        class="data-cell"
      >
        <base-mo00046
          v-model="refValue.or15108Values.drugTokkiKnj"
          :oneway-model-value="localOneway.mo00046Oneway"
        />
      </c-v-col>
    </c-v-row>
    <!-- Or51775: 有機体: GUI00937 入力支援［ケアマネ］をポップアップで起動する。 -->
    <g-custom-or51775
      v-if="showDialogOr51775"
      v-bind="or51775"
      v-model="local.or51775"
      :oneway-model-value="localOneway.or51775"
      @confirm="handleOr51775Confirm"
    />
  </div>
</template>

<style scoped lang="scss">
.row {
  display: flex;
  align-items: center;
  border-bottom: 1px gainsboro solid;
  border-left: 1px gainsboro solid;
  min-height: 62px;
}

.header-cell {
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 12px !important;
}

.header-title-cell {
  background-color: transparent;
  border-right: 1px gainsboro solid;
  display: grid;
  align-items: center;
}

.data-cell {
  border-left: 1px gainsboro solid;
  border-right: 1px gainsboro solid;
  background: #fff;
  padding: 10px 12px;
  width: 100%;
  min-height: 62px;
  display: grid;
  align-items: center;
}

:deep(.v-input__control) {
  background-color: rgb(var(--v-theme-surface));
}
:deep(.v-selection-control-group--inline) {
  align-items: center;
}
.flex-20 {
  flex: 0 0 20%;
  max-width: 20%;
}
.flex-80 {
  flex: 0 0 80%;
  max-width: 80%;
}
.flex-7 {
  flex: 0 0 7.333333%;
  max-width: 7.3333333333%;
}
.flex-56 {
  flex: 0 0 56%;
  max-width: 56%;
}
.mlr-10 {
  margin: 0 10%;
}
.left {
  margin-left: 4.9%;
}
.left2 {
  margin-left: 2.1%;
}
.title_font {
  font-size: 18px !important;
}
.title {
  margin-top: 12px;
  background-color: #fff;
  border-left: 1px gainsboro solid;
  border-right: 1px gainsboro solid;
  border-bottom: 1px gainsboro solid;
}
.widthFix {
  width: 45%;
}
</style>
