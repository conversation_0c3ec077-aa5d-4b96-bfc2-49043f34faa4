/**
 * Or52916:有機体:承認欄の複写画面
 * GUI00618_承認欄の複写画面
 *
 * @description
 * GUI00618_承認欄の複写画面
 *
 *  <AUTHOR>
 */
import { HttpResponse } from 'msw'
import defaultData from './data/default.json'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { CopyApprovalFieldSelectInEntity } from '~/repositories/cmn/entities/CopyApprovalFieldSelectEntity'

/**
 * GUI00618_承認欄の複写画面処理の取得APIモック
 *
 * @description
 * GUI00618_承認欄の複写画面処理のメイン画面に表示されるデータを返却する。
 * dataName："copyApprovalFieldSelect"
 */
export function handler(inEntity: CopyApprovalFieldSelectInEntity) {
  const responceJson: BaseResponseBody = {
    statusCode: 'success',
    data: {
      ...defaultData,
    },
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
