import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'

import type {
  displaySettingDataItem,
  displaySettingReqDataItem,
  jigyoListEntity,
} from '~/components/custom-components/organisms/Or52612/Or52612.type'
/**
 * Or52612:［表示設定］画面
 * GUI00625_［表示設定］画面
 *
 * @description
 * GUI00625_［表示設定］画面
 *
 * <AUTHOR>
 */
/** ［表示設定］画面入力エンティティ */
export interface displaySettingInEntity extends InWebEntity {
  /**
   *全体の判断
   */
  svJigyoId: string
  /**
   *事業所CDリスト
   */
  jigyoList: jigyoListEntity[]
  /**
   *職員ID
   */
  shokuinId: string
  /**
   *システムコード
   */
  sysCd: string
}

/**
 *［表示設定］画面出力エンティティ
 */
export interface displaySettingOutEntity extends OutWebEntity {
  /** data */
  data: {
    detailList: displaySettingDataItem[]
    /**
     * 宛先の初期値
     */
    updateType?: string
  }
}
/**
 *［表示設定］画面入力エンティティ
 */
export interface displaySettingReqOutEntity extends InWebEntity {
  /**
   *全体の判断
   */
  svJigyoId?: string
  /**
   *職員ID
   */
  shokuinId?: string
  /**
   *表示設定リスト
   */
  hyoujiSetList?: displaySettingReqDataItem[]
  /**
   * 宛先の初期値
   */
  defaultAtesaki?: string
  /**
   *システムコード
   */
  sysCd?: string
}
