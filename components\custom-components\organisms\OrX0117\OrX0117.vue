<script setup lang="ts">
/**
 * OrX0117:有機体:印刷設定帳票出力状態リスト
 *
 * @description
 * 計画期間
 *
 * <AUTHOR>
 */
import { reactive, ref, watch, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { OrX0117Const } from './OrX0117.constants'
import type { OrX0117StateType } from './OrX0117.type'
import { useNuxtApp, useScreenOneWayBind } from '#imports'
import type {
  Mo01334OnewayType,
  Mo01334Type,
  Mo01334Items,
} from '~/types/business/components/Mo01334Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Mo01344OnewayType } from '@/types/business/components/Mo01344Type'
import type {
  OrX0117TableHistory,
  OrX0117OnewayType,
} from '~/types/cmn/business/components/OrX0117Type'
import { OrX0117Logic } from '~/components/custom-components/organisms/OrX0117/OrX0117.logic'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { InWebEntity } from '@/repositories/AbstructWebRepository'
import { useReportUtils } from '~/utils/useReportUtils'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import type { DebugLogPluginInterface } from '~/plugins/debug-log'
import type { ReportOutputType } from '~/utils/useReportUtils'
const { reportOutput } = useReportUtils()
const systemCommonsStore = useSystemCommonsStore()
const $log = useNuxtApp().$log as DebugLogPluginInterface

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  onewayModelValue: OrX0117OnewayType
}

const props = defineProps<Props>()

/**************************************************
 * Pinia
 **************************************************/
useScreenOneWayBind<OrX0117StateType>({
  cpId: OrX0117Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? OrX0117Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * 変数定義
 **************************************************/
const mo01334Oneway = ref<Mo01334OnewayType>({
  headers: [
    /**
     * 利用者名
     */
    {
      title: t('label.user-name'),
      key: 'userName',
      sortable: false,
      minWidth: '200',
    },
    /**
     * 結果
     */
    {
      title: t('label.result'),
      key: 'status',
      sortable: false,
      minWidth: '200',
    },
  ],
  items: Array<Mo01334Items>(),
  height: '455px',
} as Mo01334OnewayType)

const defaultOnewayModelValue: OrX0117OnewayType = {
  type: '',
  historyList: [],
}

const localOneway = reactive({
  orX0117: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  mo00024Oneway: {
    width: '500px',
    height: '600px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'OrX0117',
      toolbarTitle: t('label.print-executioning'),
      toolbarName: 'OrX0117ToolBar',
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
      cardTextClass: 'OrX0117_content',
    } as Mo01344OnewayType,
  } as Mo00024OnewayType,
  /**
   * キャンセルボタン
   */
  mo00609Oneway: {
    btnLabel: t('btn.cancel'),
    disabled: true,
  } as Mo00609OnewayType,
  /**
   * 閉じるボタン
   */
  mo00611OneWay: {
    btnLabel: t('btn.close'),
    disabled: true,
  } as Mo00611OnewayType,
})

const mo00024 = ref<Mo00024Type>({
  isOpen: OrX0117Const.DEFAULT.IS_OPEN,
})

/**
 * 一覧
 */
const mo01334Type = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

/**
 * 中断フラゲ
 */
let chudanFlag = false

/**
 * 実行フラグ
 */
let executeFlag = false

/**
 * 履歴複数
 */
const historyList = ref<OrX0117TableHistory[]>([])

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  // 初期情報取得
  await init()
})

/**
 * 初期設定
 */
const initSetting = () => {
  chudanFlag = false
  executeFlag = false
  mo01334Oneway.value.items = []
  localOneway.mo00609Oneway.disabled = false
  localOneway.mo00611OneWay.disabled = true
}

/**
 * 初期情報取得
 */
const init = async () => {
  initSetting()

  reportDataInit()

  await printOutput()
}

/**
 * パラメータ処理
 */
const reportDataInit = () => {
  if (props.onewayModelValue) {
    if (props.onewayModelValue.historyList) {
      const userHukusuuList: Mo01334Items[] = []
      const historyHukusuuList: OrX0117TableHistory[] = []
      for (let i = 0; i < props.onewayModelValue.historyList.length; i++) {
        // テーブルデータを構築
        // 利用者複数
        if (props.onewayModelValue.type === OrX0117Const.DEFAULT.USER_HUKUSUU_TYPE) {
          const data: Mo01334Items = {
            id: String(i),
            mo01337OnewayUserName: {
              value: props.onewayModelValue.historyList[i].userName,
              unit: '',
            } as Mo01337OnewayType,
            mo01337OnewayStatus: {
              value: props.onewayModelValue.historyList[i].result ?? t('label.is-printing'),
              unit: '',
            } as Mo01337OnewayType,
            selectable: false,
            reportId: props.onewayModelValue.historyList[i].reportId,
            outputType: props.onewayModelValue.historyList[i].outputType,
            reportData: props.onewayModelValue.historyList[i].reportData,
          } as Mo01334Items
          userHukusuuList.push(data)
        }
        // その他
        else {
          historyHukusuuList.push({
            id: String(i),
            userName: props.onewayModelValue.historyList[i].userName,
            rowSpan:
              i === OrX0117Const.DEFAULT.NUMBER.ZERO
                ? props.onewayModelValue.historyList.length
                : OrX0117Const.DEFAULT.NUMBER.ZERO,
            historyDate: props.onewayModelValue.historyList[i].historyDate,
            status: props.onewayModelValue.historyList[i].result ?? t('label.is-printing'),
            reportId: props.onewayModelValue.historyList[i].reportId,
            outputType: props.onewayModelValue.historyList[i].outputType,
            reportData: props.onewayModelValue.historyList[i].reportData,
          } as OrX0117TableHistory)
        }
      }
      mo01334Oneway.value.items = userHukusuuList
      historyList.value = historyHukusuuList
    }
  }
}

/**
 * 帳票出力
 */
const printOutput = async () => {
  localOneway.mo00024Oneway.mo01344Oneway!.toolbarTitle = t('label.print-executioning')
  localOneway.mo00609Oneway.disabled = false
  localOneway.mo00611OneWay.disabled = true
  executeFlag = true

  // 利用者複数
  if (props.onewayModelValue.type === OrX0117Const.DEFAULT.USER_HUKUSUU_TYPE) {
    for (const item of mo01334Oneway.value.items) {
      // 中断
      if (chudanFlag) {
        return
      }
      if (item) {
        const obj = item.mo01337OnewayStatus as Mo01337OnewayType
        // statusがある場合は、出力しない
        if (
          t('label.invalid-assessment-kind') !== obj.value &&
          t('label.history-empty') !== obj.value
        ) {
          item.mo01337OnewayStatus = {
            value: t('label.is-printing'),
            unit: '',
          }

          try {
            // API処理失敗時のシステムエラーダイアログを非表示にする
            systemCommonsStore.setSystemErrorDialogType('hide')

            // 帳票出力
            const result = await reportOutput(
              item.reportId as string,
              item.reportData as InWebEntity,
              item.outputType as ReportOutputType
            )
            for (const data of mo01334Oneway.value.items) {
              if (item.id === data.id) {
                if (data.mo01337OnewayStatus && 'value' in data.mo01337OnewayStatus) {
                  if (result) {
                    data.mo01337OnewayStatus = {
                      value: t('label.print-end'),
                      unit: '',
                    } as Mo01337OnewayType
                  }
                }
              }
            }
          } catch (e) {
            $log.debug('帳票の出力に失敗しました。', item.reportId, item.outputType, e)
          } finally {
            // API処理失敗時のシステムエラーダイアログの表示タイプをデフォルトに戻す
            systemCommonsStore.setSystemErrorDialogType('details')
          }
        }
      }
    }
    localOneway.mo00024Oneway.mo01344Oneway!.toolbarTitle = t('label.print-execution-end')
  }
  // その他
  else {
    for (const item of historyList.value) {
      // 中断
      if (chudanFlag) {
        return
      }
      if (item) {
        // statusがある場合は、出力しない
        if (t('label.history-empty') !== item.status) {
          item.status = t('label.is-printing')

          try {
            // API処理失敗時のシステムエラーダイアログを非表示にする
            systemCommonsStore.setSystemErrorDialogType('hide')

            // 帳票出力
            const result = await reportOutput(item.reportId, item.reportData, item.outputType)
            for (const data of historyList.value) {
              if (item.id === data.id) {
                if (result) {
                  data.status = t('label.print-end')
                }
              }
            }
          } catch (e) {
            $log.debug('帳票の出力に失敗しました。', item.reportId, item.outputType, e)
          } finally {
            // API処理失敗時のシステムエラーダイアログの表示タイプをデフォルトに戻す
            systemCommonsStore.setSystemErrorDialogType('details')
          }
        }
      }
    }
    localOneway.mo00024Oneway.mo01344Oneway!.toolbarTitle = t('label.print-execution-end')
  }
  localOneway.mo00609Oneway.disabled = true
  localOneway.mo00611OneWay.disabled = false
  executeFlag = false
}

/**
 * キャンセル
 */
const cancel = () => {
  chudanFlag = true
  executeFlag = false
  localOneway.mo00609Oneway.disabled = true
  localOneway.mo00611OneWay.disabled = false
}

/**
 * 閉じる
 */
const close = () => {
  // OrX0117のダイアログ開閉状態を更新する
  OrX0117Logic.state.set({
    uniqueCpId: props.uniqueCpId,
    state: {
      isOpen: false,
    },
  })
}

/**
 * イベントリスナーの解除
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      close()
      if (executeFlag) {
        mo00024.value.isOpen = true
        return
      }
      mo01334Oneway.value.items = []
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <base-mo-01334
        v-if="props.onewayModelValue.type === OrX0117Const.DEFAULT.USER_HUKUSUU_TYPE"
        v-model="mo01334Type"
        :oneway-model-value="mo01334Oneway"
        class="list-wrapper"
      >
        <!-- 利用者名 -->
        <template #[`item.userName`]="{ item }">
          <!-- 分子：一覧専用ラベル（文字列型） -->
          <base-mo01337 :oneway-model-value="item.mo01337OnewayUserName" />
        </template>
        <!-- 結果 -->
        <template #[`item.status`]="{ item }">
          <!-- 分子：一覧専用ラベル（文字列型） -->
          <base-mo01337 :oneway-model-value="item.mo01337OnewayStatus" />
        </template>
        <!-- ページングを非表示 -->
        <template #bottom />
      </base-mo-01334>
      <table
        v-else
        style="border-collapse: collapse; width: 450px"
      >
        <thead>
          <tr>
            <th class="customThClass">{{ t('label.user-name') }}</th>
            <th class="customThClass">{{ t('label.result') }}</th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="(item, index) in historyList"
            :key="index"
          >
            <td
              v-if="OrX0117Const.DEFAULT.NUMBER.ZERO !== item.rowSpan"
              :rowspan="item.rowSpan"
              class="customTdClass"
            >
              {{ item.userName }}
            </td>
            <td class="customTdClass">
              {{ item.historyDate + OrX0117Const.DEFAULT.STR.SPLIT_COLON + item.status }}
            </td>
          </tr>
        </tbody>
      </table>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row>
        <c-v-spacer />
        <!-- キャンセルボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          @click="cancel()"
        >
        </base-mo00609>
        <!-- 閉じるボタン -->
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          class="mx-2"
          @click="close"
        >
        </base-mo00611>
      </c-v-row>
    </template>
  </base-mo00024>
</template>
<style scoped lang="scss">
:deep(.v-data-table__td) {
  border-left: thin solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
  border-bottom: thin solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
}

.customTdClass {
  height: 36px;
  border: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  padding: 0 16px;
  align-content: start;
}

.customThClass {
  font-weight: 500;
  height: 37px;
  width: 50px;
  border: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  background: rgb(var(--v-theme-black-100));
  padding: 0 8px;
}
</style>
