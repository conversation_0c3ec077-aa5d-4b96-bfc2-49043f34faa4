import { getSequencedCpId } from '#imports'

/**
 * Or32406:支援経過記録画面
 * GUI01258_支援経過記録
 *
 * @description
 * 静的データ
 *
 * <AUTHOR> NGUYEN NHUT THANH
 */
export namespace Or32406Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or32406', seq)

  /**
   * 全
   */
  export const STR_ALL = '全'

  /**
   * デフォルト値
   */
  export namespace DEFAULT {
    /**
     * 事業所コード
     */
    export const SYSCD = 'CMN'
    /**
     * 事業所コード
     */
    export const JIGYOSHOCD = '50010'
    /**
     * 事業所名
     */
    export const DEFAULT_0 = '0'
    /**
     * デフォルト値
     */
    export const DEFAULT_1 = '1'
    /**
     * デフォルト値（未定義）
     */
    export const DEFAULT_UNDEFINED = undefined
  }
}
