<script setup lang="ts">
/**
 * Or12336:課題立案区分一覧
 * GUI00903_課題立案区分マスタ
 *
 * @description
 * 課題立案区分一覧
 *
 * <AUTHOR>
 */
import {
  computed,
  reactive,
  ref,
  watch,
  nextTick,
  onUnmounted,
  type ComponentPublicInstance,
} from 'vue'
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'
import type { TableData } from './Or12336.type'
import { Or12336Const } from './Or12336.constants'
import type {
  Or12336Type,
} from '~/types/cmn/business/components/Or12336Type'
import { UPDATE_KBN } from '~/constants/classification-constants'
import { useScreenTwoWayBind } from '~/composables/useComponentVue'
import { useValidation } from '~/utils/useValidation'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { useScreenStore } from '#imports'
import type { Mo01354Type } from '~/components/base-components/molecules/Mo01354/Mo01354Type'
import type { ResizableGridBinding } from '~/plugins/resizableGrid.client'

const { t } = useI18n()
const validation = useValidation()
/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or12336Type
  uniqueCpId: string
  parentUniqueCpId: string
}

const props = defineProps<Props>()

const defaultModelValue: Or12336Type = {
  delBtnDisabled: false,
  focusIndex: '',
  focusType: '',
  issuesPlanningCategoryMasterInfoList: [],
}

// テーブルヘッダ
const headers = [
  {
    title: t('label.category-number'),
    key: 'kbnCd',
    sortable: false,
    required: true,
  },
  { title: t('label.content'), key: 'textKnj', sortable: false, required: true },
]

const localOneWay = reactive({
  mo01278Oneway: {
    maxLength: Or12336Const.DEFAULT.MAX_LENGTH_FOUR,
    min: 0,
    max: 9999,
    isEditCamma: false,
    isRequired: true,
    showItemLabel: false,
    rules: [
      validation.required,
      validation.numeric,
      validation.minValue(1000),
      validation.maxValue(9999),
    ],
  },
  mo01274Oneway: {
    maxLength: Or12336Const.DEFAULT.MAX_LENGTH_THIRTY,
    isRequired: true,
    showItemLabel: false,
    rules: [validation.required],
  },
  mo01354Oneway: {
    columnMinWidth: {
      columnWidths: [275, 530],
    } as ResizableGridBinding,
    headers: headers,
    height: '212px',
  },
})

const local = reactive({
  Or12336: {
    ...defaultModelValue,
    ...props.modelValue,
  },
})

const or1354Data = ref<Mo01354Type>({
  values: {
    selectedRowId: '1',
    selectedRowIds: [],
    items: [],
  },
})
/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<TableData[]>({
  cpId: Or12336Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
refValue.value = []
/**************************************************
 * 変数定義
 **************************************************/
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(1) })
// ダイアログ表示フラグ
const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

// 区分番号リスト
const kbnCdList = ref<string[]>([])

const regex = /\d$/

// 元のテーブルデータ
const orgTableData = ref<TableData[]>()
// テーブルデータ
const tableDataFilter = computed(() => {
  const titleList = refValue.value
  return titleList!.filter((i: TableData) => i.updateKbn !== UPDATE_KBN.DELETE)
})

const itemRefs = new Map<string, HTMLElement>()
const textRefs = new Map<string, HTMLElement>()
const tableDiv = ref<HTMLElement>()
const tableWarpperDiv = ref<HTMLElement>()

const setItemRef = (el: Element | ComponentPublicInstance | null, id: string, type: boolean) => {
  const elHtml: HTMLElement = el as HTMLElement
  if (el) {
    if (type) {
      itemRefs.set(id, elHtml)
    } else {
      textRefs.set(id, elHtml)
    }
  }
}

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])
/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * 課題立案区分マスタ情報取得
 *
 */
async function init() {
  // 戻り値はテーブルデータとして処理されます
  let tmpArr = []
  tmpArr = []
  kbnCdList.value = []
  let i = 1
  for (const item of local.Or12336.issuesPlanningCategoryMasterInfoList) {
    tmpArr.push({
      id: '' + i++,
      kbnCd: { value: item.kbnCd },
      textKnj: { value: item.textKnj },
      cf1Id: item.cf1Id,
      modifiedCnt: item.modifiedCnt,
      kbnFlg: item.kbnFlg,
      updateKbn: UPDATE_KBN.NONE,
    })
    kbnCdList.value.splice(tmpArr.length, 0, item.kbnCd)
  }
  // 元のテーブルデータの設定
  orgTableData.value = cloneDeep(tmpArr)

  // APIから取得されたデータでRefValueを更新する
  useScreenStore().setCpTwoWay({
    cpId: Or12336Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: tmpArr,
    isInit: true,
  })
  if (tmpArr.length > 0) {
    // 行削除活性
    local.Or12336.delBtnDisabled = true
    emit('update:modelValue', local.Or12336)
  }
  await nextTick()
  or1354Data.value.values.selectedRowId = '1'
  if (tableWarpperDiv.value) {
    return
  }
  const warpperDiv = tableDiv.value!.querySelector('.v-table__wrapper')!
  tableWarpperDiv.value = warpperDiv as HTMLElement
}

/**
 * 入力した区分番号が半角数字以外の場合、変更前の区分番号に戻す。
 *
 * @param id - 行Id
 */
const cdBulr = (id: string) => {
  const index = tableDataFilter.value.findIndex((item) => item.id === id)
  const item = tableDataFilter.value[index]
  if (item.kbnCd.value === '') {
    return
  }
  const kbnCd = kbnCdList.value[index]
  if (!regex.test(item.kbnCd.value)) {
    tableDataFilter.value[index].kbnCd.value = kbnCd ? kbnCd : ''
  } else if (item.kbnCd.value.length === 4) {
    kbnCdList.value.splice(index, 1, item.kbnCd.value)
  }
}

/**
 * 行選択
 */
watch(
  () => or1354Data.value.values.selectedRowId,
  (newValue) => {
    if (!newValue) {
      return
    }
    // 行削除活性
    local.Or12336.delBtnDisabled = true
    emit('update:modelValue', local.Or12336)
  }
)

/**
 * 「行追加」押下
 */
async function createRow() {
  // 最終に新しい行を追加する。
  const data = {
    id: refValue.value?.length
      ? Math.max(...refValue.value.map((i) => parseInt(i.id))) + 1 + ''
      : '1',
    // 区分番号：空白
    kbnCd: { value: '' },
    // 内容：空白
    textKnj: { value: '' },
    // 入力ID
    cf1Id: '',
    // 更新回数
    modifiedCnt: '',
    // 更新区分
    updateKbn: UPDATE_KBN.CREATE,
    // 区分フラグ
    kbnFlg: '',
  }
  refValue.value!.push(data)
  or1354Data.value.values.selectedRowId = data.id
  await nextTick()
  setFocus(data.id, true)
}

/**
 * 行削除ボタン押下
 */
function deleteRow() {
  if (or1354Data.value.values.selectedRowId) {
    // メッセージを表示
    // 確認ダイアログのpiniaを設定
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: {
        // ダイアログタイトル
        dialogTitle: t('label.confirm-dialog-title-info'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-11397'),
        // 第1ボタンタイプ
        firstBtnType: 'normal1',
        // 第1ボタンラベル
        firstBtnLabel: t('btn.yes'),
        // 第2ボタンタイプ
        secondBtnType: 'destroy1',
        // 第2ボタンラベル
        secondBtnLabel: t('btn.no'),
        // 第3ボタンタイプ
        thirdBtnType: 'blank',
        iconColor: 'rgb(var(--v-theme-blue-700))',
        iconBackgroundColor: 'rgb(var(--v-theme-blue-200))',
      },
    })
    // 確認ダイアログ表示
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: { isOpen: true },
    })
  }
}

/**
 * 行複写ボタン押下
 */
async function copyRow() {
  if (or1354Data.value.values.selectedRowId) {
    let kbnCd = ''
    let textKnj = ''
    let index = 0
    tableDataFilter.value.forEach((item: TableData, i) => {
      if (item.id === or1354Data.value.values.selectedRowId) {
        kbnCd = item.kbnCd.value
        textKnj = item.textKnj.value
        index = i
      }
    })
    // 最終に新しい行を追加する。
    const data = {
      id: Math.max(...refValue.value!.map((i) => parseInt(i.id))) + 1 + '',
      // 区分番号
      kbnCd: { value: kbnCd },
      // 内容
      textKnj: { value: textKnj },
      // 入力ID
      cf1Id: '',
      // 更新回数
      modifiedCnt: '',
      // 更新区分
      updateKbn: UPDATE_KBN.CREATE,
      // 区分フラグ
      kbnFlg: '',
    }

    refValue.value!.splice(++index, 0, data)
    or1354Data.value.values.selectedRowId = data.id
    await nextTick()
    setFocus(data.id, true)
  }
}

/**
 * フォーカス設定
 *
 * @param id - 行id
 *
 * @param type - true: 番号入力/false: 内容入力
 */
const setFocus = (id: string, type: boolean) => {
  if (!id) {
    return
  }
  if (tableDataFilter.value.length > 4) {
    tableWarpperDiv.value!.scrollTop = tableWarpperDiv.value!.scrollTop - 32
  }
  const inputElement = type
    ? itemRefs.get(id)!.querySelector(`#input-${id} input`)!
    : textRefs.get(id)!.querySelector(`#text-${id} input`)!
  const inputHtmlElement = inputElement as HTMLElement
  inputHtmlElement.focus()
}

/**
 * データ変更処理
 *
 * @param tableData - 行情報
 */
const dataChange = (tableData: TableData) => {
  tableData.updateKbn = UPDATE_KBN.UPDATE
}
/**************************************************
 * ワッチャー
 **************************************************/

/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    // 'はい'
    if (newValue.firstBtnClickFlg) {
      let rowId = ''
      refValue.value!.forEach((item: TableData, index: number) => {
        if (or1354Data.value.values.selectedRowId === item.id) {
          // 更新前後配列長さ
          const newLength = tableDataFilter.value.length ?? 0
          if (newLength > 1) {
            // 削除するデータは配列の最後にあります
            if (item.id === tableDataFilter.value[newLength - 1].id) {
              rowId = tableDataFilter.value[newLength - 2].id
            } else {
              const idx = tableDataFilter.value.findIndex((sItem) => item.id === sItem.id)
              // 削除するデータは配列の最後にありません
              rowId = tableDataFilter.value[idx + 1].id
            }
          }
          // 新規行の場合
          if (item.updateKbn === UPDATE_KBN.CREATE) {
            // 当該行を廃棄する
            refValue.value?.splice(index, 1)
          } else {
            // 既存行の場合
            item.updateKbn = UPDATE_KBN.DELETE
          }
        }
      })
      // 一行だけ
      if (or1354Data.value.values.items.length === 1) {
        or1354Data.value.values.selectedRowId = ''
        // 行削除非活性
        local.Or12336.delBtnDisabled = false
        emit('update:modelValue', local.Or12336)
        return
      }
      // 選択行再設定
      or1354Data.value.values.selectedRowId = rowId
      await nextTick()
      // フォーカス
      setFocus(rowId, true)
    } else {
      return
    }
  }
)

/**
 * エラー行のフォーカス位置に戻す
 */
watch(
  () => local.Or12336.focusIndex,
  async (newValue) => {
    if (!newValue) {
      return
    }
    local.Or12336.focusType = props.modelValue.focusType
    const row = refValue.value![Number(newValue)]
    or1354Data.value.values.selectedRowId = row.id
    await nextTick()
    if (local.Or12336.focusType === Or12336Const.DEFAULT.KBN_CD) {
      setFocus(row.id, true)
    } else {
      setFocus(row.id, false)
    }
  }
)
/**
 * テーブルデータの更新
 */
watch(
  () => tableDataFilter.value,
  (newValue) => {
    or1354Data.value.values.items = newValue
  },
  { deep: true }
)

/**
 * 一覧データ変更監視
 */
watch(
  () => props.modelValue.issuesPlanningCategoryMasterInfoList,
  (newValue) => {
    if (!newValue) return
    local.Or12336.issuesPlanningCategoryMasterInfoList = cloneDeep(newValue)
  },
  { deep: true }
)

onUnmounted(() => {
  itemRefs.clear()
  textRefs.clear()
})

defineExpose({
  createRow,
  copyRow,
  deleteRow,
  init,
})
</script>

<template>
  <!-- マスタ一覧 -->
  <div>
    <div
      ref="tableDiv"
      class="table-header"
    >
      <base-mo-01354
        v-model="or1354Data"
        :oneway-model-value="localOneWay.mo01354Oneway"
        class="list-wrapper d-flex"
      >
        <template #[`item.kbnCd`]="{ item }">
          <div
            :ref="(el) => setItemRef(el, item.id, true)"
            class="h-100"
          >
            <base-mo00045
              :id="`input-${item.id}`"
              v-model="item.kbnCd"
              class="background-transparent"
              :oneway-model-value="localOneWay.mo01278Oneway"
              @blur="() => cdBulr(item.id)"
              @update:model-value="dataChange(item)"
            />
          </div>
        </template>
        <template #[`item.textKnj`]="{ item }">
          <div
            :ref="(el) => setItemRef(el, item.id, false)"
            class="h-100"
          >
            <base-mo00045
              :id="`text-${item.id}`"
              v-model="item.textKnj"
              class="background-transparent"
              :oneway-model-value="localOneWay.mo01274Oneway"
              @update:model-value="dataChange(item)"
            />
          </div>
        </template>
        <!-- ページングを非表示 -->
        <template #bottom />
      </base-mo-01354>
    </div>
  </div>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  >
  </g-base-or21814>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table.scss';

.h-100 {
  margin: 3px 1px 1px 1px !important;
  :deep(.v-sheet.mr-2) {
    margin: 0 !important;
  }
}
:deep(.full-width-field) {
  padding: 0 12px !important;
}
:deep(tr:first-child td) {
  padding-top: 30px;
}

:deep(.v-data-table) {
  width: auto !important;
}

// transparent
.background-transparent {
  background-color: transparent !important;
}
</style>
