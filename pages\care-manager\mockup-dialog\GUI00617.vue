<script setup lang="ts">
import { computed, definePageMeta, ref, useScreenStore } from '#imports'
import { OrX0135Logic } from '~/components/custom-components/organisms/OrX0135/OrX0135.logic'

import type { OrX0135OnewayType } from '~/types/cmn/business/components/OrX0135Type'
definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI00617'
// ルーティング
const routing = 'GUI00617/pinia'
// 画面物理名
const screenName = 'GUI00617'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const orX0135 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00617' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  OrX0135Logic.initialize(pageComponent.uniqueCpId)
}
// 子コンポーネントのユニークIDを設定する
orX0135.value.uniqueCpId = pageComponent.uniqueCpId

/**************************************************
 * コンポーネント固有処理
 **************************************************/
const orX0135Data: OrX0135OnewayType = {
  // ★ｻｰﾋﾞｽ事業者ID
  svJigyoId: '1',
  houjinId: '1',
  shisetuId: '1',
  chohyoCd: '',
}

/**
 *  ボタン押下時の処理
 *
 */
function OnClick() {
  // Orx0135のダイアログ開閉状態を更新する
  OrX0135Logic.state.set({
    uniqueCpId: orX0135.value.uniqueCpId,
    state: { isOpen: true },
  })
}
// ダイアログ表示フラグ
const showDialog = computed(() => {
  // Orx0135のダイアログ開閉状態
  return OrX0135Logic.state.get(orX0135.value.uniqueCpId)?.isOpen ?? false
})
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="OnClick"
        >GUI00617_承認欄登録画面
      </v-btn>
      <g-custom-or-x0135
        v-if="showDialog"
        v-bind="orX0135"
        :oneway-model-value="orX0135Data"
        :parent-unique-cp-id="pageComponent.uniqueCpId"
      />
    </c-v-col>
  </c-v-row>
</template>
