<script setup lang="ts">
/**
 * Or57074:有機体:印刷設定
 * GUI01232_印刷設定
 *
 * @description
 * 実施モニタリング
 *
 * <AUTHOR>
 */ import { useI18n } from 'vue-i18n'
import { reactive, ref, onMounted, watch, computed } from 'vue'
import { Or10016Const } from '../Or10016/Or10016.constants'
import { Or10016Logic } from '../Or10016/Or10016.logic'
import { OrX0135Const } from '../OrX0135/OrX0135.constants'
import { OrX0135Logic } from '../OrX0135/OrX0135.logic'
import { Or57074Const } from '../Or57074/Or57074.constants'
import type { Or57074StateType, Or57074MsgBtnType, Or57074Param } from './Or57074.type'
import { useSetupChildProps, useScreenOneWayBind, useNuxtApp, dateUtils } from '#imports'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01338OnewayType } from '@/types/business/components/Mo01338Type'
import type { Mo01344OnewayType } from '@/types/business/components/Mo01344Type'
import type { Mo00039Items, Mo00039OnewayType } from '@/types/business/components/Mo00039Type'
import type { Mo00020Type, Mo00020OnewayType } from '@/types/business/components/Mo00020Type'
import type { Mo00018Type, Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { CustomClass } from '~/types/CustomClassType'
import type {
  UserEntity,
  PeriodHistoryEntity,
  PrtEntity,
  PlanMonitoringPrintSettingsUpdateInEntity,
  PlanMonitoringPrintSettingsUpdateOutEntity,
  PlanMonitoringPrintSettingsHistorySelectInEntity,
  PlanMonitoringPrintSettingsUserChangeSelectInEntity,
  PlanMonitoringPrintSettingsUserChangeSelectOutEntity,
  PlanMonitoringPrintSettingsHistorySelectOutEntity,
} from '~/repositories/cmn/entities/PlanMonitoringPrintSettingsUpdateEntity'
import type {
  SysIniInfoEntity,
  IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity,
  IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateOutEntity,
} from '~/repositories/cmn/entities/FreeAssessmentFacePrintSettingsPrtNoChangeUpdateEntity'
import type {
  IFreeAssessmentFacePrintSettingsUpdateInEntity,
  IFreeAssessmentFacePrintSettingsUpdateOutEntity,
} from '~/repositories/cmn/entities/FreeAssessmentFacePrintSettingsUpdateEntity'
import type {
  Mo01334OnewayType,
  Mo01334Type,
  Mo01334Items,
} from '~/types/business/components/Mo01334Type'
import { OrX0117Const } from '~/components/custom-components/organisms/OrX0117/OrX0117.constants'
import { OrX0117Logic } from '~/components/custom-components/organisms/OrX0117/OrX0117.logic'
import type { OrX0117History, OrX0117OnewayType } from '~/types/cmn/business/components/OrX0117Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { Or21814OnewayType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { OrX0128Const } from '~/components/custom-components/organisms/OrX0128/OrX0128.constants'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import { OrX0130Logic } from '~/components/custom-components/organisms/OrX0130/OrX0130.logic'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import type {
  OrX0128OnewayType,
  OrX0128Items,
  OrX0128Headers,
} from '~/types/cmn/business/components/OrX0128Type'
import { OrX0128Logic } from '~/components/custom-components/organisms/OrX0128/OrX0128.logic'

import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { useReportUtils, reportOutputType } from '~/utils/useReportUtils'
import type {
  PrintSetEntity,
  PrintOptionEntity,
  PrintSubjectHistoryEntity,
  ChoPrtEntity,
  ICpnTucRaiAssReportSelectInEntity,
} from '~/repositories/cmn/entities/CpnTucRaiAssReportSelectEntity'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { DebugLogPluginInterface } from '~/plugins/debug-log'
import { useCmnCom } from '@/utils/useCmnCom'
import { useCmnRouteCom } from '~/utils/useCmnRouteCom'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'

import type { Mo00045Type, Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Mo00040OnewayType, Mo00040Type } from '~/types/business/components/Mo00040Type'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
import { OrX0145Const } from '~/components/custom-components/organisms/OrX0145/OrX0145.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import type { Or21813StateType } from '~/components/base-components/organisms/Or21813/Or21813.type'
import type { Or21815StateType } from '~/components/base-components/organisms/Or21815/Or21815.type'
import type { Or10016OnewayType } from '~/types/cmn/business/components/Or10016Type'
import type { OrX0135OnewayType } from '~/types/cmn/business/components/OrX0135Type'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'

const systemCommonsStore = useSystemCommonsStore()
const cmnRouteCom = useCmnRouteCom()
const { reportOutput } = useReportUtils()
const { convertDateToSeireki } = dateUtils()
const $log = useNuxtApp().$log as DebugLogPluginInterface

const { t } = useI18n()
/************************************************
 * Props
 ************************************************/
interface Props {
  uniqueCpId: string
}

const props = defineProps<Props>()

// 子コンポーネント用変数
const or21815 = ref({ uniqueCpId: '' })
const orX0117 = ref({ uniqueCpId: '' })
const orX0128 = ref({ uniqueCpId: '' })
const orX0130 = ref({ uniqueCpId: '' })
const or21814 = ref({ uniqueCpId: '' })
const or21813 = ref({ uniqueCpId: '' })
const orx0145 = ref({ uniqueCpId: '' })

const or10016_1 = ref({ uniqueCpId: '' })
const orX0135_1 = ref({ uniqueCpId: '' })

const localOneway = reactive({
  /**
   * 基本設定
   */
  mo01338OneWayBasicSettings: {
    value: t('label.basic-settings'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00611Oneway2: {
    btnLabel: t('btn.seal-column'),
    labelColor: 'rgb(var(--v-theme-key))',
    borderColor: 'rgb(var(--v-theme-key))',
  } as Mo00611OnewayType,
  /**
   * 帳票タイトル
   */
  mo00045OneWay: {
    itemLabel: t('label.title'),
    showItemLabel: true,
    isVerticalLabel: false,
    width: '334',
  } as Mo00045OnewayType,

  /**
   * 日付印刷区分
   */
  mo00039OneWay: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  /**
   * 指定日
   */
  mo00020OneWay: {
    showItemLabel: false,
    showSelectArrow: false,
    mo00009OnewayBack: {},
    mo00009OnewayForward: {},
  } as Mo00020OnewayType,
  /**
   * 敬称を変更する
   */
  mo00018OneWayTitleOfHonorFlg: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.honorifics-change'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /** 敬称 */
  mo00045OneWayTitleOfHonor: {
    itemLabel: '',
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
    width: '60',
    disabled: false,
  } as Mo00045OnewayType,
  /**
   * 記入用シートを印刷する
   */
  mo00018OneWayEmptyFlg: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-entry-sheet'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
   * 一覧印刷する
   */
  mo00018OneWayListPrintFlg: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-list'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
   * 承認欄を印刷する
   */
  mo00018OneWayApprovalPrintFlg: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.confirm-form-copying'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
   * 承認欄の登録ボタン
   */
  mo00611OneWayApproval: {
    btnLabel: t('btn.confirm-form-registration‌'),
    width: '110px',
    minWidth: '110px',
    disabled: false,
  } as Mo00611OnewayType,
  /**
   * 総括部分を印刷しない
   */
  mo00018OneWaySummaryPrintFlg: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.print-summary-part'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
   * 再アセスメントの必要性を印刷しない
   */
  mo00018OneWayReassessmentPrintFlg: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.assessment-necessity-no-print'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,

  /**
   * 印刷する要介護度
   */
  mo00615OneWayKaigodo: {
    itemLabel: t('label.print-nursing-care-required'),
    showItemLabel: true,
    isVerticalLabel: true,
    isRequired: false,
    hideDetails: 'auto',
    width: '64',
    disabled: false,
  } as Mo00615OnewayType,
  /**
   * 印刷する要介護度
   */
  mo00040OneWayKaigodo: {
    showItemLabel: false,
    items: [],
    itemTitle: 'label',
    itemValue: 'value',
    hideDetails: true,
    width: '280px',
  } as Mo00040OnewayType,

  /**
   * 印刷オプション
   */
  mo01338OneWayPrinterOption: {
    value: t('label.printer-option'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 利用者選択ラベル
   */
  mo01338OneWayPrinterUserSelect: {
    value: t('label.printer-user-select'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 履歴選択ラベル
   */
  mo01338OneWayPrinterHistorySelect: {
    value: t('label.printer-history-select'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 利用者選択
   */
  mo00039OneWayUserSelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  /**
   * 履歴選択
   */
  mo00039OneWayHistorySelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  /**
   * 基準日
   */
  mo00615OneWayType: {
    itemLabel: t('label.base-date'),
    showItemLabel: true,
  } as Mo00615OnewayType,
  /**
   * 基準日: 表用テキストフィールド
   */
  mo00020KijunbiOneWay: {
    showItemLabel: false,
    showSelectArrow: true,
    width: '132px',
    totalWidth: '220px',
    mo00009OnewayBack: {},
    mo00009OnewayForward: {},
  } as Mo00020OnewayType,
  /**
   * 担当ケアマネプルダウン
   */
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: true,
    multiple: false,
    selectedUserCounter: '2',
  } as OrX0145OnewayType,
  /**
   * 閉じるボタン
   */
  mo00611OneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  /**
   * PDFダウンロードボタン
   */
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
    disabled: false,
  } as Mo00609OnewayType,
  /**
   *  改訂区分
   */
  kaiteiKbnList: [] as { label: string; value: string }[],
  /**
   * モニタリングフラグ種別
   */
  monitorFlagList: [] as { label: string; value: string }[],
  /**
   * 「種別」プルダウンリスト
   */
  mo00040OneWayMonitorFlag: {
    showItemLabel: false,
    items: [] as { label: string; value: string }[],
    itemTitle: 'label',
    itemValue: 'value',
    hideDetails: true,
    width: '160px',
    disabled: false,
  },

  or10016Oneway: {} as Or10016OnewayType,
  orX0135Oneway: {} as OrX0135OnewayType,
})

/**
 * 親画面の情報
 */
const local = {
  /**
   * 個人情報使用フラグ
   */
  kojinhogoUsedFlg: Or57074Const.DEFAULT.KOJINHOGO_USED_FLG,
  /**
   * 個人情報番号
   */
  sectionAddNo: Or57074Const.DEFAULT.SECTION_ADD_NO,
  /**
   * 親画面.法人ID
   */
  houjinId: Or57074Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.事業所ID
   */
  svJigyoId: Or57074Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.施設ID
   */
  shisetuId: Or57074Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.担当者ID
   */
  tantoId: Or57074Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.種別ID
   */
  syubetsuId: Or57074Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.セクション名
   */
  sectionName: Or57074Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.利用者ID
   */
  userId: Or57074Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.事業所名
   */
  svJigyoKnj: Or57074Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.処理年月日
   */
  processYmd: Or57074Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.ヘッダID
   */
  cmoni1Id: Or57074Const.DEFAULT.STR.EMPTY,
  /**
   * 選択された帳票のプロファイル
   */
  profile: Or57074Const.DEFAULT.STR.EMPTY,
  /**
   * 選択利用者ID
   */
  selectUserId: Or57074Const.DEFAULT.STR.EMPTY,
  /**
   * 出力帳票名一覧に選択行番号
   */
  index: Or57074Const.DEFAULT.STR.EMPTY,
  /**
   * システムINI情報
   */
  sysIniInfo: {} as SysIniInfoEntity,
  /**
   * 履歴一覧が0件選択の場合（※履歴リストが0件、複数件を含む）
   */
  historyNoSelect: false,
  /**
   * 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
   */
  userNoSelect: false,
  /**
   * 帳票番号
   */
  prtNo: Or57074Const.DEFAULT.STR.EMPTY,
  /**
   * 選択利用者リスト
   */
  userList: [] as UserEntity[],
  /**
   * 帳票ID
   */
  reportId: Or57074Const.DEFAULT.STR.EMPTY,
  /**
   * 履歴選択の明細
   */
  orX0128DetList: [] as OrX0128Items[],
  /**
   * 印刷設定情報リスト
   */
  prtList: [] as PrtEntity[],
  /** 画面.敬称 */
  titleOfHonor: {
    value: Or57074Const.DEFAULT.STR.EMPTY,
  } as Mo00045Type,

  mo00040TypeKaigodo: {
    modelValue: Or57074Const.DEFAULT.STR.EMPTY,
  } as Mo00040Type,
  mo00040TypeMonitorFlag: {
    modelValue: Or57074Const.DEFAULT.STR.EMPTY,
  } as Mo00040Type,
  mo00045ModelTitle: {
    value: '',
  },
  orX0135Type: {},
}

/**
 * レスポンスパラメータ詳細
 */
const localData: PlanMonitoringPrintSettingsUpdateOutEntity = {
  data: {},
} as PlanMonitoringPrintSettingsUpdateOutEntity
/**************************************************
 * 変数定義
 **************************************************/
// ダイアログ
const mo00024Oneway = ref<Mo00024OnewayType>({
  width: '1300px',
  persistent: true,
  showCloseBtn: true,
  mo01344Oneway: {
    name: 'Or57074',
    toolbarTitle: t('label.print-set'),
    toolbarName: 'Or57074ToolBar',
    toolbarTitleCenteredFlg: false,
    showCardActions: true,
    cardTextClass: 'or57074_content',
  } as Mo01344OnewayType,
})

const mo00024 = ref<Mo00024Type>({
  isOpen: Or57074Const.DEFAULT.IS_OPEN,
})

/**
 * 出力帳票名一覧
 */
const mo01334Oneway = ref<Mo01334OnewayType>({
  headers: [
    {
      title: t('label.ledger'),
      key: 'ledgerName',
      sortable: false,
      minWidth: '100',
    },
  ],
  items: [],
  height: 655,
})

/**
 * 出力帳票名一覧
 */
const mo01334Type = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

/**
 * 日付印刷区分
 */
const mo00039Type = ref<string>('')

/**
 * 指定日
 */
const mo00020Type = ref<Mo00020Type>({
  value: convertDateToSeireki(undefined),
} as Mo00020Type)

/**
 * 帳票タイトル
 */
const mo00045Type = ref<Mo00045Type>({
  value: Or57074Const.DEFAULT.STR.EMPTY,
} as Mo00045Type)

/**
 * 敬称を変更するチェックボックス
 */
const mo00018TitleOfHonorFlg = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 記入用シートを印刷するチェックボックス
 */
const mo00018EmptyFlg = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 一覧印刷するチェックボックス
 */
const mo00018ListPrintFlg = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 承認欄を印刷するチェックボックス
 */
const mo00018ApprovalPrintFlg = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 総括部分を印刷しないチェックボックス
 */
const mo00018SummaryPrintFlg = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 再アセスメントの必要性を印刷しないチェックボックス
 */
const mo00018ReassessmentFlg = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 印刷時に色をつける
 */
const mo00018TypeColor = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 利用者選択
 */
const mo00039OneWayUserSelectType = ref<string>('')

/**
 * 履歴選択
 */
const mo00039OneWayHistorySelectType = ref<string>('')

/**
 * 当ケアマネプルダウン選択値
 */
const orX0145Type = ref<OrX0145Type>({
  value: {} as TantoCmnShokuin,
} as OrX0145Type)

/**
 * 基準日
 */
// const mo00020TypeKijunbi = ref<Mo00020Type>({
//   value: convertDateToSeireki(undefined),
// } as Mo00020Type)

/**
 * 指定日非表示/表示フラゲ
 */
const mo00020Flag = ref<boolean>(false)

/**
 * 計画作成日/表示フラゲ
 */
const mo00020CpnFlag = ref<boolean>(false)

const orX0128Headers_1 = [
  { title: t('label.create-date'), key: 'createYmd', width: '120px', sortable: false },
  { title: t('label.author'), key: 'fullName', width: '160px', sortable: false },
  { title: t('label.ledge-name'), key: 'titleKnj', sortable: false },
] as OrX0128Headers[]
const orX0128Headers_2 = [
  { title: t('label.create-date'), key: 'createYmd', width: '120px', sortable: false },
  { title: t('label.author'), key: 'fullName', width: '160px', sortable: false },
  {
    title: t('label.history-selection-case-number'),
    key: 'caseNo',
    width: '120px',
    sortable: false,
  },
  {
    title: t('label.history-selection-revision'),
    key: 'kaiteiFlg',
    width: '80px',
    sortable: false,
  },
  { title: t('label.case-type'), key: 'fullName', width: '120px', sortable: false },
] as unknown as OrX0128Headers[]

/** 共通情報.ケアプラン方式が"5:パッケージ"の場合 */
const isCarePlanStyle5 = computed(() => {
  return cmnRouteCom.getInitialSettingMaster()?.cpnFlg === Or57074Const.DEFAULT.CARE_PLAN_STYLE_5
    ? true
    : false
})
/**
 * 基準日
 */
const mo00020TypeKijunbi = ref<Mo00020Type>({
  value: convertDateToSeireki(undefined),
} as Mo00020Type)

/**
 * 基準日フラゲ
 */
const kijunbiFlag = ref<boolean>(false)
/**
 * 履歴一覧セクションフラゲ
 */
const mo01334TypeHistoryFlag = ref<boolean>(true)

/**
 * 利用者列幅
 */
const userCols = ref<number>(5)

const orX0128OnewayModel = reactive<OrX0128OnewayType>({
  /**
   * 期間管理フラグ
   */
  kikanFlg: '1',
  /**
   * 単一複数フラグ(0:単一,1:複数)
   */
  singleFlg: OrX0128Const.DEFAULT.TANI,
  tableStyle: 'width:480px',
  headers: isCarePlanStyle5.value ? orX0128Headers_2 : orX0128Headers_1,
  items: [],
})

/**
 * 利用者
 */
const orX0130Oneway = reactive<OrX0130OnewayType>({
  selectMode: OrX0130Const.DEFAULT.TANI,
  tableStyle: 'width:210px',
  // 指定行選択
  userId: Or57074Const.DEFAULT.STR.EMPTY,
})

/**
 * 担当ケアマネ選択
 */
const tantoIconBtn = ref<boolean>(false)
/**
 * 印刷設定帳票出力
 */
const orX0117Oneway: OrX0117OnewayType = {
  type: Or57074Const.DEFAULT.TANI,
  historyList: [] as OrX0117History[],
} as OrX0117OnewayType

/**
 * 初期情報取得フラゲ
 */
const initFlag = ref<boolean>(false)

/**
 * 期間管理フラグ
 */
const kikanFlag = ref<string>(Or57074Const.DEFAULT.STR.EMPTY)

// ダイアログ表示フラグ
const showDialogOr10016 = computed(() => {
  // Or10016のダイアログ開閉状態
  return Or10016Logic.state.get(or10016_1.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOrX0135 = computed(() => {
  // OrX0135のダイアログ開閉状態
  return OrX0135Logic.state.get(orX0135_1.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or57074StateType>({
  cpId: Or57074Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or57074Const.DEFAULT.IS_OPEN
    },
    param: (value) => {
      if (value) {
        local.prtNo = value.prtNo
        local.houjinId = value.houjinId
        local.svJigyoId = value.svJigyoId
        local.shisetuId = value.shisetuId
        local.tantoId = value.tantoId
        local.syubetsuId = value.syubetsuId
        local.sectionName = value.sectionName
        local.userId = value.userId
        local.svJigyoKnj = value.svJigyoKnj
        local.processYmd = value.processYmd
      }
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21815Const.CP_ID(0)]: or21815.value,
  [OrX0117Const.CP_ID(0)]: orX0117.value,
  [OrX0128Const.CP_ID(0)]: orX0128.value,
  [OrX0130Const.CP_ID(0)]: orX0130.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or21813Const.CP_ID(0)]: or21813.value,
  [OrX0145Const.CP_ID(0)]: orx0145.value,
  [Or10016Const.CP_ID(1)]: or10016_1.value,
  [OrX0135Const.CP_ID(1)]: orX0135_1.value,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 画面ID
const screenId = 'GUI00793'
// ルーティング
const routing = 'GUI00793/pinia'
// ケアマネ画面を初期化する
await useCmnCom().initialize({
  screenId,
  routing,
})

onMounted(async () => {
  // 汎用コード取得API実行
  await initCodes()

  // 画面ボタン活性非活性設定
  btnItemSetting()

  // 初期情報取得
  await init()
})

// ダイアログ表示フラグ
const showDialogOrX0117 = computed(() => {
  // Or57074のダイアログ開閉状態
  return OrX0117Logic.state.get(orX0117.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 汎用コード取得API実行
 */
const initCodes = async () => {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 日付印刷区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    // 単複数選択区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY },
    // 性別区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_GENDER_CATEGORY },
    // 印刷する要介護
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_PRINTED_CARE },
    // 改訂区分コード
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_REVISION_INFO_SHEET },
    // M_CD_KBN_ID_MONITOR_FLAG_ID
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_MONITOR_FLAG_ID },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  // モニタリングフラグID
  const monitorFlagCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_MONITOR_FLAG_ID
  )

  if (monitorFlagCodeTypes?.length > 0) {
    local.mo00040TypeMonitorFlag.modelValue = monitorFlagCodeTypes[0].value
    const list = []
    for (const item of monitorFlagCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        })
      }
    }
    localOneway.mo00040OneWayMonitorFlag.items = list
  }

  // 改訂区分
  const kaiteiKbnListCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_REVISION_INFO_SHEET
  )
  if (kaiteiKbnListCodeTypes?.length > 0) {
    const list = []
    for (const item of kaiteiKbnListCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        })
      }
    }
    localOneway.kaiteiKbnList = list
  }

  // 印刷する要介護
  const printedCareCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_PRINTED_CARE
  )
  if (printedCareCodeTypes?.length > 0) {
    local.mo00040TypeKaigodo.modelValue = printedCareCodeTypes[0].value
    const list = []
    for (const item of printedCareCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        })
      }
    }
    localOneway.mo00040OneWayKaigodo.items = list
  }

  // 日付印刷区分
  const bizukePrintCategoryCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )
  if (bizukePrintCategoryCodeTypes?.length > 0) {
    mo00039Type.value = bizukePrintCategoryCodeTypes[0].value
    const list: Mo00039Items[] = []
    for (const item of bizukePrintCategoryCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    localOneway.mo00039OneWay.items = list
  }

  // 利用者選択 TAN_MULTIPLE_SELECT_CATEGORY
  const tanMultipleSelectCategoryCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )
  if (tanMultipleSelectCategoryCodeTypes?.length > 0) {
    mo00039OneWayUserSelectType.value = tanMultipleSelectCategoryCodeTypes[0].value
    mo00039OneWayHistorySelectType.value = tanMultipleSelectCategoryCodeTypes[0].value
    const list: Mo00039Items[] = []
    for (const item of tanMultipleSelectCategoryCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    localOneway.mo00039OneWayHistorySelectType.items = list
    localOneway.mo00039OneWayUserSelectType.items = list
  }
}

/**
 * 初期プロジェクト設定
 */
const initSetting = () => {
  // 親画面.処理年月日が""の場合
  if (Or57074Const.DEFAULT.STR.EMPTY === local.processYmd) {
    // 担当ケアマネ選択
    tantoIconBtn.value = false
  } else {
    // 担当ケアマネ選択
    tantoIconBtn.value = true
  }
}

/**
 * 初期情報取得
 */
const init = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: PlanMonitoringPrintSettingsUpdateInEntity = {
    sysCd: systemCommonsStore.getSystemCode ?? '71101',
    sysRyaku: Or57074Const.DEFAULT.SYS_RYAKU,
    houjinId: systemCommonsStore.getHoujinId ?? '0',
    shisetuId: local.shisetuId,
    svJigyoId: local.svJigyoId,
    shokuId: systemCommonsStore.getStaffId ?? '0',
    userId: local.userId,
    tantoId: local.tantoId,
    sectionName: local.sectionName,
    index: Or57074Const.DEFAULT.INDEX,
    syubetsuId: local.syubetsuId,
    kojinhogoUsedFlg: Or57074Const.DEFAULT.KOJINHOGO_USED_FLG,
    sectionAddNo: Or57074Const.DEFAULT.SECTION_ADD_NO,
  } as PlanMonitoringPrintSettingsUpdateInEntity
  const resp: PlanMonitoringPrintSettingsUpdateOutEntity = await ScreenRepository.update(
    'planMonitoringPrintSettingsUpdate',
    inputData
  )
  if (resp?.data) {
    // レスポンスパラメータ詳細
    localData.data = { ...resp?.data }

    // 期間管理フラグ
    kikanFlag.value = localData.data.kikanFlg

    // 担当ケアマネ

    // 担当ケアマネ
    orX0145Type.value.value = {
      counter: Or57074Const.DEFAULT.STR.EMPTY,
      chkShokuId: Or57074Const.DEFAULT.STR.EMPTY,
      houjinId: Or57074Const.DEFAULT.STR.EMPTY,
      shisetuId: Or57074Const.DEFAULT.STR.EMPTY,
      svJigyoId: Or57074Const.DEFAULT.STR.EMPTY,
      shokuin1Kana: Or57074Const.DEFAULT.STR.EMPTY,
      shokuin2Kana: Or57074Const.DEFAULT.STR.EMPTY,
      shokuin1Knj: Or57074Const.DEFAULT.STR.EMPTY,
      shokuin2Knj: Or57074Const.DEFAULT.STR.EMPTY,
      sex: Or57074Const.DEFAULT.STR.EMPTY,
      birthdayYmd: Or57074Const.DEFAULT.STR.EMPTY,
      zip: Or57074Const.DEFAULT.STR.EMPTY,
      kencode: Or57074Const.DEFAULT.STR.EMPTY,
      citycode: Or57074Const.DEFAULT.STR.EMPTY,
      areacode: Or57074Const.DEFAULT.STR.EMPTY,
      addressKnj: Or57074Const.DEFAULT.STR.EMPTY,
      tel: Or57074Const.DEFAULT.STR.EMPTY,
      kaikeiId: Or57074Const.DEFAULT.STR.EMPTY,
      kyuyoKbn: Or57074Const.DEFAULT.STR.EMPTY,
      partKbn: Or57074Const.DEFAULT.STR.EMPTY,
      inYmd: Or57074Const.DEFAULT.STR.EMPTY,
      outYmd: Or57074Const.DEFAULT.STR.EMPTY,
      shozokuId: Or57074Const.DEFAULT.STR.EMPTY,
      shokushuId: Or57074Const.DEFAULT.STR.EMPTY,
      shokuId: Or57074Const.DEFAULT.STR.EMPTY,
      timeStmp: Or57074Const.DEFAULT.STR.EMPTY,
      delFlg: Or57074Const.DEFAULT.STR.EMPTY,
      shokuNumber: Or57074Const.DEFAULT.STR.EMPTY,
      caremanagerKbn: Or57074Const.DEFAULT.STR.EMPTY,
      shokuType1: Or57074Const.DEFAULT.STR.EMPTY,
      shokuType2: Or57074Const.DEFAULT.STR.EMPTY,
      kGroupid: Or57074Const.DEFAULT.STR.EMPTY,
      bmpPath: Or57074Const.DEFAULT.STR.EMPTY,
      bmpYmd: Or57074Const.DEFAULT.STR.EMPTY,
      hankoPath: Or57074Const.DEFAULT.STR.EMPTY,
      kojinPath: Or57074Const.DEFAULT.STR.EMPTY,
      keitaitel: Or57074Const.DEFAULT.STR.EMPTY,
      eMail: Or57074Const.DEFAULT.STR.EMPTY,
      senmonNo: Or57074Const.DEFAULT.STR.EMPTY,
      sgfFlg: Or57074Const.DEFAULT.STR.EMPTY,
      srvSekiKbn: Or57074Const.DEFAULT.STR.EMPTY,
      shokushuId2: Or57074Const.DEFAULT.STR.EMPTY,
      shokushuId3: Or57074Const.DEFAULT.STR.EMPTY,
      shokushuId4: Or57074Const.DEFAULT.STR.EMPTY,
      shokushuId5: Or57074Const.DEFAULT.STR.EMPTY,
      shikakuId1: Or57074Const.DEFAULT.STR.EMPTY,
      shikakuId2: Or57074Const.DEFAULT.STR.EMPTY,
      shikakuId3: Or57074Const.DEFAULT.STR.EMPTY,
      shikakuId4: Or57074Const.DEFAULT.STR.EMPTY,
      shikakuId5: Or57074Const.DEFAULT.STR.EMPTY,
      kyuseiFlg: Or57074Const.DEFAULT.STR.EMPTY,
      kyuseiKana: Or57074Const.DEFAULT.STR.EMPTY,
      kyuseiKnj: Or57074Const.DEFAULT.STR.EMPTY,
      sort: Or57074Const.DEFAULT.STR.EMPTY,
      selfNumber: Or57074Const.DEFAULT.STR.EMPTY,
      ichiranShokushuIdNm: Or57074Const.DEFAULT.STR.EMPTY,
      shokushuId2Nm: Or57074Const.DEFAULT.STR.EMPTY,
      shokushuId3Nm: Or57074Const.DEFAULT.STR.EMPTY,
      shokushuId4Nm: Or57074Const.DEFAULT.STR.EMPTY,
      shokushuId5Nm: Or57074Const.DEFAULT.STR.EMPTY,
      stopFlg: Or57074Const.DEFAULT.STR.EMPTY,
      shokuinKnj: local.tantoId,
      shokuinKana: Or57074Const.DEFAULT.STR.EMPTY,
      title: Or57074Const.DEFAULT.STR.EMPTY,
      value: Or57074Const.DEFAULT.STR.EMPTY,
    } as TantoCmnShokuin

    const prtList: Mo01334Items[] = []
    for (const item of resp.data.prtList) {
      if (item) {
        prtList.push({
          id: item.prtNo,
          mo01337OnewayLedgerName: {
            value: item.prtTitle,
            unit: Or57074Const.DEFAULT.STR.EMPTY,

            customClass: new CustomClass({
              outerClass: 'mr-2',
              outerStyle: 'background-color: rgba(0, 0, 0, 0);',
              labelClass: 'ma-0',
              itemClass: 'ml-0',
              itemStyle: 'width: 128px;',
            }),
          } as Mo01337OnewayType,
          prnDate: item.prnDate === Or57074Const.DEFAULT.STR.TRUE,
          selectable: true,
          profile: item.profile,
          index: item.index,
          prtNo: item.prtNo,
        } as Mo01334Items)

        if (prtList.length === Or57074Const.DEFAULT.NUMBER.ONE) {
          mo00018TypeColor.value.modelValue = item.param05 === Or57074Const.DEFAULT.STR.TRUE
        }
      }
    }
    mo01334Oneway.value.items = prtList

    initFlag.value = true
    getHistoryData(resp.data.historyList)
    // アセスメント履歴情報を取得する
    await getPrintSettingsHistoryList()
    outputLedgerName(local.prtNo)
  }

  initSetting()
}

/**
 * アセスメント履歴一覧データ
 *
 * @param periodHistoryList - アセスメント履歴リスト
 */
const getHistoryData = (periodHistoryList: PeriodHistoryEntity[]) => {
  if (Or57074Const.DEFAULT.KIKAN_FLG_1 === kikanFlag.value) {
    const tempList: string[] = [] as string[]
    let list: OrX0128Items[] = []
    for (const item of periodHistoryList) {
      if (item) {
        const planPeriod =
          t('label.plan-period') +
          Or57074Const.DEFAULT.STR.SPLIT_COLON +
          item.startYmd +
          Or57074Const.DEFAULT.STR.SPLIT_TILDE +
          item.endYmd
        if (!tempList.includes(planPeriod)) {
          const historyList: OrX0128Items[] = []
          for (const data of periodHistoryList) {
            const dataPlanPeriod =
              t('label.plan-period') +
              Or57074Const.DEFAULT.STR.SPLIT_COLON +
              data.startYmd +
              Or57074Const.DEFAULT.STR.SPLIT_TILDE +
              data.endYmd
            if (planPeriod === dataPlanPeriod) {
              historyList.push({
                id: item.cmoni1Id,
                sc1Id: item.sc1Id,
                startYmd: item.startYmd,
                endYmd: item.endYmd,
                sel: item.sel,
                cmoni1Id: item.cmoni1Id,
                userId: item.userId,
                createYmd: item.createYmd,
                shokuId: item.shokuId,
                fullName: item.fullName,
                kaiteiFlg: getKaiteiKbnName(item.kaiteiFlg),
                caseNo: item.caseNo,
                moniFlg: getMoniFlgName(item.moniFlg),
                free1Id: item.free1Id,
                titleKnj: item.titleKnj,
                sypFlg: item.sypFlg,
              } as OrX0128Items)
            }
          }
          if (historyList.length > 0) {
            list.push({
              sc1Id: item.sc1Id,
              startYmd: item.startYmd,
              endYmd: item.endYmd,
              isPeriodManagementMergedRow: true,
              planPeriod:
                t('label.plan-period') +
                Or57074Const.DEFAULT.STR.SPLIT_COLON +
                item.startYmd +
                Or57074Const.DEFAULT.STR.SPLIT_TILDE +
                item.endYmd,
              id: Or57074Const.DEFAULT.STR.EMPTY,
            } as OrX0128Items)
            list = list.concat(historyList)
            tempList.push(planPeriod)
          }
        }
      }
    }
    list.forEach((item, index) => {
      item.id = String(++index)
    })
    orX0128OnewayModel.items = list
    // 画面.利用者一覧明細に親画面.利用者IDが存在する場合
    if (local.cmoni1Id) {
      // 親画面.アセスメントIDを対するレコードを選択状態にする
      orX0128OnewayModel.initSelectId = (
        list.findIndex((item) => item.cmoni1Id === local.cmoni1Id) + 1
      ).toString()
    }
  } else {
    const list: OrX0128Items[] = []
    for (const data of periodHistoryList) {
      if (data) {
        list.push({
          id: data.cmoni1Id,
          sc1Id: data.sc1Id,
          startYmd: data.startYmd,
          endYmd: data.endYmd,
          sel: data.sel,
          cmoni1Id: data.cmoni1Id,
          userId: data.userId,
          createYmd: data.createYmd,
          shokuId: data.shokuId,
          fullName: data.fullName,
          kaiteiFlg: getKaiteiKbnName(data.kaiteiFlg),
          caseNo: data.caseNo,
          moniFlg: getMoniFlgName(data.moniFlg),
          free1Id: data.free1Id,
          titleKnj: data.titleKnj,
          sypFlg: data.sypFlg,
        } as OrX0128Items)
      }
    }
    orX0128OnewayModel.items = list
    // 履歴一覧明細に親画面.履歴IDが存在する場合
    if (local.cmoni1Id) {
      // 画面.履歴一覧明細に親画面.履歴IDを対するレコードを選択状態にする
      orX0128OnewayModel.initSelectId = (
        list.findIndex((item) => item.raiId === local.cmoni1Id) + 1
      ).toString()
    }
  }
}

/**
 * 帳票タイトル入力変更時
 *
 * @param param - 入力値
 */
const inputChange = async (param: Mo00045Type) => {
  if (!param.value) {
    // メッセージを表示
    await openWarnDialog(or21815.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.warning'),
      // ダイアログテキスト
      dialogText: t('message.w-cmn-20845'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    } as Or21815StateType)
  }
}

/**
 * 改訂区分コード
 *
 * @param kaiteiKbn - 改訂区分コード
 */
const getKaiteiKbnName = (kaiteiKbn: string): string => {
  let kaiteiKbnName = Or57074Const.DEFAULT.STR.EMPTY
  if (localOneway.kaiteiKbnList && localOneway.kaiteiKbnList?.length > 0) {
    for (const item of localOneway.kaiteiKbnList) {
      if (item) {
        if (item.value === kaiteiKbn) {
          kaiteiKbnName = item.label
        }
      }
    }
  }
  return kaiteiKbnName
}

/**
 * 改訂区分コード
 *
 * @param moniFlg - 改訂区分コード
 */
const getMoniFlgName = (moniFlg: string): string => {
  let moniFlgName = Or57074Const.DEFAULT.STR.EMPTY
  if (
    localOneway.mo00040OneWayMonitorFlag.items &&
    localOneway.mo00040OneWayMonitorFlag.items?.length > 0
  ) {
    for (const item of localOneway.mo00040OneWayMonitorFlag.items) {
      if (item) {
        if (item.value === moniFlg) {
          moniFlgName = item.label
        }
      }
    }
  }
  return moniFlgName
}

/**
 * 画面印刷設定内容を保存
 */
const save = async (): Promise<IFreeAssessmentFacePrintSettingsUpdateOutEntity> => {
  // バックエンドAPIから印刷設定情報保存
  const inputData: IFreeAssessmentFacePrintSettingsUpdateInEntity = {
    sysRyaku: Or57074Const.DEFAULT.SYS_RYAKU,
    sectionName: local.sectionName,
    gsyscd: systemCommonsStore.getSystemCode ?? '71101',
    shokuId: systemCommonsStore.getStaffId ?? '0',
    houjinId: systemCommonsStore.getHoujinId ?? '0',
    shisetuId: local.shisetuId,
    svJigyoId: local.svJigyoId,
    index: local.index,
    sysIniInfo: local.sysIniInfo,
    prtList: localData.data.prtList,
  } as IFreeAssessmentFacePrintSettingsUpdateInEntity

  const resp: IFreeAssessmentFacePrintSettingsUpdateOutEntity = await ScreenRepository.update(
    'freeAssessmentFacePrintSettingsUpdate',
    inputData
  )
  return resp
}

/**
 * 「GUI01110_印鑑欄設定」画面をポップアップ表示する。
 */
const openGUI01110 = () => {
  // 法人ID:親画面.法人ID
  localOneway.or10016Oneway.houjinId = local.houjinId
  // 施設ID:親画面.施設ID
  localOneway.or10016Oneway.shisetsuId = local.shisetuId
  // 事業所ID:親画面.事業所ID
  localOneway.or10016Oneway.jigyoshoId = local.svJigyoId
  // 帳票セクション番号:"3GKU0081"
  localOneway.or10016Oneway.reportSectionNumber = '3GKU0081'
  // 会議録フラグ:FALSE
  localOneway.or10016Oneway.conferenceFlag = false
  // ケアプラン方式:親画面.初期設定マスタの情報.ケアプラン方式
  localOneway.or10016Oneway.assessment =
    cmnRouteCom.getInitialSettingMaster()?.cpnFlg ?? Or57074Const.DEFAULT.STR.EMPTY

  Or10016Logic.state.set({
    uniqueCpId: or10016_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI00617_承認欄登録画面をポップアップで起動する。
 */
const openGUI00617 = () => {
  // 承認欄情報：親画面.承認欄情報（0：共有する、1：帳票毎保持する）TODO
  // 事業所ID：親画面.事業所ID
  localOneway.orX0135Oneway.svJigyoId = local.svJigyoId
  // セクション：印刷設定情報リスト.プロファイル
  // 法人ID：親画面.法人ID
  localOneway.orX0135Oneway.houjinId = local.houjinId
  // 施設ID：親画面.施設ID
  localOneway.orX0135Oneway.shisetuId = local.shisetuId

  OrX0135Logic.state.set({
    uniqueCpId: orX0135_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「閉じるボタン」押下
 */
const close = async () => {
  await save()
  setState({
    isOpen: false,
    param: {} as Or57074Param,
  })
}

/**
 * 「PDFダウンロード」ボタン押下
 */
const pdfDownload = async () => {
  // 利用者選択方法が「単一」
  if (Or57074Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
    // 履歴選択が「単一」
    if (Or57074Const.DEFAULT.TANI === mo00039OneWayHistorySelectType.value) {
      orX0117Oneway.type = Or57074Const.DEFAULT.STR.ONE
      //記入用シートを印刷するチェック入れるの場合
      if (mo00018EmptyFlg.value.modelValue) {
        // 帳票側の処理を呼び出し、帳票レイアウトのみ印刷する
        const inputData: ICpnTucRaiAssReportSelectInEntity = {
          svJigyoKnj: local.svJigyoKnj,
          syscd: systemCommonsStore.getSystemCode,
          printSet: {
            shiTeiKubun: mo00039Type.value,
            shiTeiDate: mo00020Type.value.value ? mo00020Type.value.value.split('/').join('-') : '',
          } as PrintSetEntity,
          printOption: {
            emptyFlg: String(mo00018EmptyFlg.value.modelValue),
            kinyuAssType: '',
            colorFlg: String(mo00018TypeColor.value.modelValue),
          } as PrintOptionEntity,
          printSubjectHistoryList: [] as PrintSubjectHistoryEntity[],
        } as ICpnTucRaiAssReportSelectInEntity
        // 帳票出力
        await reportOutput(local.reportId, inputData, reportOutputType.DOWNLOAD)
        return
      }
      // 記入用シートを印刷するチェック外すの場合
      else {
        // 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
        if (local.userNoSelect) {
          // メッセージを表示
          const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
            // ダイアログタイトル
            dialogTitle: t('label.confirm'),
            // ダイアログテキスト
            dialogText: t('message.i-cmn-11393'),
            firstBtnType: 'normal1',
            firstBtnLabel: t('btn.yes'),
            secondBtnType: 'blank',
            thirdBtnType: 'blank',
          })
          // はい
          if (dialogResult === Or57074Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
            // 処理終了
            return
          }
        }

        // 履歴一覧が0件選択の場合（※履歴リストが0件、複数件を含む）
        if (local.historyNoSelect) {
          // メッセージを表示
          const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
            // ダイアログタイトル
            dialogTitle: t('label.confirm'),
            // ダイアログテキスト
            dialogText: t('message.i-cmn-11455'),
            firstBtnType: 'normal1',
            firstBtnLabel: t('btn.yes'),
            secondBtnType: 'blank',
            thirdBtnType: 'blank',
          })
          // はい
          if (dialogResult === Or57074Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
            // 処理終了
            return
          }
        }
      }

      // 履歴情報リストにデータを選択する場合
      if (!local.historyNoSelect) {
        // 印刷ダイアログ画面を開かずに、画面.印刷対象履歴リスト「利用者情報+履歴情報+出力帳票対象」を直接に利用して、帳票側の処理を呼び出す
        await reportOutputPdf()
        return
      }
    }
    // 履歴選択方法が「複数」
    else if (Or57074Const.DEFAULT.HUKUSUU === mo00039OneWayHistorySelectType.value) {
      // 履歴一覧が0件選択場合（※履歴リストが0件、複数件を含む）
      if (local.historyNoSelect) {
        // メッセージを表示
        const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
          // ダイアログタイトル
          dialogTitle: t('label.confirm'),
          // ダイアログテキスト
          dialogText: t('message.i-cmn-11455'),
          firstBtnType: 'normal1',
          firstBtnLabel: t('btn.yes'),
          secondBtnType: 'blank',
          thirdBtnType: 'blank',
        })
        // はい
        if (dialogResult === Or57074Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
          // 処理終了
          return
        }
      }
      // 履歴情報リストにデータを選択する場合
      else {
        // 印刷設定情報リストを作成
        createReportOutputData(local.prtNo)
      }
    }
  }

  // 利用者選択方法が「複数」の場合
  if (Or57074Const.DEFAULT.HUKUSUU === mo00039OneWayUserSelectType.value) {
    orX0117Oneway.type = Or57074Const.DEFAULT.STR.ONE
    // 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
    if (local.userNoSelect) {
      // メッセージを表示
      const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
        // ダイアログタイトル
        dialogTitle: t('label.confirm'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-11393'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      })
      // はい
      if (dialogResult === Or57074Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
        // 処理終了
        return
      }
    }
    // 利用者情報リストにデータを選択する場合
    else {
      // 印刷設定情報リストを作成
      await PrintSettingsSubjectSelect()
    }
  }

  // AC019-2と同じ => 画面の印刷設定情報を保存する
  await save()

  // 選択された帳票のプロファイルが””の場合
  if (local.profile === Or57074Const.DEFAULT.STR.EMPTY) {
    // メッセージを表示
    const dialogResult = await openErrorDialog(or21813.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト
      dialogText: t('message.e-cmn-40172'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
    // はい
    if (dialogResult === Or57074Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
      // 処理終了
      return
    }
  }

  // 「AC020-5-2 また AC020-5-3」で取得した印刷設定情報リスト＞0件の場合
  if (orX0117Oneway.historyList.length > 0) {
    // OrX0117のダイアログ開閉状態を更新する
    OrX0117Logic.state.set({
      uniqueCpId: orX0117.value.uniqueCpId,
      state: {
        isOpen: true,
      },
    })
  }
}

/**
 * 印刷ダイアログ画面を開
 */
const reportOutputPdf = async () => {
  try {
    // API処理失敗時のシステムエラーダイアログを非表示にする
    systemCommonsStore.setSystemErrorDialogType('hide')

    const reportData: ICpnTucRaiAssReportSelectInEntity = {
      svJigyoKnj: local.svJigyoKnj,
      syscd: systemCommonsStore.getSystemCode ?? '71101',
      printSet: {
        shiTeiKubun: mo00039Type.value,
        shiTeiDate: mo00020Type.value.value ? mo00020Type.value.value.split('/').join('-') : '',
      } as PrintSetEntity,
      printOption: {
        emptyFlg: String(mo00018EmptyFlg.value.modelValue),
        kinyuAssType: '',
        colorFlg: String(mo00018TypeColor.value.modelValue),
      } as PrintOptionEntity,
      printSubjectHistoryList: [] as PrintSubjectHistoryEntity[],
    } as ICpnTucRaiAssReportSelectInEntity

    const choPrtList: ChoPrtEntity[] = []
    for (const item of localData.data.prtList) {
      if (item) {
        if (local.prtNo === item.prtNo) {
          choPrtList.push({
            shokuId: systemCommonsStore.getStaffId,
            sysRyaku: systemCommonsStore.getSystemAbbreviation,
            section: local.sectionName,
            prtNo: item.prtNo,
            choPro: item.profile,
            sectionName: item.defPrtTitle,
            dwobject: item.dwobject,
            prtOrient: item.prtOrient,
            prtSize: item.prtSize,
            listTitle: item.listTitle,
            prtTitle: item.prtTitle,
            mTop: item.mtop,
            mBottom: item.mbottom,
            mLeft: item.mleft,
            mRight: item.mright,
            ruler: item.ruler,
            prndate: item.prnDate,
            prnshoku: item.prnshoku,
            serialFlg: item.serialFlg,
            modFlg: item.modFlg,
            secFlg: item.secFlg,
            param01: item.param01,
            param02: item.param02,
            param03: item.param03,
            param04: item.param04,
            param05: item.param05,
            param06: item.param06,
            param07: item.param07,
            param08: item.param08,
            param09: item.param09,
            param10: item.param10,
            serialHeight: item.serialHeight,
            serialPagelen: item.serialPagelen,
            hsjId: systemCommonsStore.getHoujinId,
            param11: item.param11,
            param12: item.param12,
            param13: item.param13,
            param14: item.param14,
            param15: item.param15,
            param16: item.param16,
            param17: item.param17,
            param18: item.param18,
            param19: item.param19,
            param20: item.param20,
            param21: item.param21,
            param22: item.param22,
            param23: item.param23,
            param24: item.param24,
            param25: item.param25,
            param26: item.param26,
            param27: item.param28,
            param28: item.param28,
            param29: item.param29,
            param30: item.param30,
            param31: item.param31,
            param32: item.param32,
            param33: item.param33,
            param34: item.param34,
            param35: item.param35,
            param36: item.param36,
            param37: item.param37,
            param38: item.param38,
            param39: item.param39,
            param40: item.param40,
            param41: item.param41,
            param42: item.param42,
            param43: item.param43,
            param44: item.param44,
            param45: item.param45,
            param46: item.param46,
            param47: item.param47,
            param48: item.param48,
            param49: item.param49,
            param50: item.param50,
            houjinId: systemCommonsStore.getHoujinId,
            shisetuId: systemCommonsStore.getShisetuId,
            svJigyoId: systemCommonsStore.getSvJigyoId,
            zoomRate: item.zoomRate,
            modifiedCnt: item.modifiedCnt,
          } as ChoPrtEntity)
        }
      }
    }

    // TODO 印刷設定情報リストパラメータを作成
    reportData.printSubjectHistoryList.push({
      userId: local.userList.length > 0 ? local.userList[0].userId : Or57074Const.DEFAULT.STR.EMPTY,
      userName:
        local.userList.length > 0 ? local.userList[0].userName : Or57074Const.DEFAULT.STR.EMPTY,
      sc1Id: '1708',
      startYmd: '2022/10/01',
      endYmd: '2023/09/30',
      raiId: '63',
      assType:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].assType as string)
          : Or57074Const.DEFAULT.STR.EMPTY,
      assDateYmd:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].assDateYmd as string)
          : Or57074Const.DEFAULT.STR.EMPTY,
      assShokuId: '1',
      result: Or57074Const.DEFAULT.STR.EMPTY,
      choPrtList: choPrtList,
    } as PrintSubjectHistoryEntity)
    // 帳票出力
    await reportOutput(local.reportId, reportData, reportOutputType.DOWNLOAD)
  } catch (e) {
    $log.debug('帳票の出力に失敗しました。', local.reportId, reportOutputType.DOWNLOAD, e)
  } finally {
    // API処理失敗時のシステムエラーダイアログの表示タイプをデフォルトに戻す
    systemCommonsStore.setSystemErrorDialogType('details')
  }
}

/**
 * 印刷設定情報リストを作成(利用者選択が「複数」、利用者情報リストにデータを選択する場合)
 */
const PrintSettingsSubjectSelect = async () => {
  // api_1147_印刷設定情報リストを作成する
  const inputData: PlanMonitoringPrintSettingsHistorySelectInEntity = {
    userlist: local.userList,
    svJigyoIds: systemCommonsStore.getSvJigyoIdList,
    kijunbiYmd: mo00020TypeKijunbi.value.value,
    cpnFlg: cmnRouteCom.getInitialSettingMaster()?.cpnFlg ?? Or57074Const.DEFAULT.STR.EMPTY,
    index: local.index,
  } as PlanMonitoringPrintSettingsHistorySelectInEntity
  const resp: PlanMonitoringPrintSettingsHistorySelectOutEntity = await ScreenRepository.select(
    'planMonitoringPrintSettingsHistorySelect',
    inputData
  )

  const list: OrX0117History[] = []
  if (resp.data) {
    for (const data of resp.data.reportInfoList) {
      if (data) {
        // 利用者複数の場合
        if (Or57074Const.DEFAULT.HUKUSUU === mo00039OneWayUserSelectType.value) {
          // TODO 印刷設定情報リストを作成
          const reportData: ICpnTucRaiAssReportSelectInEntity = {
            svJigyoKnj: local.svJigyoKnj,
            syscd: systemCommonsStore.getSystemCode ?? '71101',
            printSet: {
              shiTeiKubun: mo00039Type.value,
              shiTeiDate: mo00020Type.value.value
                ? mo00020Type.value.value.split('/').join('-')
                : '',
            } as PrintSetEntity,
            printOption: {
              emptyFlg: String(mo00018EmptyFlg.value.modelValue),
              kinyuAssType: '',
              colorFlg: String(mo00018TypeColor.value.modelValue),
            } as PrintOptionEntity,
            printSubjectHistoryList: [] as PrintSubjectHistoryEntity[],
          } as ICpnTucRaiAssReportSelectInEntity

          const choPrtList: ChoPrtEntity[] = []
          for (const item of localData.data.prtList) {
            if (item) {
              if (local.prtNo === item.prtNo) {
                choPrtList.push({
                  shokuId: systemCommonsStore.getStaffId,
                  sysRyaku: systemCommonsStore.getSystemAbbreviation,
                  section: local.sectionName,
                  prtNo: item.prtNo,
                  choPro: item.profile,
                  sectionName: item.defPrtTitle,
                  dwobject: item.dwobject,
                  prtOrient: item.prtOrient,
                  prtSize: item.prtSize,
                  listTitle: item.listTitle,
                  prtTitle: item.prtTitle,
                  mTop: item.mtop,
                  mBottom: item.mbottom,
                  mLeft: item.mleft,
                  mRight: item.mright,
                  ruler: item.ruler,
                  prndate: item.prnDate,
                  prnshoku: item.prnshoku,
                  serialFlg: item.serialFlg,
                  modFlg: item.modFlg,
                  secFlg: item.secFlg,
                  param01: item.param01,
                  param02: item.param02,
                  param03: item.param03,
                  param04: item.param04,
                  param05: item.param05,
                  param06: item.param06,
                  param07: item.param07,
                  param08: item.param08,
                  param09: item.param09,
                  param10: item.param10,
                  serialHeight: item.serialHeight,
                  serialPagelen: item.serialPagelen,
                  hsjId: systemCommonsStore.getHoujinId,
                  param11: item.param11,
                  param12: item.param12,
                  param13: item.param13,
                  param14: item.param14,
                  param15: item.param15,
                  param16: item.param16,
                  param17: item.param17,
                  param18: item.param18,
                  param19: item.param19,
                  param20: item.param20,
                  param21: item.param21,
                  param22: item.param22,
                  param23: item.param23,
                  param24: item.param24,
                  param25: item.param25,
                  param26: item.param26,
                  param27: item.param28,
                  param28: item.param28,
                  param29: item.param29,
                  param30: item.param30,
                  param31: item.param31,
                  param32: item.param32,
                  param33: item.param33,
                  param34: item.param34,
                  param35: item.param35,
                  param36: item.param36,
                  param37: item.param37,
                  param38: item.param38,
                  param39: item.param39,
                  param40: item.param40,
                  param41: item.param41,
                  param42: item.param42,
                  param43: item.param43,
                  param44: item.param44,
                  param45: item.param45,
                  param46: item.param46,
                  param47: item.param47,
                  param48: item.param48,
                  param49: item.param49,
                  param50: item.param50,
                  houjinId: systemCommonsStore.getHoujinId,
                  shisetuId: systemCommonsStore.getShisetuId,
                  svJigyoId: systemCommonsStore.getSvJigyoId,
                  zoomRate: item.zoomRate,
                  modifiedCnt: item.modifiedCnt,
                } as ChoPrtEntity)
              }
            }
          }
          reportData.printSubjectHistoryList.push({
            userId: '',
            userName: data.userName,
            sc1Id: '',
            startYmd: '',
            endYmd: '',
            raiId: '',
            assType: '',
            assDateYmd: '',
            assShokuId: '',
            result: data.result,
            choPrtList: choPrtList,
          } as PrintSubjectHistoryEntity)
          list.push({
            reportId: local.reportId,
            outputType: reportOutputType.DOWNLOAD,
            reportData: reportData,
            userName: data.userName,
            historyDate: '',
            result: data.result,
          } as OrX0117History)
        }
      }
    }
  }
  orX0117Oneway.historyList = list
}

/**
 * 切替前の印刷設定を保存する
 *
 * @param selectId - 出力帳票ID
 */
const setBeforChangePrintData = (selectId: string) => {
  if (selectId) {
    for (const item of localData.data.prtList) {
      if (item) {
        if (selectId === item.prtNo) {
          // 日付表示有無
          item.prnDate = mo00039Type.value
          // 敬称を変更するチェックボックス
          item.param03 = mo00018TitleOfHonorFlg.value.modelValue ? '1' : '0'
          // 敬称
          item.param04 = local.titleOfHonor.value
          // 承認欄を印刷するチェックボックス
          item.param05 = mo00018ApprovalPrintFlg.value.modelValue ? '1' : '0'
          // 一覧印刷するチェックボックス
          item.param07 = mo00018ListPrintFlg.value.modelValue ? '1' : '0'
          // 総括部分を印刷しないチェックボックス
          item.param09 = mo00018SummaryPrintFlg.value.modelValue ? '1' : '0'
          // 再アセスメントの必要性を印刷しないチェックbox
          item.param10 = mo00018ReassessmentFlg.value.modelValue ? '1' : '0'
          item.prtTitle = local.mo00045ModelTitle.value
        }
      }
    }
  }
}

/**
 * 切替後の印刷設定を画面に設定する
 *
 * @param selectId - 出力帳票ID
 */
const setAfterChangePrintData = (selectId: string) => {
  if (selectId) {
    for (const item of localData.data.prtList) {
      if (item) {
        if (selectId === item.prtNo) {
          // 日付表示有無
          mo00039Type.value = item.prnDate
          // 敬称を変更するチェックボックス
          mo00018TitleOfHonorFlg.value.modelValue = item.param03 === '1' ? true : false
          // 敬称
          local.titleOfHonor.value = item.param04
          // 承認欄を印刷するチェックボックス
          mo00018ApprovalPrintFlg.value.modelValue = item.param05 === '1' ? true : false
          // 一覧印刷するチェックボックス
          mo00018ListPrintFlg.value.modelValue = item.param07 === '1' ? true : false
          // 総括部分を印刷しないチェックボックス
          mo00018SummaryPrintFlg.value.modelValue = item.param09 === '1' ? true : false
          // 再アセスメントの必要性を印刷しないチェックボックス
          mo00018ReassessmentFlg.value.modelValue = item.param10 === '1' ? true : false
          local.mo00045ModelTitle.value = item.prtTitle
          break
        }
      }
    }
  }
}

/**
 * 「出力帳票名」選択
 *
 * @param selectId - 出力帳票ID
 */
const outputLedgerName = (selectId: string) => {
  if (!selectId) {
    selectId = Or57074Const.DEFAULT.SECTION_NO
  }
  let label = Or57074Const.DEFAULT.STR.EMPTY
  for (const item of mo01334Oneway.value.items) {
    if (item) {
      if (selectId === item.id && item.mo01337OnewayLedgerName) {
        const data = item.mo01337OnewayLedgerName as Mo01337OnewayType
        label = data.value
        mo01334Type.value.value = item.id

        // プロファイル
        local.profile = item.profile as string

        // 出力帳票名一覧に選択行番号
        local.index = item.index as string

        // 帳票番号
        local.prtNo = item.prtNo as string

        // 帳票ID
        setReportId(item.id)
        break
      }
    }
  }
  mo00045Type.value.value = label

  // 画面PDFダウンロードボタン活性非活性設定
  pdfDownloadBtnSetting(selectId)

  // 帳票イニシャライズデータを取得する
  void getSectionInitializeData()
}

/**
 * 帳票ID設定
 *
 * @param prtNo - 帳票番号
 */
const setReportId = (prtNo: string) => {
  switch (prtNo) {
    case Or57074Const.DEFAULT.STR.ONE:
      local.reportId = Or57074Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.TABLE
      break
    case Or57074Const.DEFAULT.STR.TWO:
      local.reportId = Or57074Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.TABLE_CASE_LIST
      break
    case Or57074Const.DEFAULT.STR.THREE:
      local.reportId = Or57074Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.TABLE_APPENDIX
      break
    case Or57074Const.DEFAULT.STR.FOUR:
      local.reportId = Or57074Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.TABLE_EVALUATION
      break
    default:
      local.reportId = Or57074Const.DEFAULT.STR.EMPTY
      break
  }
}

/**
 * 印刷設定情報リストを作成
 *
 * @param prtNo - 帳票番号
 */
const createReportOutputData = (prtNo: string) => {
  const list: OrX0117History[] = []
  for (const orX0128DetData of local.orX0128DetList) {
    const reportData: ICpnTucRaiAssReportSelectInEntity = {
      svJigyoKnj: local.svJigyoKnj,
      syscd: systemCommonsStore.getSystemCode ?? '71101',
      printSet: {
        shiTeiKubun: mo00039Type.value,
        shiTeiDate: mo00020Type.value.value ? mo00020Type.value.value.split('/').join('-') : '',
      } as PrintSetEntity,
      printOption: {
        emptyFlg: String(mo00018EmptyFlg.value.modelValue),
        kinyuAssType: '',
        colorFlg: String(mo00018TypeColor.value.modelValue),
      } as PrintOptionEntity,
      printSubjectHistoryList: [] as PrintSubjectHistoryEntity[],
    } as ICpnTucRaiAssReportSelectInEntity

    const choPrtList: ChoPrtEntity[] = []
    for (const item of localData.data.prtList) {
      if (item) {
        if (prtNo === item.prtNo) {
          choPrtList.push({
            shokuId: systemCommonsStore.getStaffId,
            sysRyaku: systemCommonsStore.getSystemAbbreviation,
            section: local.sectionName,
            prtNo: item.prtNo,
            choPro: item.profile,
            sectionName: item.defPrtTitle,
            dwobject: item.dwobject,
            prtOrient: item.prtOrient,
            prtSize: item.prtSize,
            listTitle: item.listTitle,
            prtTitle: item.prtTitle,
            mTop: item.mtop,
            mBottom: item.mbottom,
            mLeft: item.mleft,
            mRight: item.mright,
            ruler: item.ruler,
            prndate: item.prnDate,
            prnshoku: item.prnshoku,
            serialFlg: item.serialFlg,
            modFlg: item.modFlg,
            secFlg: item.secFlg,
            param01: item.param01,
            param02: item.param02,
            param03: item.param03,
            param04: item.param04,
            param05: item.param05,
            param06: item.param06,
            param07: item.param07,
            param08: item.param08,
            param09: item.param09,
            param10: item.param10,
            serialHeight: item.serialHeight,
            serialPagelen: item.serialPagelen,
            hsjId: systemCommonsStore.getHoujinId,
            param11: item.param11,
            param12: item.param12,
            param13: item.param13,
            param14: item.param14,
            param15: item.param15,
            param16: item.param16,
            param17: item.param17,
            param18: item.param18,
            param19: item.param19,
            param20: item.param20,
            param21: item.param21,
            param22: item.param22,
            param23: item.param23,
            param24: item.param24,
            param25: item.param25,
            param26: item.param26,
            param27: item.param28,
            param28: item.param28,
            param29: item.param29,
            param30: item.param30,
            param31: item.param31,
            param32: item.param32,
            param33: item.param33,
            param34: item.param34,
            param35: item.param35,
            param36: item.param36,
            param37: item.param37,
            param38: item.param38,
            param39: item.param39,
            param40: item.param40,
            param41: item.param41,
            param42: item.param42,
            param43: item.param43,
            param44: item.param44,
            param45: item.param45,
            param46: item.param46,
            param47: item.param47,
            param48: item.param48,
            param49: item.param49,
            param50: item.param50,
            houjinId: systemCommonsStore.getHoujinId,
            shisetuId: systemCommonsStore.getShisetuId,
            svJigyoId: systemCommonsStore.getSvJigyoId,
            zoomRate: item.zoomRate,
            modifiedCnt: item.modifiedCnt,
          } as ChoPrtEntity)
        }
      }
    }

    // TODO 印刷設定情報リストパラメータを作成
    reportData.printSubjectHistoryList.push({
      userId: local.userList.length > 0 ? local.userList[0].userId : Or57074Const.DEFAULT.STR.EMPTY,
      userName:
        local.userList.length > 0 ? local.userList[0].userName : Or57074Const.DEFAULT.STR.EMPTY,
      sc1Id: '1708',
      startYmd: '2022/10/01',
      endYmd: '2023/09/30',
      raiId: '63',
      assType: orX0128DetData.assType as string,
      assDateYmd: orX0128DetData.assDateYmd as string,
      assShokuId: Or57074Const.DEFAULT.STR.EMPTY,
      result: Or57074Const.DEFAULT.STR.EMPTY,
      choPrtList: choPrtList,
    } as PrintSubjectHistoryEntity)
    list.push({
      reportId: local.reportId,
      outputType: reportOutputType.DOWNLOAD,
      reportData: reportData,
      userName:
        local.userList.length > 0 ? local.userList[0].userName : Or57074Const.DEFAULT.STR.EMPTY,
      historyDate: orX0128DetData.assDateYmd as string,
      result: Or57074Const.DEFAULT.STR.EMPTY,
    } as OrX0117History)
  }
  orX0117Oneway.historyList = list
}

/**
 * 画面PDFダウンロードボタン活性非活性設定
 *
 * @param selectId - 出力帳票ID
 */
const pdfDownloadBtnSetting = (selectId: string) => {
  console.log('pdfDownloadBtnSetting', selectId)
  // PDFダウンロードボタンが活性
  localOneway.mo00609Oneway.disabled = false
}

/**
 * 帳票イニシャライズデータを取得する
 */
const getSectionInitializeData = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity = {
    profile: local.profile,
    gsyscd: systemCommonsStore.getSystemCode ?? '71101',
    shokuId: systemCommonsStore.getStaffId ?? '0',
    kojinhogoUsedFlg: Or57074Const.DEFAULT.KOJINHOGO_USED_FLG,
    sectionAddNo: Or57074Const.DEFAULT.SECTION_ADD_NO,
  } as IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity
  const resp: IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateOutEntity =
    await ScreenRepository.update('freeAssessmentFacePrintSettingsPrtNoChangeUpdate', inputData)
  if (resp.data) {
    local.sysIniInfo = resp.data.sysIniInfo
  }
}

/**
 * 印刷設定履歴リスト取得
 */
const getPrintSettingsHistoryList = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: PlanMonitoringPrintSettingsUserChangeSelectInEntity = {
    svJigyoId: local.svJigyoId,
    userId: local.selectUserId,
    index: local.index,
    kikanFlg: kikanFlag.value,
    cpnFlg: cmnRouteCom.getInitialSettingMaster()?.cpnFlg ?? Or57074Const.DEFAULT.STR.EMPTY,
  } as PlanMonitoringPrintSettingsUserChangeSelectInEntity
  const resp: PlanMonitoringPrintSettingsUserChangeSelectOutEntity = await ScreenRepository.select(
    'planMonitoringPrintSettingsUserChangeSelect',
    inputData
  )
  if (resp.data) {
    // アセスメント履歴一覧データ
    getHistoryData(resp.data.historyList)
  }
}

/**
 * 画面ボタン活性非活性設定
 */
const btnItemSetting = () => {
  // 利用者選択方法が「単一」の場合
  if (Or57074Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
    // 基準日を非表示にする
    // 履歴選択を活性表示にする
    kijunbiFlag.value = false

    // 履歴選択方法が「単一」の場合
    if (Or57074Const.DEFAULT.TANI === mo00039OneWayHistorySelectType.value) {
      // 記入用シートを印刷するチェックボックス表示
      localOneway.mo00018OneWayEmptyFlg.disabled = false
    }
    // 以外の場合
    else {
      // 記入用シートを印刷するを非活性表示にする
      localOneway.mo00018OneWayEmptyFlg.disabled = true
    }
  }
  // 以外の場合
  else {
    // 基準日を活性表示にする
    // 履歴選択を非表示にする
    kijunbiFlag.value = true
    localOneway.mo00018OneWayEmptyFlg.disabled = true
  }

  // 履歴選択方法が「単一」の場合
  if (Or57074Const.DEFAULT.TANI === mo00039OneWayHistorySelectType.value) {
    // 利用者選択方法が「単一」の場合
    if (Or57074Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
      // 記入用シートを印刷するチェックボックス表示
      localOneway.mo00018OneWayEmptyFlg.disabled = false
    }
    // 以外の場合
    else {
      // 記入用シートを印刷するをチェックオフにする
      mo00018EmptyFlg.value.modelValue = false
      localOneway.mo00018OneWayEmptyFlg.disabled = true
    }
  }
  // 以外の場合
  else {
    // 記入用シートを印刷するをチェックオフにする
    mo00018EmptyFlg.value.modelValue = false
    // 記入用シートを印刷するを非活性表示にする
    localOneway.mo00018OneWayEmptyFlg.disabled = true
  }

  // 履歴一覧セクション
  if (Or57074Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
    userCols.value = 5
    mo01334TypeHistoryFlag.value = true
  } else {
    userCols.value = 12
    mo01334TypeHistoryFlag.value = false
  }

  // 担当ケアマネ選択アイコンボタン非活性/活性設定
  const kkjTantoFlg: string =
    cmnRouteCom.getInitialSettingMaster()?.kkjTantoFlg ?? Or57074Const.DEFAULT.STR.EMPTY
  // 共通情報.担当ケアマネ設定フラグ > 0、且つ、共通情報.担当者IDが0以外の場合
  if (
    systemCommonsStore.getManagerId !== Or57074Const.DEFAULT.STR.ZERO &&
    parseInt(kkjTantoFlg) > Or57074Const.DEFAULT.NUMBER.ZERO
  ) {
    // 非活性
    localOneway.orX0145Oneway.disabled = true
  }
  // その他場合
  else {
    // 活性
    localOneway.orX0145Oneway.disabled = false
  }

  // 計画作成日セクション表示/非表示
  const cpnFlg: string =
    cmnRouteCom.getInitialSettingMaster()?.cpnFlg ?? Or57074Const.DEFAULT.STR.EMPTY
  if (cpnFlg === Or57074Const.DEFAULT.STR.FIVE) {
    // 共通情報.ケアプラン方式が"5:パッケージ"の場合
    mo00020CpnFlag.value = true
  } else {
    // 上記以外の場合
    mo00020CpnFlag.value = true
  }
  // 敬称/活性/非活性設定
  if (mo00018TitleOfHonorFlg.value.modelValue) {
    // 敬称を変更するチェックボックスがチェックオンの場合、活性。
    localOneway.mo00045OneWayTitleOfHonor.disabled = false
  } else {
    // 非活性
    localOneway.mo00045OneWayTitleOfHonor.disabled = true
  }

  // 一覧印刷するチェックボックス/活性/非活性設定
  if (mo01334Type.value.value === '1') {
    //  印刷帳票が"実施モニタリング表"の場合、活性。
    localOneway.mo00018OneWayListPrintFlg.disabled = false
  } else {
    // 非活性
    localOneway.mo00018OneWayListPrintFlg.disabled = true
    mo00018ListPrintFlg.value.modelValue = false
  }

  // 承認欄を印刷するチェックボックス/活性/非活性設定
  if (mo01334Type.value.value === '1') {
    //  印刷帳票が"実施モニタリング表"の場合、活性。
    localOneway.mo00018OneWayApprovalPrintFlg.disabled = false
  } else {
    // 非活性
    localOneway.mo00018OneWayApprovalPrintFlg.disabled = true
    mo00018ApprovalPrintFlg.value.modelValue = false
  }

  // 総括部分を印刷しないチェックボックス/活性/非活性設定
  if (mo01334Type.value.value === '3') {
    //  1.帳票インデックスが「3：モニタリング・評価表（カスタマイズ様式）」の場合、「活性」にする。
    localOneway.mo00018OneWaySummaryPrintFlg.disabled = false
    // 再アセスメントの必要性を印刷しないチェックボックス/活性/非活性設定
    // a.「総括部分を印刷しない」がチェックされない場合、「活性」にする。
    if (!mo00018SummaryPrintFlg.value.modelValue) {
      localOneway.mo00018OneWayReassessmentPrintFlg.disabled = false
    } else {
      // b.「総括部分を印刷しない」がチェックされる場合、「非活性」にする。
      localOneway.mo00018OneWayReassessmentPrintFlg.disabled = true
    }
    // チェックONの場合、チェックOFFにする。
  } else {
    // 2.上記以外の場合、「非活性」にする、チェックOFF。
    localOneway.mo00018OneWaySummaryPrintFlg.disabled = true
    mo00018SummaryPrintFlg.value.modelValue = false
    // 再アセスメントの必要性を印刷しないチェックボックス 「非活性」にする、チェックOFF。
    localOneway.mo00018OneWayReassessmentPrintFlg.disabled = true
    mo00018ReassessmentFlg.value.modelValue = false
  }

  // 種別プルダウン/活性/非活性設定
  if (mo01334Type.value.value === '3') {
    //   ①「5:新型養護老人ホームパッケージプラン」の場合、
    //  ①-1.帳票インデックスが「3：モニタリング・評価表（H21改訂版）」の場合、「表示」にする。
    //         ①-1-1.「記入用シートを印刷するチェックボックス」が「チェックする場合」、「活性」にする。
    if (mo00018EmptyFlg.value.modelValue === true) {
      localOneway.mo00040OneWayMonitorFlag.disabled = false
    } else {
      //  ①-1-2.上記以外の場合、「非活性」にする。
      localOneway.mo00040OneWayMonitorFlag.disabled = true
    }
  }
}
/**
 * 確認ダイアログ表示
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openConfirmDialog = async (
  uniqueCpId: string,
  state: Or21814OnewayType
): Promise<Or57074MsgBtnType> => {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result: Or57074MsgBtnType = Or57074Const.DEFAULT.MESSAGE_BTN_TYPE_YES

        if (event?.firstBtnClickFlg) {
          result = Or57074Const.DEFAULT.MESSAGE_BTN_TYPE_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or57074Const.DEFAULT.MESSAGE_BTN_TYPE_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = Or57074Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL
        }
        if (event?.closeBtnClickFlg) {
          result = Or57074Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * エラーダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openErrorDialog = async (
  uniqueCpId: string,
  state: Or21813StateType
): Promise<Or57074MsgBtnType> => {
  Or21813Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(uniqueCpId)

        let result = Or57074Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL as Or57074MsgBtnType

        if (event?.firstBtnClickFlg) {
          result = Or57074Const.DEFAULT.MESSAGE_BTN_TYPE_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or57074Const.DEFAULT.MESSAGE_BTN_TYPE_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = Or57074Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL
        }
        if (event?.closeBtnClickFlg) {
          result = Or57074Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL
        }

        // エラーダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 警告ーダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - 警告ーダイアログOneWayBind領域用の構造
 */
const openWarnDialog = async (
  uniqueCpId: string,
  state: Or21815StateType
): Promise<Or57074MsgBtnType> => {
  Or21815Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(uniqueCpId)

        let result: Or57074MsgBtnType = Or57074Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL

        if (event?.firstBtnClickFlg) {
          result = Or57074Const.DEFAULT.MESSAGE_BTN_TYPE_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or57074Const.DEFAULT.MESSAGE_BTN_TYPE_YES
        }
        if (event?.thirdBtnClickFlg) {
          result = Or57074Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL
        }
        if (event?.closeBtnClickFlg) {
          result = Or57074Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 「出力帳票名」選択
 */
watch(
  () => mo01334Type.value.value,
  (newValue, oldValue) => {
    setBeforChangePrintData(oldValue)
    setAfterChangePrintData(newValue)
    outputLedgerName(newValue)
    btnItemSetting()
    // 日付印刷区分が2の場合
    if (Or57074Const.DEFAULT.STR.TWO === mo00039Type.value) {
      // 指定日を活性表示にする。
      mo00020Flag.value = true
    } else {
      // 指定日を非表示にする。
      mo00020Flag.value = false
    }
  }
)

/**
 * 「日付印刷区分」ラジオボタン押下
 */
watch(
  () => mo00039Type.value,
  (newValue) => {
    if (Or57074Const.DEFAULT.STR.TWO === newValue) {
      // 指定日を活性表示にする
      mo00020Flag.value = true
    } else {
      // 指定日を非表示にする
      mo00020Flag.value = false
    }
  }
)

/**
 * 敬称を変更するチェックボックス押下
 */
watch(
  () => mo00018TitleOfHonorFlg.value.modelValue,
  (newValue) => {
    if (newValue === true) {
      localOneway.mo00045OneWayTitleOfHonor.disabled = false
    } else {
      localOneway.mo00045OneWayTitleOfHonor.disabled = true
    }
  }
)

/**
 * 「利用者選択方法」ラジオボタン選択
 */
watch(
  () => mo00039OneWayUserSelectType.value,
  (newValue) => {
    // 画面ボタン活性非活性設定
    btnItemSetting()

    // 利用者選択方法が「単一」の場合
    if (Or57074Const.DEFAULT.TANI === newValue) {
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.TANI
      orX0130Oneway.tableStyle = 'width: 210px'

      if (OrX0130Logic.event.get(orX0130.value.uniqueCpId)) {
        if (
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList &&
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList.length > 0
        ) {
          local.userList = []
          for (const item of OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList) {
            if (item) {
              local.userList.push({
                userId: item.userId,
                userName: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
              } as UserEntity)
            }
          }
          local.userNoSelect = false
        } else {
          local.userList = []
          local.userNoSelect = true
        }
      } else {
        local.userList = []
        local.userNoSelect = true
      }
    } else {
      // 復元
      orX0117Oneway.type = Or57074Const.DEFAULT.STR.ONE
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.HUKUSUU
      orX0130Oneway.tableStyle = 'width: 430px'

      if (OrX0130Logic.event.get(orX0130.value.uniqueCpId)) {
        if (
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList &&
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList.length > 0
        ) {
          local.userList = []
          for (const item of OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList) {
            if (item) {
              local.userList.push({
                userId: item.userId,
                userName: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
              } as UserEntity)
            }
          }
          local.userNoSelect = false
        } else {
          local.userList = []
          local.userNoSelect = true
        }
      } else {
        local.userList = []
        local.userNoSelect = true
      }
    }

    // 利用者一覧明細に親画面.利用者IDが存在する場合
    if (local.userId) {
      // 利用者IDを対するレコードを選択状態にする
      orX0130Oneway.userId = local.userId
    }
    // 画面PDFダウンロードボタン活性非活性設定
    pdfDownloadBtnSetting(local.prtNo)
  }
)

/**
 * 「履歴選択方法」ラジオボタン押下
 */
watch(
  () => mo00039OneWayHistorySelectType.value,
  (newValue) => {
    // 画面ボタン活性非活性設定
    btnItemSetting()

    if (OrX0128Logic.event.get(orX0128.value.uniqueCpId)) {
      if (
        OrX0128Logic.event.get(orX0128.value.uniqueCpId)!.orX0128DetList &&
        OrX0128Logic.event.get(orX0128.value.uniqueCpId)!.orX0128DetList.length > 0
      ) {
        local.historyNoSelect = false
      } else {
        local.historyNoSelect = true
      }
    } else {
      local.historyNoSelect = true
    }

    // 履歴選択方法が「単一」の場合
    if (Or57074Const.DEFAULT.TANI === newValue) {
      orX0128OnewayModel.singleFlg = OrX0128Const.DEFAULT.TANI
    } else {
      orX0128OnewayModel.singleFlg = OrX0128Const.DEFAULT.HUKUSUU
      orX0117Oneway.type = Or57074Const.DEFAULT.STR.ZERO
    }
  }
)

/**
 * 記入用シート方式ラジオボタン監視
 */
watch(
  () => mo00018EmptyFlg.value.modelValue,
  () => {
    // 画面PDFダウンロードボタン活性非活性設定
    pdfDownloadBtnSetting(local.prtNo)

    // 画面ボタン活性非活性設定
    btnItemSetting()
  }
)

/**
 * 「総括部分を印刷しない」チェックボックス監視
 */
watch(
  () => mo00018SummaryPrintFlg.value.modelValue,
  () => {
    // 画面ボタン活性非活性設定
    btnItemSetting()
  }
)

/**
 * 「履歴選択」の監視
 */
watch(
  () => OrX0128Logic.event.get(orX0128.value.uniqueCpId),
  (newValue) => {
    // 画面ボタン活性非活性設定
    btnItemSetting()
    if (newValue) {
      if (newValue.historyDetClickFlg) {
        local.orX0128DetList = newValue.orX0128DetList
        if (newValue.orX0128DetList.length > 0) {
          local.historyNoSelect = false
        } else {
          local.historyNoSelect = true
        }
      } else {
        local.historyNoSelect = true
      }
    }
  }
)

/**
 * 「利用者選択」の監視
 */
watch(
  () => OrX0130Logic.event.get(orX0130.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.clickFlg) {
      if (newValue.userList.length > 0) {
        local.userNoSelect = false
        local.selectUserId = newValue.userList[0].id

        local.userList = []
        for (const item of newValue.userList) {
          if (item) {
            local.userList.push({
              userId: item.userId,
              userName: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
            } as UserEntity)
          }
        }

        // 利用者選択方法が「単一」の場合
        if (Or57074Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
          if (initFlag.value) {
            // アセスメント履歴情報を取得する
            await getPrintSettingsHistoryList()
          }
        }
        // 利用者選択方法が「複数」の場合
        else if (Or57074Const.DEFAULT.HUKUSUU === mo00039OneWayUserSelectType.value) {
          // 利用者一覧明細に前回選択された利用者が選択状態になる
          createReportOutputData(local.prtNo)
        }
      } else {
        local.userNoSelect = true
        local.userList = []
      }
    } else {
      local.userNoSelect = true
      local.userList = []
    }
  }
)

/**
 * 「担当ケアマネプルダウン」選択の監視
 */
watch(
  () => orX0145Type.value.value,
  (newValue) => {
    // TODO 担当ケアマネ有機体が未作成 「担当ケアマネプルダウン」選択変更がある場合
    console.log(newValue)
  }
)

/**
 * イベントリスナーの解除
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    if (!newValue) {
      await save()
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        class="or57074_row flex-nowrap"
        no-gutter
      >
        <c-v-col
          cols="12"
          sm="auto"
          class="or57074_table"
        >
          <base-mo-01334
            v-model="mo01334Type"
            :oneway-model-value="mo01334Oneway"
            class="list-wrapper"
            style="text-align: left"
          >
            <!-- 帳票 -->
            <template #[`item.ledgerName`]="{ item }">
              <!-- 分子：一覧専用ラベル（文字列型） -->
              <base-mo01337 :oneway-model-value="item.mo01337OnewayLedgerName" />
            </template>
            <!-- ページングを非表示 -->
            <template #bottom />
          </base-mo-01334>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="auto"
          class="content_center"
        >
          <c-v-row
            no-gutter
            class="customCol or57074_row"
          >
            <c-v-col
              cols="12"
              sm="12"
            >
              <!-- <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayBasicSettings"
                style="background-color: transparent"
              >
              </base-mo01338> -->
              <base-mo00611
                :oneway-model-value="localOneway.mo00611Oneway2"
                @click.stop="openGUI01110"
              />
            </c-v-col>
          </c-v-row>
          <c-v-divider></c-v-divider>
          <!-- 基本設定  -->
          <c-v-row
            no-gutter
            class="customCol or57074_row pr-0"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pr-2"
            >
              <base-mo00045
                v-model="mo00045Type"
                :oneway-model-value="localOneway.mo00045OneWay"
                @update:model-value="inputChange"
              ></base-mo00045>
            </c-v-col>
          </c-v-row>

          <c-v-divider></c-v-divider>
          <!-- 日付印刷セクション -->
          <c-v-row
            no-gutter
            class="customCol or57074_row pt-2 pb-2 d-flex justify-space-between"
          >
            <c-v-col
              cols="12"
              sm="6"
              class="pa-0"
            >
              <base-mo00039
                v-model="mo00039Type"
                :oneway-model-value="localOneway.mo00039OneWay"
              >
              </base-mo00039>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="4"
              class="pa-0 pr-4"
            >
              <base-mo00020
                v-if="mo00020Flag"
                v-model="mo00020Type"
                :oneway-model-value="localOneway.mo00020OneWay"
              >
              </base-mo00020>
            </c-v-col>
          </c-v-row>
          <!-- 印刷オプション -->
          <c-v-row
            no-gutter
            class="printerOption customCol or57074_row"
          >
            <c-v-col
              cols="12"
              sm="12"
            >
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayPrinterOption"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
          <!-- 敬称を変更するチェックボックス -->
          <c-v-row
            no-gutter
            class="customCol or57074_row d-flex align-center pt-2 pb-2"
          >
            <c-v-col
              cols="12"
              sm="auto"
              class="pa-0"
            >
              <base-mo00018
                v-model="mo00018TitleOfHonorFlg"
                :oneway-model-value="localOneway.mo00018OneWayTitleOfHonorFlg"
              >
              </base-mo00018>
            </c-v-col>
            <!-- 敬称 -->
            <c-v-col
              cols="12"
              sm="5"
              class="pa-0 d-flex align-center"
            >
              <span class="pr-2">(</span>
              <base-mo00045
                v-model="local.titleOfHonor"
                :oneway-model-value="localOneway.mo00045OneWayTitleOfHonor"
              >
              </base-mo00045>
              <span> )</span>
            </c-v-col>
          </c-v-row>
          <!-- 承認欄を印刷するチェックボックス -->
          <c-v-row
            no-gutter
            class="customCol or57074_row pb-2"
          >
            <c-v-col
              cols="12"
              sm="auto"
              class="pa-0"
            >
              <base-mo00018
                v-model="mo00018ApprovalPrintFlg"
                :oneway-model-value="localOneway.mo00018OneWayApprovalPrintFlg"
              >
              </base-mo00018>
            </c-v-col>
            <!-- 承認欄の登録ボタン -->
            <c-v-col
              cols="12"
              sm="6"
              class="pa-0"
            >
              <base-mo00611
                :oneway-model-value="localOneway.mo00611OneWayApproval"
                @click="openGUI00617"
              >
              </base-mo00611>
            </c-v-col>
          </c-v-row>
          <!-- 記入用シートを印刷するチェックボックス -->
          <c-v-row
            no-gutter
            class="customCol or57074_row pb-2"
          >
            <c-v-col
              cols="12"
              sm="6"
              class="pa-0"
            >
              <base-mo00018
                v-model="mo00018EmptyFlg"
                :oneway-model-value="localOneway.mo00018OneWayEmptyFlg"
              >
              </base-mo00018>
            </c-v-col>
            <!-- 種別プルダウン -->
            <c-v-col
              v-if="mo01334Type.value === '3' && isCarePlanStyle5"
              cols="12"
              sm="6"
              class="pa-0 pr-2"
            >
              <base-mo00040
                v-model="local.mo00040TypeMonitorFlag"
                :oneway-model-value="localOneway.mo00040OneWayMonitorFlag"
              />
            </c-v-col>
          </c-v-row>
          <!-- 一覧印刷する -->
          <c-v-row
            no-gutter
            class="customCol or57074_row pb-2"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <base-mo00018
                v-model="mo00018ListPrintFlg"
                :oneway-model-value="localOneway.mo00018OneWayListPrintFlg"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>
          <!-- 印刷する要介護度 -->
          <!-- 共通情報.ケアプラン方式が"5:パッケージ"の場合、非表示。上記以外の場合、表示 -->
          <c-v-row
            v-if="!isCarePlanStyle5"
            no-gutter
            class="customCol or57074_row d-flex align-center pb-3"
          >
            <c-v-col
              cols="12"
              sm="auto"
              class="pa-0 pl-3"
            >
              <base-mo00615
                :oneway-model-value="localOneway.mo00615OneWayKaigodo"
                class="input-hide-border"
              >
              </base-mo00615>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="auto"
              class="pa-0 pr-2"
            >
              <base-mo00040
                v-model="local.mo00040TypeKaigodo"
                :oneway-model-value="localOneway.mo00040OneWayKaigodo"
              />
            </c-v-col>
          </c-v-row>
          <!-- 総括部分を印刷しないチェックボックス -->
          <!-- 「5:新型養護老人ホームパッケージプラン」の場合、非表示にする。 -->
          <c-v-row
            v-if="!isCarePlanStyle5"
            no-gutter
            class="customCol or57074_row pb-2"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <base-mo00018
                v-model="mo00018SummaryPrintFlg"
                :oneway-model-value="localOneway.mo00018OneWaySummaryPrintFlg"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>

          <!-- 再アセスメントの必要性を印刷しないチェックボックス -->
          <c-v-row
            v-if="!isCarePlanStyle5"
            no-gutter
            class="customCol or57074_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0"
            >
              <base-mo00018
                v-model="mo00018ReassessmentFlg"
                :oneway-model-value="localOneway.mo00018OneWayReassessmentPrintFlg"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="auto"
          class="content_center_2"
          style="width: 619px"
        >
          <c-v-row
            class="or57074_row"
            no-gutter
            style="align-items: center; padding-bottom: 8px"
          >
            <c-v-col
              cols="12"
              sm="4"
              style="padding: 0px"
            >
              <c-v-row
                no-gutter
                class="or57074_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                >
                  <base-mo01338
                    :oneway-model-value="localOneway.mo01338OneWayPrinterUserSelect"
                    style="background-color: transparent"
                  >
                  </base-mo01338>
                </c-v-col>
              </c-v-row>
              <c-v-row
                no-gutter
                class="or57074_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  style="padding: 0px"
                >
                  <base-mo00039
                    v-model="mo00039OneWayUserSelectType"
                    :oneway-model-value="localOneway.mo00039OneWayUserSelectType"
                  >
                  </base-mo00039>
                </c-v-col>
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-if="kijunbiFlag"
              cols="12"
              sm="4"
              style="padding: 0px"
            >
              <c-v-row
                no-gutter
                class="or57074_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                >
                  <base-mo00615 :oneway-model-value="localOneway.mo00615OneWayType"> </base-mo00615>
                </c-v-col>
              </c-v-row>
              <c-v-row
                no-gutter
                class="or57074_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  style="padding: 0px"
                >
                  <base-mo00020
                    v-model="mo00020TypeKijunbi"
                    :oneway-model-value="localOneway.mo00020KijunbiOneWay"
                  />
                </c-v-col>
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-else
              cols="12"
              sm="4"
              style="padding: 0px"
            >
              <c-v-row
                no-gutter
                class="or57074_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                >
                  <base-mo01338
                    :oneway-model-value="localOneway.mo01338OneWayPrinterHistorySelect"
                    style="background-color: transparent"
                  >
                  </base-mo01338>
                </c-v-col>
              </c-v-row>
              <c-v-row
                no-gutter
                class="or57074_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  style="padding: 0px"
                >
                  <base-mo00039
                    v-model="mo00039OneWayHistorySelectType"
                    :oneway-model-value="localOneway.mo00039OneWayHistorySelectType"
                  >
                  </base-mo00039>
                </c-v-col>
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-if="tantoIconBtn"
              cols="12"
              sm="4"
              style="padding: 0px"
            >
              <!-- 担当ケアマネプルダウン -->
              <g-custom-or-x-0145
                v-bind="orx0145"
                v-model="orX0145Type"
                :oneway-model-value="localOneway.orX0145Oneway"
              ></g-custom-or-x-0145>
            </c-v-col>
          </c-v-row>
          <!-- <c-v-divider></c-v-divider> -->
          <c-v-row
            class="or57074_row flex-nowrap"
            no-gutter
          >
            <c-v-col :cols="userCols">
              <!-- 印刷設定画面利用者一覧 -->
              <g-custom-or-x-0130
                v-if="orX0130Oneway.selectMode && initFlag"
                v-bind="orX0130"
                :oneway-model-value="orX0130Oneway"
              >
              </g-custom-or-x-0130>
            </c-v-col>
            <c-v-col
              v-if="mo01334TypeHistoryFlag && initFlag"
              cols="auto"
              class="pr-2"
              style="width: 448px; height: inherit"
            >
              <div style="width: 100%; height: 100%; overflow: hidden">
                <!-- 計画期間＆履歴一覧 -->
                <g-custom-or-x-0128
                  v-if="orX0128OnewayModel.singleFlg"
                  v-bind="orX0128"
                  :oneway-model-value="orX0128OnewayModel"
                ></g-custom-or-x-0128>
              </div>
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->

      <c-v-row>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          @click="close"
        >
        </base-mo00611>
        <!-- PDFダウンロードボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          class="mx-2"
          @click="pdfDownload()"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- メッセージ 警告 -->
  <g-base-or-21815 v-bind="or21815" />
  <!-- 印刷設定帳票出力状態リスト画面 -->
  <g-custom-or-x-0117
    v-if="showDialogOrX0117"
    v-bind="orX0117"
    :oneway-model-value="orX0117Oneway"
  ></g-custom-or-x-0117>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814 v-bind="or21814" />
  <!--「GUI01110_印鑑欄設定」画面-->
  <g-custom-or-10016
    v-if="showDialogOr10016"
    v-bind="or10016_1"
    :oneway-model-value="localOneway.or10016Oneway"
  />
  <!--GUI00617_承認欄登録画面-->
  <g-custom-or-x0135
    v-if="showDialogOrX0135"
    v-bind="orX0135_1"
    v-model="local.orX0135Type"
    :oneway-model-value="localOneway.orX0135Oneway"
    :parent-unique-cp-id="props.uniqueCpId"
  />
</template>
<style>
.or57074_content {
  padding: 0px !important;
}

.or57074_gokeiClass {
  label {
    color: #ffffff;
  }
}
</style>
<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';
:deep(.v-table__wrapper) {
  overflow-x: auto !important;
}

.or57074_table {
  padding: 0px !important;
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.or57074_row {
  margin: 0px !important;
}

.content_center {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.content_center,
.content_center_2 {
  padding: 0px !important;

  .label_left {
    padding-right: 0px;
  }

  .label_right {
    padding-left: 0px;
    padding-right: 0px;
  }

  .printerOption {
    background-color: #edf1f7;
  }

  :deep(.label-area-style) {
    display: none;
  }

  .customCol {
    margin-left: 0px;
    margin-right: 0px;
  }
}
</style>
