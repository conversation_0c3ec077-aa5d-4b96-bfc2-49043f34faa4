<script setup lang="ts">
/**
 * Or28635: ［経過管理］画面ダイアログ
 * GUI00622_［経過管理］画面
 *
 * @description
 *  ［経過管理］画面 ダイアログ
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { onMounted, reactive, ref, watch, computed } from 'vue'

import { Gui00018Logic } from '../Gui00018/Gui00018.logic'
import { Gui00018Const } from '../Gui00018/Gui00018.constants'
import { Or28635Const } from './Or28635.constants'
import type { Or28635StateType } from './Or28635.type'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import { useScreenOneWayBind ,useSetupChildProps} from '~/composables/useComponentVue'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Or28635OnewayType } from '~/types/cmn/business/components/Or28635Type'

import type { Mo01334OnewayType } from '~/types/business/components/Mo01334Type'
import type {
  PassageManagementSelectInEntity,
  PassageManagementSelectOutEntity,
} from '~/repositories/cmn/entities/PassageManagementSelectEntity'


const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or28635OnewayType
  uniqueCpId: string
}
/**
 *props
 */
const props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/
/**
 *gui00018 ダイヤログ uniqueCpId
 */
// const gui00018Test = ref({ uniqueCpId: '' })
const gui00018 = ref({ uniqueCpId: '' })
// 片方向バインド用の内部変数
/**
 *ローカルOneway
 */
const localOneway = reactive({
  or28635: {
    ...props.onewayModelValue,
  },
  // 閉じるコンポーネント
  mo00611CloseBtnOneWay: {
    btnLabel: t('label.close'),
    width: '90px',
    tooltipText: t('tooltip.screen-close'),
  } as Mo00611OnewayType,
  // 確定コンポーネント
  mo00609ConfirmOneway: {
    btnLabel: t('btn.confirm'),
    width: '90px',
    tooltipText: t('tooltip.confirm-btn'),
  } as Mo00609OnewayType,
  mo00024Oneway: {
    height: '570px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      toolbarTitle: t('label.passagemanagement'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
    },
  } as Mo00024OnewayType,
  mo01334HistoryselectOnewayModelValue: {
    // ヘッダーに表示される名称
    headers: [],
    height: 400,
    items: [],
  } as Mo01334OnewayType,
  csvOutput: {
    btnLabel: t('btn.csv-btn'),
    tooltipText: t('tooltip.csv-output'),
  } as Mo00611OnewayType,
})

/**
 *ダイアログ設置
 */
const mo00024 = ref<Mo00024Type>({
  isOpen: Or28635Const.DEFAULT.IS_OPEN,
})

/**************************************************
 * Pinia
 **************************************************/
/**
 *State
 */
const { setState } = useScreenOneWayBind<Or28635StateType>({
  cpId: Or28635Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    /**
     * ダイヤログ設定
     *
     * @param value - ダイヤログ open
     */
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or28635Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Gui00018Const.CP_ID(0)]: gui00018.value,
})

onMounted(async () => {
  //初期情報取得
  await getInitDataInfo()
})

/** 初期情報取得 */
const getInitDataInfo = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: PassageManagementSelectInEntity = {
    shokuinId: localOneway.or28635.shokuinId,
    gSysCd: localOneway.or28635.gSysCd,
  }
  const resData: PassageManagementSelectOutEntity = await ScreenRepository.select(
    'passageManagementSelect',
    inputData
  )

  localOneway.mo01334HistoryselectOnewayModelValue.headers = resData.data.displayTitleList.map(
    (item) => {
      return {
        title: item.dispnameKnj,
        key: item.colnameKnj,
        sortable: false,
        minWidth: '180px',
      }
    }
  )
  localOneway.mo01334HistoryselectOnewayModelValue.items = localOneway.or28635.dataList.map(
    (item, index) => {
      return {
        id: index + '',
        ...item,
      }
    }
  )
}
const showDialog = computed(() => {
  // GuiD00018のダイアログ開閉状態
  return Gui00018Logic.state.get(gui00018.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 「CSV出力」ボタン押下
 */
const onClickCSVBtn = () => {
  //   システムコード：親画面.システムコード
  // 統計処理ID：802
  // 統計処理種類：1
  // CSV機能区分：親画面.機能区分
  // 統計機能名：“[win1][3GK]経過管理CSV共通ダイアログ"
  // 施設IDリスト：共通情報.施設IDリスト
  // 利用登録：0
  // 事業所IDリスト：共通情報.事業所IDリスト
  // 利用開始日：-
  // 利用終了日：-
  // 出力範囲タイプ：-
  // 出力範囲開始日：-
  // 出力範囲終了日：-
  // 給付種類 ：-
  // 給付率：-
  // 参照対象：-

  Gui00018Logic.state.set({
    uniqueCpId: gui00018.value.uniqueCpId,
    state: { isOpen: true },
  })
}
/**
 * 閉じるボタン押下時
 */
const onClickCloseBtn = (): void => {
  setState({ isOpen: false })
}
/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      onClickCloseBtn()
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet>
        <c-v-row no-gutters>
          <c-v-col class="d-flex justify-end mr-4">
            <base-mo00611
              :oneway-model-value="localOneway.csvOutput"
              @click="onClickCSVBtn"
            ></base-mo00611>
          </c-v-col>
        </c-v-row>
        <c-v-row
          no-gutters
          class="pa-2"
        >
          <c-v-col
            cols="12"
            class="table-header"
          >
            <base-mo01334
              class="list-wrapper"
              hide-default-footer
              :oneway-model-value="localOneway.mo01334HistoryselectOnewayModelValue"
            >
            </base-mo01334>
          </c-v-col>
        </c-v-row>
      </c-v-sheet>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          class="mr-2"
          @click="onClickCloseBtn"
        />
      </c-v-row>
    </template>
  </base-mo00024>
  <g-custom-gui-00018
    v-if="showDialog"
    v-bind="gui00018"
  />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';
</style>
