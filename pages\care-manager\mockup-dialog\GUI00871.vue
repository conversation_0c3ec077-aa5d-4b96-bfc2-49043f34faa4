<script setup lang="ts">
/**
 * GUI00871:有機体:印刷設定
 * GUI00871_印刷設定
 *
 * @description
 * 印刷設定
 *
 * <AUTHOR>
 */
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or55904Const } from '~/components/custom-components/organisms/Or55904/Or55904.constants'
import { Or55904Logic } from '~/components/custom-components/organisms/Or55904/Or55904.logic'
import type { Or55904Param } from '~/components/custom-components/organisms/Or55904/Or55904.type'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI00871'
// ルーティング
const routing = 'GUI00871/pinia'
// 画面物理名
const screenName = 'GUI00871'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or55904 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00871' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 子コンポーネントのユニークIDを設定する
// or55904.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI00871',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or55904Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or55904Const.CP_ID(1)]: or55904.value,
})

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  Or55904Logic.initialize(or55904.value.uniqueCpId)
}

// ダイアログ表示フラグ
const showDialogOr55904 = computed(() => {
  // Or55904のダイアログ開閉状態
  return Or55904Logic.state.get(or55904.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(Or55904)--期間管理フラグが「管理する」の場合
 *
 */
function onClickOr55904_1() {
  // Or55904のダイアログ開閉状態を更新する
  Or55904Logic.state.set({
    uniqueCpId: or55904.value.uniqueCpId,
    state: {
      isOpen: true,
      param: {
        shokuId: '1',
        prtNo: '1',
        svJigyoId: '1',
        shisetuId: '1',
        tantoId: '1',
        syubetsuId: '2',
        sectionName: 'インターライ方式ケアアセスメント表',
        userId: '2',
        assessmentId: '2',
        svJigyoKnj: '1',
        processYmd: '2025/07/02',
        parentUserIdSelectDataFlag: false,
        focusSettingInitial: ['あ', 'い', 'う', 'え', 'お'],
        selectedUserCounter: '2',
      } as Or55904Param,
    },
  })
}

/**************************************************
 * コンポーネント固有処理
 **************************************************/
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr55904_1"
        >GUI00871_印刷設定</v-btn
      >
      <g-custom-or-55904
        v-if="showDialogOr55904"
        v-bind="or55904"
      />
    </c-v-col>
  </c-v-row>
</template>
