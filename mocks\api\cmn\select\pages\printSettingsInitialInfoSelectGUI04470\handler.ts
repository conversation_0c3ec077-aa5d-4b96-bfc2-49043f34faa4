/**
 * OrX0149:有機体:印刷設定モーダル
 * GUI04470_［印刷設定］画面
 *
 * <AUTHOR>
 */
import { HttpResponse } from 'msw'
import defaultData from './data/default.json'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { printSettingsInitialInfoSelectGUI04470InEntity } from '~/repositories/cmn/entities/PrintSettingsInitialInfoSelectGUI04470Entity'

/**
 *  OrX0149:有機体:印刷設定モーダル画面処理の取得APIモック
 *
 * @description
 *  OrX0149:有機体:印刷設定モーダル画面処理のメイン画面に表示されるデータを返却する。
 * dataName："PrintSettingsInitialInfoSelectGUI04470"
 */
export function handler(inEntity: printSettingsInitialInfoSelectGUI04470InEntity) {
  const responceJson: BaseResponseBody = {
    statusCode: 'success',
    data: {
      ...defaultData,
    },
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
