<script setup lang="ts">
/**
 * Or28675：有機体：月間・年間表複写 履歴情報一覧
 * GUI00941_月間・年間表複写POP画面
 *
 * @description
 * 月間・年間表複写 履歴情報一覧
 *
 * <AUTHOR>
 */
import { reactive } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or28675Const } from './Or28675.constants'
import type { Or28675OnewayType, Or28675Type } from '~/types/cmn/business/components/Or28675Type'
import { useCommonProps } from '~/composables/useCommonProps'
import { useScreenOneWayBind, useScreenTwoWayBind } from '#imports'
import type { IRirekiInfo } from '~/repositories/cmn/entities/MonthlyYearlyTableCopyInfoSelectEntity'

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
const props = defineProps(useCommonProps())
const localOneway = reactive({
  or28675Oneway: {
    rirekiList: [] as IRirekiInfo[],
    kikanFlg: '',
  } as Or28675OnewayType,
  headers: [],
})
/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<Or28675Type>({
  cpId: Or28675Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
useScreenOneWayBind<Or28675OnewayType>({
  cpId: Or28675Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    rirekiList: (value?: IRirekiInfo[]) => {
      localOneway.or28675Oneway.rirekiList = value!
    },
    kikanFlg: (value) => {
      localOneway.or28675Oneway.kikanFlg = value!
    },
  },
})
// テーブルヘッダ
const TABLE_HEADER = [
  // 作成日
  {
    title: t('label.create-date'),
    key: 'createYmd',
    sortable: false,
    width: '100px',
  },
  // 作成者
  { title: t('label.author'), key: 'shokuKnj', sortable: false, width: '141px' },
  // ケース番号
  { title: t('label.caseNo'), key: 'caseNo', sortable: false, width: '120px' },
  // 年度
  {
    title: t('label.fiscal-year'),
    key: 'nendoY',
    sortable: false,
    width: '80px',
  },
  // 改訂
  { title: t('label.revision'), key: 'revision', sortable: false, width: '80px' },
]

//事業所名
const HEADER_OFFICE = [
  {
    title: t('label.office-name'), // ヘッダーに表示される名称
    key: 'jigyoRyakuKnj',
    width: '180px',
    sortable: false,
  },
]

/**
 * 履歴選択
 *
 * @param item - 履歴情報
 */
function periodSelectRow(item: IRirekiInfo) {
  if (refValue.value!.rirekiId !== item.rirekiId) {
    refValue.value!.rirekiId = item.rirekiId
    refValue.value!.isRowChange = true
  }
}
</script>

<template>
  <c-v-data-table
    :items="localOneway.or28675Oneway.rirekiList"
    :headers="
      localOneway.or28675Oneway.kikanFlg === '0'
        ? [...TABLE_HEADER, ...HEADER_OFFICE]
        : TABLE_HEADER
    "
    :hide-default-footer="true"
    class="table-wrapper"
    height="135px"
    :items-per-page="-1"
    hover
    fixed-header
  >
    <!-- 一覧 -->
    <template #item="{ item }">
      <tr
        :class="{ 'row-selected': refValue!.rirekiId === item.rirekiId }"
        @click="periodSelectRow(item)"
      >
        <!-- 作成日 -->
        <td>
          <base-mo01335 :oneway-model-value="{ value: item.createYmd }" />
        </td>
        <!-- 作成者 -->
        <td>
          <base-mo01337
            :oneway-model-value="{
              value: item.shokuKnj,
            }"
          ></base-mo01337>
        </td>
        <!-- ケース番号 -->
        <td>
          <base-mo01336
            :oneway-model-value="{
              value: item.caseNo,
            }"
          ></base-mo01336>
        </td>
        <!-- 年度 -->
        <td class="year-right">
          <span>{{ item.nendoY }}</span>
        </td>
        <!-- 改訂 -->
        <td>
          <base-mo01337
            :oneway-model-value="{
              value: item.kaiteiFlg === '2' ? Or28675Const.REVISION_H21 : '',
            }"
          ></base-mo01337>
        </td>
        <!-- 事業所名 -->
        <td v-if="localOneway.or28675Oneway.kikanFlg === '0'">
          <base-mo01337
            :oneway-model-value="{
              value: item.jigyoRyakuKnj,
            }"
          ></base-mo01337>
        </td>
      </tr>
    </template>
  </c-v-data-table>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';
.year-right {
  text-align: right;
}
</style>
