<script setup lang="ts">
/**
 * Or13872:有機体:（アセスメント（居宅）計画対象期間入力
 * GUI00794_［アセスメント］画面（居宅）（1）
 * GUI00795_［アセスメント］画面（居宅）（2）
 * GUI00804_［アセスメント］画面（居宅）（6医）
 * GUI00805_［アセスメント］画面（居宅）（7まとめ）
 * GUI00806_［アセスメント］画面（居宅）（7スケジュール）
 * GUI00812_［アセスメント］画面（居宅）（6④）
 *
 * @description
 * 計画対象期間を表示するためのコンポーネント。
 *
 * <AUTHOR>
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or13844Const } from './Or13844.constants'
import { Or13844Logic } from './Or13844.logic'
import { useNuxtApp } from '#app'
import type { DebugLogPluginInterface } from '~/plugins/debug-log'
import type { Or13844OnewayType } from '~/types/cmn/business/components/Or13844Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
const $log = useNuxtApp().$log as DebugLogPluginInterface

/**************************************************
 * Props
 **************************************************/
interface Props {
  /** uniqueCpId */
  uniqueCpId: string
  onewayModelValue?: Or13844OnewayType
}

const props = defineProps<Props>()

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['openBtnClick'])

const openBtnClick = () => {
  emit('openBtnClick')
}

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

/** One-way */
const localOneway = reactive({
  or13844Oneway: {
    plainningPeriodManageFlg: '',
    planningPeriod: {} as {
      /** * 計画対象期間Id */
      id: string
      /** * 計画対象期間 */
      period: string
      /** 期間番号 */
      periodNo: string
      /** 期間総件数 */
      periodCnt: string
    },
    /** < > ボタン自動活性化フラグ */
    pageBtnAutoDisabled: false,
    /** 計画対象期間がない場合、メッセージ表示自動活性化フラグ */
    labelAutoDisabled: true,
    showLabelMode: true,
  } as Or13844OnewayType,
  // 計画対象期間ラベル
  mo00615Oneway: {
    /** 項目名 */
    itemLabel: t('label.planning-period'),
    /** アイテムラベル表示フラグ */
    showItemLabel: true,
    /** 必須ラベル表示フラグ */
    showRequiredLabel: false,
    /** 項目名のフォントの太さ */
    itemLabelFontWeight: 'normal',
    /** カスタムクラス */
    customClass: new CustomClass({
      outerClass: '',
      itemClass: 'ml-4 align-center',
    }),
  } as Mo00615OnewayType,
  // 編集アイコンボタン
  mo00009Oneway: {
    btnIcon: 'edit_square',
    name: 'officeSelectIconBtn',
    density: 'compact',
    minWidth: '24px',
    minHeight: '24px',
    width: '24px',
    height: '24px',
  } as Mo00009OnewayType,
  // 計画対象期間
  planningPeriodMo01338Oneway: {
    value: '',
    customClass: new CustomClass({ outerClass: 'pa-1', outerStyle: 'ml-2' }),
  } as Mo01338OnewayType,
  planningPeriodLabel: '',
  // 履歴 < ボタン
  leftMo00009Oneway: {
    name: 'leftIconBtn',
    minWidth: '24px',
    minHeight: '24px',
    width: '24px',
    height: '24px',
    disabled: false,
    btnIcon: 'chevron_left',
  } as Mo00009OnewayType,
  // 履歴ページ
  rightMo00009Oneway: {
    name: 'rifhtIconBtn',
    minWidth: '24px',
    minHeight: '24px',
    width: '24px',
    height: '24px',
    disabled: false,
    btnIcon: 'chevron_right',
  } as Mo00009OnewayType,
  // 履歴 > ボタン
  hisotryPageMo01338Oneway: {
    value: '',
    customClass: new CustomClass({ outerClass: 'pa-1', outerStyle: 'ml-2' }),
  } as Mo01338OnewayType,
})

const isPlanningPeriodRegister = ref(true)

// < ボタン活性化フラグ
const isPreBtnDisabled = computed(() => {
  if (localOneway.or13844Oneway.pageBtnAutoDisabled) {
    if (localOneway.or13844Oneway.planningPeriodInfo?.periodCnt) {
      const intNo = parseInt(localOneway.or13844Oneway.planningPeriodInfo?.periodNo)
      if (intNo <= 1) {
        return true
      } else {
        return false
      }
    }
  }

  return false
})

// > ボタン活性化フラグ
const isNextBtnDisabled = computed(() => {
  if (localOneway.or13844Oneway.pageBtnAutoDisabled) {
    if (
      localOneway.or13844Oneway.planningPeriodInfo?.periodCnt &&
      localOneway.or13844Oneway.planningPeriodInfo?.periodCnt
    ) {
      const intNo = parseInt(localOneway.or13844Oneway.planningPeriodInfo?.periodNo)
      const intTotalCount = parseInt(localOneway.or13844Oneway.planningPeriodInfo?.periodCnt)
      if (intNo >= intTotalCount) {
        return true
      } else {
        return false
      }
    }
  }

  return false
})

/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(() => {
  $log.debug(`★[onMounted] [cpId]${Or13844Const.CP_ID(0)} [uId]${props.uniqueCpId}`)

  localOneway.hisotryPageMo01338Oneway.value = '0/0'
})

/**************************************************
 * ウォッチャー
 **************************************************/
watch(
  () => props.onewayModelValue,
  (newValue) => {
    if (newValue) {
      localOneway.or13844Oneway = {
        ...localOneway.or13844Oneway,
        ...newValue,
      }

      // 日付を設定
      if (
        localOneway.or13844Oneway.plainningPeriodManageFlg ===
        Or13844Const.DEFAULT.PLANNING_PERIOD_MANAGE
      ) {
        // 計画期間管理フラグが「1:管理する」場合、日付を設定
        if (newValue.planningPeriodInfo?.period) {
          localOneway.planningPeriodLabel = newValue.planningPeriodInfo?.period
          localOneway.planningPeriodMo01338Oneway.value = newValue.planningPeriodInfo?.period
          localOneway.planningPeriodMo01338Oneway.customClass = new CustomClass({
            outerClass: 'pa-1',
            outerStyle: 'ml-2',
          })
          isPlanningPeriodRegister.value = true
        } else {
          // 計画対象期間がない場合、メッセージ表示
          // "計画対象期間アイコンボタンから期間登録を行ってください"
          if (localOneway.or13844Oneway.labelAutoDisabled) {
            localOneway.planningPeriodLabel = t('label.planning-period-no-manage')
            localOneway.planningPeriodMo01338Oneway.value = t('label.planning-period-no-manage')
            localOneway.planningPeriodMo01338Oneway.customClass = new CustomClass({
              outerClass: 'pa-1',
              outerStyle: 'ml-2',
              itemClass: 'planningPeriodRegister',
            })
          } else {
            localOneway.planningPeriodLabel = newValue.planningPeriodInfo?.period ?? ''
            localOneway.planningPeriodMo01338Oneway.value =
              newValue.planningPeriodInfo?.period ?? ''
            localOneway.planningPeriodMo01338Oneway.customClass = new CustomClass({
              outerClass: 'pa-1',
              outerStyle: 'ml-2',
            })
          }
          isPlanningPeriodRegister.value = false
        }

        // データ数を設定
        localOneway.hisotryPageMo01338Oneway.value =
          newValue.planningPeriodInfo?.periodNo + '/' + newValue.planningPeriodInfo?.periodCnt
      }
    }
  },
  { deep: true }
)

watch(
  () => props.onewayModelValue?.showLabelMode,
  (newValue) => {
    if (newValue === undefined) return
    localOneway.or13844Oneway.showLabelMode = newValue
  },
  { deep: true, immediate: true }
)

/**************************************************
 * 関数
 **************************************************/
/**
 * 選択画面ボタンクリック処理
 */
function onOpenBtnClick() {
  openBtnClick()
}

/**
 * <ボタンクリック処理
 */
function preBtnClick() {
  Or13844Logic.event.set({
    uniqueCpId: props.uniqueCpId,
    events: {
      preBtnClickFlg: true,
    },
  })
}

/**
 * >ボタンクリック処理
 */
function nextBtnClick() {
  Or13844Logic.event.set({
    uniqueCpId: props.uniqueCpId,
    events: {
      nextBtnClickFlg: true,
    },
  })
}
</script>

<template>
  <c-v-row
    class="componentWrapper flex-0-0 w-auto ma-0"
    :class="{ 'vertical-mode': localOneway.or13844Oneway.showLabelMode === false }"
  >
    <c-v-col class="itemTitle">
      <base-mo-00615 :oneway-model-value="localOneway.mo00615Oneway"></base-mo-00615>
    </c-v-col>
    <c-v-col>
      <c-v-row
        no-gutters
        align="center"
      >
        <c-v-col class="align-self-center">
          <base-mo00009
            :oneway-model-value="localOneway.mo00009Oneway"
            @click="onOpenBtnClick"
          >
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.assessment-home-planning-period-show')"
            />
          </base-mo00009>
        </c-v-col>
        <c-v-col class="align-self-center">
          <div
            :class="{ planningPeriodRegister: !isPlanningPeriodRegister }"
            class="item-label pl-2"
          >
            {{ localOneway.planningPeriodLabel }}
          </div>
        </c-v-col>
        <c-v-col
          cols="auto"
          class="align-self-center ml-1"
        >
          <!-- ＜ボタン -->
          <base-mo00009
            v-bind="{ ...$attrs }"
            class="iconBtn"
            :oneway-model-value="localOneway.leftMo00009Oneway"
            :disabled="isPreBtnDisabled"
            @click="preBtnClick"
          />
        </c-v-col>
        <c-v-col
          cols="auto"
          class="align-self-center"
        >
          <!-- 期間 -->
          <base-mo01338 :oneway-model-value="localOneway.hisotryPageMo01338Oneway"></base-mo01338>
        </c-v-col>
        <c-v-col
          cols="auto"
          class="align-self-center"
        >
          <!--＞ボタン -->
          <base-mo00009
            v-bind="{ ...$attrs }"
            class="iconBtn"
            :oneway-model-value="localOneway.rightMo00009Oneway"
            :disabled="isNextBtnDisabled"
            @click="nextBtnClick"
          />
        </c-v-col>
      </c-v-row>
    </c-v-col>
  </c-v-row>
</template>

<style scoped lang="scss">
.componentWrapper {
  .v-col {
    max-width: unset;
    width: auto;
    flex: 0 1 auto;
    padding: 0;
    align-self: center;

    .v-sheet {
      background: rgb(var(--v-theme-background));
    }

    :deep(label) {
      line-height: 2rem;
    }
  }
}

.itemTitle {
  margin-right: 4px;
}

.vertical-mode {
  flex-direction: column;

  .itemTitle {
    margin-left: 1px !important;
    margin-top: 4px !important;
    margin-bottom: 4px !important;
    margin-right: 4px !important;
    padding-top: 4px !important;
  }

  :deep(.itemTitle label) {
    line-height: 1rem;
  }

  .v-col {
    align-self: flex-start;
  }
}

.iconBtn {
  margin: 0 !important;
}

.item-label {
  white-space: pre-line;
  font-weight: normal;
  background: rgb(var(--v-theme-background));
}

.item-label :deep(.v-col label) {
  line-height: 2rem;
}

.planningPeriodRegister {
  color: rgb(var(--v-theme-red-800));
}
:deep(.v-sheet) {
  background: rgb(var(--v-theme-background));
}
</style>
