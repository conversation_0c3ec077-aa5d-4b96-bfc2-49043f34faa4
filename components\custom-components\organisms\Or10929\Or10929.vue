<script setup lang="ts">
/**
 * Or10929:有機体:［履歴選択］画面 アセスメント(インターライ)
 * GUI00792_［履歴選択］画面 アセスメント(インターライ)
 *
 * @description
 * ［履歴選択］画面 アセスメント(インターライ)
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { reactive, ref, onMounted, watch } from 'vue'
import type { HistorySelectTableDataItem, Or10929StateType } from './Or10929.type'
import { Or10929Const } from './Or10929.constants'
import { useScreenOneWayBind } from '#imports'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type {
  HistorySelectInfoType,
  Or10929OnewayType,
  Or10929Type,
} from '~/types/cmn/business/components/Or10929Type'
import type {
  IHistorySelectScreenAssessmentInterraiInEntity,
  IHistorySelectScreenAssessmentInterraiOutEntity,
} from '~/repositories/cmn/entities/HistorySelectScreenAssessmentInterraiEntity'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  Mo01334Headers,
  Mo01334OnewayType,
  Mo01334Type,
} from '~/types/business/components/Mo01334Type'
import { ResBodyStatusCode } from '~/constants/api-constants'

const { t } = useI18n()
/************************************************
 * Props
 ************************************************/
interface Props {
  modelValue: Or10929Type
  onewayModelValue: HistorySelectInfoType
  uniqueCpId: string
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/

const defaultOnewayModelValue: Or10929OnewayType = {
  historySelectInfo: {
    // 事業者ID
    svJigyoId: '0',
    // 利用者ID
    userId: '0',
    // 計画期間ID
    sc1Id: '1',
    // 履歴ID
    raiId: '0',
  },
}

const localOneway = reactive({
  or10929: {
    ...defaultOnewayModelValue.historySelectInfo,
    ...props.onewayModelValue,
  },
  // 「履歴選択」ダイアログ
  mo00024Oneway: {
    width: '450px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      toolbarTitle: t('label.history-select'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
    },
  },
  // 履歴選択情報一覧
  historySelectInfoMo01334: {
    // 履歴選択情報データテーブルのヘッダー
    headers: [
      // 基準日
      {
        title: t('label.base-date'),
        key: 'assDateYmd',
        sortable: false,
        width: '120px',
      } as unknown as Mo01334Headers,
      // 作成者
      {
        title: t('label.author'),
        key: 'shokuKnj',
        sortable: false,
        width: '150px',
      },
      // アセスメント種別
      {
        title: t('label.assessment-kind'),
        key: 'assType',
        sortable: false,
      },
    ],
    height: 327,
    items: [
      {
        id: '',
        raiId: '0',
        sc1Id: '',
        shokuKnj: '',
        assDateYmd: '',
        assShokuId: '',
        assType: '0',
        assTypeName: '',
      },
    ],
  } as Mo01334OnewayType,
})

// 閉じるボタン設置
const mo00611Oneway: Mo00611OnewayType = {
  btnLabel: t('btn.close'),
  width: '90px',
  tooltipText: t('tooltip.screen-close'),
}

// 確定ボタン設置
const mo00609Oneway: Mo00609OnewayType = {
  btnLabel: t('btn.confirm'),
  width: '90px',
  tooltipText: t('tooltip.confirm-btn'),
}

// ダイアログ設置
const mo00024 = ref<Mo00024Type>({
  isOpen: Or10929Const.DEFAULT.IS_OPEN,
})

// 履歴選択情報選択行データ設定
const historySelectedItem = ref<Mo01334Type>({
  value: '',
  values: [],
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or10929StateType>({
  cpId: Or10929Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or10929Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  // 初期情報取得
  await getInitDataInfo()
})

/** 初期情報取得 */
const getInitDataInfo = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: IHistorySelectScreenAssessmentInterraiInEntity = {
    /**
     * 事業者ID
     */
    svJigyoId: localOneway.or10929.svJigyoId,
    /**
     * 利用者ID
     */
    userId: localOneway.or10929.userId,
    /**
     *計画期間ID
     */
    sc1Id: localOneway.or10929.sc1Id,
  }
  const resData: IHistorySelectScreenAssessmentInterraiOutEntity = await ScreenRepository.select(
    'historySelectScreenAssessmentInterRAISelect',
    inputData
  )
  if (resData.statusCode === ResBodyStatusCode.SUCCESS) {
    // データ情報設定
    localOneway.historySelectInfoMo01334.items = resData.data.historyList.map((item) => {
      return {
        ...item,
        id: item.raiId,
      }
    })
    // 親画面.履歴IDが存在する場合
    // デフォルトでは第一条を選択します
    historySelectedItem.value.value =
      localOneway.or10929.raiId ?? localOneway.historySelectInfoMo01334.items?.[0]?.id
  }
}

/**
 * 履歴情報選択行
 *
 * @param item - 履歴情報選択行
 */
const clickHistorySelectRow = (item: HistorySelectTableDataItem) => {
  historySelectedItem.value.value = item.id
}

/**
 * 履歴情報の行をダブルクリックで選択
 *
 * @param item - 履歴情報選択行
 */
const doubleClickHistorySelectRow = (item: HistorySelectTableDataItem) => {
  historySelectedItem.value.value = item.id
  onConfirmBtn()
}

/**
 * 履歴情報選択行設定様式
 *
 * @param item - 履歴情報選択行
 */
const historyIsSelected = (item: { id: string }) => historySelectedItem.value.value === item.id

/**
 * 「確定」ボタン押下
 */
const onConfirmBtn = () => {
  // 履歴データがない場合、操作なし
  if (!historySelectedItem.value.value) return
  // 選択情報値戻り
  const findItem = localOneway.historySelectInfoMo01334.items.find(
    (item) => item.id === historySelectedItem.value.value
  )
  emit('update:modelValue', findItem)
  onClickCloseBtn()
}

/**
 * 閉じるボタン押下時
 */
const onClickCloseBtn = (): void => {
  setState({ isOpen: false })
}

watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      // 本画面を閉じる。（AC022と同じ）
      onClickCloseBtn()
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet>
        <c-v-row no-gutters>
          <c-v-col
            cols="12"
            class="table-header"
          >
            <!-- 履歴選択一覧 -->
            <base-mo01334
              v-model="historySelectedItem"
              class="list-wrapper"
              hide-default-footer
              :oneway-model-value="localOneway.historySelectInfoMo01334"
            >
              <template #item="{ item }">
                <tr
                  :class="['cursor-pointer', historyIsSelected(item) ? 'selected-row' : '']"
                  @click="clickHistorySelectRow(item)"
                  @dblclick="doubleClickHistorySelectRow(item)"
                >
                  <td>
                    <span>{{ item.assDateYmd }}</span>
                  </td>
                  <td>
                    <span>{{ item.shokuKnj }}</span>
                  </td>
                  <td>
                    <span>{{ item.assTypeName }}</span>
                  </td>
                </tr>
              </template>
            </base-mo01334>
          </c-v-col>
        </c-v-row>
      </c-v-sheet>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="mo00611Oneway"
          class="mr-2"
          @click="onClickCloseBtn"
        >
        </base-mo00611>
        <!-- 確定ボタン-->
        <base-mo00609
          :oneway-model-value="mo00609Oneway"
          @click="onConfirmBtn"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';
</style>
