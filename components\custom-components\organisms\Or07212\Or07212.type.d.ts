/**
 *Or07212:有機体:モーダル（画面/特殊コンポーネント）
 *GUI04473_日割利用期間
 *
 * <AUTHOR> 董永強
 */
export interface Or07212StateType {
  /**
   * 開閉フラグ
   */
  isOpen?: boolean
}

/**
 * 日割利用期間
 */
export interface DailyRateUsingPeriod {
  /** 利用者ID */
  userid: string
  /** 事業所ID */
  jigyoId: string
  /** 事業所名 */
  jigyoNm: string
  /** 利用開始日 */
  startYmd: DateModelValue
  /** 利用終了日 */
  endYmd: DateModelValue
  /** 更新区分 */
  updKbn: string
  /** 連番 */
  seqNo: string
  /** 更新回数 */
  modifiedCnt: string
}

/**
 * 選択value
 */
export interface DateModelValue {
  /** value */
  value: string
}
