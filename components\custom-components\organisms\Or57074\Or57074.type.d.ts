/**
 * Or57074:有機体:印刷設定モーダル（画面/特殊コンポーネント）
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR>
 */
export interface Or57074StateType {
  /**
   * 開閉フラグ
   */
  isOpen?: boolean
  /**
   * パラメータ
   */
  param: Or57074Param
}

/**
 * パラメータ構造
 */
export interface Or57074Param {
  /**
   * 帳票番号
   */
  prtNo: string
  /**
   * 親画面.法人ID
   */
  houjinId: string
  /**
   * 事業者ID
   */
  svJigyoId: string
  /**
   * 施設ID
   */
  shisetuId: string
  /**
   * 担当者ID
   */
  tantoId: string
  /**
   * 種別ID
   */
  syubetsuId: string
  /**
   * セクション名
   */
  sectionName: string
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 事業所名
   */
  svJigyoKnj: string
  /**
   * 処理年月日
   */
  processYmd: string
  /**
   * ヘッダID
   */
  cmoni1Id: string
  /**
   * 利用者情報リストにデータを選択フラゲ
   */
  parentUserIdSelectDataFlag: boolean
}

/**
 * メッセージのボタンの値の型
 */
export type Or57074MsgBtnType = 'yes' | 'no' | 'cancel'
