<script setup lang="ts">
import {
  computed,
  definePageMeta,
  reactive,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import type {
  Mo01354Items,
  Mo01354Type,
} from '~/components/base-components/molecules/Mo01354/Mo01354Type'
import { OrX0149Const } from '~/components/custom-components/organisms/OrX0149/OrX0149.constants'
import { OrX0149Logic } from '~/components/custom-components/organisms/OrX0149/OrX0149.logic'
import type { OrX0149OnewayType, OrX0149Type } from '~/types/cmn/business/components/OrX0149Type'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 * GUI04469_印刷設定画面
 * KMD 靳先念 2025/06/25 ADD START
 **************************************************/
// 画面ID
const screenId = 'GUI04469'
// ルーティング
const routing = 'GUI04469/pinia'
// 画面物理名
const screenName = 'GUI04469'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const local = reactive({
  OrX0149: {
    listValue: {
      values: {
        selectedRowId: '-1',
        selectedRowIds: [],
        items: [] as Mo01354Items[],
      },
    } as Mo01354Type,
  } as OrX0149Type,
})

const localOneway = reactive({
  OrX0149Oneway: {
    sysCd: '1',
    sysRyaku: '1',
    houjinId: '1',
    shisetuId: '1',
    svJigyoId: '1',
    shokuId: '1',
    sectionName: '1',
    choIndex: '1',
  } as OrX0149OnewayType,
})
/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01178' },
})

/**************************************************
 * Props
 **************************************************/
const OrX0149 = ref({ uniqueCpId: '' })
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 自身のPinia領域をセットアップ
const { childCpIds } = useInitialize({
  cpId: 'GUI04470',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: OrX0149Const.CP_ID(0) }],
})
OrX0149Logic.initialize(childCpIds.OrX0149.uniqueCpId)
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [OrX0149Const.CP_ID(0)]: OrX0149.value,
})

/**************************************************
 * Props
 **************************************************/

// ダイアログ表示フラグ
const showDialogOrX0149 = computed(() => {
  // Or00100のダイアログ開閉状態
  return OrX0149Logic.state.get(OrX0149.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * 初期データ取得
 */
async function initData() {}

/***
 * ボタン押下時の処理
 */
function OrX0149OnClick_0() {
  localOneway.OrX0149Oneway.kanrenSetKbn = '0'
  OrX0149Logic.state.set({
    uniqueCpId: OrX0149.value.uniqueCpId,
    state: { isOpen: true },
  })
}

await initData()
/**************************************************
 * 印刷設定画面
 * KMD 靳先念 2025/06/04 ADD START
 **************************************************/
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="OrX0149OnClick_0()"
        >GUI04470_印刷設定
      </v-btn>
    </c-v-col>
  </c-v-row>
  <g-custom-or-x-0149
    v-if="showDialogOrX0149"
    v-bind="OrX0149"
    v-model="local.OrX0149"
    :oneway-model-value="localOneway.OrX0149Oneway"
    :unique-cp-id="OrX0149.uniqueCpId"
    :parent-cp-id="pageComponent.uniqueCpId"
  />
</template>
