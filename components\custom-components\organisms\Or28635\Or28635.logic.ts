import { Gui00018Const } from '../Gui00018/Gui00018.constants'
import { Gui00018Logic } from '../Gui00018/Gui00018.logic'
import { Or28635Const } from './Or28635.constants'
import type { Or28635StateType } from './Or28635.type'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'

/**
 * Or28635:［経過管理］画面ダイアログ
 * GUI00622_［経過管理］画面
 *
 * @description
 * 処理ロジック initialize処理とpinia領域操作用の処理を提供する
 *
 * <AUTHOR>
 */
export namespace Or28635Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or28635Const.CP_ID(1),
      uniqueCpId,
      initOneWayState: {
        isOpen: Or28635Const.DEFAULT.IS_OPEN,
      },
      childCps: [{ cpId: Gui00018Const.CP_ID(0) }],
    })

    // 子コンポーネントのセットアップ
    Gui00018Logic.initialize(childCpIds.Gui00018.uniqueCpId)
    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }
  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or28635StateType>(Or28635Const.CP_ID(0))
}
