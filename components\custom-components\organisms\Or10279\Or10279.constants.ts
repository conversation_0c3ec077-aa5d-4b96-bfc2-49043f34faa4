import type { Mo01354Headers } from '~/components/base-components/molecules/Mo01354/Mo01354Type'
import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * Or10279:有機体:アセスメント（インターライ）CSV出力
 * 静的データ
 */
export namespace Or10279Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or10279', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false
    /**
     * 初期値：ヘッダー情報
     */
    export const INIT_HEADERS = [
      { title: 'A2', minWidth: '100px', sortable: false, key: 'A2' },
      { title: 'A3', minWidth: '100px', sortable: false, key: 'A3' },
      { title: 'A4', minWidth: '100px', sortable: false, key: 'A4' },
      { title: 'A5', minWidth: '100px', sortable: false, key: 'A5' },
      { title: 'A6', minWidth: '100px', sortable: false, key: 'A6' },
      { title: 'A7', minWidth: '100px', sortable: false, key: 'A7' },
      { title: 'A8', minWidth: '100px', sortable: false, key: 'A8' },
      { title: 'A9', minWidth: '100px', sortable: false, key: 'A9' },
      { title: 'A11', minWidth: '100px', sortable: false, key: 'A11' },
      { title: 'A12a', minWidth: '100px', sortable: false, key: 'A12a' },
      { title: 'A12b', minWidth: '100px', sortable: false, key: 'A12b' },
      { title: 'A12c', minWidth: '100px', sortable: false, key: 'A12c' },
      { title: 'A13', minWidth: '100px', sortable: false, key: 'A13' },
      { title: 'B1', minWidth: '100px', sortable: false, key: 'B1' },
      { title: 'B2', minWidth: '100px', sortable: false, key: 'B2' },
      { title: 'B5a', minWidth: '100px', sortable: false, key: 'B5a' },
      { title: 'B5b', minWidth: '100px', sortable: false, key: 'B5b' },
      { title: 'B5c', minWidth: '100px', sortable: false, key: 'B5c' },
      { title: 'B5d', minWidth: '100px', sortable: false, key: 'B5d' },
      { title: 'B5e', minWidth: '100px', sortable: false, key: 'B5e' },
      { title: 'B5f', minWidth: '100px', sortable: false, key: 'B5f' },
      { title: 'B6a', minWidth: '100px', sortable: false, key: 'B6a' },
      { title: 'B6b', minWidth: '100px', sortable: false, key: 'B6b' },
      { title: 'B7', minWidth: '100px', sortable: false, key: 'B7' },
      { title: 'B8', minWidth: '100px', sortable: false, key: 'B8' },
      { title: 'B9', minWidth: '100px', sortable: false, key: 'B9' },
      { title: 'B10', minWidth: '100px', sortable: false, key: 'B10' },
      { title: 'B11', minWidth: '100px', sortable: false, key: 'B11' },
      { title: 'C1', minWidth: '100px', sortable: false, key: 'C1' },
      { title: 'C2a', minWidth: '100px', sortable: false, key: 'C2a' },
      { title: 'C2b', minWidth: '100px', sortable: false, key: 'C2b' },
      { title: 'C2c', minWidth: '100px', sortable: false, key: 'C2c' },
      { title: 'C2d', minWidth: '100px', sortable: false, key: 'C2d' },
      { title: 'C3a', minWidth: '100px', sortable: false, key: 'C3a' },
      { title: 'C3b', minWidth: '100px', sortable: false, key: 'C3b' },
      { title: 'C3c', minWidth: '100px', sortable: false, key: 'C3c' },
      { title: 'C4', minWidth: '100px', sortable: false, key: 'C4' },
      { title: 'C5', minWidth: '100px', sortable: false, key: 'C5' },
      { title: 'D1', minWidth: '100px', sortable: false, key: 'D1' },
      { title: 'D2', minWidth: '100px', sortable: false, key: 'D2' },
      { title: 'D3a', minWidth: '100px', sortable: false, key: 'D3a' },
      { title: 'D3b', minWidth: '100px', sortable: false, key: 'D3b' },
      { title: 'D4a', minWidth: '100px', sortable: false, key: 'D4a' },
      { title: 'D4b', minWidth: '100px', sortable: false, key: 'D4b' },
      { title: 'E1a', minWidth: '100px', sortable: false, key: 'E1a' },
      { title: 'E1b', minWidth: '100px', sortable: false, key: 'E1b' },
      { title: 'E1c', minWidth: '100px', sortable: false, key: 'E1c' },
      { title: 'E1d', minWidth: '100px', sortable: false, key: 'E1d' },
      { title: 'E1e', minWidth: '100px', sortable: false, key: 'E1e' },
      { title: 'E1f', minWidth: '100px', sortable: false, key: 'E1f' },
      { title: 'E1g', minWidth: '100px', sortable: false, key: 'E1g' },
      { title: 'E1h', minWidth: '100px', sortable: false, key: 'E1h' },
      { title: 'E1i', minWidth: '100px', sortable: false, key: 'E1i' },
      { title: 'E1j', minWidth: '100px', sortable: false, key: 'E1j' },
      { title: 'E1k', minWidth: '100px', sortable: false, key: 'E1k' },
      { title: 'E2a', minWidth: '100px', sortable: false, key: 'E2a' },
      { title: 'E2b', minWidth: '100px', sortable: false, key: 'E2b' },
      { title: 'E2c', minWidth: '100px', sortable: false, key: 'E2c' },
      { title: 'E3a', minWidth: '100px', sortable: false, key: 'E3a' },
      { title: 'E3b', minWidth: '100px', sortable: false, key: 'E3b' },
      { title: 'E3c', minWidth: '100px', sortable: false, key: 'E3c' },
      { title: 'E3d', minWidth: '100px', sortable: false, key: 'E3d' },
      { title: 'E3e', minWidth: '100px', sortable: false, key: 'E3e' },
      { title: 'E3f', minWidth: '100px', sortable: false, key: 'E3f' },
      { title: 'E3g', minWidth: '100px', sortable: false, key: 'E3g' },
      { title: 'E4', minWidth: '100px', sortable: false, key: 'E4' },
      { title: 'F1a', minWidth: '100px', sortable: false, key: 'F1a' },
      { title: 'F1b', minWidth: '100px', sortable: false, key: 'F1b' },
      { title: 'F1c', minWidth: '100px', sortable: false, key: 'F1c' },
      { title: 'F1d', minWidth: '100px', sortable: false, key: 'F1d' },
      { title: 'F1e', minWidth: '100px', sortable: false, key: 'F1e' },
      { title: 'F1f', minWidth: '100px', sortable: false, key: 'F1f' },
      { title: 'F2', minWidth: '100px', sortable: false, key: 'F2' },
      { title: 'F3', minWidth: '100px', sortable: false, key: 'F3' },
      { title: 'F4', minWidth: '100px', sortable: false, key: 'F4' },
      { title: 'F5a', minWidth: '100px', sortable: false, key: 'F5a' },
      { title: 'F5b', minWidth: '100px', sortable: false, key: 'F5b' },
      { title: 'F5c', minWidth: '100px', sortable: false, key: 'F5c' },
      { title: 'F5d', minWidth: '100px', sortable: false, key: 'F5d' },
      { title: 'F5e', minWidth: '100px', sortable: false, key: 'F5e' },
      { title: 'F5f', minWidth: '100px', sortable: false, key: 'F5f' },
      { title: 'F5g', minWidth: '100px', sortable: false, key: 'F5g' },
      { title: 'F6a', minWidth: '100px', sortable: false, key: 'F6a' },
      { title: 'F6b', minWidth: '100px', sortable: false, key: 'F6b' },
      { title: 'F6c', minWidth: '100px', sortable: false, key: 'F6c' },
      { title: 'F6d', minWidth: '100px', sortable: false, key: 'F6d' },
      { title: 'F7', minWidth: '100px', sortable: false, key: 'F7' },
      { title: 'F8a', minWidth: '100px', sortable: false, key: 'F8a' },
      { title: 'F8b', minWidth: '100px', sortable: false, key: 'F8b' },
      { title: 'F8c', minWidth: '100px', sortable: false, key: 'F8c' },
      { title: 'G1aa', minWidth: '100px', sortable: false, key: 'G1aa' },
      { title: 'G1ab', minWidth: '100px', sortable: false, key: 'G1ab' },
      { title: 'G1ba', minWidth: '100px', sortable: false, key: 'G1ba' },
      { title: 'G1bb', minWidth: '100px', sortable: false, key: 'G1bb' },
      { title: 'G1ca', minWidth: '100px', sortable: false, key: 'G1ca' },
      { title: 'G1cb', minWidth: '100px', sortable: false, key: 'G1cb' },
      { title: 'G1da', minWidth: '100px', sortable: false, key: 'G1da' },
      { title: 'G1db', minWidth: '100px', sortable: false, key: 'G1db' },
      { title: 'G1ea', minWidth: '100px', sortable: false, key: 'G1ea' },
      { title: 'G1eb', minWidth: '100px', sortable: false, key: 'G1eb' },
      { title: 'G1fa', minWidth: '100px', sortable: false, key: 'G1fa' },
      { title: 'G1fb', minWidth: '100px', sortable: false, key: 'G1fb' },
      { title: 'G1ga', minWidth: '100px', sortable: false, key: 'G1ga' },
      { title: 'G1gb', minWidth: '100px', sortable: false, key: 'G1gb' },
      { title: 'G1ha', minWidth: '100px', sortable: false, key: 'G1ha' },
      { title: 'G1hb', minWidth: '100px', sortable: false, key: 'G1hb' },
      { title: 'G2a', minWidth: '100px', sortable: false, key: 'G2a' },
      { title: 'G2b', minWidth: '100px', sortable: false, key: 'G2b' },
      { title: 'G2c', minWidth: '100px', sortable: false, key: 'G2c' },
      { title: 'G2d', minWidth: '100px', sortable: false, key: 'G2d' },
      { title: 'G2e', minWidth: '100px', sortable: false, key: 'G2e' },
      { title: 'G2f', minWidth: '100px', sortable: false, key: 'G2f' },
      { title: 'G2g', minWidth: '100px', sortable: false, key: 'G2g' },
      { title: 'G2h', minWidth: '100px', sortable: false, key: 'G2h' },
      { title: 'G2i', minWidth: '100px', sortable: false, key: 'G2i' },
      { title: 'G2j', minWidth: '100px', sortable: false, key: 'G2j' },
      { title: 'G3a', minWidth: '100px', sortable: false, key: 'G3a' },
      { title: 'G3b', minWidth: '100px', sortable: false, key: 'G3b' },
      { title: 'G3c', minWidth: '100px', sortable: false, key: 'G3c' },
      { title: 'G3d', minWidth: '100px', sortable: false, key: 'G3d' },
      { title: 'G4a', minWidth: '100px', sortable: false, key: 'G4a' },
      { title: 'G4b', minWidth: '100px', sortable: false, key: 'G4b' },
      { title: 'G5a', minWidth: '100px', sortable: false, key: 'G5a' },
      { title: 'G5b', minWidth: '100px', sortable: false, key: 'G5b' },
      { title: 'G6', minWidth: '100px', sortable: false, key: 'G6' },
      { title: 'G7a', minWidth: '100px', sortable: false, key: 'G7a' },
      { title: 'G7b', minWidth: '100px', sortable: false, key: 'G7b' },
      { title: 'H1', minWidth: '100px', sortable: false, key: 'H1' },
      { title: 'H2', minWidth: '100px', sortable: false, key: 'H2' },
      { title: 'H3', minWidth: '100px', sortable: false, key: 'H3' },
      { title: 'H4', minWidth: '100px', sortable: false, key: 'H4' },
      { title: 'H5', minWidth: '100px', sortable: false, key: 'H5' },
      { title: 'I1a', minWidth: '100px', sortable: false, key: 'I1a' },
      { title: 'I1b', minWidth: '100px', sortable: false, key: 'I1b' },
      { title: 'I1c', minWidth: '100px', sortable: false, key: 'I1c' },
      { title: 'I1d', minWidth: '100px', sortable: false, key: 'I1d' },
      { title: 'I1e', minWidth: '100px', sortable: false, key: 'I1e' },
      { title: 'I1f', minWidth: '100px', sortable: false, key: 'I1f' },
      { title: 'I1g', minWidth: '100px', sortable: false, key: 'I1g' },
      { title: 'I1h', minWidth: '100px', sortable: false, key: 'I1h' },
      { title: 'I1i', minWidth: '100px', sortable: false, key: 'I1i' },
      { title: 'I1j', minWidth: '100px', sortable: false, key: 'I1j' },
      { title: 'I1k', minWidth: '100px', sortable: false, key: 'I1k' },
      { title: 'I1l', minWidth: '100px', sortable: false, key: 'I1l' },
      { title: 'I1m', minWidth: '100px', sortable: false, key: 'I1m' },
      { title: 'I1n', minWidth: '100px', sortable: false, key: 'I1n' },
      { title: 'I1o', minWidth: '100px', sortable: false, key: 'I1o' },
      { title: 'I1p', minWidth: '100px', sortable: false, key: 'I1p' },
      { title: 'I1q', minWidth: '100px', sortable: false, key: 'I1q' },
      { title: 'I1r', minWidth: '100px', sortable: false, key: 'I1r' },
      { title: 'I1s', minWidth: '100px', sortable: false, key: 'I1s' },
      { title: 'I1t', minWidth: '100px', sortable: false, key: 'I1t' },
      { title: 'I1u', minWidth: '100px', sortable: false, key: 'I1u' },
      { title: 'I1v', minWidth: '100px', sortable: false, key: 'I1v' },
      { title: 'I2a', minWidth: '100px', sortable: false, key: 'I2a' },
      { title: 'I2b', minWidth: '100px', sortable: false, key: 'I2b' },
      { title: 'I2c', minWidth: '100px', sortable: false, key: 'I2c' },
      { title: 'I2d', minWidth: '100px', sortable: false, key: 'I2d' },
      { title: 'I2e', minWidth: '100px', sortable: false, key: 'I2e' },
      { title: 'I2f', minWidth: '100px', sortable: false, key: 'I2f' },
      { title: 'I2g', minWidth: '100px', sortable: false, key: 'I2g' },
      { title: 'I2h', minWidth: '100px', sortable: false, key: 'I2h' },
      { title: 'I2i', minWidth: '100px', sortable: false, key: 'I2i' },
      { title: 'I2j', minWidth: '100px', sortable: false, key: 'I2j' },
      { title: 'J1', minWidth: '100px', sortable: false, key: 'J1' },
      { title: 'J2', minWidth: '100px', sortable: false, key: 'J2' },
      { title: 'J3a', minWidth: '100px', sortable: false, key: 'J3a' },
      { title: 'J3b', minWidth: '100px', sortable: false, key: 'J3b' },
      { title: 'J3c', minWidth: '100px', sortable: false, key: 'J3c' },
      { title: 'J3d', minWidth: '100px', sortable: false, key: 'J3d' },
      { title: 'J3e', minWidth: '100px', sortable: false, key: 'J3e' },
      { title: 'J3f', minWidth: '100px', sortable: false, key: 'J3f' },
      { title: 'J3g', minWidth: '100px', sortable: false, key: 'J3g' },
      { title: 'J3h', minWidth: '100px', sortable: false, key: 'J3h' },
      { title: 'J3i', minWidth: '100px', sortable: false, key: 'J3i' },
      { title: 'J3j', minWidth: '100px', sortable: false, key: 'J3j' },
      { title: 'J3k', minWidth: '100px', sortable: false, key: 'J3k' },
      { title: 'J3l', minWidth: '100px', sortable: false, key: 'J3l' },
      { title: 'J3m', minWidth: '100px', sortable: false, key: 'J3m' },
      { title: 'J3n', minWidth: '100px', sortable: false, key: 'J3n' },
      { title: 'J3o', minWidth: '100px', sortable: false, key: 'J3o' },
      { title: 'J3p', minWidth: '100px', sortable: false, key: 'J3p' },
      { title: 'J3q', minWidth: '100px', sortable: false, key: 'J3q' },
      { title: 'J3r', minWidth: '100px', sortable: false, key: 'J3r' },
      { title: 'J3s', minWidth: '100px', sortable: false, key: 'J3s' },
      { title: 'J3t', minWidth: '100px', sortable: false, key: 'J3t' },
      { title: 'J3u', minWidth: '100px', sortable: false, key: 'J3u' },
      { title: 'J4', minWidth: '100px', sortable: false, key: 'J4' },
      { title: 'J5', minWidth: '100px', sortable: false, key: 'J5' },
      { title: 'J6a', minWidth: '100px', sortable: false, key: 'J6a' },
      { title: 'J6b', minWidth: '100px', sortable: false, key: 'J6b' },
      { title: 'J6c', minWidth: '100px', sortable: false, key: 'J6c' },
      { title: 'J6d', minWidth: '100px', sortable: false, key: 'J6d' },
      { title: 'J6e', minWidth: '100px', sortable: false, key: 'J6e' },
      { title: 'J7a', minWidth: '100px', sortable: false, key: 'J7a' },
      { title: 'J7b', minWidth: '100px', sortable: false, key: 'J7b' },
      { title: 'J7c', minWidth: '100px', sortable: false, key: 'J7c' },
      { title: 'J8', minWidth: '100px', sortable: false, key: 'J8' },
      { title: 'J9a', minWidth: '100px', sortable: false, key: 'J9a' },
      { title: 'J9b', minWidth: '100px', sortable: false, key: 'J9b' },
      { title: 'K1a', minWidth: '100px', sortable: false, key: 'K1a' },
      { title: 'K1b', minWidth: '100px', sortable: false, key: 'K1b' },
      { title: 'K2a', minWidth: '100px', sortable: false, key: 'K2a' },
      { title: 'K2b', minWidth: '100px', sortable: false, key: 'K2b' },
      { title: 'K2c', minWidth: '100px', sortable: false, key: 'K2c' },
      { title: 'K2d', minWidth: '100px', sortable: false, key: 'K2d' },
      { title: 'K3', minWidth: '100px', sortable: false, key: 'K3' },
      { title: 'K4', minWidth: '100px', sortable: false, key: 'K4' },
      { title: 'K5a', minWidth: '100px', sortable: false, key: 'K5a' },
      { title: 'K5b', minWidth: '100px', sortable: false, key: 'K5b' },
      { title: 'K5c', minWidth: '100px', sortable: false, key: 'K5c' },
      { title: 'K5d', minWidth: '100px', sortable: false, key: 'K5d' },
      { title: 'K5e', minWidth: '100px', sortable: false, key: 'K5e' },
      { title: 'K5f', minWidth: '100px', sortable: false, key: 'K5f' },
      { title: 'K6a', minWidth: '100px', sortable: false, key: 'K6a' },
      { title: 'K6b', minWidth: '100px', sortable: false, key: 'K6b' },
      { title: 'K6c', minWidth: '100px', sortable: false, key: 'K6c' },
      { title: 'K6d', minWidth: '100px', sortable: false, key: 'K6d' },
      { title: 'K6e', minWidth: '100px', sortable: false, key: 'K6e' },
      { title: 'L1', minWidth: '100px', sortable: false, key: 'L1' },
      { title: 'L2', minWidth: '100px', sortable: false, key: 'L2' },
      { title: 'L3', minWidth: '100px', sortable: false, key: 'L3' },
      { title: 'L4', minWidth: '100px', sortable: false, key: 'L4' },
      { title: 'L5', minWidth: '100px', sortable: false, key: 'L5' },
      { title: 'L6', minWidth: '100px', sortable: false, key: 'L6' },
      { title: 'L7', minWidth: '100px', sortable: false, key: 'L7' },
      { title: 'M1', minWidth: '100px', sortable: false, key: 'M1' },
      { title: 'M2a', minWidth: '100px', sortable: false, key: 'M2a' },
      { title: 'M2b', minWidth: '100px', sortable: false, key: 'M2b' },
      { title: 'M2c', minWidth: '100px', sortable: false, key: 'M2c' },
      { title: 'M2d', minWidth: '100px', sortable: false, key: 'M2d' },
      { title: 'M2e', minWidth: '100px', sortable: false, key: 'M2e' },
      { title: 'M2f', minWidth: '100px', sortable: false, key: 'M2f' },
      { title: 'M2g', minWidth: '100px', sortable: false, key: 'M2g' },
      { title: 'M2h', minWidth: '100px', sortable: false, key: 'M2h' },
      { title: 'M2i', minWidth: '100px', sortable: false, key: 'M2i' },
      { title: 'M2j', minWidth: '100px', sortable: false, key: 'M2j' },
      { title: 'M2k', minWidth: '100px', sortable: false, key: 'M2k' },
      { title: 'M2l', minWidth: '100px', sortable: false, key: 'M2l' },
      { title: 'M2m', minWidth: '100px', sortable: false, key: 'M2m' },
      { title: 'M2n', minWidth: '100px', sortable: false, key: 'M2n' },
      { title: 'M2o', minWidth: '100px', sortable: false, key: 'M2o' },
      { title: 'M2p', minWidth: '100px', sortable: false, key: 'M2p' },
      { title: 'M2q', minWidth: '100px', sortable: false, key: 'M2q' },
      { title: 'M2r', minWidth: '100px', sortable: false, key: 'M2r' },
      { title: 'M2s', minWidth: '100px', sortable: false, key: 'M2s' },
      { title: 'M2t', minWidth: '100px', sortable: false, key: 'M2t' },
      { title: 'M3', minWidth: '100px', sortable: false, key: 'M3' },
      { title: 'M4a', minWidth: '100px', sortable: false, key: 'M4a' },
      { title: 'M4b', minWidth: '100px', sortable: false, key: 'M4b' },
      { title: 'M4c', minWidth: '100px', sortable: false, key: 'M4c' },
      { title: 'M4d', minWidth: '100px', sortable: false, key: 'M4d' },
      { title: 'N1a1', minWidth: '100px', sortable: false, key: 'N1a1' },
      { title: 'N1a2', minWidth: '100px', sortable: false, key: 'N1a2' },
      { title: 'N1a3', minWidth: '100px', sortable: false, key: 'N1a3' },
      { title: 'N1a4', minWidth: '100px', sortable: false, key: 'N1a4' },
      { title: 'N1a5', minWidth: '100px', sortable: false, key: 'N1a5' },
      { title: 'N1a6', minWidth: '100px', sortable: false, key: 'N1a6' },
      { title: 'N1a7', minWidth: '100px', sortable: false, key: 'N1a7' },
      { title: 'N1a8', minWidth: '100px', sortable: false, key: 'N1a8' },
      { title: 'N1a9', minWidth: '100px', sortable: false, key: 'N1a9' },
      { title: 'N1a10', minWidth: '100px', sortable: false, key: 'N1a10' },
      { title: 'N1a11', minWidth: '100px', sortable: false, key: 'N1a11' },
      { title: 'N1a12', minWidth: '100px', sortable: false, key: 'N1a12' },
      { title: 'N1a13', minWidth: '100px', sortable: false, key: 'N1a13' },
      { title: 'N1a14', minWidth: '100px', sortable: false, key: 'N1a14' },
      { title: 'N1a15', minWidth: '100px', sortable: false, key: 'N1a15' },
      { title: 'N1b1', minWidth: '100px', sortable: false, key: 'N1b1' },
      { title: 'N1b2', minWidth: '100px', sortable: false, key: 'N1b2' },
      { title: 'N1b3', minWidth: '100px', sortable: false, key: 'N1b3' },
      { title: 'N1b4', minWidth: '100px', sortable: false, key: 'N1b4' },
      { title: 'N1b5', minWidth: '100px', sortable: false, key: 'N1b5' },
      { title: 'N1b6', minWidth: '100px', sortable: false, key: 'N1b6' },
      { title: 'N1b7', minWidth: '100px', sortable: false, key: 'N1b7' },
      { title: 'N1b8', minWidth: '100px', sortable: false, key: 'N1b8' },
      { title: 'N1b9', minWidth: '100px', sortable: false, key: 'N1b9' },
      { title: 'N1b10', minWidth: '100px', sortable: false, key: 'N1b10' },
      { title: 'N1b11', minWidth: '100px', sortable: false, key: 'N1b11' },
      { title: 'N1b12', minWidth: '100px', sortable: false, key: 'N1b12' },
      { title: 'N1b13', minWidth: '100px', sortable: false, key: 'N1b13' },
      { title: 'N1b14', minWidth: '100px', sortable: false, key: 'N1b14' },
      { title: 'N1b15', minWidth: '100px', sortable: false, key: 'N1b15' },
      { title: 'N1c1', minWidth: '100px', sortable: false, key: 'N1c1' },
      { title: 'N1c2', minWidth: '100px', sortable: false, key: 'N1c2' },
      { title: 'N1c3', minWidth: '100px', sortable: false, key: 'N1c3' },
      { title: 'N1c4', minWidth: '100px', sortable: false, key: 'N1c4' },
      { title: 'N1c5', minWidth: '100px', sortable: false, key: 'N1c5' },
      { title: 'N1c6', minWidth: '100px', sortable: false, key: 'N1c6' },
      { title: 'N1c7', minWidth: '100px', sortable: false, key: 'N1c7' },
      { title: 'N1c8', minWidth: '100px', sortable: false, key: 'N1c8' },
      { title: 'N1c9', minWidth: '100px', sortable: false, key: 'N1c9' },
      { title: 'N1c10', minWidth: '100px', sortable: false, key: 'N1c10' },
      { title: 'N1c11', minWidth: '100px', sortable: false, key: 'N1c11' },
      { title: 'N1c12', minWidth: '100px', sortable: false, key: 'N1c12' },
      { title: 'N1c13', minWidth: '100px', sortable: false, key: 'N1c13' },
      { title: 'N1c14', minWidth: '100px', sortable: false, key: 'N1c14' },
      { title: 'N1c15', minWidth: '100px', sortable: false, key: 'N1c15' },
      { title: 'N1d1', minWidth: '100px', sortable: false, key: 'N1d1' },
      { title: 'N1d2', minWidth: '100px', sortable: false, key: 'N1d2' },
      { title: 'N1d3', minWidth: '100px', sortable: false, key: 'N1d3' },
      { title: 'N1d4', minWidth: '100px', sortable: false, key: 'N1d4' },
      { title: 'N1d5', minWidth: '100px', sortable: false, key: 'N1d5' },
      { title: 'N1d6', minWidth: '100px', sortable: false, key: 'N1d6' },
      { title: 'N1d7', minWidth: '100px', sortable: false, key: 'N1d7' },
      { title: 'N1d8', minWidth: '100px', sortable: false, key: 'N1d8' },
      { title: 'N1d9', minWidth: '100px', sortable: false, key: 'N1d9' },
      { title: 'N1d10', minWidth: '100px', sortable: false, key: 'N1d10' },
      { title: 'N1d11', minWidth: '100px', sortable: false, key: 'N1d11' },
      { title: 'N1d12', minWidth: '100px', sortable: false, key: 'N1d12' },
      { title: 'N1d13', minWidth: '100px', sortable: false, key: 'N1d13' },
      { title: 'N1d14', minWidth: '100px', sortable: false, key: 'N1d14' },
      { title: 'N1d15', minWidth: '100px', sortable: false, key: 'N1d15' },
      { title: 'N1e1', minWidth: '100px', sortable: false, key: 'N1e1' },
      { title: 'N1e2', minWidth: '100px', sortable: false, key: 'N1e2' },
      { title: 'N1e3', minWidth: '100px', sortable: false, key: 'N1e3' },
      { title: 'N1e4', minWidth: '100px', sortable: false, key: 'N1e4' },
      { title: 'N1e5', minWidth: '100px', sortable: false, key: 'N1e5' },
      { title: 'N1e6', minWidth: '100px', sortable: false, key: 'N1e6' },
      { title: 'N1e7', minWidth: '100px', sortable: false, key: 'N1e7' },
      { title: 'N1e8', minWidth: '100px', sortable: false, key: 'N1e8' },
      { title: 'N1e9', minWidth: '100px', sortable: false, key: 'N1e9' },
      { title: 'N1e10', minWidth: '100px', sortable: false, key: 'N1e10' },
      { title: 'N1e11', minWidth: '100px', sortable: false, key: 'N1e11' },
      { title: 'N1e12', minWidth: '100px', sortable: false, key: 'N1e12' },
      { title: 'N1e13', minWidth: '100px', sortable: false, key: 'N1e13' },
      { title: 'N1e14', minWidth: '100px', sortable: false, key: 'N1e14' },
      { title: 'N1e15', minWidth: '100px', sortable: false, key: 'N1e15' },
      { title: 'N1f1', minWidth: '100px', sortable: false, key: 'N1f1' },
      { title: 'N1f2', minWidth: '100px', sortable: false, key: 'N1f2' },
      { title: 'N1f3', minWidth: '100px', sortable: false, key: 'N1f3' },
      { title: 'N1f4', minWidth: '100px', sortable: false, key: 'N1f4' },
      { title: 'N1f5', minWidth: '100px', sortable: false, key: 'N1f5' },
      { title: 'N1f6', minWidth: '100px', sortable: false, key: 'N1f6' },
      { title: 'N1f7', minWidth: '100px', sortable: false, key: 'N1f7' },
      { title: 'N1f8', minWidth: '100px', sortable: false, key: 'N1f8' },
      { title: 'N1f9', minWidth: '100px', sortable: false, key: 'N1f9' },
      { title: 'N1f10', minWidth: '100px', sortable: false, key: 'N1f10' },
      { title: 'N1f11', minWidth: '100px', sortable: false, key: 'N1f11' },
      { title: 'N1f12', minWidth: '100px', sortable: false, key: 'N1f12' },
      { title: 'N1f13', minWidth: '100px', sortable: false, key: 'N1f13' },
      { title: 'N1f14', minWidth: '100px', sortable: false, key: 'N1f14' },
      { title: 'N1f15', minWidth: '100px', sortable: false, key: 'N1f15' },
      { title: 'N2', minWidth: '100px', sortable: false, key: 'N2' },
      { title: 'N3', minWidth: '100px', sortable: false, key: 'N3' },
      { title: 'O1a', minWidth: '100px', sortable: false, key: 'O1a' },
      { title: 'O1b', minWidth: '100px', sortable: false, key: 'O1b' },
      { title: 'O1c', minWidth: '100px', sortable: false, key: 'O1c' },
      { title: 'O1d', minWidth: '100px', sortable: false, key: 'O1d' },
      { title: 'O1e', minWidth: '100px', sortable: false, key: 'O1e' },
      { title: 'O1f', minWidth: '100px', sortable: false, key: 'O1f' },
      { title: 'O1g', minWidth: '100px', sortable: false, key: 'O1g' },
      { title: 'O1h', minWidth: '100px', sortable: false, key: 'O1h' },
      { title: 'O2a', minWidth: '100px', sortable: false, key: 'O2a' },
      { title: 'O2b', minWidth: '100px', sortable: false, key: 'O2b' },
      { title: 'O2c', minWidth: '100px', sortable: false, key: 'O2c' },
      { title: 'O2d', minWidth: '100px', sortable: false, key: 'O2d' },
      { title: 'O2e', minWidth: '100px', sortable: false, key: 'O2e' },
      { title: 'O2f', minWidth: '100px', sortable: false, key: 'O2f' },
      { title: 'O2g', minWidth: '100px', sortable: false, key: 'O2g' },
      { title: 'O2h', minWidth: '100px', sortable: false, key: 'O2h' },
      { title: 'O2i', minWidth: '100px', sortable: false, key: 'O2i' },
      { title: 'O2j', minWidth: '100px', sortable: false, key: 'O2j' },
      { title: 'O2k', minWidth: '100px', sortable: false, key: 'O2k' },
      { title: 'O2l', minWidth: '100px', sortable: false, key: 'O2l' },
      { title: 'O2m', minWidth: '100px', sortable: false, key: 'O2m' },
      { title: 'O2n', minWidth: '100px', sortable: false, key: 'O2n' },
      { title: 'O3aa', minWidth: '100px', sortable: false, key: 'O3aa' },
      { title: 'O3ab', minWidth: '100px', sortable: false, key: 'O3ab' },
      { title: 'O3ba', minWidth: '100px', sortable: false, key: 'O3ba' },
      { title: 'O3bb', minWidth: '100px', sortable: false, key: 'O3bb' },
      { title: 'O3ca', minWidth: '100px', sortable: false, key: 'O3ca' },
      { title: 'O3da', minWidth: '100px', sortable: false, key: 'O3da' },
      { title: 'O4aa', minWidth: '100px', sortable: false, key: 'O4aa' },
      { title: 'O4ab', minWidth: '100px', sortable: false, key: 'O4ab' },
      { title: 'O4ac', minWidth: '100px', sortable: false, key: 'O4ac' },
      { title: 'O4ba', minWidth: '100px', sortable: false, key: 'O4ba' },
      { title: 'O4bb', minWidth: '100px', sortable: false, key: 'O4bb' },
      { title: 'O4bc', minWidth: '100px', sortable: false, key: 'O4bc' },
      { title: 'O4ca', minWidth: '100px', sortable: false, key: 'O4ca' },
      { title: 'O4cb', minWidth: '100px', sortable: false, key: 'O4cb' },
      { title: 'O4cc', minWidth: '100px', sortable: false, key: 'O4cc' },
      { title: 'O4da', minWidth: '100px', sortable: false, key: 'O4da' },
      { title: 'O4db', minWidth: '100px', sortable: false, key: 'O4db' },
      { title: 'O4dc', minWidth: '100px', sortable: false, key: 'O4dc' },
      { title: 'O4ea', minWidth: '100px', sortable: false, key: 'O4ea' },
      { title: 'O4eb', minWidth: '100px', sortable: false, key: 'O4eb' },
      { title: 'O4ec', minWidth: '100px', sortable: false, key: 'O4ec' },
      { title: 'O4fa', minWidth: '100px', sortable: false, key: 'O4fa' },
      { title: 'O4fb', minWidth: '100px', sortable: false, key: 'O4fb' },
      { title: 'O4fc', minWidth: '100px', sortable: false, key: 'O4fc' },
      { title: 'O5a', minWidth: '100px', sortable: false, key: 'O5a' },
      { title: 'O5b', minWidth: '100px', sortable: false, key: 'O5b' },
      { title: 'O5c', minWidth: '100px', sortable: false, key: 'O5c' },
      { title: 'O6', minWidth: '100px', sortable: false, key: 'O6' },
      { title: 'O7', minWidth: '100px', sortable: false, key: 'O7' },
      { title: 'O8a', minWidth: '100px', sortable: false, key: 'O8a' },
      { title: 'O8b', minWidth: '100px', sortable: false, key: 'O8b' },
      { title: 'O8c', minWidth: '100px', sortable: false, key: 'O8c' },
      { title: 'O8d', minWidth: '100px', sortable: false, key: 'O8d' },
      { title: 'P1a', minWidth: '100px', sortable: false, key: 'P1a' },
      { title: 'P1b', minWidth: '100px', sortable: false, key: 'P1b' },
      { title: 'P1c', minWidth: '100px', sortable: false, key: 'P1c' },
      { title: 'P2a', minWidth: '100px', sortable: false, key: 'P2a' },
      { title: 'P2b', minWidth: '100px', sortable: false, key: 'P2b' },
      { title: 'P2c', minWidth: '100px', sortable: false, key: 'P2c' },
      { title: 'P2d', minWidth: '100px', sortable: false, key: 'P2d' },
      { title: 'P2e', minWidth: '100px', sortable: false, key: 'P2e' },
      { title: 'Q1a1', minWidth: '100px', sortable: false, key: 'Q1a1' },
      { title: 'Q1a2', minWidth: '100px', sortable: false, key: 'Q1a2' },
      { title: 'Q1b1', minWidth: '100px', sortable: false, key: 'Q1b1' },
      { title: 'Q1b2', minWidth: '100px', sortable: false, key: 'Q1b2' },
      { title: 'Q1c1', minWidth: '100px', sortable: false, key: 'Q1c1' },
      { title: 'Q1c2', minWidth: '100px', sortable: false, key: 'Q1c2' },
      { title: 'Q1d1', minWidth: '100px', sortable: false, key: 'Q1d1' },
      { title: 'Q1d2', minWidth: '100px', sortable: false, key: 'Q1d2' },
      { title: 'Q2a', minWidth: '100px', sortable: false, key: 'Q2a' },
      { title: 'Q2b', minWidth: '100px', sortable: false, key: 'Q2b' },
      { title: 'Q2c', minWidth: '100px', sortable: false, key: 'Q2c' },
      { title: 'Q3', minWidth: '100px', sortable: false, key: 'Q3' },
      { title: 'R1a', minWidth: '100px', sortable: false, key: 'R1a' },
      { title: 'R1b', minWidth: '100px', sortable: false, key: 'R1b' },
      { title: 'R1c', minWidth: '100px', sortable: false, key: 'R1c' },
      { title: 'R2', minWidth: '100px', sortable: false, key: 'R2' },
      { title: 'S1a', minWidth: '100px', sortable: false, key: 'S1a' },
      { title: 'S1b', minWidth: '100px', sortable: false, key: 'S1b' },
      { title: 'S1c', minWidth: '100px', sortable: false, key: 'S1c' },
      { title: 'S1d', minWidth: '100px', sortable: false, key: 'S1d' },
      { title: 'S1e', minWidth: '100px', sortable: false, key: 'S1e' },
      { title: 'S2', minWidth: '100px', sortable: false, key: 'S2' },
      { title: 'S3a', minWidth: '100px', sortable: false, key: 'S3a' },
      { title: 'S3b', minWidth: '100px', sortable: false, key: 'S3b' },
      { title: 'S3c', minWidth: '100px', sortable: false, key: 'S3c' },
      { title: 'S4', minWidth: '100px', sortable: false, key: 'S4' },
      { title: 'T1', minWidth: '100px', sortable: false, key: 'T1' },
      { title: 'T2', minWidth: '100px', sortable: false, key: 'T2' },
      { title: 'T3', minWidth: '100px', sortable: false, key: 'T3' },
      { title: 'T4', minWidth: '100px', sortable: false, key: 'T4' },
      { title: 'T5', minWidth: '100px', sortable: false, key: 'T5' },
      { title: 'U1', minWidth: '100px', sortable: false, key: 'U1' },
      { title: 'U2', minWidth: '100px', sortable: false, key: 'U2' },
      { title: 'U3', minWidth: '100px', sortable: false, key: 'U3' },
      { title: 'V2', minWidth: '100px', sortable: false, key: 'V2' },
      { title: 'W1a', minWidth: '100px', sortable: false, key: 'W1a' },
      { title: 'W1b', minWidth: '100px', sortable: false, key: 'W1b' },
      { title: 'W1c', minWidth: '100px', sortable: false, key: 'W1c' },
      { title: 'W1d', minWidth: '100px', sortable: false, key: 'W1d' },
      { title: 'W1e', minWidth: '100px', sortable: false, key: 'W1e' },
      { title: 'W1f', minWidth: '100px', sortable: false, key: 'W1f' },
      { title: 'W1g', minWidth: '100px', sortable: false, key: 'W1g' },
      { title: 'W2a', minWidth: '100px', sortable: false, key: 'W2a' },
      { title: 'W2b', minWidth: '100px', sortable: false, key: 'W2b' },
      { title: 'W2c', minWidth: '100px', sortable: false, key: 'W2c' },
      { title: 'W2d', minWidth: '100px', sortable: false, key: 'W2d' },
      { title: 'W2e', minWidth: '100px', sortable: false, key: 'W2e' },
      { title: 'W2f', minWidth: '100px', sortable: false, key: 'W2f' },
      { title: 'W2g', minWidth: '100px', sortable: false, key: 'W2g' },
      { title: 'W2h', minWidth: '100px', sortable: false, key: 'W2h' },
      { title: 'W2i', minWidth: '100px', sortable: false, key: 'W2i' },
      { title: 'W2j', minWidth: '100px', sortable: false, key: 'W2j' },
      { title: 'W2k', minWidth: '100px', sortable: false, key: 'W2k' },
      { title: 'W2l', minWidth: '100px', sortable: false, key: 'W2l' },
      { title: 'W2m', minWidth: '100px', sortable: false, key: 'W2m' },
      { title: 'W2n', minWidth: '100px', sortable: false, key: 'W2n' },
      { title: 'W2o', minWidth: '100px', sortable: false, key: 'W2o' },
      { title: 'W2p', minWidth: '100px', sortable: false, key: 'W2p' },
      { title: 'W2q', minWidth: '100px', sortable: false, key: 'W2q' },
      { title: 'W2r', minWidth: '100px', sortable: false, key: 'W2r' },
      { title: 'W2s', minWidth: '100px', sortable: false, key: 'W2s' },
      { title: 'W2t', minWidth: '100px', sortable: false, key: 'W2t' },
      { title: 'W2u', minWidth: '100px', sortable: false, key: 'W2u' },
      { title: 'W2v', minWidth: '100px', sortable: false, key: 'W2v' },
      { title: 'W2w', minWidth: '100px', sortable: false, key: 'W2w' },
      { title: 'W2x', minWidth: '100px', sortable: false, key: 'W2x' },
      { title: 'W2y', minWidth: '100px', sortable: false, key: 'W2y' },
      { title: 'W2z', minWidth: '100px', sortable: false, key: 'W2z' },
      { title: 'W2za', minWidth: '100px', sortable: false, key: 'W2za' },
    ] as Mo01354Headers[]
  }
}
