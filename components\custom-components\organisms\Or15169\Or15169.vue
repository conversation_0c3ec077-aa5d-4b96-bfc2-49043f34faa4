<script setup lang="ts">
/**
 * Or15169：有機体：（確定版）入院基本情報
 *
 * <AUTHOR>
 */
import { computed, reactive, ref, watch, type Ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { Gui00059Logic } from '../Gui00059/Gui00059.logic'
import { Gui00059Const } from '../Gui00059/Gui00059.constants'
import { Or10648Logic } from '../Or10648/Or10648.logic'
import { Or10648Const } from '../Or10648/Or10648.constants'
import { Or26261Const } from '../Or26261/Or26261.constants'
import { Or26261Logic } from '../Or26261/Or26261.logic'
import { TeX0012Logic } from '../../template/TeX0012/TeX0012.logic'
import { Or15169Const } from './Or15169.constants'
import type { Or15169OneWayType, Or15169ValuesType } from './Or15169.Type'
import { useScreenOneWayBind, useScreenTwoWayBind, useSetupChildProps } from '#imports'
import type { Mo00020OnewayType } from '~/types/business/components/Mo00020Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Or10648SelectInfoType } from '~/types/cmn/business/components/Or10648Type'
import type { Or26261OnewayType, Or26261Type } from '~/types/cmn/business/components/Or26261Type'
import type {
  JigyoInfo,
  TantoInfo,
} from '~/repositories/cmn/entities/HospitalizationTimeInfoOfferPeriodSelectServiceEntity'
import type { TeX0012Type } from '~/types/cmn/business/components/TeX0012Type'
/**************************************************
 * Props
 **************************************************/
/**************************************************
 * Props
 **************************************************/
interface Props {
  /** uniqueCpId */
  uniqueCpId: string
  parentUniqueCpId: string
}

const props = defineProps<Props>()
/**************************************************
 * Pinia
 **************************************************/
const { t } = useI18n()

const { refValue } = useScreenTwoWayBind<Or15169ValuesType>({
  cpId: Or15169Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
}) as { refValue: Ref<Or15169ValuesType> }
useScreenOneWayBind<Or15169OneWayType>({
  cpId: Or15169Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    codeList: (value) => {
      localOneway.codeListOneway = value as unknown as {
        /** 担当情報リスト */
        tantoInfoList: TantoInfo[]
        /** 事業所情報リスト */
        jigyoInfoList: JigyoInfo[]
      }
      initSvJigyoData(refValue.value.or15169Values.shienJigyoId, 'init')
      initTantoShokuData(refValue.value.or15169Values.tantoShokuId, 'init')
      localOneway.or10648OnewayType.svJigyoId = refValue.value.or15169Values.shienJigyoId
    },
  },
})

const gui00059 = ref({ uniqueCpId: '' })
const or10648 = ref({ uniqueCpId: '' })
const or26261 = ref({ uniqueCpId: '' })
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Gui00059Const.CP_ID(1)]: gui00059.value,
  [Or10648Const.CP_ID(1)]: or10648.value,
  [Or26261Const.CP_ID(1)]: or26261.value,
})
/**************************************************
 * 変数定義
 **************************************************/

const local = reactive({
  commonInfo: {} as TeX0012Type,
  jigyoKnj: '',
  tantoShokuKnj: '',
  or26261: {
    shokuin: {
      shokuin1Knj: 'no',
      shokuin2Knj: 'selected',
    },
  } as Or26261Type,
})

const localOneway = reactive({
  codeListOneway: {} as {
    /** 担当情報リスト */
    tantoInfoList: TantoInfo[]
    /** 事業所情報リスト */
    jigyoInfoList: JigyoInfo[]
  },
  or26261Oneway: {
    /** 編集モード */
    editMode: Or15169Const.DEFAULT.VALUE_1,
    // 職員ID
    shokuinId: [],
    // モード
    selectMode: Or15169Const.DEFAULT.VALUE_12,
    // 初期選択事業所ID
    defSvJigyoId: '',
    // 適用事業所ＩＤリスト
    svJigyoId: [{ svJigyoId: Or15169Const.DEFAULT.VALUE_1 }],
    // 未設定フラグ
    misetteiFlg: Or15169Const.DEFAULT.VALUE_0,
    // 基準日
    kijunYmd: local.commonInfo.createYmd,
    // フィルターフラグ
    filterDwFlg: Or15169Const.DEFAULT.VALUE_1,
    // 表示するカラム
    hyoujiColumn: [],
    // 本日の勤務予定者のみを選択する
    isToday: false,
    // 勤務表パターン表示フラグ
    pattern: '',
    // 資格免許非活性フラグ
    qualification: '',
  } as Or26261OnewayType,
  or10648OnewayType: {
    svJigyoId: '',
  } as Or10648SelectInfoType,
  mo00045Oneway: {
    maxlength: '50',
    width: '705px',
    isVerticalLabel: false,
    showItemLabel: false,
  } as Mo00045OnewayType,
  mo00045Oneway1: {
    maxlength: '41',
    width: '585px',
    isVerticalLabel: false,
    showItemLabel: false,
  } as Mo00045OnewayType,
  mo00020Oneway: {
    isRequired: false,
    isVerticalLabel: false,
    showItemLabel: false,
    showSelectArrow: false,
  } as Mo00020OnewayType,
  mo00009Oneway: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  mo00030Oneway: {
    mo00045Oneway: {
      maxlength: '14',
      width: '180px',
      isVerticalLabel: false,
      showItemLabel: false,
    },
    mode: Or15169Const.DEFAULT.VALUE_1,
  },
})

// ダイアログ表示フラグ
const showDialog = computed(() => {
  // Gui00059のダイアログ開閉状態
  return Gui00059Logic.state.get(gui00059.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr10648 = computed(() => {
  // Or28824のダイアログ開閉状態
  return Or10648Logic.state.get(or10648.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr26261 = computed(() => {
  // Or26261のダイアログ開閉状態
  return Or26261Logic.state.get(or26261.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理（検索機能からでない画面呼び出し）
 */
function handleHospKnjHead() {
  // Gui00059のダイアログ開閉状態を更新する
  Gui00059Logic.state.set({
    uniqueCpId: gui00059.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  ボタン押下時の処理
 */
function handleShienJigyoId() {
  // Or10648のダイアログ開閉状態を更新する
  Or10648Logic.state.set({
    uniqueCpId: or10648.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 *  ボタン押下時の処理
 */
function handleTantoShokuId() {
  // Or26261のダイアログ開閉状態を更新する
  Or26261Logic.state.set({
    uniqueCpId: or26261.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const svJigyoIdChange = (val: { svJigyoId: number }) => {
  initSvJigyoData(val.svJigyoId.toString(), 'set')
}

const tantoShokuIdChange = (val: Or26261Type) => {
  initTantoShokuData(val.shokuin.chkShokuId.toString(), 'set')
}

const initTantoShokuData = (tantoShokuId: string, key: string) => {
  const data: TantoInfo = localOneway.codeListOneway.tantoInfoList.find(
    (item) => item.tantoShokuId === tantoShokuId
  ) as TantoInfo
  if (key === 'set') {
    refValue.value.or15169Values.tantoShokuId = data ? (data.tantoShokuId ?? '') : ''
  }
  local.tantoShokuKnj = data ? (data.tantoShokuKnj ?? '') : ''
}

const initSvJigyoData = (svJigyoId: string, key: string) => {
  let data: JigyoInfo = localOneway.codeListOneway.jigyoInfoList.find(
    (item) => item.svJigyoId === svJigyoId
  ) as JigyoInfo
  if (!data) {
    data = {
      jigyoKnj: '',
      svJigyoId: svJigyoId,
      tel: '',
      fax: '',
    }
  }
  local.jigyoKnj = data.jigyoKnj ?? ''
  if (key === 'set') {
    refValue.value.or15169Values.shienJigyoId = data.svJigyoId ?? ''
    if (refValue.value.or15169Values.telHead.mo00045) {
      refValue.value.or15169Values.telHead.mo00045.value = data.tel ?? ''
    }
    if (refValue.value.or15169Values.fax.mo00045) {
      refValue.value.or15169Values.fax.mo00045.value = data.fax ?? ''
    }
  }
}

/**
 * 画面メニューのイベントを監視
 */
watch(
  () => TeX0012Logic.data.get(props.parentUniqueCpId),
  (newValue) => {
    if (newValue?.teikyouId) {
      local.commonInfo = newValue
    }
  },
  { deep: true }
)

/**
 * Gui00059のイベントを監視
 *
 * @description
 * 自身のEvent領域の検索ボタン押下フラグを更新する。
 * またGui00059のボタン押下フラグをリセットする。
 */
watch(
  () => Gui00059Logic.event.get(gui00059.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    console.log(newValue, '111111111111111111')
    if (newValue.confirmFlg && newValue.confirmParams) {
      // refValue.value.or15169Values.hospKnjHead.value =
      //     info.hospital.find((item) => item.id === newValue.confirmParams?.hospitalCode)?.name ?? ''
      Gui00059Logic.event.set({
        uniqueCpId: gui00059.value.uniqueCpId,
        events: {
          confirmFlg: false,
          confirmParams: undefined,
        },
      })
    }

    if (newValue.confirmFlg && newValue.confirmParams) {
      // 子コンポーネントのflgをリセットする
      Gui00059Logic.event.set({
        uniqueCpId: gui00059.value.uniqueCpId,
        events: {
          confirmFlg: false,
          confirmParams: undefined,
        },
      })
    }
  }
)
</script>

<template>
  <div v-if="refValue.or15169Values">
    <c-v-row class="row border-top">
      <!-- 入院日 -->
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.admission_date') }}
      </c-v-col>
      <c-v-col
        cols="4"
        class="data-cell"
      >
        <base-mo00020
          v-model="refValue.or15169Values.sickYmd"
          :oneway-model-value="localOneway.mo00020Oneway"
        />
      </c-v-col>
      <!-- 情報提供日 -->
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.information_provision_date') }}
      </c-v-col>
      <c-v-col
        cols="4"
        class="data-cell"
      >
        <base-mo00020
          v-model="refValue.or15169Values.teikyouYmd"
          :oneway-model-value="localOneway.mo00020Oneway"
        />
      </c-v-col>
    </c-v-row>
    <!-- 医療機関名 -->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.medical_institution_name') }}
        <div class="d-flex">
          <c-v-divider
            vertical
            inset
          />
          <!--医療機関名選択アイコンボタン-->
          <base-mo00009
            :oneway-model-value="localOneway.mo00009Oneway"
            @click="handleHospKnjHead"
          >
            <!-- 医療機関選択モーダル -->
            <g-custom-gui-00059
              v-if="showDialog"
              v-bind="gui00059"
            />
          </base-mo00009>
        </div>
      </c-v-col>
      <c-v-col
        cols="10"
        class="data-cell"
      >
        <base-mo00045
          v-model="refValue.or15169Values.hospKnjHead"
          :oneway-model-value="localOneway.mo00045Oneway"
        />
      </c-v-col>
    </c-v-row>
    <!-- ご担当者名 -->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.responsible_person_name_label') }}
      </c-v-col>
      <c-v-col
        cols="10"
        class="data-cell"
      >
        <base-mo00045
          v-model="refValue.or15169Values.tantoKnj"
          :oneway-model-value="localOneway.mo00045Oneway1"
        />
      </c-v-col>
    </c-v-row>
    <!-- 事業所名 -->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.home_care_support_office_name_label') }}
        <div class="d-flex">
          <c-v-divider
            vertical
            inset
          />
          <!--居宅介護支援事業所名選択アイコンボタン-->
          <base-mo00009
            :oneway-model-value="localOneway.mo00009Oneway"
            @click="handleShienJigyoId"
          >
            <!-- Or10648:有機体:［事業所検索］画面 -->
            <g-custom-or-10648
              v-if="showDialogOr10648"
              v-bind="or10648"
              :oneway-model-value="localOneway.or10648OnewayType"
              @update:model-value="svJigyoIdChange"
            />
          </base-mo00009>
        </div>
      </c-v-col>
      <c-v-col
        cols="10"
        class="data-cell"
      >
        {{ local.jigyoKnj }}
      </c-v-col>
    </c-v-row>
    <!-- ケアマネジャー氏名-->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.care_manager_name_label') }}
        <div class="d-flex">
          <c-v-divider
            vertical
            inset
          />
          <!--ケアマネジャー氏名選択アイコンボタン-->
          <base-mo00009
            :oneway-model-value="localOneway.mo00009Oneway"
            @click="handleTantoShokuId"
          >
            <!-- GUI01186_担当ケアマネ検索画面を起動する -->
            <g-custom-or-26261
              v-if="showDialogOr26261"
              v-bind="or26261"
              v-model="local.or26261"
              :oneway-model-value="localOneway.or26261Oneway"
              @update:model-value="tantoShokuIdChange"
            />
          </base-mo00009>
        </div>
      </c-v-col>
      <c-v-col
        cols="4"
        class="data-cell"
      >
        {{ local.tantoShokuKnj }}
      </c-v-col>
      <!-- TEL -->
      <c-v-col
        cols="1"
        class="header-cell"
      >
        {{ t('label.tel_label') }}
      </c-v-col>
      <c-v-col
        cols="2"
        class="data-cell"
      >
        <base-mo00030
          v-model="refValue.or15169Values.telHead"
          :oneway-model-value="localOneway.mo00030Oneway"
        />
      </c-v-col>
      <!-- FAX -->
      <c-v-col
        cols="1"
        class="header-cell"
      >
        {{ t('label.fax_label') }}
      </c-v-col>
      <c-v-col
        cols="2"
        class="data-cell"
      >
        <base-mo00030
          v-model="refValue.or15169Values.fax"
          :oneway-model-value="localOneway.mo00030Oneway"
        />
      </c-v-col>
    </c-v-row>
  </div>
</template>

<style scoped lang="scss">
.row {
  display: flex;
  align-items: center;
  border-bottom: 1px gainsboro solid;
  border-left: 1px gainsboro solid;
  min-height: 62px;
}

.header-cell {
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 12px !important;
}

.header-title-cell {
  background-color: transparent;
  border-right: 1px gainsboro solid;
  display: grid;
  align-items: center;
}

.data-cell {
  border-left: 1px gainsboro solid;
  border-right: 1px gainsboro solid;
  background: #fff;
  padding: 0 12px;
  width: 100%;
  min-height: 62px;
  display: grid;
  align-items: center;
}

.border-top {
  border-top: 1px gainsboro solid;
}
:deep(.v-input__control) {
  background-color: rgb(var(--v-theme-surface));
}
</style>
