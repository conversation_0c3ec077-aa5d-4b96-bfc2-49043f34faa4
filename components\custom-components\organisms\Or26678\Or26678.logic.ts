import { Or26678Const } from './Or26678.constants'
import type { Or26678StateType } from './Or26678.type'
import { useInitialize, useOneWayBindAccessor, useTwoWayBindAccessor } from '#imports'
import type { MonitoringItem, Or26678Type } from '~/types/cmn/business/components/Or26678Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'

/**
 * Or26715:有機体:実施モニタリングマスタ
 *
 * @description
 * initialize処理とpinia領域操作用の処理を提供する
 *
 * <AUTHOR>
 */
export namespace Or26678Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize<Or26678Type>({
      cpId: Or26678Const.CP_ID(0),
      uniqueCpId,
      initTwoWayValue: {
        implementationMonitoringMasterList: [],
        /**
         * 番号
         */
        numberItem: {} as MonitoringItem,
        /**
         * 計画取込
         */
        planImportItem: {} as MonitoringItem,
        /**
         * 担当者
         */
        managerItem: {} as MonitoringItem,
        /**
         * 頻度
         */
        frequencyItem: {} as MonitoringItem,
        /**
         * 内容の省略
         */
        contentOmittedItem: [],
        /**
         * 枠内クリックの動作
         */
        inFrameItem: {} as MonitoringItem,
      },
      childCps: [
        { cpId: Or41179Const.CP_ID(0) },
        { cpId: Or21813Const.CP_ID(0) },
        { cpId: Or21814Const.CP_ID(1) },
      ],
    })
    // 子コンポーネントのセットアップ
    Or41179Logic.initialize(childCpIds[Or41179Const.CP_ID(0)].uniqueCpId)
    Or21813Logic.initialize(childCpIds[Or21813Const.CP_ID(0)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(1)].uniqueCpId)
    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }
  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or26678StateType>(Or26678Const.CP_ID(0))

  /**
   * TwoWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const data = useTwoWayBindAccessor<string>(Or26678Const.CP_ID(0))
}
