<script setup lang="ts">
/**
 * Or08207_基本情報画面入力フォーム
 * GUI01067_基本情報
 *
 * @description
 * 基本情報画面入力フォーム
 *
 *
 * <AUTHOR> HOANG SY TOAN
 */

import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or27633Const } from '../Or27633/Or27633.constants'
import { Or27633Logic } from '../Or27633/Or27633.logic'
import { Or10883Logic } from '../Or10883/Or10883.logic'
import { Or10883Const } from '../Or10883/Or10883.constants'
import { Or27349Const } from '../Or27349/Or27349.constants'
import { Or27349Logic } from '../Or27349/Or27349.logic'
import type { NinteiList } from '../Or27349/Or27349.type'
import type { Or08207OnewayType, Or08207StateType, ScreenData } from './Or08207.type'
import { Or08207Const } from './Or08207.constants'
import type { Or27633OnewayType } from '@/types/cmn/business/components/Or27633Type'
import { Or26491Const } from '~/components/custom-components/organisms/Or26491/Or26491.constants'
import { Or26491Logic } from '~/components/custom-components/organisms/Or26491/Or26491.logic'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { ConsultationUserInfo } from '~/repositories/cmn/entities/ConsultationUserInitSelectEntity'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import type { Mo00020OnewayType } from '~/types/business/components/Mo00020Type'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo00040OnewayType } from '~/types/business/components/Mo00040Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Mo00046OnewayType } from '~/types/business/components/Mo00046Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo01298OnewayType } from '~/types/business/components/Mo01298Type'
import type { Mo01299OnewayType } from '~/types/business/components/Mo01299Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Or26491OnewayType } from '~/types/cmn/business/components/Or26491Type'
import { CustomClass } from '~/types/CustomClassType'
import {
  dateUtils,
  useScreenTwoWayBind,
  useScreenUtils,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import type {
  Or10883OnewayType,
  Or10883TwowayType,
} from '~/types/cmn/business/components/Or10883Type'
import type { Or27349OnewayType } from '~/types/cmn/business/components/Or27349Type'

interface Props {
  onewayModelValue: Or08207OnewayType
  uniqueCpId: string
}

const props = defineProps<Props>()

const { t } = useI18n()
const { getChildCpBinds, setChildCpBinds } = useScreenUtils()
const systemCommonStore = useSystemCommonsStore()
const { convertDateToSeireki } = dateUtils()

const defaultOneway = {
  /**
   *基本情報ID
   */
  khn11Id: '1001',
  /**
   *コピーフラグ
   */
  isCopyFlag: false,
} as Or08207OnewayType

const localOneway = reactive({
  or08207: {
    ...defaultOneway,
    ...props.onewayModelValue,
  },
  mo01298OnewayConsultationRoute: {
    anchorPoint: 's-3',
    title: t('label.consultation-route'),
  } as Mo01298OnewayType,
  mo00039OnewayConsultationMethod: {
    name: '',
    showItemLabel: false,
    items: [],
  } as Mo00039OnewayType,
  mo00045OnewayConsulationRouteOthers: {
    width: '150',
    maxLength: '20',
    showItemLabel: false,
  } as Mo00045OnewayType,
  mo00009OnewayConsulationRouteIcon: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,
  mo00039HomeVisitKind: {
    name: '',
    showItemLabel: false,
    items: [],
  } as Mo00039OnewayType,
  Mo00020OnewayLastConsultationDate: {
    itemLabel: t('label.last_consultation_date'),
    showItemLabel: true,
    clearable: true,
    disabled: false,
    isVerticalLabel: false,
  } as Mo00020OnewayType,

  mo01298OnewayPersonPresentCondition: {
    anchorPoint: 's-3',
    title: t('label.person_present_condition'),
  } as Mo01298OnewayType,
  mo00018OnewayAtHome: {
    name: '',
    itemLabel: t('label.at_home'),
    isVerticalLabel: false,
  } as Mo00018OnewayType,
  Mo00018OnewayHospitalized: {
    name: '',
    itemLabel: t('label.hospitalized'),
    isVerticalLabel: false,
  } as Mo00018OnewayType,
  mo00018OnewayInstitutionalized: {
    name: '',
    itemLabel: t('label.institutionalized'),
    isVerticalLabel: false,
  } as Mo00018OnewayType,
  Mo00045OnewayPresentConditionOthers: {
    width: '300',
    minWidth: '500',
    maxLength: '40',
    showItemLabel: false,
    isVerticalLabel: true,
  } as Mo00045OnewayType,

  Mo00009OnewayPresentConditionOthersIcon: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,

  Mo01298OnewayEverydayLifeIndependenceLevel: {
    anchorPoint: 's-3',
    title: t('label.everydaylife-life-independence-level'),
  } as Mo01298OnewayType,
  mo01299OnewayHandycapSeniorEverydayLifeIndependenceLevel: {
    title: t('label.degree_indep_daily_living_eld_disab'),
  } as Mo01299OnewayType,
  Mo00039OnewayHandycapSeniorEverydayLifeIndependenceLevel: {
    name: '',
    showItemLabel: false,
    items: [],
  } as Mo00039OnewayType,
  Mo01299OnewayDegreeIndepDailyLivingEldDem: {
    title: t('label.degree_indep_daily_living_eld_dem'),
  } as Mo01299OnewayType,
  Mo00039OnewayDegreeIndepDailyLivingEldDem: {
    name: '',
    showItemLabel: false,
    items: [],
  } as Mo00039OnewayType,
  Mo01298OnewayCertCompBusinessInfo: {
    anchorPoint: 's-3',
    title: t('label.cert_comp_business_info'),
  } as Mo01298OnewayType,
  Mo00615OnewayManager: {
    itemLabel: t('label.manager'),
    customClass: new CustomClass({ outerClass: 'mr-0', labelClass: 'ma-1' }),
  } as Mo00615OnewayType,
  Mo00009OnewayCertificationInfoIcon: {
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  Mo00040OnewayLevelOfCareRequired: {
    showItemLabel: false,
    width: '150px',
    items: [],
    itemTitle: 'label',
    itemValue: 'value',
  } as Mo00040OnewayType,
  Mo00615OnewayExpirationDate: {
    itemLabel: t('label.expiration_date'),
    customClass: new CustomClass({ outerClass: 'mr-0', labelClass: 'ma-1' }),
  } as Mo00615OnewayType,
  Mo00020OnewayValidTimeLimitStartDate: {
    showItemLabel: false,
    clearable: true,
    disabled: false,
    isVerticalLabel: false,
    width: '160px',
  } as Mo00020OnewayType,
  Mo01338OnewayWaveDash: {
    itemLabel: '～',
    customClass: new CustomClass({ outerClass: 'mx-2', labelClass: 'ma-1' }),
  } as Mo01338OnewayType,
  Mo00020OnewayValidTimeLimitEndDate: {
    showItemLabel: false,
    clearable: true,
    disabled: false,
    isVerticalLabel: false,
    width: '160px',
  } as Mo00020OnewayType,
  Mo00615OnewayPrevCareDegree: {
    itemLabel: t('label.prev_care_degree'),
    customClass: new CustomClass({ outerClass: 'mr-0', labelClass: 'ma-1' }),
  } as Mo00615OnewayType,
  Mo00040OnewayPrevCareDegree: {
    showItemLabel: false,
    width: '150px',
    items: [],
    itemTitle: 'label',
    itemValue: 'value',
  } as Mo00040OnewayType,

  Mo01299OnewayBasicChecklistEntryResults: {
    title: t('label.basic_checklist_entry_results'),
  } as Mo01299OnewayType,
  Mo00039OnewayBasicChecklistEntryResult: {
    name: '',
    showItemLabel: false,
    items: [],
  } as Mo00039OnewayType,
  Mo01299OnewayBasicChecklistEntryDate: {
    title: t('label.basic_checklist_entry_date'),
  } as Mo01299OnewayType,
  Mo00009OnewayBasicChecklistEntryHistorySelectIcon: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  Mo00020OnewayBasicChecklistEntryDate: {
    showItemLabel: false,
    clearable: true,
    disabled: false,
    isVerticalLabel: false,
    width: '160px',
  } as Mo00020OnewayType,

  Mo01298OnewayDisabCert: {
    anchorPoint: 's-3',
    title: t('label.disab_cert'),
  } as Mo01298OnewayType,
  Mo00018OnewayDisability: {
    showItemLabel: false,
    checkboxLabel: t('label.disability'),
  } as Mo00018OnewayType,
  Mo00040OnewayDisabilityLevel: {
    showItemLabel: false,
    width: '90',
    items: [],
    itemTitle: 'label',
    itemValue: 'value',
  } as Mo00040OnewayType,
  Mo00018OnewayUpbringing: {
    showItemLabel: false,
    checkboxLabel: t('label.upbringing'),
  } as Mo00018OnewayType,
  Mo00040OnewayRemedialEducationGrade: {
    showItemLabel: false,
    width: '90',
    items: [],
    itemTitle: 'label',
    itemValue: 'value',
  } as Mo00040OnewayType,
  Mo00018OnewaySpirit: {
    showItemLabel: false,
    checkboxLabel: t('label.spirit'),
  } as Mo00018OnewayType,
  Mo00040OnewayMentalGrade: {
    showItemLabel: false,
    width: '90',
    items: [],
    itemTitle: 'label',
    itemValue: 'value',
  } as Mo00040OnewayType,
  Mo00018OnewayIncurableDisease: {
    showItemLabel: false,
    checkboxLabel: t('label.incurable-disease'),
  } as Mo00018OnewayType,
  Mo00045OnewayMemo1: {
    width: '150',
    maxLength: '16',
    showItemLabel: false,
    isVerticalLabel: true,
  } as Mo00045OnewayType,
  Mo00009OnewayMemo1Icon: {
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  Mo00045OnewayMemo2: {
    width: '180',
    maxLength: '22',
    showItemLabel: false,
    isVerticalLabel: true,
  } as Mo00045OnewayType,
  Mo00009OnewayMemo2Icon: {
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  Mo01298OnewayPersonLivingEnv: {
    anchorPoint: 's-3',
    title: t('label.person_living_env'),
  } as Mo01298OnewayType,
  Mo00018OnewayPersonalHome: {
    showItemLabel: false,
    checkboxLabel: t('label.home'),
  } as Mo00018OnewayType,
  Mo00018OnewayRentedHouse: {
    showItemLabel: false,
    checkboxLabel: t('label.rented_house'),
  } as Mo00018OnewayType,
  Mo00018OnewayDetachedHouse: {
    showItemLabel: false,
    checkboxLabel: t('label.detached_house'),
  } as Mo00018OnewayType,
  Mo00018OnewayApartment: {
    showItemLabel: false,
    checkboxLabel: t('label.apartment'),
  } as Mo00018OnewayType,
  Mo00039OnewayPrivateRoom: {
    name: 'private_room',
    itemLabel: t('label.private_room'),
    inline: true,
    items: [],
    customClass: new CustomClass({ outerClass: 'd-flex align-center' }),
  } as Mo00039OnewayType,
  Mo00045OnewayPrivateRoomFloor: {
    showItemLabel: false,
    width: '60px',
    maxLength: '3',
  } as Mo00045OnewayType,
  floorInputAppendLabel: { value: t('label.floor-input-append-label') },
  Mo00039OnewayHousingRepair: {
    name: 'housing-repair',
    itemLabel: t('label.housing-repair'),
    inline: true,
    items: [],
    customClass: new CustomClass({ outerClass: 'd-flex align-center' }),
  } as Mo00039OnewayType,

  Mo01298OnewayEconomicSituation: {
    anchorPoint: 's-3',
    title: t('label.economic_situation'),
  } as Mo01298OnewayType,
  Mo00018OnewayNationalPension: {
    showItemLabel: false,
    checkboxLabel: t('label.national_pension'),
  } as Mo00018OnewayType,
  Mo00018OnewayWelfarePension: {
    showItemLabel: false,
    checkboxLabel: t('label.welfare_pension'),
  } as Mo00018OnewayType,
  Mo00018OnewayDisabPension: {
    showItemLabel: false,
    checkboxLabel: t('label.disab_pension'),
  } as Mo00018OnewayType,
  Mo00018OnewayLifeProtection: {
    showItemLabel: false,
    checkboxLabel: t('label.life-protection'),
  } as Mo00018OnewayType,
  Mo00046OnewayEconomySituation: {
    showItemLabel: false,
    rows: '2',
    autoGrow: false,
    maxLength: '114',
    customClass: new CustomClass({ outerClass: 'w-75' }),
  } as Mo00046OnewayType,
  Mo00009OnewayEconomySituationIcon: {
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,

  Mo01298OnewayVisitorConsulteeInfo: {
    anchorPoint: 's-3',
    title: t('label.visitor_consultee_info'),
  } as Mo01298OnewayType,
  Mo00009OnewayVisitorConsulteeInfoIcon: {
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  Mo00045OnewayNameConsent: {
    itemLabel: t('label.name-consent'),
    isVerticalLabel: false,
    maxLength: '42',
    customClass: new CustomClass({ outerClass: 'w-75 mb-1', labelClass: 'mx-2' }),
  } as Mo00045OnewayType,
  Mo00045OnewayRelationship: {
    itemLabel: t('label.relationship'),
    isVerticalLabel: false,
    maxLength: '20',
    customClass: new CustomClass({ outerClass: 'w-50', labelClass: 'mx-2' }),
  } as Mo00045OnewayType,
  Mo00046OnewayAddressContactInfo: {
    itemLabel: t('label.address_contact_info'),
    isVerticalLabel: false,
    rows: '2',
    autoGrow: false,
    maxLength: '160',
    customClass: new CustomClass({ outerClass: 'w-75', labelClass: 'mt-1 mr-2' }),
  } as Mo00046OnewayType,

  Mo01298OnewayFamilyStructure: {
    anchorPoint: 's-3',
    title: t('label.family_structure'),
  } as Mo01298OnewayType,
  familyStructureLabel: { value: t('label.family_structure') },
  Mo00009OnewayFamilyStructureIcon: {
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  familyDiagram: {},
  familyDiagramZoomOutFlag: {},
  familyDiagramDescriptionLabel: {
    value:
      '◎＝本人、〇＝女性、□＝男性\n●■＝死亡、☆＝キーパーソン\n主介護者に「主」、副介護者に「副」\n（同居家族は〇で囲む）',
  },
  Mo00615OnewayFamilyRelSituation: {
    itemLabel: t('label.family_rel_situation'),
  } as Mo00615OnewayType,
  Mo00009OnewayFamilyRelSituationIcon: {
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  Mo00046OnewayFamilyRelationEtcSituation: {
    showItemLabel: false,
    rows: '3',
    autoGrow: false,
    maxLength: '320',
    customClass: new CustomClass({ outerClass: 'w-100', labelClass: 'mt-1 mr-2' }),
  } as Mo00046OnewayType,
})

const Or26491 = ref({ uniqueCpId: Or26491Const.CP_ID(1) })
const or10883 = ref({ uniqueCpId: '', editCpNo: '' })

const Or26491OnewayModel: Or26491OnewayType = {
  sc1Id: '1',
  itkJigyoId: '1',
  userid: '1',
}

const Or10883OnewayModel: Or10883OnewayType = {
  // 利用者ID
  userId: systemCommonStore.getUserId!,
  // 親画面.大分類ＣＤ
  t1Cd: '40',
  // 親画面.中分類ＣＤ
  t2Cd: '14',
  // 親画面.小分類ＣＤ
  t3Cd: '0',
  // 親画面.過去履歴用テーブル名
  historyTableName: 'kyc_tuc_plan11',
  // 過去履歴用カラム名
  historyColumnName: 'soudan_h_knj',
  // 親画面.選択セール文章内容
  inputContents: '',
  // タイトル
  title: t('label.opinion'),
}

const or27633 = ref({ uniqueCpId: Or27633Const.CP_ID(0) })
const or27349 = ref({ uniqueCpId: '' })

const or27633Data: Or27633OnewayType = {
  /**
   * 利用者ID
   */
  alUserid: '1',
}

const defaultLocal = {
  data: {
    consulationMethod: '',
    consulationRouteOthers: { value: '' },
    homeVisitKind: '',
    lastConsultationDate: { value: '' },
    atHome: { modelValue: false },
    hospitalized: { modelValue: false },
    institutionalized: { modelValue: false },
    presentConditionOthers: { value: '' },
    handycapSeniorEverydayLifeIndependenceLevel: '',
    degreeIndepDailyLivingEldDem: '',
    levelOfCareRequired: { modelValue: '' },
    validTimeLimitStartDate: { value: '' },
    validTimeLimitEndDate: { value: '' },
    prevCareDegree: { modelValue: '' },
    basicChecklistEntryResult: '',
    basicChecklistEntryDate: { value: '' },
    disability: { modelValue: false },
    disabilityLevel: { modelValue: '' },
    upbringing: { modelValue: false },
    remedialEducationGrade: { modelValue: '' },
    spirit: { modelValue: false },
    mentalGrade: { modelValue: '' },
    incrudableDisease: { modelValue: false },
    memo1: { value: '' },
    memo2: { value: '' },
    personalHome: { modelValue: false },
    rentedHouse: { modelValue: false },
    detachedHouse: { modelValue: false },
    apartment: { modelValue: false },
    privateRoom: '',
    privateRoomFloor: { value: '' },
    housingRepair: '',
    nationalPension: { modelValue: false },
    welfarePension: { modelValue: false },
    disabPension: { modelValue: false },
    lifeProtection: { modelValue: false },
    economySituation: { value: '' },
    nameConsent: { value: '' },
    relationship: { value: '' },
    addressContactInfo: { value: '' },
    Or31675: {
      line1: {
        name: '',
        relationship: '',
        address: '',
      },
      line2: {
        name: '',
        relationship: '',
        address: '',
      },
      line3: {
        name: '',
        relationship: '',
        address: '',
      },
      line4: {
        name: '',
        relationship: '',
        address: '',
      },
    },
    familyDiagramZoomOutFlag: false,
    familyRelationEtcSituation: { value: '' },
  },
} as ScreenData

const { refValue } = useScreenTwoWayBind<Or08207StateType>({
  cpId: Or08207Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
})

const local = ref<ScreenData>({
  ...defaultLocal,
})

//COMPUTED

const isShowDialogOr26491 = computed(() => {
  return Or26491Logic.state.get(Or26491.value.uniqueCpId)?.isOpen ?? false
})

const isShowDialogOr10883 = computed(() => {
  return Or10883Logic.state.get(or10883.value.uniqueCpId)?.isOpen ?? false
})

const showDialogOr27349 = computed(() => {
  return Or27349Logic.state.get(or27349.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr27633 = computed(() => {
  // Or27633のダイアログ開閉状態
  const state = Or27633Logic.state.get(or27633.value.uniqueCpId) as { isOpen?: boolean }
  return state?.isOpen ?? false
})

useSetupChildProps(props.uniqueCpId, {
  [Or27349Const.CP_ID(1)]: or27349.value,
  [Or10883Const.CP_ID(1)]: or10883.value,
})

watch(
  () => props.onewayModelValue,
  (newValue) => {
    localOneway.or08207 = {
      ...defaultOneway,
      ...newValue,
    }
  },
  { deep: true }
)

watch(
  () => refValue.value,
  (newValue) => {
    if (!newValue) return

    initData(newValue)
  },
  { deep: true, immediate: true }
)

watch(
  () => Or10883Logic.state.get(or10883.value.uniqueCpId)?.isOpen,
  (newValue, oldValue) => {
    // ダイアログが閉じたとき（trueからfalse）、返されたデータを処理します
    if (oldValue === true && newValue === false && or10883.value.editCpNo) {
      const bindData = getChildCpBinds(props.uniqueCpId, {
        Or10883: { cpPath: 'Or10883', twoWayFlg: true },
      })
      const data = bindData?.Or10883?.twoWayBind?.value as Or10883TwowayType

      if (data?.naiyo) {
        const content = data.naiyo
        const localData = local.value.data

        switch (or10883.value.editCpNo) {
          case 'consulationRouteOthers':
            if (localData.consulationRouteOthers?.value !== undefined) {
              localData.consulationRouteOthers.value = content
            }
            break
          case 'presentConditionOthers':
            if (localData.presentConditionOthers?.value !== undefined) {
              localData.presentConditionOthers.value = content
            }
            break
          case 'memo1':
            if (localData.memo1?.value !== undefined) {
              localData.memo1.value = content
            }
            break
          case 'memo2':
            if (localData.memo2?.value !== undefined) {
              localData.memo2.value = content
            }
            break
          case 'economySituation':
            if (localData.economySituation?.value !== undefined) {
              localData.economySituation.value = content
            }
            break
          case 'familyRelationEtcSituation':
            if (localData.familyRelationEtcSituation?.value !== undefined) {
              localData.familyRelationEtcSituation.value = content
            }
            break
          default:
            break
        }
      }
      or10883.value.editCpNo = ''
    }
  }
)

watch(
  () => localOneway.or08207.isCopyFlag,
  (newValue) => {
    if (newValue) {
      localOneway.mo00039OnewayConsultationMethod.disabled = true
      localOneway.mo00045OnewayConsulationRouteOthers.disabled = true
      localOneway.mo00009OnewayConsulationRouteIcon.disabled = true
      localOneway.mo00018OnewayAtHome.disabled = true
      localOneway.Mo00018OnewayHospitalized.disabled = true
      localOneway.mo00018OnewayInstitutionalized.disabled = true
      localOneway.Mo00045OnewayPresentConditionOthers.disabled = true
      localOneway.Mo00039OnewayHandycapSeniorEverydayLifeIndependenceLevel.disabled = true
      localOneway.Mo00039OnewayDegreeIndepDailyLivingEldDem.disabled = true
      localOneway.Mo00040OnewayLevelOfCareRequired.disabled = true
      localOneway.Mo00040OnewayPrevCareDegree.disabled = true
      localOneway.Mo00039OnewayBasicChecklistEntryResult.disabled = true
      localOneway.Mo00018OnewayDisability.disabled = true
      localOneway.Mo00040OnewayDisabilityLevel.disabled = true
      localOneway.Mo00018OnewayUpbringing.disabled = true
      localOneway.Mo00040OnewayRemedialEducationGrade.disabled = true
      localOneway.Mo00018OnewaySpirit.disabled = true
      localOneway.Mo00040OnewayMentalGrade.disabled = true
      localOneway.Mo00018OnewayIncurableDisease.disabled = true
      localOneway.Mo00045OnewayMemo1.disabled = true
      localOneway.Mo00045OnewayMemo2.disabled = true
      localOneway.Mo00020OnewayLastConsultationDate.disabled = true
      localOneway.Mo00020OnewayValidTimeLimitStartDate.disabled = true
      localOneway.Mo00020OnewayValidTimeLimitEndDate.disabled = true
      localOneway.Mo00020OnewayBasicChecklistEntryDate.disabled = true
      localOneway.Mo00018OnewayPersonalHome.disabled = true
      localOneway.Mo00018OnewayRentedHouse.disabled = true
      localOneway.Mo00018OnewayDetachedHouse.disabled = true
      localOneway.Mo00018OnewayApartment.disabled = true
      localOneway.Mo00039OnewayPrivateRoom.disabled = true
      localOneway.Mo00045OnewayPrivateRoomFloor.disabled = true
      localOneway.Mo00039OnewayHousingRepair.disabled = true
      localOneway.Mo00018OnewayNationalPension.disabled = true
      localOneway.Mo00018OnewayWelfarePension.disabled = true
      localOneway.Mo00018OnewayDisabPension.disabled = true
      localOneway.Mo00018OnewayLifeProtection.disabled = true
      localOneway.Mo00046OnewayEconomySituation.disabled = true
      localOneway.Mo00045OnewayNameConsent.disabled = true
      localOneway.Mo00045OnewayRelationship.disabled = true
      localOneway.Mo00046OnewayAddressContactInfo.disabled = true
      localOneway.Mo00046OnewayFamilyRelationEtcSituation.disabled = true
    }
  },
  { immediate: true }
)

watch(
  () => Or27349Logic.event.get(or27349.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.closeFlg) {
      Or27349Logic.event.set({
        uniqueCpId: or27349.value.uniqueCpId,
        events: { closeFlg: false },
      })
    }
    if (newValue.comfirmFlg) {
      Or27349Logic.event.set({
        uniqueCpId: or27349.value.uniqueCpId,
        events: { comfirmFlg: false },
      })
    }
  }
)

onMounted(async () => {
  await initCodes()
})

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 回数区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CONSULTATIONROUTE },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_VISITTYPE },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DEMENTIAEVRYDALIFEINDEPENDENCELEVEL },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_SENIOREVERYDALIFEINDEPENDENCELEVEL },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BASICCHECKLISTENTRYRESULTS },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_CAREDEGREECATEGORY },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DISABILITYNOTEBOOK },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_HANDYCAP },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_WELFARENOTEBOOK },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_PRIVATEROOMPRESENCE },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_HOMERENOVATIONPRESENCE },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  localOneway.mo00039OnewayConsultationMethod.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_CONSULTATIONROUTE
  )
  localOneway.mo00039HomeVisitKind.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_VISITTYPE
  )

  localOneway.Mo00039OnewayHandycapSeniorEverydayLifeIndependenceLevel.items =
    CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_DEMENTIAEVRYDALIFEINDEPENDENCELEVEL)
  localOneway.Mo00039OnewayDegreeIndepDailyLivingEldDem.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_SENIOREVERYDALIFEINDEPENDENCELEVEL
  )

  localOneway.Mo00039OnewayBasicChecklistEntryResult.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BASICCHECKLISTENTRYRESULTS
  )
  const careDegreeCategory = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_CAREDEGREECATEGORY
  )
  const levelOfCareRequiredItems = careDegreeCategory.filter(
    (item) => item.value !== Or08207Const.DEFAULTS.NURSING_CARE_VALUE
  )
  localOneway.Mo00040OnewayLevelOfCareRequired.items = [
    Or08207Const.DEFAULTS.EMPTY_OPTION,
    ...levelOfCareRequiredItems,
  ]
  localOneway.Mo00040OnewayPrevCareDegree.items = [
    Or08207Const.DEFAULTS.EMPTY_OPTION,
    ...careDegreeCategory,
  ]

  localOneway.Mo00040OnewayDisabilityLevel.items = [
    Or08207Const.DEFAULTS.EMPTY_OPTION,
    ...CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_DISABILITYNOTEBOOK),
  ]
  localOneway.Mo00040OnewayRemedialEducationGrade.items = [
    Or08207Const.DEFAULTS.EMPTY_OPTION,
    ...CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_HANDYCAP),
  ]
  localOneway.Mo00040OnewayMentalGrade.items = [
    Or08207Const.DEFAULTS.EMPTY_OPTION,
    ...CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_WELFARENOTEBOOK),
  ]

  localOneway.Mo00039OnewayPrivateRoom.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_PRIVATEROOMPRESENCE
  )
  localOneway.Mo00039OnewayHousingRepair.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_HOMERENOVATIONPRESENCE
  )
}

function initData(retrive: Or08207StateType) {
  const ret = retrive.kihonList
  if (!ret) return
  //相談経路
  local.value.data.consulationMethod = ret.soudanH
  local.value.data.consulationRouteOthers.value = ret.soudanIn
  local.value.data.homeVisitKind = ret.soudanH2
  local.value.data.lastConsultationDate.value = ret.maesouInYmd

  //本人の現況
  local.value.data.atHome.modelValue = ret.ztkUmu === Or08207Const.DEFAULTS.TRUE
  local.value.data.hospitalized.modelValue = ret.nyuinUmu === Or08207Const.DEFAULTS.TRUE
  local.value.data.institutionalized.modelValue = ret.nyushoUmu === Or08207Const.DEFAULTS.TRUE
  local.value.data.presentConditionOthers.value = ret.genkyoKnj

  //日常生活自立度
  local.value.data.handycapSeniorEverydayLifeIndependenceLevel = ret.netaCd1
  local.value.data.degreeIndepDailyLivingEldDem = ret.netaCd2

  //認定・総合事業情報
  local.value.data.levelOfCareRequired.modelValue = ret.ninteiHyouji
  local.value.data.validTimeLimitStartDate.value = ret.startYmd1
  local.value.data.validTimeLimitEndDate.value = ret.endYmd1
  local.value.data.prevCareDegree.modelValue = ret.maeyokaiHyouji
  local.value.data.basicChecklistEntryResult = ret.chklistkekaSentaku
  local.value.data.basicChecklistEntryDate.value = ret.chklistYmdHyouji

  //障害等認定
  local.value.data.disability.modelValue = ret.sinshoUmu === Or08207Const.DEFAULTS.TRUE
  local.value.data.disabilityLevel.modelValue = ret.sinshoHyouji
  local.value.data.upbringing.modelValue = ret.ryoikuUmu === Or08207Const.DEFAULTS.TRUE
  local.value.data.remedialEducationGrade.modelValue = ret.ryoikuHyouji
  local.value.data.spirit.modelValue = ret.seisinUmu === Or08207Const.DEFAULTS.TRUE
  local.value.data.mentalGrade.modelValue = ret.seisinHyouji
  local.value.data.incrudableDisease.modelValue = ret.nanbyoUmu === Or08207Const.DEFAULTS.TRUE
  local.value.data.memo1.value = ret.nanbyoMemoknj1
  local.value.data.memo2.value = ret.nanbyoMemoknj2

  //本人の住居環境
  local.value.data.personalHome.modelValue = ret.jitaKu === Or08207Const.DEFAULTS.TRUE
  local.value.data.rentedHouse.modelValue = ret.shakuya === Or08207Const.DEFAULTS.TRUE
  local.value.data.detachedHouse.modelValue = ret.ikkodate === Or08207Const.DEFAULTS.TRUE
  local.value.data.apartment.modelValue = ret.shugou === Or08207Const.DEFAULTS.TRUE
  local.value.data.privateRoom = ret.jisiTu
  local.value.data.privateRoomFloor.value = ret.jisituKai
  local.value.data.housingRepair = ret.kaishu

  //経済状況
  local.value.data.nationalPension.modelValue = ret.kokuNen === Or08207Const.DEFAULTS.TRUE
  local.value.data.welfarePension.modelValue = ret.kouNen === Or08207Const.DEFAULTS.TRUE
  local.value.data.disabPension.modelValue = ret.shoNen === Or08207Const.DEFAULTS.TRUE
  local.value.data.lifeProtection.modelValue = ret.seikatuHogo === Or08207Const.DEFAULTS.TRUE
  local.value.data.economySituation.value = ret.keizai

  //来所者(相談者)情報
  local.value.data.nameConsent.value = ret.sdNameKnj
  local.value.data.relationship.value = ret.sdZokugaraKnj
  local.value.data.addressContactInfo.value = ret.sdAddrKnj

  //家族構成
  local.value.data.familyDiagramZoomOutFlag = ret.shukushoFlg === Or08207Const.DEFAULTS.TRUE
  local.value.data.familyRelationEtcSituation.value = ret.kazokuKankeiIn

  //緊急連絡先
  local.value.data.Or31675.line1.name = ret.sdNameKnj1
  local.value.data.Or31675.line1.relationship = ret.sdZokugaraKnj1
  local.value.data.Or31675.line1.address = ret.jyuusyoKnj1
  local.value.data.Or31675.line2.name = ret.sdNameKnj2
  local.value.data.Or31675.line2.relationship = ret.sdZokugaraKnj2
  local.value.data.Or31675.line2.address = ret.jyuusyoKnj2
  local.value.data.Or31675.line3.name = ret.sdNameKnj3
  local.value.data.Or31675.line3.relationship = ret.sdZokugaraKnj3
  local.value.data.Or31675.line3.address = ret.jyuusyoKnj3
  local.value.data.Or31675.line4.name = ret.sdNameKnj4
  local.value.data.Or31675.line4.relationship = ret.sdZokugaraKnj4
  local.value.data.Or31675.line4.address = ret.jyuusyoKnj4
}

//METHODS
function handleOpenConsultationRouteModal() {
  // AC024
  // GUI01109 入力支援【相談経路】画面をポップアップで起動する。
  Or10883OnewayModel.inputContents = local.value.data.consulationRouteOthers.value
  or10883.value.editCpNo = 'consulationRouteOthers'
  Or10883Logic.state.set({
    uniqueCpId: or10883.value.uniqueCpId,
    state: { isOpen: true },
  })
}

function handleOpenPersonalStatusModal() {
  // AC027
  // GUI01109 入力支援【本人の現況】画面をポップアップで起動する。
  Or10883OnewayModel.inputContents = local.value.data.presentConditionOthers.value
  or10883.value.editCpNo = 'presentConditionOthers'
  Or10883Logic.state.set({
    uniqueCpId: or10883.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const Or27349Data: Or27349OnewayType = {
  // listData: [
  //   {
  //     id: '1',
  //     startDate: 1585699200000,
  //     endDate: 1585699200000,
  //     degreeOfCareRequired: '度護介要3',
  //     accreditationDate: 1583107200000,
  //     limit: 27048,
  //   },
  //   {
  //     id: '2',
  //     startDate: 1585699200000,
  //     endDate: 1585785600000,
  //     degreeOfCareRequired: '度護介要4',
  //     accreditationDate: 1583193600000,
  //     limit: 27048,
  //   },
  //   {
  //     id: '3',
  //     startDate: 1585699200000,
  //     endDate: 1585872000000,
  //     degreeOfCareRequired: '度護介要5',
  //     accreditationDate: 1583280000000,
  //     limit: 27048,
  //   },
  //   {
  //     id: '4',
  //     startDate: 1585699200000,
  //     endDate: 1585958400000,
  //     degreeOfCareRequired: '度護介要6',
  //     accreditationDate: 1583366400000,
  //     limit: 27048,
  //   },
  //   {
  //     id: '5',
  //     startDate: 1585699200000,
  //     endDate: 1586044800000,
  //     degreeOfCareRequired: '度護介要7',
  //     accreditationDate: 1583452800000,
  //     limit: 27048,
  //   },
  //   {
  //     id: '6',
  //     startDate: 1585699200000,
  //     endDate: 1586131200000,
  //     degreeOfCareRequired: '度護介要8',
  //     accreditationDate: 1583539200000,
  //     limit: 27048,
  //   },
  // ],
  startYmd1: '',
}

function handleClickCertificationInfoIcon() {
  // AC028
  // GUI01099 認定情報選択画面をポップアップで起動する。
  setChildCpBinds(props.uniqueCpId, {
    Or27349: {
      twoWayValue: {
        // 現在の認定情報を渡す
        selectedData: {
          yokaiKbn: local.value.data.levelOfCareRequired.modelValue,
          startYmd: local.value.data.validTimeLimitStartDate.value,
          endYmd: local.value.data.validTimeLimitEndDate.value,
        },
      },
    },
  })
  Or27349Logic.state.set({
    uniqueCpId: or27349.value.uniqueCpId,
    state: { isOpen: true },
  })
}

//GUI01074 基本チェックリスト履歴選択画面をポップアップで起動する。
function handleClickBasicChecklistEntryHistorySelectIcon() {
  Or26491Logic.state.set({
    uniqueCpId: Or26491.value.uniqueCpId,
    state: { isOpen: true },
  })
}

function handleConfirmOr26491(value: string) {
  local.value.data.basicChecklistEntryDate.value = value
}

function onClickMemo1Icon() {
  // AC036
  // GUI01109 入力支援【経済状況】画面をポップアップで起動する。
  Or10883OnewayModel.inputContents = local.value.data.memo1.value
  or10883.value.editCpNo = 'memo1'

  Or10883Logic.state.set({
    uniqueCpId: or10883.value.uniqueCpId,
    state: { isOpen: true },
  })
}

function onClickMemo2Icon() {
  // AC037
  // GUI01109 入力支援【経済状況】画面をポップアップで起動する。
  Or10883OnewayModel.inputContents = local.value.data.memo2.value
  or10883.value.editCpNo = 'memo2'

  Or10883Logic.state.set({
    uniqueCpId: or10883.value.uniqueCpId,
    state: { isOpen: true },
  })
}

function handleClickEconomySituationIcon() {
  // AC038
  // GUI01109 入力支援【経済状況】画面をポップアップで起動する。
  Or10883OnewayModel.inputContents = local.value.data.economySituation.value ?? ''
  or10883.value.editCpNo = 'economySituation'

  Or10883Logic.state.set({
    uniqueCpId: or10883.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/*
 *GUI01073 相談者選択画面をポップアップで起動する。
 */
function handleClickVisitorConsulteeInfoIcon() {
  Or27633Logic.state.set({
    uniqueCpId: or27633.value.uniqueCpId,
    state: { isOpen: true },
  })
}

function handleConfirmOr27633(value: ConsultationUserInfo) {
  local.value.data.nameConsent.value = value.nameKnj
  local.value.data.relationship.value = value.zokugaraKnj ?? ''
  local.value.data.addressContactInfo.value = value.addressKnj
}

function handleConfirmOr27349(value: NinteiList) {
  // 認定情報選択画面から返されたデータを処理
  if (value) {
    // 要介護度を設定
    if (value.yokaiKbn) {
      local.value.data.levelOfCareRequired.modelValue = value.yokaiKbn
    }
    // 認定有効開始日を設定
    if (value.startYmd) {
      const startDate = new Date(Number(value.startYmd))
      local.value.data.validTimeLimitStartDate.value = convertDateToSeireki(startDate)
    }
    // 認定終了日を設定
    if (value.endYmd) {
      const endDate = new Date(Number(value.endYmd))
      local.value.data.validTimeLimitEndDate.value = convertDateToSeireki(endDate)
    }
  }
}

function handleClickFamilyStructureIcon() {
  // AC040
  // GUI00629 家族図の登録画面をポップアップで起動する。
  // TODO 画面未作成
}

function handleClickFamilyRelSituationIcon() {
  // AC041
  // GUI01109 入力支援【家族関係等の状況】画面をポップアップで起動する。
  Or10883OnewayModel.inputContents = local.value.data.familyRelationEtcSituation.value ?? ''
  or10883.value.editCpNo = 'familyRelationEtcSituation'

  Or10883Logic.state.set({
    uniqueCpId: or10883.value.uniqueCpId,
    state: { isOpen: true },
  })
}

watch(
  () => local.value.data,
  (newValue) => {
    // Cập nhật lại refValue khi dữ liệu trên màn hình thay đổi
    if (refValue.value?.kihonList) {
      refValue.value.kihonList = {
        ...refValue.value.kihonList,
        soudanH: newValue.consulationMethod,
        soudanIn: newValue.consulationRouteOthers.value,
        soudanH2: newValue.homeVisitKind,
        maesouInYmd: newValue.lastConsultationDate.value,
        ztkUmu: newValue.atHome.modelValue ? '1' : '0',
        nyuinUmu: newValue.hospitalized.modelValue ? '1' : '0',
        nyushoUmu: newValue.institutionalized.modelValue ? '1' : '0',
        genkyoKnj: newValue.presentConditionOthers.value,
        netaCd1: newValue.handycapSeniorEverydayLifeIndependenceLevel,
        netaCd2: newValue.degreeIndepDailyLivingEldDem,
        ninteiHyouji: newValue.levelOfCareRequired.modelValue ?? '',
        startYmd1: newValue.validTimeLimitStartDate.value,
        endYmd1: newValue.validTimeLimitEndDate.value,
        maeyokaiHyouji: newValue.prevCareDegree.modelValue ?? '',
        chklistkekaSentaku: newValue.basicChecklistEntryResult,
        chklistYmdHyouji: newValue.basicChecklistEntryDate.value,
        sinshoUmu: newValue.disability.modelValue ? '1' : '0',
        sinshoHyouji: newValue.disabilityLevel.modelValue ?? '',
        ryoikuUmu: newValue.upbringing.modelValue ? '1' : '0',
        ryoikuHyouji: newValue.remedialEducationGrade.modelValue ?? '',
        seisinUmu: newValue.spirit.modelValue ? '1' : '0',
        seisinHyouji: newValue.mentalGrade.modelValue ?? '',
        nanbyoUmu: newValue.incrudableDisease.modelValue ? '1' : '0',
        nanbyoMemoknj1: newValue.memo1.value,
        nanbyoMemoknj2: newValue.memo2.value,
        jitaKu: newValue.personalHome.modelValue ? '1' : '0',
        shakuya: newValue.rentedHouse.modelValue ? '1' : '0',
        ikkodate: newValue.detachedHouse.modelValue ? '1' : '0',
        shugou: newValue.apartment.modelValue ? '1' : '0',
        jisiTu: newValue.privateRoom,
        jisituKai: newValue.privateRoomFloor.value,
        kaishu: newValue.housingRepair,
        kokuNen: newValue.nationalPension.modelValue ? '1' : '0',
        kouNen: newValue.welfarePension.modelValue ? '1' : '0',
        shoNen: newValue.disabPension.modelValue ? '1' : '0',
        seikatuHogo: newValue.lifeProtection.modelValue ? '1' : '0',
        keizai: newValue.economySituation.value ?? '',
        sdNameKnj: newValue.nameConsent.value,
        sdZokugaraKnj: newValue.relationship.value,
        sdAddrKnj: newValue.addressContactInfo.value ?? '',
        shukushoFlg: newValue.familyDiagramZoomOutFlag ? '1' : '0',
        kazokuKankeiIn: newValue.familyRelationEtcSituation.value ?? '',
        sdNameKnj1: newValue.Or31675.line1.name,
        sdZokugaraKnj1: newValue.Or31675.line1.relationship,
        jyuusyoKnj1: newValue.Or31675.line1.address,
        sdNameKnj2: newValue.Or31675.line2.name,
        sdZokugaraKnj2: newValue.Or31675.line2.relationship,
        jyuusyoKnj2: newValue.Or31675.line2.address,
        sdNameKnj3: newValue.Or31675.line3.name,
        sdZokugaraKnj3: newValue.Or31675.line3.relationship,
        jyuusyoKnj3: newValue.Or31675.line3.address,
        sdNameKnj4: newValue.Or31675.line4.name,
        sdZokugaraKnj4: newValue.Or31675.line4.relationship,
        jyuusyoKnj4: newValue.Or31675.line4.address,
      }
    }
  },
  { deep: true }
)
</script>

<template>
  <c-v-sheet
    class="d-flex flex-column w-100 pa-2"
    style="background: white !important"
  >
    <!-- 相談経路 -->
    <base-mo01298 :oneway-model-value="localOneway.mo01298OnewayConsultationRoute" />
    <c-v-sheet>
      <c-v-row
        no-gutters
        class="border-sm pa-2 align-end ga-2"
      >
        <base-mo00039
          v-model="local.data.consulationMethod"
          :oneway-model-value="localOneway.mo00039OnewayConsultationMethod"
        ></base-mo00039>
        <div class="d-flex">
          <base-mo00045
            v-model="local.data.consulationRouteOthers"
            :oneway-model-value="localOneway.mo00045OnewayConsulationRouteOthers"
          ></base-mo00045>
          <base-mo00009
            v-if="!localOneway.or08207.isCopyFlag"
            :oneway-model-value="localOneway.mo00009OnewayConsulationRouteIcon"
            @click="handleOpenConsultationRouteModal"
          ></base-mo00009>
        </div>
        <base-mo00039
          v-model="local.data.homeVisitKind"
          :oneway-model-value="localOneway.mo00039HomeVisitKind"
        />
        <base-mo00020
          v-model="local.data.lastConsultationDate"
          :oneway-model-value="localOneway.Mo00020OnewayLastConsultationDate"
        ></base-mo00020>
      </c-v-row>
    </c-v-sheet>

    <!-- 本人の現況 -->
    <base-mo01298 :oneway-model-value="localOneway.mo01298OnewayPersonPresentCondition" />
    <c-v-row
      no-gutters
      class="border-sm pa-2"
    >
      <div class="d-flex align-center">
        <base-mo00018
          v-model="local.data.atHome"
          :oneway-model-value="localOneway.mo00018OnewayAtHome"
        ></base-mo00018>
        <base-mo00018
          v-model="local.data.hospitalized"
          :oneway-model-value="localOneway.Mo00018OnewayHospitalized"
        ></base-mo00018>
        <base-mo00018
          v-model="local.data.institutionalized"
          :oneway-model-value="localOneway.mo00018OnewayInstitutionalized"
        ></base-mo00018>
      </div>
      <c-v-col
        cols="auto"
        class="d-flex align-center"
      >
        <base-mo00045
          v-model="local.data.presentConditionOthers"
          :oneway-model-value="localOneway.Mo00045OnewayPresentConditionOthers"
        ></base-mo00045>
        <base-mo00009
          v-if="!localOneway.or08207.isCopyFlag"
          :oneway-model-value="localOneway.Mo00009OnewayPresentConditionOthersIcon"
          @click="handleOpenPersonalStatusModal"
        ></base-mo00009>
      </c-v-col>
    </c-v-row>

    <!-- 日常生活自立度 -->
    <base-mo01298 :oneway-model-value="localOneway.Mo01298OnewayEverydayLifeIndependenceLevel" />
    <c-v-sheet>
      <c-v-row no-gutters>
        <c-v-col>
          <base-mo01299
            :oneway-model-value="
              localOneway.mo01299OnewayHandycapSeniorEverydayLifeIndependenceLevel
            "
          >
            <template #content>
              <base-mo00039
                v-model="local.data.handycapSeniorEverydayLifeIndependenceLevel"
                :oneway-model-value="
                  localOneway.Mo00039OnewayHandycapSeniorEverydayLifeIndependenceLevel
                "
              ></base-mo00039>
            </template>
          </base-mo01299>
        </c-v-col>
      </c-v-row>
      <c-v-row no-gutters>
        <c-v-col>
          <base-mo01299 :oneway-model-value="localOneway.Mo01299OnewayDegreeIndepDailyLivingEldDem">
            <template #content>
              <base-mo00039
                v-model="local.data.degreeIndepDailyLivingEldDem"
                :oneway-model-value="localOneway.Mo00039OnewayDegreeIndepDailyLivingEldDem"
              ></base-mo00039>
            </template>
          </base-mo01299>
        </c-v-col>
      </c-v-row>
    </c-v-sheet>

    <!-- 認定・総合事業情報 -->
    <base-mo01298 :oneway-model-value="localOneway.Mo01298OnewayCertCompBusinessInfo" />
    <c-v-sheet>
      <c-v-row
        no-gutters
        class="border-sm pa-2 ga-2"
      >
        <div class="d-flex align-center">
          <base-mo00615 :oneway-model-value="localOneway.Mo00615OnewayManager" />
          <base-mo00009
            v-if="!localOneway.or08207.isCopyFlag"
            :oneway-model-value="localOneway.Mo00009OnewayCertificationInfoIcon"
            @click="handleClickCertificationInfoIcon"
          ></base-mo00009>
          <base-mo00040
            v-model="local.data.levelOfCareRequired"
            :oneway-model-value="localOneway.Mo00040OnewayLevelOfCareRequired"
          ></base-mo00040>
        </div>
        <div class="d-flex align-center">
          <base-mo00615 :oneway-model-value="localOneway.Mo00615OnewayExpirationDate" />
          <base-mo00020
            v-model="local.data.validTimeLimitStartDate"
            :oneway-model-value="localOneway.Mo00020OnewayValidTimeLimitStartDate"
          ></base-mo00020>
          <base-mo00615 :oneway-model-value="localOneway.Mo01338OnewayWaveDash" />
          <base-mo00020
            v-model="local.data.validTimeLimitEndDate"
            :oneway-model-value="localOneway.Mo00020OnewayValidTimeLimitEndDate"
          ></base-mo00020>
        </div>
        <div class="d-flex align-center">
          <base-mo00615 :oneway-model-value="localOneway.Mo00615OnewayPrevCareDegree" />
          <base-mo00040
            v-model="local.data.prevCareDegree"
            :oneway-model-value="localOneway.Mo00040OnewayPrevCareDegree"
          ></base-mo00040>
        </div>
      </c-v-row>
      <c-v-row no-gutters>
        <c-v-col>
          <base-mo01299 :oneway-model-value="localOneway.Mo01299OnewayBasicChecklistEntryResults">
            <template #content>
              <base-mo00039
                v-model="local.data.basicChecklistEntryResult"
                :oneway-model-value="localOneway.Mo00039OnewayBasicChecklistEntryResult"
              ></base-mo00039>
            </template>
          </base-mo01299>
        </c-v-col>
      </c-v-row>
      <c-v-row no-gutters>
        <c-v-col>
          <base-mo01299 :oneway-model-value="localOneway.Mo01299OnewayBasicChecklistEntryDate">
            <template #content>
              <c-v-row no-gutters>
                <c-v-col class="d-flex align-center ga-2">
                  <base-mo00009
                    v-if="!localOneway.or08207.isCopyFlag"
                    :oneway-model-value="
                      localOneway.Mo00009OnewayBasicChecklistEntryHistorySelectIcon
                    "
                    @click="handleClickBasicChecklistEntryHistorySelectIcon"
                  ></base-mo00009>
                  <base-mo00020
                    v-model="local.data.basicChecklistEntryDate"
                    :oneway-model-value="localOneway.Mo00020OnewayBasicChecklistEntryDate"
                  ></base-mo00020>
                </c-v-col>
              </c-v-row>
            </template>
          </base-mo01299>
        </c-v-col>
      </c-v-row>
    </c-v-sheet>

    <!-- GUI01074 基本チェックリスト履歴選択画面をポップアップで起動する。 -->
    <g-custom-or-26491
      v-if="isShowDialogOr26491"
      v-bind="Or26491"
      :oneway-model-value="Or26491OnewayModel"
      @on-confirm="(value: string) => handleConfirmOr26491(value)"
    />

    <!-- 障害等認定 -->
    <base-mo01298 :oneway-model-value="localOneway.Mo01298OnewayDisabCert" />
    <c-v-sheet class="border-sm pa-2">
      <c-v-row no-gutters>
        <c-v-col>
          <c-v-row
            no-gutters
            class="ga-2"
          >
            <div class="d-flex align-center">
              <base-mo00018
                v-model="local.data.disability"
                :oneway-model-value="localOneway.Mo00018OnewayDisability"
              ></base-mo00018>
              <base-mo00040
                v-model="local.data.disabilityLevel"
                :oneway-model-value="localOneway.Mo00040OnewayDisabilityLevel"
              ></base-mo00040>
            </div>
            <div class="d-flex align-center">
              <base-mo00018
                v-model="local.data.upbringing"
                :oneway-model-value="localOneway.Mo00018OnewayUpbringing"
              ></base-mo00018>
              <base-mo00040
                v-model="local.data.remedialEducationGrade"
                :oneway-model-value="localOneway.Mo00040OnewayRemedialEducationGrade"
              ></base-mo00040>
            </div>
            <div class="d-flex align-center">
              <base-mo00018
                v-model="local.data.spirit"
                :oneway-model-value="localOneway.Mo00018OnewaySpirit"
              ></base-mo00018>
              <base-mo00040
                v-model="local.data.mentalGrade"
                :oneway-model-value="localOneway.Mo00040OnewayMentalGrade"
              ></base-mo00040>
            </div>
            <base-mo00018
              v-model="local.data.incrudableDisease"
              :oneway-model-value="localOneway.Mo00018OnewayIncurableDisease"
            ></base-mo00018>

            <div class="d-flex align-center mr-2">
              <base-mo00045
                v-model="local.data.memo1"
                :oneway-model-value="localOneway.Mo00045OnewayMemo1"
              ></base-mo00045>
              <base-mo00009
                v-if="!localOneway.or08207.isCopyFlag"
                :oneway-model-value="localOneway.Mo00009OnewayMemo1Icon"
                @click="onClickMemo1Icon"
              ></base-mo00009>
            </div>
            <div class="d-flex align-center">
              <base-mo00045
                v-model="local.data.memo2"
                :oneway-model-value="localOneway.Mo00045OnewayMemo2"
              ></base-mo00045>
              <base-mo00009
                v-if="!localOneway.or08207.isCopyFlag"
                :oneway-model-value="localOneway.Mo00009OnewayMemo2Icon"
                @click="onClickMemo2Icon"
              ></base-mo00009>
            </div>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </c-v-sheet>

    <!-- 本人の住居環境 -->
    <base-mo01298 :oneway-model-value="localOneway.Mo01298OnewayPersonLivingEnv" />
    <c-v-sheet class="border-sm pa-2">
      <c-v-row no-gutters>
        <div class="d-flex align-center justify-space-between ga-2">
          <base-mo00018
            v-model="local.data.personalHome"
            :oneway-model-value="localOneway.Mo00018OnewayPersonalHome"
          ></base-mo00018>
          <base-mo00018
            v-model="local.data.rentedHouse"
            :oneway-model-value="localOneway.Mo00018OnewayRentedHouse"
          ></base-mo00018>
          <base-mo00018
            v-model="local.data.detachedHouse"
            :oneway-model-value="localOneway.Mo00018OnewayDetachedHouse"
          ></base-mo00018>
          <base-mo00018
            v-model="local.data.apartment"
            :oneway-model-value="localOneway.Mo00018OnewayApartment"
          ></base-mo00018>
        </div>
        <div class="d-flex align-center ga-4 pl-4">
          <base-mo00039
            v-model="local.data.privateRoom"
            :oneway-model-value="localOneway.Mo00039OnewayPrivateRoom"
          ></base-mo00039>

          <div class="d-flex align-center">
            <base-mo00045
              v-model="local.data.privateRoomFloor"
              :oneway-model-value="localOneway.Mo00045OnewayPrivateRoomFloor"
            ></base-mo00045>
            <base-at-label v-bind="localOneway.floorInputAppendLabel"></base-at-label>
          </div>

          <base-mo00039
            v-model="local.data.housingRepair"
            :oneway-model-value="localOneway.Mo00039OnewayHousingRepair"
          ></base-mo00039>
        </div>
      </c-v-row>
    </c-v-sheet>

    <!-- 経済状況 -->
    <base-mo01298 :oneway-model-value="localOneway.Mo01298OnewayEconomicSituation" />
    <c-v-sheet class="border-sm pa-2">
      <c-v-row no-gutters>
        <div class="d-flex align-center justify-space-between">
          <base-mo00018
            v-model="local.data.nationalPension"
            :oneway-model-value="localOneway.Mo00018OnewayNationalPension"
          ></base-mo00018>
          <base-mo00018
            v-model="local.data.welfarePension"
            :oneway-model-value="localOneway.Mo00018OnewayWelfarePension"
          ></base-mo00018>
          <base-mo00018
            v-model="local.data.disabPension"
            :oneway-model-value="localOneway.Mo00018OnewayDisabPension"
          ></base-mo00018>
          <base-mo00018
            v-model="local.data.lifeProtection"
            :oneway-model-value="localOneway.Mo00018OnewayLifeProtection"
          ></base-mo00018>
        </div>
        <c-v-col class="d-flex align-center pl-4">
          <base-mo00046
            v-model="local.data.economySituation"
            :oneway-model-value="localOneway.Mo00046OnewayEconomySituation"
          ></base-mo00046>
          <base-mo00009
            v-if="!localOneway.or08207.isCopyFlag"
            :oneway-model-value="localOneway.Mo00009OnewayEconomySituationIcon"
            @click="handleClickEconomySituationIcon"
          ></base-mo00009>
        </c-v-col>
      </c-v-row>
    </c-v-sheet>

    <!-- 来所者(相談者)情報 -->
    <base-mo01298 :oneway-model-value="localOneway.Mo01298OnewayVisitorConsulteeInfo" />
    <c-v-sheet class="border-sm pa-2">
      <c-v-row no-gutters>
        <c-v-col class="d-flex align-center">
          <base-mo00009
            v-if="!localOneway.or08207.isCopyFlag"
            :oneway-model-value="localOneway.Mo00009OnewayVisitorConsulteeInfoIcon"
            @click="handleClickVisitorConsulteeInfoIcon"
          ></base-mo00009>
          <div class="w-100">
            <base-mo00045
              v-model="local.data.nameConsent"
              :oneway-model-value="localOneway.Mo00045OnewayNameConsent"
            ></base-mo00045>
            <base-mo00045
              v-model="local.data.relationship"
              :oneway-model-value="localOneway.Mo00045OnewayRelationship"
            ></base-mo00045>
          </div>
        </c-v-col>
        <c-v-col>
          <base-mo00046
            v-model="local.data.addressContactInfo"
            :oneway-model-value="localOneway.Mo00046OnewayAddressContactInfo"
          ></base-mo00046>
        </c-v-col>
      </c-v-row>
    </c-v-sheet>

    <!-- GUI01073 相談者選択画面をポップアップで起動する。 -->
    <g-custom-or-27633
      v-if="showDialogOr27633"
      v-bind="or27633"
      :unique-cp-id="or27633.uniqueCpId"
      :oneway-model-value="or27633Data"
      @on-confirm="(value: ConsultationUserInfo) => handleConfirmOr27633(value)"
    />

    <!-- 家族構成 -->
    <base-mo01298 :oneway-model-value="localOneway.Mo01298OnewayFamilyStructure" />
    <c-v-sheet class="border-sm pa-2">
      <c-v-row no-gutters>
        <c-v-col cols="4">
          <div class="d-flex justify-space-between">
            <base-at-label v-bind="localOneway.familyStructureLabel"></base-at-label>
            <div class="d-flex">
              <c-v-divider
                vertical
                style="height: 80%; align-self: center"
              ></c-v-divider>
              <base-mo00009
                v-if="!localOneway.or08207.isCopyFlag"
                :oneway-model-value="localOneway.Mo00009OnewayFamilyStructureIcon"
                @click="handleClickFamilyStructureIcon"
              ></base-mo00009>
            </div>
          </div>
          <!-- familyDiagram - Or37183 -->
          <!-- TODO 課題 #123175により、図を作成する機能については、中期では既存システムにて対応するため対象外とします。 -->
          <div class="familyDiagram border-sm"></div>
          <!-- TODO familyDiagramZoomOutFlag Hidden -->
        </c-v-col>
        <c-v-col
          cols="4"
          class="d-flex flex-column ml-4"
        >
          <label class="mb-2">
            <pre class="familyDiagramDescriptionLabel">{{
              localOneway.familyDiagramDescriptionLabel.value
            }}</pre>
          </label>
          <div class="d-flex justify-space-between">
            <base-mo00615
              :oneway-model-value="localOneway.Mo00615OnewayFamilyRelSituation"
            ></base-mo00615>
            <div class="d-flex">
              <c-v-divider
                vertical
                style="height: 80%; align-self: center"
              ></c-v-divider>
              <base-mo00009
                v-if="!localOneway.or08207.isCopyFlag"
                :oneway-model-value="localOneway.Mo00009OnewayFamilyRelSituationIcon"
                @click="handleClickFamilyRelSituationIcon"
              ></base-mo00009>
            </div>
          </div>
          <base-mo00046
            v-model="local.data.familyRelationEtcSituation"
            :oneway-model-value="localOneway.Mo00046OnewayFamilyRelationEtcSituation"
          ></base-mo00046>
        </c-v-col>
      </c-v-row>
    </c-v-sheet>

    <!-- 緊急連絡先 -->
    <g-custom-or-31675
      v-model="local.data.Or31675"
      :is-copy-flag="localOneway.or08207.isCopyFlag"
    ></g-custom-or-31675>

    <g-custom-or-10883
      v-if="isShowDialogOr10883"
      v-bind="or10883"
      :oneway-model-value="Or10883OnewayModel"
    />

    <g-custom-or-27349
      v-if="showDialogOr27349"
      v-bind="or27349"
      :oneway-model-value="Or27349Data"
      @confirm="handleConfirmOr27349"
    />
  </c-v-sheet>
</template>

<style scoped lang="scss">
:deep(.section-header) {
  max-width: 250px;
}

.familyDiagram {
  width: 100%;
  aspect-ratio: 2 / 1;
}
pre.familyDiagramDescriptionLabel {
  font-family: inherit;
}
</style>
