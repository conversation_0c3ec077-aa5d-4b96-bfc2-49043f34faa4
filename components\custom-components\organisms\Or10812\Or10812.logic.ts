import { Or10812Const } from './Or10812.constants'
import type { Or10812StateType } from './Or10812.type'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'

/**
 * Or10812:有機体:モーダル（特記事項選択画面モーダル））
 * 処理ロジック
 *
 * @description
 * initialize処理とpinia領域操作用の処理を提供する
 *
 * <AUTHOR>
 */
export namespace Or10812Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or10812Const.CP_ID(0),
      uniqueCpId,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [{ cpId: Or21815Const.CP_ID(0) }],
    })

    // 子コンポーネントのセットアップ
    Or21815Logic.initialize(childCpIds[Or21815Const.CP_ID(0)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }

  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or10812StateType>(Or10812Const.CP_ID(0))
}
