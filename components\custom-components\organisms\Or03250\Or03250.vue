<script setup lang="ts">
/**
 * GUI00834_［アセスメント（包括）］食事画面
 *
 * @description
 *
 * ［アセスメント（包括）］食事画面
 *
 * 画面ID_ GUI00834
 *
 * <AUTHOR>
 */

import { computed, onMounted, reactive, ref, watch } from 'vue'

import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'
import { TeX0008Logic } from '../../template/TeX0008/TeX0008.logic'
import type { Or34069Type } from '../Or34069/Or34069.type'
import { Or34069Const } from '../Or34069/Or34069.constants'
import { Or53105Const } from '../Or53105/Or53105.constants'
import { Or53105Logic } from '../Or53105/Or53105.logic'
import type {
  ConcreteCareItemType,
  ConcreteContentType,
  OrX00096OnewayType,
  OrX0096Type,
} from '../OrX0096/OrX0096.type'
import { Or59423Const } from '../Or59423/Or59423.constants'
import { Or59423Logic } from '../Or59423/Or59423.logic'
import { TeX0008Const } from '../../template/TeX0008/TeX0008.constants'
import { OrX0096Const } from '../OrX0096/OrX0096.constants'
import type { TeX0008StateType } from '../../template/TeX0008/TeX0008.type'
import { Or03250Const } from './Or03250.constants'
import { Or03250Logic } from './Or03250.logic'
import type { Or03250OnewayType } from './Or03250.type'
import type { TeX0008Type } from '~/types/cmn/business/components/TeX0008Type'
import { useScreenStore, useScreenUtils, useSetupChildProps, useSystemCommonsStore } from '#imports'

import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import { UPDATE_KBN } from '~/constants/classification-constants'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type { Or51105OnewayType, Or51105Type } from '~/types/cmn/business/components/Or51105Type'
import type { Or34069OnewayType } from '~/types/cmn/business/components/Or34069Type'
import type {
  assessmentComprehensiveQuestionInEntity,
  assessmentComprehensiveQuestionOutWebEntity,
} from '~/repositories/cmn/entities/assessmentComprehensiveQuestionSelect'
import type {
  AssessmentComprehensiveExcretionInitSelectInEntity,
  AssessmentComprehensiveExcretionInitSelectOutEntity,
  AssessmentComprehensiveExcretionUpdateInEntity,
  assessmentComprehensiveExcretionUpdateOutEntity,
  subInfoExcretion,
} from '~/repositories/cmn/entities/AssessmentComprehensiveExcretionInitSelectEntity'
import type { Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo00040Type } from '~/types/business/components/Mo00040Type'

/**************************************************
 * Props
 **************************************************/

interface Props {
  uniqueCpId: string
  onewayModelValue: Or03250OnewayType
  parentUniqueCpId: string
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/

const { t } = useI18n()

const { getChildCpBinds } = useScreenUtils()

/**
 * ロード状態制御
 */
const isLoading = ref<boolean>(false)

/**
 * componentRef
 */
const componentRef = ref<HTMLDivElement | null>(null)

/**
 * 保存用テーブルデータ
 */
const tableData = ref<Or34069Type>({} as Or34069Type)

/**
 * 画面更新区分
 */
const screenUpdateKbn = ref(UPDATE_KBN.DELETE)

/**
 * 共通情報ストア
 */
const systemCommonsStore = useSystemCommonsStore()

/**
 * データの更新回数
 */
let updateNum = ''

/**
 * API返却値の番号リストを一時保存する
 */
let dotNumberList: assessmentComprehensiveQuestionOutWebEntity['problemDotSolutionEtcInfo']['problemDotNumberInfoList'] =
  []

/**
 * Twoway
 */
const local = reactive({
  tableHeader: {},
  commonInfo: {} as TeX0008Type,
  orX0096: {
    listSection: props.onewayModelValue.questionList ?? [],
  } as OrX0096Type,
  or51105: {
    kigoImiList: [],
  } as Or51105Type,
  or34096: {
    title: t('label.excretion-wipe-care-2-title'),
    careItems: [
      {
        title: t('label.preparation-adjustment-title'),
        showMode: '0',
        careLabel: [
          {
            valueType: ['cn011', 'cn012', 'cn013'],
            label: t('label.excretion-wipe-care-2-cellTitle11'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn021', 'cn022', 'cn023'],
            label: t('label.excretion-wipe-care-2-cellTitle12'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn031', 'cn032', 'cn033'],
            label: t('label.excretion-wipe-care-2-cellTitle13'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn041', 'cn042', 'cn043'],
            label: t('label.excretion-wipe-care-2-cellTitle14'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn051', 'cn052', 'cn053'],
            label: t('label.excretion-wipe-care-2-cellTitle15'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn061', 'cn062', 'cn063'],
            label: t('label.excretion-wipe-care-2-cellTitle16'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn071', 'cn072', 'cn073'],
            label: t('label.excretion-wipe-care-2-cellTitle17'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
        ],
      },
      {
        title: t('label.move-title'),
        showMode: '0',
        careLabel: [
          {
            valueType: ['cn081', 'cn082', 'cn083'],
            label: t('label.excretion-wipe-care-2-cellTitle21'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn091', 'cn092', 'cn093'],
            label: t('label.excretion-wipe-care-2-cellTitle22'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn101', 'cn102', 'cn103'],
            label: t('label.excretion-wipe-care-2-cellTitle23'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn111', 'cn112', 'cn113'],
            label: t('label.excretion-wipe-care-2-cellTitle24'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn121', 'cn122', 'cn123'],
            label: t('label.excretion-wipe-care-2-cellTitle25'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
        ],
      },
      {
        title: t('label.excretion-wipe-care-2-dataTitle1'),
        showMode: '0',
        careLabel: [
          {
            valueType: ['cn131', 'cn132', 'cn133'],
            label: t('label.excretion-wipe-care-2-cellTitle31'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn141', 'cn142', 'cn143'],
            label: t('label.excretion-wipe-care-2-cellTitle32'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn151', 'cn152', 'cn153'],
            label: t('label.excretion-wipe-care-2-cellTitle33'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn161', 'cn162', 'cn163'],
            label: t('label.excretion-wipe-care-2-cellTitle34'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn171', 'cn172', 'cn173'],
            label: t('label.excretion-wipe-care-2-cellTitle35'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn181', 'cn182', 'cn183'],
            label: t('label.excretion-wipe-care-2-cellTitle36'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn191', 'cn192', 'cn193'],
            label: t('label.excretion-wipe-care-2-cellTitle37'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn201', 'cn202', 'cn203'],
            label: t('label.excretion-wipe-care-2-cellTitle38'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
        ],
      },
      {
        title: t('label.excretion-wipe-care-2-dataTitle2'),
        showMode: '0',
        careLabel: [
          {
            valueType: ['cn211', 'cn212', 'cn213'],
            label: t('label.excretion-wipe-care-2-cellTitle41'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn221', 'cn222', 'cn223'],
            label: t('label.excretion-wipe-care-2-cellTitle42'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn231', 'cn232', 'cn233'],
            label: t('label.excretion-wipe-care-2-cellTitle43'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn241', 'cn242', 'cn243'],
            label: t('label.excretion-wipe-care-2-cellTitle44'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn251', 'cn252', 'cn253'],
            label: t('label.excretion-wipe-care-2-cellTitle45'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn261', 'cn262', 'cn263'],
            label: t('label.excretion-wipe-care-2-cellTitle46'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
        ],
      },
      {
        title: t('label.excretion-wipe-care-2-dataTitle3-3'),
        showMode: '0',
        careLabel: [
          {
            valueType: ['cn271', 'cn272', 'cn273'],
            label: t('label.excretion-wipe-care-2-cellTitle51'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn281', 'cn282', 'cn283'],
            label: t('label.excretion-wipe-care-2-cellTitle52'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn291', 'cn292', 'cn293'],
            label: t('label.excretion-wipe-care-2-cellTitle53'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
          {
            valueType: ['cn301', 'cn302', 'cn303'],
            label: t('label.excretion-wipe-care-2-cellTitle54'),
            offerValue: { modelValue: '' },
            familyValue: { modelValue: '' },
            planValue: { modelValue: '' },
          },
        ],
      },
    ],
    careLocationItems: [
      {
        title: t('label.excretion-wipe-care-2-dataTitle4'),
        showMode: '0',
        careLocationLabel: [
          {
            valueType: ['cb011'],
            locationValue: { modelValue: '' },
            label: t('label.assessment-home-6-2-checkbox-label-9'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            valueType: ['cb021'],
            locationValue: { modelValue: '' },
            label: t('label.excretion-wipe-care-2-cell62'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            valueType: ['cb031'],
            locationValue: { modelValue: '' },
            label: t('label.excretion-wipe-care-2-cell63'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            valueType: ['cb041', 'cb042Knj'],
            locationValue: { modelValue: '' },
            label: t('label.other-label') + '：',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
          },
        ],
      },
      {
        title: t('label.excretion-wipe-care-2-dataTitle5'),
        showMode: '0',
        careLocationLabel: [
          {
            valueType: ['cb051'],
            locationValue: { modelValue: '' },
            label: t('label.excretion-wipe-care-2-cell62'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            valueType: ['cb061'],
            locationValue: { modelValue: '' },
            label: t('label.assessment-home-6-2-checkbox-label-15'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            valueType: ['cb071'],
            locationValue: { modelValue: '' },
            label: t('label.excretion-wipe-care-2-cell70'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            valueType: ['cb081'],
            locationValue: { modelValue: '' },
            label: t('label.excretion-wipe-care-2-cell71'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            valueType: ['cb091'],
            locationValue: { modelValue: '' },
            label: t('label.excretion-wipe-care-2-cell72'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            valueType: ['cb101'],
            locationValue: { modelValue: '' },
            label: t('label.excretion-wipe-care-2-cell73'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            valueType: ['cb111'],
            locationValue: { modelValue: '' },
            label: t('label.excretion-wipe-care-2-cell74'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            valueType: ['cb121'],
            locationValue: { modelValue: '' },
            label: t('label.excretion-wipe-care-2-cell75'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            valueType: ['cb131'],
            locationValue: { modelValue: '' },
            label: t('label.excretion-wipe-care-2-cell76'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            valueType: ['cb141'],
            locationValue: { modelValue: '' },
            label: t('label.excretion-wipe-care-2-cell77'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            valueType: ['cb151'],
            locationValue: { modelValue: '' },
            label: t('label.excretion-wipe-care-2-cell78'),
            inputShowMode: {
              appendInput: '0',
            },
          },
          {
            valueType: ['cb161', 'cb162Knj'],
            locationValue: { modelValue: '' },
            label: t('label.excretion-wipe-care-2-cell79'),
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
          },
          {
            valueType: ['cb171', 'cb172Knj'],
            locationValue: { modelValue: '' },
            label: t('label.bathtub-type-label-6'),
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
          },
          {
            valueType: ['cb181', 'cb182Knj'],
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
          },
          {
            valueType: ['cb191', 'cb192Knj'],
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
          },
          {
            valueType: ['cb201', 'cb202Knj'],
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
          },
          {
            valueType: ['cb211', 'cb212Knj'],
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
          },
          {
            valueType: ['cb221', 'cb222Knj'],
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
          },
          {
            valueType: ['cb231', 'cb232Knj'],
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
          },
          {
            valueType: ['cb241', 'cb242Knj'],
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
          },
          {
            valueType: ['cb251', 'cb252Knj'],
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
          },
          {
            valueType: ['cb261', 'cb262Knj'],
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
          },
          {
            valueType: ['cb271', 'cb272Knj'],
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
          },
          {
            valueType: ['cb281', 'cb282Knj'],
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
          },
          {
            valueType: ['cb291', 'cb292Knj'],
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
          },
          {
            valueType: ['cb301', 'cb302Knj'],
            locationValue: { modelValue: '' },
            label: '',
            inputShowMode: {
              appendInput: '1',
            },
            inputContent: {
              value: '',
            },
          },
        ],
      },
    ],
  } as Or34069Type,
})

/**
 * ロカールOneway
 */
const localOneway = reactive({
  orX0096Oneway: {
    showInputFlg: false,
    tableDisplayFlg: true,
    b1Cd: '1',
  } as OrX00096OnewayType,
  or51105Oneway: {
    sc1Id: '',
    cc1Id: '',
  } as Or51105OnewayType,
  or34069Oneway: {
    showTableBodyFlg: true,
  } as Or34069OnewayType,
})

const or34069 = ref({ uniqueCpId: '', showTableBodyFlg: true })
const or53105 = ref({ uniqueCpId: '' })
const orX0096 = ref({ uniqueCpId: '', parentUniqueCpId: props.uniqueCpId })
const or21814 = ref({ uniqueCpId: '' })

/**************************************************
 * Pinia
 **************************************************/
useSetupChildProps(props.uniqueCpId, {
  [Or34069Const.CP_ID(0)]: or34069.value,
  [Or53105Const.CP_ID(0)]: or53105.value,
  [Or21814Const.CP_ID(1)]: or21814.value,
  [OrX0096Const.CP_ID(1)]: orX0096.value,
})

/**************************************************
 * computed
 **************************************************/

/**
 * ダイアログ表示フラグ
 */
const showDialogOr53105CksFlg1 = computed(() => {
  // Or53105 cks_flg=1 のダイアログ開閉状態
  return Or53105Logic.state.get(or53105.value.uniqueCpId)?.isOpen ?? false
})

/**
 * ダイアログ表示フラグ
 */
const showOr21814DialogFlg = computed(() => {
  // ダイアログの開閉フラグ
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 画面データ変更フラグ
 */
const _isEdit = computed(() => {
  const isEditByUniqueCpIds = useScreenStore().isEditByUniqueCpId(props.uniqueCpId)
  return isEditByUniqueCpIds
})

/**************************************************
 * 関数定義
 **************************************************/
/**
 * コントロール初期化
 */
const initControl = () => {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      firstBtnLabel: t('btn.yes'),
      firstBtnType: 'normal1',
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      dialogTitle: t('label.confirm'),
    },
  })
}

/**
 * 番号設定関数
 *
 * @param index - 変換前のインデックス
 */
function setCircleNumber(index: number) {
  const circleNumbers = [
    '①',
    '②',
    '③',
    '④',
    '⑤',
    '⑥',
    '⑦',
    '⑧',
    '⑨',
    '⑩',
    '⑪',
    '⑫',
    '⑬',
    '⑭',
    '⑮',
    '⑯',
    '⑰',
    '⑱',
    '⑲',
    '⑳',
  ]
  if (index >= 1 && index <= 20) {
    return circleNumbers[index - 1]
  }
}

/**
 * 共通情報取得
 */
function getCommonInfo() {
  const commonInfo = TeX0008Logic.data.get(props.parentUniqueCpId)
  if (commonInfo) {
    local.commonInfo = commonInfo
    screenUpdateKbn.value = commonInfo.updateKbn ?? ''
    // 更新区分クリア
    if (commonInfo.updateKbn === UPDATE_KBN.DELETE) {
      localOneway.orX0096Oneway.tableDisplayFlg = false
    } else {
      localOneway.orX0096Oneway.tableDisplayFlg = true
    }
  }
}

/**
 * 複写モード共通情報取得
 */
const getDuplicateCommonInfo = () => {
  const commonInfo = Or59423Logic.data.get(props.parentUniqueCpId + Or59423Const.CP_ID(0))
  console.log(commonInfo, 'commonInfo')
  if (commonInfo) {
    local.commonInfo = {
      ninteiFormF: commonInfo.duplicateInfo?.ninteiFormF,
      activeTabId: commonInfo.duplicateInfo?.activeTabId,
      jigyoId: commonInfo.duplicateInfo?.jigyoId,
      houjinId: commonInfo.duplicateInfo?.houjinId,
      shisetuId: commonInfo.duplicateInfo?.shisetuId,
      userId: commonInfo.duplicateInfo?.userId,
      syubetsuId: commonInfo.duplicateInfo?.syubetsuId,
      createYmd: commonInfo.duplicateInfo?.createYmd,
      historyUpdateKbn: commonInfo.duplicateInfo?.historyUpdateKbn,
      historyModifiedCnt: commonInfo.duplicateInfo?.historyModifiedCnt,
      sc1Id: commonInfo.duplicateInfo?.sc1Id,
      recId: commonInfo.duplicateInfo?.recId,
      cc1Id: commonInfo.duplicateInfo?.cc1Id,
      createUserId: commonInfo.duplicateInfo?.createUserId,
      svJigyoId: commonInfo.duplicateInfo?.svJigyoId,
      listSection: commonInfo.duplicateInfo?.listSection,
      modifiedCnt: commonInfo.duplicateInfo?.modifiedCnt,
      updateKbn: commonInfo.duplicateInfo?.updateKbn,
      thingsToKeepInMindContentsInfo: commonInfo.duplicateInfo?.thingsToKeepInMindContentsInfo,
      planPeriodFlg: commonInfo.duplicateInfo?.planPeriodFlg,
    }
  }
}

/**
 * 親画面のstateを変更する
 *
 * @param state - state
 */
const setTeX0008State = (state: TeX0008StateType) => {
  TeX0008Logic.state.set({
    uniqueCpId: props.parentUniqueCpId,
    state: state,
  })
}

/**
 * 初期情報取得
 */
async function getInitDataInfo() {
  try {
    const inputData: AssessmentComprehensiveExcretionInitSelectInEntity = {
      cc1Id: local.commonInfo.cc1Id ?? '',
      sc1Id: local.commonInfo.sc1Id ?? '',
    }

    // 初期情報取得APIを呼び出す
    const resData: BaseResponseBody<AssessmentComprehensiveExcretionInitSelectOutEntity> =
      await ScreenRepository.select('assessmentComprehensiveExcretionInitSelect', inputData)
    // 返却値チェック
    if (resData.data) {
      processInfoData(resData, true)
      updateNum = resData.data.subInfoExcretion.modifiedCnt ?? ''
    }
  } catch (e) {
    console.log(e)
  }
}

/**
 * 問題点初期情報取得
 */
const getProblemDotSolutionEtcInfoData = async () => {
  try {
    const inputData: assessmentComprehensiveQuestionInEntity = {
      cc1Id: local.commonInfo?.cc1Id,
      sc1Id: local.commonInfo?.sc1Id,
      // タブID：「1:食事」
      typeId: '1',
    }

    const resData: BaseResponseBody<assessmentComprehensiveQuestionOutWebEntity> =
      await ScreenRepository.select('problemDotSolutionEtcInfoSelect', inputData)
    if (resData.data) {
      processParentData(resData.data.problemDotSolutionEtcInfo)
    }
  } catch (e) {
    console.log(e)
  }
}

/**
 * 複写情報取得
 */
const getDuplicateDataInfo = async () => {
  const inputData: AssessmentComprehensiveExcretionInitSelectInEntity = {
    cc1Id: local.commonInfo.duplicateCareCheckId ?? '',
    sc1Id: local.commonInfo.duplicatePlanId ?? '',
  }
  // 初期情報取得APIを呼び出す
  const resData: BaseResponseBody<AssessmentComprehensiveExcretionInitSelectOutEntity> =
    await ScreenRepository.select('assessmentComprehensiveExcretionInitSelect', inputData)
  // 返却値チェック
  if (resData.data) {
    processInfoData(resData, false)
  }
}

/**
 * 複写情報取得「問題点情報」
 */
const getDuplicateProblemDotSolutionEtcInfoData = async () => {
  try {
    const inputData: assessmentComprehensiveQuestionInEntity = {
      cc1Id: local.commonInfo?.duplicateCareCheckId ?? '',
      sc1Id: local.commonInfo?.duplicatePlanId ?? '',
      // タブID：「1：食事」
      typeId: '1',
    }

    const resData: BaseResponseBody<assessmentComprehensiveQuestionOutWebEntity> =
      await ScreenRepository.select('problemDotSolutionEtcInfoSelect', inputData)
    if (resData.data) {
      processParentData(resData.data.problemDotSolutionEtcInfo)
    }
  } catch (e) {
    console.log(e)
  }
}

/**
 * API返却値処理
 *
 * @param resData - API返却値
 *
 * @param updateFlg - 更新回数フラグ
 */
const processInfoData = (
  resData: BaseResponseBody<AssessmentComprehensiveExcretionInitSelectOutEntity>,
  updateFlg: boolean
) => {
  if (!resData.data) return
  // 複写モードの場合、返却値を保存する
  if (props.onewayModelValue.screenMode === TeX0008Const.DEFAULT.MODE_COPY) {
    Or03250Logic.data.set({
      uniqueCpId: props.uniqueCpId,
      value: {
        comprehensiveQuestionInfo: props.onewayModelValue.comprehensiveQuestionInfo,
        result: resData,
      },
    })
  }
  // セレクト選択肢設定
  const { careOfferLocationInfoList, scheduleInfoList, familyInfoList, offerInfoList } =
    resData.data.markInfo
  localOneway.or34069Oneway.careOfferMarkMeaning = offerInfoList.map((item) => {
    return {
      title: item.textKnj,
      value: item.kbnCd,
    }
  })
  localOneway.or34069Oneway.careFamilyMarkMeaning = familyInfoList.map((item) => {
    return {
      title: item.textKnj,
      value: item.kbnCd,
    }
  })
  localOneway.or34069Oneway.carePlanMarkMeaning = scheduleInfoList.map((item) => {
    return {
      title: item.textKnj,
      value: item.kbnCd,
    }
  })
  localOneway.or34069Oneway.careLocationMarkMeaning = careOfferLocationInfoList.map((item) => {
    return {
      title: item.textKnj,
      value: item.kbnCd,
    }
  })

  // 複数回の更新による無限再帰を防ぐために、データをディープコピーし、以下の処理は別データに移管する
  tableData.value = cloneDeep(local.or34096)
  for (const key in resData.data.subInfoExcretion) {
    tableData.value.careItems.forEach((item) => {
      item.careLabel.forEach((itm) => {
        const index = itm.valueType?.indexOf(key)
        switch (index) {
          case 0:
            itm.offerValue.modelValue = resData.data?.subInfoExcretion[key]
            break
          case 1:
            itm.familyValue.modelValue = resData.data?.subInfoExcretion[key]
            break
          case 2:
            itm.planValue.modelValue = resData.data?.subInfoExcretion[key]
            break
        }
      })
    })
    tableData.value.careLocationItems.forEach((item) => {
      item.careLocationLabel.forEach((itm) => {
        const index = itm.valueType?.indexOf(key)
        switch (index) {
          case 0:
            itm.locationValue.modelValue = resData.data?.subInfoExcretion[key] ?? ''
            break
          case 1:
            ;(itm.inputContent as Mo00045Type).value = resData.data?.subInfoExcretion[key] ?? ''
            break
        }
      })
    })
  }
  // 処理済みのデータを画面に表示する
  local.or34096 = tableData.value
  console.log(local.or34096, '32321323132313')
  // 複写再発火の場合は、サブ情報の更新回数は上書きしない
  if (updateFlg) {
    updateNum = resData.data.subInfoExcretion.modifiedCnt ?? ''
  }
}

/**
 * 新規処理
 */
const createNew = () => {
  local.or34096.careItems.forEach((item) => {
    // 更新区分を新規にする
    screenUpdateKbn.value = UPDATE_KBN.CREATE
    item.careLabel.forEach((sItem) => {
      sItem.planValue = { modelValue: '' }
      sItem.offerValue = { modelValue: '' }
      sItem.familyValue = { modelValue: '' }
      if (sItem.label === '') {
        sItem.inputContent = {
          value: '',
        }
      }
    })
  })
  local.or34096.careLocationItems.forEach((item) => {
    item.careLocationLabel.forEach((sItem) => {
      sItem.locationValue = { modelValue: '' }
      if (
        sItem.label === '' ||
        sItem.inputShowMode.appendInput === '1' ||
        sItem.inputShowMode.appendInput === '2'
      ) {
        sItem.inputContent = {
          value: '',
        }
      }
    })
  })
  // 各ラベルをクリア
  const newQuestionList = local.orX0096.listSection.map((item) => {
    return {
      ...item,
      isHaveIssuesFlg: false,
      isPlanningFlg: false,
    }
  })
  // 要介護課題等々クリア
  local.orX0096 = {
    listSection: newQuestionList,
    concreteCareItemList: [],
    concreteContentList: [],
  }
  // 各プルダウン選択肢リストクリア
  localOneway.or34069Oneway = {
    carePlanMarkMeaning: [],
    careOfferMarkMeaning: [],
    careFamilyMarkMeaning: [],
    careLocationMarkMeaning: [],
    showTableBodyFlg: true,
  }
}

/**
 * 問題点情報の共通処理
 *
 * @param comprehensiveQuestionInfo - API返却値
 */
function processParentData(
  comprehensiveQuestionInfo: assessmentComprehensiveQuestionOutWebEntity['problemDotSolutionEtcInfo']
) {
  localOneway.orX0096Oneway.officeId = local.commonInfo.svJigyoId ?? ''
  // 本画面のデータを絞り出す
  // 対応するケア項目リスト
  const concreteCareItemList: ConcreteCareItemType[] = comprehensiveQuestionInfo.careItemList
    .filter((item) => item.b1Cd === Or03250Const.DEFAULT.TAB_ID)
    .map((item) => {
      return {
        key: item.cc33Id,
        content: item.memoKnj,
        dmyCc32Id: item.dmyCc32Id,
        modifiedCnt: item.modifiedCnt,
        cc33Id: item.cc33Id,
        b1Cd: item.b1Cd,
        cc32Id: item.cc32Id,
        seq: item.seq,
        ci2Id: item.ci2Id,
        updateKbn: '',
      }
    })
  local.orX0096.concreteCareItemList = concreteCareItemList
  // 具体的内容
  const concreteContentList: ConcreteContentType[] =
    comprehensiveQuestionInfo.concreteContentsInfoList
      .filter((item) => item.b1Cd === Or03250Const.DEFAULT.TAB_ID)
      .map((item): ConcreteContentType => {
        return {
          key: item.cc32Id,
          correspondenceKeys: [],
          content: item.memoKnj,
          cc32Id: item.cc32Id,
          modifiedCnt: item.modifiedCnt,
          juni: item.juni,
          b1Cd: item.b1Cd,
          seq: item.seq,
          cc32Type: item.cc32Type,
          ci1Id: item.ci1Id,
          dmyB4Cd: item.dmyB4Cd,
          b4Cd: item.b4Cd,
          number: '',
          updateKbn: '',
        }
      })
  // 番号リストを作成
  const numberList = comprehensiveQuestionInfo.problemDotNumberInfoList
    .filter((item) => item.b1Cd === Or03250Const.DEFAULT.TAB_ID)
    .map((item) => {
      return {
        cc32Id: item.cc32Id,
        b4Cd: item.b4Cd,
        seq: item.seq,
        modifiedCnt: item.modifiedCnt1,
        cc34Id: item.cc34Id,
        b1Cd: item.b1Cd,
        modifiedCnt1: item.modifiedCnt1,
      }
    })
  if (concreteContentList.length > 0) {
    concreteContentList.forEach((item) => {
      const findedItem = numberList.filter((sItem) => sItem.cc32Id === item.cc32Id)
      if (findedItem) {
        item.correspondenceKeys = findedItem
        item.number = findedItem.map((item) => setCircleNumber(parseInt(item.b4Cd))).join('')
      }
    })
  }

  local.orX0096.concreteContentList = concreteContentList
  /**
   * 問題点リスト
   */
  const questionList = comprehensiveQuestionInfo.problemDotSolutionInfoList.filter(
    (item) => item.b1Cd === Or03250Const.DEFAULT.TAB_ID
  )
  local.orX0096.listSection = local.orX0096.listSection.map((item) => {
    const findedItem = questionList.find((sItem) => sItem.b4Cd === item.b4cd)
    if (findedItem) {
      return {
        ...item,
        cc31Id: findedItem?.cc31Id ?? '',
        isPlanningFlg: findedItem.f1 === '1',
        isHaveIssuesFlg: findedItem.f2 === '1',
        modifiedCnt: findedItem?.modifiedCnt,
      }
    } else {
      return {
        ...item,
        isPlanningFlg: false,
        isHaveIssuesFlg: false,
        cc31Id: '',
        modifiedCnt: '0',
      }
    }
  })
  dotNumberList = comprehensiveQuestionInfo.problemDotNumberInfoList
  localOneway.orX0096Oneway.maxCount = parseInt(comprehensiveQuestionInfo.cc32IdMax)
  // 医療画面
  local.orX0096.thingsToKeepInMindContentsInfo =
    comprehensiveQuestionInfo.thingsToKeepInMindContentsInfo
}

/**
 * tabを制御
 *
 * @param component - コンポーネント
 */
const disableTab = (component: HTMLElement) => {
  const focusableSelectors = [
    'a[href]',
    'area[href]',
    'input',
    'select',
    'textarea',
    'button',
    'iframe',
    '[tabindex]',
    '[contenteditable]',
  ]

  const focusables = component.querySelectorAll(focusableSelectors.join(','))
  focusables.forEach((el) => {
    el.setAttribute('tabindex', '-1')
  })
}

/**
 * APIリクエストパラメータ作成
 *
 * @param request - リクエスト
 */
const createRequest = (request: string | undefined) => {
  if (!request) return '0'
  if (request === '') {
    return '0'
  }
  return request
}

/**
 * 保存処理
 */
const _userSave = async () => {
  // 保存前のチェック
  if (
    !_isEdit.value &&
    screenUpdateKbn.value !== UPDATE_KBN.DELETE &&
    screenUpdateKbn.value !== UPDATE_KBN.CREATE
  ) {
    setShowDialog(t('message.i-cmn-21800'))
    return
  }
  // 共通情報.e-文書法対象機能の電子ファイル保存設定区分が「true：適用する」の場合 Todo
  if (!systemCommonsStore) return
  try {
    isLoading.value = true
    // 子コンポーネントからデータを一括取得する
    const childrenTableCpBinds = getChildCpBinds(props.uniqueCpId, {
      [Or34069Const.CP_ID(0)]: { cpPath: Or34069Const.CP_ID(0), twoWayFlg: true },
      [OrX0096Const.CP_ID(1)]: { cpPath: OrX0096Const.CP_ID(1), twoWayFlg: true },
    })
    // 要介護者などの健康上や生活上の問題点及び解決すべき課題等を取得する
    const careRecipientHealthAndLifeIssues = childrenTableCpBinds[OrX0096Const.CP_ID(1)].twoWayBind
      ?.value as OrX0096Type
    const inputData: AssessmentComprehensiveExcretionUpdateInEntity = {
      cc1Id: createRequest(local.commonInfo.cc1Id),
      sc1Id: createRequest(local.commonInfo.sc1Id),
      houjinId: createRequest(local.commonInfo.houjinId),
      shisetuId: local.commonInfo.shisetuId ?? '',
      svJigyoId: local.commonInfo.svJigyoId ?? '',
      userId: local.commonInfo.userId ?? '',
      syubetsuId: local.commonInfo.syubetsuId ?? '2',
      createYmd: local.commonInfo.createYmd ?? '',
      updateKbn:
        screenUpdateKbn.value === UPDATE_KBN.NONE ? UPDATE_KBN.UPDATE : screenUpdateKbn.value,
      historyUpdateKbn:
        local.commonInfo.historyUpdateKbn === UPDATE_KBN.NONE
          ? UPDATE_KBN.UPDATE
          : (local.commonInfo.historyUpdateKbn ?? ''),
      historyModifiedCnt: createRequest(local.commonInfo.historyModifiedCnt),
      shokuId: '1',
      typeId: '1',
      subInfoExcretion: {
        ...handleCareItems(['offerValue', 'familyValue', 'planValue']),
        ...handleCareLocationItems(['locationValue']),
        modifiedCnt: updateNum === '' ? '0' : updateNum,
      } as subInfoExcretion,
    }

    // 問題点解決情報リスト
    const issuseList: AssessmentComprehensiveExcretionUpdateInEntity['problemDotSolutionInfoList'] =
      careRecipientHealthAndLifeIssues.listSection.map((item) => {
        return {
          cc31Id: item.cc31Id,
          b1Cd: '1',
          b4Cd: item.b4cd,
          f1: item.isPlanningFlg ? '1' : '0',
          f2: item.isHaveIssuesFlg ? '1' : '0',
          modifiedCnt: item.modifiedCnt,
        }
      })
    inputData.problemDotSolutionInfoList = issuseList
    // 具体内容情報リスト
    const concreteContentsInfoList: AssessmentComprehensiveExcretionUpdateInEntity['concreteContentsInfoList'] =
      careRecipientHealthAndLifeIssues.concreteContentList?.map((item, index) => {
        return {
          // 更新区分チェック
          cc32Id: item.updateKbn === UPDATE_KBN.CREATE ? '0' : item.cc32Id,
          // 更新区分チェック、新規の場合は、画面記録IDを設定する
          cc32IdRecord: item.updateKbn === UPDATE_KBN.CREATE ? item.cc32Id : '',
          b1Cd: '1',
          memoKnj: item.content,
          seq: (index + 1).toString(),
          juni: item.juni,
          cc32Type: item.cc32Type,
          ci1Id: item.ci1Id,
          dmyB4Cd: item.correspondenceKeys.map((item) => {
            return {
              b4Cd: item.b4Cd,
            }
          }),
          updateKbn: item.updateKbn === UPDATE_KBN.NONE ? UPDATE_KBN.UPDATE : item.updateKbn,
          modifiedCnt: item.modifiedCnt,
        }
      })
    inputData.concreteContentsInfoList = concreteContentsInfoList

    // ケア情報リスト
    const careItemList: AssessmentComprehensiveExcretionUpdateInEntity['careItemList'] =
      careRecipientHealthAndLifeIssues.concreteCareItemList?.map((item, index) => {
        return {
          // 該当情報が新規の場合、画面に表示したIDを削除する
          cc33Id: item.updateKbn === UPDATE_KBN.CREATE ? '0' : item.cc33Id,
          b1Cd: '1',
          memoKnj: item.content,
          cc32Id: item.cc32Id,
          seq: (index + 1).toString(),
          ci2Id: item.ci2Id,
          dmyCc32Id: item.dmyCc32Id,
          updateKbn: item.updateKbn === UPDATE_KBN.NONE ? UPDATE_KBN.UPDATE : item.updateKbn,
          modifiedCnt: item.modifiedCnt,
        }
      })
    inputData.careItemList = careItemList
    // 問題点番号リスト
    if (screenUpdateKbn.value !== UPDATE_KBN.CREATE) {
      inputData.problemDotNumberInfoList = dotNumberList
    } else {
      inputData.problemDotNumberInfoList = undefined
    }

    // 医療画面は留意事項内容情報を inputData に追加する

    const resData: assessmentComprehensiveExcretionUpdateOutEntity = await ScreenRepository.update(
      'assessmentComprehensiveExcretionUpdate',
      inputData
    )
    if (resData.data) {
      // 保存処理返却値を親画面Piniaに設定する
      TeX0008Logic.data.set({
        uniqueCpId: props.parentUniqueCpId,
        value: {
          ...local.commonInfo,
          childrenScreenApiResult: {
            sc1Id: resData.data.sc1Id,
            cc1Id: resData.data.cc1Id,
          },
        },
      })
      setTeX0008State({ saveCompletedState: true })
      // 更新区分クリア
      screenUpdateKbn.value = UPDATE_KBN.NONE
    }
  } finally {
    isLoading.value = false
  }
}

/**
 * データの保存処理
 *
 * @param arr - 操作したデータ
 */
function handleCareItems(arr: string[]) {
  const obj: Record<string, unknown> = {}
  local.or34096.careItems.forEach((item) => {
    item.careLabel.forEach((itm) => {
      itm.valueType?.forEach((i: string, x: number) => {
        if (x < arr.length) {
          // 断言处理
          const key = arr[x] as keyof typeof itm
          if (key in itm) {
            obj[i] = (itm[key] as Mo00040Type).modelValue
          } else {
            obj[i] = itm.inputContent?.value
          }
        } else {
          obj[i] = itm.inputContent?.value
        }
      })
    })
  })
  return obj
}
/**
 * データの保存処理
 *
 * @param arr - 操作したデータ
 */
function handleCareLocationItems(arr: string[]) {
  const obj: Record<string, unknown> = {}
  local.or34096.careLocationItems.forEach((item) => {
    item.careLocationLabel.forEach((itm) => {
      itm.valueType?.forEach((i: string, x: number) => {
        if (x < arr.length) {
          const key = arr[x] as keyof typeof itm
          if (key in itm) {
            obj[i] = (itm[key] as Mo00040Type).modelValue
          } else {
            obj[i] = itm.inputContent?.value
          }
        } else {
          obj[i] = itm.inputContent?.value
        }
      })
    })
  })
  return obj
}

/**
 * 削除処理を行う
 */
const userDelete = () => {
  // 更新区分を D に設定する
  screenUpdateKbn.value = UPDATE_KBN.DELETE
  // テーブルボディーを非表示にする
  localOneway.or34069Oneway.showTableBodyFlg = false
  localOneway.orX0096Oneway.tableDisplayFlg = false
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no)
 */

const setShowDialog = (paramDialogText: string) => {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })
  // 確認ダイアログを閉じたタイミングで結果を返却
}

const reload = async () => {
  // 全表示フラグをtrueにする
  localOneway.or34069Oneway.showTableBodyFlg = true
  localOneway.orX0096Oneway.tableDisplayFlg = true
  // 更新区分クリア
  screenUpdateKbn.value = UPDATE_KBN.NONE
  isLoading.value = true
  await getInitDataInfo()
  await getProblemDotSolutionEtcInfoData()
  isLoading.value = false
}

/**
 * 初期化処理
 */
onMounted(() => {
  initControl()
})

/**************************************************
 * watch関数
 **************************************************/

/** 画面イベント監視 */
watch(
  () => TeX0008Logic.event.get(props.parentUniqueCpId),
  async (newValue) => {
    // 初期情報取得
    getCommonInfo()

    // 本画面ではない場所、処理を中断する
    if (local.commonInfo.activeTabId !== Or03250Const.DEFAULT.TAB_ID) {
      return
    }
    /** 再表示処理 */
    if (newValue?.isRefresh) {
      await reload()
    }
    /** 保存イベント処理 */
    if (newValue?.saveEventFlg) {
      await _userSave()
    }

    /** 削除イベント処理 */
    if (newValue?.deleteEventFlg) {
      userDelete()
    }

    // 新規処理
    if (newValue?.createEventFlg) {
      createNew()
    }

    /** 作成日変更処理 */
    if (newValue?.isCreateDateChanged) {
      return
    }

    /** お気に入りイベント処理 */
    if (newValue?.favoriteEventFlg) {
      return
    }

    /** 複写イベント処理 */
    if (newValue?.copyEventFlg) {
      isLoading.value = true
      await getDuplicateDataInfo()
      await getDuplicateProblemDotSolutionEtcInfoData()
      isLoading.value = false
      return
    }

    /** 印刷イベント処理 */
    if (newValue?.printEventFlg) {
      return
    }

    /** マスタ他イベント処理 */
    if (newValue?.masterEventFlg) {
      return
    }

    /** 優先順位表示フラグ */
    if (newValue?.priorityOrderEventFlg) {
      return
    }
  },
  { deep: true }
)

/**
 * 複写モードイベント監視
 */
watch(
  () => Or59423Logic.event.get(props.parentUniqueCpId + Or59423Const.CP_ID(0)),
  async (newValue) => {
    if (!newValue) return

    // 複写モード共通情報取得
    getDuplicateCommonInfo()

    // 本画面ではない場合、処理終了
    if (local.commonInfo.activeTabId !== Or03250Const.DEFAULT.TAB_ID) {
      return
    }

    if (newValue.reloadEvent) {
      isLoading.value = true
      await getInitDataInfo()
      await getProblemDotSolutionEtcInfoData()
      // 全処理済み、タブ変更を禁止する
      if (props.onewayModelValue.screenMode === 'copy') {
        if (componentRef.value) {
          disableTab(componentRef.value)
        }
      }
      isLoading.value = false
    }
  }
)
</script>

<template>
  <div
    ref="componentRef"
    class="or03250Wrapper"
  >
    <v-overlay
      :model-value="isLoading"
      :persistent="false"
      class="align-center justify-center"
      ><v-progress-circular
        indeterminate
        color="primary"
      ></v-progress-circular
    ></v-overlay>
    <c-v-row no-gutters>
      <g-custom-or34069
        v-bind="or34069"
        :oneway-model-value="localOneway.or34069Oneway"
        :model-value="local.or34096"
      />
      <div
        class="w-100 mt-2"
        style="height: 1px; background-color: #cccccc"
      ></div>
      <g-custom-or-x0096
        v-bind="orX0096"
        :oneway-model-value="localOneway.orX0096Oneway"
        :model-value="local.orX0096"
      />
    </c-v-row>
    <g-custom-or-53105
      v-if="showDialogOr53105CksFlg1"
      v-bind="or53105"
    />
    <g-base-or-21814
      v-if="showOr21814DialogFlg"
      v-bind="or21814"
    />
  </div>
</template>

<style scoped lang="scss">
.or03249Wrapper {
  .pageTitle {
    border: 1px rgb(var(--v-theme-black-200)) solid;
    background-color: rgb(var(--v-theme-black-100)) !important;
  }
  .titilLabel {
    font-weight: bold;
    font-size: 16px;
    padding: 8px;
  }
}
</style>
