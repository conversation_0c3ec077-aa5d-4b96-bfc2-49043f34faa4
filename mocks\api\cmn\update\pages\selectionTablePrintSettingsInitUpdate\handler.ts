import { HttpResponse } from 'msw'
import defaultData from './data/default.json'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { ISelectionTablePrintSettingsInitUpdateInEntity } from '~/repositories/cmn/entities/SelectionTablePrintSettingsEntity.ts'
/**
 * GUI00844_印刷設定
 *
 * @description
 * GUI00844_印刷設定初期情報データを返却する。
 * dataName："selectionTablePrintSettingsInitUpdate"
 */
export function handler(inEntity: ISelectionTablePrintSettingsInitUpdateInEntity) {
  const responceJson: BaseResponseBody = {
    statusCode: 'success',
    data: {
      ...defaultData,
    },
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
