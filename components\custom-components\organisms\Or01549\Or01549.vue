<script setup lang="ts">
/**
 * Or01549_（予防基本）コンテンツエリア
 * GUI01067_基本情報
 *
 * @description
 * （予防基本）コンテンツエリア
 *
 *
 * <AUTHOR> HOANG SY TOAN
 */
import { isUndefined } from 'lodash'
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { OrD2002Logic } from '../OrD2002/OrD2002.logic'
import { Or31659Const } from '../Or31659/Or31659.constants'
import { Or31659Logic } from '../Or31659/Or31659.logic'
import { OrX0008Const } from '../OrX0008/OrX0008.constants'
import { OrX0009Const } from '../OrX0009/OrX0009.constants'
import { OrX0009Logic } from '../OrX0009/OrX0009.logic'
import { OrX0010Const } from '../OrX0010/OrX0010.constants'
import { OrD2002Const } from '../OrD2002/OrD2002.constants'
import { OrX0010Logic } from '../OrX0010/OrX0010.logic'
import { Or08207Const } from '../Or08207/Or08207.constants'
import { OrX0008Logic } from '../OrX0008/OrX0008.logic'
import type { HistorySelectTableDataItem } from '../Or10929/Or10929.type'
import type { BasicInfo } from '../Or08207/Or08207.type'
import { Or31728Logic } from '../Or31728/Or31728.logic'
import { Or31728Const } from '../Or31728/Or31728.constants'
import { Or10443Const } from '../Or10443/Or10443.constants'
import { Or10443Logic } from '../Or10443/Or10443.logic'
import { Or01549Const } from './Or01549.constants'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or00249Const } from '~/components/base-components/organisms/Or00249/Or00249.constants'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import type { Or21815StateType } from '~/components/base-components/organisms/Or21815/Or21815.type'
import { OrHeadLineConst } from '~/components/base-components/organisms/OrHeadLine/OrHeadLine.constants'
import { Or27631Const } from '~/components/custom-components/organisms/Or27631/Or27631.constants'
import { Or27631Logic } from '~/components/custom-components/organisms/Or27631/Or27631.logic'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { OrHeadLineType } from '~/types/business/generator-components/OrHeadLineType'
import type { OrX0006OnewayType } from '~/types/cmn/business/components/OrX0006Type'
import type { Or31659OnewayType } from '~/types/cmn/business/components/Or31659Type'
import type { OrX0008OnewayType } from '~/types/cmn/business/components/OrX0008Type'
import type { OrX0009OnewayType } from '~/types/cmn/business/components/OrX0009Type'
import type { OrX0010OnewayType } from '~/types/cmn/business/components/OrX0010Type'
import type { Or10443OnewayType } from '~/types/cmn/business/components/Or10443Type'
import type { Or27631Type } from '~/types/cmn/business/components/Or27631Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import {
  useScreenInitFlg,
  useScreenStore,
  useScreenTwoWayBind,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'

import type { PlanCreateDataType } from '~/types/PlanCreateDataType'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { OrX0002OnewayType } from '~/types/cmn/business/components/OrX0002Type'

import type { PlanTargetPeriodDataType } from '~/types/cmn/PlanTargetPeriodDataType'
import type { Mo00020OnewayType, Mo00020Type } from '~/types/business/components/Mo00020Type'
import { CustomClass } from '~/types/CustomClassType'
import type {
  preventionBasicInitialInfoSelectInEntity,
  preventionBasicInitialInfoSelectOutEntity,
} from '~/repositories/cmn/entities/preventionBasicInitialInfoSelectEntity'
import type {
  PlanPeriodInfo,
  preventionBasicPeriodSelectInEntity,
  preventionBasicPeriodSelectOutEntity,
} from '~/repositories/cmn/entities/preventionBasicPeriodSelectEntity'
import type {
  PreventiveHistoryInfo,
  preventionBasicHistorySelectInEntity,
  preventionBasicHistorySelectOutEntity,
} from '~/repositories/cmn/entities/preventionBasicHistorySelectEntity'
import type { Mo01343Type } from '~/types/business/components/Mo01343Type'
import type {
  preventionBasicDetailInfoSelectInEntity,
  preventionBasicDetailInfoSelectOutEntity,
  PreventiveCareInfo,
  MedicalHistoryInfo,
  FamilyStructureInfo,
} from '~/repositories/cmn/entities/preventionBasicDetailInfoSelectEntity'
import type {
  Gui01067BasicInfoListInEntity,
  PreventionBasicDetailInfoUpdateInEntity,
} from '~/repositories/cmn/entities/PreventionBasicDetailInfoUpdateEntity'
import type {
  HistorySelectInfoType,
  Or10929Type,
  Or10929OnewayType,
} from '~/types/cmn/business/components/Or10929Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { Or10929Logic } from '~/components/custom-components/organisms/Or10929/Or10929.logic'
import { Or10929Const } from '~/components/custom-components/organisms/Or10929/Or10929.constants'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { useUserListInfo } from '~/utils/useUserListInfo'
import type { Or31728Type } from '~/types/cmn/business/components/Or31728Type'
import { useJigyoList } from '~/utils/useJigyoList'
import { hasPrintAuth } from '~/utils/useCmnAuthz'
import { Or00248Logic } from '~/components/base-components/organisms/Or00248/Or00248.logic'
const { syscomUserListFunc } = useUserListInfo()

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
}
const props = defineProps<Props>()

const isPermissionPrint = ref<boolean>()

// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
const { jigyoListWatch } = useJigyoList()

const or08207 = ref({ uniqueCpId: '' })
const or11871 = ref({ uniqueCpId: '' })
const or00248 = ref({ uniqueCpId: '' })
const or00249 = ref({ uniqueCpId: '' })
const orHeadLine = ref({ uniqueCpId: '' })
const Or31659 = ref({ uniqueCpId: '' })
const orX0008_1 = ref({ uniqueCpId: '' })
const orX0009_1 = ref({ uniqueCpId: '' })
const orX0010_1 = ref({ uniqueCpId: '' })
const or21815 = ref({ uniqueCpId: Or21815Const.CP_ID(1) })
const orD2002_1 = ref({ uniqueCpId: '' })
const or21814_1 = ref({ uniqueCpId: Or21814Const.CP_ID(1) })
const or21813 = ref({ uniqueCpId: Or21813Const.CP_ID(1) })
const or10929 = ref({ uniqueCpId: '' })
const or31728 = ref({ uniqueCpId: '' })
const or10443 = ref({ uniqueCpId: '' })
const or41179 = ref({ uniqueCpId: '' })

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  return useScreenStore().getCpNavControl(or08207.value.uniqueCpId)
})

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const commonInfo = {
  baseDate: '',
  loginUserName: '管理者　太郎',
  sc1Id: '',
  assessmentId: '',
  shokuId: '',
  surveyAssessmentKind: '',
  showMessageFlg: 1,
  serviceType: '50010',
  userId: systemCommonsStore.getUserId,
  kycFlg: systemCommonsStore.getFunctionCategory,
  houjinId: systemCommonsStore.getHoujinId,
  shisetsuId: systemCommonsStore.getShisetuId,
  svJigyoId: systemCommonsStore.getSvJigyoId,
}

// 選択中利用者ID
const userId = ref('')

// 計画期間表示フラグ
const planPeriodShow = ref<boolean>(true)

// 履歴表示フラグ
const historyShow = ref<boolean>(true)

// 作成者表示フラグ
const authorShow = ref<boolean>(true)

// 相談日表示フラグ
const consultationDateShow = ref<boolean>(true)

// 入力フォーム表示フラグ
const inputBoomShow = ref<boolean>(true)

// 削除フラグ
const deleteFlag = ref<boolean>(false)

// データ再取得フラグ
const retrieveCmCp1DataFlg = ref<boolean>(false)

// 地域キー
const regionKey = ref(Or00248Const.DEFAULT.REGION_KEY)

const local = reactive({
  consultationDate: {
    value: systemCommonsStore.getSystemDate,
    mo01343: {
      value: systemCommonsStore.getSystemDate,
      mo00024: {
        isOpen: false,
      } as Mo00024Type,
    } as Mo01343Type,
  } as Mo00020Type,

  mo00043: {
    id: '',
  } as Mo00043Type,

  flag: {
    periodManage: '0',

    periodManageRegistration: false,
  },

  addBtnState: false,

  officeId: '1',

  planPeriodId: 1,

  planPeriod: [] as PlanPeriodInfo[],

  planPeriodIndex: 0,

  totalCountPlanPeriod: 0,

  historyId: 1,

  history: [] as PreventiveHistoryInfo[],

  historyIndex: 0,

  totalCountHistory: 0,

  degreeAbility: 5,

  houjinId: '',

  shisetuId: '',

  userId: '**********',

  svJigyoId: systemCommonsStore.getSvJigyoId,

  raiId: '',

  kijunbiYmd: '',

  sakuseiId: '',
  historyModifiedCnt: '',
  shisetsuId: '1',
  itkJigyoId: '1',
  shubetsuId: '1',
  sc1Id: '1',
  ks51Id: '1',
  khn11Id: '1',
  /** 予防基本複写詳細_介護予防リスト */
  kaigoList: [] as PreventiveCareInfo[],
  /** 予防基本複写詳細_病歴と経過リスト */
  byourekiList: [] as MedicalHistoryInfo[],
  /** 家族構成図リスト */
  kozokuList: [] as FamilyStructureInfo[],
})

const defaultOneway = reactive({
  mo00043Oneway: {
    tabItems: [
      {
        id: 'basicInfo',
        title: t('label.basic_infomation'),
        tooltipText: t('label.basic_infomation'),
        tooltipLocation: 'bottom',
      },
      {
        id: 'carePrevention',
        title: t('label.matters_related_to_care_prevention'),
        tooltipText: t('label.matters_related_to_care_prevention'),
        tooltipLocation: 'bottom',
      },
      {
        id: 'currentMedical',
        title: t('label.current_medical_history_past_medical_history_and_progress'),
        tooltipText: t('label.current_medical_history_past_medical_history_and_progress'),
        tooltipLocation: 'bottom',
      },
      {
        id: 'currentService',
        title: t('label.services_currently_being_used'),
        tooltipText: t('label.services_currently_being_used'),
        tooltipLocation: 'bottom',
      },
    ],
  } as Mo00043OnewayType,
  mo00611Oneway: {
    name: 'consentInfoInputIconBtn',
    btnLabel: t('btn.consent-info'),
    width: '70px',
  } as Mo00611OnewayType,
  //事業所
  orX0006Oneway: {} as OrX0006OnewayType,
  //期間データ
  Or31659Oneway: { planTargetPeriodData: { currentIndex: 0, totalCount: 0 } } as Or31659OnewayType,
  //履歴
  orX0008Oneway: {} as OrX0008OnewayType,
  //作成者
  orX0009Oneway: {
    createData: {} as PlanCreateDataType,
    deleteFlg: false,
    isDisabled: false,
  } as OrX0009OnewayType,
  //作成日
  orX0010Oneway: { deleteFlg: false, isDisabled: false } as OrX0010OnewayType,
})

const localOneway = reactive({
  mo00043Oneway: {
    ...defaultOneway.mo00043Oneway,
  } as Mo00043OnewayType,
  mo00611Oneway: {
    ...defaultOneway.mo00611Oneway,
  } as Mo00611OnewayType,
  orX0006Oneway: {
    ...defaultOneway.orX0006Oneway,
  } as OrX0006OnewayType,
  Or31659Oneway: {
    ...defaultOneway.Or31659Oneway,
  } as Or31659OnewayType,
  orX0008Oneway: {
    ...defaultOneway.orX0008Oneway,
  } as OrX0008OnewayType,
  orX0009Oneway: {
    ...defaultOneway.orX0009Oneway,
  } as OrX0009OnewayType,
  orX0010Oneway: {
    ...defaultOneway.orX0010Oneway,
  } as OrX0010OnewayType,
  //確認ダイアログ
  orX0002DialogOneway: {
    message: t('message.i-cmn-11276'),
    showYesBtn: true,
    showNoBtn: false,
    mo00024Oneway: {
      class: 'mr-1',
      name: '',
      width: '360px',
    } as Mo00024OnewayType,
  } as OrX0002OnewayType,
  updateConfirm: {
    emitType: '',
    mo00024: {
      isOpen: false,
    } as Mo00024Type,
  },
  consultationDateOneway: {
    itemLabel: t('label.consultation-date'),
    width: '170',
    isRequired: true,
    isVerticalLabel: false,
    showItemLabel: true,
    showSelectArrow: false,
    clearable: true,
    customClass: new CustomClass({ labelClass: 'ma-1' }),
  } as Mo00020OnewayType,
  or10929Oneway: {
    historySelectInfo: {} as HistorySelectInfoType,
  } as Or10929OnewayType,
})

const isDisabledCopyBtn = ref<boolean>(false)

const or10929Type = ref<Or10929Type>({
  historySelectDataList: [],
})

const Or10443OnewayModel = ref<Or10443OnewayType>({
  userId: local.userId ?? '',
  sectionName: '',
  choIndex: '1',
  kikanFlg: local.flag.periodManage,
  historyId: '',
  careManagerInChargeSettingsFlag: 2,
  svJigyoId: '',
  svJigyoIdList: [''],
  tantoShokuId: '0',
  khn11Id: local.khn11Id,
})

// ラベル単方向モデル
const mo01338Oneway: Mo01338OnewayType = {
  value: Or01549Const.NOTIIFICATION_REGISTER_TIME,
  customClass: new CustomClass({ outerClass: 'ml-0 mr-1', itemClass: 'notice' }),
  itemLabelFontWeight: 'bold',
}

const historyUpdateKbn = ref<string>(Or01549Const.HISTORY_UPDATE_KBN_UPDATE) // 'D' khi delete, 'C' khi create

/**************************************************
 * コンポーネント固有処理
 **************************************************/
const isInit = useScreenInitFlg()

onMounted(async () => {
  // 一覧情報取得関数を呼び出し
  await syscomUserListFunc()
  if (systemCommonsStore.getUserSelectSelfId) {
    Or41179Logic.state.set({
      uniqueCpId: or41179.value.uniqueCpId,
      state: {
        searchCriteria: {
          selfId: systemCommonsStore.getUserSelectSelfId(),
        },
      },
    })
  }
  // 利用者を全選択です。
  await nextTick(() => {
    useScreenTwoWayBind<OrHeadLineType>({
      cpId: OrHeadLineConst.CP_ID,
      uniqueCpId: orHeadLine.value.uniqueCpId,
    }).setValue({ value: Or01549Const.STR_ALL })
  })

  await initOr01549()
  if (isInit) {
    // 確認ダイアログを初期化
    OrD2002Logic.state.set({
      uniqueCpId: orD2002_1.value.uniqueCpId,
      state: {
        mainMessage: t('message.i-cmn-10430'),
      },
    })
  }
  setupOr00248()
})
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or00248Const.CP_ID(1)]: or00248.value,
  [Or11871Const.CP_ID]: or11871.value,
  [Or31659Const.CP_ID(1)]: Or31659.value,
  [OrX0008Const.CP_ID(1)]: orX0008_1.value,
  [OrX0009Const.CP_ID(1)]: orX0009_1.value,
  [OrX0010Const.CP_ID(1)]: orX0010_1.value,
  [Or21813Const.CP_ID(1)]: or21813.value,
  [Or21815Const.CP_ID(1)]: or21815.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [OrD2002Const.CP_ID(1)]: orD2002_1.value,
  [Or10929Const.CP_ID(1)]: or10929.value,
  [Or08207Const.CP_ID(1)]: or08207.value,
  [Or31728Const.CP_ID(1)]: or31728.value,
  [Or10443Const.CP_ID(1)]: or10443.value,
  [Or41179Const.CP_ID(1)]: or41179.value,
})

useSetupChildProps(or00248.value.uniqueCpId, {
  [OrHeadLineConst.CP_ID]: orHeadLine.value,
  [Or00249Const.CP_ID(1)]: or00249.value,
})

// ★事業所選択監視関数を実行
jigyoListWatch(or41179.value.uniqueCpId, callbackFuncJigyo)
/**
 * 事業所選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newJigyoId - 変更後の事業者ID
 */
function callbackFuncJigyo(newJigyoId: string) {
  if (newJigyoId !== '') {
    const jigyoInfoList = Or41179Logic.state.get(or41179.value.uniqueCpId)?.jigyoInfoList
    const _jigyoInfo = jigyoInfoList?.find((jigyoInfo) => jigyoInfo.svJigyoId === newJigyoId)
  }
}

Or11871Logic.state.set({
  uniqueCpId: or11871.value.uniqueCpId,
  state: {
    screenTitleLabel: t('label.prevention-basic'),
    showFavorite: true,
    showViewSelect: false,
    showCreateBtn: true,
    showCreateMenuCopy: false,
    showPrintBtn: true,
    showOptionMenuBtn: true,
    viewSelectItems: [],
    showMasterBtn: true,
    showOptionMenuDelete: false,
    showSaveBtn: true,
    tooltipTextSaveBtn: t('tooltip.save'),
    tooltipTextCreateBtn: t('tooltip.care-plan2-new-btn'),
    tooltipTextPrintBtn: t('tooltip.care-plan2-print-setting-btn'),
    tooltipTextMasterBtn: t('tooltip.regular-master-icon'),
    tooltipTextOptionMenuBtn: '',
  },
})

const or27631 = ref({ uniqueCpId: Or27631Const.CP_ID(1) })

const or27631Type = ref<Or27631Type>({
  consentDate: { value: '' },
  consentName: { value: '' },
})

const or31728Type = ref<Or31728Type>({
  /**
   * サービス種類
   */
  serviceType: '基本情報',
  /**
   * 期間管理フラグ
   */
  periodManagementFlg: '管理する',
})

// ダイアログ表示フラグ
const showDialogOr27631 = computed(() => {
  return Or27631Logic.state.get(or27631.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr31728 = computed(() => {
  // Or31728のダイアログ開閉状態
  const state = Or31728Logic.state.get(or31728.value.uniqueCpId) as { isOpen?: boolean }
  return state?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr10443 = computed(() => {
  // Or10443のダイアログ開閉状態
  const state = Or10443Logic.state.get(or10443.value.uniqueCpId) as { isOpen?: boolean }
  return state?.isOpen ?? false
})

/**
 * ユーザーがツールバーのボタンを押したタイミングを追跡する
 */
watch(
  () => Or11871Logic.event.get(or11871.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }

    if (newValue.favoriteEventFlg) {
      favoriteIconClick()
      setOr11871Event({ favoriteEventFlg: false })
    }

    if (newValue.saveEventFlg) {
      historyUpdateKbn.value = Or01549Const.HISTORY_UPDATE_KBN_UPDATE
      await _save()
      setOr11871Event({ saveEventFlg: false })
    }

    if (newValue.createEventFlg) {
      // 実行無効制御
      // 新規ボタンが押下された場合、新規作成処理を実行する
      await handleClickCreate()
      setOr11871Event({ createEventFlg: false })
    }

    if (newValue.printEventFlg) {
      await _print()
      setOr11871Event({ printEventFlg: false })
    }

    if (newValue.masterEventFlg) {
      await _master()
      setOr11871Event({ masterEventFlg: false })
    }
  }
)

/**
 * AC014_「計画対象期間-前へアイコンボタン」押下
 * AC015_「計画対象期間-次へアイコンボタン」押下
 * 計画期間変更の監視
 */
watch(
  () => Or31659Logic.data.get(Or31659.value.uniqueCpId),
  (newValue) => {
    if (isUndefined(newValue)) {
      return
    }

    const planID = newValue.planTargetPeriodId
    const planUpdateFlg = newValue.PlanTargetPeriodUpdateFlg

    if (planUpdateFlg === undefined || planUpdateFlg === '') {
      return
    }

    if (planUpdateFlg === '0') {
      void planTargetPeriodIconClick()
    } else if (planUpdateFlg === '1') {
      local.planPeriodId = planID - 1

      Or31659Logic.data.set({
        uniqueCpId: Or31659.value.uniqueCpId,
        value: {
          planTargetPeriodId: local.planPeriodId,
          PlanTargetPeriodUpdateFlg: '',
        },
      })

      retrieveCmCp1DataFlg.value = true
    } else if (planUpdateFlg === '2') {
      local.planPeriodId = planID + 1

      Or31659Logic.data.set({
        uniqueCpId: Or31659.value.uniqueCpId,
        value: {
          planTargetPeriodId: local.planPeriodId,
          PlanTargetPeriodUpdateFlg: '',
        },
      })

      retrieveCmCp1DataFlg.value = true
    } else if (planUpdateFlg === '3') {
      local.planPeriodId = Or31659Logic.data.get(Or31659.value.uniqueCpId)!.planTargetPeriodId

      Or31659Logic.data.set({
        uniqueCpId: Or31659.value.uniqueCpId,
        value: {
          planTargetPeriodId: local.planPeriodId,
          PlanTargetPeriodUpdateFlg: '',
        },
      })

      retrieveCmCp1DataFlg.value = true
    }
  },
  { deep: true }
)

watch(
  () => OrX0008Logic.data.get(orX0008_1.value.uniqueCpId),
  (newValue) => {
    if (isUndefined(newValue)) {
      return
    }
    const createId = Number(newValue.createId)
    const createUpateFlg = newValue.createUpateFlg

    if (createUpateFlg === undefined || createUpateFlg === '') {
      return
    }

    if (createUpateFlg === '0') {
      Or10929Logic.state.set({
        uniqueCpId: or10929.value.uniqueCpId,
        state: { isOpen: true },
      })
    } else if (createUpateFlg === '1') {
      if (createId <= 1) {
        return
      }

      local.historyId = createId - 1

      OrX0008Logic.data.set({
        uniqueCpId: orX0008_1.value.uniqueCpId,
        value: {
          createId: local.historyId.toString(),
          createUpateFlg: '',
        },
      })

      retrieveCmCp1DataFlg.value = true
    } else if (createUpateFlg === '2') {
      local.historyId = createId + 1

      OrX0008Logic.data.set({
        uniqueCpId: orX0008_1.value.uniqueCpId,
        value: {
          createId: local.historyId.toString(),
          createUpateFlg: '',
        },
      })

      retrieveCmCp1DataFlg.value = true
    } else if (createUpateFlg === '3') {
      local.historyId = Number(OrX0008Logic.data.get(orX0008_1.value.uniqueCpId)!.createId)

      OrX0008Logic.data.set({
        uniqueCpId: orX0008_1.value.uniqueCpId,
        value: {
          createId: local.historyId.toString(),
          createUpateFlg: '',
        },
      })

      retrieveCmCp1DataFlg.value = true
    }
  },
  { deep: true }
)

/**
 * 利用者一覧における利用者の変更を監視
 */
watch(
  () => systemCommonsStore.getUserSelectSelfId(regionKey.value),
  async (newUserId, oldUserId) => {
    if (newUserId === undefined || newUserId === oldUserId) {
      return
    }

    // Check if there are unsaved changes
    if (isEdit.value) {
      const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
        dialogTitle: t('label.confirm'),
        dialogText: t('message.i-cmn-10430'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'destroy1',
        secondBtnLabel: t('btn.no'),
        thirdBtnType: 'normal3',
        thirdBtnLabel: t('btn.cancel'),
      })

      switch (dialogResult) {
        case Or01549Const.DIALOG_RESULT_YES:
          // 保存してユーザーを切り替える
          await _save()
          await switchToNewUser(newUserId)
          break
        case Or01549Const.DIALOG_RESULT_NO:
          // 保存せずに切り替える
          await switchToNewUser(newUserId)
          break
        case Or01549Const.DIALOG_RESULT_CANCEL:
          // ユーザー変更をキャンセルし、選択を元に戻す
          await revertUserSelection(oldUserId)
          return
      }
    } else {
      // 保存されていない変更はありません。直接切り替えます
      await switchToNewUser(newUserId)
    }
  },
  { immediate: false }
)

watch(
  () => retrieveCmCp1DataFlg.value,
  async (newValue) => {
    if (newValue) {
      await getCommonData()

      await getTabsData()
    }
  }
)
/**
 * 方法
 */

/**
 * 新しい利用者に切り替える
 *
 * @param newUserId - 切り替える利用者ID
 */
const switchToNewUser = async (newUserId: string) => {
  // ローカルの利用者データを更新
  local.userId = newUserId
  userId.value = newUserId

  // 新しい利用者のデータを再取得
  await getCommonData()
  await getTabsData()
}

/**
 * キャンセル時に利用者選択を元に戻す
 *
 * @param oldUserId - 元に戻す利用者ID
 */
const revertUserSelection = async (oldUserId: string | undefined) => {
  if (oldUserId) {
    // 無限ループを防ぐため、一時的にwatchを無効化
    await nextTick(() => {
      systemCommonsStore.setUserSelectSelfId(oldUserId, regionKey.value)
    })
  }
}

const getCommonData = async () => {
  await getBasicInitialInfoSelect()

  await getPreventionBasicPeriodSelect()

  await getPreventionBasicHistorySelect()
}

const getTabsData = async () => {
  await getPreventionBasicDetailInfoSelect()
}
/**
 * タブ切り替え時の処理（表示順序の復元）
 */
async function tabsClick() {
  if (isEdit.value) {
    // AC004-2と同じ
    const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    await handleDialogResult(dialogResult)
  }
}

/**
 *  AC001_初期表示
 */

const initOr01549 = async () => {
  if (commonInfo.showMessageFlg === 1 && commonInfo.serviceType === '50010') {
    // 以下のメッセージを表示: i.cmn.11276
    localOneway.updateConfirm.mo00024.isOpen = true
  }
  isPermissionPrint.value = await hasPrintAuth(Or01549Const.LINK_AUTH)

  if (!isPermissionPrint.value) {
    Or11871Logic.state.set({
      uniqueCpId: or11871.value.uniqueCpId,
      state: {
        disabledPrintBtn: true,
      },
    })
  }

  await getCommonData()

  await getTabsData()
}

const getBasicInitialInfoSelect = async () => {
  const inputData: preventionBasicInitialInfoSelectInEntity = {
    /** 施設ID */
    shisetuId: local.shisetsuId,

    /** 利用者ID */
    userId: local.userId,

    /** 事業者ID */
    svJigyoId: local.svJigyoId ?? '',

    /** 委託先事業者ID */
    itkJigyoId: local.itkJigyoId,

    /** 種別ID */
    syubetsuId: local.shubetsuId,
  }

  const res: preventionBasicInitialInfoSelectOutEntity = await ScreenRepository.select(
    'preventionBasicInitialInfoSelect',
    inputData
  )
  const resData = res.data
  local.flag.periodManage = resData.kikanFlag
  planPeriodShow.value = resData.kikanFlag === '1'
  // local.flag.periodManage = '0'
  // planPeriodShow.value = false
}

const getPreventionBasicPeriodSelect = async () => {
  const inputData: preventionBasicPeriodSelectInEntity = {
    /** 事業者ID */
    svJigyoId: local.svJigyoId ?? '',

    /** 委託先事業者ID */
    itkJigyoId: local.itkJigyoId,

    /** 施設ID */
    shisetuId: local.shisetsuId,
    /** 利用者ID */
    userId: local.userId,

    /** 種別ID */
    syubetsuId: local.shubetsuId,

    /** 計画対象期間ID */
    sc1Id: local.sc1Id,

    /**
     * 計画期間ページ区分
     *
     * "0:選択している期間ID画面
     *1:選択している期間ID画面の前画面
     *2:選択している期間ID画面の後画面"
     */
    kikanPage: '0',

    /** 期間管理フラグ */
    kikanFlag: local.flag.periodManage,
  }

  const res: preventionBasicPeriodSelectOutEntity = await ScreenRepository.select(
    'preventionBasicPeriodSelect',
    inputData
  )
  const { kikanObj } = res.data
  if (kikanObj.length === 0) {
    // 計画対象期間-ページングを"0 / 0"で表示にする
    localOneway.Or31659Oneway.planTargetPeriodData.currentIndex = 0
    localOneway.Or31659Oneway.planTargetPeriodData.totalCount = 0
    localOneway.Or31659Oneway.planTargetPeriodData.planTargetPeriodId = 0

    isDisabledCopyBtn.value = true
  } else {
    local.totalCountPlanPeriod = kikanObj.length
    local.planPeriodIndex = local.totalCountPlanPeriod - 1
    const firstData = kikanObj[local.planPeriodIndex]

    local.planPeriodId = parseInt(firstData.sc1Id)
    localOneway.Or31659Oneway.planTargetPeriodData = {
      planTargetPeriodId: parseInt(firstData.sc1Id),
      planTargetPeriod: firstData.startYmd + ' ~ ' + firstData.endYmd,
      currentIndex: local.planPeriodIndex + 1,
      totalCount: local.totalCountPlanPeriod,
    } as PlanTargetPeriodDataType

    isDisabledCopyBtn.value = false
  }
}

const getPreventionBasicHistorySelect = async () => {
  const inputData: preventionBasicHistorySelectInEntity = {
    /** 利用者ID */
    userId: local.userId,

    /** 事業者ID */
    svJigyoId: local.svJigyoId ?? '',

    /** 計画対象期間ID */
    sc1Id: local.sc1Id,

    /** 基本情報ID */
    ks51Id: local.ks51Id,

    /** 履歴ページ区分 */
    historyPage: '0',
  }

  const res: preventionBasicHistorySelectOutEntity = await ScreenRepository.select(
    'preventionBasicHistorySelect',
    inputData
  )

  const { historyObj } = res.data
  if (historyObj.length) {
    local.totalCountHistory = historyObj.length
    historyShow.value = true
    const firstHistory = historyObj[local.historyIndex]
    local.historyId = parseInt(firstHistory.shokuId)

    localOneway.orX0008Oneway.createData = {
      createId: String(firstHistory.shokuId),
      createDate: systemCommonsStore.getSystemDate ?? '',
      staffId: '0',
      staffName: systemCommonsStore.getStaffId ?? '',
      currentIndex: local.historyIndex + 1,
      totalCount: local.totalCountHistory,
    }

    localOneway.orX0009Oneway.createData = {
      createId: 0,
      createDate: '',
      staffId: parseInt(firstHistory.shokuId),
      staffName: firstHistory.shokuKnj,
      currentIndex: 1,
      totalCount: 3,
      ks21Id: '',
    } as PlanCreateDataType

    or27631Type.value.consentName.value = firstHistory.doiNameKnj
    or27631Type.value.consentDate.value = firstHistory.doiYmd
    local.consultationDate.value = firstHistory.soudanYmd
  }
}

const getPreventionBasicDetailInfoSelect = async () => {
  const inputData: preventionBasicDetailInfoSelectInEntity = {
    khn11Id: local.khn11Id,
  }

  const res: preventionBasicDetailInfoSelectOutEntity = await ScreenRepository.select(
    'preventionBasicDetailInfoSelect',
    inputData
  )

  if (res.data.kihonList.length) {
    screenStore.setCpTwoWay({
      cpId: Or08207Const.CP_ID(1),
      uniqueCpId: or08207.value.uniqueCpId,
      value: { kihonList: res.data.kihonList[0] },
      isInit: true,
    })
  }

  local.kozokuList = res.data.kozokuList
  local.kaigoList = res.data.kaigoList
  local.byourekiList = res.data.byourekiList
}

/**
 * 画面初期化処理
 */
const setupOr00248 = () => {
  Or00248Logic.state.set({
    uniqueCpId: or00248.value.uniqueCpId,
    state: {
      regionKey: regionKey.value,
      displayUserInfoSectionFlg: true,
    },
  })
}

/**
 * 保存時処理
 */
async function _save() {
  if (!isEdit.value) {
    return
  }
  console.log('Saving data...', historyUpdateKbn.value)

  const updateData: PreventionBasicDetailInfoUpdateInEntity = {
    historyUpdateKbn: historyUpdateKbn.value,
    khn11Id: local.khn11Id,
    sc1Id: local.sc1Id,
    userId: commonInfo.userId,
    kycFlg: commonInfo.kycFlg,
    houjinId: commonInfo.houjinId,
    shisetuId: commonInfo.shisetsuId,
    svJigyoId: commonInfo.svJigyoId ?? '',
    soudanYmd: local.consultationDate.value,
    shokuId: localOneway.orX0009Oneway.createData?.staffId?.toString() ?? '',
    shokuKnj: localOneway.orX0009Oneway.createData?.staffName ?? '',
    doiYmd: or27631Type.value.consentDate.value,
    doiNameKnj: or27631Type.value.consentName.value,
    basicInfoList: useScreenTwoWayBind<{ kihonList?: Gui01067BasicInfoListInEntity[] }>({
      cpId: Or08207Const.CP_ID(1),
      uniqueCpId: or08207.value.uniqueCpId,
    }).refValue.value?.kihonList,
    familyInfoList: local.kozokuList,
    nursingCareInfoList: local.kaigoList,
    oneDayScheduleList: [],
    medicalHistoryList: local.byourekiList,
    modifiedCnt: local.historyModifiedCnt,
  }
  // バックエンドAPIから初期情報取得
  await ScreenRepository.update('preventionBasicDetailInfoUpdate', updateData)

  await getCommonData()
  await getTabsData()
}

/**
 * マスターデータ取得処理
 */
async function _master() {
  if (isEdit.value) {
    const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
      dialogTitle: t('label.confirm'),

      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case Or01549Const.DIALOG_RESULT_YES:
        await _save()
        await getTabsData()
        break
      case Or01549Const.DIALOG_RESULT_NO:
        await getTabsData()
        break
      case Or01549Const.DIALOG_RESULT_CANCEL:
        return
    }
  }
}

/**
 * 削除処理
 */
async function _delete() {
  //AC08

  // 削除時に変数 historyUpdateKbn に値 'D' を代入する
  historyUpdateKbn.value = Or01549Const.HISTORY_UPDATE_KBN_DELETE

  //削除確認（）
  const dialogResult = await openInfoDialog(Or01549Const.INFO_MESSAGE_TYPE_11326)
  switch (dialogResult) {
    case Or01549Const.DIALOG_RESULT_YES:
      // はい：処理継続
      //・以下の項目を非活性にする。
      //成者選択アイコンボタン、相談日、相談日カレンダー
      //・以下のボタンは実行無効にする。
      //新規、複写、印刷、削除

      Or11871Logic.state.set({
        uniqueCpId: or11871.value.uniqueCpId,
        state: {
          disabledCreateBtn: true,
          disabledSaveBtn: true,
          disabledPrintBtn: true,
        },
      })
      localOneway.consultationDateOneway.disabled = true

      OrX0009Logic.state.set({
        uniqueCpId: orX0009_1.value.uniqueCpId,
        state: {
          isDisabled: true,
        },
      })
      //・以下の項目を非表示にする。
      //入力フームのすべて項目
      deleteFlag.value = true
      isDisabledCopyBtn.value = true
      inputBoomShow.value = false

      break
    case Or01549Const.DIALOG_RESULT_NO:
      // いいえ：処理終了
      return
  }
}

/**
 * 印刷設定ダイアログを開く処理
 */
async function _print() {
  if (isEdit.value) {
    const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
      dialogTitle: t('label.confirm'),

      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    switch (dialogResult) {
      case Or01549Const.DIALOG_RESULT_YES:
        await _save()
        // Open print settings dialog
        Or10443Logic.state.set({
          uniqueCpId: or10443.value.uniqueCpId,
          state: { isOpen: true },
        })
        break
      case Or01549Const.DIALOG_RESULT_NO:
        // Open print settings dialog without saving
        Or10443Logic.state.set({
          uniqueCpId: or10443.value.uniqueCpId,
          state: { isOpen: true },
        })
        break
      case Or01549Const.DIALOG_RESULT_CANCEL:
        return
    }
  } else {
    // GUI01079 印刷設定画面をポップアップで起動する。
    Or10443Logic.state.set({
      uniqueCpId: or10443.value.uniqueCpId,
      state: { isOpen: true },
    })
  }
}

/**
 * GUI01071_予防基本複写画面をポップアップで起動する。
 */
function copyBtnClick() {
  // GUI01071_予防基本複写画面をポップアップで起動する。
  Or31728Logic.state.set({
    uniqueCpId: or31728.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * Or27631のダイアログ開閉状態を更新する
 */
function handleClickSI042() {
  // Or27631のダイアログ開閉状態を更新する
  Or27631Logic.state.set({
    uniqueCpId: or27631.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * AC013_「計画対象期間選択アイコンボタン」押下
 */
const planTargetPeriodIconClick = async () => {
  // 画面入力データに変更がある場合
  if (isEdit.value) {
    // AC004-2と同じ
    const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    })

    await handleDialogResult(dialogResult)
  }
}

/**
 * エラーダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openWarningDialog = (uniqueCpId: string, state: Or21815StateType) => {
  Or21815Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(uniqueCpId)

        let result = Or01549Const.DIALOG_RESULT_YES

        if (event?.firstBtnClickFlg) {
          result = Or01549Const.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or01549Const.DIALOG_RESULT_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = Or01549Const.DIALOG_RESULT_CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * ダイアログの結果を処理する
 *
 * @param dialogResult - ダイアログの選択結果
 */
const handleDialogResult = async (dialogResult: unknown) => {
  switch (dialogResult) {
    case Or01549Const.DIALOG_RESULT_YES:
      // 「はい」ボタンの処理
      await _save()
      // 必要に応じて追加処理を続行
      break
    case Or01549Const.DIALOG_RESULT_NO:
      // 「いいえ」ボタンの処理
      // 保存せずに続行
      break
    case Or01549Const.DIALOG_RESULT_CANCEL:
      // 「キャンセル」ボタンの処理
      // 処理を中止して戻る
      return
  }
}

/**
 * 新規ボタン押下時の処理
 */
const handleClickCreate = async () => {
  // 2回目の新規ボタン押下時の処理
  if (local.addBtnState) {
    await openWarningDialog(or21815.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11265', [t('label.prevention-basic')]),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
    return
  }

  local.addBtnState = true
  // Gán giá trị 'C' cho biến historyUpdateKbn khi tạo mới
  historyUpdateKbn.value = Or01549Const.HISTORY_UPDATE_KBN_CREATE
  // 期間管理フラグが「1:管理する」の場合
  if (local.flag.periodManage === '1') {
    // 計画期間情報の期間総件数が0（期間なし）の場合
    if (local.totalCountHistory === 0) {
      const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
        // ダイアログタイトル
        dialogTitle: t('label.error'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-11300'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.ok'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      })
      // 「はい」選択時
      if (Or01549Const.DIALOG_RESULT_YES === dialogResult) {
        // AC013を実行
        Or11871Logic.state.set({
          uniqueCpId: or11871.value.uniqueCpId,
          state: {
            disabledSaveBtn: false,
            disabledPrintBtn: false,
            disabledOptionMenuDelete: false,
          },
        })
        OrX0010Logic.state.set({
          uniqueCpId: orX0010_1.value.uniqueCpId,
          state: {
            isDisabled: false,
          },
        })
        // 入力フォームのすべて項目を表示
        deleteFlag.value = false
      }
    }
  } else {
    if (isEdit.value) {
      const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
        dialogTitle: t('label.confirm'),

        dialogText: t('message.i-cmn-10430'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'destroy1',
        secondBtnLabel: t('btn.no'),
        thirdBtnType: 'normal3',
        thirdBtnLabel: t('btn.cancel'),
      })

      switch (dialogResult) {
        case 'yes':
          await _save()
          break
        case 'no':
          break
        case 'cancel':
          return
      }
    }
  }
}

/**
 * メニューイベントを設定する
 *
 * @param event - イベント
 */
const setOr11871Event = (event: Record<string, boolean>) => {
  Or11871Logic.event.set({
    uniqueCpId: or11871.value.uniqueCpId,
    events: event,
  })
}

/**
 * 編集破棄ダイアログ表示
 *
 * @param msgType - message TYPE
 *
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
async function openInfoDialog(msgType: string): Promise<string> {
  if (msgType === Or01549Const.INFO_MESSAGE_TYPE_10430) {
    // データ変更確認ダイアログを初期化(i.cmn.10430)
    Or21814Logic.state.set({
      uniqueCpId: or21814_1.value.uniqueCpId,
      state: {
        // ダイアログタイトル
        dialogTitle: t('label.top-btn-title'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-10430'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'destroy1',
        secondBtnLabel: t('btn.no'),
        thirdBtnType: 'normal3',
        thirdBtnLabel: t('btn.cancel'),
      },
    })
  } else if (msgType === Or01549Const.INFO_MESSAGE_TYPE_11326) {
    // 削除確認ダイアログを初期化(i.cmn.11326)
    Or21814Logic.state.set({
      uniqueCpId: or21814_1.value.uniqueCpId,
      state: {
        // ダイアログタイトル
        dialogTitle: t('label.top-btn-title'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-11326', [
          local.consultationDate.value,
          t('label.prevention-basic'),
        ]),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'destroy1',
        secondBtnLabel: t('btn.no'),
        thirdBtnType: 'blank',
      },
    })
  } else if (msgType === Or01549Const.INFO_MESSAGE_TYPE_11265) {
    // 二回目新規確認ダイアログを初期化(i.cmn.11265)
    Or21814Logic.state.set({
      uniqueCpId: or21814_1.value.uniqueCpId,
      state: {
        // ダイアログタイトル
        dialogTitle: t('label.top-btn-title'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-11265'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      },
    })
  } else if (msgType === Or01549Const.INFO_MESSAGE_TYPE_11262) {
    Or21814Logic.state.set({
      uniqueCpId: or21814_1.value.uniqueCpId,
      state: {
        // ダイアログタイトル
        dialogTitle: t('label.confirm'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-11262'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      },
    })
  }

  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)

        let result = Or01549Const.DIALOG_RESULT_CANCEL

        if (event?.firstBtnClickFlg) {
          result = Or01549Const.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or01549Const.DIALOG_RESULT_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = Or01549Const.DIALOG_RESULT_CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

const changeCreateDate = (dateValue: Mo00020Type) => {
  commonInfo.baseDate = dateValue.value
}

const favoriteIconClick = () => {
  return
}

const historySelectChange = async (
  selectItem: HistorySelectTableDataItem | undefined
): Promise<void> => {
  if (!selectItem) {
    return
  } else {
    local.historyId = Number(selectItem.raiId)
    await getTabsData()
  }
}

/**
 * 「確定ボタン」押下時の処理
 *
 * @param _value - 結果
 */
function onConfirmOr31728(_value: BasicInfo[]) {
  return
}
</script>

<template>
  <c-v-sheet class="d-flex flex-column h-100 view">
    <!-- Or11871：有機体：画面メニューエリア -->
    <g-base-or11871 v-bind="or11871">
      <template #createItems>
        <c-v-list-item
          :disabled="isDisabledCopyBtn"
          :title="t('btn.copy')"
          @click="copyBtnClick"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.care-plan2-copy-btn')"
          />
        </c-v-list-item>
      </template>

      <template #optionMenuItems>
        <c-v-list-item
          :title="t('btn.delete')"
          prepend-icon="delete"
          :disabled="deleteFlag"
          @click="_delete()"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.delete-data')"
          />
        </c-v-list-item>
      </template>
    </g-base-or11871>

    <c-v-row
      no-gutters
      class="content-area"
    >
      <c-v-col
        cols="2"
        class="hidden-scroll h-100"
      >
        <!-- （利用者基本）利用者選択の表示 -->
        <g-base-or00248 v-bind="or00248" />
      </c-v-col>

      <c-v-col class="hidden-scroll h-100 px-2">
        <c-v-sheet class="content">
          <c-v-container>
            <c-v-row class="first-row">
              <c-v-col style="padding: 8px">
                <!-- 事業所 -->
                <g-base-or41179 v-bind="or41179" />
              </c-v-col>
            </c-v-row>
            <c-v-row class="second-row">
              <c-v-col
                v-if="planPeriodShow"
                cols="auto"
                style="padding-top: 8px; padding-bottom: 0px; padding-right: 8px; padding-left: 8px"
              >
                <!-- 計画対象期間 -->
                <g-custom-or31659
                  v-bind="Or31659"
                  :oneway-model-value="localOneway.Or31659Oneway"
                  :unique-cp-id="Or31659.uniqueCpId"
                />
              </c-v-col>
              <c-v-col
                v-else
                cols="auto"
                style="padding-top: 8px; padding-bottom: 0px; padding-right: 8px; padding-left: 8px"
              >
                <base-mo01338 :oneway-model-value="mo01338Oneway" />
              </c-v-col>
              <c-v-col
                v-if="historyShow"
                cols="auto"
                style="padding-left: 8px; padding-top: 8px; padding-bottom: 0px; padding-right: 8px"
              >
                <!-- 履歴 -->
                <g-custom-orX0008
                  v-bind="orX0008_1"
                  :oneway-model-value="localOneway.orX0008Oneway"
                  :unique-cp-id="orX0008_1.uniqueCpId"
                />
              </c-v-col>
              <c-v-col
                v-if="authorShow"
                cols="auto"
                :style="{ width: '200px' }"
                style="padding-top: 8px; padding-bottom: 0px; padding-right: 8px; padding-left: 0px"
              >
                <!-- 作成者 -->
                <g-custom-orX0009
                  v-bind="orX0009_1"
                  :oneway-model-value="localOneway.orX0009Oneway"
                  :unique-cp-id="orX0009_1.uniqueCpId"
                />
              </c-v-col>
              <c-v-col
                v-if="consultationDateShow"
                cols="auto"
                style="padding-top: 8px; padding-bottom: 0px; padding-right: 8px; padding-left: 0px"
              >
                <!-- 作成日 -->
                <base-mo00020
                  :model-value="local.consultationDate"
                  :oneway-model-value="localOneway.consultationDateOneway"
                  v-bind="{ ...$attrs }"
                  @update:model-value="changeCreateDate"
                />
              </c-v-col>
              <c-v-col
                cols="auto"
                style="padding-top: 8px; padding-bottom: 0px; padding-right: 8px; padding-left: 0px"
              >
                <!-- 同意 -->
                <base-mo00611
                  :oneway-model-value="localOneway.mo00611Oneway"
                  @click="handleClickSI042"
                />

                <g-custom-or-27631
                  v-if="showDialogOr27631"
                  v-bind="or27631"
                  v-model="or27631Type"
                />
              </c-v-col>
            </c-v-row>
            <c-v-row no-gutters>
              <base-mo00043
                v-model="local.mo00043"
                class="mt-2 w-100"
                style="padding: 0 !important"
                :oneway-model-value="localOneway.mo00043Oneway"
                @click="tabsClick"
              >
              </base-mo00043>
            </c-v-row>
            <c-v-row
              v-show="inputBoomShow"
              no-gutters
              class="mt-2"
            >
              <c-v-window v-model="local.mo00043.id">
                <!--基本情報-->
                <c-v-window-item value="basicInfo">
                  <g-custom-or-08207 v-bind="or08207"></g-custom-or-08207>
                </c-v-window-item>
                <!-- 介護予防に関する事項 -->
                <c-v-window-item value="carePrevention">介護予防に関する事項</c-v-window-item>
                <!-- 現病歴・既往歴と経過 -->
                <c-v-window-item value="currentMedical">現病歴・既往歴と経過 </c-v-window-item>
                <!-- 現在利用しているサービス -->
                <c-v-window-item value="currentService"> 現在利用しているサービス</c-v-window-item>
              </c-v-window>
            </c-v-row>
          </c-v-container>
          <!-- 下段 -->
        </c-v-sheet>
      </c-v-col>
    </c-v-row>
    <c-v-row
      no-gutters
      class="footer"
    >
      <c-v-col>
        <g-base-or00051 />
      </c-v-col>
    </c-v-row>
  </c-v-sheet>

  <!-- warning メッセージ -->
  <g-base-or-21815 v-bind="or21815" />
  <!-- INFO 確認ダイアログ -->
  <g-base-or21814 v-bind="or21814_1" />
  <!-- 有機体:エラーダイアログ -->
  <g-base-or21813 v-bind="or21813" />

  <!-- 確認ダイアログ -->
  <g-custom-or-x-0002
    v-model="localOneway.updateConfirm"
    :oneway-model-value="localOneway.orX0002DialogOneway"
  >
  </g-custom-or-x-0002>

  <g-custom-or-10929
    v-bind="or10929"
    v-model="or10929Type"
    :oneway-model-value="localOneway.or10929Oneway"
    @update:model-value="historySelectChange"
  />

  <g-custom-or-31728
    v-if="showDialogOr31728"
    v-bind="or31728"
    v-model="or31728Type"
    :unique-cp-id="or31728.uniqueCpId"
    @on-confirm="onConfirmOr31728"
  />

  <g-custom-or-10443
    v-if="showDialogOr10443"
    v-bind="or10443"
    :oneway-model-value="Or10443OnewayModel"
  />
</template>

<style scoped lang="scss">
.divider-class {
  border-width: thin;
  margin: 8px 0px;
}
.divider-noLine-class {
  border: none;
  margin: 32px 0px;
  border-color: white;
}

.view {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: transparent;
}

.content-area {
  display: flex;
  flex-grow: 1;
  overflow: hidden;
  height: 100%;
}

.content {
  padding: 5px 0px;
  overflow-x: auto;
}
.second-row {
  margin-top: 0px;
  align-items: baseline;
}

:deep(.v-slide-group__content) {
  gap: 8px;
}
:deep(.section-border-all-sides) {
  border: 0;
}
.v-sheet {
  background-color: transparent !important;
}
.first-row,
.second-row {
  :deep(.v-sheet) {
    background-color: transparent !important;
  }
}
:deep(.notice) {
  .v-col {
    .item-label {
      color: red !important;
      font-weight: bold !important;
    }
  }
}
</style>
