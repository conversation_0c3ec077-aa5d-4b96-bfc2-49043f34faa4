<script setup lang="ts">
/**
 * Or27778:意見一覧
 * GUI01224_意見マスタ
 *
 * @description
 * 意見一覧
 *
 * <AUTHOR>
 */
import {
  computed,
  reactive,
  ref,
  watch,
  nextTick,
  type ComponentPublicInstance,
  onUnmounted,
} from 'vue'
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash'
import { Or12336Const } from '../Or12336/Or12336.constants'
import type { TableData } from './Or27778.type'
import { Or27778Const } from './Or27778.constants'
import type {
  Or27778OnewayType,
  Or27778Type,
} from '~/types/cmn/business/components/Or27778Type'
import { UPDATE_KBN } from '~/constants/classification-constants'
import { useScreenTwoWayBind, useSetupChildProps } from '~/composables/useComponentVue'
import { useValidation } from '~/utils/useValidation'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type {
  Mo01354Headers,
  Mo01354OnewayType,
  Mo01354Type,
} from '~/components/base-components/molecules/Mo01354/Mo01354Type'
import { useScreenStore } from '#imports'

const { t } = useI18n()
const validation = useValidation()
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or27778OnewayType
  modelValue: Or27778Type
  uniqueCpId: string
  parentUniqueCpId: string
}

const props = defineProps<Props>()

const defaultOnewayModelValue: Or27778OnewayType = {
  kbnFlg: '',
}

const defaultModelValue: Or27778Type = {
  delBtnDisabled: false,
  focusIndex: '',
  focusType: '',
  opinionMasterInfoList: [],
}

const localOneWay = reactive({
  Or27778: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  mo01278Oneway: {
    maxLength: '4',
    min: 0,
    max: 9999,
    isEditCamma: false,
    isRequired: true,
    showItemLabel: false,
    rules: [
      validation.required,
      validation.numeric,
      validation.minValue(1000),
      validation.maxValue(9999),
    ],
  },
  mo01274Oneway: {
    maxLength: '30',
    isRequired: true,
    showItemLabel: false,
    rules: [validation.required],
  },
  /** ヘーダOneway */
  headerOneway: {
    columnMinWidth: {
      columnWidths: [160, 420],
    },
    rowHeight: '32',
    height: '601',
    headers: [
      {
        key: 'kbnCd',
        required: true,
        /** 区分番号 */
        title: t('label.category-number'),
        sortable: false,
      },
      {
        key: 'textKnj',
        required: true,
        /** 内容 */
        title: t('label.content'),
        sortable: false,
      },
    ] as Mo01354Headers[],
  } as Mo01354OnewayType,
})

const local = reactive({
  Or27778: {
    ...defaultModelValue,
    ...props.modelValue,
  },
})

/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<TableData[]>({
  cpId: Or27778Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
refValue.value = []
/**************************************************
 * 変数定義
 **************************************************/
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(1) })
// ダイアログ表示フラグ
const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(1)]: or21814.value,
})

// 説明-区分番号
const descriptionCategoryNumber: string = t('label.category-number-input')
// 説明-全共通
const descriptionAllCommon: string = t('label.all-common')
// 番号ref
const itemRefs = new Map<string, HTMLElement>()
// 種別ref
const textRefs = new Map<string, HTMLElement>()
const setItemRef = (el: Element | ComponentPublicInstance | null, id: string, type: boolean) => {
  const elHtml: HTMLElement = el as HTMLElement
  if (el) {
    if (type) {
      itemRefs.set(id, elHtml)
    } else {
      textRefs.set(id, elHtml)
    }
  }
}

// 元のテーブルデータ
const orgTableData = ref<TableData[]>()
// テーブルデータ
const tableDataFilter = computed(() => {
  const titleList = refValue.value
  return titleList!.filter((i: TableData) => i.updateKbn !== UPDATE_KBN.DELETE)
})
const mo01354Type = ref<Mo01354Type>({
  values: {
    selectedRowId: '',
    selectedRowIds: [],
    items: [],
  },
})
/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])
/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * 意見マスタ情報取得
 *
 */
function init() {
  // 戻り値はテーブルデータとして処理されます
  let tmpArr = [] as TableData[]
  tmpArr = []
  for (const item of local.Or27778.opinionMasterInfoList) {
    tmpArr.push({
      id: tmpArr.length + 1 + '',
      kbnCd: { value: item.kbnCd },
      textKnj: { value: item.textKnj },
      cf1Id: item.cf1Id,
      modifiedCnt: item.modifiedCnt,
      updateKbn: UPDATE_KBN.NONE,
      changeF: item.changeF,
      kbnFlg: item.kbnFlg,
      cf1Flg: item.cf1Flg,
    })
  }
  // 元のテーブルデータの設定
  orgTableData.value = cloneDeep(tmpArr)

  // APIから取得されたデータでRefValueを更新する
  useScreenStore().setCpTwoWay({
    cpId: Or12336Const.CP_ID(0),
    uniqueCpId: props.uniqueCpId,
    value: tmpArr,
    isInit: true,
  })
  if (tmpArr.length > 0) {
    // 一覧の一行目を選択状態にする
    mo01354Type.value.values.selectedRowId = '1'
    // 行削除活性
    local.Or27778.delBtnDisabled = true
    emit('update:modelValue', local.Or27778)
  }
}

/**
 * 行選択
 */
watch(
  () => mo01354Type.value.values.selectedRowId,
  (newValue) => {
    if (!newValue) {
      return
    }
    // 行削除活性
    local.Or27778.delBtnDisabled = true
    emit('update:modelValue', local.Or27778)
  }
)

/**
 * 「新規」押下
 */
async function createRow() {
  // 予定マスタのタイトル一覧の最終に新しい行を追加する。
  const data = {
    // 区分番号：空白
    kbnCd: { value: '' },
    // 内容：空白
    textKnj: { value: '' },
    // 入力ID
    cf1Id: '',
    // 入力区分
    cf1Flg: Or27778Const.DEFAULT.CF1_FLG,
    // 更新回数
    modifiedCnt: '',
    // テーブルINDEX(行固有ID)
    id: refValue.value?.length
      ? Math.max(...mo01354Type.value.values.items.map((i) => parseInt(i.id))) + 1 + ''
      : '1',
    // 更新区分
    updateKbn: UPDATE_KBN.CREATE,
    // 区分フラグ
    kbnFlg: localOneWay.Or27778.kbnFlg,
    // 変更フラグ
    changeF: Or27778Const.DEFAULT.CHANGE_F,
  }
  refValue.value!.push(data)
  mo01354Type.value.values.selectedRowId = data.id
  await nextTick()
  setFocus(data.id, true)
}

/**
 * 行削除ボタン押下
 */
function deleteRow() {
  if (mo01354Type.value.values.items.length !== -1) {
    // メッセージを表示
    // 確認ダイアログのpiniaを設定
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: {
        // ダイアログタイトル
        dialogTitle: t('label.confirm-dialog-title-info'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-11397'),
        // 第1ボタンタイプ
        firstBtnType: 'normal1',
        // 第1ボタンラベル
        firstBtnLabel: t('btn.yes'),
        // 第2ボタンタイプ
        secondBtnType: 'normal3',
        // 第2ボタンラベル
        secondBtnLabel: t('btn.no'),
        // 第3ボタンタイプ
        thirdBtnType: 'blank',
        iconColor: 'rgb(var(--v-theme-blue-700))',
        iconBackgroundColor: 'rgb(var(--v-theme-blue-200))',
      },
    })
    // 確認ダイアログ表示
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: { isOpen: true },
    })
  }
}

/**
 * 行複写ボタン押下
 */
async function copyRow() {
  if (mo01354Type.value.values.items.length !== -1) {
    let kbnCd = ''
    let textKnj = ''
    let index = 0
    tableDataFilter.value.forEach((item: TableData, i) => {
      if (item.id === mo01354Type.value.values.selectedRowId) {
        kbnCd = item.kbnCd.value
        textKnj = item.textKnj.value
        index = i
      }
    })
    // タイトル一覧の最終に新しい行を追加する。
    const data = {
      // 区分番号：空白
      kbnCd: { value: kbnCd },
      // 内容：空白
      textKnj: { value: textKnj },
      // 入力ID
      cf1Id: '',
      // 入力区分
      cf1Flg: Or27778Const.DEFAULT.CF1_FLG,
      // 更新回数
      modifiedCnt: '',
      // テーブルINDEX(行固有ID)
      id: Math.max(...refValue.value!.map((i) => parseInt(i.id))) + 1 + '',
      // 更新区分
      updateKbn: UPDATE_KBN.CREATE,
      // 区分フラグ
      kbnFlg: localOneWay.Or27778.kbnFlg,
      // 変更フラグ
      changeF: Or27778Const.DEFAULT.CHANGE_F,
    }
    refValue.value!.splice(++index, 0, data)
    mo01354Type.value.values.selectedRowId = data.id
    await nextTick()
    setFocus(data.id, true)
  }
}

/**
 * 行番号フォーカス
 *
 * @param id - 行id
 *
 * @param flg - 種別
 */
const setFocus = (id: string, flg: boolean) => {
  const element = flg
    ? itemRefs.get(id)!.querySelector('#input-' + id + ' input')
    : textRefs.get(id)!.querySelector('#text-' + id)
  const inputHtmlElement = element as HTMLElement
  inputHtmlElement.focus()
}


/**
 * データ変更処理
 *
 * @param tableData - 行情報
 */
const dataChange = (tableData: TableData) => {
  tableData.updateKbn = UPDATE_KBN.UPDATE
}
/**************************************************
 * ワッチャー
 **************************************************/
/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    // 'はい'
    if (newValue.firstBtnClickFlg) {
      let rowId = ''
      refValue.value!.forEach((item: TableData, index: number) => {
        if (mo01354Type.value.values.selectedRowId === item.id) {
          // 更新前後配列長さ
          const newLength = tableDataFilter.value.length ?? 0
          if (newLength > 1) {
            // 削除するデータは配列の最後にあります
            if (item.id === tableDataFilter.value[newLength - 1].id) {
              rowId = tableDataFilter.value[newLength - 2].id
            } else {
              const idx = tableDataFilter.value.findIndex((sItem) => item.id === sItem.id)
              // 削除するデータは配列の最後にありません
              rowId = tableDataFilter.value[idx + 1].id
            }
          }
          // 新規行の場合
          if (item.updateKbn === UPDATE_KBN.CREATE) {
            // 当該行を廃棄する
            refValue.value?.splice(index, 1)
          } else {
            // 既存行の場合
            item.updateKbn = UPDATE_KBN.DELETE
          }
        }
      })
      // 一行だけ
      if (mo01354Type.value.values.items.length === 1) {
        mo01354Type.value.values.selectedRowId = ''
        // 行削除非活性
        local.Or27778.delBtnDisabled = false
        emit('update:modelValue', local.Or27778)
        return
      }
      // 選択行再設定
      mo01354Type.value.values.selectedRowId = rowId
      await nextTick()
      // フォーカス
      setFocus(rowId, true)
    } else {
      return
    }
  }
)

/**
 * エラー行のフォーカス位置に戻す
 */
watch(
  () => props.modelValue.focusIndex,
  async (newValue) => {
    if (!newValue) {
      return
    }
    local.Or27778.focusType = props.modelValue.focusType
    const row = refValue.value![Number(newValue)]
    if (local.Or27778.focusType === Or27778Const.DEFAULT.KBN_CD) {
      await nextTick()
      setFocus(row.id, true)
      mo01354Type.value.values.selectedRowId = row.id
    } else if (local.Or27778.focusType === Or27778Const.DEFAULT.TEXT_KNJ) {
      await nextTick()
      setFocus(row.id, false)
      mo01354Type.value.values.selectedRowId = row.id
    }
    mo01354Type.value.values.scrollToId = row.id
  }
)
/**
 * 画面modelの設定
 */
watch(
  () => tableDataFilter.value,
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    mo01354Type.value.values.items = newValue
  }
)

/**
 * 画面Onewayの設定
 */
watch(
  () => props.modelValue.opinionMasterInfoList,
  (newVal) => {
    local.Or27778.opinionMasterInfoList = cloneDeep(newVal)
  },
  { deep: true }
)
// コンポーネントがアンマウントされる際に実行されるクリーンアップ操作
onUnmounted(() => {

  itemRefs.clear()
  textRefs.clear()
})
/**
 * コンポーネントのメソッドを親コンポーネントに公開する
 */
defineExpose({
  createRow,
  copyRow,
  deleteRow,
  init,
})
</script>

<template>
  <div>
    <div class="table-header">
      <base-mo-01354
        v-model="mo01354Type"
        :oneway-model-value="localOneWay.headerOneway"
        class="list-wrapper d-flex"
      >
        <template #[`item.kbnCd`]="{ item }">
          <div
            :ref="(el) => setItemRef(el, item.id, true)"
            class="h-100"
          >
            <base-mo00045
              :id="`input-${item.id}`"
              v-model="item.kbnCd"
              class="background-transparent number"
              :oneway-model-value="localOneWay.mo01278Oneway"
              @update:model-value="dataChange(item)"
            />
          </div>
        </template>
        <template #[`item.textKnj`]="{ item }">
          <div
            :ref="(el) => setItemRef(el, item.id, false)"
            class="h-100"
          >
            <base-mo00045
              :id="`text-${item.id}`"
              v-model="item.textKnj"
              class="background-transparent"
              :oneway-model-value="localOneWay.mo01274Oneway"
              @update:model-value="dataChange(item)"
            />
          </div>
        </template>
        <!-- ページングを非表示 -->
        <template #bottom />
      </base-mo-01354>
    </div>
  </div>
  <!-- 説明:※区分番号は1000以上の値を入力してください。  -->
  <div class="mt-15px mb-2 font-weight-bold font-size-text font-red">
    {{ descriptionCategoryNumber }}
  </div>
  <!-- 説明:※全共通  -->
  <div class="font-size-text">
    {{ descriptionAllCommon }}
  </div>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  >
  </g-base-or21814>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table.scss';

.font-size-text {
  font-size: 12px;
}
.font-red {
  color: red;
}
:deep(.number .v-field__input) {
  text-align: right !important;
}
:deep(.v-field__input) {
  height: 31px !important;
  min-height: 31px !important;
}


.background-transparent :deep(.v-col) {
  padding: 0 !important;
}

// transparent
.background-transparent {
  background-color: transparent !important;
}

:deep(.v-table--density-compact) {
  --v-table-row-height:32px !important;
}

.mt-15px{
  margin-top: 15px;
}
</style>
