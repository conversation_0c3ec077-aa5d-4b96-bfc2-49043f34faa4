<script setup lang="ts">
/**
 * Or10320:有機体:モーダル（画面/特殊コンポーネント）
 * GUI00627_［アセスメント（インターライ）マスタ］画面
 *
 * @description
 * アセスメント（インターライ）マスタ
 *
 * <AUTHOR>
 */
import { onMounted, reactive, watch, ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or10320Const } from './Or10320.constants'

import type { Or10320StateType } from './Or10320.type'
import { useScreenOneWayBind, useSetupChildProps, useSystemCommonsStore } from '#imports'
import type { Or10320Type, Or10320OnewayType } from '~/types/cmn/business/components/Or10320Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Mo00039OnewayType } from '~/types/cmn/business/components/Mo00039Type'
import type { CustomClass } from '~/types/CustomClassType'
import type { Mo01344OnewayType } from '@/types/business/components/Mo01344Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  IAssessmentInterraiMasterInsertInEntity,
  IAssessmentInterraiMasterInsertOutEntity,
} from '~/repositories/cmn/entities/AssessmentInterraiMasterInsertEntity'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { useJigyoList } from '~/utils/useJigyoList'
import { useColorUtils } from '~/utils/useColorUtils'
const { convertDecimalToHex, convertHexToDecimal } = useColorUtils()

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or10320Type
  onewayModelValue: Or10320OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or10320Const.DEFAULT.IS_OPEN,
})

const or21814 = ref({ uniqueCpId: '' })
const or41179 = ref({ uniqueCpId: Or41179Const.CP_ID(0) })

// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
const { jigyoListWatch } = useJigyoList()

// 情報ダイアログ
const mo00024Oneway = ref<Mo00024OnewayType>({
  width: '768px',
  height: '349px',
  persistent: true,
  showCloseBtn: false,
  mo01344Oneway: {
    name: 'Or10320',
    toolbarTitle: t('label.assessment-interRAI-master'),
    toolbarName: 'Or10320ToolBar',
    // ツールバータイトルの左寄せ
    toolbarTitleCenteredFlg: false,
    showCardActions: true,
    cardTextClass: 'pa-2 pt-0',
  } as Mo01344OnewayType,
})

// 保存権限（モック）
const saveAuthority = ref('F')

// 記録画面に力が入ってい
const inputChangeFlag = ref(false)
// 処理区分（noSaveAuthority:保存権限なし、close:画面閉じる、tabClick:タブ押下）
const processCategory = ref('')
// メモの文字サイズ_リスト
const memoLetterSizeList = ref<CodeType[]>([])
// メモの文字色表示_リスト
const memoLetterColorList = ref<CodeType[]>([])

const colorStyle = ref({
  width: '32px',
  height: '32px',
  'background-color': '#000000',
  cursor: 'pointer',
})

// 事業所IDバックアップ
const jigyoIdOld = ref('1')

// 事業所Id
const svJigyoId = ref<string>('')

const isApi = ref(true)

// メモの文字サイズ
const mo01338onewayMemoLetterSize = ref<string>('0')

// メモの文字色
const memoLetterColor = ref<string>('')

const local = reactive({
  mo00043Transfer: { id: 'assessmentInterRAI' } as Mo00043Type,
  mo00043: { id: '' } as Mo00043Type,
})

const localOneway = reactive({
  or10320: {
    ...props.onewayModelValue,
  },
  mo00043Oneway: {
    tabItems: [
      {
        id: 'assessmentInterRAI',
        title: t('label.assessment-interRAI'),
        tooltipText: t('label.assessment-interRAI'),
        tooltipLocation: 'bottom',
      },
      {
        id: 'diseaseName',
        title: t('label.disease-name'),
        tooltipText: t('label.disease-name'),
        tooltipLocation: 'bottom',
      },
      {
        id: 'medicine',
        title: t('label.medicine'),
        tooltipText: t('label.medicine'),
        tooltipLocation: 'bottom',
      },
      {
        id: 'medicineUnit',
        title: t('label.medicine-unit'),
        tooltipText: t('label.medicine-unit'),
        tooltipLocation: 'bottom',
      },
    ],
  } as Mo00043OnewayType,
  mo01338onewayInit: {
    value: t('label.initial-value'),
    valueFontWeight: 'bolder',
    customClass: {
      itemClass: 'customLabel',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338onewayMemoLetterSize: {
    value: t('label.memo-letter-size'),
    valueFontWeight: 'bolder',
    customClass: {
      itemClass: 'd-flex align-center',
      itemStyle: 'height: 36px',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338onewayMemoLetterSizeRadioGroup: {
    name: t('label.memo-letter-size'),
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  mo01338onewayMemoLetterColor: {
    value: t('label.memo-letter-color'),
    valueFontWeight: 'bolder',
    customClass: {
      itemClass: 'd-flex align-center',
      itemStyle: 'height: 36px',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo01338onewayFooter: {
    value: t('label.office-unit-save'),
    customClass: {
      itemClass: 'footerLabel',
    } as CustomClass,
  } as Mo01338OnewayType,
  mo00611Oneway: {
    btnLabel: t('btn.close'),
    tooltipLocation: 'bottom',
    tooltipText: t('tooltip.screen-close'),
  } as Mo00611OnewayType,
  mo00609Oneway: {
    btnLabel: t('btn.save'),
    tooltipLocation: 'bottom',
    tooltipText: t('tooltip.save'),
  } as Mo00609OnewayType,
})

const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or10320StateType>({
  cpId: Or10320Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or10320Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/

onMounted(() => {
  // 汎用コードマスタデータを取得し初期化
  void initCodes()
  init()
  // 事業所選択リストを初期化
  Or41179Logic.state.set({
    uniqueCpId: or41179.value.uniqueCpId,
    state: {
      searchCriteria: {
        selfId: systemCommonsStore.getUserSelectSelfId(),
      },
    },
  })
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(1)]: or21814.value,
  [Or41179Const.CP_ID(1)]: or41179.value,
})

// ★事業所選択監視関数を実行
jigyoListWatch(or41179.value.uniqueCpId, callbackFuncJigyo)
/**
 * 事業所選択監視関数に渡すコールバック関数
 * 状態管理のシステム共有領域．利用者選択領域．利用者番号が変更されたタイミングで
 * 実行する処理です。
 *
 * @param newJigyoId - 変更後の事業者ID
 */
function callbackFuncJigyo(newJigyoId: string) {
  if (newJigyoId === jigyoIdOld.value) {
    return
  }

  // まず変更前の事業所を保持
  setJigyo(jigyoIdOld.value)

  // 事業所変更処理
  void jigyoChange(newJigyoId)
}

/**
 * 事業所変更処理
 *
 * @param newJigyoId - 変更後の事業者ID
 */
async function jigyoChange(newJigyoId: string) {
  // 画面変更チェック
  if (inputChangeFlag.value) {
    if (saveAuthority.value === 'F') {
      // 画面入力変更あり、かつ保存権限がない場合
      // 確認ダイアログ表示
      const dialogResult = await openConfirmDialog2(t('message.w-com-10006'))
      switch (dialogResult) {
        case 'yes': {
          svJigyoId.value = newJigyoId
          jigyoIdOld.value = newJigyoId
          // 変更後の事業所に設定
          setJigyo(newJigyoId)
          inputChangeFlag.value = false
        }
      }
      return
    } else {
      // 画面入力変更あり、かつ保存権限がある場合
      // 確認ダイアログ表示
      const dialogResult = await openConfirmDialog1(t('message.i-cmn-10430'))
      switch (dialogResult) {
        case 'yes': {
          svJigyoId.value = newJigyoId
          jigyoIdOld.value = newJigyoId
          // 変更後の事業所に設定
          setJigyo(newJigyoId)

          // はい選択時は入力内容を保存する
          await save()
          break
        }
        case 'no':
          // いいえ選択時は編集内容を破棄するので何もしない
          svJigyoId.value = newJigyoId
          jigyoIdOld.value = newJigyoId
          // 変更後の事業所に設定
          setJigyo(newJigyoId)
          inputChangeFlag.value = false
          break
        case 'cancel':
          // キャンセル選択時は一覧の選択を戻す
          break
      }

      return
    }
  } else {
    svJigyoId.value = newJigyoId
    jigyoIdOld.value = newJigyoId
    // 変更後の事業所に設定
    setJigyo(newJigyoId)
  }
}

/**
 * 事業所を選択
 *
 * @param jigyoId - 事業所ID
 */
function setJigyo(jigyoId: string) {
  Or41179Logic.data.set({
    uniqueCpId: or41179.value.uniqueCpId,
    value: {
      modelValue: jigyoId,
    },
  })
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no, cancel)
 */
async function openConfirmDialog1(paramDialogText: string): Promise<'yes' | 'no' | 'cancel'> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: paramDialogText,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = 'cancel' as 'yes' | 'no' | 'cancel'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }
        if (event?.thirdBtnClickFlg) {
          result = 'cancel'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no)
 */
async function openConfirmDialog2(paramDialogText: string): Promise<'yes' | 'no'> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: paramDialogText,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = 'no' as 'yes' | 'no'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      close()
    }
  }
)

//メッセージの選択結果を監視
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    // 処理区分が「タブ押下」の場合
    if (processCategory.value === 'tabClick') {
      if (newValue.firstBtnClickFlg) {
        // AC009処理を行う。
        void save()

        // 画面のタイトルに切替
        tabTitleChange()
      } else if (newValue.secondBtnClickFlg) {
        local.mo00043Transfer.id = local.mo00043.id
      } else {
        local.mo00043Transfer.id = local.mo00043.id
      }
    } else if (processCategory.value === 'close') {
      if (newValue.firstBtnClickFlg) {
        // AC009処理を行う。
        void save()
      } else if (newValue.secondBtnClickFlg) {
        inputChangeFlag.value = false
        setState({ isOpen: false })
      } else {
        local.mo00043Transfer.id = local.mo00043.id
      }
    } else if (processCategory.value === 'noSaveAuthority') {
      if (newValue.firstBtnClickFlg) {
        init()

        // 画面のタイトルに切替
        tabTitleChange()
      } else {
        local.mo00043Transfer.id = local.mo00043.id
      }
    }
  }
)

/**
 * 保存権限がない場合、確認メッセージを表示する
 *
 * @param errormsg - Message
 */
function showOr21814MsgTwoBtn(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: errormsg,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 保存権限がある場合、確認メッセージを表示する
 *
 * @param confirmMsg - エラー内容
 */
function showOr21814Msg(confirmMsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: confirmMsg,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * メモの文字サイズ変更
 */
watch(
  () => mo01338onewayMemoLetterSize.value,
  () => {
    if (!inputChangeFlag.value) {
      inputChangeFlag.value = true
    }
  }
)

/**
 * メモの文字色変更
 */
watch(
  () => memoLetterColor.value,
  (newValue) => {
    colorStyle.value['background-color'] = newValue ?? ''
    if (isApi.value) {
      isApi.value = false
      return
    }
    if (!inputChangeFlag.value) {
      inputChangeFlag.value = true
    }
  }
)

/**
 * AC001_初期情報取得(アセスメント（インターライ）マスタ情報を取得する)
 */
function init() {
  isApi.value = true
  // 画面項目変更ないにする
  inputChangeFlag.value = false
}

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // メモの文字サイズ
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_MEMO_LETTER_SIZE },
    // メモの文字色表示
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_MEMO_LETTER_COLOR },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // コード取得
  // メモの文字サイズ選択肢
  memoLetterSizeList.value = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_MEMO_LETTER_SIZE
  )
  // メモの文字色選択肢
  memoLetterColorList.value = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_MEMO_LETTER_COLOR
  )
  isApi.value = true
  // APIから取得に色分類３に「2：メモ文字色」対応の整数値
  const colorCode = ref('999')
  memoLetterColor.value = convertDecimalToHex(0)
  for (const item of memoLetterColorList.value) {
    if (colorCode.value === item.value) {
      memoLetterColor.value = convertDecimalToHex(parseInt(colorCode.value))
    }
    item.value = convertDecimalToHex(parseInt(item.value))
  }
}

/**
 * タブ「病名」押下、タブ「薬剤」押下、タブ「薬剤単位タブ」押下
 */
function tabClick() {
  if (local.mo00043Transfer.id !== local.mo00043.id) {
    // 該当画面に変更がある場合
    if (inputChangeFlag.value) {
      // 保存権限がある場合
      if (saveAuthority.value === 'T') {
        // 処理区分
        processCategory.value = 'tabClick'
        // 以下のメッセージを表示(q_cmn_10430)
        showOr21814Msg(t('message.q-cmn-10430'))
      } else {
        // 処理区分
        processCategory.value = 'noSaveAuthority'
        // 保存権限がない場合
        showOr21814MsgTwoBtn(t('message.i-cmn-10006'))
      }
    } else {
      // 画面のタブに切替
      tabTitleChange()

      // ・処理終了
      return
    }
  }
}

/**
 * 画面のタイトルに切替
 */
function tabTitleChange() {
  local.mo00043.id = local.mo00043Transfer.id
  if (local.mo00043.id === 'assessmentInterRAI') {
    mo00024Oneway.value.mo01344Oneway!.toolbarTitle = t('label.assessment-interRAI-master')
  } else if (local.mo00043.id === 'diseaseName') {
    mo00024Oneway.value.mo01344Oneway!.toolbarTitle = t('label.disease-name-master')
  } else if (local.mo00043.id === 'medicine') {
    mo00024Oneway.value.mo01344Oneway!.toolbarTitle = t('label.medicine-master')
  } else if (local.mo00043.id === 'medicineUnit') {
    mo00024Oneway.value.mo01344Oneway!.toolbarTitle = t('label.medicine-unit-master')
  }
}

/**
 * AC002_「×ボタン」押下
 * AC008_「閉じボタン」押下
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOnewayBind領域のフラグを更新する。
 */
function close() {
  // 該当画面に変更がある場合
  if (inputChangeFlag.value) {
    // 処理区分
    processCategory.value = 'close'
    // 以下のメッセージを表示(q_cmn_10430)
    showOr21814Msg(t('message.q-cmn-10430'))
  } else {
    setState({ isOpen: false })
  }
}

/**
 * AC009_「保存ボタン」押下
 * 「保存」ボタン押下
 */
async function save() {
  // 変更ありの項目データを保存する
  if (inputChangeFlag.value) {
    const inputData: IAssessmentInterraiMasterInsertInEntity = {
      // 事業所ID
      svJigyoId: svJigyoId.value,
      // メモの文字サイズ
      memoLetterSize: mo01338onewayMemoLetterSize.value,
      // メモの文字色
      memoLetterColor: convertHexToDecimal(memoLetterColor.value),
    }
    const resData: IAssessmentInterraiMasterInsertOutEntity = await ScreenRepository.insert(
      'assessmentInterraiMasterInsert',
      inputData
    )
    if (resData.statusCode === 'success') {
      inputChangeFlag.value = false
    }
    if (processCategory.value === 'close') {
      setState({ isOpen: false })
    }
  }
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="mo00024Oneway"
  >
    <template #toolbarRight>
      <c-v-btn
        class="mr-2"
        @click="close"
      >
        <base-at-icon icon="close" />
      </c-v-btn>
    </template>
    <template #cardItem>
      <base-mo00043
        v-model="local.mo00043Transfer"
        :oneway-model-value="localOneway.mo00043Oneway"
        style="padding-left: 0 !important"
        @click="tabClick"
      >
      </base-mo00043>
      <!-- タブ switch -->
      <c-v-window v-model="local.mo00043.id">
        <!-- タブ：アセスメント（インターライ）-->
        <c-v-window-item value="assessmentInterRAI">
          <div class="py-2">
            <g-base-or-41179 v-bind="or41179" />
          </div>
          <c-v-divider />
          <c-v-card class="assessmentCard">
            <c-v-card-text class="w-auto flex-0-0">
              <c-v-row
                no-gutter
                class="content"
              >
                <div
                  style="width: 50px; height: 100px"
                  class="initValue d-flex align-center justify-center"
                >
                  <base-mo01338
                    :oneway-model-value="localOneway.mo01338onewayInit"
                    class="titleLabel"
                  ></base-mo01338>
                </div>
                <c-v-col>
                  <c-v-row
                    no-gutter
                    class="letterSize"
                  >
                    <div
                      class="letterSizeLabel d-flex align-center"
                      style="padding-left: 8px !important; width: 200px"
                    >
                      <base-mo01338
                        :oneway-model-value="localOneway.mo01338onewayMemoLetterSize"
                        class="titleLabel"
                      ></base-mo01338>
                    </div>
                    <c-v-col class="d-flex align-center">
                      <base-mo00039
                        v-model="mo01338onewayMemoLetterSize"
                        :oneway-model-value="localOneway.mo01338onewayMemoLetterSizeRadioGroup"
                      >
                        <base-at-radio
                          v-for="(item, index) in memoLetterSizeList"
                          :key="'or10320-' + index"
                          :name="'or10320-radio-' + index"
                          :radio-label="item.label"
                          :value="item.value"
                          class="radioItem"
                        />
                      </base-mo00039>
                    </c-v-col>
                  </c-v-row>
                  <c-v-row no-gutter>
                    <div
                      class="letterColorLabel d-flex align-center"
                      style="padding-left: 8px !important; width: 200px"
                    >
                      <base-mo01338
                        :oneway-model-value="localOneway.mo01338onewayMemoLetterColor"
                        class="titleLabel"
                      ></base-mo01338>
                    </div>
                    <c-v-col class="letterSize d-flex align-center">
                      <div
                        class="pl-2"
                        style="display: inline-flex; align-items: center"
                      >
                        <!-- メモの文字色表示 -->
                        <div
                          :style="colorStyle"
                          class="elevation-2"
                        ></div>
                        <!-- メモの文字色リスト -->
                        <base-at-select
                          v-model="memoLetterColor"
                          name="selectColor"
                          :items="memoLetterColorList"
                          item-title="label"
                          item-value="value"
                          label="Compact"
                          single-line
                          class="ml-2"
                          style="width: 160px"
                        ></base-at-select>
                      </div>
                    </c-v-col>
                  </c-v-row>
                </c-v-col>
              </c-v-row>
            </c-v-card-text>
          </c-v-card>
          <base-mo01338 :oneway-model-value="localOneway.mo01338onewayFooter"></base-mo01338>
        </c-v-window-item>
        <!-- タブ：病名 -->
        <c-v-window-item value="diseaseName">
          <span>{{ t('label.disease-name') }}</span>
        </c-v-window-item>
        <!-- タブ：薬剤 -->
        <c-v-window-item value="medicine">
          <span>{{ t('label.medicine') }}</span>
        </c-v-window-item>
        <!-- タブ：薬剤単位 -->
        <c-v-window-item value="medicineUnit">
          <span>{{ t('label.medicine-unit') }}</span>
        </c-v-window-item>
      </c-v-window>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          v-bind="localOneway.mo00611Oneway"
          class="mx-2"
          @click="close"
        >
          <!-- ツールチップ表示 -->
          <c-v-tooltip
            v-if="localOneway.mo00611Oneway.tooltipText"
            :text="localOneway.mo00611Oneway.tooltipText"
            :location="localOneway.mo00611Oneway.tooltipLocation"
            activator="parent"
          />
        </base-mo00611>

        <!-- 保存ボタン -->
        <base-mo00609
          v-bind="localOneway.mo00609Oneway"
          @click="save()"
        >
          <!-- ツールチップ表示 -->
          <c-v-tooltip
            v-if="localOneway.mo00609Oneway.tooltipText"
            :text="localOneway.mo00609Oneway.tooltipText"
            :location="localOneway.mo00609Oneway.tooltipLocation"
            activator="parent"
          />
        </base-mo00609>
      </c-v-row>
    </template>
    <g-base-or21814
      v-if="showDialogOr21814"
      v-bind="or21814"
    >
    </g-base-or21814>
  </base-mo00024>
</template>

<style scoped lang="scss">
$font-size-comment: 12px;
$line-height-content: 50px;

:deep(.v-card-item) {
  border-bottom: 1px solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
  padding: 8px 0 !important;
}

:deep(.v-input__details) {
  display: none;
}

:deep(.v-card-text) {
  padding: 0;
  border-left: 1px solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
  border-right: 1px solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
}

.assessmentCard {
  box-shadow: none;
}

.initValue,
.letterSizeLabel,
.letterColorLabel {
  height: $line-height-content;
  background-color: rgb(var(--v-theme-blue-50));
  border-right: 1px solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
  border-bottom: 1px solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
}

// 文字が縦に表示
:has(> .customLabel) {
  writing-mode: vertical-lr !important;
  letter-spacing: 8px;
}

div:has(> .footerLabel) {
  :nth-child(2) {
    :first-child {
      :first-child {
        color: rgb(var(--v-theme-subText)) !important;
        font-size: $font-size-comment !important;
      }
    }
  }
}

.radioItem {
  width: 160px;
}

.titleLabel {
  background-color: rgb(var(--v-theme-blue-50));
}

.letterSize {
  height: $line-height-content;
  border-bottom: 1px solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
}

.content {
  border-top: 1px solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
  margin: 8px 0;
  :deep(.v-col) {
    padding: 0px !important;
  }
  :deep(.v-row) {
    margin: 0;
  }
}
</style>
