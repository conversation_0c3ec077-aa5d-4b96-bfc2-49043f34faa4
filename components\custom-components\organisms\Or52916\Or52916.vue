<script setup lang="ts">
/**
 * Or52916:有機体:承認欄の複写画面
 * GUI00618_承認欄の複写画面
 *
 * @description
 * 承認欄の複写画面
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { reactive, ref, onMounted, watch, computed } from 'vue'
import { Or52916Const } from './Or52916.constants'
import type { ApprovalColumnTableDataItem, Or52916StateType } from './Or52916.type'
import { useScreenOneWayBind } from '#imports'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo01334OnewayType, Mo01334Type } from '~/types/business/components/Mo01334Type'
import type { Or52916OnewayType, Or52916Type } from '~/types/cmn/business/components/Or52916Type'
import type {
  CopyApprovalFieldSelectOutEntity,
  CopyApprovalFieldSelectInEntity,
  CopyOutListType,
} from '~/repositories/cmn/entities/CopyApprovalFieldSelectEntity'
import type { Mo00018OnewayType, Mo00018Type } from '~/types/business/components/Mo00018Type'

const { t } = useI18n()
/************************************************
 * Props
 ************************************************/
interface Props {
  modelValue: Or52916Type
  onewayModelValue: Or52916OnewayType
  uniqueCpId: string
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/

// 定数の分割代入
const {
  FONT_SIZE_9,
  FONT_SIZE_10,
  FONT_SIZE_11,
  FONT_SIZE_12,
  WIDTH_PX,
  SECTIONNO_3GKV00241P002,
  SECTIONNO_3GKV00242P002,
  SECTIONNO_3GKU0P149P001,
  SECTIONNO_3GKU0P141P001,
  STRING_0,
  STRING_1,
  STRING_2,
  STRING_3,
  STRING_4,
  TEXTSTR,
  WIDTHSTR,
  KNJSTR,
  FONTSTR,
  DAYSTR,
} = Or52916Const.DEFAULT

const localOneway = reactive({
  or52916: {
    ...props.onewayModelValue,
  },
  // 「承認欄の複写画面 ダイアログ
  mo00024Oneway: {
    width: '1236px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      toolbarTitle: t('label.confirm-form-duplicate'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
    },
  },
  //自事業者checkbox
  selfBusinessOperatorMo00018: {
    showItemLabel: false,
    checkboxLabel: t('label.self-business-operators'),
  } as Mo00018OnewayType,
  // 承認欄の複写情報一覧
  approvalColumnMo01334: {
    // 承認欄の複写情報データテーブルのヘッダー
    headers: [
      // 事業番号
      {
        title: t('label.business-seq'),
        key: 'svJigyoId',
        sortable: false,
        width: '115px',
      },
      // 事業所番号
      {
        title: t('label.plan-business-name'),
        key: 'jigyoNumber',
        sortable: false,
        width: '150px',
      },
      // 名称
      {
        title: t('label.formalname-knj'),
        key: 'jigyoKnj',
        sortable: false,
        width: '390px',
      },
      // サービス種類
      {
        title: t('label.serviceType'),
        key: 'svKbn',
        sortable: false,
        width: '200px',
      },
      // 帳票名
      {
        title: t('label.evaluation-ledger-name'),
        key: 'chohyoNm',
        sortable: false,
      },
    ],
    height: 294,
    items: [],
  } as unknown as Mo01334OnewayType,
})

// 閉じるボタン設置
const mo00611Oneway: Mo00611OnewayType = {
  btnLabel: t('btn.close'),
  width: '90px',
  tooltipText: t('tooltip.screen-close'),
}

// 確定ボタン設置
const mo00609Oneway: Mo00609OnewayType = {
  btnLabel: t('btn.confirm'),
  width: '90px',
  tooltipText: t('tooltip.confirm-btn'),
}

// ダイアログ設置
const mo00024 = ref<Mo00024Type>({
  isOpen: Or52916Const.DEFAULT.IS_OPEN,
})

// 承認欄方向：
let orientType = ''

// 承認欄の複写情報選択行データ設定
const approvalColumnSelectedItem = ref<Mo01334Type>({
  value: '',
  values: [],
})

// 自事業者のみmodelValue
const mo00018ModelValue = ref<Mo00018Type>({
  modelValue: true,
})

/**************************************************
 * 算出プロパティ
 **************************************************/

const approvalColumnTextList = computed(() => {
  return localOneway.approvalColumnMo01334.items.find((el) => {
    return el.id === approvalColumnSelectedItem.value.value
  })
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or52916StateType>({
  cpId: Or52916Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or52916Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  if (
    localOneway.or52916.sectionNo === SECTIONNO_3GKV00241P002 ||
    localOneway.or52916.sectionNo === SECTIONNO_3GKV00242P002
  ) {
    // ①親画面.セクション番号(istr_prt.section) = "3GKV00241P002" OR "3GKV00242P002"の場合:
    // 画面のタイトル
    localOneway.mo00024Oneway.mo01344Oneway.toolbarTitle = t('label.receipt-form-duplicate')
    // 承認欄方向： ③親画面.セクション番号(istr_prt.section)  = "3GKV00241P002" OR "3GKV00242P002"の場合：４
    orientType = STRING_4
  } else if (localOneway.or52916.sectionNo === SECTIONNO_3GKU0P149P001) {
    // ①親画面.セクション番号(istr_prt.section)  = "3GKU0P149P001"の場合： 3
    orientType = STRING_3
  } else if (localOneway.or52916.sectionNo === SECTIONNO_3GKU0P141P001) {
    // ①親画面.セクション番号(istr_prt.section)  = "3GKU0P149P001"の場合： 2
    orientType = STRING_2
    // ④親画面.セクション番号(istr_prt.section) が上記条件以外の場合：1
    orientType = STRING_1
  }
  // 初期情報取得
  await getInitDataInfo()
})

/** 初期情報取得 */
const getInitDataInfo = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: CopyApprovalFieldSelectInEntity = {
    ...localOneway.or52916,
    /**
     *承認欄方向
     */
    orientType,
    /**
     *自事業者のみ選択
     */
    sel: mo00018ModelValue.value.modelValue ? STRING_1 : STRING_0,
  }
  const resData: CopyApprovalFieldSelectOutEntity = await ScreenRepository.select(
    'copyApprovalFieldSelect',
    inputData
  )
  if (resData.data) {
    const { CopyOutList } = resData.data
    // データ情報設定
    localOneway.approvalColumnMo01334.items = approvalColumnTextProcess(CopyOutList)
    approvalColumnSelectedItem.value.value = CopyOutList[0]?.id ?? ''
  }
}

/**
 * 承認欄の内容を処理する関数
 *
 * @param data -データベースの返り値
 */
const approvalColumnTextProcess = (data: CopyOutListType[]) => {
  const result: ApprovalColumnTableDataItem[] = data.map((item) => {
    return {
      ...item,
      textRenderList: Array.from({ length: 4 }, (_, index) => {
        index++
        const leftwidth = item[TEXTSTR + index + WIDTHSTR]
        const rightWidth = item[DAYSTR + index + WIDTHSTR]
        return {
          leftText: item[TEXTSTR + index + KNJSTR],
          rightText: item[DAYSTR + index + KNJSTR],
          fontsize: item[TEXTSTR + index + FONTSTR],
          leftWidth: leftwidth + WIDTH_PX,
          rightWidth: rightWidth + WIDTH_PX,
          leftFlg: leftwidth !== STRING_0,
          rightFlg: rightWidth !== STRING_0,
        }
      }),
    }
  })
  return result
}

/**
 * 「確定」ボタン押下
 */
const onConfirmBtn = () => {
  // 承認欄データがない場合、操作なし
  if (!approvalColumnSelectedItem.value.value) return
  // 選択情報値戻り
  const findItem = localOneway.approvalColumnMo01334.items.find(
    (item) => item.id === approvalColumnSelectedItem.value.value
  )
  emit('update:modelValue', findItem)
  onClickCloseBtn()
}

/**
 * 閉じるボタン押下時
 */
const onClickCloseBtn = (): void => {
  setState({ isOpen: false })
}

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * mo00024閉じるかどうかの監視
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      onClickCloseBtn()
    }
  }
)

/**
 *自事業者のみ選択の監視
 */
watch(
  () => mo00018ModelValue.value,
  () => {
    void getInitDataInfo()
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet>
        <!-- 自事業者欄表示 -->
        <!-- 親画面.帳票毎の保持 = 1(保存単位：共有する) の場合、自事業者欄表示 -->
        <c-v-row
          v-if="localOneway.or52916.shoninFlg === '1'"
          no-gutters
          class="mb-2"
        >
          <c-v-col>
            <base-mo00018
              v-model="mo00018ModelValue"
              :oneway-model-value="localOneway.selfBusinessOperatorMo00018"
            />
          </c-v-col>
        </c-v-row>
        <c-v-row no-gutters>
          <c-v-col
            cols="12"
            class="table-header"
          >
            <!-- 承認欄の複写一覧 -->
            <base-mo01334
              v-model="approvalColumnSelectedItem"
              class="list-wrapper"
              hide-default-footer
              :oneway-model-value="localOneway.approvalColumnMo01334"
            >
              <template #[`item.svJigyoId`]="{ item }">
                <div class="text-right">{{ item.svJigyoId }}</div>
              </template>
            </base-mo01334>
          </c-v-col>
        </c-v-row>
        <c-v-row
          no-gutters
          class="textcontentContainer mt-2"
        >
          <c-v-col cols="12">
            <template
              v-for="(item, index) in approvalColumnTextList?.textRenderList"
              :key="index"
            >
              <div v-show="index < Number(approvalColumnTextList?.dispKbn)">
                <div class="d-flex pt-2 height-30">
                  <!-- 承認欄下線無プレビューラベル -->
                  <div
                    v-show="item.leftFlg"
                    :style="{ width: item.leftWidth }"
                    class="overflow-hidden text-no-wrap"
                    :class="{
                      'font-size-9': item.fontsize === FONT_SIZE_9,
                      'font-size-10': item.fontsize === FONT_SIZE_10,
                      'font-size-11': item.fontsize === FONT_SIZE_11,
                      'font-size-12': item.fontsize === FONT_SIZE_12,
                    }"
                  >
                    {{ item.leftText }}
                  </div>

                  <!-- 承認欄下線有プレビューラベル -->
                  <div
                    v-show="item.rightFlg"
                    :style="{ width: item.rightWidth }"
                    class="bordered-black overflow-hidden text-no-wrap"
                    :class="{
                      'font-size-9': item.fontsize === FONT_SIZE_9,
                      'font-size-10': item.fontsize === FONT_SIZE_10,
                      'font-size-11': item.fontsize === FONT_SIZE_11,
                      'font-size-12': item.fontsize === FONT_SIZE_12,
                    }"
                  >
                    {{ item.rightText }}
                  </div>
                </div>
              </div>
            </template>
          </c-v-col>
        </c-v-row>
      </c-v-sheet>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="mo00611Oneway"
          class="mr-2"
          @click="onClickCloseBtn"
        >
        </base-mo00611>
        <!-- 確定ボタン-->
        <base-mo00609
          :oneway-model-value="mo00609Oneway"
          @click="onConfirmBtn"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';
.textcontentContainer {
  border: 1px solid rgb(var(--v-theme-black-200));
  width: 1218px;
  padding: 0 8px 8px 8px;
  height: 130px;
  .height-30 {
    height: 30px;
    line-height: 30px;
  }
}
.font-size-9 {
  font-size: 9px;
}

.font-size-10 {
  font-size: 10px;
}

.font-size-11 {
  font-size: 11px;
}

.font-size-12 {
  font-size: 12px;
}
.bordered-black {
  border-bottom: 1px solid rgb(var(--v-theme-black-800));
}
</style>
