<script setup lang="ts">
/**
 * Or10600:有機体:(月間・年間表パターン)ダイアログ
 * GUI03248_月間・年間表パターン画面(タイトル)
 *
 * @description
 *
 * <AUTHOR>
 */
import { computed, nextTick, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or01533Const } from '../Or01533/Or01533.constants'
import { Or10600Const } from './Or10600.constants'
import type { Or10600StateType, AsyncFunction } from './Or10600.type'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Or10600OnewayType } from '~/types/cmn/business/components/Or10600Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { OrX0054OnewayType, OrX0054Type } from '~/types/cmn/business/components/OrX0054Type'
import { UPDATE_KBN } from '~/constants/classification-constants'
import { useScreenOneWayBind, useSetupChildProps } from '~/composables/useComponentVue'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { useScreenStore } from '~/stores/session/screen'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { OrX0053OnewayType, OrX0053Type } from '~/types/cmn/business/components/OrX0053Type'
import type {
  Or21814EventType,
  Or21814OnewayType,
} from '~/components/base-components/organisms/Or21814/Or21814.type'
import type { NavControlJsonType } from '~/types/business/core/DefinitionJsonType'
import { OrX0053Const } from '~/components/custom-components/organisms/OrX0053/OrX0053.constants'
import { OrX0054Const } from '~/components/custom-components/organisms/OrX0054/OrX0054.constants'

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or10600OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

const defaultOnewayModelValue: Or10600OnewayType = {
  mstKbn: '2',
}
const or01533 = ref({ uniqueCpId: Or01533Const.CP_ID(0) })
const orX0053 = ref({ uniqueCpId: OrX0053Const.CP_ID(0) })
const orX0054 = ref({ uniqueCpId: OrX0054Const.CP_ID(0) })
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
const or21813 = ref({ uniqueCpId: Or21813Const.CP_ID(0) })

// 画面.画面タイトル
let titleId: unknown
switch (props.onewayModelValue.mstKbn) {
  case Or10600Const.DEFAULT.MSTKBN_LIST.DAILY_PARTTEN:
    titleId = t('label.daily-table-pattern')
    break

  case Or10600Const.DEFAULT.MSTKBN_LIST.WEEK_PARTTEN:
    titleId = t('label.week-table-pattern')
    break
  case Or10600Const.DEFAULT.MSTKBN_LIST.MONTHYEAR_PARTTEN:
    titleId = t('label.monthly-yearly-table-pattern')
    break
  default:
    break
}

// ダイアログ表示フラグ
const showInfoDialog = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})
const showErrDialog = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})

const localOneway = reactive({
  or10600: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // 情報ダイアログ
  mo00024Oneway: {
    width: '950px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or10600',
      toolbarTitle: titleId,
      toolbarName: 'Or10600ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  // タブ
  mo00043OneWay: {
    tabItems: [
      { id: Or10600Const.TAB.TAB_ID_TITLE, title: t('label.title') },
      { id: Or10600Const.TAB.TAB_ID_SET, title: t('label.settings') },
      { id: Or10600Const.TAB.TAB_ID_GROUP, title: t('label.group') },
    ],
  } as Mo00043OnewayType,
  // 閉じる
  mo00611OneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 保存
  mo00609OneWay: {
    btnLabel: t('btn.save'),
  } as Mo00609OnewayType,
  // タブ：グループ
  orX0053OneWay: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  } as OrX0053OnewayType,
  // タブ：タイトル
  orX0054OneWay: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  } as OrX0054OnewayType,
})

const local = reactive({
  mo00043: { id: Or10600Const.TAB.TAB_ID_TITLE } as Mo00043Type,
  orX0053: {
    editFlg: false,
  } as OrX0053Type,
  orX0054: {
    editFlg: false,
  } as OrX0054Type,
})

/**************************************************
 * 変数定義
 **************************************************/
const mo00024 = ref<Mo00024Type>({
  isOpen: Or10600Const.DEFAULT.IS_OPEN,
})
const or01533Ref = ref<{ insert: AsyncFunction }>()
// OrX0054 Ref
const orX0054Ref = ref<{ save: AsyncFunction }>()
// OrX0053 Ref
const orX0053Ref = ref<{ save: AsyncFunction }>()
// ナビゲーション制御領域のいずれかの編集フラグがON
useSystemCommonsStore().setShowEditDiscardDialog(useScreenStore().isEditNavControl())
const isEdit = computed(() => {
  return useSystemCommonsStore().getShowEditDiscardDialog
})
// 表示順が数値
let isNumber = false
// TAB
const isNewTab = { newTabFlg: false, newTab: '', oldTab: local.mo00043.id }
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or10600StateType>({
  cpId: Or10600Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or10600Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or01533Const.CP_ID(0)]: or01533.value,
  [OrX0054Const.CP_ID(0)]: orX0054.value,
  [OrX0053Const.CP_ID(0)]: orX0053.value,
  [Or21813Const.CP_ID(0)]: or21813.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
})

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    // 組織dialog自動クローズを手動判定に変更
    mo00024.value.isOpen = true
    if (!newValue) {
      await close()
    }
  }
)

/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    await nextTick()
    if (resolvePromise !== undefined && newValue !== undefined) {
      resolvePromise(newValue)
    }
    return
  }
)

// メニュー切替
watch(
  () => local.mo00043.id,
  async (newValue) => {
    if (isNewTab.oldTab === newValue) {
      return
    }
    isNewTab.newTab = newValue
    // 変更がない場合、画面データあるかどうかを判定する。
    // タブを設定に切り替え
    if (
      isNewTab.oldTab === Or10600Const.TAB.TAB_ID_SET ||
      (!isEditGroup.value && isNewTab.oldTab === Or10600Const.TAB.TAB_ID_GROUP) ||
      (!isEditTitle.value && isNewTab.oldTab === Or10600Const.TAB.TAB_ID_TITLE)
    ) {
      isNewTab.newTabFlg = false
      if (
        isNewTab.oldTab === Or10600Const.TAB.TAB_ID_TITLE &&
        newValue === Or10600Const.TAB.TAB_ID_SET
      ) {
        if (!Array.isArray(local.orX0054.titleList) || local.orX0054.titleList.length === 0) {
          // 処理終了,タブ切替の阻止
          local.mo00043.id = Or10600Const.TAB.TAB_ID_TITLE
          await openErrorDialog(t('message.e-cmn-41523'))
        } else {
          isNewTab.oldTab = newValue
        }
      } else {
        isNewTab.oldTab = newValue
      }
    } else {
      await nextTick()
      // 画面入力データ変更があるかどうかを判定する
      if (!isNewTab.newTabFlg) {
        isNewTab.newTabFlg = false
        // 処理終了,タブ切替の阻止
        local.mo00043.id = isNewTab.oldTab
        // 変更がある場合、確認ダイアログを表示する。
        const rs = await confirmMsg()
        if (rs.firstBtnClickFlg) {
          if (isNumber) {
            if (await save()) {
              isNewTab.oldTab = newValue
              local.mo00043.id = isNewTab.newTab
            } else {
              // 処理終了,タブ切替の阻止
              return
            }
          }
        } else if (rs.secondBtnClickFlg) {
          isNewTab.oldTab = newValue
          isNewTab.newTabFlg = true
          local.mo00043.id = isNewTab.newTab
        } else if (rs.thirdBtnClickFlg) {
          // 処理終了,タブ切替の阻止
          return
        }
      }
    }
  }
)

// ダイアログResolve
let resolvePromise: (value: Or21814EventType) => void

/**
 * 確定メッセージを開きます
 *
 * @param uniqueCpId -uniqueCpId
 *
 * @param state -Or21814OnewayType
 */
function openConfirmDialog(uniqueCpId: string, state: Or21814OnewayType) {
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise<Or21814EventType>((resolve) => {
    resolvePromise = resolve
  })
}

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
async function close() {
  // 画面入力データ変更があるかどうかを判定する
  // 変更がない場合、画面を閉じる。
  if (!isEdit.value) {
    setState({ isOpen: false })
  } else {
    const rs = await confirmMsg()
    if (rs.firstBtnClickFlg) {
      if (isNumber) {
        if (await save()) {
          setState({ isOpen: false })
        } else {
          return
        }
      }
    } else if (rs.secondBtnClickFlg) {
      setState({ isOpen: false })
    } else if (rs.thirdBtnClickFlg) {
      return
    }
  }
}

/**
 * メッセージ処理
 *
 * @description
 * 。
 */
async function confirmMsg() {
  const dataArray = local.orX0054.titleList.filter(
    (data) => data.updateKbn !== UPDATE_KBN.DELETE && Number.isNaN(Number(data.seq))
  )
  let rs: Promise<Or21814EventType>
  // 画面入力データ変更がある、且つ、画面.表示順が数値以外の場合
  if (dataArray.length > 0) {
    isNumber = false
    rs = showOr21814MsgTwoBtn(t('message.i-cmn-11167', [titleId]))
  } else {
    //画面入力データ変更がある場合、且つ、画面.表示順が数値の場合
    isNumber = true
    rs = showOr21814MsgThreeBtn(t('message.i-cmn-10430'))
  }
  return rs
}

/**
 * 保存
 */
async function save() {
  if (isNewTab.oldTab === Or10600Const.TAB.TAB_ID_TITLE) {
    const saveTitleResult = await orX0054Ref.value?.save()
    if (saveTitleResult?.isBreak) {
      if (saveTitleResult.error) {
        await openErrorDialog(saveTitleResult.msg)
        return false
      }
      return false
    }
    return true
  } else if (isNewTab.oldTab === Or10600Const.TAB.TAB_ID_SET) {
    const saveTitleResult = await or01533Ref.value?.insert()
    if (saveTitleResult?.isBreak) {
      if (saveTitleResult.error) {
        await openErrorDialog(saveTitleResult.msg)
        return false
      }
      return false
    }
    return true
  } else if (isNewTab.oldTab === Or10600Const.TAB.TAB_ID_GROUP) {
    const saveGroupResult = await orX0053Ref.value?.save()
    if (saveGroupResult?.isBreak) {
      if (saveGroupResult.error) {
        await openErrorDialog(saveGroupResult.msg)
        return false
      }
      return false
    }
    return true
  }
}

/**
 * メッセージの開閉
 *
 * @param infomsg - Message
 */
async function showOr21814MsgThreeBtn(infomsg: string) {
  const rs = await openConfirmDialog(or21814.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: infomsg,
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'destroy1',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'normal3',
    thirdBtnLabel: t('btn.cancel'),
  })
  return rs
}

/**
 * メッセージの開閉
 *
 * @param infomsg - Message
 */
async function showOr21814MsgTwoBtn(infomsg: string) {
  const rs = await openConfirmDialog(or21814.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: infomsg,
    firstBtnType: 'normal1',
    firstBtnLabel: t('btn.yes'),
    secondBtnType: 'destroy1',
    secondBtnLabel: t('btn.no'),
    thirdBtnType: 'blank',
  })
  return rs
}

/**
 * errorダイアログを閉じたタイミングで結果を返却
 *
 * @param msg - msg
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
async function openErrorDialog(msg: string): Promise<string> {
  // 選択行削除確認ダイアログを開く
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      isOpen: true,
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: msg,
      firstBtnType: 'blank',
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: 'blank',
    },
  })

  /**
   * 選択行削除確認ダイアログを閉じたタイミングで結果を返却
   *
   * @returns ダイアログの選択結果（yes, no）
   */
  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813.value.uniqueCpId)

        let result = Or10600Const.DEFAULT.DIALOG_RESULT_YES

        if (event?.firstBtnClickFlg) {
          result = Or10600Const.DEFAULT.DIALOG_RESULT_YES
        }

        // 選択行削除確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

// 変更されたリスニングのコンポーネントIDリスト
const groupWatchedComponents = ref<string[]>([orX0053.value.uniqueCpId])
const isEditGroup = computed(() => {
  return isEditNavControl(groupWatchedComponents.value)
})

// 変更されたリスニングのコンポーネントIDリスト
const titleWatchedComponents = ref<string[]>([orX0054.value.uniqueCpId])
const isEditTitle = computed(() => {
  return isEditNavControl(titleWatchedComponents.value)
})

/**
 * いずれかの編集フラグがONになっているかチェックする
 *
 * @param components - 変更のリスニングが必要なリスト
 *
 * @returns いずれかの編集有無
 */
function isEditNavControl(components: string[]) {
  const screenStore = useScreenStore()
  const screenNavControl = screenStore.getScreenNavControl<NavControlJsonType>()

  // ナビゲーション制御領域を走査
  for (const key in screenNavControl) {
    if (screenNavControl[key] === screenNavControl.components) {
      // ナビゲーション制御領域 - コンポーネント毎の領域を走査
      for (const cpKey in screenNavControl[key]) {
        if (components.includes(cpKey)) {
          if (screenNavControl[key][cpKey].editFlg) {
            // コンポーネント毎の編集フラグがON
            return true
          }
          if (screenNavControl[key][cpKey].items) {
            // ナビゲーション制御領域 - コンポーネント毎の領域にitemsが存在する場合は走査
            for (const itemKey in screenNavControl[key][cpKey].items) {
              if (screenNavControl[key][cpKey].items[itemKey].editFlg) {
                // コンポーネント - itemsの編集フラグがON
                return true
              }
            }
          }
        }
      }
    }
  }

  return false
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
    style="padding: 8px !important"
  >
    <template #cardItem>
      <c-v-row>
        <c-v-col>
          <base-mo00043
            v-model="local.mo00043"
            :oneway-model-value="localOneway.mo00043OneWay"
          ></base-mo00043>
          <!-- タブ switch -->
          <c-v-window v-model="local.mo00043.id">
            <c-v-window-item value="title">
              <!-- タブ：タイトル -->
              <g-custom-or-x-0054
                v-if="local.mo00043.id === Or10600Const.TAB.TAB_ID_TITLE"
                ref="orX0054Ref"
                v-model="local.orX0054"
                v-bind="orX0054"
                :oneway-model-value="localOneway.orX0054OneWay"
                :parent-unique-cp-id="props.uniqueCpId"
              ></g-custom-or-x-0054>
            </c-v-window-item>
            <c-v-window-item value="settings">
              <g-custom-Or01533
                v-if="local.mo00043.id === Or10600Const.TAB.TAB_ID_SET"
                ref="or01533Ref"
                v-bind="or01533"
                :unique-cp-id="or01533.uniqueCpId"
              />
            </c-v-window-item>
            <c-v-window-item value="group">
              <!-- タブ：グループ -->
              <g-custom-or-x-0053
                v-if="local.mo00043.id === Or10600Const.TAB.TAB_ID_GROUP"
                ref="orX0053Ref"
                v-model="local.orX0053"
                v-bind="orX0053"
                :oneway-model-value="localOneway.orX0053OneWay"
                :parent-unique-cp-id="props.uniqueCpId"
              ></g-custom-or-x-0053>
            </c-v-window-item>
          </c-v-window>
        </c-v-col>
      </c-v-row>
    </template>
    <!-- フッター -->
    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          @click="close"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <base-mo00609
          v-bind="localOneway.mo00609OneWay"
          class="mx-2"
          @click="save"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.save')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showInfoDialog"
    v-bind="or21814"
  />
  <!-- Or21813:有機体:エラーダイアログ -->
  <g-base-or21813
    v-if="showErrDialog"
    v-bind="or21813"
  />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table.scss';
</style>
