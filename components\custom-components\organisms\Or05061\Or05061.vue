<script setup lang="ts">
/**
 * Or05061:(見通し)見通しリスト
 * GUI00916_見通し
 *
 * @description
 * (見通し)見通しリスト
 *
 * <AUTHOR> NGUYEN MANH HUNG
 */
import { useI18n } from 'vue-i18n'

import { ref, reactive, computed, nextTick, watch } from 'vue'
import { Or05061Const } from './Or05061.constants'
import { useScreenTwoWayBind, useSetupChildProps, useScreenStore } from '#imports'
import type { Or05061OnewayType, Or05061Type } from '~/types/cmn/business/components/Or05061Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo01280OnewayType } from '~/types/business/components/Mo01280Type'
import { UPDATE_KBN } from '~/constants/classification-constants'
import type { MitosiSyosaiType } from './Or05061.type'
import type { Mo01280Type } from '~/types/business/components/Mo01280Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type { Or21814EventType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or52823Logic } from '../Or52823/Or52823.logic'
import type { Or52823Type } from '~/types/cmn/business/components/Or52823Type'
import { useScreenUtils } from '~/utils/useScreenUtils'
import { Or52823Const } from '../Or52823/Or52823.constants'
import type { Or27604OnewayType, Or27604Type } from '~/types/cmn/business/components/Or27604Type'
import { Or27604Logic } from '../Or27604/Or27604.logic'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { Or05063Logic } from '../Or05063/Or05063.logic'
import { Or05063Const } from '../Or05063/Or05063.constants'
import type { Or51775OnewayType, Or51775Type } from '~/types/cmn/business/components/Or51775Type'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import { Or05656Const } from '../Or05656/Or05656.constants'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or29163Logic } from '../Or29163/Or29163.logic'
import type { Or29163OneWayType } from '~/types/cmn/business/components/Or29163Type'
import { Or29163Const } from '../Or29163/Or29163.constants'
import type { Or05656OnewayType } from '~/types/cmn/business/components/Or05656Type'
import type { Or05658Type } from '~/types/cmn/business/components/Or05658Type'
import { Or27604Const } from '../Or27604/Or27604.constants'
import type { Mo01274Type } from '../Or26837/Or26837.type'
import type { Mo01274OnewayType } from '~/types/business/components/Mo01274Type'
/**
 * useI18n
 */
const { t } = useI18n()

const or21814 = ref({ uniqueCpId: '' })
/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: Or05061OnewayType[]
  modelValue: Or05061Type[]
  uniqueCpId: string
  parentUniqueCpId: string
}
/**
 * システム共有情報取得
 */
const systemCommonsStore = useSystemCommonsStore()
/**
 * Propsの定義
 */
const props = defineProps<Props>()

/** テーブルの参照 */
const dataTableRef = ref()
/**
 * Or52823_子コンポーネントの状態
 */
const or52823 = ref({
  uniqueCpId: '',
  // 親画面.画面ID
  tabId: '',
})
/**
 * or27604
 */
const or27604 = ref({ uniqueCpId: '' })
/**
 * or05063
 */
const or05063 = ref({ uniqueCpId: '' })
/**
 * 子コンポーネントのプロパティを保持する変数
 * - uniqueCpId: 子コンポーネントの一意のID
 */
const or51775 = ref({ uniqueCpId: '' })
/**
 * Or29163
 */
const or29163 = ref({ uniqueCpId: '' })
/**
 * or05656
 */
const or05656 = ref({ uniqueCpId: '' })
/**
 * Or52823_表示順変更課題整理総括モーダル
 */
const isShowDialogOr52823 = computed(() => {
  return Or52823Logic.state.get(or52823.value.uniqueCpId)?.isOpen ?? false
})
/**
 * ダイアログ表示フラグ
 * - Or51775のダイアログ開閉状態を取得
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})
/**
 * Or29163ダイアログの表示状態
 */
const showDialogOr29163 = computed(() => {
  // Or29163のダイアログ開閉状態
  return Or29163Logic.state.get(or29163.value.uniqueCpId)?.isOpen ?? false
})
/**
 *  ダイアログ表示フラグ
 */
const showDialogOr27604 = computed(() => {
  // Or27604のダイアログ開閉状態
  return Or27604Logic.state.get(or27604.value.uniqueCpId)?.isOpen ?? false
})
/**
 *  ダイアログ表示フラグ
 */
const showDialogOr05063 = computed(() => {
  // Or05063のダイアログ開閉状態
  return Or05063Logic.state.get(or05063.value.uniqueCpId)?.isOpen ?? false
})

/**
 * コンポーネント固有処理
 */
const or27604Data: Or27604OnewayType = {
  /** 事業者ID */
  svJigyoId: systemCommonsStore.getSvJigyoId!,
  /** 利用者ID */
  userId: systemCommonsStore.getUserId!,
  /** 種別ID */
  syubetsuId: systemCommonsStore.getSyubetu!,
  /** 施設ID */
  shisetuId: systemCommonsStore.getShisetuId!,
  /** 期間管理フラグ */
  kanriFlg: '',
  /** 期間ID */
  sc1Id: '',
}

/**************************************************
 * Pinia
 **************************************************/
/**
 * 画面ユーティリティ関数の取得
 */
const { setChildCpBinds, getChildCpBinds } = useScreenUtils()
/**
 * 画面ストアの取得
 */
const screenStore = useScreenStore()
/**
 * ページコンポーネント情報の取得
 */
const pageComponent = screenStore.screen().supplement.pageComponent
/**
 * Or52823_子コンポーネントの一意なIDを設定
 */
or52823.value.uniqueCpId = pageComponent.uniqueCpId
/**
 * useScreenTwoWayBind
 */
const { refValue } = useScreenTwoWayBind<Or05061Type[]>({
  cpId: Or05061Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or52823Const.CP_ID(0)]: or52823.value,
  [Or05063Const.CP_ID(0)]: or05063.value,
  [Or51775Const.CP_ID(0)]: or51775.value,
  [Or29163Const.CP_ID(0)]: or29163.value,
  [Or05656Const.CP_ID(0)]: or05656.value,
  [Or27604Const.CP_ID(0)]: or27604.value,
})
const selectedRow = ref(-1)
const defaultOr05061 = ref({
  kikanFlg: '',
  kikanObj: {
    sc1Id: '',
    startYmd: '',
    endYmd: '',
    modifiedCnt: '',
    kikanNo: '',
    kikanTotalCnt: '',
  },
  idParent: '',
})
/**
 * ローカルのOnewayオブジェクト
 */
const localOneway = reactive({
  or05061: { ...defaultOr05061.value, ...props.onewayModelValue } as Or05061OnewayType,
  mo00009OneWay: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,

  //カラム5
  mo01280NoteOneWay: {
    maxLength: 4000,
  } as Mo01280OnewayType,
  /**
   * ローカル変数
   */
  or51775Oneway: {
    title: '',
    screenId: '',
    bunruiId: '',
    t1Cd: '750',
    t2Cd: '',
    t3Cd: '0',
    tableName: 'cpn_tuc_kss3',
    columnName: '',
    assessmentMethod: systemCommonsStore.getAuthzOtherCaremanager?.assessmentMethod ?? '',
    inputContents: '',
    userId: systemCommonsStore.getUserId ?? '',
  } as Or51775OnewayType,
  or29163: {
    inhibitorsSection: {
      youin1Knj: {
        value: '',
      },
      youin2Knj: {
        value: '',
      },
      youin3Knj: {
        value: '',
      },
      youin4Knj: {
        value: '',
      },
      youin5Knj: {
        value: '',
      },
      youin6Knj: {
        value: '',
      },
    },

    /**
     * 項目リスト
     */
    koumokuList: [
      {
        koumoku1Id: '1',
        sort: '1',
        stopFlg: '0',
        koumoku1Id2: '1',
        koumoku2Id: '1',
        youshikiId2: '1',
        tekiyouFlg: '1',
        sort2: '1',
        stopFlg2: '0',
        showFlg: '1',
        youshikiId: '1',
        koumoku1Knj: '移動',
        koumoku2Knj: '室内移動',
        col2: { value: '1' },
        col3: { values: ['1', '2', '3'] },
        col4: { value: '0' },
        col5: { value: '' },
      },
      {
        koumoku1Id: '1',
        sort: '1',
        stopFlg: '0',
        koumoku1Id2: '1',
        koumoku2Id: '2',
        youshikiId2: '1',
        tekiyouFlg: '1',
        sort2: '2',
        stopFlg2: '0',
        showFlg: '1',
        youshikiId: '1',
        koumoku1Knj: '移動',
        koumoku2Knj: '屋外移動',
        col2: { value: '2' },
        col3: { values: ['4', '5', '6'] },
        col4: { value: '1' },
        col5: { value: 'xyz' },
      },
    ],
  } as Or29163OneWayType,
  mo01274OneWay: {
    maxlength: 2,
    showItemLabel: false,
  } as Mo01274OnewayType,
})

/**
 * ヘッダーの設定
 */
const headers = [
  {
    title: t('label.outlook*5'),
    key: 'mitosiKnj',
    width: '200px',
    sortable: false,
  },
  {
    title: t('label.life-whole-solution-issues-needs-suggestion'),
    key: 'kadaiKnj',
    width: '200px',
    sortable: false,
  },
  {
    title: t('label.*6'),
    key: 'yusenNo',
    width: '10px',
    sortable: false,
  },
]
const local = reactive({
  keyTable: '' as string,
  or52823: [] as Or52823Type[],
  or27604: [] as Or27604Type[],
  or51775: { modelValue: '' } as Or51775Type,
})
/**
 * 削除されていないアイテムのみを表示する
 * @type {ComputedRef<DataTableType[]>}
 * @returns {DataTableType[]} - 削除されていないアイテムのグループ配列
 */
const visibleGroupedItems = computed(() => {
  if (!refValue?.value) return []
  if (selectedRow.value === -1 && refValue.value.length > 0) {
    selectedRow.value = 0
  }
  return refValue.value
    .map((item, index) => ({
      ...item,
      index,
    }))
    .filter((item) => item.updateKbn !== UPDATE_KBN.DELETE)
})
const visibleGroupedItemsDelete = computed(() => {
  if (!refValue?.value) return []
  return refValue.value
    .map((item, index) => ({
      ...item,
      index,
    }))
    .filter((item) => item.updateKbn === UPDATE_KBN.DELETE)
})
/**
 * テーブル内の総アイテム数
 * @returns {number} - 表示可能なアイテムの総数
 */
const totalItemCount = computed(() => {
  return visibleGroupedItems.value.length
})

/**
 * Lấy danh sách visible items
 */
const getVisibleItems = () => {
  if (!refValue.value) return []
  return refValue.value
    .map((item, index) => ({ ...item, index }))
    .filter((item) => item.updateKbn !== UPDATE_KBN.DELETE)
}
/**
 * Tìm vị trí của selectedRow trong danh sách visible
 */
const getCurrentVisibleIndex = () => {
  const visibleItems = getVisibleItems()
  return visibleItems.findIndex((item) => item.index === selectedRow.value)
}

/**
 * Scroll đến row được select
 */
const scrollToSelectedRow = () => {
  nextTick(() => {
    if (selectedRow.value >= 0) {
      const selectedElement = document.getElementById(selectedRow.value.toString())
      if (selectedElement) {
        selectedElement.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
        })
      }
    }
  })
}

/**
 * Tạo item mới
 */
const createNewItem = (): Or05061Type => ({
  mitosiKnj: { value: '' } as Mo01280Type,
  kadaiKnj: { value: '' } as Mo01280Type,
  yusenNo: { value: '' } as Mo01280Type,
  kss3Id: '',
  kss1Id: '',
  modifiedCnt: '',
  sort: '',
  updateKbn: UPDATE_KBN.CREATE,
})

async function createItem() {
  const newItem = createNewItem()

  refValue.value?.push(newItem)
  await nextTick()
  selectedRow.value = (refValue.value?.length ?? 0) - 1
  scrollToSelectedRow()
}

async function deleteItem() {
  if (refValue.value && selectedRow.value >= 0 && selectedRow.value < refValue.value.length) {
    const dialogResult = await openInfoDialog(t('message.i-cmn-10219'))
    if (dialogResult?.secondBtnClickFlg === true) {
      return
    }
    const visibleItems = getVisibleItems()
    const currentIndex = getCurrentVisibleIndex()

    // Đánh dấu xóa
    refValue.value[selectedRow.value].updateKbn = UPDATE_KBN.DELETE

    // Select item tiếp theo
    if (currentIndex >= 0) {
      selectedRow.value =
        currentIndex === visibleItems.length - 1
          ? currentIndex > 0
            ? visibleItems[currentIndex - 1].index
            : -1
          : visibleItems[currentIndex + 1].index
    }
    scrollToSelectedRow()
  }
}

function moveUp() {
  if (refValue.value && selectedRow.value > 0) {
    const visibleItems = getVisibleItems()
    const currentIndex = getCurrentVisibleIndex()

    if (currentIndex > 0) {
      selectedRow.value = visibleItems[currentIndex - 1].index
      scrollToSelectedRow()
    }
  }
}

function moveDown() {
  if (refValue.value && selectedRow.value >= 0) {
    const visibleItems = getVisibleItems()
    const currentIndex = getCurrentVisibleIndex()

    if (currentIndex >= 0 && currentIndex < visibleItems.length - 1) {
      selectedRow.value = visibleItems[currentIndex + 1].index
      scrollToSelectedRow()
    }
  }
}

function duplicateItem() {
  if (refValue.value && selectedRow.value >= 0 && selectedRow.value < refValue.value.length) {
    const selectedItem = refValue.value[selectedRow.value]

    // Tạo item mới với nội dung được sao chép
    const newItem: Or05061Type = {
      mitosiKnj: { value: selectedItem.mitosiKnj?.value || '' } as Mo01280Type,
      kadaiKnj: { value: selectedItem.kadaiKnj?.value || '' } as Mo01280Type,
      yusenNo: { value: selectedItem.yusenNo?.value || '' } as Mo01280Type,
      kss3Id: '',
      kss1Id: '',
      modifiedCnt: '',
      sort: '',
      updateKbn: UPDATE_KBN.CREATE,
    }

    // Chèn item mới vào vị trí sau item đang được chọn
    refValue.value.splice(selectedRow.value + 1, 0, newItem)

    // Chọn item mới được chèn
    selectedRow.value = selectedRow.value + 1

    scrollToSelectedRow()
  }
}

function insertItem() {
  if (refValue.value && selectedRow.value >= 0) {
    const newItem = createNewItem()

    // Chèn item mới vào vị trí trước item đang được chọn
    refValue.value.splice(selectedRow.value, 0, newItem)

    scrollToSelectedRow()
  }
}

function onClickItem(index: number) {
  selectedRow.value = index
}
/**
 * エラーダイアログをオープンする
 *
 * @param dialogText - メッセージ
 */
function openInfoDialog(dialogText: string): Promise<Or21814EventType | undefined> {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: dialogText,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })
  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(event)
      },
      { once: true }
    )
  })
}

function sortBy() {
  local.or52823 = visibleGroupedItems.value.map((item) => ({
    ...item,
    displayOrder: '',
    kadaiKnj: item.kadaiKnj?.value || '',
    mitosiKnj: item.mitosiKnj?.value || '',
    yusenNo: item.yusenNo?.value || '',
    updateKbn: item.updateKbn,
  }))
  setChildCpBinds(props.uniqueCpId, {
    Or52823: {
      twoWayValue: local.or52823,
    },
  })

  Or52823Logic.state.set({
    uniqueCpId: or52823.value.uniqueCpId,
    state: { isOpen: true },
  })
}
function handleChangeOr52823(newVal: Or52823Type[]) {
  // Lấy các item đã bị xóa
  const deletedItems = visibleGroupedItemsDelete.value

  // Chuyển đổi newVal về Or05061Type nếu cần
  const merged = [
    ...newVal.map((item) => ({
      ...item,
      mitosiKnj: { value: item.mitosiKnj } as Mo01280Type,
      kadaiKnj: { value: item.kadaiKnj } as Mo01280Type,
      yusenNo: { value: item.yusenNo } as Mo01280Type,
    })),
    ...deletedItems,
  ]
  // Gán lại vào refValue
  refValue.value = merged as Or05061Type[]
}
function assessment() {
  // 親画面.期間管理フラグを設定する。
  or27604Data.kanriFlg = localOneway.or05061.kikanFlg
  or27604Data.sc1Id = localOneway.or05061.kikanObj.sc1Id
  // Or27604のダイアログ開閉状態を更新する
  Or27604Logic.state.set({
    uniqueCpId: or27604.value.uniqueCpId,
    state: { isOpen: true },
  })
}
async function handleChangeOr27604(newVal: Or27604Type[]) {
  if (refValue.value) {
    newVal.forEach(async (item: Or27604Type) => {
      const newItem = createNewItem()
      newItem.kadaiKnj = { value: item.issues } as Mo01280Type
      refValue?.value?.push(newItem)
    })

    await nextTick()
    selectedRow.value = refValue.value.length - 1
    scrollToSelectedRow()
  }
}
// GUI00928
function statusFactReference() {
  const data2 = getChildCpBinds(props.parentUniqueCpId, {
    Or05656: { cpPath: 'Or05656', twoWayFlg: true },
  })
  const or05656Data = data2.Or05656.twoWayBind?.value as Or05656OnewayType[][]
  localOneway.or29163.koumokuList = or05656Data
  const data = getChildCpBinds(props.parentUniqueCpId ?? '', {
    Or05658: { cpPath: 'Or05658', twoWayFlg: true },
  })
  const or05658 = data.Or05658.twoWayBind?.value as Or05658Type
  localOneway.or29163.inhibitorsSection = {
    youin1Knj: {
      value: or05658.youin1Knj || '',
    },
    youin2Knj: {
      value: or05658.youin2Knj || '',
    },
    youin3Knj: {
      value: or05658.youin3Knj || '',
    },
    youin4Knj: {
      value: or05658.youin4Knj || '',
    },
    youin5Knj: {
      value: or05658.youin5Knj || '',
    },
    youin6Knj: {
      value: or05658.youin6Knj || '',
    },
  }
  // Or29163のダイアログ開閉状態を更新する
  Or29163Logic.state.set({
    uniqueCpId: or29163.value.uniqueCpId,
    state: { isOpen: true },
  })
}
function notes() {
  Or05063Logic.state.set({
    uniqueCpId: or05063.value.uniqueCpId,
    state: { isOpen: true },
  })
}
/**
 * Or51775のダイアログ確認処理
 */
const handleConfirm51775 = (data: Or51775ConfirmType) => {
  if (!refValue.value || selectedRow.value < 0) return

  const currentItem = refValue.value[selectedRow.value] as any
  const field = currentItem[local.keyTable]

  if (!field) return

  currentItem.updateKbn = UPDATE_KBN.UPDATE
  // タイプによって処理を分ける
  if (data.type === Or05061Const.STRING_1) {
    // タイプが1の場合：完全に置き換え
    field.value = data.value
  } else {
    // タイプが1以外の場合：既存の値に追加
    field.value = field.value + data.value
  }
}
function handleClickMo00009(key: string) {
  local.keyTable = key
  if (key == 'mitosiKnj') {
    localOneway.or51775Oneway.title = t('label.prospect_tab_title')
    localOneway.or51775Oneway.columnName = 'mitosi_knj'
    localOneway.or51775Oneway.t2Cd = '3'
  } else {
    localOneway.or51775Oneway.title = t('label.issues')
    localOneway.or51775Oneway.columnName = 'kadai_knj'
    localOneway.or51775Oneway.t2Cd = '4'
  }

  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}
/**
 * Handle field changes
 */
const handleFieldChange = (index: number, field: keyof Or05061Type, value: any) => {
  if (!refValue.value) return
  refValue.value[index][field] = { value: formatForDisplay(value.value, 14, 5) }
  if (refValue.value[index].updateKbn !== UPDATE_KBN.CREATE) {
    refValue.value[index].updateKbn = UPDATE_KBN.UPDATE
  }
} /**
 * 表示制御用に文字列を最大34文字＋[…]に省略する
 * @param {string} input 入力文字列
 * @returns {string} 表示用文字列（最大numPerLine文字、超過部分は[…]で省略）
 */
function formatForDisplay(text: string, numPerLine: number, numberLine: number): string {
  if (!text || !text.length) return ''

  const cleaned = text.replace(/\r?\n/g, '').trim()
  const lines: string[] = []
  let line = '',
    i = 0

  while (i < cleaned.length && lines.length < numberLine) {
    line += cleaned[i]
    if (line.length === numPerLine) {
      lines.push(line)
      line = ''
    }
    i++
  }

  if (line) lines.push(line)

  if (i < cleaned.length && lines.length === numberLine) {
    lines[numberLine - 1] = lines[numberLine - 1].slice(0, numPerLine - 1) + '…'
  }

  return lines.join('\n')
}

defineExpose({
  createItem,
  deleteItem,
  duplicateItem,
  insertItem,
  moveUp,
  moveDown,
  sortBy,
  assessment,
  statusFactReference,
  notes,
  selectedRow,
  totalItemCount,
})
</script>

<template>
  <c-v-form
    ref="tableForm"
    style="padding-left: 8px; padding-right: 8px"
  >
    <div style="width: 760px">
      <c-v-data-table
        ref="dataTableRef"
        fixed-header
        height="485"
        :headers="headers"
        :items="visibleGroupedItems"
        hide-default-footer
        :items-per-page="-1"
        class="table-wrapper table-header"
      >
        <template #headers="{ columns }">
          <tr>
            <th
              v-for="column in columns"
              :key="column.key"
              :style="{
                minWidth: column.minWidth,
                width: column.width,
              }"
            >
              <div
                class="d-flex justify-space-between align-center"
                style="white-space: pre-line"
              >
                <span>{{ column.title }}</span>
                <div class="d-flex align-center">
                  <c-v-divider
                    vertical
                    :thickness="1"
                    class="mx-0"
                  />
                  <base-mo00009
                    v-if="column.key != 'yusenNo'"
                    :oneway-model-value="localOneway.mo00009OneWay"
                    @click="handleClickMo00009(column.key)"
                  />
                </div>
              </div>
            </th>
          </tr>
        </template>

        <template #item="{ item, index }">
          <tr
            :id="item.index"
            @click="onClickItem(item.index)"
            :class="{ 'highlight-row': selectedRow === item.index }"
          >
            <td>
              <base-mo01280
                v-model="item.mitosiKnj"
                :oneway-model-value="localOneway.mo01280NoteOneWay"
                @change="() => handleFieldChange(item.index, 'mitosiKnj', item.mitosiKnj)"
              />
            </td>
            <td>
              <base-mo01280
                v-model="item.kadaiKnj"
                :oneway-model-value="localOneway.mo01280NoteOneWay"
                class="col-textarea w-100"
                @change="() => handleFieldChange(item.index, 'kadaiKnj', item.kadaiKnj)"
              />
            </td>
            <td>
              <base-mo01274
                v-model="item.yusenNo"
                style="text-align: end"
                :oneway-model-value="localOneway.mo01274OneWay"
                @change="() => handleFieldChange(item.index, 'yusenNo', item.yusenNo)"
              ></base-mo01274>
            </td>
          </tr>
        </template>
      </c-v-data-table>
    </div>
    <!-- 確認ダイアログ -->
    <g-base-or21814 v-bind="or21814" />
    <!-- GUI00921_表示順変更課題整理総括画面をポップアップで起動する。 -->
    <g-custom-or-52823
      v-if="isShowDialogOr52823"
      v-bind="or52823"
      v-model="local.or52823"
      :oneway-model-value="local.or52823"
      @change="handleChangeOr52823"
    />
    <!-- GUI00923_課題取込画面 -->
    <g-custom-or-27604
      v-if="showDialogOr27604"
      v-bind="or27604"
      v-model="local.or27604"
      :oneway-model-value="or27604Data"
      @change="handleChangeOr27604"
    />
    <!-- 注釈モーダル画面をポップアップで起動する。 -->
    <g-custom-or-05063
      v-if="showDialogOr05063"
      v-bind="or05063"
    />
    <!-- GUI00937 入力支援［ケアマネ］画面をポップアップで起動する。 -->
    <g-custom-or-51775
      v-if="showDialogOr51775"
      v-bind="or51775"
      v-model="local.or51775"
      :oneway-model-value="localOneway.or51775Oneway"
      @confirm="handleConfirm51775"
    />
    <!-- GUI00928_［状況の事実参照］画面をポップアップで起動する。 -->
    <g-custom-or-29163
      v-if="showDialogOr29163"
      v-bind="or29163"
      :oneway-model-value="localOneway.or29163"
      :unique-cp-id="or29163.uniqueCpId"
    />
  </c-v-form>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/page-data-table.scss';
:deep(
  .table-wrapper .v-table__wrapper td:has(textarea) .table-cell:not([disabled]):not([readonly])
) {
  outline: none !important;
}
:deep(
  .table-wrapper
    .v-table__wrapper
    td:has(input:not([readonly]):not([disabled]):not([type='checkbox']):not([type='radio']))
    input
) {
  outline: none !important;
  height: 115px !important;
}
</style>
