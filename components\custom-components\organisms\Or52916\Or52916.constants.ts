import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * Or52916:有機体:承認欄の複写画面
 * GUI00618_承認欄の複写画面
 *
 * @description
 * 静的データ
 *
 * <AUTHOR>
 */
export namespace Or52916Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or52916', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false
    /**
     * 極小: 9
     */
    export const FONT_SIZE_9 = '9'
    /**
     * 小さい: 10
     */
    export const FONT_SIZE_10 = '10'
    /**
     * 普通: 11
     */
    export const FONT_SIZE_11 = '11'
    /**
     * 大きい: 12
     */
    export const FONT_SIZE_12 = '12'
    /**
     * px
     */
    export const WIDTH_PX = 'px'
    /**
     * セクション番号 3GKV00241P002
     */
    export const SECTIONNO_3GKV00241P002 = '3GKV00241P002'
    /**
     * セクション番号 3GKV00242P002
     */
    export const SECTIONNO_3GKV00242P002 = '3GKV00242P002'
    /**
     * セクション番号 3GKU0P149P001
     */
    export const SECTIONNO_3GKU0P149P001 = '3GKU0P149P001'
    /**
     * セクション番号 3GKU0P141P001
     */
    export const SECTIONNO_3GKU0P141P001 = '3GKU0P141P001'
    /**
     *文字列:0
     */
    export const STRING_0 = '0'
    /**
     *文字列:1
     */
    export const STRING_1 = '1'
    /**
     *文字列:1
     */
    export const STRING_2 = '2'
    /**
     *文字列:1
     */
    export const STRING_3 = '3'
    /**
     *文字列:1
     */
    export const STRING_4 = '4'
    /**
     *文字列:text
     */
    export const TEXTSTR = 'text'
    /**
     *文字列:Width
     */
    export const WIDTHSTR = 'Width'
    /**
     *文字列:Knj
     */
    export const KNJSTR = 'Knj'
    /**
     *文字列:Font
     */
    export const FONTSTR = 'Font'
    /**
     *文字列:day
     */
    export const DAYSTR = 'day'
  }
}
