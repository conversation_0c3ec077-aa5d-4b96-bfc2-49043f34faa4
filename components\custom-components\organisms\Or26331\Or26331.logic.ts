import { Or26331Const } from './Or26331.constants'
import type { Or26331Type } from '~/types/cmn/business/components/Or26331Type'
import { useTwoWayBindAccessor, useInitialize } from '~/composables/useComponentLogic'
/**
 * Or26331Logic：有機体：利用者選択
 * GUI01290_［印刷設定］画面
 *
 * @description
 * 処理ロジック
 *
 * <AUTHOR> 朱征宇
 */
export namespace Or26331Logic {
  /**
   * initialize
   *
   * @description
   * コンポーネントの初期化処理。
   * 共通関数を呼びpiniaの領域を初期化する。
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId } = useInitialize<Or26331Type>({
      cpId: Or26331Const.CP_ID(0),
      uniqueCpId,
      editFlgNecessity: false,
    })

    return {
      cpId,
      uniqueCpId: uniqCpId,
    }
  }

  /**
   * TwoWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const data = useTwoWayBindAccessor<Or26331Type>(Or26331Const.CP_ID(0))
}
