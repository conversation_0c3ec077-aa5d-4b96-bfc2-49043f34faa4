<script setup lang="ts">
/**
 * Or10412:有機体:モーダル（画面/特殊コンポーネント）
 * GUI00787_［メモ入力］画面
 *
 * @description
 * メモ入力
 *
 * <AUTHOR>
 */
import {
  computed,
  onMounted,
  ref,
  watch,
  reactive,
  type ComponentPublicInstance,
  useTemplateRef,
  nextTick,
} from 'vue'
import { useI18n } from 'vue-i18n'
import { Or10412Const } from './Or10412.constants'
import type { Or10412StateType } from './Or10412.type'
import { useScreenOneWayBind, useSetupChildProps } from '#imports'
import type { Or10412Type, Or10412OnewayType } from '~/types/cmn/business/components/Or10412Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00046Type, Mo00046OnewayType } from '~/types/business/components/Mo00046Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type { Mo00038Type, Mo00038OnewayType } from '~/types/business/components/Mo00038Type'
import type { Mo00039OnewayType } from '~/types/cmn/business/components/Mo00039Type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type { CustomClass } from '@/types/CustomClassType'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import type { Or21815StateType } from '~/components/base-components/organisms/Or21815/Or21815.type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import type { Or51775OnewayType, Or51775Type } from '~/types/cmn/business/components/Or51775Type'
import type { Or51775ConfirmType } from '~/components/custom-components/organisms/Or51775/Or51775.type'
import { useCmnRouteCom } from '~/utils/useCmnRouteCom'
import { useCmnCom } from '@/utils/useCmnCom'

const { t } = useI18n()
const cmnRouteCom = useCmnRouteCom()

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or10412Type
  onewayModelValue: Or10412OnewayType
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

const localOneway = reactive({
  /**
   * 情報ダイアログ
   */
  mo00024Oneway: {
    width: '900px',
    height: '528px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or10412',
      toolbarTitle: t('label.memo-input'),
      toolbarName: 'Or10412ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  /**
   * GUI00937_入力支援
   */
  or51775Oneway: {
    title: t('label.memo'),
    screenId: Or10412Const.DEFAULT.SCREEN_ID,
    bunruiId: Or10412Const.DEFAULT.EMPTY,
    t1Cd: Or10412Const.DEFAULT.EMPTY,
    t2Cd: Or10412Const.DEFAULT.EMPTY,
    t3Cd: Or10412Const.DEFAULT.ZERO,
    tableName: Or10412Const.DEFAULT.ZERO,
    columnName: Or10412Const.DEFAULT.ZERO,
    assessmentMethod: Or10412Const.DEFAULT.ZERO,
    inputContents: Or10412Const.DEFAULT.ZERO,
    userId: Or10412Const.DEFAULT.ZERO,
    mode: Or10412Const.DEFAULT.ZERO,
  } as Or51775OnewayType,
})

const respData: Or10412Type = {
  selectItemNo: '',
  userId: '',
  fontColor: '',
  historyTable: '',
  historyTableColum: '',
  meMoContent: '',
  textSize: null,
  flag: '',
}

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * 変数定義
 **************************************************/
const mo00046TypeRef = useTemplateRef<ComponentPublicInstance>('textArea')

// 子コンポーネント用変数
const or21815 = ref({ uniqueCpId: '' })
const or51775 = ref({ uniqueCpId: '' })

const mo00024 = ref<Mo00024Type>({
  isOpen: Or10412Const.DEFAULT.IS_OPEN,
})

const Mo00039OModelValue = ref('1')

const Mo00039OnewayModelValue: Mo00039OnewayType = {
  name: 'size',
}

let inputNumberValue = '12'

const inputNumberModelValue = ref<Mo00038Type>({
  mo00045: {
    value: '12',
  },
})

const inputNumberOnewayModelValue = ref<Mo00038OnewayType>({
  showSpinBtn: true,
  isEditCamma: false,
  isEditPeriod: false,
  disalbeSpinBtnDefaultProcessing: true,
})

const hiddeBtn = ref<string>('')

const ColorArrays = ref(Or10412Const.ColorArray)

const textSize: Mo00615OnewayType = {
  itemLabel: t('label.letter-size'),
  showItemLabel: true,
  showRequiredLabel: false,
}

// 文字色Title
const TitleOnewayModelValue: Mo00615OnewayType = {
  itemLabel: t('label.letter-color'),
  showItemLabel: true,
  showRequiredLabel: false,
}

const articleMaster: Mo00615OnewayType = {
  itemLabel: t('label.sentence-master'),
  showItemLabel: true,
  showRequiredLabel: false,
}

const Mo0009OnewayModelValue: Mo00009OnewayType = {
  icon: true,
  btnIcon: 'edit_square',
  prependIcon: 'edit_square',
}
const mo00046ModelValue = ref<Mo00046Type>({
  value: props.onewayModelValue.meMoContent,
})

watch(
  () => props.onewayModelValue.meMoContent,
  () => {
    mo00046ModelValue.value = { value: props.onewayModelValue.meMoContent }
  }
)

const mo00046OnewayModelValue: Mo00046OnewayType = {
  class: 'customStyle',
  noResize: true,
  autoGrow: false,
  customClass: {
    itemClass: 'customItem',
  } as CustomClass,
}

const Mo00611OnewayModelValue: Mo00611OnewayType = {
  btnLabel: t('btn.close'),
}

const Mo00609OnewayModelValue: Mo00609OnewayType = {
  btnLabel: t('btn.confirm'),
}

const codeTypes = ref<CodeType[]>()

/**
 * 文章内容
 */
const input = ref<Or51775Type>({ modelValue: '' } as Or51775Type)
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or10412StateType>({
  cpId: Or10412Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or10412Const.DEFAULT.IS_OPEN
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21815Const.CP_ID(1)]: or21815.value,
  [Or51775Const.CP_ID(1)]: or51775.value,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 画面ID
const screenId = 'GUI00787'
// ルーティング
const routing = 'GUI00787/pinia'
// ケアマネ画面を初期化する
await useCmnCom().initialize({
  screenId,
  routing,
})

/**
 * ダイアログ表示フラグ
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

onMounted(async () => {
  await init()
})
watch(
  () => props.onewayModelValue.selectItemNo,
  () => {
    void init()
  }
)
/**
 * 警告ダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openWarningDialog = (uniqueCpId: string, state: Or21815StateType) => {
  Or21815Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(uniqueCpId)

        let result = 'yes'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }
        if (event?.thirdBtnClickFlg) {
          result = 'cancel'
        }

        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * AC004_「文字サイズテキスト」▲ボタン押下
 * AC004_「文字サイズテキスト」▼ボタン押下
 */
watch(
  () => inputNumberModelValue.value.emitType,
  (newValue) => {
    if (newValue === 'clickSpinBtnUp') {
      // 「▲ボタン」押下時の処理
      inputNumberModelValue.value.mo00045.value = (
        parseInt(inputNumberModelValue.value.mo00045.value) + 1
      ).toString()
      fontSizeUp()
    } else if (newValue === 'clickSpinBtnDown') {
      // 「▼ボタン」押下時の処理
      inputNumberModelValue.value.mo00045.value = (
        parseInt(inputNumberModelValue.value.mo00045.value) - 1
      ).toString()
      fontSizeDown()
    }
    // ラジオボタンへの値反映処理などを記載

    // emitTypeを初期化
    inputNumberModelValue.value.emitType = undefined
  }
)

/**
 * 初期情報取得
 */
async function init() {
  // 汎用コード取得API実行
  await initCodes()
  codeTypes.value = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_MEMO_LETTER_SIZE)

  // 文字色
  if (ColorArrays.value) {
    for (const item of ColorArrays.value) {
      if (props.onewayModelValue.fontColor) {
        if (item?.color.toLowerCase() === props.onewayModelValue.fontColor.toLowerCase()) {
          item.iconStr = 'check'
          void cssStyleChange(
            mo00046TypeRef.value?.$el as HTMLElement,
            '.customStyle',
            item?.color,
            -1
          )
          respData.fontColor = item?.color
        } else {
          item.iconStr = ''
        }
      }
      // 親ページに値が伝わっていない場合は初期値に戻
      else {
        if ('#000000' === item?.color) {
          item.iconStr = 'check'
          void cssStyleChange(
            mo00046TypeRef.value?.$el as HTMLElement,
            '.customStyle',
            item?.color,
            -1
          )
        } else {
          item.iconStr = ''
        }
      }
    }
  }

  // 親画面.文字サイズが空又は12の場合
  const fontSizeInt = parseInt(props.onewayModelValue.textSize!) || 12
  void cssStyleChange(mo00046TypeRef.value?.$el as HTMLElement, '.customStyle', '', fontSizeInt)
  if ('12' === props.onewayModelValue.textSize || !props.onewayModelValue.textSize) {
    // 画面.文字サイズが12を設定し、文字サイズラジオボタンが"普通"で選定する。
    respData.textSize = '12'
    Mo00039OModelValue.value = '1'
  }
  // 親画面.文字サイズが9の場合
  else if ('9' === props.onewayModelValue.textSize) {
    // 画面.文字サイズが9を設定し、文字サイズラジオボタンが"小さい"で選定する。
    respData.textSize = '9'
    Mo00039OModelValue.value = '0'
  }
  // 親画面.文字サイズが15の場合
  else if ('15' === props.onewayModelValue.textSize) {
    // 画面.文字サイズが15を設定し、文字サイズラジオボタンが"大きい"で選定する。
    respData.textSize = '15'
    Mo00039OModelValue.value = '2'
  }
  // 上記以外の場合
  else {
    // 画面.文字サイズが親画面.文字サイズを設定し、文字サイズラジオボタンが未選定の状態です。
    respData.textSize = fontSizeInt + ''
    Mo00039OModelValue.value = fontSizeInt + ''
    inputNumberModelValue.value.mo00045.value = fontSizeInt + ''
  }
  // 返却情報-選択項目番号
  respData.selectItemNo = props.onewayModelValue.selectItemNo
}

/**
 * 汎用コード取得API実行
 */
const initCodes = async () => {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // アセスメント種別名
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_MEMO_LETTER_SIZE },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
}

/**
 * AC003_「文字サイズラジオボタング」選択変更
 * 効果が実現できず、デバッグが必要です
 *
 * @param value - 選択の文字サイズ
 */
function selectRadioChange(value: string) {
  if (value) {
    const inputValue = parseInt(value)
    if (inputValue > 4 && inputValue < 16) {
      hiddeBtn.value = ''
    }
    let size = 12
    //「小さい」の場合
    if (0 === inputValue) {
      // 文字サイズテキストを9に設定し、メモ内容テキストエリアの文字サイズに９を変わる。
      inputNumberModelValue.value.mo00045.value = '9'
      respData.textSize = '9'
      inputNumberValue = '9'
      size = 9
    }
    // 「普通」の場合
    else if (1 === inputValue) {
      // 文字サイズテキストを12に設定し、メモ内容テキストエリアの文字サイズに12を変わる。
      inputNumberModelValue.value.mo00045.value = '12'
      respData.textSize = '12'
      inputNumberValue = '12'
      size = 12
    }
    // 「大きい」の場合
    else if (2 === inputValue) {
      // 文字サイズテキストを15に設定し、メモ内容テキストエリアの文字サイズに15を変わる。
      hiddeBtn.value = 'up'
      inputNumberModelValue.value.mo00045.value = '15'
      respData.textSize = '15'
      inputNumberValue = '15'
      size = 15
    } else {
      inputNumberValue = value + ''
      size = inputValue
    }

    void cssStyleChange(
      mo00046TypeRef.value?.$el as HTMLElement,
      '.customStyle',
      respData.fontColor,
      size
    )
  }
}

/**
 * AC004_「文字サイズテキスト」フォーカスアウト
 */
function fontSizeLostFocue() {
  if (inputNumberModelValue.value.mo00045.value) {
    void fontSizeChange(parseInt(inputNumberModelValue.value.mo00045.value))
  }
}

/**
 * 文字サイズ変換--上向き
 */
function fontSizeUp() {
  const inputValue = parseInt(inputNumberModelValue.value.mo00045.value)
  // 9で入力の場合
  if (9 === inputValue) {
    // 文字サイズラジオボタングを"小さい"に選択し、メモ内容テキストエリアの文字サイズに９を変わる。
    Mo00039OModelValue.value = '0'
    respData.textSize = inputValue + ''
    void cssStyleChange(
      mo00046TypeRef.value?.$el as HTMLElement,
      '.customStyle',
      respData.fontColor,
      inputValue
    )
  }
  // 12で入力の場合
  else if (12 === inputValue) {
    // 文字サイズラジオボタングを"普通"に選択し、メモ内容テキストエリアの文字サイズに12を変わる。
    Mo00039OModelValue.value = '1'
    respData.textSize = inputValue + ''
    void cssStyleChange(
      mo00046TypeRef.value?.$el as HTMLElement,
      '.customStyle',
      respData.fontColor,
      inputValue
    )
  }
  // 15で入力の場合
  else if (inputValue >= 15) {
    // 文字サイズラジオボタングを"大きい"に選択し、メモ内容テキストエリアの文字サイズに15を変わる。
    Mo00039OModelValue.value = '2'
    respData.textSize = '15'
    void cssStyleChange(
      mo00046TypeRef.value?.$el as HTMLElement,
      '.customStyle',
      respData.fontColor,
      15
    )

    // 文字サイズが15以上の場合
    hiddeBtn.value = 'up'
    inputNumberModelValue.value.mo00045.value = '15'
  } else {
    hiddeBtn.value = ''
    Mo00039OModelValue.value = inputValue + ''
    respData.textSize = inputValue + ''
    void cssStyleChange(
      mo00046TypeRef.value?.$el as HTMLElement,
      '.customStyle',
      respData.fontColor,
      inputValue
    )
  }
}

/**
 * 文字サイズ変換--下向き
 */
function fontSizeDown() {
  const inputValue = parseInt(inputNumberModelValue.value.mo00045.value)
  // 文字サイズが5以下の場合
  if (inputValue <= 5) {
    // 何もしない。
    hiddeBtn.value = 'down'
    inputNumberModelValue.value.mo00045.value = '5'
  }
  // 9で入力の場合
  else if (9 === inputValue) {
    // 文字サイズラジオボタングを"小さい"に選択し、メモ内容テキストエリアの文字サイズに９を変わる。
    Mo00039OModelValue.value = '0'
    respData.textSize = inputValue + ''
    void cssStyleChange(
      mo00046TypeRef.value?.$el as HTMLElement,
      '.customStyle',
      respData.fontColor,
      inputValue
    )
  }
  // 12で入力の場合
  else if (12 === inputValue) {
    // 文字サイズラジオボタングを"普通"に選択し、メモ内容テキストエリアの文字サイズに12を変わる。
    Mo00039OModelValue.value = '1'
    respData.textSize = inputValue + ''
    void cssStyleChange(
      mo00046TypeRef.value?.$el as HTMLElement,
      '.customStyle',
      respData.fontColor,
      inputValue
    )
  } else {
    hiddeBtn.value = ''
    Mo00039OModelValue.value = inputValue + ''
    respData.textSize = inputValue + ''
    void cssStyleChange(
      mo00046TypeRef.value?.$el as HTMLElement,
      '.customStyle',
      respData.fontColor,
      inputValue
    )
  }
}

/**
 * 文字サイズ変換
 *
 * @param size - 文字サイズ
 */
async function fontSizeChange(size: number) {
  // 4以下、16以上で入力の場合
  if (size <= 4 || size >= 16) {
    // 以下のメッセージを表示
    const dialogResult = await openWarningDialog(or21815.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.font-size'),
      // ダイアログテキスト
      dialogText: t('message.w-cmn-20873'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
    if ('yes' === dialogResult) {
      // 文字サイズ復元
      inputNumberModelValue.value.mo00045.value = inputNumberValue
    }
  }
  // 9で入力の場合
  else if (9 === size) {
    // 文字サイズラジオボタングを"小さい"に選択し、メモ内容テキストエリアの文字サイズに９を変わる。
    Mo00039OModelValue.value = '0'
    respData.textSize = size + ''
    void cssStyleChange(
      mo00046TypeRef.value?.$el as HTMLElement,
      '.customStyle',
      respData.fontColor,
      size
    )
    hiddeBtn.value = ''
    inputNumberValue = inputNumberModelValue.value.mo00045.value
  }
  // 12で入力の場合
  else if (12 === size) {
    // 文字サイズラジオボタングを"普通"に選択し、メモ内容テキストエリアの文字サイズに12を変わる。
    Mo00039OModelValue.value = '1'
    respData.textSize = size + ''
    void cssStyleChange(
      mo00046TypeRef.value?.$el as HTMLElement,
      '.customStyle',
      respData.fontColor,
      size
    )
    hiddeBtn.value = ''
    inputNumberValue = inputNumberModelValue.value.mo00045.value
  }
  // 15で入力の場合
  else if (15 === size) {
    // 文字サイズラジオボタングを"大きい"に選択し、メモ内容テキストエリアの文字サイズに15を変わる。
    hiddeBtn.value = 'up'
    Mo00039OModelValue.value = '2'
    respData.textSize = size + ''
    void cssStyleChange(
      mo00046TypeRef.value?.$el as HTMLElement,
      '.customStyle',
      respData.fontColor,
      size
    )
    inputNumberValue = inputNumberModelValue.value.mo00045.value
  }
  // 上記以外の場合
  else {
    // 文字サイズラジオボタングをクリアし、メモ内容テキストエリアの文字サイズに文字サイズテキスト入力したサイズを変わる。
    Mo00039OModelValue.value = ''
    respData.textSize = size + ''
    void cssStyleChange(
      mo00046TypeRef.value?.$el as HTMLElement,
      '.customStyle',
      respData.fontColor,
      size
    )
    if (size <= 5) {
      hiddeBtn.value = 'down'
    } else {
      hiddeBtn.value = ''
    }
    inputNumberValue = inputNumberModelValue.value.mo00045.value
  }
}

/**
 * AC005_「ケアマネ入力支援アイコンボタン」押下
 */
function editBtnClick() {
  // 大分類CD
  // 親画面.フラグが1の場合
  if (Or10412Const.DEFAULT.ONE === props.onewayModelValue.flag) {
    localOneway.or51775Oneway.t1Cd = Or10412Const.DEFAULT.SIX_ZERO_THREE
  }
  // 親画面.フラグが3の場合
  else if (Or10412Const.DEFAULT.THREE === props.onewayModelValue.flag) {
    localOneway.or51775Oneway.t1Cd = Or10412Const.DEFAULT.SIX_ONE_ZERO
  }
  // 以外の場合
  else {
    localOneway.or51775Oneway.t1Cd = Or10412Const.DEFAULT.SIX_ZERO_FOUR
  }
  // 中分類CD
  localOneway.or51775Oneway.t2Cd = props.onewayModelValue.selectItemNo
  // 小分類CD
  localOneway.or51775Oneway.tableName = props.onewayModelValue.historyTable
  // テーブル名
  localOneway.or51775Oneway.columnName = props.onewayModelValue.historyTableColum
  // カラム名
  localOneway.or51775Oneway.inputContents =
    mo00046ModelValue.value.value ?? Or10412Const.DEFAULT.EMPTY
  // アセスメント方式
  localOneway.or51775Oneway.assessmentMethod =
    cmnRouteCom.getInitialSettingMaster()?.cpnFlg ?? Or10412Const.DEFAULT.EMPTY
  // Or51775のダイアログを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId, // ユニークコンポーネントID
    state: { isOpen: true }, // ダイアログを開くフラグ
  })
}

/**
 * AC006_「色選び板」選択
 *
 * @param index - 選択色の索引
 */
function colorSelect(index: number) {
  // 「メモ内容」テキストエリアの内容を「色選び板」に選定した色を変わる。
  if (index >= 0) {
    for (let i = 0; i < ColorArrays.value.length; i++) {
      // 現在選択中のボタンにアイコンを表示する
      if (index === i) {
        ColorArrays.value[i].iconStr = 'check'
        respData.fontColor = ColorArrays.value[i].color

        void cssStyleChange(
          mo00046TypeRef.value?.$el as HTMLElement,
          '.customStyle',
          ColorArrays.value[i].color,
          -1
        )
      }
      // 前回選択したボタンのアイコンを解除する
      else {
        ColorArrays.value[i].iconStr = undefined
      }
    }
  }
}

/**
 * 仕様変更
 *
 * @param templateDom - DOM要素
 *
 * @param className - クラス名
 *
 * @param color - 色
 *
 * @param fontSize - 大きさ
 */
async function cssStyleChange(
  templateDom: HTMLElement,
  className: string,
  color: string,
  fontSize: number
) {
  await nextTick(() => {
    if (templateDom) {
      const htmlElement = templateDom.querySelector(className)
      if (htmlElement) {
        const html = htmlElement.querySelectorAll('textarea')
        if (html) {
          for (const item of html) {
            if (item.value) {
              // メモフォント
              item.style.fontSize = fontSize + 'px'
              // メモ色
              item.style.color = color
            }
          }
        }
      }
    }
  })
}

/**
 * AC002_「×ボタン」押下
 * AC007_「閉じボタン」押下
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function close() {
  setState({ isOpen: false })
}

/**
 * AC008_「確定ボタン」押下
 * 「確定」ボタン押下
 */
function confirm() {
  // メモ内容は32000文字以上の場合
  if (mo00046ModelValue.value.value) {
    if (mo00046ModelValue.value.value?.length > 32000) {
      respData.meMoContent = mo00046ModelValue.value.value?.substring(0, 32000)
    } else if (mo00046ModelValue.value.value?.length <= 32000) {
      respData.meMoContent = mo00046ModelValue.value.value
    }
  }
  // 画面.情報を親画面に戻る。
  emit('update:modelValue', respData)
  close()
}

/**
 * 認定ボタンを押下時
 *
 * @param data - 入力支援情報
 */
const or51775Confirm = (data: Or51775ConfirmType) => {
  if (data) {
    // 本文末に追加の場合
    if (Or10412Const.DEFAULT.ADD_END_TEXT_IMPORT_TYPE === data.type) {
      mo00046ModelValue.value.value = mo00046ModelValue.value.value + data.value
    }
    // 本文上書の場合
    else if (Or10412Const.DEFAULT.OVERWRITE_TEXT_IMPORT_TYPE === data.type) {
      mo00046ModelValue.value.value = data.value
    }
  }
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
    class="mt-2"
  >
    <template #cardItem>
      <c-v-row no-gutters>
        <c-v-col
          cols="12"
          sm="6"
          style="padding-right: 8px;"
        >
          <c-v-row no-gutters>
            <c-v-card
              variant="outlined"
              color="#CCCCCC"
              class="sizeCard"
            >
              <template #title>
                <base-mo00615 :oneway-model-value="textSize"></base-mo00615>
              </template>
              <c-v-card-text>
                <c-v-row no-gutters>
                  <base-mo00039
                    v-model="Mo00039OModelValue"
                    :oneway-model-value="Mo00039OnewayModelValue"
                    @update:model-value="selectRadioChange"
                  >
                    <base-at-radio
                      v-for="(item, index) in codeTypes"
                      :key="index"
                      name="small"
                      :value="item.value"
                      :radio-label="item.label"
                    ></base-at-radio>
                  </base-mo00039>
                </c-v-row>
                <c-v-row
                  no-gutters
                  style="margin-top: 8px"
                >
                  <base-mo00038
                    v-model="inputNumberModelValue"
                    :oneway-model-value="inputNumberOnewayModelValue"
                    :hidde-btn="hiddeBtn"
                    class="textSize"
                    @blur="fontSizeLostFocue"
                  ></base-mo00038>
                </c-v-row>
              </c-v-card-text>
            </c-v-card>
          </c-v-row>
          <c-v-row
            no-gutters
            style="margin-top: 8px"
          >
            <c-v-card
              variant="outlined"
              color="#CCCCCC"
              class="sizeCard"
            >
              <template #title>
                <base-mo00615 :oneway-model-value="TitleOnewayModelValue"></base-mo00615>
              </template>
              <c-v-card-text>
                <c-v-row no-gutters>
                  <c-v-col
                    cols="12"
                    sm="12"
                  >
                    <div class="colorSelectDiv">
                      <div
                        v-for="(item, index) in ColorArrays"
                        :key="index"
                      >
                        <base-at-button
                          :color="item.color"
                          :prepend-icon="item.iconStr"
                          :style="item.style"
                          @click="colorSelect(index)"
                        ></base-at-button>
                      </div>
                    </div>
                  </c-v-col>
                </c-v-row>
              </c-v-card-text>
            </c-v-card>
          </c-v-row>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="6"
        >
          <c-v-card
            variant="outlined"
            color="#CCCCCC"
            class="memoInputSentenceMaster"
          >
            <template #title>
              <c-v-row no-gutters>
                <c-v-col
                  cols="12"
                  sm="11"
                >
                  <base-mo00615 :oneway-model-value="articleMaster"></base-mo00615>
                </c-v-col>
                <c-v-col
                  cols="12"
                  sm="1"
                  style="text-align: right"
                >
                  <base-mo00009
                    :oneway-model-value="Mo0009OnewayModelValue"
                    variant="flat"
                    density="compact"
                    @click="editBtnClick"
                  >
                    <c-v-tooltip
                      activator="parent"
                      location="bottom"
                      :text="$t('tooltip.memo-input')"
                    ></c-v-tooltip>
                  </base-mo00009>
                </c-v-col>
              </c-v-row>
            </template>
            <base-mo00046
              ref="textArea"
              v-model="mo00046ModelValue"
              :oneway-model-value="mo00046OnewayModelValue"
              variant="outlined"
              class="memoInputTextArea"
            ></base-mo00046>
          </c-v-card>
        </c-v-col>
      </c-v-row>
    </template>
    <!-- フッター -->
    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <base-mo00611
          :oneway-model-value="Mo00611OnewayModelValue"
          class="mx-2"
          @click="close"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.screen-close')"
          ></c-v-tooltip>
        </base-mo00611>
        <base-mo00609
          :oneway-model-value="Mo00609OnewayModelValue"
          @click="confirm"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.confirm-btn')"
          ></c-v-tooltip>
        </base-mo00609>
      </c-v-row>
    </template>
    <!-- メッセージ 警告 -->
    <g-base-or-21815 v-bind="or21815" />
    <!-- GUI00937_入力支援［ケアマネ］ -->
    <g-custom-or-51775
      v-if="showDialogOr51775"
      v-bind="or51775"
      v-model="input"
      :oneway-model-value="localOneway.or51775Oneway"
      @confirm="or51775Confirm"
    />
  </base-mo00024>
</template>
<style scoped lang="scss">
:deep(.v-input__details) {
  display: none;
}

:deep(.v-row .v-row--no-gutters .ma-1) {
  display: none;
}

.sizeCard {
  width: 100%;

  :deep(.v-card-item) {
    padding: 8px !important;
  }

  .v-card-text {
    padding: 8px !important;
  }
}

.colorSelectDiv {
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  grid-template-rows: 1fr 1fr 1fr 1fr;
  grid-gap: 19px;
  width: 100%;
}

.memoInputSentenceMaster {
  height: 410px;

  :deep(.v-card-item) {
    padding: 8px !important;
  }

  .v-card-text {
    padding: 8px !important;
  }
}

.memoInputTextArea {
  margin: 0 auto;
  width: 100%;
  height: 196px;
  max-height: 196px;
  min-height: 196px;
  padding: 8px;
}

.memoInputTextArea :deep(.v-field__input) {
  width: 390px;
  height: 180px;
  max-height: 180px;
  min-height: 180px;
}

:deep(.customItem) {
  .v-col-auto {
    display: none;
  }
}

:deep(.item-label) {
  font-weight: bold !important;
}
</style>
