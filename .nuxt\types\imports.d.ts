// Generated by auto imports
export {}
declare global {
  const DOWNLOAD: typeof import('../../utils/useReportUtils')['DOWNLOAD']
  const STATUS_CODE_SUCCESS_200: typeof import('../../utils/usePrint')['STATUS_CODE_SUCCESS_200']
  const STATUS_CODE_SUCCESS_204: typeof import('../../utils/usePrint')['STATUS_CODE_SUCCESS_204']
  const VIEWER_TAB: typeof import('../../utils/useReportUtils')['VIEWER_TAB']
  const VIEWER_WINDOW: typeof import('../../utils/useReportUtils')['VIEWER_WINDOW']
  const abortNavigation: typeof import('../../node_modules/nuxt/dist/app/composables/router')['abortNavigation']
  const acceptHMRUpdate: typeof import('../../node_modules/@pinia/nuxt/dist/runtime/composables')['acceptHMRUpdate']
  const addRouteMiddleware: typeof import('../../node_modules/nuxt/dist/app/composables/router')['addRouteMiddleware']
  const callOnce: typeof import('../../node_modules/nuxt/dist/app/composables/once')['callOnce']
  const cancelIdleCallback: typeof import('../../node_modules/nuxt/dist/app/compat/idle-callback')['cancelIdleCallback']
  const clearError: typeof import('../../node_modules/nuxt/dist/app/composables/error')['clearError']
  const clearNuxtData: typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['clearNuxtData']
  const clearNuxtState: typeof import('../../node_modules/nuxt/dist/app/composables/state')['clearNuxtState']
  const computed: typeof import('vue')['computed']
  const createError: typeof import('../../node_modules/nuxt/dist/app/composables/error')['createError']
  const customRef: typeof import('vue')['customRef']
  const dateUtils: typeof import('../../utils/dateUtils')['dateUtils']
  const defineAppConfig: typeof import('../../node_modules/nuxt/dist/app/nuxt')['defineAppConfig']
  const defineAsyncComponent: typeof import('vue')['defineAsyncComponent']
  const defineComponent: typeof import('vue')['defineComponent']
  const defineNuxtComponent: typeof import('../../node_modules/nuxt/dist/app/composables/component')['defineNuxtComponent']
  const defineNuxtLink: typeof import('../../node_modules/nuxt/dist/app/components/nuxt-link')['defineNuxtLink']
  const defineNuxtPlugin: typeof import('../../node_modules/nuxt/dist/app/nuxt')['defineNuxtPlugin']
  const defineNuxtRouteMiddleware: typeof import('../../node_modules/nuxt/dist/app/composables/router')['defineNuxtRouteMiddleware']
  const definePageMeta: typeof import('../../node_modules/nuxt/dist/pages/runtime/composables')['definePageMeta']
  const definePayloadPlugin: typeof import('../../node_modules/nuxt/dist/app/nuxt')['definePayloadPlugin']
  const definePayloadReducer: typeof import('../../node_modules/nuxt/dist/app/composables/payload')['definePayloadReducer']
  const definePayloadReviver: typeof import('../../node_modules/nuxt/dist/app/composables/payload')['definePayloadReviver']
  const defineStore: typeof import('../../node_modules/@pinia/nuxt/dist/runtime/composables')['defineStore']
  const effect: typeof import('vue')['effect']
  const effectScope: typeof import('vue')['effectScope']
  const fileUploadDownloadRequestRepository: typeof import('../../utils/fileUploadDownloadRequestRepository')['fileUploadDownloadRequestRepository']
  const getAppManifest: typeof import('../../node_modules/nuxt/dist/app/composables/manifest')['getAppManifest']
  const getCurrentInstance: typeof import('vue')['getCurrentInstance']
  const getCurrentScope: typeof import('vue')['getCurrentScope']
  const getGlobalUniqueScreenIdForPage: typeof import('../../composables/uniqueScreenIdControl')['getGlobalUniqueScreenIdForPage']
  const getGlobalUniqueScreenIdsForPushState: typeof import('../../composables/uniqueScreenIdControl')['getGlobalUniqueScreenIdsForPushState']
  const getNewGlobalUniqueScreenId: typeof import('../../composables/uniqueScreenIdControl')['getNewGlobalUniqueScreenId']
  const getNextHistoryId: typeof import('../../composables/uniqueScreenIdControl')['getNextHistoryId']
  const getNowDatetime: typeof import('../../composables/uniqueScreenIdControl')['getNowDatetime']
  const getNowUnixTime: typeof import('../../composables/uniqueScreenIdControl')['getNowUnixTime']
  const getOldPushStateFromPopstate: typeof import('../../composables/uniqueScreenIdControl')['getOldPushStateFromPopstate']
  const getRandomNum: typeof import('../../composables/uniqueScreenIdControl')['getRandomNum']
  const getRouteRules: typeof import('../../node_modules/nuxt/dist/app/composables/manifest')['getRouteRules']
  const getSequencedCpId: typeof import('../../utils/useScreenUtils')['getSequencedCpId']
  const h: typeof import('vue')['h']
  const hasInjectionContext: typeof import('vue')['hasInjectionContext']
  const hasOutputAuth: typeof import('../../utils/useCmnAuthz')['hasOutputAuth']
  const hasPrintAuth: typeof import('../../utils/useCmnAuthz')['hasPrintAuth']
  const hasRegistAuth: typeof import('../../utils/useCmnAuthz')['hasRegistAuth']
  const hasViewAuth: typeof import('../../utils/useCmnAuthz')['hasViewAuth']
  const initEditFlg: typeof import('../../composables/useComponentVue')['initEditFlg']
  const inject: typeof import('vue')['inject']
  const injectHead: typeof import('../../node_modules/nuxt/dist/app/composables/head')['injectHead']
  const isNuxtError: typeof import('../../node_modules/nuxt/dist/app/composables/error')['isNuxtError']
  const isPrerendered: typeof import('../../node_modules/nuxt/dist/app/composables/payload')['isPrerendered']
  const isProxy: typeof import('vue')['isProxy']
  const isReactive: typeof import('vue')['isReactive']
  const isReadonly: typeof import('vue')['isReadonly']
  const isRef: typeof import('vue')['isRef']
  const isShallow: typeof import('vue')['isShallow']
  const isVue2: typeof import('../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue2']
  const isVue3: typeof import('../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue3']
  const loadPayload: typeof import('../../node_modules/nuxt/dist/app/composables/payload')['loadPayload']
  const markRaw: typeof import('vue')['markRaw']
  const mergeModels: typeof import('vue')['mergeModels']
  const mockUtils: typeof import('../../utils/mockUtils')['mockUtils']
  const navigateTo: typeof import('../../node_modules/nuxt/dist/app/composables/router')['navigateTo']
  const nextTick: typeof import('vue')['nextTick']
  const onActivated: typeof import('vue')['onActivated']
  const onBeforeMount: typeof import('vue')['onBeforeMount']
  const onBeforeRouteLeave: typeof import('vue-router')['onBeforeRouteLeave']
  const onBeforeRouteUpdate: typeof import('vue-router')['onBeforeRouteUpdate']
  const onBeforeUnmount: typeof import('vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('vue')['onBeforeUpdate']
  const onDeactivated: typeof import('vue')['onDeactivated']
  const onErrorCaptured: typeof import('vue')['onErrorCaptured']
  const onMounted: typeof import('vue')['onMounted']
  const onNuxtReady: typeof import('../../node_modules/nuxt/dist/app/composables/ready')['onNuxtReady']
  const onPrehydrate: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['onPrehydrate']
  const onRenderTracked: typeof import('vue')['onRenderTracked']
  const onRenderTriggered: typeof import('vue')['onRenderTriggered']
  const onScopeDispose: typeof import('vue')['onScopeDispose']
  const onServerPrefetch: typeof import('vue')['onServerPrefetch']
  const onUnmounted: typeof import('vue')['onUnmounted']
  const onUpdated: typeof import('vue')['onUpdated']
  const persistedState: typeof import('../../node_modules/@pinia-plugin-persistedstate/nuxt/dist/runtime/storages')['persistedState']
  const postRequest: typeof import('../../utils/useApi')['postRequest']
  const prefetchComponents: typeof import('../../node_modules/nuxt/dist/app/composables/preload')['prefetchComponents']
  const preloadComponents: typeof import('../../node_modules/nuxt/dist/app/composables/preload')['preloadComponents']
  const preloadPayload: typeof import('../../node_modules/nuxt/dist/app/composables/payload')['preloadPayload']
  const preloadRouteComponents: typeof import('../../node_modules/nuxt/dist/app/composables/preload')['preloadRouteComponents']
  const prerenderRoutes: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['prerenderRoutes']
  const provide: typeof import('vue')['provide']
  const proxyRefs: typeof import('vue')['proxyRefs']
  const reactive: typeof import('vue')['reactive']
  const readonly: typeof import('vue')['readonly']
  const ref: typeof import('vue')['ref']
  const refreshCookie: typeof import('../../node_modules/nuxt/dist/app/composables/cookie')['refreshCookie']
  const refreshNuxtData: typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['refreshNuxtData']
  const reloadNuxtApp: typeof import('../../node_modules/nuxt/dist/app/composables/chunk')['reloadNuxtApp']
  const requestIdleCallback: typeof import('../../node_modules/nuxt/dist/app/compat/idle-callback')['requestIdleCallback']
  const resolveComponent: typeof import('vue')['resolveComponent']
  const setBlankGlobalUniqueScreenIds: typeof import('../../composables/uniqueScreenIdControl')['setBlankGlobalUniqueScreenIds']
  const setGlobalUniqueScreenIdsFromPopstate: typeof import('../../composables/uniqueScreenIdControl')['setGlobalUniqueScreenIdsFromPopstate']
  const setInterval: typeof import('../../node_modules/nuxt/dist/app/compat/interval')['setInterval']
  const setPageLayout: typeof import('../../node_modules/nuxt/dist/app/composables/router')['setPageLayout']
  const setResponseStatus: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['setResponseStatus']
  const shallowReactive: typeof import('vue')['shallowReactive']
  const shallowReadonly: typeof import('vue')['shallowReadonly']
  const shallowRef: typeof import('vue')['shallowRef']
  const showError: typeof import('../../node_modules/nuxt/dist/app/composables/error')['showError']
  const storeToRefs: typeof import('../../node_modules/@pinia/nuxt/dist/runtime/composables')['storeToRefs']
  const toRaw: typeof import('vue')['toRaw']
  const toRef: typeof import('vue')['toRef']
  const toRefs: typeof import('vue')['toRefs']
  const toValue: typeof import('vue')['toValue']
  const triggerRef: typeof import('vue')['triggerRef']
  const tryUseNuxtApp: typeof import('../../node_modules/nuxt/dist/app/nuxt')['tryUseNuxtApp']
  const unref: typeof import('vue')['unref']
  const updateAppConfig: typeof import('../../node_modules/nuxt/dist/app/config')['updateAppConfig']
  const updateState: typeof import('../../composables/useComponentVue')['updateState']
  const useApi: typeof import('../../utils/useApi')['useApi']
  const useAppConfig: typeof import('../../node_modules/nuxt/dist/app/config')['useAppConfig']
  const useAreaLinkedStore: typeof import('../../stores/session/business-platform/areaLinked')['useAreaLinkedStore']
  const useAsyncData: typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useAsyncData']
  const useAttrs: typeof import('vue')['useAttrs']
  const useAuthn: typeof import('../../utils/useAuthn')['useAuthn']
  const useAuthz: typeof import('../../utils/useAuthz')['useAuthz']
  const useBusinessPlatformScreenStore: typeof import('../../stores/session/business-platform/businessPlatformScreen')['useBusinessPlatformScreenStore']
  const useCacheStore: typeof import('../../stores/session/business-platform/cache')['useCacheStore']
  const useCmnCom: typeof import('../../utils/useCmnCom')['useCmnCom']
  const useCmnRouteCom: typeof import('../../utils/useCmnRouteCom')['useCmnRouteCom']
  const useColorUtils: typeof import('../../utils/useColorUtils')['useColorUtils']
  const useCommonProps: typeof import('../../composables/useCommonProps')['useCommonProps']
  const useComponentSelector: typeof import('../../utils/useComponentSelector')['useComponentSelector']
  const useCookie: typeof import('../../node_modules/nuxt/dist/app/composables/cookie')['useCookie']
  const useCookieData: typeof import('../../utils/useCookieData')['useCookieData']
  const useCssModule: typeof import('vue')['useCssModule']
  const useCssVars: typeof import('vue')['useCssVars']
  const useCurrentScreenData: typeof import('../../composables/currentScreenManager')['useCurrentScreenData']
  const useCurrentScreenEvent: typeof import('../../composables/currentScreenManager')['useCurrentScreenEvent']
  const useCurrentScreenState: typeof import('../../composables/currentScreenManager')['useCurrentScreenState']
  const useCurrentScreenStore: typeof import('../../stores/session/currentScreen')['useCurrentScreenStore']
  const useCurrentScreenUtils: typeof import('../../utils/useCurrentScreenUtils')['useCurrentScreenUtils']
  const useCurrentUserStore: typeof import('../../stores/session/auth')['useCurrentUserStore']
  const useData: typeof import('../../utils/useData')['useData']
  const useError: typeof import('../../node_modules/nuxt/dist/app/composables/error')['useError']
  const useEventStatusAccessor: typeof import('../../composables/useComponentLogic')['useEventStatusAccessor']
  const useFetch: typeof import('../../node_modules/nuxt/dist/app/composables/fetch')['useFetch']
  const useFileUtils: typeof import('../../utils/useFileUtils')['useFileUtils']
  const useGyoumuCom: typeof import('../../utils/useGyoumuCom')['useGyoumuCom']
  const useHead: typeof import('../../node_modules/nuxt/dist/app/composables/head')['useHead']
  const useHeadSafe: typeof import('../../node_modules/nuxt/dist/app/composables/head')['useHeadSafe']
  const useHydration: typeof import('../../node_modules/nuxt/dist/app/composables/hydrate')['useHydration']
  const useId: typeof import('vue')['useId']
  const useInitialize: typeof import('../../composables/useComponentLogic')['useInitialize']
  const useJigyoList: typeof import('../../utils/useJigyoList')['useJigyoList']
  const useLazyAsyncData: typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useLazyAsyncData']
  const useLazyFetch: typeof import('../../node_modules/nuxt/dist/app/composables/fetch')['useLazyFetch']
  const useLink: typeof import('vue-router')['useLink']
  const useLoadingIndicator: typeof import('../../node_modules/nuxt/dist/app/composables/loading-indicator')['useLoadingIndicator']
  const useModel: typeof import('vue')['useModel']
  const useNuxtApp: typeof import('../../node_modules/nuxt/dist/app/nuxt')['useNuxtApp']
  const useNuxtData: typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useNuxtData']
  const useNuxtDevTools: typeof import('../../node_modules/@nuxt/devtools/dist/runtime/use-nuxt-devtools')['useNuxtDevTools']
  const useOneWayBindAccessor: typeof import('../../composables/useComponentLogic')['useOneWayBindAccessor']
  const usePinia: typeof import('../../node_modules/@pinia/nuxt/dist/runtime/composables')['usePinia']
  const usePreviewMode: typeof import('../../node_modules/nuxt/dist/app/composables/preview')['usePreviewMode']
  const usePrint: typeof import('../../utils/usePrint')['usePrint']
  const useReportUtils: typeof import('../../utils/useReportUtils')['useReportUtils']
  const useRequestEvent: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestEvent']
  const useRequestFetch: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestFetch']
  const useRequestHeader: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestHeader']
  const useRequestHeaders: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestHeaders']
  const useRequestURL: typeof import('../../node_modules/nuxt/dist/app/composables/url')['useRequestURL']
  const useResponseHeader: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useResponseHeader']
  const useRoute: typeof import('../../node_modules/nuxt/dist/app/composables/router')['useRoute']
  const useRouteAnnouncer: typeof import('../../node_modules/nuxt/dist/app/composables/route-announcer')['useRouteAnnouncer']
  const useRouteCommonsStore: typeof import('../../stores/session/routeCommons')['useRouteCommonsStore']
  const useRouter: typeof import('../../node_modules/nuxt/dist/app/composables/router')['useRouter']
  const useRuntimeConfig: typeof import('../../node_modules/nuxt/dist/app/nuxt')['useRuntimeConfig']
  const useRuntimeHook: typeof import('../../node_modules/nuxt/dist/app/composables/runtime-hook')['useRuntimeHook']
  const useScreenEventStatus: typeof import('../../composables/useComponentVue')['useScreenEventStatus']
  const useScreenInitFlg: typeof import('../../composables/useComponentVue')['useScreenInitFlg']
  const useScreenNavControl: typeof import('../../composables/useComponentVue')['useScreenNavControl']
  const useScreenOneWayBind: typeof import('../../composables/useComponentVue')['useScreenOneWayBind']
  const useScreenStore: typeof import('../../stores/session/screen')['useScreenStore']
  const useScreenTwoWayBind: typeof import('../../composables/useComponentVue')['useScreenTwoWayBind']
  const useScreenUtils: typeof import('../../utils/useScreenUtils')['useScreenUtils']
  const useScript: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScript']
  const useScriptClarity: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptClarity']
  const useScriptCloudflareWebAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCloudflareWebAnalytics']
  const useScriptCrisp: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCrisp']
  const useScriptEventPage: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptEventPage']
  const useScriptFathomAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptFathomAnalytics']
  const useScriptGoogleAdsense: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAdsense']
  const useScriptGoogleAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAnalytics']
  const useScriptGoogleMaps: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleMaps']
  const useScriptGoogleTagManager: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleTagManager']
  const useScriptHotjar: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptHotjar']
  const useScriptIntercom: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptIntercom']
  const useScriptLemonSqueezy: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptLemonSqueezy']
  const useScriptMatomoAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMatomoAnalytics']
  const useScriptMetaPixel: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMetaPixel']
  const useScriptNpm: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptNpm']
  const useScriptPlausibleAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptPlausibleAnalytics']
  const useScriptSegment: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptSegment']
  const useScriptSnapchatPixel: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptSnapchatPixel']
  const useScriptStripe: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptStripe']
  const useScriptTriggerConsent: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerConsent']
  const useScriptTriggerElement: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerElement']
  const useScriptUmamiAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptUmamiAnalytics']
  const useScriptVimeoPlayer: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptVimeoPlayer']
  const useScriptXPixel: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptXPixel']
  const useScriptYouTubePlayer: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptYouTubePlayer']
  const useSeoMeta: typeof import('../../node_modules/nuxt/dist/app/composables/head')['useSeoMeta']
  const useServerHead: typeof import('../../node_modules/nuxt/dist/app/composables/head')['useServerHead']
  const useServerHeadSafe: typeof import('../../node_modules/nuxt/dist/app/composables/head')['useServerHeadSafe']
  const useServerSeoMeta: typeof import('../../node_modules/nuxt/dist/app/composables/head')['useServerSeoMeta']
  const useSetupChildProps: typeof import('../../composables/useComponentVue')['useSetupChildProps']
  const useShadowRoot: typeof import('vue')['useShadowRoot']
  const useShokuinListInfo: typeof import('../../utils/useShokuinListInfo')['useShokuinListInfo']
  const useSlots: typeof import('vue')['useSlots']
  const useStaffProfileView: typeof import('../../composables/staffsProfileViewManager')['useStaffProfileView']
  const useState: typeof import('../../node_modules/nuxt/dist/app/composables/state')['useState']
  const useSystemCommonsStore: typeof import('../../stores/session/systemCommons')['useSystemCommonsStore']
  const useTableCpValidation: typeof import('../../composables/useTableCpValidation')['useTableCpValidation']
  const useTemplateRef: typeof import('vue')['useTemplateRef']
  const useTransitionState: typeof import('vue')['useTransitionState']
  const useTwoWayBindAccessor: typeof import('../../composables/useComponentLogic')['useTwoWayBindAccessor']
  const useUserListHistoryInfo: typeof import('../../utils/useUserListHistoryInfo')['useUserListHistoryInfo']
  const useUserListInfo: typeof import('../../utils/useUserListInfo')['useUserListInfo']
  const useUsersProfileView: typeof import('../../composables/usersProfileViewManager')['useUsersProfileView']
  const useUtils: typeof import('../../utils/useUtils')['useUtils']
  const useValidation: typeof import('../../utils/useValidation')['useValidation']
  const useViewSelectStore: typeof import('../../stores/session/viewSelect')['useViewSelectStore']
  const watch: typeof import('vue')['watch']
  const watchEffect: typeof import('vue')['watchEffect']
  const watchPostEffect: typeof import('vue')['watchPostEffect']
  const watchSyncEffect: typeof import('vue')['watchSyncEffect']
  const withCtx: typeof import('vue')['withCtx']
  const withDirectives: typeof import('vue')['withDirectives']
  const withKeys: typeof import('vue')['withKeys']
  const withMemo: typeof import('vue')['withMemo']
  const withModifiers: typeof import('vue')['withModifiers']
  const withScopeId: typeof import('vue')['withScopeId']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Component, ComponentPublicInstance, ComputedRef, DirectiveBinding, ExtractDefaultPropTypes, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, MaybeRef, MaybeRefOrGetter, VNode, WritableComputedRef } from 'vue'
  import('vue')
  // @ts-ignore
  export type { DataOptions, StateOptions, EventOptions } from '../../composables/currentScreenManager'
  import('../../composables/currentScreenManager')
  // @ts-ignore
  export type { UseValidationOptions } from '../../composables/useTableCpValidation'
  import('../../composables/useTableCpValidation')
  // @ts-ignore
  export type { fileUploadDownloadRequestRepository, fileUploadRequestOutEntity, fileUploadApiOutEntity, getPresignedUrlForDownloadRequestOutEntity, downloadPresignedUrlApiOutEntity, execPresignedUrlOutEntity, commonFileDownloadOutEntity } from '../../utils/fileUploadDownloadRequestRepository'
  import('../../utils/fileUploadDownloadRequestRepository')
  // @ts-ignore
  export type { OutParam } from '../../utils/useApi'
  import('../../utils/useApi')
  // @ts-ignore
  export type { CookieParam } from '../../utils/useCookieData'
  import('../../utils/useCookieData')
  // @ts-ignore
  export type { HistoryInfo } from '../../utils/usePrint'
  import('../../utils/usePrint')
  // @ts-ignore
  export type { ReportOutputType } from '../../utils/useReportUtils'
  import('../../utils/useReportUtils')
  // @ts-ignore
  export type { CurrentUser } from '../../stores/session/auth'
  import('../../stores/session/auth')
  // @ts-ignore
  export type { ViewSelectStoreEntity } from '../../stores/session/viewSelect'
  import('../../stores/session/viewSelect')
}
// for vue template auto import
import { UnwrapRef } from 'vue'
declare module 'vue' {
  interface ComponentCustomProperties {
    readonly DOWNLOAD: UnwrapRef<typeof import('../../utils/useReportUtils')['DOWNLOAD']>
    readonly STATUS_CODE_SUCCESS_200: UnwrapRef<typeof import('../../utils/usePrint')['STATUS_CODE_SUCCESS_200']>
    readonly STATUS_CODE_SUCCESS_204: UnwrapRef<typeof import('../../utils/usePrint')['STATUS_CODE_SUCCESS_204']>
    readonly VIEWER_TAB: UnwrapRef<typeof import('../../utils/useReportUtils')['VIEWER_TAB']>
    readonly VIEWER_WINDOW: UnwrapRef<typeof import('../../utils/useReportUtils')['VIEWER_WINDOW']>
    readonly abortNavigation: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['abortNavigation']>
    readonly acceptHMRUpdate: UnwrapRef<typeof import('../../node_modules/@pinia/nuxt/dist/runtime/composables')['acceptHMRUpdate']>
    readonly addRouteMiddleware: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['addRouteMiddleware']>
    readonly callOnce: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/once')['callOnce']>
    readonly cancelIdleCallback: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/idle-callback')['cancelIdleCallback']>
    readonly clearError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/error')['clearError']>
    readonly clearNuxtData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['clearNuxtData']>
    readonly clearNuxtState: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/state')['clearNuxtState']>
    readonly computed: UnwrapRef<typeof import('vue')['computed']>
    readonly createError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/error')['createError']>
    readonly customRef: UnwrapRef<typeof import('vue')['customRef']>
    readonly dateUtils: UnwrapRef<typeof import('../../utils/dateUtils')['dateUtils']>
    readonly defineAppConfig: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['defineAppConfig']>
    readonly defineAsyncComponent: UnwrapRef<typeof import('vue')['defineAsyncComponent']>
    readonly defineComponent: UnwrapRef<typeof import('vue')['defineComponent']>
    readonly defineNuxtComponent: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/component')['defineNuxtComponent']>
    readonly defineNuxtLink: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/components/nuxt-link')['defineNuxtLink']>
    readonly defineNuxtPlugin: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['defineNuxtPlugin']>
    readonly defineNuxtRouteMiddleware: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['defineNuxtRouteMiddleware']>
    readonly definePageMeta: UnwrapRef<typeof import('../../node_modules/nuxt/dist/pages/runtime/composables')['definePageMeta']>
    readonly definePayloadPlugin: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['definePayloadPlugin']>
    readonly definePayloadReducer: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/payload')['definePayloadReducer']>
    readonly definePayloadReviver: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/payload')['definePayloadReviver']>
    readonly defineStore: UnwrapRef<typeof import('../../node_modules/@pinia/nuxt/dist/runtime/composables')['defineStore']>
    readonly effect: UnwrapRef<typeof import('vue')['effect']>
    readonly effectScope: UnwrapRef<typeof import('vue')['effectScope']>
    readonly fileUploadDownloadRequestRepository: UnwrapRef<typeof import('../../utils/fileUploadDownloadRequestRepository')['fileUploadDownloadRequestRepository']>
    readonly getAppManifest: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/manifest')['getAppManifest']>
    readonly getCurrentInstance: UnwrapRef<typeof import('vue')['getCurrentInstance']>
    readonly getCurrentScope: UnwrapRef<typeof import('vue')['getCurrentScope']>
    readonly getGlobalUniqueScreenIdForPage: UnwrapRef<typeof import('../../composables/uniqueScreenIdControl')['getGlobalUniqueScreenIdForPage']>
    readonly getGlobalUniqueScreenIdsForPushState: UnwrapRef<typeof import('../../composables/uniqueScreenIdControl')['getGlobalUniqueScreenIdsForPushState']>
    readonly getNewGlobalUniqueScreenId: UnwrapRef<typeof import('../../composables/uniqueScreenIdControl')['getNewGlobalUniqueScreenId']>
    readonly getNextHistoryId: UnwrapRef<typeof import('../../composables/uniqueScreenIdControl')['getNextHistoryId']>
    readonly getNowDatetime: UnwrapRef<typeof import('../../composables/uniqueScreenIdControl')['getNowDatetime']>
    readonly getNowUnixTime: UnwrapRef<typeof import('../../composables/uniqueScreenIdControl')['getNowUnixTime']>
    readonly getOldPushStateFromPopstate: UnwrapRef<typeof import('../../composables/uniqueScreenIdControl')['getOldPushStateFromPopstate']>
    readonly getRandomNum: UnwrapRef<typeof import('../../composables/uniqueScreenIdControl')['getRandomNum']>
    readonly getRouteRules: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/manifest')['getRouteRules']>
    readonly getSequencedCpId: UnwrapRef<typeof import('../../utils/useScreenUtils')['getSequencedCpId']>
    readonly h: UnwrapRef<typeof import('vue')['h']>
    readonly hasInjectionContext: UnwrapRef<typeof import('vue')['hasInjectionContext']>
    readonly hasOutputAuth: UnwrapRef<typeof import('../../utils/useCmnAuthz')['hasOutputAuth']>
    readonly hasPrintAuth: UnwrapRef<typeof import('../../utils/useCmnAuthz')['hasPrintAuth']>
    readonly hasRegistAuth: UnwrapRef<typeof import('../../utils/useCmnAuthz')['hasRegistAuth']>
    readonly hasViewAuth: UnwrapRef<typeof import('../../utils/useCmnAuthz')['hasViewAuth']>
    readonly initEditFlg: UnwrapRef<typeof import('../../composables/useComponentVue')['initEditFlg']>
    readonly inject: UnwrapRef<typeof import('vue')['inject']>
    readonly injectHead: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/head')['injectHead']>
    readonly isNuxtError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/error')['isNuxtError']>
    readonly isPrerendered: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/payload')['isPrerendered']>
    readonly isProxy: UnwrapRef<typeof import('vue')['isProxy']>
    readonly isReactive: UnwrapRef<typeof import('vue')['isReactive']>
    readonly isReadonly: UnwrapRef<typeof import('vue')['isReadonly']>
    readonly isRef: UnwrapRef<typeof import('vue')['isRef']>
    readonly isShallow: UnwrapRef<typeof import('vue')['isShallow']>
    readonly isVue2: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue2']>
    readonly isVue3: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue3']>
    readonly loadPayload: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/payload')['loadPayload']>
    readonly markRaw: UnwrapRef<typeof import('vue')['markRaw']>
    readonly mergeModels: UnwrapRef<typeof import('vue')['mergeModels']>
    readonly mockUtils: UnwrapRef<typeof import('../../utils/mockUtils')['mockUtils']>
    readonly navigateTo: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['navigateTo']>
    readonly nextTick: UnwrapRef<typeof import('vue')['nextTick']>
    readonly onActivated: UnwrapRef<typeof import('vue')['onActivated']>
    readonly onBeforeMount: UnwrapRef<typeof import('vue')['onBeforeMount']>
    readonly onBeforeRouteLeave: UnwrapRef<typeof import('vue-router')['onBeforeRouteLeave']>
    readonly onBeforeRouteUpdate: UnwrapRef<typeof import('vue-router')['onBeforeRouteUpdate']>
    readonly onBeforeUnmount: UnwrapRef<typeof import('vue')['onBeforeUnmount']>
    readonly onBeforeUpdate: UnwrapRef<typeof import('vue')['onBeforeUpdate']>
    readonly onDeactivated: UnwrapRef<typeof import('vue')['onDeactivated']>
    readonly onErrorCaptured: UnwrapRef<typeof import('vue')['onErrorCaptured']>
    readonly onMounted: UnwrapRef<typeof import('vue')['onMounted']>
    readonly onNuxtReady: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ready')['onNuxtReady']>
    readonly onPrehydrate: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['onPrehydrate']>
    readonly onRenderTracked: UnwrapRef<typeof import('vue')['onRenderTracked']>
    readonly onRenderTriggered: UnwrapRef<typeof import('vue')['onRenderTriggered']>
    readonly onScopeDispose: UnwrapRef<typeof import('vue')['onScopeDispose']>
    readonly onServerPrefetch: UnwrapRef<typeof import('vue')['onServerPrefetch']>
    readonly onUnmounted: UnwrapRef<typeof import('vue')['onUnmounted']>
    readonly onUpdated: UnwrapRef<typeof import('vue')['onUpdated']>
    readonly persistedState: UnwrapRef<typeof import('../../node_modules/@pinia-plugin-persistedstate/nuxt/dist/runtime/storages')['persistedState']>
    readonly postRequest: UnwrapRef<typeof import('../../utils/useApi')['postRequest']>
    readonly prefetchComponents: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/preload')['prefetchComponents']>
    readonly preloadComponents: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/preload')['preloadComponents']>
    readonly preloadPayload: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/payload')['preloadPayload']>
    readonly preloadRouteComponents: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/preload')['preloadRouteComponents']>
    readonly prerenderRoutes: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['prerenderRoutes']>
    readonly provide: UnwrapRef<typeof import('vue')['provide']>
    readonly proxyRefs: UnwrapRef<typeof import('vue')['proxyRefs']>
    readonly reactive: UnwrapRef<typeof import('vue')['reactive']>
    readonly readonly: UnwrapRef<typeof import('vue')['readonly']>
    readonly ref: UnwrapRef<typeof import('vue')['ref']>
    readonly refreshCookie: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/cookie')['refreshCookie']>
    readonly refreshNuxtData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['refreshNuxtData']>
    readonly reloadNuxtApp: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/chunk')['reloadNuxtApp']>
    readonly requestIdleCallback: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/idle-callback')['requestIdleCallback']>
    readonly resolveComponent: UnwrapRef<typeof import('vue')['resolveComponent']>
    readonly setBlankGlobalUniqueScreenIds: UnwrapRef<typeof import('../../composables/uniqueScreenIdControl')['setBlankGlobalUniqueScreenIds']>
    readonly setGlobalUniqueScreenIdsFromPopstate: UnwrapRef<typeof import('../../composables/uniqueScreenIdControl')['setGlobalUniqueScreenIdsFromPopstate']>
    readonly setInterval: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/interval')['setInterval']>
    readonly setPageLayout: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['setPageLayout']>
    readonly setResponseStatus: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['setResponseStatus']>
    readonly shallowReactive: UnwrapRef<typeof import('vue')['shallowReactive']>
    readonly shallowReadonly: UnwrapRef<typeof import('vue')['shallowReadonly']>
    readonly shallowRef: UnwrapRef<typeof import('vue')['shallowRef']>
    readonly showError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/error')['showError']>
    readonly storeToRefs: UnwrapRef<typeof import('../../node_modules/@pinia/nuxt/dist/runtime/composables')['storeToRefs']>
    readonly toRaw: UnwrapRef<typeof import('vue')['toRaw']>
    readonly toRef: UnwrapRef<typeof import('vue')['toRef']>
    readonly toRefs: UnwrapRef<typeof import('vue')['toRefs']>
    readonly toValue: UnwrapRef<typeof import('vue')['toValue']>
    readonly triggerRef: UnwrapRef<typeof import('vue')['triggerRef']>
    readonly tryUseNuxtApp: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['tryUseNuxtApp']>
    readonly unref: UnwrapRef<typeof import('vue')['unref']>
    readonly updateAppConfig: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/config')['updateAppConfig']>
    readonly updateState: UnwrapRef<typeof import('../../composables/useComponentVue')['updateState']>
    readonly useApi: UnwrapRef<typeof import('../../utils/useApi')['useApi']>
    readonly useAppConfig: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/config')['useAppConfig']>
    readonly useAreaLinkedStore: UnwrapRef<typeof import('../../stores/session/business-platform/areaLinked')['useAreaLinkedStore']>
    readonly useAsyncData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useAsyncData']>
    readonly useAttrs: UnwrapRef<typeof import('vue')['useAttrs']>
    readonly useAuthn: UnwrapRef<typeof import('../../utils/useAuthn')['useAuthn']>
    readonly useAuthz: UnwrapRef<typeof import('../../utils/useAuthz')['useAuthz']>
    readonly useBusinessPlatformScreenStore: UnwrapRef<typeof import('../../stores/session/business-platform/businessPlatformScreen')['useBusinessPlatformScreenStore']>
    readonly useCacheStore: UnwrapRef<typeof import('../../stores/session/business-platform/cache')['useCacheStore']>
    readonly useCmnCom: UnwrapRef<typeof import('../../utils/useCmnCom')['useCmnCom']>
    readonly useCmnRouteCom: UnwrapRef<typeof import('../../utils/useCmnRouteCom')['useCmnRouteCom']>
    readonly useColorUtils: UnwrapRef<typeof import('../../utils/useColorUtils')['useColorUtils']>
    readonly useCommonProps: UnwrapRef<typeof import('../../composables/useCommonProps')['useCommonProps']>
    readonly useComponentSelector: UnwrapRef<typeof import('../../utils/useComponentSelector')['useComponentSelector']>
    readonly useCookie: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/cookie')['useCookie']>
    readonly useCookieData: UnwrapRef<typeof import('../../utils/useCookieData')['useCookieData']>
    readonly useCssModule: UnwrapRef<typeof import('vue')['useCssModule']>
    readonly useCssVars: UnwrapRef<typeof import('vue')['useCssVars']>
    readonly useCurrentScreenData: UnwrapRef<typeof import('../../composables/currentScreenManager')['useCurrentScreenData']>
    readonly useCurrentScreenEvent: UnwrapRef<typeof import('../../composables/currentScreenManager')['useCurrentScreenEvent']>
    readonly useCurrentScreenState: UnwrapRef<typeof import('../../composables/currentScreenManager')['useCurrentScreenState']>
    readonly useCurrentScreenStore: UnwrapRef<typeof import('../../stores/session/currentScreen')['useCurrentScreenStore']>
    readonly useCurrentScreenUtils: UnwrapRef<typeof import('../../utils/useCurrentScreenUtils')['useCurrentScreenUtils']>
    readonly useCurrentUserStore: UnwrapRef<typeof import('../../stores/session/auth')['useCurrentUserStore']>
    readonly useData: UnwrapRef<typeof import('../../utils/useData')['useData']>
    readonly useError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/error')['useError']>
    readonly useEventStatusAccessor: UnwrapRef<typeof import('../../composables/useComponentLogic')['useEventStatusAccessor']>
    readonly useFetch: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/fetch')['useFetch']>
    readonly useFileUtils: UnwrapRef<typeof import('../../utils/useFileUtils')['useFileUtils']>
    readonly useGyoumuCom: UnwrapRef<typeof import('../../utils/useGyoumuCom')['useGyoumuCom']>
    readonly useHead: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/head')['useHead']>
    readonly useHeadSafe: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/head')['useHeadSafe']>
    readonly useHydration: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/hydrate')['useHydration']>
    readonly useId: UnwrapRef<typeof import('vue')['useId']>
    readonly useInitialize: UnwrapRef<typeof import('../../composables/useComponentLogic')['useInitialize']>
    readonly useJigyoList: UnwrapRef<typeof import('../../utils/useJigyoList')['useJigyoList']>
    readonly useLazyAsyncData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useLazyAsyncData']>
    readonly useLazyFetch: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/fetch')['useLazyFetch']>
    readonly useLink: UnwrapRef<typeof import('vue-router')['useLink']>
    readonly useLoadingIndicator: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/loading-indicator')['useLoadingIndicator']>
    readonly useModel: UnwrapRef<typeof import('vue')['useModel']>
    readonly useNuxtApp: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['useNuxtApp']>
    readonly useNuxtData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useNuxtData']>
    readonly useNuxtDevTools: UnwrapRef<typeof import('../../node_modules/@nuxt/devtools/dist/runtime/use-nuxt-devtools')['useNuxtDevTools']>
    readonly useOneWayBindAccessor: UnwrapRef<typeof import('../../composables/useComponentLogic')['useOneWayBindAccessor']>
    readonly usePinia: UnwrapRef<typeof import('../../node_modules/@pinia/nuxt/dist/runtime/composables')['usePinia']>
    readonly usePreviewMode: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/preview')['usePreviewMode']>
    readonly usePrint: UnwrapRef<typeof import('../../utils/usePrint')['usePrint']>
    readonly useReportUtils: UnwrapRef<typeof import('../../utils/useReportUtils')['useReportUtils']>
    readonly useRequestEvent: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestEvent']>
    readonly useRequestFetch: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestFetch']>
    readonly useRequestHeader: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestHeader']>
    readonly useRequestHeaders: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestHeaders']>
    readonly useRequestURL: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/url')['useRequestURL']>
    readonly useResponseHeader: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useResponseHeader']>
    readonly useRoute: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['useRoute']>
    readonly useRouteAnnouncer: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/route-announcer')['useRouteAnnouncer']>
    readonly useRouteCommonsStore: UnwrapRef<typeof import('../../stores/session/routeCommons')['useRouteCommonsStore']>
    readonly useRouter: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['useRouter']>
    readonly useRuntimeConfig: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['useRuntimeConfig']>
    readonly useRuntimeHook: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/runtime-hook')['useRuntimeHook']>
    readonly useScreenEventStatus: UnwrapRef<typeof import('../../composables/useComponentVue')['useScreenEventStatus']>
    readonly useScreenInitFlg: UnwrapRef<typeof import('../../composables/useComponentVue')['useScreenInitFlg']>
    readonly useScreenNavControl: UnwrapRef<typeof import('../../composables/useComponentVue')['useScreenNavControl']>
    readonly useScreenOneWayBind: UnwrapRef<typeof import('../../composables/useComponentVue')['useScreenOneWayBind']>
    readonly useScreenStore: UnwrapRef<typeof import('../../stores/session/screen')['useScreenStore']>
    readonly useScreenTwoWayBind: UnwrapRef<typeof import('../../composables/useComponentVue')['useScreenTwoWayBind']>
    readonly useScreenUtils: UnwrapRef<typeof import('../../utils/useScreenUtils')['useScreenUtils']>
    readonly useScript: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScript']>
    readonly useScriptClarity: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptClarity']>
    readonly useScriptCloudflareWebAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCloudflareWebAnalytics']>
    readonly useScriptCrisp: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCrisp']>
    readonly useScriptEventPage: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptEventPage']>
    readonly useScriptFathomAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptFathomAnalytics']>
    readonly useScriptGoogleAdsense: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAdsense']>
    readonly useScriptGoogleAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAnalytics']>
    readonly useScriptGoogleMaps: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleMaps']>
    readonly useScriptGoogleTagManager: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleTagManager']>
    readonly useScriptHotjar: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptHotjar']>
    readonly useScriptIntercom: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptIntercom']>
    readonly useScriptLemonSqueezy: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptLemonSqueezy']>
    readonly useScriptMatomoAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMatomoAnalytics']>
    readonly useScriptMetaPixel: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMetaPixel']>
    readonly useScriptNpm: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptNpm']>
    readonly useScriptPlausibleAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptPlausibleAnalytics']>
    readonly useScriptSegment: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptSegment']>
    readonly useScriptSnapchatPixel: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptSnapchatPixel']>
    readonly useScriptStripe: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptStripe']>
    readonly useScriptTriggerConsent: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerConsent']>
    readonly useScriptTriggerElement: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerElement']>
    readonly useScriptUmamiAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptUmamiAnalytics']>
    readonly useScriptVimeoPlayer: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptVimeoPlayer']>
    readonly useScriptXPixel: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptXPixel']>
    readonly useScriptYouTubePlayer: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptYouTubePlayer']>
    readonly useSeoMeta: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/head')['useSeoMeta']>
    readonly useServerHead: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/head')['useServerHead']>
    readonly useServerHeadSafe: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/head')['useServerHeadSafe']>
    readonly useServerSeoMeta: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/head')['useServerSeoMeta']>
    readonly useSetupChildProps: UnwrapRef<typeof import('../../composables/useComponentVue')['useSetupChildProps']>
    readonly useShadowRoot: UnwrapRef<typeof import('vue')['useShadowRoot']>
    readonly useShokuinListInfo: UnwrapRef<typeof import('../../utils/useShokuinListInfo')['useShokuinListInfo']>
    readonly useSlots: UnwrapRef<typeof import('vue')['useSlots']>
    readonly useStaffProfileView: UnwrapRef<typeof import('../../composables/staffsProfileViewManager')['useStaffProfileView']>
    readonly useState: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/state')['useState']>
    readonly useSystemCommonsStore: UnwrapRef<typeof import('../../stores/session/systemCommons')['useSystemCommonsStore']>
    readonly useTableCpValidation: UnwrapRef<typeof import('../../composables/useTableCpValidation')['useTableCpValidation']>
    readonly useTemplateRef: UnwrapRef<typeof import('vue')['useTemplateRef']>
    readonly useTransitionState: UnwrapRef<typeof import('vue')['useTransitionState']>
    readonly useTwoWayBindAccessor: UnwrapRef<typeof import('../../composables/useComponentLogic')['useTwoWayBindAccessor']>
    readonly useUserListHistoryInfo: UnwrapRef<typeof import('../../utils/useUserListHistoryInfo')['useUserListHistoryInfo']>
    readonly useUserListInfo: UnwrapRef<typeof import('../../utils/useUserListInfo')['useUserListInfo']>
    readonly useUsersProfileView: UnwrapRef<typeof import('../../composables/usersProfileViewManager')['useUsersProfileView']>
    readonly useUtils: UnwrapRef<typeof import('../../utils/useUtils')['useUtils']>
    readonly useValidation: UnwrapRef<typeof import('../../utils/useValidation')['useValidation']>
    readonly useViewSelectStore: UnwrapRef<typeof import('../../stores/session/viewSelect')['useViewSelectStore']>
    readonly watch: UnwrapRef<typeof import('vue')['watch']>
    readonly watchEffect: UnwrapRef<typeof import('vue')['watchEffect']>
    readonly watchPostEffect: UnwrapRef<typeof import('vue')['watchPostEffect']>
    readonly watchSyncEffect: UnwrapRef<typeof import('vue')['watchSyncEffect']>
    readonly withCtx: UnwrapRef<typeof import('vue')['withCtx']>
    readonly withDirectives: UnwrapRef<typeof import('vue')['withDirectives']>
    readonly withKeys: UnwrapRef<typeof import('vue')['withKeys']>
    readonly withMemo: UnwrapRef<typeof import('vue')['withMemo']>
    readonly withModifiers: UnwrapRef<typeof import('vue')['withModifiers']>
    readonly withScopeId: UnwrapRef<typeof import('vue')['withScopeId']>
  }
}