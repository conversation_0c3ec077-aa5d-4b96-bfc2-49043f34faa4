<script setup lang="ts">
/**
 * Or00725:有機体:(内容マスタ)アクションエリア
 * GUI00935_内容マスタ
 *
 * @description
 * 内容マスタ画面
 *
 * <AUTHOR>
 */

import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { v4 as uuidv4 } from 'uuid'
import type {ContentMasterItem } from './Or00725.type'
import { Or00725Const } from './Or00725.constants'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import { useScreenStore, useScreenTwoWayBind, useSetupChildProps } from '#build/imports'
import { useScreenUtils } from '~/utils/useScreenUtils'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { UPDATE_KBN } from '~/constants/classification-constants'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  ContextMasterInitSelectInEntity,
  ContextMasterInitSelectOutEntity,
} from '~/repositories/cmn/entities/ContextMasterInitSelectEntity'
import type {ContextMasterInsertInEntity} from '~/repositories/cmn/entities/ContextMasterInsertEntity'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import type { Mo01354OnewayType, Mo01354Type } from '~/components/base-components/molecules/Mo01354/Mo01354Type'
const { setChildCpBinds } = useScreenUtils()
const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  parentUniqueCpId: string
}
const props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/
// ローカルTwoway
const local = reactive({
  mo00043: { id: Or00725Const.TAB.TAB_ID_DAILY_LIFE_ACTIVITIES } as Mo00043Type,
  mo00043Transfer: { id: Or00725Const.TAB.TAB_ID_DAILY_LIFE_ACTIVITIES } as Mo00043Type,
  mo00043Change: { id: Or00725Const.TAB.TAB_ID_DAILY_LIFE_ACTIVITIES } as Mo00043Type,
  mo01354: {
    values: {
      selectedRowId: '',
      selectedRowIds:[],
      items: []
    }
  } as Mo01354Type,
})

// ローカルOneway
const localOneway = reactive({
  or00725Oneway: {
    kbn: Or00725Const.TAB.TAB_ID_DAILY_LIFE_ACTIVITIES,
  },
  // タブ
  mo00043OneWay: {
    tabItems: [
      {
        id: Or00725Const.TAB.TAB_ID_DAILY_LIFE_ACTIVITIES,
        title: t('label.daily-life-activities'),
      },
      {
        id: Or00725Const.TAB.TAB_ID_TREATMENT_RELATED_TO_SELF_RELIANCE_SUPPORT,
        title: t('label.treatment-related-to-self-reliance-support'),
      },
      {
        id: Or00725Const.TAB.TAB_ID_NURSING_PREVENTIVE_CARE_SERVICES,
        title: t('label.nursing-preventive-care-services'),
      },
      { id: Or00725Const.TAB.TAB_ID_OTHERS, title: t('label.others-note') },
    ],
  } as Mo00043OnewayType,
  //  行追加ボタン
  mo00611OnewayAdd: {
    btnLabel: t('btn.add-row'),
    prependIcon: 'add',
    width: '90px',
  } as Mo00611OnewayType,
  // 行複写ボタン
  mo00611OnewayCopy: {
    btnLabel: t('btn.duplicate-row'),
    prependIcon: 'file_copy',
    width: '90px',
  } as Mo00611OnewayType,
  // 行削除ボタン
  mo01265Oneway: {
    btnLabel: t('btn.delete-row'),
    prependIcon: 'delete',
    width: '90px',
    disabled: true,
  } as Mo01265OnewayType,
  mo01354Oneway: {
    headers: [
      { title: t('label.cd'), key: 'naiyoCd', minWidth: '50px', sortable: false },
      { title: t('label.content'), key: 'naiyoKnj', minWidth: '250px', sortable: false},
      { title: t('label.sort'), key: 'seq', minWidth: '80px', sortable: false },
    ],
    height: '180px',
    columnMinWidth: {columnWidths:[50,272,90]}
  } as Mo01354OnewayType,
})
const or21813_1 = ref({ uniqueCpId: '' })
const or21814_1 = ref({ uniqueCpId: '' })
const or21815_1 = ref({ uniqueCpId: '' })
// "*"
const required: string = Or00725Const.DEFAULT.REQUIRED
/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<ContentMasterItem[]>({
  cpId: Or00725Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
refValue.value = []

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21813Const.CP_ID(1)]: or21813_1.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or21815Const.CP_ID(1)]: or21815_1.value,
})

/**************************************************
 * ウォッチャー
 **************************************************/
onMounted(async () => {
  // コントロール設定
  local.mo00043.id = Or00725Const.TAB.TAB_ID_DAILY_LIFE_ACTIVITIES
  await getInitDataInfo()
  Or21813Logic.state.set({
    uniqueCpId: or21813_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.e-cmn-41708'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10430'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })
  Or21815Logic.state.set({
    uniqueCpId: or21815_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: t('message.w-cmn-20803'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
    },
  })
})

/**
 *  初期情報取得
 */
async function getInitDataInfo() {
  const inputData: ContextMasterInitSelectInEntity = {
    dataKbn: localOneway.or00725Oneway.kbn,
  }

  const ret: ContextMasterInitSelectOutEntity = await ScreenRepository.select(
    'contextMasterInitSelect',
    inputData
  )

  const dataArr = []
  for (const item of ret.data.contextMasterInitList) {
    dataArr.push({
      id : uuidv4(),
      naiyoCd: item.naiyoCd,
      dataKbn: item.dataKbn,
      naiyoKnj: { value: item.naiyoKnj },
      seq: { value: item.seq },
      updateKbn: UPDATE_KBN.NONE,
      modifiedCnt: item.modifiedCnt,
    })
  }
  local.mo01354.values.selectedRowId = ''
  setChildCpBinds(props.parentUniqueCpId, {
    Or00725: {
      twoWayValue: dataArr,
    },
  })
}

/**************************************************
 * 関数
 **************************************************/
// メニュー切替
watch(
  () => local.mo00043Transfer.id,
  async (newValue) => {
    if (local.mo00043.id !== newValue) {
      if (isEdit.value) {
        local.mo00043Change.id = newValue
        local.mo00043Transfer.id = local.mo00043.id
        const dialogResult = await openEditDialog()
        switch (dialogResult) {
          case Or00725Const.DEFAULT.DIALOG_RESULT_YES:
            await save()
            localOneway.or00725Oneway.kbn = newValue
            local.mo00043.id = newValue
            local.mo00043Transfer.id = newValue
            await getInitDataInfo()
            break
          case Or00725Const.DEFAULT.DIALOG_RESULT_NO:
            local.mo00043.id = newValue
            local.mo00043Transfer.id = newValue
            localOneway.or00725Oneway.kbn = newValue
            await getInitDataInfo()
            break
          case Or00725Const.DEFAULT.DIALOG_RESULT_CANCEL:
            // キャンセル選択時は複写データの作成を行わずに終了する
            return
        }
      } else {
        local.mo00043.id = newValue
        localOneway.or00725Oneway.kbn = newValue
        await getInitDataInfo()
      }
    }
  }
)

watch(
  () => refValue.value,
  () => {
    local.mo01354.values.items = refValue.value!.filter((i) => i.updateKbn !== UPDATE_KBN.DELETE);
  }
)

/**
 * 行追加
 */
function onAddItem() {
  let lastSort = 0
  if (refValue.value!.length > 0) {
    for (const data of refValue.value!) {
      if (data.updateKbn !== UPDATE_KBN.DELETE) {
        lastSort = Math.max(lastSort, Number(data.seq.value))
      }
    }
  }
  // 表示順最大値＜9999の場合
  if (lastSort < 9999) {
    // 表示順最大値＋1を設定
    lastSort += 1
  } else {
    // 上記以外の場合、9999を設定
    lastSort = 9999
  }
  const newId = uuidv4()
  const data = {
    id: newId,
    naiyoCd: '',
    dataKbn: localOneway.or00725Oneway.kbn,
    naiyoKnj: {
      value: '',
    },
    seq: {
      value: lastSort.toString(),
    },
    updateKbn: UPDATE_KBN.CREATE,
    modifiedCnt: '',
  }
  refValue.value!.push(data)
  local.mo01354.values.selectedRowId =  newId
}
/**
 * 行複写
 */
function onCloneItem() {
  if (local.mo01354.values.selectedRowId) {
    const copydata = refValue.value?.find((item) => item.id === local.mo01354.values.selectedRowId)
    if (copydata) {
      const newId = uuidv4()
      const data = {
        id: newId,
        naiyoCd: '',
        dataKbn: copydata.dataKbn,
        naiyoKnj: {
          value: copydata.naiyoKnj.value,
        },
        seq: {
          value: copydata.seq.value,
        },
        updateKbn: UPDATE_KBN.CREATE,
        modifiedCnt: '',
      }
      const index = refValue.value?.findIndex(element => element.id === copydata.id);
      if (index) {
        refValue.value?.splice(index + 1, 0, data)
        local.mo01354.values.selectedRowId =  newId
      }
    }
  }
}

/**
 * 行削除
 */
async function onDelete() {
  const dialogResult = await openWarnDialog()
  switch (dialogResult) {
    case Or00725Const.DEFAULT.DIALOG_RESULT_YES:
      if (local.mo01354.values.selectedRowId) {
        const copydata = refValue.value?.find((item) => item.id === local.mo01354.values.selectedRowId)
        if (copydata) {
          if (copydata.updateKbn === UPDATE_KBN.CREATE) {
            refValue.value = refValue.value?.filter((item) => item.id !== local.mo01354.values.selectedRowId)
          } else {
            copydata.updateKbn = UPDATE_KBN.DELETE
          }
        }
      }
      break
    case Or00725Const.DEFAULT.DIALOG_RESULT_NO:
      break
  }
}

// 行挿入,行複写,行削除の活性制御
const disableButtons = computed(() => {
  return local.mo01354.values.items.length === 0 || !local.mo01354.values.selectedRowId
})

/**
 * コンテンツの更新
 *
 */
function onUpdate() {
    if (local.mo01354.values.selectedRowId) {
    const data = refValue.value?.find((item) => item.id === local.mo01354.values.selectedRowId)
    if (data) {
      if (data.updateKbn !== UPDATE_KBN.CREATE) {
        data.updateKbn = UPDATE_KBN.UPDATE
      }
    }
  }
}

/**
 * 保存
 */
async function save() {
  const dataArray = refValue.value?.filter((data) => data.updateKbn !== '')
  if (!dataArray || dataArray.length <= 0) {
    return true
  }
  const updateArray = []
  for (const item of dataArray) {
    if (!item.naiyoKnj.value || !item.seq.value) {
      await openErrorDialog()
      return false
    }
    updateArray.push({
      naiyoCd: item.naiyoCd,
      dataKbn: item.dataKbn,
      naiyoKnj: item.naiyoKnj.value,
      seq: item.seq.value,
      modifiedCnt: item.modifiedCnt,
      updateKbn: item.updateKbn,
    })
  }
  const param: ContextMasterInsertInEntity = {
    contextMasterUpdateList: updateArray,
  }
  // 内容マスタ情報保存
  await ScreenRepository.insert('contextMasterInsert',param)
}

/**
 * 編集破棄ダイアログ表示
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
async function openEditDialog(): Promise<string> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)

        let result = Or00725Const.DEFAULT.DIALOG_RESULT_CANCEL

        if (event?.firstBtnClickFlg) {
          result = Or00725Const.DEFAULT.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or00725Const.DEFAULT.DIALOG_RESULT_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = Or00725Const.DEFAULT.DIALOG_RESULT_CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 選択行削除確認ダイアログを閉じたタイミングで結果を返却
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
async function openWarnDialog(): Promise<string> {
  // 選択行削除確認ダイアログを開く
  Or21815Logic.state.set({
    uniqueCpId: or21815_1.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })

  /**
   * 選択行削除確認ダイアログを閉じたタイミングで結果を返却
   *
   * @returns ダイアログの選択結果（yes, no）
   */
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815_1.value.uniqueCpId)

        let result = Or00725Const.DEFAULT.DIALOG_RESULT_CANCEL

        if (event?.firstBtnClickFlg) {
          result = Or00725Const.DEFAULT.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or00725Const.DEFAULT.DIALOG_RESULT_NO
        }

        // 選択行削除確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: or21815_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * errorダイアログを閉じたタイミングで結果を返却
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
async function openErrorDialog(): Promise<string> {
  // 選択行削除確認ダイアログを開く
  Or21813Logic.state.set({
    uniqueCpId: or21813_1.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })

  /**
   * 選択行削除確認ダイアログを閉じたタイミングで結果を返却
   *
   * @returns ダイアログの選択結果（yes, no）
   */
  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813_1.value.uniqueCpId)

        let result = Or00725Const.DEFAULT.DIALOG_RESULT_CANCEL

        if (event?.firstBtnClickFlg) {
          result = Or00725Const.DEFAULT.DIALOG_RESULT_YES
        }

        // 選択行削除確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

defineExpose({
  save,
})

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  return useScreenStore().isEditNavControl()
})
</script>

<template>
  <base-mo00043
    v-model="local.mo00043Transfer"
    :oneway-model-value="localOneway.mo00043OneWay"
  ></base-mo00043>
  <c-v-window v-model="local.mo00043.id">
    <c-v-sheet class="content">
      <c-v-row
        class="justify-center align-center ma-0 text-center"
        style="margin: 0px !important"
      >
        <c-v-col class="v-col">
          <base-mo00611
            :oneway-model-value="localOneway.mo00611OnewayAdd"
            class="mx-0 mr-2"
            :disabled="disableButtons"
            @click="onAddItem"
          >
          </base-mo00611>
          <base-mo00611
            :oneway-model-value="localOneway.mo00611OnewayCopy"
            class="mx-0 mr-2"
            :disabled="disableButtons"
            @click="onCloneItem"
          >
          </base-mo00611>
          <base-mo01265
            :oneway-model-value="localOneway.mo01265Oneway"
            class="mx-0 mr-2"
            :disabled="disableButtons"
            @click="onDelete"
          >
          </base-mo01265>
        </c-v-col>
      </c-v-row>
      <c-v-row
        class="justify-center align-center ma-0 text-center"
        style="margin: 0px !important"
      >
        <c-v-col class="align-center text-center" cols="6">
          <base-mo-01354
            v-model="local.mo01354"
            :oneway-model-value="localOneway.mo01354Oneway"
            class="list-wrapper"
            :hide-default-footer="true"
          >
            <template #[`header.content`]>
              <span>
                <span style="color: red">{{ required }}</span>
                  {{ t('label.naiyoKnj') }}
                </span>
              </template>
              <template #[`header.sort`]>
              <span>
                <span style="color: red">{{ required }}</span>
                  {{ t('label.seq') }}
                </span>
              </template>
            <template #[`item.naiyoCd`]="{ item }">
              <base-mo01337
                :oneway-model-value="{
                  value: item.naiyoCd,
                  customClass:
                    {
                      outerStyle: 'background-color: rgba(0, 0, 0, 0); font-size: 12px !important;',
                      itemClass: 'text-right',
                    },
                }"></base-mo01337>
            </template>
            <template #[`item.naiyoKnj`]="{ item }">
                <base-mo01274
                  v-model="item.naiyoKnj"
                  @change="onUpdate"
                />
            </template>
            <template #[`item.seq`]="{ item }"  >
                <base-mo01274
                  v-model="item.seq"
                  :class="{ 'text-align-right':true, 'text-padding':true }"
                  max-length="4"
                  @change="onUpdate"
                />
            </template>
          </base-mo-01354>
        </c-v-col>
      </c-v-row>
      <g-base-or21813 v-bind="or21813_1" />
      <g-base-or21814 v-bind="or21814_1" />
      <g-base-or21815 v-bind="or21815_1" />
    </c-v-sheet>
    <c-v-window v-model="local.mo00043.id">
      <c-v-window-item value="1"> </c-v-window-item>
      <c-v-window-item value="2"> </c-v-window-item>
      <c-v-window-item value="3"> </c-v-window-item>
      <c-v-window-item value="4"> </c-v-window-item
    ></c-v-window>
  </c-v-window>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table.scss';
</style>
