<script setup lang="ts">
/**
 * Or15109：有機体：（確定版）入院基本情報 ９．かかりつけ医についてセクション
 *
 * <AUTHOR>
 */
import { computed, reactive, ref, type Ref } from 'vue'
import { useI18n } from 'vue-i18n'
import type { CodeType } from '../Or28326/Or28326.type'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import type { Or51775ConfirmType } from '../Or51775/Or51775.type'
import { Or15109Const } from './Or15109.constants'
import type { Or15109ValuesType, Or15109OneWayType } from './Or15109.type'
import {
  useCommonProps,
  useScreenOneWayBind,
  useScreenTwoWayBind,
  useSetupChildProps,
  useSystemCommonsStore,
} from '#imports'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo00046OnewayType, Mo00046Type } from '~/types/business/components/Mo00046Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00039OnewayType } from '~/types/business/components/Mo00039Type'
import type { Mo00030OnewayType } from '~/types/business/components/Mo00030Type'
import type { Mo00038OnewayType } from '~/types/business/components/Mo00038Type'
import type { Or51775OnewayType, Or51775Type } from '~/types/cmn/business/components/Or51775Type'
/**************************************************
 * Props
 **************************************************/
const systemCommonsStore = useSystemCommonsStore()
const props = defineProps(useCommonProps())
/**************************************************
 * Pinia
 **************************************************/
const { t } = useI18n()

const { refValue } = useScreenTwoWayBind<Or15109ValuesType>({
  cpId: Or15109Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
}) as { refValue: Ref<Or15109ValuesType> }
useScreenOneWayBind<Or15109OneWayType>({
  cpId: Or15109Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    codeList: (value) => {
      localOneway.codeListOneway = value as Record<string, CodeType[]>
      console.log(value, '213313222232331')
    },
  },
})
/**************************************************
 * 変数定義
 **************************************************/

/**
 * 共通入力支援画面の確定ボタン押す後の処理
 *
 * @param data - 入力支援情報
 */
const handleOr51775Confirm = (data: Or51775ConfirmType) => {
  const setOrAppendValue = (str: string, data: Or51775ConfirmType) => {
    if (data.type === Or15109Const.DEFAULT.ADD_END_TEXT_IMPORT_TYPE) {
      // 本文末に追加の場合
      return `${str}${data.value}`
    } else if (data.type === Or15109Const.DEFAULT.OVERWRITE_TEXT_IMPORT_TYPE) {
      // 本文上書の場合
      return data.value
    } else {
      return ''
    }
  }
  ;(refValue.value.or15109Values[local.or51775Value] as unknown as Mo00045Type).value =
    setOrAppendValue(
      (refValue.value.or15109Values[local.or51775Value] as unknown as Mo00045Type).value ?? '',
      data
    )
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: false },
  })
}
/**
 * 入力支援アイコンボタンクリック
 *
 * @param column
 *
 * @param title
 *
 * @param t2Cd
 *
 * @param t3Cd
 *
 * @param columnName
 */
const handPropUp = (
  column: string,
  title: string,
  t2Cd: string,
  t3Cd: string,
  columnName: string
) => {
  // GUI00937 共通入力支援画面をポップアップで起動する
  console.log(column, title, t2Cd, t3Cd, columnName)
  local.or51775Value = column
  localOneway.or51775.title = title
  localOneway.or51775.t2Cd = t2Cd
  localOneway.or51775.t3Cd = t3Cd
  localOneway.or51775.columnName = columnName
  localOneway.or51775.inputContents = title
  local.or51775.modelValue =
    (refValue.value.or15109Values[local.or51775Value] as unknown as Mo00045Type).value ?? ''
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

const localOneway = reactive({
  codeListOneway: {} as Record<string, CodeType[]>,
  // GUI00937 共通入力支援画面
  or51775: {
    screenId: 'GUI01300',
    bunruiId: '-', // 分類ID TBD
    t2Cd: '',
    t3Cd: '',
    tableName: 'cpn_tuc_hosp_info_teikyou_data',
    assessmentMethod: '共通情報.アセスメント方式', // アセスメント方式 TBD
    userId: systemCommonsStore.getUserId ?? '',
  } as Or51775OnewayType,
  mo00045Oneway: {
    maxlength: '50',
    width: '410px',
    isVerticalLabel: false,
    showItemLabel: false,
  } as Mo00045OnewayType,
  mo00045Oneway2: {
    maxlength: '41',
    width: '338px',
    isVerticalLabel: false,
    showItemLabel: true,
    itemLabel: t('label.name'),
    customClass: new CustomClass({
      labelClass: 'd-flex align-center',
      labelStyle: 'margin-right:9.99%',
    }),
  } as Mo00045OnewayType,
  mo00046Oneway: {
    autoGrow: false,
    rows: '3',
    maxRows: '3',
    width: '80%',
    maxlength: '4000',
    noResize: true,
  } as Mo00046OnewayType,
  mo00009Oneway: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  mo00030Oneway: {
    mo00045Oneway: {
      showItemLabel: true,
      itemLabel: t('label.name-furigana'),
      isVerticalLabel: false,
      maxLength: '41',
      width: '338px',
      customClass: new CustomClass({
        labelClass: 'd-flex align-center mr-10',
      }),
    } as Mo00045OnewayType,
    mode: '1',
    disalbeSpinBtnDefaultProcessing: false,
  } as Mo00030OnewayType,
  mo00030OnewayTel: {
    mo00045Oneway: {
      showItemLabel: false,
      isVerticalLabel: false,
      maxLength: '4',
      width: '120px',
      customClass: new CustomClass({
        labelClass: 'd-flex align-center mr-2',
      }),
    } as Mo00045OnewayType,
    mode: '1',
    disalbeSpinBtnDefaultProcessing: false,
  } as Mo00030OnewayType,
  mo00038Oneway: {
    mo00045Oneway: {
      itemLabel: t('label.frequency_label'),
      appendLabel: t('label.times_per_month'),
      showItemLabel: true,
      isVerticalLabel: false,
      maxLength: '4',
      width: '125px',
      customClass: new CustomClass({
        labelClass: 'd-flex align-center mr-1',
      }),
    } as Mo00045OnewayType,
    min: 0,
    max: 99,
    disalbeSpinBtnDefaultProcessing: false,
  } as Mo00038OnewayType,
  mo00039Oneway: {
    showItemLabel: false,
    inline: true,
    customClass: { outerClass: 'd-flex align-center' } as CustomClass,
  } as Mo00039OnewayType,
  //９．かかりつけ医についてセクション
  primaryPhysicianTitle: {
    valueFontWeight: 'blod',
    value: t('label.primary_physician_title'),
    customClass: {
      itemStyle: 'font-size:18px; !import',
    } as CustomClass,
  } as Mo01338OnewayType,
})

const or51775 = ref({ uniqueCpId: '' }) // Or51775：有機体：入力支援［ケアマネ］モーダル

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or51775Const.CP_ID(1)]: or51775.value,
})
const local = reactive({
  or51775: { modelValue: '' } as Or51775Type,
  or51775Value: '',
  mo01343: {
    value: '',
    endValue: '',
    mo00024: { isOpen: false },
  },
  or15109: {},
})
/**************************************************
 * 変数定義
 **************************************************/
const mo00030Oneway = ref<Mo00030OnewayType>({
  mo00045Oneway: {
    width: '200px',
    itemLabel: t('label.name-furigana'),
  },
  mode: '1',
})

const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})
</script>

<template>
  <div v-if="refValue.or15109Values">
    <c-v-row class="title">
      <c-v-col>
        <base-mo01338 :oneway-model-value="localOneway.primaryPhysicianTitle"></base-mo01338>
      </c-v-col>
    </c-v-row>

    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.primary_physician_institution_label') }}
        <div class="d-flex align-center">
          <c-v-divider
            class="ml-2"
            vertical
            inset
          />
          <base-mo00009
            :oneway-model-value="localOneway.mo00009Oneway"
            @click="
              handPropUp(
                'hospKnj',
                t('label.primary_physician_institution_label'),
                Or15109Const.DEFAULT.T2_CD_2,
                Or15109Const.DEFAULT.T3_CD_17,
                'hosp_knj'
              )
            "
          />
        </div>
      </c-v-col>
      <c-v-col
        cols="5"
        class="data-cell move"
      >
        <!-- Mo00045 -->
        <base-mo00045
          v-model="refValue.or15109Values.hospKnj"
          :oneway-model-value="localOneway.mo00045Oneway"
        />
      </c-v-col>
      <!-- TEL  電話番号-->
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.tel_label') }}
      </c-v-col>
      <c-v-col
        cols="3"
        class="data-cell"
      >
        <!-- Mo00030 -->
        <base-mo00030
          v-model="refValue.or15109Values.hospTel"
          class="ml-1"
          :oneway-model-value="localOneway.mo00030OnewayTel"
        />
      </c-v-col>
    </c-v-row>
    <!-- 医師名-->
    <c-v-row class="row">
      <c-v-col
        cols="2"
        class="header-cell"
      >
        {{ t('label.physician_name_label') }}
      </c-v-col>
      <c-v-col
        cols="5"
        class="data-cell move"
      >
        <!-- Mo00030：分子：半角文字専用テキストフィールド -->

        <base-mo00030
          v-model="refValue.or15109Values.doctorKana"
          class="mb-1"
          :oneway-model-value="localOneway.mo00030Oneway"
        />

        <base-mo00045
          v-model="refValue.or15109Values.doctorKnj"
          class="mt-1"
          :oneway-model-value="localOneway.mo00045Oneway2"
        />
      </c-v-col>
      <!-- 診察方法・頻度 -->
      <c-v-col
        cols="2"
        class="header-cell"
      >
        <!-- 診察方法・頻度 -->
        {{ t('label.consultation_method_frequency_label') }}
      </c-v-col>
      <c-v-col
        cols="3"
        class="data-cell"
      >
        <!-- Mo00039：分子：半角文字専用テキストフィールド -->
        <base-mo00039
          v-model="refValue.or15109Values.hospHouhou"
          class="mb-2"
          :oneway-model-value="localOneway.mo00039Oneway"
        >
          <base-at-radio
            v-for="(item, index) in localOneway.codeListOneway.MEDICAL_EXAMINATION_METHOD"
            :key="'radio' + '_' + index"
            :name="item.label"
            :radio-label="item.label"
            :value="item.value"
          />
        </base-mo00039>
        <!-- Mo00038 -->
        <base-mo00038
          v-model="refValue.or15109Values.hospKaisu"
          class="mt-2"
          :oneway-model-value="localOneway.mo00038Oneway"
        ></base-mo00038>
      </c-v-col>
    </c-v-row>
    <!-- Or51775: 有機体: GUI00937 入力支援［ケアマネ］をポップアップで起動する。 -->
    <g-custom-or51775
      v-if="showDialogOr51775"
      v-bind="or51775"
      v-model="local.or51775"
      :oneway-model-value="localOneway.or51775"
      @confirm="handleOr51775Confirm"
    />
  </div>
</template>

<style scoped lang="scss">
.row {
  display: flex;
  align-items: center;
  border-bottom: 1px gainsboro solid;
  border-left: 1px gainsboro solid;
  min-height: 62px;
}

.header-cell {
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 12px !important;
}

.header-title-cell {
  background-color: transparent;
  border-right: 1px gainsboro solid;
  display: grid;
  align-items: center;
}

.data-cell {
  border-left: 1px gainsboro solid;
  border-right: 1px gainsboro solid;
  background: #fff;
  padding: 10px 12px;
  width: 100%;
  min-height: 62px;
  display: grid;
  align-items: center;
}

:deep(.v-input__control) {
  background-color: rgb(var(--v-theme-surface));
}
.flex-container {
  display: flex;
  align-items: center; /* 垂直居中对齐 */
}
.title {
  margin-top: 12px;
  background-color: #fff;
  border-left: 1px gainsboro solid;
  border-right: 1px gainsboro solid;
  border-bottom: 1px gainsboro solid;
}
.move {
  padding: 13px 2%;
}
</style>
