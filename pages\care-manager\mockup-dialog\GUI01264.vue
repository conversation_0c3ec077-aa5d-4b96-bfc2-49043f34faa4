<script lang="ts" setup>
/**
 * GUI01264_印刷設定
 *
 * @description
 * 印刷設定
 *
 * <AUTHOR> PHAM TIEN THANH
 */
import _ from 'lodash'
import {
  computed,
  definePageMeta,
  ref,
  useScreenStore,
  useSetupChildProps,
  useInitialize,
} from '#imports'
import type { Or27592OnewayType } from '~/types/cmn/business/components/Or27592Type'
import { Or27592Const } from '~/components/custom-components/organisms/Or27592/Or27592.constants'
import { Or27592Logic } from '~/components/custom-components/organisms/Or27592/Or27592.logic'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

const screenId = 'GUI01264'
const routing = 'GUI01264/pinia'
const screenName = 'GUI01264'
const screenStore = useScreenStore()
const Or27592 = ref({ uniqueCpId: '' })
const Or27592OnewayModel: Or27592OnewayType = {
  kikanFlg: '1',
  userId: '31',
  sectionName: '1',
  choIndex: '1',
  historyId: '',
  careManagerInChargeSettingsFlag: '1',
  gojuonGyoBango: '0',
  gojuonBoin: '0',
  /**
   * ケアプラン方式
   * '0:未作成、1:包括的、2:居宅GL、3:MDS-HC2.0、4:MDS2.1、5:新型養護老人ホームパッケージプラン
   */
  cpnFlg: '1',
  /**
   * 計画書書式
   * '1:旧書式、2:新書式
   */
  shosikiFlg: '1',
  /**
   * 担当者ID
   */
  tantoId: '1',
  /**
   * 事業者名
   */
  svJigyoKnj: '事業者名 1',
  /**
   * 支援経過記録様式
   * 1: 改訂前
   * 2: R3/4改訂版
   */
  shienKeikaKirokuYoshiki: '1',
}

screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI01264' },
})

const pageComponent = screenStore.screen().supplement.pageComponent
Or27592.value.uniqueCpId = pageComponent.uniqueCpId

/**
 * 自身のPinia領域をセットアップ
 * - 現在の画面のコンポーネントIDとユニークコンポーネントIDを設定
 * - 子コンポーネントの情報を登録
 */
const { childCpIds } = useInitialize({
  cpId: 'GUI01264', // 現在の画面のコンポーネントID
  uniqueCpId: pageComponent.uniqueCpId, // ユニークコンポーネントID
  childCps: [{ cpId: Or27592Const.CP_ID(0) }], // 子コンポーネントの情報
})

Or27592Logic.initialize(childCpIds.Or27592.uniqueCpId)

/**
 * 子コンポーネントのプロパティを設定
 * - 子コンポーネントのユニークIDを設定
 */
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or27592Const.CP_ID(0)]: Or27592.value,
})

const isShowDialogOr27592 = computed(() => {
  return Or27592Logic.state.get(Or27592.value.uniqueCpId)?.isOpen ?? false
})

/**
 * テーブルヘッダークリック時にOr27592ダイアログを開く
 */
function onClickOr27592() {
  Or27592Logic.state.set({
    uniqueCpId: Or27592.value.uniqueCpId,
    state: { isOpen: true },
  })
}
</script>
<template>
  <c-v-row no-gutters>
    <c-v-col> ケアマネモックアップ開発ダイヤログ画面確認用ページ </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <!-- GUI01264_［印刷設定］画面 KMD PHAM TIEN THANH 2025/06/11 ADD START-->
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr27592()"
      >
        GUI01264_印刷設定画面
      </v-btn>
    </c-v-col>
  </c-v-row>
  <g-custom-or-27592
    v-if="isShowDialogOr27592"
    v-bind="Or27592"
    :oneway-model-value="Or27592OnewayModel"
  />
  <!-- GUI01264_［印刷設定］画面 KMD PHAM TIEN THANH 2025/06/11 ADD END-->
</template>
<style lang="scss" scoped></style>
