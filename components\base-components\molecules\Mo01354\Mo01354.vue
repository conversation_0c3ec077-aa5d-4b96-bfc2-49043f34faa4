<script setup lang="ts">
/**
 * Mo01354：分子：表
 *
 * @description
 * 表の汎用コンポーネント。
 * 行の選択値と表のデータを双方向バインドモデルで管理します。
 *
 * @remarks
 * - 双方向バインドモデルのitemsプロパティには、必ず「id」のフィールドを設けてください。行選択時の処理に使用します。
 * - データ行には必ず「表用」の分子コンポーネントを使用してください。（例：表用テキストフィールド、表用セレクトフィールドなど）
 * - 単方向バインドモデルのfilterConditionsでリストのフィルタ条件を設定できます。
 */
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { VueDraggable } from 'vue-draggable-plus'
import type { Mo01354Type, Mo01354OnewayType, Mo01354Headers } from './Mo01354Type'
import type { Mo01378OnewayType, Mo01378Type } from '~/types/business/components/Mo01378Type'

/**************************************************
 * Props
 **************************************************/
/** Propsのインタフェース定義 */
interface Props {
  /** 双方向モデル */
  modelValue: Mo01354Type | undefined
  /** 単方向モデル  */
  onewayModelValue: Mo01354OnewayType
}

/** Propsの変数定義 */
const props = defineProps<Props>()
const tableRef = ref<HTMLElement>()
const headerRef = ref<HTMLElement>()
const tableWarpperRef = ref<HTMLElement>()

/** 双方向モデルのデフォルト値 */
const defaultModelValue: Mo01354Type = {
  values: {
    selectedRowId: '',
    selectedRowIds: [],
    items: [],
    scrollToId: '',
  },
}

/**
 * 選択行クラス付与関数
 *
 * @param row - 行情報
 */
const getItemClass = (row: { item: { id: string } }) => {
  // 選択行の場合「selected-row」のクラスを付与
  return mo01354.value.includes(row.item.id) ? { class: { 'selected-row': true } } : { class: {} }
}

/** 単方向モデルのデフォルト値 */
const defaultOnewayModelValue: Mo01354OnewayType = {
  density: 'compact',
  height: '193px',
  rowHeight: '32px',
  headers: [] as Mo01354Headers[],
  rowProps: getItemClass,
  sticky: true,
  itemsPerPage: -1,
  showPaginationTopFlg: true,
  showPaginationBottomFlg: true,
  showSelect: false,
  selectStrategy: 'single',
  mandatory: true,
  showDragIndicatorFlg: false,
  filterConditions: [{ field: 'deleteFlg', hiddenValue: true }],
  useDefaultHeader: true,
  scrollToAdjustment: 0,
  editabilityMixFlg: false,
}

/** 双方向モデルのローカル変数 */
const local = reactive({
  mo01354: {
    ...defaultModelValue,
    ...props.modelValue,
  },
})

/** 単方向モデルのローカル変数 */
const localOneway = reactive({
  mo01354Oneway: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
})

/**************************************************
 * Watch
 **************************************************/

/** 双方向モデルのpropsの値をローカル変数に反映 */
watch(
  () => props.modelValue,
  (newValue) => {
    local.mo01354 = {
      ...defaultModelValue,
      ...newValue,
    }
  },
  { deep: true }
)

/** 単方向モデルのpropsの値をローカル変数に反映 */
watch(
  () => props.onewayModelValue,
  (newValue) => {
    localOneway.mo01354Oneway = {
      ...defaultOnewayModelValue,
      ...newValue,
    }
  },
  { deep: true }
)

watch(
  () => local.mo01354.values.scrollToId,
  (newVal) => {
    if (!newVal) {
      return
    }

    let scrollToAdjustment = 0
    if (localOneway.mo01354Oneway?.scrollToAdjustment) {
      scrollToAdjustment = localOneway.mo01354Oneway.scrollToAdjustment
    }

    let oreScroll: number
    const index = local.mo01354.values.items.findIndex((item) => item.id === newVal)
    if (localOneway.mo01354Oneway.rowHeight) {
      if (isNaN(Number(localOneway.mo01354Oneway.rowHeight))) {
        const rowHeight = localOneway.mo01354Oneway.rowHeight as string
        oreScroll = Number(rowHeight.replace('px', '')) * (index + 1)
      } else {
        oreScroll = Number(localOneway.mo01354Oneway.rowHeight) * (index + 1)
      }
    } else {
      const rowHeight = defaultOnewayModelValue.rowHeight as string
      oreScroll = Number(rowHeight.replace('px', '')) * (index + 1)
    }
    tableWarpperRef.value!.scrollTop =
      oreScroll - headerRef.value!.clientHeight + scrollToAdjustment
    local.mo01354.values.scrollToId = ''
  }
)

/**************************************************
 * Emit
 **************************************************/
/** イベント定義 */
const emit = defineEmits(['click:row', 'update:modelValue', 'end'])

/**
 * 双方向モデルのローカル変数の値を親コンポーネントに反映
 */
watch(
  [
    () => local.mo01354.values.selectedRowId,
    () => local.mo01354.values.selectedRowIds,
    () => local.mo01354.values.scrollToId,
  ],
  () => {
    emit('update:modelValue', local.mo01354)
  }
)

/**************************************************
 * コンポーネント固有処理 - 行選択イベント
 **************************************************/
/**
 * 行選択イベント
 *
 * @param _e - クリックイベント（使用しない）
 *
 * @param row - 行情報
 */
const selectRow = (
  _e: never,
  row: { item: { id: string }; internalItem: { selectable: boolean } }
) => {
  // selectableがfalseの場合は切り替えない
  if (row.internalItem?.selectable === false) {
    return
  }

  if (mo01354.value.includes(row.item.id)) {
    // チェック済みの場合

    if (localOneway.mo01354Oneway.mandatory && mo01354.value.length === 1) {
      // 選択必須で選択中の行が1行の場合はなにもしない
      return
    }
    // チェックを外す
    mo01354.value = mo01354.value.filter((i) => i !== row.item.id)
  } else {
    // 未チェックの場合は追加
    if (localOneway.mo01354Oneway.selectStrategy === 'single') {
      mo01354.value = [row.item.id]
    } else {
      mo01354.value = [...mo01354.value, ...[row.item.id]]
    }
  }
}

/**************************************************
 * コンポーネント固有処理 - ページング処理
 **************************************************/
/**
 * ページ数取得処理
 */
const pageCount = computed(() => {
  if (
    Array.isArray(local.mo01354.values.items) &&
    localOneway.mo01354Oneway.itemsPerPage !== undefined &&
    localOneway.mo01354Oneway.itemsPerPage !== -1
  ) {
    return Math.ceil(local.mo01354.values.items.length / localOneway.mo01354Oneway.itemsPerPage)
  } else {
    return 1
  }
})

/** 表の双方向モデル */
const mo01354 = ref<string[]>([])

onMounted(async () => {
  if (localOneway.mo01354Oneway.selectStrategy === 'single') {
    mo01354.value =
      local.mo01354.values.selectedRowId !== '' ? [local.mo01354.values.selectedRowId] : []
  } else {
    mo01354.value = local.mo01354.values.selectedRowIds
  }
  await nextTick()
  const headerEl = tableRef.value!.querySelector('th')!
  headerRef.value = headerEl as HTMLElement
  const tableWarpperEl = tableRef.value!.querySelector('.v-table__wrapper')!
  tableWarpperRef.value = tableWarpperEl as HTMLElement
})

/**
 * 表の変更をローカルに反映
 */
watch(mo01354, () => {
  if (localOneway.mo01354Oneway.selectStrategy === 'single') {
    local.mo01354.values.selectedRowId = mo01354.value.length !== 0 ? mo01354.value[0] : ''
  } else {
    local.mo01354.values.selectedRowIds = mo01354.value
  }
})

/**
 * ローカルの変更を表に反映
 */
watch([() => local.mo01354.values.selectedRowId, () => local.mo01354.values.selectedRowIds], () => {
  if (localOneway.mo01354Oneway.selectStrategy === 'single') {
    mo01354.value =
      local.mo01354.values.selectedRowId !== '' ? [local.mo01354.values.selectedRowId] : []
  } else {
    mo01354.value = local.mo01354.values.selectedRowIds ?? []
  }
})

/** ページネーションの双方向モデル */
const mo01378 = ref<Mo01378Type>({
  page: 1,
})

/** ページネーションの単方向モデル */
const mo01378Oneway = ref<Mo01378OnewayType>({
  pageCount: pageCount.value,
})

/** ページ数の変更をページネーションに反映 */
watch(
  () => pageCount.value,
  () => {
    mo01378Oneway.value.pageCount = pageCount.value
  }
)

/** 表の先頭データだけを選択状態とする */
function selectOne() {
  if (Array.isArray(local.mo01354.values.items) && local.mo01354.values.items.length !== 0) {
    mo01354.value = [local.mo01354.values.items[0].id]
  }
}

/**************************************************
 * コンポーネント固有処理 - 表データのフィルタ処理
 **************************************************/
/**
 * 表データのフィルタ処理
 *
 * @description
 * 単方向バインドモデルのfilterConditionsの条件で双方向バインドモデルのitemsをフィルタした値を返す
 */
const filteredItems = computed(() => {
  return local.mo01354.values.items.filter((item) => {
    return localOneway.mo01354Oneway.filterConditions?.every(
      (condition) => !item[condition.field] || item[condition.field] === !condition.hiddenValue
    )
  })
})

/**************************************************
 * コンポーネント固有処理 - スタイル定義
 **************************************************/

/** 行の高さのスタイル */
const rowHeightStyle = computed(() => {
  return `${parseInt((localOneway.mo01354Oneway.rowHeight ?? defaultOnewayModelValue.rowHeight!).toString())}px`
})
</script>
<template>
  <!-- 表上部 -->
  <c-v-row
    v-if="
      (localOneway.mo01354Oneway.showPaginationTopFlg &&
        localOneway.mo01354Oneway.itemsPerPage !== -1) ||
      $slots['tableTopLeft'] ||
      $slots['tableTopRight']
    "
    no-gutters
    class="ma-2"
  >
    <c-v-col>
      <slot name="tableTopLeft" />
    </c-v-col>
    <c-v-col
      v-if="
        localOneway.mo01354Oneway.showPaginationTopFlg &&
        localOneway.mo01354Oneway.itemsPerPage !== -1
      "
      cols="auto"
    >
      <!-- Mo01378：分子：表ページネーション -->
      <base-mo01378
        v-model="mo01378"
        :oneway-model-value="mo01378Oneway"
      />
    </c-v-col>
    <c-v-col cols="auto">
      <slot name="tableTopRight" />
    </c-v-col>
  </c-v-row>
  <VueDraggable
    v-model="local.mo01354.values.items"
    target="tbody"
    :animation="150"
    handle=".drag-indicator-col"
    v-bind="{ ...$attrs }"
    @end="$emit('end', $event)"
  >
    <div ref="tableRef">
      <!-- VDataTable -->
      <c-v-data-table
        v-model="mo01354"
        v-bind="{ ...$attrs, ...localOneway.mo01354Oneway }"
        v-model:page="mo01378.page"
        v-resizable-grid="localOneway.mo01354Oneway.columnMinWidth"
        item-selectable="selectable"
        :items="filteredItems"
        :show-select="
          localOneway.mo01354Oneway.showDragIndicatorFlg || localOneway.mo01354Oneway.showSelect
        "
        :class="{ 'editability-mix-table': localOneway.mo01354Oneway.editabilityMixFlg }"
        :fixed-header="localOneway.mo01354Oneway.sticky"
        item-value="id"
        @click:row="selectRow"
      >
        <template
          v-for="(slot, slotName) in $slots"
          #[slotName]="data"
        >
          <slot
            :name="slotName"
            v-bind="{ ...data }"
          />
        </template>
        <!-- カスタムヘッダー -->
        <!-- v-data-tabelのデフォルトのヘッダーと同じスタイルが適用されるようにHTMLで実装している -->
        <template
          v-if="localOneway.mo01354Oneway.useDefaultHeader"
          #[`headers`]="{
            columns,
            allSelected,
            selectAll,
            someSelected,
            getSortIcon,
            isSorted,
            toggleSort,
          }"
        >
          <tr>
            <template
              v-for="column in columns"
              :key="column.key"
            >
              <!-- チェックボックス列のヘッダー -->
              <th
                v-if="column.key === 'data-table-select'"
                class="v-data-table-column--no-padding"
              >
                <c-v-row
                  no-gutters
                  align="center"
                  class="flex-nowrap justify-space-between"
                >
                  <c-v-col v-if="localOneway.mo01354Oneway.showDragIndicatorFlg"></c-v-col>
                  <!-- ヘッダーの選択チェックボックス -->
                  <c-v-col v-if="localOneway.mo01354Oneway.showSelect">
                    <base-at-checkbox
                      v-if="localOneway.mo01354Oneway.selectStrategy !== 'single'"
                      :indeterminate="someSelected && !allSelected"
                      :model-value="allSelected"
                      checkbox-label=""
                      @update:model-value="
                        !localOneway.mo01354Oneway.mandatory || !allSelected
                          ? selectAll(!allSelected)
                          : selectOne()
                      "
                    />
                  </c-v-col>
                </c-v-row>
              </th>
              <!-- チェックボックス以外の列のヘッダー。「ヘッダーをテーブルの上部に固定するフラグ：true」の場合、ヘッダー固定用のスタイルを適用 -->
              <th
                v-else
                :tabindex="column.sortable ? 0 : undefined"
                :class="[{ 'cursor-pointer': column.sortable }]"
                @click="column.sortable && toggleSort(column)"
              >
                <div class="align-center d-flex">
                  <!-- カスタムヘッダー：必須マーク -->
                  <span
                    v-if="column.required"
                    class="mr-1 required-asterisk"
                    >*</span
                  >
                  <span class="d-flex">
                    <!-- カスタムヘッダー：列タイトル -->
                    <span :class="['align-self-center', { 'me-2': !column.sortable }]">{{
                      column.title
                    }}</span>
                    <!-- カスタムヘッダー：ソートアイコン -->
                    <base-at-icon
                      v-if="column.sortable"
                      :icon="getSortIcon(column)"
                      :class="[
                        'sort-icon',
                        'align-self-center',
                        'me-2',
                        { 'sort-icon-sorted': isSorted(column) },
                      ]"
                    />
                  </span>
                </div>
              </th>
            </template>
          </tr>
        </template>
        <!-- ヘッダーの選択チェックボックスを置き換え -->
        <template #[`header.data-table-select`]="{ allSelected, selectAll, someSelected }">
          <c-v-row
            no-gutters
            align="center"
            class="flex-nowrap justify-space-between"
          >
            <c-v-col v-if="localOneway.mo01354Oneway.showDragIndicatorFlg"></c-v-col>
            <!-- ヘッダーの選択チェックボックス -->
            <c-v-col v-if="localOneway.mo01354Oneway.showSelect">
              <base-at-checkbox
                v-if="localOneway.mo01354Oneway.selectStrategy !== 'single'"
                :indeterminate="someSelected && !allSelected"
                :model-value="allSelected"
                checkbox-label=""
                @update:model-value="
                  !localOneway.mo01354Oneway.mandatory || !allSelected
                    ? selectAll(!allSelected)
                    : selectOne()
                "
              />
            </c-v-col>
          </c-v-row>
        </template>
        <!-- データ行の選択チェックボックスを置き換え -->
        <template #[`item.data-table-select`]="{ internalItem, isSelected }">
          <c-v-row
            no-gutters
            align="center"
            class="flex-nowrap justify-space-between row-height"
          >
            <!-- つまみアイコン -->
            <c-v-col
              v-if="localOneway.mo01354Oneway.showDragIndicatorFlg"
              class="drag-indicator-col cursor-move"
            >
              <base-at-icon
                icon="drag_indicator"
                color="black-400"
                size="20px"
              />
            </c-v-col>
            <!-- 行の選択チェックボックス -->
            <c-v-col
              v-if="localOneway.mo01354Oneway.showSelect"
              class="select-checkbox-col"
            >
              <base-at-checkbox
                v-if="localOneway.mo01354Oneway.selectStrategy !== 'single'"
                :model-value="isSelected(internalItem)"
                checkbox-label=""
                readonly
                :disabled="internalItem.selectable === false ? true : false"
              />
              <base-at-radio
                v-else
                name="radio-01354"
                :model-value="isSelected(internalItem)"
                radio-label=""
                readonly
                :disabled="internalItem.selectable === false ? true : false"
              />
            </c-v-col>
          </c-v-row>
        </template>
      </c-v-data-table>
    </div>
  </VueDraggable>
  <!-- 表下部 -->
  <c-v-row
    v-if="
      (localOneway.mo01354Oneway.showPaginationTopFlg &&
        localOneway.mo01354Oneway.itemsPerPage !== -1) ||
      $slots['tableBottomLeft'] ||
      $slots['tableBottomRight']
    "
    no-gutters
    class="ma-2"
  >
    <c-v-col>
      <slot name="tableBottomLeft" />
    </c-v-col>
    <c-v-col
      v-if="
        localOneway.mo01354Oneway.showPaginationBottomFlg &&
        localOneway.mo01354Oneway.itemsPerPage !== -1
      "
      cols="auto"
    >
      <!-- Mo01378：分子：表ページネーション -->
      <base-mo01378
        v-model="mo01378"
        :oneway-model-value="mo01378Oneway"
      />
    </c-v-col>
    <c-v-col cols="auto">
      <slot name="tableBottomRight" />
    </c-v-col>
  </c-v-row>
</template>
<style scoped lang="scss">
@use '@/styles/base-data-table.scss';

// 行の高さ
:deep(.v-table--density-compact table tbody tr td),
:deep(.v-table--density-compact .row-height) {
  height: v-bind('rowHeightStyle') !important;
}

.sort-icon {
  opacity: 0;
}

.sort-icon-sorted {
  opacity: 1 !important;
}
thead th:hover .sort-icon,
thead th:focus .sort-icon {
  opacity: 0.5;
}
</style>
