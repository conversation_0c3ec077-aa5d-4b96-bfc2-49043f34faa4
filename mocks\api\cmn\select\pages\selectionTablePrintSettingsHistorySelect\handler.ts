import { HttpResponse } from 'msw'
import defaultData from './data/default.json'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { ISelectionTablePrintSettingsHistorySelectInEntity } from '~/repositories/cmn/entities/SelectionTablePrintSettingsEntity.ts'
/**
 * GUI00844_印刷設定
 *
 * @description
 * GUI00844_印刷設定履歴リストデータを返却する。
 * dataName："selectionTablePrintSettingsHistorySelect"
 */
export function handler(inEntity: ISelectionTablePrintSettingsHistorySelectInEntity) {
  const responceJson: BaseResponseBody = {
    statusCode: 'success',
    data: {
      ...defaultData,
    },
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
