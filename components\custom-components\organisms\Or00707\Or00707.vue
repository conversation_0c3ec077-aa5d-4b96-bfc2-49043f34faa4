<script setup lang="ts">
/**
 * Or00707:有機体:長期目標取込
 * GUI01008_長期目標取込画面
 *
 * @description
 * 長期目標取込
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { reactive, ref, onMounted, watch } from 'vue'
import { Or00707Const } from './Or00707.constants'
import type {
  DataInfoDetailsType,
  HistroyInfoType,
  LongTermGoalsType,
  Or00707StateType,
  PlanPeriodInfoType,
} from './Or00707.type'
import type {
  Or00707Type,
  Or00707OnewayType,
  LongTermGoalsInfoType,
} from '~/types/cmn/business/components/Or00707Type'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type {
  ILongTermGoalImportInEntity,
  ILongTermGoalImportOutEntity,
} from '~/repositories/cmn/entities/LongTermGoalImportEntity'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { useScreenOneWayBind, useSetupChildProps } from '#imports'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import type {
  Mo01334Headers,
  Mo01334Items,
  Mo01334OnewayType,
  Mo01334Type,
} from '~/types/business/components/Mo01334Type'
import { ResBodyStatusCode } from '~/constants/api-constants'

const { t } = useI18n()
/************************************************
 * Props
 ************************************************/
interface Props {
  modelValue: Or00707Type
  onewayModelValue: Or00707OnewayType
  uniqueCpId: string
}
const props = defineProps<Props>()

const defaultOnewayModelValue: Or00707OnewayType = {
  longTermGoalsInfo: {
    // 事業者ID
    svJigyoId: '',
    // 施設ID
    shisetuId: '',
    // 利用者ID
    userId: '',
    // 種別ID
    syubetsuId: '',
    // 計画期間ID
    sc1Id: '',
    // 期間管理フラグ
    kikanFlg: '',
  } as LongTermGoalsInfoType,
}

const localOneway = reactive({
  or00707Oneway: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  // 「長期目標取込」ダイアログ
  mo00024Oneway: {
    width: '870px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      toolbarTitle: t('label.long-term-goal-import'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
    },
  },
  // 確認ボタン
  mo00609Oneway: {
    btnLabel: t('btn.ok'),
  } as Mo00609OnewayType,

  // 計画期間一覧
  mo01334Oneway_1: {
    headers: [
      // 計画期間
      {
        title: t('label.plan-period'),
        key: 'planPeriod',
        sortable: false,
        width: '210px',
      },
      // 期間内履歴数
      {
        title: t('label.within-the-period-number-of-history'),
        key: 'numberOfHistory',
        sortable: false,
        width: '120px',
      },
    ] as unknown[] as Mo01334Headers[],
    items: [] as Mo01334Items[],
    height: '168',
    rowHeight: '32',
  } as Mo01334OnewayType,
  // 履歴一覧
  mo01334Oneway_2: {
    headers: [
      // 作成日
      {
        title: t('label.create-date'),
        key: 'createYmd',
        sortable: false,
        width: '120px',
      },
      // 作成者
      {
        title: t('label.author'),
        key: 'author',
        sortable: false,
        width: '180px',
      },
      // 有効期間
      {
        title: t('label.validity-period'),
        key: 'validity',
        sortable: false,
        width: '',
      },
    ] as unknown[] as Mo01334Headers[],
    items: [] as Mo01334Items[],
    height: '168',
    rowHeight: '32',
  } as Mo01334OnewayType,
  // 長期目標情報一覧
  mo01334Oneway_3: {
    headers: [
      {
        title: t('label.long-term-goal'),
        key: 'choukiKnj',
        sortable: false,
      },
    ] as Mo01334Headers[],
    items: [] as Mo01334Items[],
    height: '300px',
    rowHeight: '86px',
    showSelect: true,
    selectStrategy: 'all',
    mandatory: false,
  } as Mo01334OnewayType,
})

const defaultModelValue = {
  or00707: {
    longTermGoalsInfo: '',
  } as Or00707Type,
  // 履歴情報BAK
  histroyInfoBak: {
    items: [
      {
        sc1Id: '',
        createYmd: '',
        shokuKnj: '',
        kikan: '',
        ks21Id: '',
      },
    ],
  },
  // 長期目標取込情報BAK
  longTermGoalsInfoBak: {
    items: [
      {
        seq: '',
        ks21Id: '',
        choukiKnj: '',
      },
    ],
  },
}

const local = reactive({
  or00707: {
    ...defaultModelValue.or00707,
    ...props.modelValue,
  } as Or00707Type,
  histroyInfoBak: {
    ...defaultModelValue.histroyInfoBak,
  },
  longTermGoalsInfoBak: {
    ...defaultModelValue.longTermGoalsInfoBak,
  },
  // 計画期間一覧
  mo01334_1: {
    value: '',
    values: [] as string[],
  } as Mo01334Type,
  // 履歴一覧
  mo01334_2: {
    value: '',
    values: [] as string[],
  } as Mo01334Type,
  // 長期目標一覧
  mo01334_3: {
    value: '',
    values: [] as string[],
  } as Mo01334Type,
})

/**************************************************
 * 変数定義
 **************************************************/
const mo00611Oneway1 = ref<Mo00611OnewayType>({
  btnLabel: t('btn.close'),
  width: '90px',
})

const mo00611Oneway2 = ref<Mo00611OnewayType>({
  btnLabel: t('btn.overwrite'),
  width: '90px',
})

const mo00611Oneway3 = ref<Mo00611OnewayType>({
  btnLabel: t('btn.add'),
  width: '90px',
})

const mo00024 = ref<Mo00024Type>({
  isOpen: Or00707Const.DEFAULT.IS_OPEN,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  // 警告ダイアログを初期化
  initContorls()

  // 初期情報取得
  await getInitDataInfo()
})

/**************************************************
 * 関数
 **************************************************/
/**
 *  コントロール初期化
 */
const initContorls = () => {
  // 警告ダイアログを初期化
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.no'),
    },
  })

  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      isOpen: false,
      dialogTitle: t('label.caution'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })
}

/** 初期情報取得 */
async function getInitDataInfo() {
  // バックエンドAPIから初期情報取得
  const inputData: ILongTermGoalImportInEntity = {
    // 事業者ID
    svJigyoId: localOneway.or00707Oneway.longTermGoalsInfo.svJigyoId,
    // 施設ID
    shisetuId: localOneway.or00707Oneway.longTermGoalsInfo.shisetuId,
    // 利用者ID
    userId: localOneway.or00707Oneway.longTermGoalsInfo.userId,
    // 種別ID
    syubetsuId: localOneway.or00707Oneway.longTermGoalsInfo.syubetsuId,
    // 期間管理フラグ
    kikanFlg: localOneway.or00707Oneway.longTermGoalsInfo.kikanFlg,
  }

  // API呼び出し
  const resData: ILongTermGoalImportOutEntity = await ScreenRepository.select(
    'longTermGoalImportSelect',
    inputData
  )
  if (resData.statusCode === ResBodyStatusCode.SUCCESS) {
    // データ情報設定
    planPeriodInfoSet(resData.data)
  }
}

/**
 * 計画期間情報リスト設定
 *
 * @param dataInfo - 計画期間選択行ID
 */
const planPeriodInfoSet = (dataInfo: DataInfoDetailsType) => {
  // 計画期間、履歴情報と長期目標情報の初期化
  localOneway.mo01334Oneway_1.items = []
  localOneway.mo01334Oneway_2.items = []
  localOneway.mo01334Oneway_3.items = []
  local.mo01334_3.values = []

  let planPeriodInfoList: PlanPeriodInfoType[] = []
  let selectPeriodId = ''
  if (dataInfo) {
    // 計画期間情報取得
    planPeriodInfoList = dataInfo.planPeriodList.map((el) => {
      return { ...el, id: el.sc1Id }
    })

    if (localOneway.or00707Oneway.longTermGoalsInfo.sc1Id) {
      // 初期情報の計画期間IDが存在する場合、
      selectPeriodId = localOneway.or00707Oneway.longTermGoalsInfo.sc1Id
    } else {
      // 上記以外の場合、
      if (planPeriodInfoList && planPeriodInfoList.length > 0) {
        selectPeriodId = planPeriodInfoList[0].sc1Id
      }
    }

    // 計画期間の選択行
    local.mo01334_1.value = selectPeriodId

    // 履歴情報BAK---設定
    local.histroyInfoBak.items = dataInfo.historyList
    // 長期目標情報BAK---設定
    local.longTermGoalsInfoBak.items = dataInfo.longTermGoalList

    // 履歴情報設定
    histroyInfoSet(selectPeriodId)
  }

  // 計画期間一覧Oneway設定
  localOneway.mo01334Oneway_1.items = planPeriodInfoList.map((item: PlanPeriodInfoType) => ({
    id: item.sc1Id,
    planPeriod: {
      value: item.planPeriod,
      unit: '',
    },
    numberOfHistory: {
      value: parseInt(item.numberOfHistory, 10),
      unit: '',
    },
  }))
}

/**
 * 履歴情報リスト設定
 *
 * @param selectId - 履歴情報選択行ID
 */
const histroyInfoSet = (selectId: string) => {
  // 履歴情報と長期目標情報の初期化
  localOneway.mo01334Oneway_2.items = []
  localOneway.mo01334Oneway_3.items = []
  local.mo01334_3.values = []

  let histroyInfoList = [] as HistroyInfoType[]
  let selectHistroyId = ''
  if (local.histroyInfoBak.items) {
    // 履歴情報取得
    histroyInfoList = local.histroyInfoBak?.items?.filter(
      (item: { sc1Id: unknown }) => item.sc1Id === selectId
    )

    if (histroyInfoList && histroyInfoList.length > 0) {
      selectHistroyId = histroyInfoList[0].ks21Id
    }

    if (selectHistroyId) {
      // 履歴情報選択明細があり場合、
      local.mo01334_2.value = selectHistroyId // 計画期間の選択行
      // 長期目標情報設定
      longTermGoalsInfoSet(selectHistroyId)
    }
  }

  // 履歴情報設定
  localOneway.mo01334Oneway_2.items = histroyInfoList.map((item: HistroyInfoType) => ({
    id: item.ks21Id,
    createYmd: {
      value: item.createYmd,
      unit: '',
    },
    author: {
      value: item.shokuKnj,
      unit: '',
    },
    validity: {
      value: item.kikan,
      unit: '',
    },
  }))
}

/**
 * 長期目標情報リスト設定
 *
 * @param selectId - 履歴情報選択行ID
 */
const longTermGoalsInfoSet = (selectId: string) => {
  localOneway.mo01334Oneway_3.items = []
  local.mo01334_3.values = []

  let longTermGoalsInfoList = [] as LongTermGoalsType[]
  if (local.longTermGoalsInfoBak.items) {
    // 長期目標情報取得
    longTermGoalsInfoList = local.longTermGoalsInfoBak?.items?.filter(
      (item: { ks21Id: unknown }) => item.ks21Id === selectId
    )
  }

  // 長期目標情報の設定
  localOneway.mo01334Oneway_3.items = longTermGoalsInfoList.map((item: LongTermGoalsType) => ({
    id: item.seq,
    choukiKnj: item.choukiKnj,
  }))
}

/**
 * 閉じるボタン押下時
 */
function onClickCloseBtn(): void {
  local.mo01334_3.values = []
  setState({ isOpen: false })
}

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

const infoMsgFlag = ref('')

// ダイアログCpId設定
const or21814 = ref({ uniqueCpId: '' }) // 確認ダイアログ
const or21815 = ref({ uniqueCpId: '' }) // 警告ダイアログ
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(1)]: or21814.value, // 確認ダイアログ
  [Or21815Const.CP_ID(1)]: or21815.value, // 警告ダイアログ
})

/**
 * 上書ボタン押下時
 */
async function onOverwriteBtn() {
  if (local.mo01334_3.values && local.mo01334_3.values.length > 0) {
    // 選択データが存在する場合、
    infoMsgFlag.value = '1'

    if (local.or00707.longTermGoalsInfo) {
      // 親画面.方針内容に値が存在する場合、上書確認ダイアログ
      // 長期目標一覧を選択していない場合、未選択情報確認ダイアログ
      // 確認ダイアログを開く
      const dialogResult = await openConfirmDialog(
        t('message.i-cmn-10193', [t('label.comprehensive-assistance-policy')])
      )
      if (dialogResult === 'yes') {
        // 上書きの確認ダイアログ表示する場合
        // 戻り値設定
        rtnDataSet('1')

        // 処理終了
        return
      }
    } else {
      // 戻り値設定
      rtnDataSet('1')
    }
  } else {
    // 選択データが存在しない場合、
    infoMsgFlag.value = '0'

    // 長期目標一覧を選択していない場合、未選択情報確認ダイアログ
    Or21815Logic.state.set({
      uniqueCpId: or21815.value.uniqueCpId,
      state: {
        isOpen: true,
        dialogText: t('message.w-cmn-20791'),
      },
    })
  }
}

// 戻り値設定 btnFlag: 1:上書き 2:追加
function rtnDataSet(btnFlag: string) {
  // 戻り値設定
  const rtnData: Or00707Type = { longTermGoalsInfo: '' }

  const longTermGoalsList: string[] = []

  if (btnFlag === '2' && local.or00707.longTermGoalsInfo) {
    // 追加処理の場合、親画面.方針内容を追加する
    longTermGoalsList.push(local.or00707.longTermGoalsInfo + '\r\n')
  }

  // 明細情報を設定する。
  local.mo01334_3.values.forEach((item, index) => {
    const endString = index === local.mo01334_3.values.length - 1 ? '' : '\r\n'
    const itemInfo: string =
      localOneway.mo01334Oneway_3.items.find((data) => data.id === item)?.choukiKnj + endString
    longTermGoalsList.push(itemInfo)
  })

  rtnData.longTermGoalsInfo = longTermGoalsList.length > 0 ? longTermGoalsList.join('') : ''
  // 選択情報値戻り
  emit('update:modelValue', rtnData)

  // 画面閉じる
  onClickCloseBtn()
}

/**
 * 追加ボタン押下時
 */
function onAppendBtn(): void {
  if (local.mo01334_3.values && local.mo01334_3.values.length > 0) {
    // 選択データが存在する場合、
    // 戻り値設定
    rtnDataSet('2')
  } else {
    // 選択データが存在しない場合、
    infoMsgFlag.value = '0'

    // 未選択確認ダイアログ表示
    Or21815Logic.state.set({
      uniqueCpId: or21815.value.uniqueCpId,
      state: {
        isOpen: true,
        dialogText: t('message.w-cmn-20791'),
      },
    })
  }
}

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      onClickCloseBtn()
    }
  }
)

// 計画期間一覧選択の変更を検知、行選択イベントを通知
watch(
  () => local.mo01334_1.value,
  (newValue) => {
    histroyInfoSet(newValue)
  }
)

// 履歴一覧選択の変更を検知、行選択イベントを通知
watch(
  () => local.mo01334_2.value,
  (newValue) => {
    longTermGoalsInfoSet(newValue)
  }
)

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no
 */
async function openConfirmDialog(paramDialogText: string): Promise<'yes' | 'no'> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = 'no' as 'yes' | 'no'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or00707StateType>({
  cpId: Or00707Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or00707Const.DEFAULT.IS_OPEN
    },
  },
})
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet>
        <c-v-row no-gutters>
          <c-v-col
            v-if="
              localOneway.or00707Oneway.longTermGoalsInfo.kikanFlg ==
              Or00707Const.DEFAULT.PERIOD_MANAGE_FLAG_1
            "
            cols="5"
            class="table-header pr-2"
          >
            <!-- 計画期間選択一覧 -->
            <base-mo-01334
              v-model="local.mo01334_1"
              :oneway-model-value="localOneway.mo01334Oneway_1"
              hide-default-footer
              class="list-wrapper"
            >
              <template #[`item.planPeriod`]="{ item }">
                <base-mo01337 :oneway-model-value="item.planPeriod" />
              </template>
              <template #[`item.numberOfHistory`]="{ item }">
                <base-mo01336 :oneway-model-value="item.numberOfHistory" />
              </template>
            </base-mo-01334>
          </c-v-col>

          <c-v-col
            cols="7"
            class="table-header"
          >
            <!-- 履歴一覧 -->
            <base-mo-01334
              v-model="local.mo01334_2"
              :oneway-model-value="localOneway.mo01334Oneway_2"
              hide-default-footer
              class="list-wrapper"
              :style="{
                width:
                  localOneway.or00707Oneway.longTermGoalsInfo.kikanFlg !==
                  Or00707Const.DEFAULT.PERIOD_MANAGE_FLAG_1
                    ? '480px'
                    : '',
              }"
            >
              <template #[`item.createYmd`]="{ item }">
                <base-mo01337 :oneway-model-value="item.createYmd" />
              </template>
              <template #[`item.author`]="{ item }">
                <base-mo01337 :oneway-model-value="item.author" />
              </template>
              <template #[`item.validity`]="{ item }">
                <base-mo01337 :oneway-model-value="item.validity" />
              </template>
            </base-mo-01334>
          </c-v-col>
        </c-v-row>
        <c-v-row
          no-gutters
          class="mt-2"
        >
          <c-v-col
            cols="12"
            class="flex-0-0 tableList-wrapper table-header"
          >
            <!-- 長期目標情報をテーブル表示 -->
            <base-mo-01334
              v-model="local.mo01334_3"
              :oneway-model-value="localOneway.mo01334Oneway_3"
              hide-default-footer
              class="list-wrapper"
            >
              <template #[`item.choukiKnj`]="{ item }">
                <div class="overflowTd">
                  <span>{{ item.choukiKnj }}</span>
                  <c-v-tooltip
                    activator="parent"
                    location="bottom"
                    :max-width="600"
                    :text="item.choukiKnj"
                    open-delay="200"
                  />
                </div>
              </template>
            </base-mo-01334>
          </c-v-col>
        </c-v-row>
      </c-v-sheet>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <!-- 閉じるボタン -->
      <base-mo00611
        :oneway-model-value="mo00611Oneway1"
        @click="onClickCloseBtn"
      >
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :max-width="300"
          :text="t('tooltip.screen-close')"
          open-delay="200"
        />
      </base-mo00611>

      <!-- 上書 -->
      <base-mo00609
        :oneway-model-value="mo00611Oneway2"
        class="ml-2"
        @click="onOverwriteBtn"
      >
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :max-width="300"
          :text="t('tooltip.overwrite')"
          open-delay="200"
        />
      </base-mo00609>

      <!-- 追加ボタン -->
      <base-mo00609
        :oneway-model-value="mo00611Oneway3"
        class="ml-2"
        @click="onAppendBtn"
      >
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :max-width="300"
          :text="t('tooltip.add')"
          open-delay="200"
        />
      </base-mo00609>
    </template>
  </base-mo00024>

  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814 v-bind="or21814" />
  <!-- Or21815:有機体:警告ダイアログ -->
  <g-base-or21815 v-bind="or21815" />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';

.overflowTd {
  width: 100%;
  height: 100%;
  height: 85px;
  box-sizing: border-box;

  span {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    line-clamp: 4;
    -webkit-line-clamp: 4;
  }
}
</style>
