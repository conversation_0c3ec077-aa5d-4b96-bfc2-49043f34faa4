import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * OrX0097:有機体:課題立案ダイアログ
 *
 * @description
 * 静的データ
 *
 * <AUTHOR>
 */
export namespace OrX0097Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('OrX0097', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false
    /**
     * TAB_1
     */
    export const TAB_1 = '1'
    /**
     * TAB_2
     */
    export const TAB_2 = '2'
    /**
     * TAB_3
     */
    export const TAB_3 = '3'
    /**
     * TAB_4
     */
    export const TAB_4 = '4'
    /**
     * TAB_5
     */
    export const TAB_5 = '5'
}
}
