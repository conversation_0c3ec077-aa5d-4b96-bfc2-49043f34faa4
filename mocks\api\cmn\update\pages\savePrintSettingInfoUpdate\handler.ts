/**
 * Or52041:有機体:［印刷設定］画面
 * GUI00623_［印刷設定］画面
 *
 * <AUTHOR>
 */
import { HttpResponse } from 'msw'
import type { SavePrintSettingInfoUpdateInEntity } from '~/repositories/cmn/entities/SavePrintSettingInfoUpdateEntity'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'


/**
 * GUI00623_［印刷設定］画面パターンタイトルのSAAPIモック
 *
 * @description
 * GUI00623_［印刷設定］画面パターンタイトルのメイン画面に表示されるデータを返却する。
 * dataName："savePrintSettingInfoUpdate"
 */
export function handler(requestParam: SavePrintSettingInfoUpdateInEntity) {
  const responceJson: BaseResponseBody = {
    statusCode: 'success',
    data: {},
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
