import { Or52881Const } from './Or52881.constants'
import type { Or52881StateType } from './Or52881.type'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'
import { OrX0117Const } from '~/components/custom-components/organisms/OrX0117/OrX0117.constants'
import { OrX0117Logic } from '~/components/custom-components/organisms/OrX0117/OrX0117.logic'
import { OrX0128Const } from '~/components/custom-components/organisms/OrX0128/OrX0128.constants'
import { OrX0128Logic } from '~/components/custom-components/organisms/OrX0128/OrX0128.logic'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import { OrX0130Logic } from '~/components/custom-components/organisms/OrX0130/OrX0130.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { OrX0145Logic } from '~/components/custom-components/organisms/OrX0145/OrX0145.logic'
import { OrX0145Const } from '~/components/custom-components/organisms/OrX0145/OrX0145.constants'

/**
 * Or52881:有機体:モーダル（特殊コンポーネント）
 * 処理ロジック
 *
 * @description
 * initialize処理とpinia領域操作用の処理を提供する
 *
 * <AUTHOR>
 */
export namespace Or52881Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or52881Const.CP_ID(0),
      uniqueCpId,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [
        { cpId: OrX0117Const.CP_ID(0) },
        { cpId: OrX0128Const.CP_ID(0) },
        { cpId: OrX0130Const.CP_ID(0) },
        { cpId: Or21814Const.CP_ID(0) },
        { cpId: Or21813Const.CP_ID(0) },
        { cpId: OrX0145Const.CP_ID(0) },
      ],
    })

    // 子コンポーネントのセットアップ
    OrX0117Logic.initialize(childCpIds[OrX0117Const.CP_ID(0)].uniqueCpId)
    OrX0128Logic.initialize(childCpIds[OrX0128Const.CP_ID(0)].uniqueCpId)
    OrX0130Logic.initialize(childCpIds[OrX0130Const.CP_ID(0)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(0)].uniqueCpId)
    Or21813Logic.initialize(childCpIds[Or21813Const.CP_ID(0)].uniqueCpId)
    OrX0145Logic.initialize(childCpIds[OrX0145Const.CP_ID(0)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }

  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or52881StateType>(Or52881Const.CP_ID(0))
}
