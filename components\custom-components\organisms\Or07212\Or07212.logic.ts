import type { Or07212StateType } from './Or07212.type'
import { Or07212Const } from './Or07212.constants'
import { useOneWayBindAccessor } from '#imports'

/**
 * Or07212:処理ロジック
 * GUI04473_日割利用期間
 *
 * @description
 * 処理ロジック
 *
 * <AUTHOR> 董永強
 */
export namespace Or07212Logic {
  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or07212StateType>(Or07212Const.CP_ID(0))
}
