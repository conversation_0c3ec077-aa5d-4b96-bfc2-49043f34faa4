import { OrX0133Const } from '../OrX0133/OrX0133.constants'
import { OrX0133Logic } from '../OrX0133/OrX0133.logic'
import { Or52057Const } from './Or52057.constants'
import type { Or52057StateType } from './Or52057.type'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { OrX0117Const } from '~/components/custom-components/organisms/OrX0117/OrX0117.constants'
import { OrX0117Logic } from '~/components/custom-components/organisms/OrX0117/OrX0117.logic'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import { OrX0130Logic } from '~/components/custom-components/organisms/OrX0130/OrX0130.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { OrX0145Logic } from '~/components/custom-components/organisms/OrX0145/OrX0145.logic'
import { OrX0145Const } from '~/components/custom-components/organisms/OrX0145/OrX0145.constants'

/**
 * Or52057:有機体:モーダル（特殊コンポーネント）
 * 処理ロジック
 *
 * @description
 * initialize処理とpinia領域操作用の処理を提供する
 *
 * <AUTHOR>
 */
export namespace Or52057Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or52057Const.CP_ID(0),
      uniqueCpId,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [
        { cpId: Or21815Const.CP_ID(0) },
        { cpId: OrX0117Const.CP_ID(0) },
        { cpId: OrX0130Const.CP_ID(0) },
        { cpId: OrX0133Const.CP_ID(0) },
        { cpId: Or21814Const.CP_ID(0) },
        { cpId: Or21813Const.CP_ID(0) },
        { cpId: OrX0145Const.CP_ID(0) },
      ],
    })

    // 子コンポーネントのセットアップ
    Or21815Logic.initialize(childCpIds[Or21815Const.CP_ID(0)].uniqueCpId)
    OrX0117Logic.initialize(childCpIds[OrX0117Const.CP_ID(0)].uniqueCpId)
    OrX0130Logic.initialize(childCpIds[OrX0130Const.CP_ID(0)].uniqueCpId)
    OrX0133Logic.initialize(childCpIds[OrX0133Const.CP_ID(0)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(0)].uniqueCpId)
    Or21813Logic.initialize(childCpIds[Or21813Const.CP_ID(0)].uniqueCpId)
    OrX0145Logic.initialize(childCpIds[OrX0145Const.CP_ID(0)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }

  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or52057StateType>(Or52057Const.CP_ID(0))
}
