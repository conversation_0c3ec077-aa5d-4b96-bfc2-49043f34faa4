import { Or10279Const } from './Or10279.constants'
import type { Or10279StateType } from './Or10279.type'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'
/**
 * Or10279:有機体:アセスメント（インターライ）CSV出力
 * 処理ロジック
 *
 * @description
 * initialize処理とpinia領域操作用の処理を提供する
 */
export namespace Or10279Logic {
  /**
   * initialize
   *
   * @description
   * コンポーネントの初期化処理。
   * 共通関数を呼びpiniaの領域を初期化する。
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or10279Const.CP_ID(0),
      uniqueCpId,
      childCps: [],
      editFlgNecessity: false,
    })
    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }
  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or10279StateType>(Or10279Const.CP_ID(0))
}
