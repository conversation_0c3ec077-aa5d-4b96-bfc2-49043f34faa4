import type { Or51734Param } from './Or51734.type'

/**
 * Or51734:有機体:(文字列入力支援) 実施モニタリング記号マスタ
 * GUI01250_実施モニタリング記号マスタ
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR>
 */
export interface Or51734StateType {
  /**
   * 開閉フラグ
   */
  isOpen?: boolean
}

/**
 * パラメータエンティティ
 */
export interface Or51734Param {
  /**
   * 実行フラグ(save：保存,getData：データ再取得,''：何もしません)
   */
  executeFlag: 'save' | 'getData' | ''
  /**
   * 区分フラグ
   */
  kbnFlg: string

  /**
   * 開閉
   */
  isClose?: boolean
  /**
   * saveType（onlySave，closeAndSave，changetabSave,selefResolve）
   */
  saveType?: string
}

/**
 * OneWayType
 */
export interface Or51734OneWayType {
  /**
   * 区分フラグ
   */
  kbnFlg: string
  /**
   * 施設ID
   */
  shisetuId: string
  /**
   * 事務所ID
   */
  svJigyoId: string
}

/**
 * Event
 */
export interface Or51734EventType {
  /**
   * 保存フラグ
   */
  isSave?: boolean
}
