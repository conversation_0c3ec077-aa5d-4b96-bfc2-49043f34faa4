import type { Mo01360Type } from '@/types/business/components/Mo01360Type'
/**
 * Or52612:［表示設定］画面
 * GUI00625_［表示設定］画面
 *
 * @description
 * GUI00625_［表示設定］画面
 *
 * <AUTHOR>
 */
export interface Or52612StateType {
  /**
   * 開閉フラグ
   */
  isOpen?: boolean
}

/**
 * ［履歴選択］画面
 */
export interface displaySettingDataItem {
  /**
   *id
   */
  id: string
  /**
   *連動番号
   */
  recno: string
  /**
   *連動予定名称
   */
  recName: string
  /**
   *表示区分
   */
  dmyUseKbn: Mo01360Type
  /**
   *機能名称
   */
  kinouKnj: string
  /**
   *表示色値
   */
  dmyDispCd: string
}
/**
 * ［履歴選択］画面
 */
export interface displaySettingReqDataItem {
  /**
   *連動番号
   */
  recno: string

  /**
   *表示区分
   */
  dmyUseKbn: string

  /**
   *表示色値
   */
  dmyDispCd: string
}
/**
 * '事業所CDリスト
 */
export interface jigyoListEntity {
  /**
   *事業所CD
   */
  svJigyoCd: string
}
