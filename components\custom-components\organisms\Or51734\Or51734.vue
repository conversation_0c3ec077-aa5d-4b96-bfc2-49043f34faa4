<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or51732Const } from '../Or51732/Or51732.constants'
import { Or51732Logic } from '../Or51732/Or51732.logic'
import { Or26678Const } from '../Or26678/Or26678.constants'
import { Or26678Logic } from '../Or26678/Or26678.logic'
import type { Or26677Param } from '../Or26677/Or26677.type'
import { Or51734Const } from './Or51734.constants'
import type { Or51734OneWayType, Or51734Param, Or51734StateType } from './Or51734.type'
import { Or51734Logic } from './Or51734.logic'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo01344OnewayType } from '~/types/business/components/Mo01344Type'
import {
  useCmnCom,
  useCmnRouteCom,
  useScreenOneWayBind,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { hasRegistAuth } from '~/utils/useCmnAuthz'

/**
 * Or51734:実施モニタリング記号ダイアログ
 * GUI01250_実施モニタリング記号マスタ
 *
 * @description
 * 実施モニタリング記号 ダイアログ
 *
 * <AUTHOR>
 */

/************************************************
 * Props
 ************************************************/
interface Props {
  uniqueCpId: string
  onewayModelValue: Or51734OneWayType
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/

const { t } = useI18n()

const or26678 = ref({ uniqueCpId: '', parentUniqueCpId: props.uniqueCpId })
const or51732 = ref({ uniqueCpId: '', parentUniqueCpId: props.uniqueCpId })
//Or21814_有機体:確認ダイアログ
const or21814_1 = ref({ uniqueCpId: '' }) // 確認ダイアログ(yes, no, cancel)
const or21814_2 = ref({ uniqueCpId: '' }) // 確認ダイアログ(yes)
const or21814_3 = ref({ uniqueCpId: '' })

// ダイアログ
const mo00024Oneway = ref<Mo00024OnewayType>({
  width: '800px',
  height: '670px',
  persistent: true,
  showCloseBtn: true,
  mo01344Oneway: {
    name: 'Or51734',
    toolbarTitle: '',
    toolbarName: 'Or51734ToolBar',
    toolbarTitleCenteredFlg: false,
    showCardActions: true,
    cardTextClass: 'pa-2',
  } as Mo01344OnewayType,
})

const mo00024 = ref<Mo00024Type>({
  isOpen: Or51734Const.DEFAULT.IS_OPEN,
})

const local = reactive({
  // タブ
  mo00043: {
    id: '',
  } as Mo00043Type,
})

//閉じる
const isClose = ref(false)

const defaultOneway = reactive({
  // タブ
  mo00043OnewayType: {
    tabItems: [
      {
        id: '1',
        title: t('label.implementation-monitoring'),
        tooltipText: t('label.implementation-monitoring'),
      },
      {
        id: '2',
        title: t('label.implementation-monitoring-mark'),
        tooltipText: t('label.implementation-monitoring-mark'),
      },
    ],
    minWidth: '58px',
  } as Mo00043OnewayType,
  // 閉じるコンポーネント
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 保存コンポーネント
  mo00609SaveOneway: {
    btnLabel: t('btn.save'),
    disabled: false,
  } as Mo00609OnewayType,
})

const localOneWay = reactive({
  or51734: {
    ...props.onewayModelValue,
  },
  or26678: {
    editFlg: true,
    shisetuId: props.onewayModelValue.shisetuId,
    svJigyoId: props.onewayModelValue.svJigyoId,
    cpnFlg: '',
  },
})

// 変更タブIDを保持
const tabIdBk = ref('')

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or26678Const.CP_ID(0)]: or26678.value,
  [Or51732Const.CP_ID(0)]: or51732.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or21814Const.CP_ID(2)]: or21814_2.value,
  [Or21814Const.CP_ID(3)]: or21814_3.value,
})

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  let editFlg = false
  switch (local.mo00043.id) {
    case '1':
      editFlg = useScreenStore().isEditByUniqueCpId(or26678.value.uniqueCpId)
      break
    case '2':
      editFlg = useScreenStore().isEditByUniqueCpId(or51732.value.uniqueCpId)
      break
  }

  return editFlg
})

// route共有情報
const cmnRouteCom = useCmnRouteCom()

// 画面ID
const screenId = 'GUI01249'
// ルーティング
const routing = 'GUI01249/pinia'
// ケアマネ画面を初期化する
await useCmnCom().initialize({
  screenId,
  routing,
})

// 共通処理の編集権限チェック
const isPermissionViewAuth = ref(false)
// ローカルで保持する共通情報
const commonInfo = {
  cpnFlg: cmnRouteCom.getInitialSettingMaster()?.cpnFlg ?? '',
}
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or51734StateType>({
  cpId: Or51734Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or51734Const.DEFAULT.IS_OPEN
    },
  },
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(() => {
  void init()
})

/**
 * 実施モニタリング記号マスタ編集権限取得
 *
 */
const init = async () => {
  isPermissionViewAuth.value = await hasRegistAuth()
  isPermissionViewAuth.value = true
  // 編集権限
  defaultOneway.mo00609SaveOneway.disabled = !isPermissionViewAuth.value
  localOneWay.or26678.editFlg = isPermissionViewAuth.value
  localOneWay.or26678.cpnFlg = commonInfo.cpnFlg
  // 確認ダイアログを初期化
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })

  // 確認ダイアログを初期化
  Or21814Logic.state.set({
    uniqueCpId: or21814_2.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'normal3',
      secondBtnLabel: t('btn.no'),
    },
  })
}

/**
 * AC009 「保存ボタン」押下
 *
 * @param saveType - saveType（onlySave，closeAndSave，changetabSave）
 */
const save = (saveType?: string) => {
  // 画面入力データ変更があるかどうかを判定する
  if (!isEdit.value) {
    // メッセージ内容：
    //「変更されている項目がないため、保存を行うことは出来ません。
    // 項目を入力変更してから、再度保存を行ってください。」
    openConfirmDialog3(t('message.i-cmn-21800'))
    return
  } else {
    const id: string = local.mo00043.id
    switch (id) {
      // タブ1
      case '1':
        // or26678のダイアログ状態を更新する
        Or26678Logic.state.set({
          uniqueCpId: or26678.value.uniqueCpId,
          state: {
            param: {
              executeFlag: 'save',
              kbnFlg: localOneWay.or51734.kbnFlg,
              isClose: isClose.value,
              saveType: saveType,
            } as Or51734Param,
          },
        })
        break
      // タブ2
      case '2':
        // Or51732のダイアログ状態を更新する
        Or51732Logic.state.set({
          uniqueCpId: or51732.value.uniqueCpId,
          state: {
            param: {
              executeFlag: 'save',
              kbnFlg: localOneWay.or51734.kbnFlg,
              isClose: isClose.value,
              saveType: saveType,
            } as Or51734Param,
          },
        })
        break
      default:
        break
    }
  }
}

/**
 * 画面最新情報を取得する
 */
const getTabsData = () => {
  switch (local.mo00043.id) {
    // タブ1
    case '1':
      // Or26678のダイアログ状態を更新する
      Or26678Logic.state.set({
        uniqueCpId: or26678.value.uniqueCpId,
        state: {
          param: {
            executeFlag: 'getData',
            shisetuId: localOneWay.or51734.shisetuId,
            svJigyoId: localOneWay.or51734.svJigyoId,
          } as Or26677Param,
        },
      })
      break
    // タブ2
    case '2':
      // Or51732のダイアログ状態を更新する
      Or51732Logic.state.set({
        uniqueCpId: or51732.value.uniqueCpId,
        state: {
          param: {
            executeFlag: 'getData',
            kbnFlg: localOneWay.or51734.kbnFlg,
          } as Or51734Param,
        },
      })
      break
    default:
      break
  }
}

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
const close = async () => {
  // 画面変更チェック
  if (isEdit.value) {
    if (!isPermissionViewAuth.value) {
      // 画面入力変更あり、かつ保存権限がない場合
      // 確認ダイアログ表示
      const dialogResult = await openConfirmDialog2(t('message.w-com-10006'))
      switch (dialogResult) {
        case 'yes': {
          setState({ isOpen: false })
        }
      }
      return
    } else {
      // 画面入力変更あり、かつ保存権限がある場合
      // 確認ダイアログ表示
      const dialogResult = await openConfirmDialog1(t('message.i-cmn-10430'))
      switch (dialogResult) {
        case 'yes': {
          // はい選択時は入力内容を保存する
          save(Or51734Const.DEFAULT.CLOSEANDSAVE)
          break
        }
        case 'no':
          // いいえ選択時は編集内容を破棄するので何もしない
          setState({ isOpen: false })
          break
        case 'cancel':
          // キャンセル選択時は一覧の選択を戻す
          break
      }

      return
    }
  } else {
    setState({ isOpen: false })
  }
}

/**
 * タブ変更処理
 *
 * @param mo00043 - タブ情報
 */
async function tabChange(mo00043: Mo00043Type) {
  // 画面変更チェック
  switch (await checkChange()) {
    case 0: {
      // 画面入力変更なし
      local.mo00043.id = mo00043.id

      break
    }
    case 1: {
      // 画面入力変更あり、編集を破棄して処理を続行
      local.mo00043.id = mo00043.id
      break
    }
    case 2: {
      // 画面入力変更あり、編集を保存して処理を続行

      // 保存
      save(Or51734Const.DEFAULT.CHANGETABSAVE)
      tabIdBk.value = mo00043.id
      break
    }
    case 3: {
      // キャンセル、何もしない
      break
    }
  }
}

/**
 *  データ変更チェック
 */
async function checkChange(): Promise<number> {
  let result = 0

  // 画面変更チェック
  if (isEdit.value) {
    if (!isPermissionViewAuth.value) {
      // 画面入力変更あり、かつ保存権限がない場合
      // 確認ダイアログ表示
      const dialogResult = await openConfirmDialog2(t('message.w-com-10006'))
      switch (dialogResult) {
        case 'yes': {
          // 編集を破棄して処理を続く
          result = 1
        }
      }
    } else {
      // 画面入力変更あり、かつ保存権限がある場合
      // 確認ダイアログ表示
      const dialogResult = await openConfirmDialog1(t('message.i-cmn-10430'))
      switch (dialogResult) {
        case 'yes': {
          // 編集を保存して処理を続く
          result = 2
          break
        }
        case 'no':
          // 編集を破棄して処理を続く
          result = 1
          break
        case 'cancel':
          // キャンセル選択時は何もしない
          result = 3
          break
      }
    }
  } else {
    // 変更なしの場合は何もしないまま処理を続く
    result = 0
  }

  return result
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no, cancel)
 */
async function openConfirmDialog1(paramDialogText: string): Promise<'yes' | 'no' | 'cancel'> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)

        let result = 'cancel' as 'yes' | 'no' | 'cancel'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }
        if (event?.thirdBtnClickFlg) {
          result = 'cancel'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 確認ダイアログ表示
 *
 * @param paramDialogText - メッセージ
 *
 * @returns ダイアログの選択結果（yes, no, cancel)
 */
async function openConfirmDialog2(paramDialogText: string): Promise<'yes' | 'no'> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_2.value.uniqueCpId,
    state: {
      isOpen: true,
      dialogText: paramDialogText,
    },
  })

  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_2.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_2.value.uniqueCpId)

        let result = 'no' as 'yes' | 'no'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_2.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })
        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 保存ボタン押下
 * 変更されている項目がないため、保存を行うことは出来ません。「改行」項目を入力変更してから、再度保存を行ってください。
 *
 * @param paramDialogText - メッセージ
 */
function openConfirmDialog3(paramDialogText: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814_3.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: paramDialogText,
      firstBtnType: 'blank',
      thirdBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.yes'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814_3.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    mo00024.value.isOpen = true
    // 組織dialog自動クローズを手動判定に変更
    if (!newValue) {
      void close()
    }
  }
)

/**
 * 保存が完了したら切り替え
 */
watch(
  () => Or51734Logic.event.get(props.uniqueCpId),
  (newVal) => {
    if (newVal?.isSave) {
      local.mo00043.id = tabIdBk.value
      Or51734Logic.event.set({
        uniqueCpId: props.uniqueCpId,
        state: {
          isSave: false,
        },
      })
    }
  }
)

/**
 * タブ切り替え
 *
 * @description
 * タブ切り替え
 */
watch(
  () => local.mo00043.id,
  (newVal) => {
    mo00024Oneway.value.mo01344Oneway!.toolbarTitle =
      defaultOneway.mo00043OnewayType.tabItems.find((item) => item.id === newVal)?.title +
      t('label.master')
    getTabsData()
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="mo00024Oneway"
  >
    <template #cardItem>
      <base-mo00043
        :model-value="local.mo00043"
        :oneway-model-value="defaultOneway.mo00043OnewayType"
        @update:model-value="tabChange"
      >
      </base-mo00043>
      <c-v-window v-model="local.mo00043.id">
        <c-v-window-item value="1">
          <!-- 実施モニタリングマスタ -->
          <g-custom-or26678
            v-bind="or26678"
            :parent-unique-cp-id="props.uniqueCpId"
            :oneway-model-value="localOneWay.or26678"
        /></c-v-window-item>
        <c-v-window-item value="2"><g-custom-or51732 v-bind="or51732" /></c-v-window-item>
      </c-v-window>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="defaultOneway.mo00611CloseBtnOneWay"
          @click="close"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <!-- 保存ボタン Mo00611 -->
        <base-mo00609
          v-bind="defaultOneway.mo00609SaveOneway"
          class="ml-2"
          @click="save(Or51734Const.DEFAULT.ONLYSAVE)"
        >
          <!--ツールチップ表示："保存します"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.save')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814 v-bind="or21814_1" />
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814 v-bind="or21814_2" />
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814 v-bind="or21814_3" />
</template>
