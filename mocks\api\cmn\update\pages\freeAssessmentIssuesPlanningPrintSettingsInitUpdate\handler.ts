import { HttpResponse } from 'msw'
import defaultData from './data/default.json'
import type { BaseResponseBody } from '~/types/common/api/BaseResponseBody'
import type { FreeAssessmentIssuesPlanningPrintSettingsSelectInEntity } from '~/repositories/cmn/entities/FreeAssessmentIssuesPlanningPrintSettingsSelectEntity.ts'
/**
 * GUI00924_印刷設定
 *
 * @description
 * GUI00924_印刷設定初期情報データを返却する。
 * dataName："freeAssessmentIssuesPlanningPrintSettingsInitUpdate"
 */
export function handler(inEntity: FreeAssessmentIssuesPlanningPrintSettingsSelectInEntity) {
  const responceJson: BaseResponseBody = {
    statusCode: 'success',
    data: {
      ...defaultData,
    },
  }
  return HttpResponse.json(responceJson, { status: 200 })
}

export default { handler }
