import type { Mo00018Type } from '../Or26828/Or26828.type'
import type { InWebEntity } from '~/repositories/AbstructWebRepository'
import type { KikanRirekiData } from '~/repositories/cmn/entities/AttendingPhysicianStatementPrintSettingsInitUpdateEntity'
import type { IInitMasterInfo } from '~/repositories/cmn/entities/GyoumuComSelectEntity'
import type { IPrintInfo, IPrintSettingInfo } from '~/repositories/cmn/entities/PrintSelectEntity'
/**
 * Or30594Const:有機体:印刷設定モーダル
 * GUI01290_［印刷設定］画面
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR>
 */
export interface Or30594StateType {
  /**
   * 開閉フラグ
   */
  isOpen?: boolean
}

/**
 * 双方向バインドのデータ構造
 */
export interface Or30594Data {
  /**
   * 印刷設定情報リスト
   */
  prtList: IPrintInfo[]
  /**
   * 期間履歴情報リスト
   */
  kikanRirekiList: KikanRirekiData[]
  /**
   * 期間管理フラグ
   */
  kikanFlag: string
}

/**
 * 主治医意見書の改定フラグ
 */
export interface RevisionType {
  /**
   * items
   */
  items?: Items[]
}

/**
 * items
 */
export interface Items {
  /** ボタンラベル */
  label: string
  /** 選択に使用する値 */
  value: string | number
}

/**
 * 行選択
 */
export interface CheckRow {
  /** 行番号 */
  index: number
  /** 選択value */
  checkbox: Mo00018Type
}

/**
 * 利用者情報リスト
 */
export interface userInfoList {
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 利用者名
   */
  nameKnj: string
  /**
   * 利用者番号
   */
  userNumber: string
  /**
   * 性別
   */
  sex: string
}

/**
 * 月間・年間表履歴リスト
 */
export interface rirekiList {
  /**
   * 週間表ID
   */
  week1Id?: string
  /**
   * 計画期間ID
   */
  sc1Id: string
  /**
   * 法人ID
   */
  houjinId?: string
  /**
   * 施設ID
   */
  shisetuId?: string
  /**
   * 事業者ID
   */
  svJigyoId?: string
  /**
   * 利用者ID
   */
  userId?: string
  /**
   * 作成者
   */
  shokuId?: string
  /**
   * 作成日
   */
  createYmd?: string
  /**
   * ケース番号
   */
  caseNo?: string
  /**
   * 処理年月
   */
  tougaiYm?: string
  /**
   * 有効期間ID
   */
  termid?: string
  /**
   * 週単位以外ｻｰﾋﾞｽ
   */
  wIgaiKnj?: string
  /**
   * 改訂
   */
  kaiteiFlg?: string
  /**
   * 開始日
   */
  startYmd?: string
  /**
   * 終了日
   */
  endYmd?: string
  /**
   * タイプ
   */
  type?: string
  /**
   * 索引
   */
  index?: number
}

/**
 *  初期設定マスタの情報
 */
export interface InitMedMasterObj extends IInitMasterInfo {
  /**
   * 計画期間管理フラグ
   */
  kikanFlg: string
  /**
   * 改訂フラグ
   */
  kaiteiFlg: string
}

/**
 * 印刷対象履歴リスト
 */
export interface HistoryInfo {
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 履歴ID
   */
  rirekiId: string
}

/**
 * 帳票用情報
 */
export interface FormInfo<I extends IInitMasterInfo, D extends DbNoSaveData, H extends HistoryInfo>
  extends InWebEntity {
  /**
   * 帳票ID
   */
  prtId: string
  /**
   * 帳票データ
   */
  prtData: {
    // 事業所名
    jigyoKnj: string
    // システム日付
    appYmd: string
    // 初期設定マスタの情報
    initMasterObj: I
    // 印刷設定
    printSetting: IPrintSettingInfo
    // DB未保存画面項目
    dbNoSaveData: D
    // 印刷オプション
    printOption: {
      // 記入用シートを印刷するフラグ
      printTheForm: boolean
      // 指定日
      designatedDate: string
      // 印刷対象履歴リスト
      historyList: H[]
      // 初期設定マスタの情報
      initMasterObj: I
      // 事業者名
      svJigyoId: string
    }
  }
}

/**
 * DB未保存画面項目
 */
export interface DbNoSaveData {
  /**
   * 指定日
   */
  selectDate: string
  /**
   * 記入用シートを印刷するフラグ
   */
  emptyFlg: string
}
