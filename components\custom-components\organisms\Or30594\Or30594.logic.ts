import { OrX0143Const } from '../OrX0143/OrX0143.constants'
import { OrX0143Logic } from '../OrX0143/OrX0143.logic'
import { Or28780Const } from '../Or28780/Or28780.constants'
import { Or28780Logic } from '../Or28780/Or28780.logic'
import { Or18615Const } from '../Or18615/Or18615.constants'
import { Or18615Logic } from '../Or18615/Or18615.logic'
import { Or26331Const } from '../Or26331/Or26331.constants'
import { Or26331Logic } from '../Or26331/Or26331.logic'
import { Or26326Const } from '../Or26326/Or26326.constants'
import { Or26326Logic } from '../Or26326/Or26326.logic'
import { Or26328Const } from '../Or26328/Or26328.constants'
import { Or26328Logic } from '../Or26328/Or26328.logic'
import { OrX0145Const } from '../OrX0145/OrX0145.constants'
import { OrX0145Logic } from '../OrX0145/OrX0145.logic'
import { Or30594Const } from './Or30594.constants'
import type { Or30594StateType } from './Or30594.type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import { OrX0130Logic } from '~/components/custom-components/organisms/OrX0130/OrX0130.logic'
import { OrX0117Const } from '~/components/custom-components/organisms/OrX0117/OrX0117.constants'
import { OrX0117Logic } from '~/components/custom-components/organisms/OrX0117/OrX0117.logic'
import { Or10016Logic } from '~/components/custom-components/organisms/Or10016/Or10016.logic'
import { Or10016Const } from '~/components/custom-components/organisms/Or10016/Or10016.constants'

/**
 * Or30594Const:有機体:印刷設定モーダル
 * GUI01290_［印刷設定］画面
 * ロジック
 *
 * @description
 * initialize処理とpinia領域操作用の処理を提供する
 *
 * <AUTHOR>
 */
export namespace Or30594Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or30594Const.CP_ID(0),
      uniqueCpId,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [
        { cpId: Or21813Const.CP_ID(0) },
        { cpId: Or21814Const.CP_ID(0) },
        { cpId: OrX0130Const.CP_ID(0) },
        { cpId: OrX0143Const.CP_ID(0) },
        { cpId: OrX0145Const.CP_ID(0) },
        { cpId: OrX0117Const.CP_ID(0) },
        { cpId: Or10016Const.CP_ID(0) },
        { cpId: Or28780Const.CP_ID(0) },
        { cpId: Or18615Const.CP_ID(0) },
        { cpId: Or26331Const.CP_ID(0) },
        { cpId: Or26326Const.CP_ID(0) },
        { cpId: Or26328Const.CP_ID(0) },
      ],

      // 編集フラグ不要
      // ※ 元々双方向領域を持たないため記載不要だが、サンプル用にナビゲーション制御領域で持つデータを分かりやすくするために設定
      editFlgNecessity: false,
    })

    // 子コンポーネントのセットアップ
    Or21813Logic.initialize(childCpIds[Or21813Const.CP_ID(0)].uniqueCpId)
    Or21814Logic.initialize(childCpIds[Or21814Const.CP_ID(0)].uniqueCpId)
    OrX0130Logic.initialize(childCpIds[OrX0130Const.CP_ID(0)].uniqueCpId)
    OrX0143Logic.initialize(childCpIds[OrX0143Const.CP_ID(0)].uniqueCpId)
    OrX0145Logic.initialize(childCpIds[OrX0145Const.CP_ID(0)].uniqueCpId)
    OrX0117Logic.initialize(childCpIds[OrX0117Const.CP_ID(0)].uniqueCpId)
    Or10016Logic.initialize(childCpIds[Or10016Const.CP_ID(0)].uniqueCpId)
    Or28780Logic.initialize(childCpIds[Or28780Const.CP_ID(0)].uniqueCpId)
    Or18615Logic.initialize(childCpIds[Or18615Const.CP_ID(0)].uniqueCpId)
    Or26331Logic.initialize(childCpIds[Or26331Const.CP_ID(0)].uniqueCpId)
    Or26326Logic.initialize(childCpIds[Or26326Const.CP_ID(0)].uniqueCpId)
    Or26328Logic.initialize(childCpIds[Or26328Const.CP_ID(0)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }

  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or30594StateType>(Or30594Const.CP_ID(0))
}
