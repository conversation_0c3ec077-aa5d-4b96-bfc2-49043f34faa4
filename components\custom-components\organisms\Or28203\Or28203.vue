<script setup lang="ts">
/**
 * Or28203:有機体:（基本チェックリスト）基本チェックリスト画面
 * GUI01080_基本チェックリスト
 *
 * @description
 * （基本チェックリスト）基本チェックリスト画面
 *
 * <AUTHOR> NGUYEN VAN PHONG
 */
import { onMounted, reactive, ref, watch, computed, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { isUndefined, isEqual } from 'lodash'
import { Or40815Const } from '../Or40815/Or40815.constants'
import { Or28202Const } from '../Or28202/Or28202.constants'
import { Or28169Const } from '../Or28169/Or28169.constants'
import { OrD2002Const } from '../OrD2002/OrD2002.constants'
import { Or28170Const } from '../Or28170/Or28170.constants'
import { Or28171Const } from '../Or28171/Or28171.constants'
import { Or28172Const } from '../Or28172/Or28172.constants'
import { Or28173Const } from '../Or28173/Or28173.constants'
import { Or40815Logic } from '../Or40815/Or40815.logic'
import { Or28198Const } from '../Or28198/Or28198.constants'
import { Or28199Const } from '../Or28199/Or28199.constants'
import { Or28200Const } from '../Or28200/Or28200.constants'
import { Or28201Const } from '../Or28201/Or28201.constants'
import { OrX0115Logic } from '../OrX0115/OrX0115.logic'
import { OrX0115Const } from '../OrX0115/OrX0115.constants'
import { Or52740Logic } from '../Or52740/Or52740.logic'
import { Or28171Logic } from '../Or28171/Or28171.logic'
import { Or31173Const } from '../Or31173/Or31173.constants'
import { Or28202Logic } from '../Or28202/Or28202.logic'
import { OrX0132Logic } from '../OrX0132/OrX0132.logic'
import { Or28169Logic } from '../Or28169/Or28169.logic'
import { Or26385Logic } from '../Or26385/Or26385.logic'
import type { Or28203StateType } from './Or28203.type'
import { Or28203Const } from './Or28203.constants'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import { OrHeadLineConst } from '~/components/base-components/organisms/OrHeadLine/OrHeadLine.constants'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import {
  useCommonProps,
  useNuxtApp,
  useScreenStore,
  useSetupChildProps,
  useScreenUtils,
  useScreenTwoWayBind,
  useScreenInitFlg,
} from '#imports'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  IBaseCheckListSelectInEntity,
  IBaseCheckListSelectOutEntity,
} from '~/repositories/cmn/entities/BaseCheckListSelectEntity'
import type { OrX0021OnewayType } from '~/types/cmn/business/components/OrX0021Type'
import type { OrCpGroupDefinitionInputFormDeleteDialogOnewayType } from '~/types/business/generator-components/OrCpGroupDefinitionInputFormDeleteDialog'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Or40815OnewayType, Or40815Type } from '~/types/cmn/business/components/Or40815Type'
import type { Or28169OnewayType, Or28169Type } from '~/types/cmn/business/components/Or28169Type'
import type { PlanCreateDataType } from '~/types/PlanCreateDataType'
import type { Mo00020Type } from '~/types/business/components/Mo00020Type'
import type { Mo01343Type } from '~/types/business/components/Mo01343Type'
import type { Or28170OnewayType, Or28170Type } from '~/types/cmn/business/components/Or28170Type'
import type { Or28171OnewayType } from '~/types/cmn/business/components/Or28171Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { AuthzRepository } from '~/repositories/business/core/authz/AuthzRepository'
import type {
  ICheckAuthzKinouInEntity,
  ICheckAuthzKinouOutEntity,
} from '~/repositories/business/core/authz/entities/CheckAuthzKinouEntity'
import type {
  IBasicChecklist,
  Or28202OnewayType,
  Or28202Type,
} from '~/types/cmn/business/components/Or28202Type'
import type { Or52740OnewayType, Or52740Type } from '~/types/cmn/business/components/Or52740Type'
import { Or00249Const } from '~/components/base-components/organisms/Or00249/Or00249.constants'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'
import type { Or41179StateType } from '~/components/base-components/organisms/Or41179/Or41179.type'
import type { IBasicChecklistOutEntity } from '~/repositories/cmn/entities/BasicChecklistEntity'
import type { Or21814OnewayType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import type { Or26257Type } from '~/types/cmn/business/components/Or26257Type'
import type {
  IOrBasicChecklistPlanPeriodModifiedSelectInEntity,
  IOrBasicChecklistPlanPeriodModifiedSelectOutEntity,
} from '~/repositories/cmn/entities/OrBasicChecklistPlanPeriodModifiedSelectEntity'
import type {
  IOrBasicChecklistHistoryModifiedSelectInEntity,
  IOrBasicChecklistHistoryModifiedSelectOutEntity,
  chkList2List,
  KihonChklistRirekObj,
} from '~/repositories/cmn/entities/OrBasicChecklistHistoryModifiedSelectEntity'
import type { OrHeadLineType } from '~/types/business/generator-components/OrHeadLineType'
import type { NavControlJsonType } from '~/types/business/core/DefinitionJsonType'
import type {
  BaseCheckListUpdateInEntity,
  kihinChekuList,
} from '~/repositories/cmn/entities/BaseCheckListUpdateEntity'

/**
 * setChildCpBinds
 */
const { setChildCpBinds } = useScreenUtils()
/**
 * vue-i18n
 */
const { t } = useI18n()
/** システム共有領域の状態管理 */
const systemCommonsStore = useSystemCommonsStore()
// 画面状態管理用操作変数
const screenStore = useScreenStore()
/**************************************************
 * Props
 **************************************************/
/**
 * defineProps
 */
const props = defineProps(useCommonProps())

const or28202Ref = ref({
  initData: (_or28202Oneway: Or28202OnewayType) => {},
})

/**
 * 共通情報
 */
const commonInfoData = reactive({
  // 事業所ID
  svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
  //svJigyoId: '0',
  // 利用者ID
  // userId: systemCommonsStore.getUserId ?? '',
  userId: '0000000148', // TODO APIとバックエンドを結合するために使用される
  // 施設ID
  shisetsuId: systemCommonsStore.getShisetuId ?? '',
  //shisetsuId: '19', // TODO APIとバックエンドを結合するために使用される
  // 種別ID
  shubetsuId: systemCommonsStore.getSyubetu ?? '',
  //shubetsuId: '4', // TODO APIとバックエンドを結合するために使用される
  // 法人ID
  houjinId: systemCommonsStore.getHoujinId ?? '',
  //houjinId: '0', // TODO APIとバックエンドを結合するために使用される
  // システム年月日
  sysDate: systemCommonsStore.getSystemDate ?? '',
  // ログイン職員ＩＤ
  loginUserId: systemCommonsStore.getCurrentUser.chkShokuId ?? '', // TODO
  //loginUserId: '31', // TODO APIとバックエンドを結合するために使用される
  // 職員名
  loginUserName: systemCommonsStore.getCurrentUser.shokuinKnj ?? '',
  // 計画期間ID
  kikanId: '',
  // 履歴ID
  historyId: '0',
  // 履歴変更区分
  createUpdateFlg: '',
})

/**
 * 閲覧権限がない場合：画面に遷移するためのタブを非表示とする
 */
const input: ICheckAuthzKinouInEntity = {
  keys: [{ path: '/components/custom-components/organisms/Or28203/Or28203' }],
}
/**
 * Response AuthzRepository
 */
const res: ICheckAuthzKinouOutEntity = await AuthzRepository.checkAuthzKinou(input)

/**
 * viewAuthority
 */
const viewAuthority = ref<boolean>(
  Array.isArray(res.data?.authzKinou)
    ? res.data.authzKinou
        .filter((authz) => authz.path === '/components/custom-components/organisms/Or28203/Or28203')
        .every((authz) => authz.use1 === true)
    : false
)

/**
 * Default One-way
 */
const defaultOneway = reactive({
  // 事業所
  or41179State: {} as Or41179StateType,
  // 作成日
  or28171Oneway: { deleteFlg: false } as Or28171OnewayType,
})

/**
 * Local One-way
 */
const localOneway = reactive({
  or41179State: {
    ...defaultOneway.or41179State,
  } as Or41179StateType,
  // 計画対象期間
  or40815Oneway: {
    planTargetPeriodData: {
      planTargetPeriodId: 0,
      planTargetPeriod: '',
      currentIndex: 0,
      totalCount: 0,
    },
    planTargetPeriodSelectList: [],
    orX0115Oneway: {
      kindId: systemCommonsStore.getSyubetu ?? '',
      sc1Id: '',
    },
  } as Or40815OnewayType,
  or28169Oneway: {} as Or28169OnewayType,
  or28170Oneway: {} as Or28170OnewayType,
  or28202Oneway: {
    cpyFlg: false,
    periodManageFlag: '',
    planTargetPeriodId: '',
    basicChecklist: {} as IBasicChecklist,
  } as Or28202OnewayType,
  or28171Oneway: {
    ...defaultOneway.or28171Oneway,
  } as Or28171OnewayType,
  mo00615Twoway: {
    itemLabelFontWeight: 'bold',
    itemLabel: t('label.policy-support-independence'),
    customClass: new CustomClass({ outerClass: 'mr-2', labelClass: 'my-2' }),
  } as Mo00615OnewayType,

  // 確認ダイアログ
  mo00009DialogOneway: {
    msg: t('message.i-cmn-11276'),
    btnNoDisplay: false,
    btnCancelDisplay: false,
    mo00024Oneway: {
      class: 'mr-1',
      name: '',
      width: '360px',
    } as Mo00024OnewayType,
  } as OrCpGroupDefinitionInputFormDeleteDialogOnewayType,
  updateConfirm: {
    emitType: '',
    mo00024: {
      isOpen: false,
    } as Mo00024Type,
  },
  // 期間管理選択のエラーダイアログ
  orX0021ErrorDialogOneWay: {
    message: t('message.i-cmn-11300'),
    mo00024Oneway: {
      persistent: true,
    } as Mo00024OnewayType,
  } as OrX0021OnewayType,
  orX0021ErrorDialog: {
    emitType: '',
    mo00024: {
      isOpen: false,
    } as Mo00024Type,
  },
})

// GUI01081_基本チェックリスト複写をポップアップで起動する
const or52740OnewayModel: Or52740OnewayType = {
  shisetuId: '',
  svJigyoId: '',
  userId: '',
  syubetuId: '',
  kikanFlg: '1',
  kikanId: '',
}
// GUI01081_基本チェックリスト複写をポップアップで起動する
const or52704Type = ref<Or52740Type>({
  histId: '',
} as Or52740Type)

/**
 * defaultModelValue
 */
const defaultModelValue = {
  or28203: {
    /** 利用者一覧の選択値 */
    headlineValue: '',
    /** ケアマネ総合計画データ再取得フラグ */
    retrieveCmComprehensivePlanDataFlg: false,
    /** 計画書新規作成フラグ */
    newPlanFlg: false,
    /** 計画書保存（新規登録・更新）フラグ */
    savePlanUpdateFlg: false,
    /** 計画書保存（削除） */
    savePlanDeleteFlg: false,
    /** 計画書様式 */
    planFormat: 0,
    setInputComponentsFlg: false,
    createId: 0,
    /** 計画対象期間表示フラグ */
    showOr40815Flg: true,
    /** 作成者表示フラグ */
    showOr28170Flg: true,
    /** 作成日表示フラグ */
    showOr28171Flg: true,
    /** 履歴表示フラグ */
    showOr28169Flg: true,
    createUpdateFlg: '',
    planTargetPeriodId: 0,
    planTargetPeriodUpdateFlg: '',
    planTargetPeriodFlg: '1',
    createDate: '',
    updateFlag: 'D',
  } as Or28203StateType,
}

/**
 * local
 */
const local = reactive({
  isShowPlanningPeriod: true as boolean,
  planningPeriodEmpty: true as boolean,
  or28203: {
    ...defaultModelValue.or28203,
  } as Or28203StateType,
  inputType: '',
  kihinChekuList: {} as IBasicChecklist,
  or28202: {} as Or28202Type,
  or28170: {} as Or26257Type,
  or40815: {
    planTargetPeriodUpdateFlg: '',
    planTargetPeriodId: '',
  } as Or40815Type,
  // Flag check local
  flagCheckLocal: {
    checkPlaningPeriodSave: false as boolean,
  },
  // 基本チェックリスト履歴画面取得情報
  rirekiDataLocal: {} as KihonChklistRirekObj,
})

const basicChecklistData = ref<Or28202Type>({} as Or28202Type)

/**
 * 履歴更新区分
 */
const historyUpdKbn = ref<string>('')

/**
 * or21814
 */
const or21814 = ref({ uniqueCpId: '' })
/**
 * or41179
 */
const or41179 = ref({ uniqueCpId: '' })
/**
 * or40815
 */
const or40815 = ref({ uniqueCpId: '' })
/**
 * or28202
 */
const or28202 = ref({ uniqueCpId: '' })
/**
 * or11871
 */
const or11871 = ref({ uniqueCpId: '' })
/**
 * or00248
 */
const or00248 = ref({ uniqueCpId: '' })
/**
 * orHeadLine
 */
const orHeadLine = ref({ uniqueCpId: '' })
/**
 * or00249
 */
const or00249 = ref({ uniqueCpId: '' })
/**
 * or28169
 */
const or28169 = ref({ uniqueCpId: '' })
/**
 * or28170
 */
const or28170 = ref({ uniqueCpId: '' })
/**
 * or28171
 */
const or28171 = ref({ uniqueCpId: '' })
/**
 * orD2002_1
 */
const orD2002_1 = ref({ uniqueCpId: '' })
/**
 * or28172
 */
const or28172 = ref({ uniqueCpId: '' })
const or28173 = ref({ uniqueCpId: '' })
const orX0115 = ref({ uniqueCpId: '' })
const or28201 = ref({ uniqueCpId: '' })
const or28198 = ref({ uniqueCpId: '' })
const or28199 = ref({ uniqueCpId: '' })
const or28200 = ref({ uniqueCpId: '' })
const or52740 = ref({ uniqueCpId: '' })

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or00248Const.CP_ID(0)]: or00248.value,
  [Or11871Const.CP_ID]: or11871.value,
  [Or41179Const.CP_ID(0)]: or41179.value,
  [Or40815Const.CP_ID(1)]: or40815.value,
  [Or28202Const.CP_ID(1)]: or28202.value,
  [Or28169Const.CP_ID(0)]: or28169.value,
  [Or28170Const.CP_ID(1)]: or28170.value,
  [Or28171Const.CP_ID(1)]: or28171.value,
  [Or28172Const.CP_ID(0)]: or28172.value,
  [Or28173Const.CP_ID(0)]: or28173.value,
  [OrD2002Const.CP_ID(0)]: orD2002_1.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
})

useSetupChildProps(or00248.value.uniqueCpId, {
  [OrHeadLineConst.CP_ID]: orHeadLine.value,
  [Or00249Const.CP_ID(0)]: or00249.value,
})

useSetupChildProps(or28202.value.uniqueCpId, {
  [Or28201Const.CP_ID(0)]: or28201.value,
})

useSetupChildProps(or28201.value.uniqueCpId, {
  [Or28198Const.CP_ID(0)]: or28198.value,
  [Or28199Const.CP_ID(0)]: or28199.value,
  [Or28200Const.CP_ID(0)]: or28200.value,
})

useSetupChildProps(or40815.value.uniqueCpId, {
  [OrX0115Const.CP_ID(0)]: orX0115.value,
})

/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<Or28202Type>({
  cpId: Or28202Const.CP_ID(1),
  uniqueCpId: or28202.value.uniqueCpId,
})

refValue.value = {
  basicChecklist: {} as IBasicChecklist,
} as Or28202Type

/**
 * 変更されたリスニングのコンポーネントIDリスト
 */
const watchedComponents = ref<string[]>([or28202.value.uniqueCpId, props.uniqueCpId])

/**************************************************
 * コンポーネント固有処理
 **************************************************/
const isInit = useScreenInitFlg()
onMounted(async () => {
  // 確認ダイアログを初期化
  console.log('systemCommonsStore.getSvJigyoId', systemCommonsStore.getSvJigyoId)

  if (systemCommonsStore.getUserSelectSelfId) {
    Or41179Logic.state.set({
      uniqueCpId: or41179.value.uniqueCpId,
      state: {
        searchCriteria: {
          selfId: systemCommonsStore.getUserSelectSelfId(),
        },
      },
    })
  }

  // 利用者を全選択です。
  await nextTick(() => {
    useScreenTwoWayBind<OrHeadLineType>({
      cpId: OrHeadLineConst.CP_ID,
      uniqueCpId: orHeadLine.value.uniqueCpId,
    }).setValue({ value: '全' })
  })

  if (isInit) {
    await initOr28203()
  }

  // 確認ダイアログを初期化
  // Or21814Logic.state.set({
  //   uniqueCpId: or21814.value.uniqueCpId,
  //   state: {
  //     dialogTitle: t('label.top-btn-title'),
  //     dialogText: t('message.i-cmn-10430'),
  //     firstBtnType: 'normal3',
  //     firstBtnLabel: t('btn.cancel'),
  //     secondBtnType: 'normal3',
  //     secondBtnLabel: t('btn.no'),
  //     thirdBtnType: 'normal1',
  //     thirdBtnLabel: t('btn.yes'),
  //     iconName: 'info',
  //     iconColor: 'rgb(var(--v-theme-blue-700))',
  //     iconBackgroundColor: 'rgb(var(--v-theme-blue-200))',
  //   },
  // })
})

Or11871Logic.state.set({
  uniqueCpId: or11871.value.uniqueCpId,
  state: {
    screenTitleLabel: t('label.basic-checklist'),
    showFavorite: true,
    showCreateBtn: true,
    showCreateMenuCopy: true,
    showPrintBtn: true,
    showOptionMenuBtn: true,
    showMasterBtn: true,
    showOptionMenuDelete: true,
    showSaveBtn: true,
    disabledViewSelect: true,
    showViewSelect: false,
  },
})

// ナビゲーション制御領域のいずれかの編集フラグがON
// const isEdit = computed(() => {
//   return useScreenStore().isEditNavControl()
// })

/**
 * いずれかの編集フラグがONになっているかチェックする
 *
 * @param components - 変更のリスニングが必要なリスト
 *
 * @returns いずれかの編集有無
 */
function isEditNavControl(components: string[]) {
  const screenStore = useScreenStore()
  const screenNavControl = screenStore.getScreenNavControl<NavControlJsonType>()

  // ナビゲーション制御領域を走査
  for (const key in screenNavControl) {
    if (screenNavControl[key] === screenNavControl.components) {
      // ナビゲーション制御領域 - コンポーネント毎の領域を走査
      for (const cpKey in screenNavControl[key]) {
        if (components.includes(cpKey)) {
          if (screenNavControl[key][cpKey].editFlg) {
            // コンポーネント毎の編集フラグがON
            return true
          }
          if (screenNavControl[key][cpKey].items) {
            // ナビゲーション制御領域 - コンポーネント毎の領域にitemsが存在する場合は走査
            for (const itemKey in screenNavControl[key][cpKey].items) {
              if (screenNavControl[key][cpKey].items[itemKey].editFlg) {
                // コンポーネント - itemsの編集フラグがON
                return true
              }
            }
          }
        }
      }
    }
  }

  return false
}

watch(
  () => local.or28202.basicChecklist,
  async (newVal) => {
    await nextTick()
    console.log('------------new value: ', newVal)
    console.log('------------refValue: ', refValue.value)

    if (JSON.stringify(newVal) !== JSON.stringify(refValue.value?.basicChecklist)) {
      // //if (!isEqual(newVal.basicChecklist, refValue.value!.basicChecklist)) {
      console.log('-------------------is edit')
      refValue.value!.basicChecklist = newVal
    }
  },
  { deep: true, immediate: true }
)

/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or11871Logic.event.get(or11871.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }

    if (newValue.favoriteEventFlg) {
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { favoriteEventFlg: false },
      })
    }

    if (newValue.saveEventFlg) {
      await _save()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { saveEventFlg: false },
      })
    }

    if (newValue.createEventFlg) {
      await _create()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { createEventFlg: false },
      })
    }

    if (newValue.printEventFlg) {
      await _print()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { printEventFlg: false },
      })
    }

    if (newValue.masterEventFlg) {
      await _master()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { masterEventFlg: false },
      })
    }

    if (newValue.deleteEventFlg) {
      await _delete()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { deleteEventFlg: false },
      })
    }

    if (newValue.copyEventFlg) {
      _copy()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { copyEventFlg: false },
      })
    }
  }
)
/**
 * 事業所選択更新の監視 (Or41179)
 */
watch(
  () => Or41179Logic.data.get(or41179.value.uniqueCpId),
  async (newValue, oldValue) => {
    // Undefinedの時戻す
    if (isUndefined(newValue) || !oldValue?.modelValue) {
      return
    }
    console.log('Or41179Logic', newValue)

    // const isEdit = computed(() => {
    //   return useScreenStore().isEditNavControl()
    // })

    // // 画面データ変更かどうかを判断する
    // if (isEdit.value) {
    //   const dialogResult = await openEditDialog()
    //   switch (dialogResult) {
    //     case 'yes':
    //       await _save()
    //       break
    //     case 'no':
    //       // いいえ選択時は編集内容を破棄するので何もしない
    //       break
    //     case 'cancel':
    //       // キャンセル選択時は処理を終了する
    //       return
    //   }
    // }

    if (newValue.modelValue) {
      commonInfoData.svJigyoId = newValue.modelValue ?? ''
      commonInfoData.kikanId = ''
      commonInfoData.historyId = '0'

      // 画面を再初期化する
      await initOr28203()
    }
  },
  {
    deep: true,
  }
)

/**
 * 計画対象期間
 */
watch(
  () => OrX0115Logic.event.get(orX0115.value.uniqueCpId),
  async (newValue, oldValue) => {
    if (newValue) {
      localOneway.or40815Oneway.planTargetPeriodData.planTargetPeriod = `${newValue.startYmd} ~ ${newValue.endYmd}`
      if (newValue?.kikanId === oldValue?.kikanId) return
      local.or40815.planTargetPeriodId = newValue?.kikanId ?? ''
      // 更新フラグINIT
      Or40815Logic.data.set({
        uniqueCpId: or40815.value.uniqueCpId,
        value: {
          planTargetPeriodId: local.or40815.planTargetPeriodId,
          planTargetPeriodUpdateFlg: '',
        },
      })

      // 計画期間変更処理を行う
      await changePlanningPeriod(
        local.or40815.planTargetPeriodId,
        Or28203Const.KIKAN_PAGE_KBN.FIXED
      )
    }
  }
)

/**
 * AC012 : 「計画対象期間選択アイコンボタン」押下
 *
 */
watch(
  () => Or40815Logic.data.get(or40815.value.uniqueCpId),
  async (newValue) => {
    /**********************************************************************
     * データ再取得
    /*********************************************************************/
    if (isUndefined(newValue)) {
      return
    }

    const planID = newValue.planTargetPeriodId
    const planUpdateFlg = newValue.planTargetPeriodUpdateFlg

    if (planUpdateFlg === undefined || planUpdateFlg === '') {
      return
    }

    // 画面入力データに変更がある場合
    const isEdit = isEditNavControl(watchedComponents.value)

    if (isEdit) {
      const dialogResult = await openConfirmDialog({
        dialogText: t('message.i-cmn-10430'),
      })
      switch (dialogResult) {
        case 'yes':
          // はい：保存して（AC003と同じ）、次の処理へ進む
          await _save()
          break
        case 'no':
          // いいえ：保存しない、次の処理へ進む
          break
        case 'cancel':
          // キャンセル：処理終了
          return
      }
    }

    //「選択前の対象期間から変更がある場合
    if (planUpdateFlg === Or40815Const.UPDATE_CATEGORY_SELECT) {
      // 期間選択画面を呼び出す
      OrX0115Logic.state.set({
        uniqueCpId: newValue.orX0115UniqueCpId!,
        state: { isOpen: true },
      })
    }
    //「期間-前へ アイコンボタン」押下
    else if (planUpdateFlg === Or40815Const.UPDATE_CATEGORY_PREVIOUS) {
      local.or40815.planTargetPeriodUpdateFlg = planUpdateFlg

      // 計画期間変更処理を行う
      await changePlanningPeriod(
        local.or40815.planTargetPeriodId,
        Or31173Const.KIKAN_PAGE_KBN.BEFORE
      )
    }
    //「期間-次へ アイコンボタン」押下
    else if (planUpdateFlg === Or40815Const.UPDATE_CATEGORY_NEXT) {
      local.or40815.planTargetPeriodUpdateFlg = planUpdateFlg
      // 計画期間変更処理を行う
      await changePlanningPeriod(
        local.or40815.planTargetPeriodId,
        Or28203Const.KIKAN_PAGE_KBN.AFTER
      )
    }
  }
)

/**
 * AC015 :「履歴選択アイコンボタン」押下
 */
watch(
  () => Or28169Logic.data.get(or28169.value.uniqueCpId),
  async (newValue, oldValue) => {
    console.log('Or28169Logic newvalue', newValue)

    /**********************************************************************
     * データ再取得
    /*********************************************************************/
    if (isUndefined(newValue)) {
      return
    }
    const historyId = newValue.historyId
    const historyUpateFlg = newValue.historyUpateFlg

    if (historyUpateFlg === undefined || historyUpateFlg === '') {
      return
    }

    // if(newValue.historyId === oldValue?.historyId) {
    //   // 更新フラグINIT
    //   Or28169Logic.data.set({
    //     uniqueCpId: or28169.value.uniqueCpId,
    //     value: {
    //       historyId: newValue.historyId,
    //       historyUpateFlg: '',
    //     },
    //   })
    //   // 履歴変更処理を行う
    //   await changeHistory(String(historyId), '0', local.or40815.planTargetPeriodId)
    //   return
    // }

    const isEdit = isEditNavControl(watchedComponents.value)

    if (isEdit) {
      const dialogResult = await openConfirmDialog({
        dialogText: t('message.i-cmn-10430'),
      })
      switch (dialogResult) {
        case 'yes':
          // はい：保存して（AC003と同じ）、次の処理へ進む
          await _save()
          break
        case 'no':
          // いいえ：保存しない、次の処理へ進む
          break
        case 'cancel':
          // キャンセル：処理終了
          return
      }
    }

    //「履歴-選択確認前 アイコンボタン」押下
    if (historyUpateFlg === Or28169Const.UPDATE_CATEGORY_SELECT || historyUpateFlg === Or28169Const.UPDATE_CATEGORY_ACTION_WHEN_CREATE) {
      // 履歴-選択画面を呼び出す
      await changeHistory(String(historyId), historyUpateFlg, local.or40815.planTargetPeriodId)
    }
    //「履歴-前へ アイコンボタン」押下
    else if (historyUpateFlg === Or28169Const.UPDATE_CATEGORY_PREVIOUS) {
      await changeHistory(String(historyId), historyUpateFlg, local.or40815.planTargetPeriodId)
    }
    //「履歴-次へ アイコンボタン」押下
    else if (historyUpateFlg === Or28169Const.UPDATE_CATEGORY_NEXT) {
      // 履歴変更処理を行う
      await changeHistory(String(historyId), historyUpateFlg, local.or40815.planTargetPeriodId)
    }
  },
  { deep: true }
)

/**
 * 編集破棄ダイアログ表示
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
async function openEditDialog(): Promise<'yes' | 'no' | 'cancel'> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = 'cancel' as 'yes' | 'no' | 'cancel'

        if (event?.thirdBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }
        if (event?.firstBtnClickFlg) {
          result = 'cancel'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 確認ダイアログ表示
 *
 * @param state -state
 *
 * @returns ダイアログの選択結果（yes, no, cancel)
 */
async function openConfirmDialog(state: Or21814OnewayType): Promise<'yes' | 'no' | 'cancel'> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      dialogTitle: t('label.info'),
      isOpen: true,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
      ...state,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)
        let result = 'no' as 'yes' | 'no' | 'cancel'
        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }
        if (event?.thirdBtnClickFlg) {
          result = 'cancel'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 新規作成時処理
 */
async function _create() {
  // AC004: 計画期間情報がデータない場合、「新規」ボタン押下
  if (local.or28203.planTargetPeriodId === 0) {
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: {
        isOpen: true,
        dialogText: t('message.i-cmn-11300'),
        firstBtnType: 'blank',
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      },
    })
  }

  const isEdit = isEditNavControl(watchedComponents.value)
  // 'AC004-2: 修正されたデータが未保存の場合
  if (isEdit) {
    const dialogResult = await openConfirmDialog({
      dialogText: t('message.i-cmn-10430'),
    })
    switch (dialogResult) {
      case 'yes':
        // はい：保存して（AC003と同じ）、次の処理へ進む
        await _save()
        setEmptyData()
        break
      case 'no':
        // いいえ：保存しない、次の処理へ進む
        setEmptyData()
        return
      case 'cancel':
        // キャンセル：処理終了
        return
    }
  }

  setEmptyData()
}

/**
 * 新しいプランフォームデータの設定
 *
 */
function setEmptyData() {

  const initTotalHistory = Number(localOneway.or28169Oneway.krirekiTotalCnt) + 1

  localOneway.or28169Oneway.krirekiNo = String(initTotalHistory)
  localOneway.or28169Oneway.krirekiTotalCnt = String(initTotalHistory)

  // 作成日
  Or28171Logic.data.set({
    uniqueCpId: or28171.value.uniqueCpId,
    value: {
      value: systemCommonsStore.getSystemDate! ,
    },
  })

  const chkList2ListData = {
    dataId: '',
    rirekiId: '',
    q1: '',
    q2: '',
    q3: '',
    q4: '',
    q5: '',
    q6: '',
    q7: '',
    q8: '',
    q9: '',
    q10: '',
    q11: '',
    q12Height: '',
    q12Weight: '',
    q12Bmi: '',
    q13: '',
    q14: '',
    q15: '',
    q16: '',
    q17: '',
    q18: '',
    q19: '',
    q20: '',
    q21: '',
    q22: '',
    q23: '',
    q24: '',
    q25: '',
    program1Flg: '',
    program2Flg: '',
    program3Flg: '',
    program4Flg: '',
    program5Flg: '',
    program6Flg: '',
    memoKnj: '',
    kiboSvKnj: '',
    modifiedCnt: '',
  } as chkList2List

  setDataContent(chkList2ListData)
}

/**
 * 画面メニュー印刷設定アイコンボタン
 *
 * @returns TODO
 */
async function _print() {
  const dialogResult = await openEditDialog()

  switch (dialogResult) {
    case 'yes':
      // TODO 画面未作成
      break
    case 'no':
      // TODO いいえ選択時は後続処理へ進む
      break
    case 'cancel':
      // キャンセル選択時は処理終了
      return
  }
}

/**
 * マスタアイコンボタン
 *
 * @returns TODO
 */
async function _master() {
  const isEdit = isEditNavControl(watchedComponents.value)
  if (isEdit) {
    const dialogResult = await openEditDialog()
    switch (dialogResult) {
      case 'yes':
        // TODO : 共通GUI00039_その他の機能画面をポップアップで起動する
        break
      case 'no':
        // TODO
        break
      case 'cancel':
        // キャンセル選択時は処理終了。
        return
    }
  }
}

/**
 * 「削除」押下
 */
async function _delete() {
  if (local.or28203.updateFlag && local.or28203.updateFlag === 'D') {
    const dialogResult = await openConfirmDialog({
      dialogText: t('message.i-cmn-11326', [local.or28203.createDate, t('label.basic-checklist')]),
      thirdBtnType: 'blank',
    })
    switch (dialogResult) {
      case 'yes':
        // はい選択時は入力内容を保存する
        await callApiUpdate()
        await initOr28203()
        break
      case 'no':
        return
    }
  }
}

/**
 * AC002: 保存処理
 * 質問No1～質問No25の項目が未入力の場合
 *
 */
function isEmptyQuestion() {
  const q12Map: Record<string, string> = {
    q12Height: '身長',
    q12Weight: '体重',
    q12Bmi: 'BMI',
  }

  const invalidKeys: string[] = []
  Object.entries(local.or28202).forEach(([key, value]) => {
    if (key.startsWith('q')) {
      // q12Height / Weight / Bmi
      if (q12Map[key]) {
        if (value === '') {
          invalidKeys.push(q12Map[key])
        }
        return
      }

      const match = /^q(\d+)$/.exec(key)
      if (match) {
        const num = match[1]
        if (value !== '0' && value !== '1') {
          invalidKeys.push(num)
        }
      }
    }
  })

  return invalidKeys.join(',')
}

/**
 * 保存時処理
 *
 */
async function _save() {
  const isEdit = isEditNavControl(watchedComponents.value)

  console.log('refValue.value', refValue.value)
  console.log('isEdit', isEdit)
  console.log('local.or28202', local.or28202)

  // AC003 : 画面.計画対象期間-ページングが"0 / 0"の場合 => 処理終了。
  if (!local.flagCheckLocal.checkPlaningPeriodSave) {
    return
  }

  // AC003 : 画面入力項目が変更なく、そのまま保存ボタン押下した場合
  if (!isEdit) {
    // 情報ダイアログ表示
    Or21814Logic.state.set({
      uniqueCpId: or21814.value.uniqueCpId,
      state: {
        isOpen: true,
        dialogTitle: t('label.info'),
        dialogText: t('message.i-cmn-21800'),
        firstBtnType: 'blank',
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      },
    })
  }

  // AC002: 保存処理
  const checkQuestion = isEmptyQuestion()
  // ■以下のメッセージを表示
  if (checkQuestion.length > 0) {
    const dialogResult = await openConfirmDialog({
      dialogText: t('message.i-cmn-11342', [checkQuestion]),
    })
    // 処理終了
    if (dialogResult === 'no' || dialogResult === 'cancel') return
  }

  await callApiUpdate()
}

async function callApiUpdate() {
  // 基本チェックリストData
  const { dataId, ...kihinChekuListNew } = local.or28202.basicChecklist
  const kihinChekuList: kihinChekuList = { ...kihinChekuListNew, delFlg: '0' }

  const dataSave: BaseCheckListUpdateInEntity = {
    houjinId: commonInfoData.houjinId,
    shisetuId: commonInfoData.shisetsuId,
    svJigyoId: commonInfoData.svJigyoId,
    userid: commonInfoData.userId,
    chiJigyoId: local.rirekiDataLocal.chiJigyoId ?? '',
    itkJigyoId: local.rirekiDataLocal.itkJigyoId ?? '',
    // 機能区分 1:予防給付、2:支援事業、3:スクリーニング
    kycFlg: local.rirekiDataLocal.kycFlg ?? '',
    syubetsuId: commonInfoData.shubetsuId,
    sc1id: local.rirekiDataLocal.sc1Id ?? '',
    rirekiId: local.rirekiDataLocal.rirekiId ?? '',
    // 履歴変更フラグ 0:変更無し、1:変更有り
    historyUpdateKbn: '0',
    createYMD: '',
    shokuId: '0',
    shokuKnj: '',
    modifiedCnt: '0',
    delFlg: '0',
    kihinChekuList: [kihinChekuList],
  }

  // 保存ボタン押下
  await ScreenRepository.update('baseCheckListUpdate', dataSave)
}

// GUI01081_基本チェックリスト複写をポップアップで起動する
const showDialogOr52740DispFlg = computed(() => {
  // GUI01081_基本チェックリスト複写をポップアップで起動する
  return Or52740Logic.state.get(or52740.value.uniqueCpId)?.isOpen ?? false
})
/**
 * Copy : GUI01081_基本チェックリスト複写をポップアップで起動する
 */
function _copy() {
  // // AC005 : GUI01081_基本チェックリスト複写をポップアップで起動する
  // Or52740Logic.state.set({
  //   uniqueCpId: or52740.value.uniqueCpId,
  //   state: { isOpen: true },
  // })
}

const initOr28203 = async () => {
  // データ取得
  const basicCheckListData: IBaseCheckListSelectOutEntity = await getBasicCheckListData()

  // 画面初期化のComponents表示フラグ設定、画面初期化のデータ設定
  setFormData(basicCheckListData)
}

const getBasicCheckListData = async (): Promise<IBaseCheckListSelectOutEntity> => {
  const inputParam: IBaseCheckListSelectInEntity = {
    /** 利用者ID */
    // userid: systemCommonsStore.getUserId ?? '',
    userid: '9999',
    /** 事業者ID */
    svJigyoId: commonInfoData.svJigyoId,
    /** 種別ID */
    syubetsuId: commonInfoData.shubetsuId,
    /** 施設ID */
    shisetuId: commonInfoData.shisetsuId,
  }

  // 初期情報取得
  const res: IBaseCheckListSelectOutEntity = await ScreenRepository.select(
    'baseCheckListSelect',
    inputParam
  )

  return res
}

/**
 * 計画期間変更処理を行う
 *
 * @param sc1Id - 計画期間ID
 *
 * @param pageFlag - 計画期間ページ区分
 */
async function changePlanningPeriod(sc1Id: string, pageFlag: string) {
  const param: IOrBasicChecklistPlanPeriodModifiedSelectInEntity = {
    /**
     * 事業所ＩＤ
     */
    svJigyoId: commonInfoData.svJigyoId,
    /**
     * 利用者ＩＤ
     */
    userId: '9999', // TODO
    /**
     * 施設ＩＤ
     */
    shisetuId: commonInfoData.shisetsuId,
    /**
     * 種別ID
     */
    syubetsuId: commonInfoData.shubetsuId,
    /**
     * 計画期間ページ区分
     * 0: 選択している期間ID
     * 1: 選択している期間IDの前の期間
     * 2: 選択している期間IDの後の期間
     */
    pageFlag: pageFlag,
    /**
     * 法人ID
     */
    houjinId: commonInfoData.houjinId,
    /**
     * 計画期間ID
     */
    sc1Id: sc1Id ?? '',
  }

  // 計画期間変条件を取得する
  const response: IOrBasicChecklistPlanPeriodModifiedSelectOutEntity =
    await ScreenRepository.select('orBasicChecklistPlanPeriodModifiedSelect', param)

  setFormData(response)
}

/**
 * 履歴変更処理を行う
 *
 * @param rirekiId - 履歴ID
 *
 * @param pageFlag - 履歴変更区分
 *
 * @param plandId - 計画期間ID
 */
async function changeHistory(rirekiId: string, pageFlag: string, plandId: string) {
  const param: IOrBasicChecklistHistoryModifiedSelectInEntity = {
    svJigyoId: commonInfoData.svJigyoId,
    userId: '9999', // TODO
    sc1Id: plandId,
    rirekiId: rirekiId,
    pageFlag: pageFlag,
  }
  // 計画期間変条件を取得する
  const response: IOrBasicChecklistHistoryModifiedSelectOutEntity = await ScreenRepository.select(
    'orBasicChecklistHistoryModifiedSelect',
    param
  )

  setDataHistory(response.data.kihonChklistRirekObj)
  setDataContent(response.data.chkList2List[0])
}
/**
 * 基本チェックリスト履歴画面取得情報
 *
 * @param historyData - 基本チェックリスト履歴画面取得情報
 */
function setDataHistory(historyData: Or28169OnewayType) {
  localOneway.or28169Oneway = historyData
  local.rirekiDataLocal = historyData
  // 作成者
  localOneway.or28170Oneway.shokuId = historyData.shokuId
  localOneway.or28170Oneway.shokuKnj = historyData.shokuKnj
  localOneway.or28170Oneway.shokuName = historyData.shokuName

  local.or28203.createDate = historyData.createYmd
  // 作成日
  Or28171Logic.data.set({
    uniqueCpId: or28171.value.uniqueCpId,
    value: {
      value: historyData.createYmd,
    },
  })
}

/**
 *
 * 基本チェックリスト詳細リスト
 *
 * @param chkList2ListData - 基本チェックリスト詳細リスト
 */
function setDataContent(chkList2ListData: chkList2List) {
  basicChecklistData.value.basicChecklist = chkList2ListData
  refValue.value = JSON.parse(JSON.stringify(basicChecklistData.value)) as Or28202Type
  setChildCpBinds(props.uniqueCpId, {
    [Or28202Const.CP_ID(1)]: {
      twoWayValue: JSON.parse(JSON.stringify(basicChecklistData.value)) as Or28202Type,
    },
  })
  local.kihinChekuList = chkList2ListData
  localOneway.or28202Oneway.basicChecklist = JSON.parse(
    JSON.stringify(basicChecklistData.value.basicChecklist)
  ) as IBasicChecklist
  or28202Ref.value.initData(localOneway.or28202Oneway)
}

/**
 *
 * 画面コントロール表示設定
 *
 * @param res - 初期情報
 */
function setFormData(res: IBaseCheckListSelectOutEntity) {
  // APIからの初期データ
  const resData = res.data

  // 計画対象期間
  if (resData && resData.kikanObj instanceof Object) {
    localOneway.or40815Oneway.planTargetPeriodData.planTargetPeriodId = Number(
      resData.kikanObj.sc1Id
    )
    localOneway.or40815Oneway.planTargetPeriodData.planTargetPeriod =
      resData.kikanObj.startYmd + ' ~ ' + resData.kikanObj.endYmd
    localOneway.or40815Oneway.planTargetPeriodData.currentIndex = Number(resData.kikanObj.kikanNo)
    localOneway.or40815Oneway.planTargetPeriodData.totalCount = Number(
      resData.kikanObj.kikanTotalCnt
    )

    local.or28203.planTargetPeriodId = Number(resData.kikanObj.sc1Id)

    local.or40815.planTargetPeriodId = resData.kikanObj.sc1Id
    local.or40815.planTargetPeriodUpdateFlg = ''

    // 画面.計画対象期間-ページングが"0 / 0"の場合
    if (
      localOneway.or40815Oneway.planTargetPeriodData.totalCount !== 0 &&
      localOneway.or40815Oneway.planTargetPeriodData.currentIndex !== 0
    ) {
      local.flagCheckLocal.checkPlaningPeriodSave = true
    }

    // 期間管理フラグが「1:管理する」、かつ、計画対象期間リストが0件
    if (
      resData.kikanFlg &&
      resData.kikanFlg === '1' &&
      localOneway.or40815Oneway.planTargetPeriodData.totalCount <= 0
    ) {
      localOneway.or40815Oneway.planTargetPeriodData.currentIndex = 0
      local.planningPeriodEmpty = false
    }
  }

  // 期間管理フラグが「0:管理しない」
  // 以下画面項目が非表示する。
  //・計画対象期間選択、計画対象期間、計画対象期間なしメッセージ、計画対象期間-ページング
  // 期間管理フラグが「1:管理する」
  // 以下画面項目が表示る。
  // ・計画対象期間選択、計画対象期間、計画対象期間-ページング
  if (resData.kikanFlg && resData.kikanFlg === '0') {
    local.isShowPlanningPeriod = false
  } else if (resData.kikanFlg && resData.kikanFlg === '1') {
    local.isShowPlanningPeriod = true
  }

  // 履歴
  if (resData && resData.kihonChklistRirekObj instanceof Object) {
    setDataHistory(resData.kihonChklistRirekObj)
  }

  // 基本チェックリスト明細入力
  if (resData.chkList2List && resData.chkList2List.length > 0) {
    setDataContent(resData.chkList2List[0])
  }

  // 事業所設定
  const jigyoSelectInfoList = systemCommonsStore.getJigyoSelectInfoList
  console.log('jigyoSelectInfoList', jigyoSelectInfoList)

  const svJigyoIdSelected = resData.svJigyoRyakuKnj ?? ''

  const svJigyoSelectedObj =
    jigyoSelectInfoList.find((item) => item.svJigyoId === svJigyoIdSelected) ??
    jigyoSelectInfoList[0]

  local.or28203.officeId = svJigyoSelectedObj.svJigyoId
  local.or28203.officeName = svJigyoSelectedObj.jigyoRyakuKnj
}
</script>

<template>
  <c-v-sheet class="view">
    <!-- Or11871：有機体：画面メニューエリア -->
    <g-base-or11871 v-bind="or11871" />
    <c-v-row
      no-gutters
      class="content-area"
    >
      <c-v-col
        cols="2"
        class="hidden-scroll h-100"
      >
        <!-- （利用者基本）利用者選択の表示 -->
        <!-- <g-base-or00248 v-bind="or00248" /> -->
      </c-v-col>

      <c-v-col
        cols="9"
        class="hidden-scroll h-100 px-2"
      >
        <c-v-sheet class="content">
          <c-v-container v-if="viewAuthority">
            <c-v-row>
              <c-v-col
                style="padding-top: 8px; padding-bottom: 0px; padding-right: 8px; padding-left: 8px"
              >
                <!-- 事業所 -->
                <g-base-or41179
                  v-bind="or41179"
                  class="mb-2"
                />
              </c-v-col>
            </c-v-row>
            <c-v-row class="second-row">
              <c-v-col
                v-if="local.isShowPlanningPeriod"
                cols="auto"
                style="padding-top: 8px; padding-bottom: 0px; padding-right: 8px; padding-left: 8px"
              >
                <!-- 計画対象期間 -->
                <g-custom-or40815
                  v-bind="or40815"
                  :oneway-model-value="localOneway.or40815Oneway"
                  :unique-cp-id="or40815.uniqueCpId"
                />
              </c-v-col>
              <c-v-col
                v-if="local.planningPeriodEmpty"
                cols="auto"
                style="padding-left: 8px; padding-top: 8px; padding-bottom: 0px; padding-right: 8px"
              >
                <g-custom-or28169
                  v-bind="or28169"
                  :oneway-model-value="localOneway.or28169Oneway"
                  :unique-cp-id="or28169.uniqueCpId"
                />
              </c-v-col>
              <c-v-col
                v-if="local.planningPeriodEmpty"
                cols="auto"
                :style="{ width: '220px' }"
                style="padding-top: 8px; padding-bottom: 0px; padding-right: 8px; padding-left: 0px"
              >
                <!-- 作成者 GUI00220 職員検索画面をポップアップで起動する。 -->
                <g-custom-or28170
                  v-bind="or28170"
                  v-model="local.or28170"
                  :oneway-model-value="localOneway.or28170Oneway"
                  :unique-cp-id="or28170.uniqueCpId"
                />
              </c-v-col>
              <c-v-col
                v-if="local.planningPeriodEmpty"
                cols="auto"
                style="padding-left: 0px; padding-top: 8px; padding-bottom: 0px; padding-right: 8px"
              >
                <!-- 作成日 -->
                <g-custom-or28171
                  v-bind="or28171"
                  :oneway-model-value="localOneway.or28171Oneway"
                  :unique-cp-id="or28171.uniqueCpId"
                />
              </c-v-col>
            </c-v-row>

            <c-v-divider
              class="divider-class"
              style="margin: 16px; margin-left: 0px; padding-left: 8px"
            />
            <c-v-row justify="left">
              <c-v-col>
                <!-- 基本チェックリスト明細入力 -->
                <g-custom-or28202
                  v-bind="or28202"
                  ref="or28202Ref"
                  v-model="local.or28202"
                />
              </c-v-col>
            </c-v-row>
          </c-v-container>
        </c-v-sheet>
      </c-v-col>
    </c-v-row>
    <c-v-row no-gutters>
      <c-v-col>
        <!-- コピーライトフッター -->
        <g-base-or00051 />
      </c-v-col>
    </c-v-row>
  </c-v-sheet>
  <!-- Or21814 確認ダイアログ -->
  <g-base-or21814 v-bind="or21814" />

  <!-- 期間管理フラグが「1:管理する」、かつ、計画対象期間リストが0件のエラーダイアログ -->
  <g-custom-or-x-0021
    v-model="localOneway.orX0021ErrorDialog"
    :oneway-model-value="localOneway.orX0021ErrorDialogOneWay"
  >
  </g-custom-or-x-0021>
  <!-- <g-custom-or-52740
    v-if="showDialogOr52740DispFlg"
    v-bind="or52740"
    v-model="or52704Type"
    :oneway-model-value="or52740OnewayModel"
  /> -->
</template>

<style scoped lang="scss">
.divider-class {
  border-width: thin;
  margin: 8px 0px;
}
.divider-noLine-class {
  border: none;
  margin: 32px 0px;
  border-color: white;
}
.view {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: transparent;
}
.content-area {
  display: flex;
  flex-grow: 1;
  overflow: hidden;
  height: 100%;
}

.content {
  padding: 5px 0px;
  overflow-x: auto;
}
.second-row {
  margin-top: 0px;
  align-items: baseline;
}
:deep(.v-sheet) {
  background-color: transparent !important;
}
</style>
