<script setup lang="ts">
/**
 * Or32281:有機体:印刷設定
 * GUI00924_印刷設定
 *
 * @description
 * 印刷設定
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { reactive, ref, onMounted, watch, computed } from 'vue'
import { Or32281Const } from './Or32281.constants'
import type { Or32281StateType, Or32281MsgBtnType, Or32281Param } from './Or32281.type'
import { useSetupChildProps, useScreenOneWayBind, useNuxtApp, dateUtils } from '#imports'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00045OnewayType, Mo00045Type } from '~/types/business/components/Mo00045Type'
import type { Mo00610OnewayType } from '~/types/business/components/Mo00610Type'
import type { Mo01344OnewayType } from '@/types/business/components/Mo01344Type'
import type { Mo00039Items, Mo00039OnewayType } from '@/types/business/components/Mo00039Type'
import type { Mo00020Type, Mo00020OnewayType } from '@/types/business/components/Mo00020Type'
import type { Mo00018Type, Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  UserEntity,
  periodHistoryList,
  PrtEntity,
  IssueOrganizeSummaryPrintSettingsSelectInEntity,
  IssueOrganizeSummaryPrintSettingsSelectOutEntity,
  IssueOrganizeSummaryPrintSettingsHistorySelectInEntity,
  IssueOrganizeSummaryPrintSettingsHistorySelectOutEntity,
  IssueOrganizeSummaryPrintSettingsListHistorySelectInEntity,
  IssueOrganizeSummaryPrintSettingsListHistorySelectOutEntity,
} from '~/repositories/cmn/entities/IssueOrganizeSummaryPrintSettingsUpdateEntity'
import type {
  SysIniInfoEntity,
  IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity,
  IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateOutEntity,
} from '~/repositories/cmn/entities/FreeAssessmentFacePrintSettingsPrtNoChangeUpdateEntity'
import type {
  IFreeAssessmentFacePrintSettingsUpdateInEntity,
  IFreeAssessmentFacePrintSettingsUpdateOutEntity,
} from '~/repositories/cmn/entities/FreeAssessmentFacePrintSettingsUpdateEntity'
import type {
  Mo01334OnewayType,
  Mo01334Type,
  Mo01334Items,
} from '~/types/business/components/Mo01334Type'
import { OrX0117Const } from '~/components/custom-components/organisms/OrX0117/OrX0117.constants'
import { OrX0117Logic } from '~/components/custom-components/organisms/OrX0117/OrX0117.logic'
import type { OrX0117History, OrX0117OnewayType } from '~/types/cmn/business/components/OrX0117Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import { CustomClass } from '~/types/CustomClassType'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { OrX0128Const } from '~/components/custom-components/organisms/OrX0128/OrX0128.constants'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import { OrX0130Logic } from '~/components/custom-components/organisms/OrX0130/OrX0130.logic'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import type {
  OrX0128OnewayType,
  OrX0128Items,
  OrX0128Headers,
} from '~/types/cmn/business/components/OrX0128Type'
import { OrX0128Logic } from '~/components/custom-components/organisms/OrX0128/OrX0128.logic'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Or21814OnewayType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { useReportUtils, reportOutputType } from '~/utils/useReportUtils'
import type {
  PrintSetEntity,
  PrintOptionEntity,
  PrintSubjectHistoryEntity,
  ChoPrtEntity,
  ICpnTucRaiAssReportSelectInEntity,
} from '~/repositories/cmn/entities/CpnTucRaiAssReportSelectEntity'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import type {
  Mo01408Type,
  Mo01408OnewayType,
  Mo01408ItemType,
} from '@/types/business/components/Mo01408Type'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import type { DebugLogPluginInterface } from '~/plugins/debug-log'
import { useCmnCom } from '@/utils/useCmnCom'
import { useCmnRouteCom } from '~/utils/useCmnRouteCom'
import type { Mo01339OnewayType } from '~/types/business/components/Mo01339Type'
import type { Or21813StateType } from '~/components/base-components/organisms/Or21813/Or21813.type'
import type { Or21815StateType } from '~/components/base-components/organisms/Or21815/Or21815.type'

const systemCommonsStore = useSystemCommonsStore()
const cmnRouteCom = useCmnRouteCom()
const { reportOutput } = useReportUtils()
const { convertDateToSeireki } = dateUtils()
const $log = useNuxtApp().$log as DebugLogPluginInterface

const { t } = useI18n()
/************************************************
 * Props
 ************************************************/
interface Props {
  uniqueCpId: string
}

const props = defineProps<Props>()

// 子コンポーネント用変数
const orX0117 = ref({ uniqueCpId: '' })
const orX0128 = ref({ uniqueCpId: '' })
const orX0130 = ref({ uniqueCpId: '' })
const or21814 = ref({ uniqueCpId: '' })
const or21813 = ref({ uniqueCpId: '' })
const or21815 = ref({ uniqueCpId: '' })

const localOneway = reactive({
  /**
   * 印鑑欄ボタン
   */
  mo00610OneWay: {
    btnLabel: t('label.seal-column'),
    width: '100px',
    disabled: false,
    prependIcon: '',
    appendIcon: '',
  } as Mo00610OnewayType,
  /**
   * 基本設定
   */
  mo01338OneWayBaseSetting: {
    value: t('label.basic-settings'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 帳票タイトル
   */
  mo00615OneWayTypeTitle: {
    itemLabel: t('label.print-settings-title'),
    showItemLabel: true,
  } as Mo00615OnewayType,
  /**
   * タイトル表示
   */
  mo00045OnewayTitleInput: {
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
    width: '200',
  } as Mo00045OnewayType,
  /**
   * 日付印刷区分
   */
  mo00039OneWay: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  /**
   * 指定日
   */
  mo00020OneWay: {
    showItemLabel: false,
    showSelectArrow: false,
    mo00009OnewayBack: {},
    mo00009OnewayForward: {},
  } as Mo00020OnewayType,
  /**
   * アセスメントタイプ
   */
  mo00039OneWayAssessmentType: {
    name: '',
    showItemLabel: false,
    inline: false,
    disabled: true,
  } as Mo00039OnewayType,
  /**
   * 印刷オプション
   */
  mo01338OneWayPrinterOption: {
    value: t('label.printer-option'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 敬称を変更する
   */
  mo00018OneWayChangeHonorific: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.honorifics-change'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
   * 敬称テキストボックス
   */
  mo00045OneWayChangeHonorific: {
    showItemLabel: false,
    isVerticalLabel: false,
    isRequired: false,
    hideDetails: true,
    width: '65',
    // 最大桁数 4
    maxLength: '2',
  } as Mo00045OnewayType,
  /**
   * 記入用シートを印刷する
   */
  mo00018OneWayPrintTheForm: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.printer-entry-sheet'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
   * 備考の下線を印刷しない
   */
  mo00018OneWayPrintUnderline: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.no-remarks-underline'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
   * 備考の開始位置を調整しない
   */
  mo00018OneWayStartPosition: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.no-remarks-position-adjustment'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
   * 見通しの下線を印刷しない
   */
  mo00018OneWayOutlookUnderline: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.no-outlook-underline'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
   * 別紙に印刷する
   */
  mo00018OneWayPrintAttachment: {
    name: '',
    itemLabel: '',
    checkboxLabel: t('label.receipt-separate-sheet'),
    isVerticalLabel: true,
    showItemLabel: true,
    indeterminate: false,
    hideDetails: 'auto',
    disabled: false,
  } as Mo00018OnewayType,
  /**
   * 備考ラベル
   */
  mo01338OneWayLabel: {
    value: t('label.remarks-label'),
    customClass: new CustomClass({
      itemStyle: 'color: #A8A8A8; font-size: smaller;',
    }),
  } as Mo01338OnewayType,
  /**
   * 利用者選択ラベル
   */
  mo01338OneWayPrinterUserSelect: {
    value: t('label.printer-user-select'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 履歴選択ラベル
   */
  mo01338OneWayPrinterHistorySelect: {
    value: t('label.printer-history-select'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 利用者選択
   */
  mo00039OneWayUserSelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  /**
   * 履歴選択
   */
  mo00039OneWayHistorySelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  // 基準日
  mo00615OneWayType: {
    itemLabel: t('label.base-date'),
    showItemLabel: true,
  } as Mo00615OnewayType,
  /**
   * 基準日: 表用テキストフィールド
   */
  mo00020KijunbiOneWay: {
    showItemLabel: false,
    showSelectArrow: true,
    width: '132px',
    totalWidth: '220px',
    mo00009OnewayBack: {},
    mo00009OnewayForward: {},
  } as Mo00020OnewayType,
  /**
   * 担当ケアマネ
   */
  mo00615OneWayCareManagerInCharge: {
    itemLabel: t('label.care-manager-in-charge'),
    showItemLabel: true,
  } as Mo00615OnewayType,
  /**
   * 担当ケアマネプルダウン
   */
  mo01408OneWayType: {
    items: [] as Mo01408ItemType[],
    disabled: false,
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01408OnewayType,
  /**
   * 閉じるボタン
   */
  mo00611OneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  /**
   * PDFダウンロードボタン
   */
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
    disabled: false,
  } as Mo00609OnewayType,
})

/**
 * 親画面の情報
 */
const local = {
  /**
   * 個人情報使用フラグ
   */
  kojinhogoUsedFlg: Or32281Const.DEFAULT.KOJINHOGO_USED_FLG,
  /**
   * 個人情報番号
   */
  sectionAddNo: Or32281Const.DEFAULT.SECTION_ADD_NO,
  /**
   * 親画面.事業所ID
   */
  svJigyoId: Or32281Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.職員ID
   */
  shokuId: Or32281Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.法人ID
   */
  houjinId: Or32281Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.施設ID
   */
  shisetuId: Or32281Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.担当者ID
   */
  tantoId: Or32281Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.種別ID
   */
  syubetsuId: Or32281Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.セクション名
   */
  sectionName: Or32281Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.利用者ID
   */
  userId: Or32281Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.事業所名
   */
  svJigyoKnj: Or32281Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.処理年月日
   */
  processYmd: Or32281Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.アセスメントID
   */
  assessmentId: Or32281Const.DEFAULT.STR.EMPTY,
  /**
   * 選択された帳票のプロファイル
   */
  profile: Or32281Const.DEFAULT.STR.EMPTY,
  /**
   * 選択利用者ID
   */
  selectUserId: Or32281Const.DEFAULT.STR.EMPTY,
  /**
   * 出力帳票名一覧に選択行番号
   */
  index: Or32281Const.DEFAULT.STR.EMPTY,
  /**
   * システムINI情報
   */
  sysIniInfo: {} as SysIniInfoEntity,
  /**
   * 履歴一覧が0件選択の場合（※履歴リストが0件、複数件を含む）
   */
  historyNoSelect: false,
  /**
   * 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
   */
  userNoSelect: false,
  /**
   * 帳票番号
   */
  prtNo: Or32281Const.DEFAULT.STR.EMPTY,
  /**
   * 選択利用者リスト
   */
  userList: [] as UserEntity[],
  /**
   * 帳票ID
   */
  reportId: Or32281Const.DEFAULT.STR.EMPTY,
  /**
   * 履歴選択の明細
   */
  orX0128DetList: [] as OrX0128Items[],
  /**
   * 印刷設定情報リスト
   */
  prtList: [] as PrtEntity[],
}

const localData: IssueOrganizeSummaryPrintSettingsSelectOutEntity = {
  data: {},
} as IssueOrganizeSummaryPrintSettingsSelectOutEntity
/**************************************************
 * 変数定義
 **************************************************/
// ダイアログ
const mo00024Oneway = ref<Mo00024OnewayType>({
  width: '1300px',
  persistent: true,
  showCloseBtn: true,
  mo01344Oneway: {
    name: 'Or32281',
    toolbarTitle: t('label.print-set'),
    toolbarName: 'Or32281ToolBar',
    toolbarTitleCenteredFlg: false,
    showCardActions: true,
    cardTextClass: 'or32281_content',
  } as Mo01344OnewayType,
})

const mo00024 = ref<Mo00024Type>({
  isOpen: Or32281Const.DEFAULT.IS_OPEN,
})

/**
 * 出力帳票名一覧
 */
const mo01334Oneway = ref<Mo01334OnewayType>({
  headers: [
    {
      title: t('label.ledger'),
      key: 'ledgerName',
      sortable: false,
      minWidth: '100',
    },
  ],
  items: [],
  height: 655,
})

/**
 * 出力帳票名一覧
 */
const mo01334Type = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

/**
 * 帳票タイトル
 */
const mo00045TypeTitleInput = ref<Mo00045Type>({
  value: '',
} as Mo00045Type)

const currentTile = ref<string>('')

/**
 * 日付印刷区分
 */
const mo00039Type = ref<string>('')

/**
 * 指定日
 */
const mo00020Type = ref<Mo00020Type>({
  value: convertDateToSeireki(undefined),
} as Mo00020Type)

/**
 * アセスメントタイプ
 */
const mo00039OneWayAssessmentTypeType = ref<string>('')

/**
 * 印刷時に色をつける
 */
const mo00018TypeColor = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * テキストの有効性
 */
const mo00045TypetextInput = ref<Mo00045Type>({
  value: '',
} as Mo00045Type)

/**
 * 敬称テキストボックス
 */
const mo00018TypeChangeTitle = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 記入用シートを印刷する
 */
const mo00018TypeList = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 *備考の下線を印刷しない
 */
const mo00018TypePrintUnderline = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 備考の開始位置を調整しない
 */
const mo00018TypeStartPosition = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 見通しの下線を印刷しない
 */
const mo00018TypeOutlookUnderline = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 別紙に印刷する
 */
const mo00018TypePrintAttachment = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 利用者選択
 */
const mo00039OneWayUserSelectType = ref<string>('')

/**
 * 履歴選択
 */
const mo00039OneWayHistorySelectType = ref<string>('')

/**
 * 当ケアマネプルダウン選択値
 */
const mo01408Type = ref<Mo01408Type>({
  value: '',
} as Mo01408Type)

/**
 * 基準日
 */
const mo00020TypeKijunbi = ref<Mo00020Type>({
  value: convertDateToSeireki(undefined),
} as Mo00020Type)

/**
 * 指定日非表示/表示フラゲ
 */
const mo00020Flag = ref<boolean>(false)
/**
 * 基準日フラゲ
 */
const kijunbiFlag = ref<boolean>(false)
/**
 * 履歴一覧セクションフラゲ
 */
const mo01334TypeHistoryFlag = ref<boolean>(false)

/**
 * 利用者列幅
 */
const userCols = ref<number>(4)

const orX0128OnewayModel = reactive<OrX0128OnewayType>({
  /**
   * 期間管理フラグ
   */
  kikanFlg: '1',
  /**
   * 単一複数フラグ(0:単一,1:複数)
   */
  singleFlg: OrX0128Const.DEFAULT.TANI,
  tableStyle: 'width:480px',
  headers: [
    { title: t('label.create-date'), key: 'assDateYmd', minWidth: '120px', sortable: false },
    { title: t('label.author'), key: 'shokuinKnj', minWidth: '160px', sortable: false },
    { title: t('label.style'), key: 'assTypeKnj', sortable: false },
  ] as OrX0128Headers[],
  items: [],
})

/**
 * 利用者
 */
const orX0130Oneway = reactive<OrX0130OnewayType>({
  selectMode: OrX0130Const.DEFAULT.TANI,
  tableStyle: 'width:210px',
})

/**
 * 履歴一覧 期間管理フラグが「管理しない」の場合
 */
const mo01334TypeHistory1 = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

/**
 * 履歴一覧 期間管理フラグが「管理しない」の場合
 */
const mo01334OnewayHistory1 = ref<Mo01334OnewayType>({
  headers: [
    // 作成日
    {
      title: t('label.create-date'),
      key: 'kijunbi',
      sortable: false,
      minWidth: '100',
    },
    // 作成者
    {
      title: t('label.author'),
      key: 'author',
      sortable: false,
      minWidth: '140',
    },
    // 様式
    {
      title: t('label.style'),
      key: 'assessmentKind',
      sortable: false,
      minWidth: '150',
    },
  ],
  items: [],
  height: 520,
})

/**
 * 履歴一覧 履歴選択方法が「複数」且つ 期間管理フラグが「管理しない」の場合
 */
const mo01334TypeHistory3 = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

/**
 * 履歴一覧 履歴選択方法が「複数」且つ 期間管理フラグが「管理しない」の場合
 */
const mo01334OnewayHistory3 = ref<Mo01334OnewayType>({
  headers: [
    // 作成日
    {
      title: t('label.prevention-evaluation-date'),
      key: 'kijunbi',
      sortable: false,
      minWidth: '100',
    },
    // 作成者
    {
      title: t('label.author'),
      key: 'author',
      sortable: false,
      minWidth: '140',
    },
    // 様式
    {
      title: t('label.style-name'),
      key: 'assessmentKind',
      sortable: false,
      minWidth: '150',
    },
  ],
  items: [],
  height: 520,
  showSelect: true,
  selectStrategy: 'all',
})

// 説明用ラベル'('
const mo013391Oneway = ref<Mo01339OnewayType>({
  value: t('label.left-bracket'),
  customClass: {
    labelStyle: 'center-label',
  } as CustomClass,
})

// 説明用ラベル')'
const mo013392Oneway = ref<Mo01339OnewayType>({
  value: t('label.right-bracket'),
  customClass: {
    labelStyle: 'center-label',
  } as CustomClass,
})

/**
 * 担当ケアマネ選択アイコン
 */
const tantoIconBtn = ref<boolean>(false)

/**
 * 印刷設定帳票出力
 */
const orX0117Oneway: OrX0117OnewayType = {
  type: Or32281Const.DEFAULT.TANI,
  historyList: [] as OrX0117History[],
} as OrX0117OnewayType

/**
 * 初期情報取得フラゲ
 */
const initFlag = ref<boolean>(false)

/**
 * 期間管理フラグ
 */
const kikanFlag = ref<string>(Or32281Const.DEFAULT.STR.EMPTY)

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or32281StateType>({
  cpId: Or32281Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or32281Const.DEFAULT.IS_OPEN
    },
    param: (value) => {
      if (value) {
        local.prtNo = value.prtNo
        local.svJigyoId = value.svJigyoId
        local.shisetuId = value.shisetuId
        local.tantoId = value.tantoId
        local.syubetsuId = value.syubetsuId
        local.sectionName = value.sectionName
        local.userId = value.userId
        local.svJigyoKnj = value.svJigyoKnj
        local.processYmd = value.processYmd
        local.assessmentId = value.assessmentId
      }
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0117Const.CP_ID(0)]: orX0117.value,
  [OrX0128Const.CP_ID(0)]: orX0128.value,
  [OrX0130Const.CP_ID(0)]: orX0130.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or21813Const.CP_ID(0)]: or21813.value,
  [Or21815Const.CP_ID(0)]: or21815.value,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 画面ID
const screenId = 'GUI00924'
// ルーティング
const routing = 'GUI00924/pinia'
// ケアマネ画面を初期化する
await useCmnCom().initialize({
  screenId,
  routing,
})

onMounted(async () => {
  // 汎用コード取得API実行
  await initCodes()

  // 画面ボタン活性非活性設定
  btnItemSetting()

  // 初期情報取得
  await init()
})

// ダイアログ表示フラグ
const showDialogOrX0117 = computed(() => {
  // Or32281のダイアログ開閉状態
  return OrX0117Logic.state.get(orX0117.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 汎用コード取得API実行
 */
const initCodes = async () => {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // アセスメント種別コード
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND },
    // 日付印刷区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    // 単複数選択区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY },
    // 性別区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_GENDER_CATEGORY },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 日付印刷区分
  const bizukePrintCategoryCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )
  if (bizukePrintCategoryCodeTypes?.length > 0) {
    mo00039Type.value = bizukePrintCategoryCodeTypes[0].value
    const list: Mo00039Items[] = []
    for (const item of bizukePrintCategoryCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    localOneway.mo00039OneWay.items = list
  }

  // アセスメントタイプ
  const assessmentKindCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND
  )
  if (assessmentKindCodeTypes?.length > 0) {
    const list: Mo00039Items[] = []
    for (const item of assessmentKindCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    localOneway.mo00039OneWayAssessmentType.items = list
  }

  // 利用者選択 TAN_MULTIPLE_SELECT_CATEGORY
  const tanMultipleSelectCategoryCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )
  if (tanMultipleSelectCategoryCodeTypes?.length > 0) {
    mo00039OneWayUserSelectType.value = tanMultipleSelectCategoryCodeTypes[0].value
    mo00039OneWayHistorySelectType.value = tanMultipleSelectCategoryCodeTypes[0].value
    const list: Mo00039Items[] = []
    for (const item of tanMultipleSelectCategoryCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    localOneway.mo00039OneWayHistorySelectType.items = list
    localOneway.mo00039OneWayUserSelectType.items = list
  }
}

/**
 * 初期プロジェクト設定
 */
const initSetting = () => {
  // 親画面.処理年月日が""の場合
  if (Or32281Const.DEFAULT.STR.EMPTY === local.processYmd) {
    // 担当ケアマネ選択
    tantoIconBtn.value = false
  } else {
    // 担当ケアマネ選択
    tantoIconBtn.value = true
  }
}

/**
 * 初期情報取得
 */
const init = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: IssueOrganizeSummaryPrintSettingsSelectInEntity = {
    sysCd: systemCommonsStore.getSystemCode,
    sysRyaku: systemCommonsStore.getSystemAbbreviation,
    houjinId: local.houjinId,
    shisetuId: local.shisetuId,
    svJigyoId: local.svJigyoId,
    shokuId: local.shokuId,
    userId: local.userId,
    tantoId: local.tantoId,
    sectionName: local.sectionName,
    index: Or32281Const.DEFAULT.INDEX,
    syubetsuId: local.syubetsuId,
    kojinhogoUsedFlg: Or32281Const.DEFAULT.KOJINHOGO_USED_FLG,
    sectionAddNo: Or32281Const.DEFAULT.SECTION_ADD_NO,
    gsyscd: systemCommonsStore.getSystemCode,
  } as IssueOrganizeSummaryPrintSettingsSelectInEntity
  const resp: IssueOrganizeSummaryPrintSettingsSelectOutEntity = await ScreenRepository.update(
    'issueOrganizeSummaryPrintSettingsUpdate',
    inputData
  )
  if (resp?.data) {
    // レスポンスパラメータ詳細
    localData.data = { ...resp?.data }

    // 期間管理フラグ
    kikanFlag.value = localData.data.kikanFlg

    // 担当ケアマネ
    mo01408Type.value.value = localData.data.tantoKnj

    const prtList: Mo01334Items[] = []
    for (const item of resp.data.prtList) {
      if (item) {
        prtList.push({
          id: item.prtNo,
          mo01337OnewayLedgerName: {
            value: item.prtTitle,
            unit: Or32281Const.DEFAULT.STR.EMPTY,
          } as Mo01337OnewayType,
          prnDate: item.prnDate === Or32281Const.DEFAULT.STR.TRUE,
          selectable: true,
          profile: item.profile,
          index: item.index,
          prtNo: item.prtNo,
          param03: item.param03,
          param04: item.param04,
          param07: item.param07,
          param08: item.param08,
          param09: item.param09,
          param10: item.param10,
        } as Mo01334Items)

        if (prtList.length === Or32281Const.DEFAULT.NUMBER.ONE) {
          mo00018TypeColor.value.modelValue = item.param05 === Or32281Const.DEFAULT.STR.TRUE
        }
      }
    }
    mo01334Oneway.value.items = prtList
    mo01334Type.value.value = prtList[0].id
    initFlag.value = true
    // アセスメント履歴情報を取得する
    await getPrintSettingsHistoryList()
    outputLedgerName(local.prtNo)
  }
  initSetting()
}

/**
 * アセスメント履歴一覧データ
 *
 * @param periodHistoryList - アセスメント履歴リスト
 */
const getHistoryData = (periodHistoryList: periodHistoryList[]) => {
  if (Or32281Const.DEFAULT.KIKAN_FLG_1 === kikanFlag.value) {
    const tempList: string[] = [] as string[]
    let list: OrX0128Items[] = []
    for (const item of periodHistoryList) {
      if (item) {
        const planPeriod =
          t('label.plan-period') +
          Or32281Const.DEFAULT.STR.SPLIT_COLON +
          item.startYmd +
          Or32281Const.DEFAULT.STR.SPLIT_TILDE +
          item.endYmd
        if (!tempList.includes(planPeriod)) {
          const historyList: OrX0128Items[] = []
          for (const data of periodHistoryList) {
            const dataPlanPeriod =
              t('label.plan-period') +
              Or32281Const.DEFAULT.STR.SPLIT_COLON +
              data.startYmd +
              Or32281Const.DEFAULT.STR.SPLIT_TILDE +
              data.endYmd
            if (planPeriod === dataPlanPeriod) {
              historyList.push({
                sel: data.sel,
                kss1Id: data.kss1Id,
                userid: data.userid,
                assDateYmd: data.createYmd,
                shokuinKnj: data.shokuinKnj,
                assTypeKnj: data.youshikiKnj,
                id: data.kss1Id,
                sc1Id: data.sc1Id,
                startYmd: data.startYmd,
                endYmd: data.endYmd,
              } as OrX0128Items)
            }
          }
          if (historyList.length > 0) {
            list.push({
              sc1Id: item.sc1Id,
              startYmd: item.startYmd,
              endYmd: item.endYmd,
              isPeriodManagementMergedRow: true,
              planPeriod:
                t('label.plan-period') +
                Or32281Const.DEFAULT.STR.SPLIT_COLON +
                item.startYmd +
                Or32281Const.DEFAULT.STR.SPLIT_TILDE +
                item.endYmd,
              id: Or32281Const.DEFAULT.STR.EMPTY,
            } as OrX0128Items)
            list = list.concat(historyList)
            tempList.push(planPeriod)
          }
        }
      }
    }
    list.forEach((item, index) => {
      item.id = String(++index)
    })
    orX0128OnewayModel.items = list
    // 画面.利用者一覧明細に親画面.利用者IDが存在する場合
    if (local.assessmentId) {
      // 親画面.アセスメントIDを対するレコードを選択状態にする
      orX0128OnewayModel.initSelectId = (
        list.findIndex((item) => item.raiId === local.assessmentId) + 1
      ).toString()
    }
  } else {
    const list: OrX0128Items[] = []
    for (const data of periodHistoryList) {
      if (data) {
        list.push({
          sel: data.sel,
          kss1Id: data.kss1Id,
          userid: data.userid,
          assDateYmd: data.createYmd,
          shokuinKnj: data.shokuinKnj,
          assTypeKnj: data.youshikiKnj,
          id: data.kss1Id,
          sc1Id: data.sc1Id,
          startYmd: data.startYmd,
          endYmd: data.endYmd,
        } as OrX0128Items)
      }
    }
    orX0128OnewayModel.items = list
  }
}

/**
 * 画面印刷設定内容を保存
 */
const save = async (): Promise<IFreeAssessmentFacePrintSettingsUpdateOutEntity> => {
  // バックエンドAPIから印刷設定情報保存
  const inputData: IFreeAssessmentFacePrintSettingsUpdateInEntity = {
    sysRyaku: Or32281Const.DEFAULT.SYS_RYAKU,
    sectionName: local.sectionName,
    gsyscd: systemCommonsStore.getSystemCode,
    shokuId: local.shokuId,
    houjinId: local.houjinId,
    shisetuId: local.shisetuId,
    svJigyoId: local.svJigyoId,
    index: local.index,
    sysIniInfo: local.sysIniInfo,
    prtList: localData.data.prtList,
  } as IFreeAssessmentFacePrintSettingsUpdateInEntity

  const resp: IFreeAssessmentFacePrintSettingsUpdateOutEntity = await ScreenRepository.select(
    'freeAssessmentFacePrintSettingsUpdate',
    inputData
  )
  return resp
}

/**
 * 「閉じるボタン」押下
 */
const close = async () => {
  await save()
  setState({
    isOpen: false,
    param: {} as Or32281Param,
  })
}

/**
 * 「PDFダウンロード」ボタン押下
 */
const pdfDownload = async () => {
  // 選択された帳票のプロファイルが””の場合
  if (local.profile === Or32281Const.DEFAULT.STR.EMPTY) {
    const dialogResult = await openErrorDialog(or21813.value.uniqueCpId, {
      dialogTitle: t('label.error'),
      // 「帳票の情報を読み込めません」
      dialogText: t('message.e-cmn-40172'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
    })
    if (dialogResult === Or32281Const.DEFAULT.MESSAGE_BTN_TYPE_YES) return
  }

  // 画面.空欄で印刷チェックボックスがオフの場合、且つ、利用者一覧明細にデータを選択しない
  if (local.userNoSelect) {
    const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
      dialogTitle: t('label.confirm'),
      // 「利用者が選択されていません。利用者を選択してください。」
      dialogText: t('message.i-cmn-11393'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
    })
    if (dialogResult === Or32281Const.DEFAULT.MESSAGE_BTN_TYPE_YES) return
  }

  // 画面.空欄で印刷チェックボックスがオフの場合、且つ、履歴一覧明細にデータを選択しない
  if (local.historyNoSelect) {
    const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
      dialogTitle: t('label.confirm'),
      // 「履歴が選択されていません。履歴を選択してください。」
      dialogText: t('message.i-cmn-11455'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
    })
    if (dialogResult === Or32281Const.DEFAULT.MESSAGE_BTN_TYPE_YES) return
  }

  // 印刷設定情報を保存する
  await save()

  // 利用者選択が「単一」
  if (mo00039OneWayUserSelectType.value === Or32281Const.DEFAULT.TANI) {
    // 履歴選択が「単一」
    if (mo00039OneWayHistorySelectType.value === Or32281Const.DEFAULT.TANI) {
      orX0117Oneway.type = Or32281Const.DEFAULT.STR.ONE

      //記入用シートを印刷するチェック入れるの場合
      if (mo00018TypeList.value.modelValue) {
        // 帳票側の処理を呼び出し、帳票レイアウトのみ印刷する
        const inputData: ICpnTucRaiAssReportSelectInEntity = {
          svJigyoKnj: local.svJigyoKnj,
          syscd: systemCommonsStore.getSystemCode ?? '',
          printSet: {
            shiTeiKubun: mo00039Type.value,
            shiTeiDate: mo00020Type.value.value?.split('/').join('-') ?? '',
          },
          printOption: {
            emptyFlg: String(mo00018TypeList.value.modelValue),
            kinyuAssType: mo00039OneWayAssessmentTypeType.value,
            colorFlg: String(mo00018TypeColor.value.modelValue),
          },
          printSubjectHistoryList: [],
        }
        // 帳票出力
        await reportOutput(local.reportId, inputData, reportOutputType.DOWNLOAD)
        return
      }

      // 履歴情報リストにデータを選択する場合
      if (!local.historyNoSelect) {
        // 印刷ダイアログ画面を開かずに、画面.印刷対象履歴リスト「利用者情報+履歴情報+出力帳票対象」を直接に利用して、帳票側の処理を呼び出す
        await reportOutputPdf()
        return
      }
    }

    // 履歴選択方法が「複数」
    else if (mo00039OneWayHistorySelectType.value === Or32281Const.DEFAULT.HUKUSUU) {
      // 履歴一覧に選択されある場合
      if (!local.historyNoSelect) {
        createReportOutputData(local.prtNo)
        return
      }
    }
  }

  // 利用者選択が「複数」
  if (mo00039OneWayUserSelectType.value === Or32281Const.DEFAULT.HUKUSUU) {
    // 利用者情報リストにデータを選択する場合
    orX0117Oneway.type = Or32281Const.DEFAULT.STR.ONE

    // API実行：印刷対象履歴リスト取得
    await PrintSettingsSubjectSelect()
  }

  // 印刷用情報リスト＞0件の場合
  if (orX0117Oneway.historyList.length > 0) {
    OrX0117Logic.state.set({
      uniqueCpId: orX0117.value.uniqueCpId,
      state: { isOpen: true },
    })
  }
}

/**
 * 印刷ダイアログ画面を開
 */
const reportOutputPdf = async () => {
  try {
    // API処理失敗時のシステムエラーダイアログを非表示にする
    systemCommonsStore.setSystemErrorDialogType('hide')

    const reportData: ICpnTucRaiAssReportSelectInEntity = {
      svJigyoKnj: local.svJigyoKnj,
      syscd: systemCommonsStore.getSystemCode,
      printSet: {
        shiTeiKubun: mo00039Type.value,
        shiTeiDate: mo00020Type.value.value ? mo00020Type.value.value.split('/').join('-') : '',
      } as PrintSetEntity,
      printOption: {
        emptyFlg: String(mo00018TypeList.value.modelValue),
        kinyuAssType: mo00039OneWayAssessmentTypeType.value,
      } as PrintOptionEntity,
      printSubjectHistoryList: [] as PrintSubjectHistoryEntity[],
    } as ICpnTucRaiAssReportSelectInEntity

    const choPrtList: ChoPrtEntity[] = []
    for (const item of localData.data.prtList) {
      if (item) {
        //  単一帳票
       if (local.prtNo === item.prtNo) {
          choPrtList.push({
            shokuId: systemCommonsStore.getStaffId,
            sysRyaku: systemCommonsStore.getSystemAbbreviation,
            section: local.sectionName,
            prtNo: item.prtNo,
            choPro: item.profile,
            sectionName: item.defPrtTitle,
            dwobject: item.dwobject,
            prtOrient: item.prtOrient,
            prtSize: item.prtSize,
            listTitle: item.listTitle,
            prtTitle: item.prtTitle,
            mTop: item.mtop,
            mBottom: item.mbottom,
            mLeft: item.mleft,
            mRight: item.mright,
            ruler: item.ruler,
            prndate: item.prnDate,
            prnshoku: item.prnshoku,
            serialFlg: item.serialFlg,
            modFlg: item.modFlg,
            secFlg: item.secFlg,
            param01: item.param01,
            param02: item.param02,
            param03: item.param03,
            param04: item.param04,
            param05: item.param05,
            param06: item.param06,
            param07: item.param07,
            param08: item.param08,
            param09: item.param09,
            param10: item.param10,
            serialHeight: item.serialHeight,
            serialPagelen: item.serialPagelen,
            hsjId: systemCommonsStore.getHoujinId,
            param11: item.param11,
            param12: item.param12,
            param13: item.param13,
            param14: item.param14,
            param15: item.param15,
            param16: item.param16,
            param17: item.param17,
            param18: item.param18,
            param19: item.param19,
            param20: item.param20,
            param21: item.param21,
            param22: item.param22,
            param23: item.param23,
            param24: item.param24,
            param25: item.param25,
            param26: item.param26,
            param27: item.param28,
            param28: item.param28,
            param29: item.param29,
            param30: item.param30,
            param31: item.param31,
            param32: item.param32,
            param33: item.param33,
            param34: item.param34,
            param35: item.param35,
            param36: item.param36,
            param37: item.param37,
            param38: item.param38,
            param39: item.param39,
            param40: item.param40,
            param41: item.param41,
            param42: item.param42,
            param43: item.param43,
            param44: item.param44,
            param45: item.param45,
            param46: item.param46,
            param47: item.param47,
            param48: item.param48,
            param49: item.param49,
            param50: item.param50,
            houjinId: systemCommonsStore.getHoujinId,
            shisetuId: systemCommonsStore.getShisetuId,
            svJigyoId: systemCommonsStore.getSvJigyoId,
            zoomRate: item.zoomRate,
            modifiedCnt: item.modifiedCnt,
          } as ChoPrtEntity)
        }
      }
    }

    // 印刷設定情報リストパラメータを作成
    reportData.printSubjectHistoryList.push({
      userId: local.userList.length > 0 ? local.userList[0].userId : Or32281Const.DEFAULT.STR.EMPTY,
      userName:
        local.userList.length > 0 ? local.userList[0].userName : Or32281Const.DEFAULT.STR.EMPTY,
      sc1Id:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].sc1Id as string)
          : Or32281Const.DEFAULT.STR.EMPTY,
      startYmd:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].startYmd as string)
          : Or32281Const.DEFAULT.STR.EMPTY,
      endYmd:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].endYmd as string)
          : Or32281Const.DEFAULT.STR.EMPTY,
      raiId:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].raiId as string)
          : Or32281Const.DEFAULT.STR.EMPTY,
      assType:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].assType as string)
          : Or32281Const.DEFAULT.STR.EMPTY,
      assDateYmd:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].assDateYmd as string)
          : Or32281Const.DEFAULT.STR.EMPTY,
      assShokuId:
        local.orX0128DetList.length > 0
          ? (local.orX0128DetList[0].assShokuId as string)
          : Or32281Const.DEFAULT.STR.EMPTY,
      result: Or32281Const.DEFAULT.STR.EMPTY,
      choPrtList: choPrtList,
    } as PrintSubjectHistoryEntity)
    // 帳票出力
    await reportOutput(local.reportId, reportData, reportOutputType.DOWNLOAD)
  } catch (e) {
    $log.debug('帳票の出力に失敗しました。', local.reportId, reportOutputType.DOWNLOAD, e)
  } finally {
    // API処理失敗時のシステムエラーダイアログの表示タイプをデフォルトに戻す
    systemCommonsStore.setSystemErrorDialogType('details')
  }
}

/**
 * 印刷設定情報リストを作成(利用者選択が「複数」、利用者情報リストにデータを選択する場合)
 */
const PrintSettingsSubjectSelect = async () => {
  // 印刷設定情報リストを作成する
  const inputData: IssueOrganizeSummaryPrintSettingsListHistorySelectInEntity = {
    kijunbi: mo00020TypeKijunbi.value.value ?? Or32281Const.DEFAULT.STR.EMPTY,
    svJigyoId: local.svJigyoId,
    userlist: local.userList,
  } as IssueOrganizeSummaryPrintSettingsListHistorySelectInEntity
  const resp: IssueOrganizeSummaryPrintSettingsListHistorySelectOutEntity =
    await ScreenRepository.select('issueOrganizeSummaryPrintSettingsListHistorySelect', inputData)

  const list: OrX0117History[] = []
  if (resp.data) {
    for (const data of resp.data.printSubjectHistoryList) {
      if (data) {
        // 利用者複数の場合
        if (Or32281Const.DEFAULT.HUKUSUU === mo00039OneWayUserSelectType.value) {
          // 印刷設定情報リストを作成
          const reportData: ICpnTucRaiAssReportSelectInEntity = {
            svJigyoKnj: local.svJigyoKnj,
            syscd: systemCommonsStore.getSystemCode,
            printSet: {
              shiTeiKubun: mo00039Type.value,
              shiTeiDate: mo00020Type.value.value
                ? mo00020Type.value.value.split('/').join('-')
                : '',
            } as PrintSetEntity,
            printOption: {
              emptyFlg: String(mo00018TypeList.value.modelValue),
              kinyuAssType: mo00039OneWayAssessmentTypeType.value,
            } as PrintOptionEntity,
            printSubjectHistoryList: [] as PrintSubjectHistoryEntity[],
          } as ICpnTucRaiAssReportSelectInEntity

          const choPrtList: ChoPrtEntity[] = []
          for (const item of localData.data.prtList) {
            if (item) {
              //  単一帳票
              if (local.prtNo === item.prtNo) {
                choPrtList.push({
                  shokuId: systemCommonsStore.getStaffId,
                  sysRyaku: systemCommonsStore.getSystemAbbreviation,
                  section: local.sectionName,
                  prtNo: item.prtNo,
                  choPro: item.profile,
                  sectionName: item.defPrtTitle,
                  dwobject: item.dwobject,
                  prtOrient: item.prtOrient,
                  prtSize: item.prtSize,
                  listTitle: item.listTitle,
                  prtTitle: item.prtTitle,
                  mTop: item.mtop,
                  mBottom: item.mbottom,
                  mLeft: item.mleft,
                  mRight: item.mright,
                  ruler: item.ruler,
                  prndate: item.prnDate,
                  prnshoku: item.prnshoku,
                  serialFlg: item.serialFlg,
                  modFlg: item.modFlg,
                  secFlg: item.secFlg,
                  param01: item.param01,
                  param02: item.param02,
                  param03: item.param03,
                  param04: item.param04,
                  param05: item.param05,
                  param06: item.param06,
                  param07: item.param07,
                  param08: item.param08,
                  param09: item.param09,
                  param10: item.param10,
                  serialHeight: item.serialHeight,
                  serialPagelen: item.serialPagelen,
                  hsjId: systemCommonsStore.getHoujinId,
                  param11: item.param11,
                  param12: item.param12,
                  param13: item.param13,
                  param14: item.param14,
                  param15: item.param15,
                  param16: item.param16,
                  param17: item.param17,
                  param18: item.param18,
                  param19: item.param19,
                  param20: item.param20,
                  param21: item.param21,
                  param22: item.param22,
                  param23: item.param23,
                  param24: item.param24,
                  param25: item.param25,
                  param26: item.param26,
                  param27: item.param28,
                  param28: item.param28,
                  param29: item.param29,
                  param30: item.param30,
                  param31: item.param31,
                  param32: item.param32,
                  param33: item.param33,
                  param34: item.param34,
                  param35: item.param35,
                  param36: item.param36,
                  param37: item.param37,
                  param38: item.param38,
                  param39: item.param39,
                  param40: item.param40,
                  param41: item.param41,
                  param42: item.param42,
                  param43: item.param43,
                  param44: item.param44,
                  param45: item.param45,
                  param46: item.param46,
                  param47: item.param47,
                  param48: item.param48,
                  param49: item.param49,
                  param50: item.param50,
                  houjinId: systemCommonsStore.getHoujinId,
                  shisetuId: systemCommonsStore.getShisetuId,
                  svJigyoId: systemCommonsStore.getSvJigyoId,
                  zoomRate: item.zoomRate,
                  modifiedCnt: item.modifiedCnt,
                } as ChoPrtEntity)
              }
            }
          }
          reportData.printSubjectHistoryList.push({
            userId: '',
            userName: data.userName,
            sc1Id: data.sc1Id,
            startYmd: '',
            endYmd: '',
            raiId: '',
            assType: '',
            assDateYmd: '',
            assShokuId: '',
            result: '',
            choPrtList: choPrtList,
          } as PrintSubjectHistoryEntity)
          list.push({
            reportId: local.reportId,
            outputType: reportOutputType.DOWNLOAD,
            reportData: reportData,
            userName: data.userName,
            historyDate: '',
            result: data.result,
          } as OrX0117History)
        }
      }
    }
  }
  orX0117Oneway.historyList = list
}
/**
 * 切替前の印刷設定を保存する
 *
 * @param selectId - 出力帳票ID
 */
const setBeforChangePrintData = (selectId: string) => {
  if (selectId) {
    for (const item of localData.data.prtList) {
      if (item) {
        if (selectId === item.sectionNo) {
          // 日付表示有無
          item.prnDate = mo00039Type.value
          //パラメータ03
          item.param03 = mo00018TypeChangeTitle.value ? '1' : '0'
          //パラメータ04
          item.param04 = mo00045TypetextInput.value.value
          //パラメータ07
          item.param07 = mo00018TypePrintUnderline.value ? '1' : '0'
          //パラメータ08
          item.param08 = mo00018TypeStartPosition.value ? '1' : '0'
          //パラメータ09
          item.param09 = mo00018TypeOutlookUnderline.value ? '1' : '0'
          //パラメータ10
          item.param10 = mo00018TypePrintAttachment.value ? '1' : '0'
          break
        }
      }
    }
  }
}

/**
 * 切替後の印刷設定を画面に設定する
 *
 * @param selectId - 出力帳票ID
 */
const setAfterChangePrintData = (selectId: string) => {
  if (selectId) {
    for (const item of localData.data.prtList) {
      if (item) {
        if (selectId === item.prtNo) {
          // 日付表示有無
          mo00039Type.value = item.prnDate
          //パラメータ03
          mo00018TypeChangeTitle.value.modelValue = item.param03 === '1' ? true : false
          //パラメータ04
          mo00045TypetextInput.value.value = item.param04
          //パラメータ07
          mo00018TypePrintUnderline.value.modelValue = item.param07 === '1' ? true : false
          //パラメータ08
          mo00018TypeStartPosition.value.modelValue = item.param08 === '1' ? true : false
          //パラメータ09
          mo00018TypeOutlookUnderline.value.modelValue = item.param09 === '1' ? true : false
          //パラメータ10
          mo00018TypePrintAttachment.value.modelValue = item.param10 === '1' ? true : false
          break
        }
      }
    }
  }
}

/**
 * 「出力帳票名」選択
 *
 * @param selectId - 出力帳票ID
 */
const outputLedgerName = (selectId: string) => {
  if (!selectId) {
    selectId = Or32281Const.DEFAULT.SECTION_NO
  }
  for (const item of mo01334Oneway.value.items) {
    if (item) {
      if (selectId === item.id && item.mo01337OnewayLedgerName) {
        mo01334Type.value.value = item.id

        // プロファイル
        local.profile = item.profile as string

        // 出力帳票名一覧に選択行番号
        local.index = item.index as string

        // 帳票番号
        local.prtNo = item.prtNo as string

        const title = item.mo01337OnewayLedgerName as Mo01337OnewayType
        mo00045TypeTitleInput.value.value = title.value
        // 帳票ID
        setReportId(item.id)
        break
      }
    }
  }
  // 帳票イニシャライズデータを取得する
  void getSectionInitializeData()
}

/**
 * 帳票ID設定
 *
 * @param sectionNo - 帳票番号
 */
const setReportId = (sectionNo: string) => {
  switch (sectionNo) {
    case Or32281Const.DEFAULT.STR.ONE:
      local.reportId = Or32281Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.ALL
      break
    case Or32281Const.DEFAULT.STR.TWO:
      local.reportId = Or32281Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.A
      break
    case Or32281Const.DEFAULT.STR.THREE:
      local.reportId = Or32281Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.B
      break
    case Or32281Const.DEFAULT.STR.FOUR:
      local.reportId = Or32281Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.C
      break
    case Or32281Const.DEFAULT.STR.FIVE:
      local.reportId = Or32281Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.D
      break
    case Or32281Const.DEFAULT.STR.SIX:
      local.reportId = Or32281Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.E
      break
    case Or32281Const.DEFAULT.STR.SEVEN:
      local.reportId = Or32281Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.F
      break
    case Or32281Const.DEFAULT.STR.EIGHT:
      local.reportId = Or32281Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.G
      break
    case Or32281Const.DEFAULT.STR.NINE:
      local.reportId = Or32281Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.H
      break
    case Or32281Const.DEFAULT.STR.TEN:
      local.reportId = Or32281Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.I
      break
    case Or32281Const.DEFAULT.STR.ELEVEN:
      local.reportId = Or32281Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.J
      break
    case Or32281Const.DEFAULT.STR.TWELVE:
      local.reportId = Or32281Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.K
      break
    case Or32281Const.DEFAULT.STR.THIRTEEN:
      local.reportId = Or32281Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.L
      break
    case Or32281Const.DEFAULT.STR.FOURTEEN:
      local.reportId = Or32281Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.M
      break
    case Or32281Const.DEFAULT.STR.FIFTEEN:
      local.reportId = Or32281Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.N
      break
    case Or32281Const.DEFAULT.STR.SIXTEEN:
      local.reportId = Or32281Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.O
      break
    case Or32281Const.DEFAULT.STR.SEVENTEEN:
      local.reportId = Or32281Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.P
      break
    case Or32281Const.DEFAULT.STR.EIGHTEEN:
      local.reportId = Or32281Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.Q
      break
    case Or32281Const.DEFAULT.STR.NINETEEN:
      local.reportId = Or32281Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.R
      break
    case Or32281Const.DEFAULT.STR.TEENTY:
      local.reportId = Or32281Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.S
      break
    case Or32281Const.DEFAULT.STR.TWENTI_ONE:
      local.reportId = Or32281Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.T
      break
    case Or32281Const.DEFAULT.STR.TWENTI_TWO:
      local.reportId = Or32281Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.U
      break
    case Or32281Const.DEFAULT.STR.TWENTI_THREE:
      local.reportId = Or32281Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.V
      break
    default:
      local.reportId = Or32281Const.DEFAULT.STR.EMPTY
      break
  }
}

/**
 * 印刷設定情報リストを作成
 *
 * @param prtNo - 帳票番号
 */
const createReportOutputData = (prtNo: string) => {
  const list: OrX0117History[] = []
  for (const orX0128DetData of local.orX0128DetList) {
    const reportData: ICpnTucRaiAssReportSelectInEntity = {
      svJigyoKnj: local.svJigyoKnj,
      syscd: systemCommonsStore.getSystemCode,
      printSet: {
        shiTeiKubun: mo00039Type.value,
        shiTeiDate: mo00020Type.value.value ? mo00020Type.value.value.split('/').join('-') : '',
      } as PrintSetEntity,
      printOption: {
        emptyFlg: String(mo00018TypeList.value.modelValue),
        kinyuAssType: mo00039OneWayAssessmentTypeType.value,
      } as PrintOptionEntity,
      printSubjectHistoryList: [] as PrintSubjectHistoryEntity[],
    } as ICpnTucRaiAssReportSelectInEntity

    const choPrtList: ChoPrtEntity[] = []
    for (const item of localData.data.prtList) {
      if (item) {
        if (prtNo === item.prtNo) {
          //  単一帳票
          choPrtList.push({
            shokuId: systemCommonsStore.getStaffId,
            sysRyaku: systemCommonsStore.getSystemAbbreviation,
            section: local.sectionName,
            prtNo: item.prtNo,
            choPro: item.profile,
            sectionName: item.defPrtTitle,
            dwobject: item.dwobject,
            prtOrient: item.prtOrient,
            prtSize: item.prtSize,
            listTitle: item.listTitle,
            prtTitle: item.prtTitle,
            mTop: item.mtop,
            mBottom: item.mbottom,
            mLeft: item.mleft,
            mRight: item.mright,
            ruler: item.ruler,
            prndate: item.prnDate,
            prnshoku: item.prnshoku,
            serialFlg: item.serialFlg,
            modFlg: item.modFlg,
            secFlg: item.secFlg,
            param01: item.param01,
            param02: item.param02,
            param03: item.param03,
            param04: item.param04,
            param05: item.param05,
            param06: item.param06,
            param07: item.param07,
            param08: item.param08,
            param09: item.param09,
            param10: item.param10,
            serialHeight: item.serialHeight,
            serialPagelen: item.serialPagelen,
            hsjId: systemCommonsStore.getHoujinId,
            param11: item.param11,
            param12: item.param12,
            param13: item.param13,
            param14: item.param14,
            param15: item.param15,
            param16: item.param16,
            param17: item.param17,
            param18: item.param18,
            param19: item.param19,
            param20: item.param20,
            param21: item.param21,
            param22: item.param22,
            param23: item.param23,
            param24: item.param24,
            param25: item.param25,
            param26: item.param26,
            param27: item.param28,
            param28: item.param28,
            param29: item.param29,
            param30: item.param30,
            param31: item.param31,
            param32: item.param32,
            param33: item.param33,
            param34: item.param34,
            param35: item.param35,
            param36: item.param36,
            param37: item.param37,
            param38: item.param38,
            param39: item.param39,
            param40: item.param40,
            param41: item.param41,
            param42: item.param42,
            param43: item.param43,
            param44: item.param44,
            param45: item.param45,
            param46: item.param46,
            param47: item.param47,
            param48: item.param48,
            param49: item.param49,
            param50: item.param50,
            houjinId: systemCommonsStore.getHoujinId,
            shisetuId: systemCommonsStore.getShisetuId,
            svJigyoId: systemCommonsStore.getSvJigyoId,
            zoomRate: item.zoomRate,
            modifiedCnt: item.modifiedCnt,
          } as ChoPrtEntity)
        }
      }
    }

    // 印刷設定情報リストパラメータを作成
    reportData.printSubjectHistoryList.push({
      userId: local.userList.length > 0 ? local.userList[0].userId : Or32281Const.DEFAULT.STR.EMPTY,
      userName:
        local.userList.length > 0 ? local.userList[0].userName : Or32281Const.DEFAULT.STR.EMPTY,
      sc1Id: orX0128DetData.sc1Id as string,
      startYmd: orX0128DetData.startYmd as string,
      endYmd: orX0128DetData.endYmd as string,
      raiId: orX0128DetData.raiId as string,
      assType: orX0128DetData.assType as string,
      assDateYmd: orX0128DetData.assDateYmd as string,
      assShokuId: orX0128DetData.assShokuId as string,
      result: Or32281Const.DEFAULT.STR.EMPTY,
      choPrtList: choPrtList,
    } as PrintSubjectHistoryEntity)
    list.push({
      reportId: local.reportId,
      outputType: reportOutputType.DOWNLOAD,
      reportData: reportData,
      userName:
        local.userList.length > 0 ? local.userList[0].userName : Or32281Const.DEFAULT.STR.EMPTY,
      historyDate: orX0128DetData.assDateYmd as string,
      result: Or32281Const.DEFAULT.STR.EMPTY,
    } as OrX0117History)
  }
  orX0117Oneway.historyList = list
}

/**
 * 帳票イニシャライズデータを取得する
 */
const getSectionInitializeData = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity = {
    profile: local.profile,
    gsyscd: systemCommonsStore.getSystemCode,
    shokuId: systemCommonsStore.getStaffId,
    kojinhogoUsedFlg: Or32281Const.DEFAULT.KOJINHOGO_USED_FLG,
    sectionAddNo: Or32281Const.DEFAULT.SECTION_ADD_NO,
  } as IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity
  const resp: IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateOutEntity =
    await ScreenRepository.update('freeAssessmentFacePrintSettingsPrtNoChangeUpdate', inputData)
  if (resp.data) {
    local.sysIniInfo = resp.data.sysIniInfo
  }
}

/**
 * 印刷設定履歴リスト取得
 */
const getPrintSettingsHistoryList = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: IssueOrganizeSummaryPrintSettingsHistorySelectInEntity = {
    svJigyoId: local.svJigyoId,
    userId: local.selectUserId,
    kikanFlg: kikanFlag.value,
  } as IssueOrganizeSummaryPrintSettingsHistorySelectInEntity
  const resp: IssueOrganizeSummaryPrintSettingsHistorySelectOutEntity =
    await ScreenRepository.select('issueOrganizeSummaryPrintSettingsHistorySelect', inputData)
  if (resp.data) {
    // アセスメント履歴一覧データ
    getHistoryData(resp.data.periodHistoryList)
  }
}

/**
 * 画面ボタン活性非活性設定
 */
const btnItemSetting = () => {
  // 利用者選択方法が「単一」の場合
  if (Or32281Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
    // 基準日を非表示にする
    // 履歴選択を活性表示にする
    kijunbiFlag.value = false

    // 履歴選択方法が「単一」の場合
    if (Or32281Const.DEFAULT.TANI === mo00039OneWayHistorySelectType.value) {
      // 記入用シートを印刷するチェックボックス表示
      localOneway.mo00018OneWayPrintTheForm.disabled = false
    }
    // 以外の場合
    else {
      // チェックオフとなって
      mo00018TypeList.value.modelValue = false
      // 記入用シートを印刷するを非活性表示にする
      localOneway.mo00018OneWayPrintTheForm.disabled = true
    }

    // 記入用シートを印刷するが「チェックオンの場合
    if (mo00018TypeList.value.modelValue) {
      localOneway.mo00039OneWayAssessmentType.disabled = false
    }
    // 以外の場合
    else {
      localOneway.mo00039OneWayAssessmentType.disabled = true
    }
  }
  // 以外の場合
  else {
    // 基準日を活性表示にする
    // 履歴選択を非表示にする
    kijunbiFlag.value = true

    // チェックオフとなって
    mo00018TypeList.value.modelValue = false
    // 記入用シートを印刷するを非活性表示にする
    localOneway.mo00018OneWayPrintTheForm.disabled = true
    localOneway.mo00039OneWayAssessmentType.disabled = true
  }

  // 履歴選択方法が「単一」の場合
  if (Or32281Const.DEFAULT.TANI === mo00039OneWayHistorySelectType.value) {
    // 利用者選択方法が「単一」の場合
    if (Or32281Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
      // 記入用シートを印刷するチェックボックス表示
      localOneway.mo00018OneWayPrintTheForm.disabled = false
    }
    // 以外の場合
    else {
      // チェックオフとなって
      mo00018TypeList.value.modelValue = false
      // 記入用シートを印刷するを非活性表示にする
      localOneway.mo00018OneWayPrintTheForm.disabled = true
    }
  }
  // 以外の場合
  else {
    // チェックオフとなって
    mo00018TypeList.value.modelValue = false
    // 記入用シートを印刷するを非活性表示にする
    localOneway.mo00018OneWayPrintTheForm.disabled = true

    // 記入用シートを印刷するをチェックオフにする
    mo00018TypeList.value.modelValue = false
    // 記入用シート方式を非活性表示にする
    localOneway.mo00039OneWayAssessmentType.disabled = true
  }

  // 履歴一覧セクション
  if (Or32281Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
    userCols.value = 4
    mo01334TypeHistoryFlag.value = true
  } else {
    userCols.value = 12
    mo01334TypeHistoryFlag.value = false
  }

  // 担当ケアマネ選択アイコンボタン非活性/活性設定
  const kkjTantoFlg: string =
    cmnRouteCom.getInitialSettingMaster()?.kkjTantoFlg ?? Or32281Const.DEFAULT.STR.EMPTY
  // 共通情報.担当ケアマネ設定フラグ > 0、且つ、共通情報.担当者IDが0以外の場合
  if (
    systemCommonsStore.getManagerId !== Or32281Const.DEFAULT.STR.ZERO &&
    parseInt(kkjTantoFlg) > Or32281Const.DEFAULT.NUMBER.ZERO
  ) {
    // 非活性
    localOneway.mo01408OneWayType.disabled = true
  }
  // その他場合
  else {
    // 活性
    localOneway.mo01408OneWayType.disabled = false
  }
}

/**
 * 確認ダイアログ表示
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openConfirmDialog = async (
  uniqueCpId: string,
  state: Or21814OnewayType
): Promise<Or32281MsgBtnType> => {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result: Or32281MsgBtnType = Or32281Const.DEFAULT.MESSAGE_BTN_TYPE_YES

        if (event?.firstBtnClickFlg) {
          result = Or32281Const.DEFAULT.MESSAGE_BTN_TYPE_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or32281Const.DEFAULT.MESSAGE_BTN_TYPE_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = Or32281Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL
        }
        if (event?.closeBtnClickFlg) {
          result = Or32281Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * エラーダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openErrorDialog = async (
  uniqueCpId: string,
  state: Or21813StateType
): Promise<Or32281MsgBtnType> => {
  Or21813Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(uniqueCpId)

        let result = Or32281Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL as Or32281MsgBtnType

        if (event?.firstBtnClickFlg) {
          result = Or32281Const.DEFAULT.MESSAGE_BTN_TYPE_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or32281Const.DEFAULT.MESSAGE_BTN_TYPE_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = Or32281Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL
        }
        if (event?.closeBtnClickFlg) {
          result = Or32281Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL
        }

        // エラーダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 「出力帳票名」選択
 */
watch(
  () => mo01334Type.value.value,
  (newValue, oldValue) => {
    setBeforChangePrintData(oldValue)
    setAfterChangePrintData(newValue)
    outputLedgerName(newValue)

    // 日付印刷区分が2の場合
    if (Or32281Const.DEFAULT.STR.TWO === mo00039Type.value) {
      // 指定日を活性表示にする。
      mo00020Flag.value = true
    } else {
      // 指定日を非表示にする。
      mo00020Flag.value = false
    }
  }
)

/**
 * 「日付印刷区分」ラジオボタン押下
 */
watch(
  () => mo00039Type.value,
  (newValue) => {
    if (Or32281Const.DEFAULT.STR.TWO === newValue) {
      // 指定日を活性表示にする
      mo00020Flag.value = true
    } else {
      // 指定日を非表示にする
      mo00020Flag.value = false
    }
  }
)

watch(
  () => mo00018TypeList.value.modelValue,
  (newVal) => {
    // 備考の下線を印刷しない
    localOneway.mo00018OneWayPrintUnderline.disabled = newVal
    // 備考の開始位置を調整しない
    localOneway.mo00018OneWayStartPosition.disabled = newVal
    // 見通しの下線を印刷しない
    localOneway.mo00018OneWayOutlookUnderline.disabled = newVal
    // 別紙に印刷する
    localOneway.mo00018OneWayPrintAttachment.disabled = newVal
  }
)

/**
 * 「利用者選択方法」ラジオボタン選択
 */
watch(
  () => mo00039OneWayUserSelectType.value,
  (newValue) => {
    // 画面ボタン活性非活性設定
    btnItemSetting()

    // 利用者選択方法が「単一」の場合
    if (Or32281Const.DEFAULT.TANI === newValue) {
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.TANI
      orX0130Oneway.tableStyle = 'width: 210px'

      if (OrX0130Logic.event.get(orX0130.value.uniqueCpId)) {
        if (
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList &&
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList.length > 0
        ) {
          local.userList = []
          for (const item of OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList) {
            if (item) {
              local.userList.push({
                userId: item.userId,
                userName: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
              } as UserEntity)
            }
          }
        } else {
          local.userList = []
        }
      } else {
        local.userList = []
      }
    } else {
      // 復元
      orX0117Oneway.type = Or32281Const.DEFAULT.STR.ONE
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.HUKUSUU
      orX0130Oneway.tableStyle = 'width: 430px'

      if (OrX0130Logic.event.get(orX0130.value.uniqueCpId)) {
        if (
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList &&
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList.length > 0
        ) {
          local.userList = []
          for (const item of OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList) {
            if (item) {
              local.userList.push({
                userId: item.userId,
                userName: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
              } as UserEntity)
            }
          }
        } else {
          local.userList = []
        }
      } else {
        local.userList = []
      }
    }

    // 利用者一覧明細に親画面.利用者IDが存在する場合
    if (local.userId) {
      // 利用者IDを対するレコードを選択状態にする
      orX0130Oneway.userId = local.userId
    }
  }
)

/**
 * 「履歴選択方法」ラジオボタン押下
 */
watch(
  () => mo00039OneWayHistorySelectType.value,
  (newValue) => {
    // 画面ボタン活性非活性設定
    btnItemSetting()

    // 履歴選択方法が「単一」の場合
    if (Or32281Const.DEFAULT.TANI === newValue) {
      if (Or32281Const.DEFAULT.KIKAN_FLG_0 === kikanFlag.value) {
        if (mo01334TypeHistory1.value.value) {
          local.historyNoSelect = false
        } else {
          local.historyNoSelect = true
        }
      } else {
        if (OrX0128Logic.event.get(orX0128.value.uniqueCpId)) {
          if (
            OrX0128Logic.event.get(orX0128.value.uniqueCpId)!.orX0128DetList &&
            OrX0128Logic.event.get(orX0128.value.uniqueCpId)!.orX0128DetList.length > 0
          ) {
            local.historyNoSelect = false
          } else {
            local.historyNoSelect = true
          }
        } else {
          local.historyNoSelect = true
        }
      }
      orX0128OnewayModel.singleFlg = OrX0128Const.DEFAULT.TANI
    } else {
      if (Or32281Const.DEFAULT.KIKAN_FLG_0 === kikanFlag.value) {
        if (mo01334TypeHistory3.value.values.length > 0) {
          local.historyNoSelect = false
        } else {
          local.historyNoSelect = true
        }
      } else {
        if (OrX0128Logic.event.get(orX0128.value.uniqueCpId)) {
          if (
            OrX0128Logic.event.get(orX0128.value.uniqueCpId)!.orX0128DetList &&
            OrX0128Logic.event.get(orX0128.value.uniqueCpId)!.orX0128DetList.length > 0
          ) {
            local.historyNoSelect = false
          } else {
            local.historyNoSelect = true
          }
        } else {
          local.historyNoSelect = true
        }
      }
      orX0128OnewayModel.singleFlg = OrX0128Const.DEFAULT.HUKUSUU
      orX0117Oneway.type = Or32281Const.DEFAULT.STR.ZERO
    }
  }
)

/**
 * 記入用シート方式ラジオボタン監視
 */
watch(
  () => mo00018TypeList.value.modelValue,
  () => {
    // 画面ボタン活性非活性設定
    btnItemSetting()
  }
)

/**
 * 「履歴選択」の監視
 */
watch(
  () => OrX0128Logic.event.get(orX0128.value.uniqueCpId),
  (newValue) => {
    // 画面ボタン活性非活性設定
    btnItemSetting()
    if (newValue) {
      if (newValue.historyDetClickFlg) {
        local.orX0128DetList = newValue.orX0128DetList
        if (newValue.orX0128DetList.length > 0) {
          local.historyNoSelect = false

          for (const item of newValue.orX0128DetList) {
            if (item) {
              // 記入用シート方式ラジオボタン(デフォルト値の設定)
              if (!mo00039OneWayAssessmentTypeType.value) {
                mo00039OneWayAssessmentTypeType.value = item.assType as string
              }
            }
          }
        } else {
          local.historyNoSelect = true
        }
      } else {
        local.historyNoSelect = true
      }
    }
  }
)

/**
 * 「利用者選択」の監視
 */
watch(
  () => OrX0130Logic.event.get(orX0130.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.clickFlg) {
      if (newValue.userList.length > 0) {
        local.userNoSelect = false
        local.selectUserId = newValue.userList[0].id

        local.userList = []
        for (const item of newValue.userList) {
          if (item) {
            local.userList.push({
              userId: item.userId,
              userName: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
            } as UserEntity)
          }
        }

        // 利用者選択方法が「単一」の場合
        if (Or32281Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
          if (initFlag.value) {
            // アセスメント履歴情報を取得する
            await getPrintSettingsHistoryList()
          }
        }
        // 利用者選択方法が「複数」の場合
        else if (Or32281Const.DEFAULT.HUKUSUU === mo00039OneWayUserSelectType.value) {
          // 基盤コンポーネントの再作成#147472 利用者一覧明細に前回選択された利用者が選択状態になる
          createReportOutputData(local.prtNo)
        }
      } else {
        local.userNoSelect = true
        local.userList = []
      }
    } else {
      local.userNoSelect = true
      local.userList = []
    }
  }
)

/**
 * 警告ダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openWarningDialog = (uniqueCpId: string, state: Or21815StateType) => {
  Or21815Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(uniqueCpId)

        let result = 'no'

        if (event?.thirdBtnClickFlg) {
          result = 'no'
        }
        if (event?.closeBtnClickFlg) {
          result = 'no'
        }

        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 帳票名が空の場合、ポップアップを表示
 */
const handleBlur = async () => {
  if (mo00045TypeTitleInput.value.value === '') {
    await openWarningDialog(or21815.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.warning'),
      // ダイアログテキスト
      dialogText: t('message.w-cmn-20845'),
      firstBtnType: 'blank',
      secondBtnType: 'blank',
      thirdBtnType: 'normal1',
      thirdBtnLabel: t('btn.yes'),
    })
    mo00045TypeTitleInput.value.value = currentTile.value
  }
}

/**
 * 「担当ケアマネプルダウン」選択の監視
 */
watch(
  () => mo01408Type.value.value,
  (newValue) => {
    // TODO
    console.log(newValue)
  }
)

/**
 * イベントリスナーの解除
 */
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    if (!newValue) {
      await save()
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        class="or32281_row"
        no-gutter
      >
        <c-v-col
          cols="12"
          sm="2"
          class="or32281_table"
        >
          <base-mo-01334
            v-model="mo01334Type"
            :oneway-model-value="mo01334Oneway"
            class="list-wrapper"
            style="text-align: center"
          >
            <!-- 帳票 -->
            <template #[`item.ledgerName`]="{ item }">
              <!-- 分子：一覧専用ラベル（文字列型） -->
              <base-mo01337 :oneway-model-value="item.mo01337OnewayLedgerName" />
            </template>
            <!-- ページングを非表示 -->
            <template #bottom />
          </base-mo-01334>
        </c-v-col>
        <c-v-col
          cols="12"
          sm="3"
          class="content_center"
        >
          <c-v-row
            class="has-border-bottom mt-2 ml-2 mr-2"
            style="align-items: center"
          >
            <base-mo00615 :oneway-model-value="localOneway.mo00615OneWayTypeTitle"></base-mo00615>

            <base-mo00045
              v-model="mo00045TypeTitleInput"
              :oneway-model-value="localOneway.mo00045OnewayTitleInput"
              @blur="handleBlur"
            ></base-mo00045>
          </c-v-row>
          <c-v-divider></c-v-divider>
          <c-v-row
            no-gutter
            class="customCol or32281_row"
          >
            <c-v-col
              cols="12"
              sm="7"
              style="padding-left: 0px; padding-right: 0px"
            >
              <base-mo00039
                v-model="mo00039Type"
                :oneway-model-value="localOneway.mo00039OneWay"
              >
              </base-mo00039>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="5"
              style="padding-left: 0px; padding-right: 8px"
            >
              <base-mo00020
                v-if="mo00020Flag"
                v-model="mo00020Type"
                :oneway-model-value="localOneway.mo00020OneWay"
              >
              </base-mo00020>
            </c-v-col>
          </c-v-row>
          <!-- 印刷オプション -->
          <c-v-row
            no-gutter
            class="printerOption customCol or32281_row"
          >
            <c-v-col
              cols="12"
              sm="12"
            >
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayPrinterOption"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
          <!-- 敬称を変更する -->
          <c-v-row
            no-gutter
            class="customCol or32281_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0 d-flex"
            >
              <base-mo00018
                v-model="mo00018TypeChangeTitle"
                :oneway-model-value="localOneway.mo00018OneWayChangeHonorific"
              ></base-mo00018>
              <base-mo-01339
                class="honorifics-tltle mr-2"
                :oneway-model-value="mo013391Oneway"
              />
              <base-mo-00045
                v-model="mo00045TypetextInput"
                :oneway-model-value="localOneway.mo00045OneWayChangeHonorific"
                :disabled="!mo00018TypeChangeTitle.modelValue"
                class="text-center"
              />
              <base-mo-01339
                class="honorifics-tltle"
                :oneway-model-value="mo013392Oneway"
              />
            </c-v-col>
          </c-v-row>
          <!-- 記入用シートを印刷する -->
          <c-v-row
            no-gutter
            class="customCol or32281_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0 d-flex"
            >
              <base-mo00018
                v-model="mo00018TypeList"
                :oneway-model-value="localOneway.mo00018OneWayPrintTheForm"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>
          <!-- 備考の下線を印刷しない -->
          <c-v-row
            no-gutter
            class="customCol or32281_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0 d-flex"
            >
              <base-mo00018
                v-model="mo00018TypePrintUnderline"
                :oneway-model-value="localOneway.mo00018OneWayPrintUnderline"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>
          <!-- 備考の開始位置を調整しない -->
          <c-v-row
            no-gutter
            class="customCol or32281_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0 d-flex"
            >
              <base-mo00018
                v-model="mo00018TypeStartPosition"
                :oneway-model-value="localOneway.mo00018OneWayStartPosition"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>
          <!-- '見通しの下線を印刷しない -->
          <c-v-row
            no-gutter
            class="customCol or32281_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0 d-flex"
            >
              <base-mo00018
                v-model="mo00018TypeOutlookUnderline"
                :oneway-model-value="localOneway.mo00018OneWayOutlookUnderline"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>
          <!-- 別紙に印刷する -->
          <c-v-row
            no-gutter
            class="customCol or32281_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              class="pa-0 d-flex"
            >
              <base-mo00018
                v-model="mo00018TypePrintAttachment"
                :oneway-model-value="localOneway.mo00018OneWayPrintAttachment"
              >
              </base-mo00018>
            </c-v-col>
          </c-v-row>
          <!-- 備考ラベル -->
          <c-v-row
            no-gutter
            class="customCol or32281_row"
          >
            <c-v-col
              no-gutter
              cols="12"
              class="pt-0 pl-3"
            >
              <base-mo-01338
                v-if="Or32281Const.DEFAULT.KIKAN_FLG_0 === kikanFlag"
                :oneway-model-value="localOneway.mo01338OneWayLabel"
              />
            </c-v-col>
          </c-v-row>
        </c-v-col>
        <!-- ボタン切替部分 -->
        <c-v-col
          cols="12"
          sm="7"
          class="content_center"
        >
          <c-v-row
            class="or32281_row"
            no-gutter
            style="align-items: center; padding-bottom: 8px"
          >
            <c-v-col
              cols="12"
              sm="4"
              style="padding: 0px"
            >
              <c-v-row
                no-gutter
                class="or32281_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                >
                  <base-mo01338
                    :oneway-model-value="localOneway.mo01338OneWayPrinterUserSelect"
                    style="background-color: transparent"
                  >
                  </base-mo01338>
                </c-v-col>
              </c-v-row>
              <c-v-row
                no-gutter
                class="or32281_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  style="padding: 0px"
                >
                  <base-mo00039
                    v-model="mo00039OneWayUserSelectType"
                    :oneway-model-value="localOneway.mo00039OneWayUserSelectType"
                  >
                  </base-mo00039>
                </c-v-col>
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-if="kijunbiFlag"
              cols="12"
              sm="4"
              style="padding: 0px"
            >
              <c-v-row
                no-gutter
                class="or32281_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                >
                  <base-mo00615 :oneway-model-value="localOneway.mo00615OneWayType"> </base-mo00615>
                </c-v-col>
              </c-v-row>
              <c-v-row
                no-gutter
                class="or32281_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  style="padding: 0px"
                >
                  <base-mo00020
                    v-model="mo00020TypeKijunbi"
                    :oneway-model-value="localOneway.mo00020KijunbiOneWay"
                  />
                </c-v-col>
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-else
              cols="12"
              sm="4"
              style="padding: 0px"
            >
              <c-v-row
                no-gutter
                class="or32281_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                >
                  <base-mo01338
                    :oneway-model-value="localOneway.mo01338OneWayPrinterHistorySelect"
                    style="background-color: transparent"
                  >
                  </base-mo01338>
                </c-v-col>
              </c-v-row>
              <c-v-row
                no-gutter
                class="or32281_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  style="padding: 0px"
                >
                  <base-mo00039
                    v-model="mo00039OneWayHistorySelectType"
                    :oneway-model-value="localOneway.mo00039OneWayHistorySelectType"
                  >
                  </base-mo00039>
                </c-v-col>
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-if="tantoIconBtn"
              cols="12"
              sm="4"
              style="padding: 0px"
            >
              <c-v-row
                no-gutter
                class="or32281_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                >
                  <base-mo00615 :oneway-model-value="localOneway.mo00615OneWayCareManagerInCharge">
                  </base-mo00615>
                </c-v-col>
              </c-v-row>
              <c-v-row
                no-gutter
                class="or32281_row"
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  style="padding-top: 0px; padding-bottom: 0px"
                >
                  <base-mo01408
                    v-model="mo01408Type"
                    :oneway-model-value="localOneway.mo01408OneWayType"
                  >
                  </base-mo01408>
                </c-v-col>
              </c-v-row>
            </c-v-col>
          </c-v-row>
          <c-v-divider></c-v-divider>
          <c-v-row
            class="or32281_row"
            no-gutter
          >
            <c-v-col :cols="userCols">
              <!-- 印刷設定画面利用者一覧 -->
              <g-custom-or-x-0130
                v-if="orX0130Oneway.selectMode && initFlag"
                v-bind="orX0130"
                :oneway-model-value="orX0130Oneway"
              >
              </g-custom-or-x-0130>
            </c-v-col>
            <c-v-col
              v-if="mo01334TypeHistoryFlag && initFlag"
              cols="8"
              style="overflow-x: auto"
            >
              <div style="height: 520px">
                <!-- 計画期間＆履歴一覧 -->
                <g-custom-or-x-0128
                  v-if="
                    Or32281Const.DEFAULT.KIKAN_FLG_1 === kikanFlag && orX0128OnewayModel.singleFlg
                  "
                  v-bind="orX0128"
                  :oneway-model-value="orX0128OnewayModel"
                ></g-custom-or-x-0128>
                <!-- 履歴選択方法が「複数」且つ 期間管理フラグが「管理しない」の場合 -->
                <base-mo-01334
                  v-if="
                    Or32281Const.DEFAULT.KIKAN_FLG_0 === kikanFlag &&
                    Or32281Const.DEFAULT.HUKUSUU === mo00039OneWayHistorySelectType
                  "
                  v-model="mo01334TypeHistory3"
                  :oneway-model-value="mo01334OnewayHistory3"
                  class="list-wrapper"
                >
                  <!-- 基準日 -->
                  <template #[`item.kijunbi`]="{ item }">
                    <!-- 分子：一覧専用ラベル（文字列型） -->
                    <base-mo01337 :oneway-model-value="item.mo01337OnewayKijunbi" />
                  </template>
                  <!-- 作成者 -->
                  <template #[`item.author`]="{ item }">
                    <!-- 分子：一覧専用ラベル（文字列型） -->
                    <base-mo01337 :oneway-model-value="item.mo01337OnewayAuthor" />
                  </template>
                  <!-- アセスメント種別 -->
                  <template #[`item.assessmentKind`]="{ item }">
                    <!-- 分子：一覧専用ラベル（文字列型） -->
                    <base-mo01337 :oneway-model-value="item.mo01337OnewayAssessmentKind" />
                  </template>
                  <!-- ページングを非表示 -->
                  <template #bottom />
                </base-mo-01334>
                <!-- 初期表示(期間管理フラグが「管理しない」の場合) -->
                <base-mo-01334
                  v-if="
                    Or32281Const.DEFAULT.KIKAN_FLG_0 === kikanFlag &&
                    Or32281Const.DEFAULT.TANI === mo00039OneWayHistorySelectType
                  "
                  v-model="mo01334TypeHistory1"
                  :oneway-model-value="mo01334OnewayHistory1"
                  class="list-wrapper"
                >
                  <!-- 基準日 -->
                  <template #[`item.kijunbi`]="{ item }">
                    <!-- 分子：一覧専用ラベル（文字列型） -->
                    <base-mo01337 :oneway-model-value="item.mo01337OnewayKijunbi" />
                  </template>
                  <!-- 作成者 -->
                  <template #[`item.author`]="{ item }">
                    <!-- 分子：一覧専用ラベル（文字列型） -->
                    <base-mo01337 :oneway-model-value="item.mo01337OnewayAuthor" />
                  </template>
                  <!-- アセスメント種別 -->
                  <template #[`item.assessmentKind`]="{ item }">
                    <!-- 分子：一覧専用ラベル（文字列型） -->
                    <base-mo01337 :oneway-model-value="item.mo01337OnewayAssessmentKind" />
                  </template>
                  <!-- ページングを非表示 -->
                  <template #bottom />
                </base-mo-01334>
              </div>
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          @click="close"
        >
        </base-mo00611>
        <!-- PDFダウンロードボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          class="mx-2"
          @click="pdfDownload()"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- 印刷設定帳票出力状態リスト画面 -->
  <g-custom-or-x-0117
    v-if="showDialogOrX0117"
    v-bind="orX0117"
    :oneway-model-value="orX0117Oneway"
  ></g-custom-or-x-0117>
  <!-- Or21813:有機体:エラーダイアログ -->
  <g-base-or21813 v-bind="or21813" />
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814 v-bind="or21814" />
  <g-base-or21815 v-bind="or21815"> </g-base-or21815>
</template>
<style>
.or32281_content {
  padding: 0px !important;
}

.or32281_gokeiClass {
  label {
    color: #ffffff;
  }
}
</style>
<style scoped lang="scss">
:deep(.v-table__wrapper) {
  overflow-x: auto !important;
}

.or32281_table {
  padding: 0px !important;
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.or32281_row {
  margin: 0px !important;
}

.content_center {
  border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  padding: 0px !important;

  .printerOption {
    background-color: #edf1f7;
  }

  :deep(.label-area-style) {
    display: none;
  }

  .customCol {
    margin-left: 0px;
    margin-right: 0px;
  }
}
.honorifics-tltle {
  display: flex;
  align-items: center;
}
.has-border-bottom {
  margin-bottom: 8px;
}
</style>
