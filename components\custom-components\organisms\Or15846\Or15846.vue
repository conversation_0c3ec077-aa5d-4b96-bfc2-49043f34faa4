<script setup lang="ts">
/**
 * Or15846:有機体:年間行事
 * GUI00939_月間・年間表
 *
 * @description
 * 週間計画パターンタイトルタブ：タイトル
 *
 * <AUTHOR>
 */
import { reactive, computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or51775Const } from '../Or51775/Or51775.constants'
import { Or51775Logic } from '../Or51775/Or51775.logic'
import { Or15846Const } from './Or15846.constants'
import {
  useScreenTwoWayBind,
  useGyoumuCom,
  useScreenOneWayBind,
  useSetupChildProps,
} from '#imports'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00046OnewayType, Mo00046Type } from '~/types/business/components/Mo00046Type'
import type { Or51775ConfirmType } from '~/components/custom-components/organisms/Or51775/Or51775.type'
import type { Or15846OnewayType } from '~/types/cmn/business/components/Or15846Type'
import type { Or51775OnewayType } from '~/types/cmn/business/components/Or51775Type'

const { t } = useI18n()

const gyoumuCom = useGyoumuCom()
/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
}

const props = defineProps<Props>()

const local = reactive({ isCopyMode: false })
const localOneway = reactive({
  or15846Oneway: {} as Or15846OnewayType,
  //月 ラベル
  mo01337Oneway: {
    value: '',
  } as Mo01337OnewayType,
  //年間行事の実施に係る総合の支援の視点ラベル
  mo00615ViewpointOneway: {
    itemLabelFontWeight: 'bold',
    itemLabel: t('label.yearly-act-view-point'),
    customClass: new CustomClass({ outerClass: 'mr-2', labelClass: 'ma-1' }),
  } as Mo00615OnewayType,
  //年間行事の実施に係る総合の支援の視点ボタン
  mo00009ViewpointOneway: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,
  //縦表示ラベル
  mo00615VerticalDisplayOneway: {
    itemLabelFontWeight: 'bold',
    itemLabel: t('label.vertical-display'),
    customClass: new CustomClass({ outerClass: 'mr-2', labelClass: 'ma-1' }),
  } as Mo00615OnewayType,
  //縦表示ボタン
  mo00009VerticalDisplayOneway: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
  } as Mo00009OnewayType,

  //総合支援視点
  mo00046oneway: {
    itemLabel: '',
    showItemLabel: Or15846Const.DEFAULT.SHOW_ITEM_LABEL,
    isRequired: Or15846Const.DEFAULT.IS_REQUIRED,
    maxlength: Or15846Const.DEFAULT.MAX_LENGTH,
    customClass: new CustomClass({ outerClass: 'mr-0' }),
    autoGrow: false,
    noResize: true,
    rows: 29,
  } as Mo00046OnewayType,
  or51775Oneway: {
    title: t('label.yearly-act-view-point'),
    t1Cd: '2500',
    t2Cd: '3',
    t3Cd: '0',
    tableName: 'cpn_tuc_syp_nenkan1',
    columnName: 'sogo_shiten_knj',
  } as Or51775OnewayType,
})

/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<Mo00046Type>({
  cpId: Or15846Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

const or51775_1 = ref({ uniqueCpId: '' })
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or51775Const.CP_ID(1)]: or51775_1.value,
})

useScreenOneWayBind<Or15846OnewayType>({
  cpId: Or15846Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    initMasterObj: (value) => {
      if (value) {
        localOneway.or15846Oneway.initMasterObj = value
      }
    },
    isCopyMode: (value) => {
      local.isCopyMode = value ?? false
    },
  },
})
/**************************************************
 * 変数定義
 **************************************************/

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * 「視点入力支援アイコン」ボタン
 */
function handleClickPoint() {
  localOneway.or51775Oneway.inputContents = refValue.value?.value ?? ''
  gyoumuCom.setGUI00937Param(localOneway.or51775Oneway, localOneway.or15846Oneway.initMasterObj)
  //GUI00937 入力支援［ケアマネ］画面をポップアップで起動する。
  Or51775Logic.state.set({
    uniqueCpId: or51775_1.value.uniqueCpId,
    state: { isOpen: true },
  })
}

// ダイアログ表示フラグ
const showDialogOr51775 = computed(() => {
  // Or51775のダイアログ開閉状態
  return Or51775Logic.state.get(or51775_1.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 返却情報を設定
 *
 * @param or51775ConfirmValue - 返却情報
 */
const handleConfirm = (or51775ConfirmValue: Or51775ConfirmType) => {
  if (or51775ConfirmValue && refValue.value) {
    const newValue = or51775ConfirmValue.value
    refValue.value.value =
      or51775ConfirmValue.type === '1'
        ? newValue
        : refValue.value.value?.concat(Or15846Const.LINE_BREAK).concat(newValue)
  }
}
</script>

<template>
  <c-v-row
    no-gutters
    class="row-center"
  >
    <!-- 年間行事の実施に係る総合の支援の視点ラベル -->
    <!-- 視点入力支援 -->
    <c-v-col
      cols="auto"
      class="section-header"
    >
      <base-mo00615
        :oneway-model-value="localOneway.mo00615ViewpointOneway"
        class="white-space"
      />
      <c-v-divider
        v-if="!local.isCopyMode"
        vertical
        inset
      />
      <base-mo00009
        v-if="!local.isCopyMode"
        :oneway-model-value="localOneway.mo00009ViewpointOneway"
        @click="handleClickPoint"
      />
    </c-v-col>
  </c-v-row>
  <c-v-row
    no-gutters
    class="row-center"
  >
    <!--総合支援視点-->
    <base-mo00046
      v-model="refValue"
      width="300px"
      :oneway-model-value="localOneway.mo00046oneway"
      :readonly="local.isCopyMode"
    />
  </c-v-row>

  <!--GUI00937 入力支援［ケアマネ］画面-->
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775_1"
    :oneway-model-value="localOneway.or51775Oneway"
    @confirm="handleConfirm"
  />
</template>

<style scoped lang="scss">
@use '@/styles/base.scss';

.row-center {
  display: flex;
  justify-content: center;
}
:deep(.v-col) {
  padding: 0px !important;
}
.section-header {
  max-width: 300px;
  min-width: 300px;
  padding: 0px;
  padding-left: 8px;
  display: flex;
  :deep(.v-sheet) {
    background-color: transparent !important;
    margin-right: 0 !important;
  }
}
</style>
