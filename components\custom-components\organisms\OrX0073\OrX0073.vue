<script setup lang="ts">
/**
 * OrX0073:有機体:［計画書複写］（計画書（2））計画書（2）複写詳細リスト
 * GUI01017
 *
 * @description
 * ［計画書複写］（計画書（2））計画書（2）複写詳細リストを表示するためのコンポーネント。
 *
 * <AUTHOR>
 */
import { ref, reactive, computed, watch, type CSSProperties, nextTick, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { OrX0042Const } from '../OrX0042/OrX0042.constants'
import type { ColumnInfo, Keikasyo2 } from '../OrX0042/OrX0042.type'
import { OrX0031Const } from '../OrX0031/OrX0031.constants'
import { Or00586Const } from '../Or00586/Or00586.constants'
import { Or00586Logic } from '../Or00586/Or00586.logic'
import { OrX0031Logic } from '../OrX0031/OrX0031.logic'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { OrX0073OnewayType, OrX0073Type } from '~/types/cmn/business/components/OrX0073Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import { SPACE_FORWARD_SLASH, UPDATE_KBN } from '~/constants/classification-constants'
import { useSetupChildProps, useSystemCommonsStore } from '#imports'
import { Or21735Const } from '~/components/base-components/organisms/Or21735/Or21735.constants'
import { Or21736Const } from '~/components/base-components/organisms/Or21736/Or21736.constants'
import { Or21737Const } from '~/components/base-components/organisms/Or21737/Or21737.constants'
import { Or21738Const } from '~/components/base-components/organisms/Or21738/Or21738.constants'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import { Or21817Const } from '~/components/base-components/organisms/Or21817/Or21817.constants'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { Or21735Logic } from '~/components/base-components/organisms/Or21735/Or21735.logic'
import { Or21736Logic } from '~/components/base-components/organisms/Or21736/Or21736.logic'
import { Or21737Logic } from '~/components/base-components/organisms/Or21737/Or21737.logic'
import { Or21738Logic } from '~/components/base-components/organisms/Or21738/Or21738.logic'
import { Or21817Logic } from '~/components/base-components/organisms/Or21817/Or21817.logic'
import type { Or00586OnewayType, Or00586Type } from '~/types/cmn/business/components/Or00586Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00018Type } from '~/types/business/components/Mo00018Type'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type {
  Or21814EventType,
  Or21814OnewayType,
} from '~/components/base-components/organisms/Or21814/Or21814.type'
import type { Or27235OnewayType, Or27235Type } from '~/types/cmn/business/components/Or27235Type'
import { Or27235Const } from '~/components/custom-components/organisms/Or27235/Or27235.constants'
import { Or27235Logic } from '~/components/custom-components/organisms/Or27235/Or27235.logic'
import type { Or28720OnewayType, Or28720Type } from '~/types/cmn/business/components/Or28720Type'
import { Or28720Const } from '~/components/custom-components/organisms/Or28720/Or28720.constants'
import { Or28720Logic } from '~/components/custom-components/organisms/Or28720/Or28720.logic'
import type { Or27362OnewayType, Or27362Type } from '~/types/cmn/business/components/Or27362Type'
import { Or27362Const } from '~/components/custom-components/organisms/Or27362/Or27362.constants'
import { Or27362Logic } from '~/components/custom-components/organisms/Or27362/Or27362.logic'
import type { Or50429OneWayType, Or50429Type } from '~/types/cmn/business/components/Or50429Type'
import { Or50429Const } from '~/components/custom-components/organisms/Or50429/Or50429.constants'
import { Or50429Logic } from '~/components/custom-components/organisms/Or50429/Or50429.logic'
import type { Or27043OnewayType, Or27043Type } from '~/types/cmn/business/components/Or27043Type'
import { Or27043Const } from '~/components/custom-components/organisms/Or27043/Or27043.constants'
import { Or27043Logic } from '~/components/custom-components/organisms/Or27043/Or27043.logic'
import type {
  Or28256OnewayType,
  Or28256DataType,
} from '~/components/custom-components/organisms/Or28256/Or28256.type.d.ts'
import { Or28256Const } from '~/components/custom-components/organisms/Or28256/Or28256.constants'
import { Or28256Logic } from '~/components/custom-components/organisms/Or28256/Or28256.logic'
import type { Or51775OnewayType, Or51775Type } from '~/types/cmn/business/components/Or51775Type'
import { Or51775Const } from '~/components/custom-components/organisms/Or51775/Or51775.constants'
import { Or51775Logic } from '~/components/custom-components/organisms/Or51775/Or51775.logic'
import type { Or54215OnewayType } from '~/types/cmn/business/components/Or54215Type'
import { Or54215Const } from '~/components/custom-components/organisms/Or54215/Or54215.constants'
import { Or54215Logic } from '~/components/custom-components/organisms/Or54215/Or54215.logic'
import type { Or53186OnewayType, Or53186Type } from '~/types/cmn/business/components/Or53186Type'
import { Or53186Const } from '~/components/custom-components/organisms/Or53186/Or53186.constants'
import { Or53186Logic } from '~/components/custom-components/organisms/Or53186/Or53186.logic'
import { Or51726Const } from '~/components/custom-components/organisms/Or51726/Or51726.constants'
import { Or51726Logic } from '~/components/custom-components/organisms/Or51726/Or51726.logic'
import type { Or51726Type } from '~/types/cmn/business/components/Or51726Type'
import type { Or28567OnewayType, Or28567Type } from '~/types/cmn/business/components/Or28567Type'
import { Or28567Const } from '~/components/custom-components/organisms/Or28567/Or28567.constants'
import { Or28567Logic } from '~/components/custom-components/organisms/Or28567/Or28567.logic'
import type { Or10477OnewayType, Or10477Type } from '~/types/cmn/business/components/Or10477Type'
import { Or10477Const } from '~/components/custom-components/organisms/Or10477/Or10477.constants'
import { Or10477Logic } from '~/components/custom-components/organisms/Or10477/Or10477.logic'
import type { Or28650OnewayType, Or28650Type } from '~/types/cmn/business/components/Or28650Type'
import { Or28650Const } from '~/components/custom-components/organisms/Or28650/Or28650.constants'
import { Or28650Logic } from '~/components/custom-components/organisms/Or28650/Or28650.logic'
import type {
  Or10860OnewayType,
  Or10860Type,
  sectionItem,
} from '~/types/cmn/business/components/Or10860Type'
import { Or10860Const } from '~/components/custom-components/organisms/Or10860/Or10860.constants'
import { Or10860Logic } from '~/components/custom-components/organisms/Or10860/Or10860.logic'
import { OrX1024Const } from '~/components/custom-components/organisms/OrX1024/OrX1024.constants'
import { OrX1024Logic } from '~/components/custom-components/organisms/OrX1024/OrX1024.logic'
import type { Or10475OnewayType, Or10475Type } from '~/types/cmn/business/components/Or10475Type'
import { Or10475Const } from '~/components/custom-components/organisms/Or10475/Or10475.constants'
import { Or10475Logic } from '~/components/custom-components/organisms/Or10475/Or10475.logic'

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  modelValue: OrX0073Type
}
const props = defineProps<Props>()

const or21735 = ref({ uniqueCpId: Or21735Const.CP_ID(0) })
const or21736 = ref({ uniqueCpId: Or21736Const.CP_ID(0) })
const or21737 = ref({ uniqueCpId: Or21737Const.CP_ID(0) })
const or21738 = ref({ uniqueCpId: Or21738Const.CP_ID(0) })
const orX0031 = ref({ uniqueCpId: OrX0031Const.CP_ID(0) })
const or21817 = ref({ uniqueCpId: Or21817Const.CP_ID(0) })
const or00586 = ref({ uniqueCpId: Or00586Const.CP_ID(0) })
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
const or27235 = ref({ uniqueCpId: Or27235Const.CP_ID(0) })
const or28720 = ref({ uniqueCpId: Or28720Const.CP_ID(0) })
const or27362 = ref({ uniqueCpId: Or27362Const.CP_ID(0) })
const or50429 = ref({ uniqueCpId: Or50429Const.CP_ID(0) })
const or27043 = ref({ uniqueCpId: Or27043Const.CP_ID(0) })
const or28256 = ref({ uniqueCpId: Or28256Const.CP_ID(0) })
const or51775 = ref({ uniqueCpId: Or51775Const.CP_ID(0) })
const or54215 = ref({ uniqueCpId: Or54215Const.CP_ID(0) })
const or53186 = ref({ uniqueCpId: Or53186Const.CP_ID(0) })
const or51726 = ref({ uniqueCpId: Or51726Const.CP_ID(0) })
const or28567 = ref({ uniqueCpId: Or28567Const.CP_ID(0) })
const or10477 = ref({ uniqueCpId: Or10477Const.CP_ID(0) })
const or28650 = ref({ uniqueCpId: Or28650Const.CP_ID(0) })
const or10860 = ref({ uniqueCpId: Or10860Const.CP_ID(0) })
const orX1024 = ref({ uniqueCpId: OrX1024Const.CP_ID(0) })
const or10475 = ref({ uniqueCpId: Or10475Const.CP_ID(0) })

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21735Const.CP_ID(0)]: or21735.value,
  [Or21736Const.CP_ID(0)]: or21736.value,
  [Or21737Const.CP_ID(0)]: or21737.value,
  [Or21738Const.CP_ID(0)]: or21738.value,
  [OrX0031Const.CP_ID(0)]: orX0031.value,
  [Or21817Const.CP_ID(0)]: or21817.value,
  [Or00586Const.CP_ID(0)]: or00586.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or27235Const.CP_ID(0)]: or27235.value,
  [Or28720Const.CP_ID(0)]: or28720.value,
  [Or27362Const.CP_ID(0)]: or27362.value,
  [Or50429Const.CP_ID(0)]: or50429.value,
  [Or27043Const.CP_ID(0)]: or27043.value,
  [Or28256Const.CP_ID(0)]: or28256.value,
  [Or51775Const.CP_ID(0)]: or51775.value,
  [Or54215Const.CP_ID(0)]: or54215.value,
  [Or53186Const.CP_ID(0)]: or53186.value,
  [Or51726Const.CP_ID(0)]: or51726.value,
  [Or28567Const.CP_ID(0)]: or28567.value,
  [Or10477Const.CP_ID(0)]: or10477.value,
  [Or28650Const.CP_ID(0)]: or28650.value,
  [Or10860Const.CP_ID(0)]: or10860.value,
  [OrX1024Const.CP_ID(0)]: orX1024.value,
  [Or10475Const.CP_ID(0)]: or10475.value,
})
// 確認ダイアログのPromise
let or21814ResolvePromise: (value: Or21814EventType) => void
// TODO 共通情報
const commonInfoData = reactive({
  //事業所ID
  officeId: '1000',
  //利用者ID
  userId: '0000001',
  //施設ID
  shisetsuId: '1',
  //種別ID
  shubetsuId: '1',
  //事業者グループ適用ID
  officeGroupId: 'G0002',
  //共通情報.基準日
  commonDate: '2025/02/26',
  //ログイン情報.職員名
  loginUserName: 'LOGIN 職員名',
  //サービス事業者IDリスト
  svJigyoIdList: ['1', '2', '3'],
  eFileSaveKbn: '1',
  showMessageFlg: '1',
  // 事業者コード
  officeCd: '50010',
  // サービスＫＢＮ
  serviceType: '50010',
  // 計画書様式フラグ = 2(居宅)
  cpStyleFlg: '2',
  // 計画書(2)マスタ
  carePlan2Mastr: {
    // 期間の管理
    periodManager: OrX0042Const.PERIOD_MANAGER.DATE,
    // 番号フラグ
    numberFlg: '0',
    // 日付フラグ
    dateFlg: '1',
    //取込設定
    importSettings: '1',
  },
  // 法人ID
  houjinId: '1',
  // システム略称
  sysAbbr: 'CPN',
  // e-文書法対象機能
  eDocumentLawTgtFunc: {
    // 電子ファイル保存設定区分
    elecFileSaveType: '0',
  },
  // 事業者ID
  svJigyoId: '1',
  // ケアプラン方式
  carePlanMethod: '1',
  //処理名
  processFirstName: '[knu1][3GK]計画書(2)',
  //システムコード
  syscd: '3GK',
  // 画面.計画書(2)ID
  ks21Id: '1',
  // 画面.有効期間ID
  termid: '2',
  // API取得システム基準日
  systemDate: '2025/04/13',
  // 頻度取込フラグ
  cks2HindoFlg: 0,
  // 期間管理フラグ
  periodManagementFlag: 'true',
})

const defaultOneway = {
  // 生活全般の解決すべき課題(ニーズ)アイコンボタン
  mo00009OnewayGutaitekiKnj: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 長期目標
  mo00009OnewaylongTermGoal: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 長期目標期間年月日
  mo00009OnewaylongTermGoalYmd: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 長期目標期間月日
  mo00009OnewaylongTermGoalMd: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 長期目標期間
  mo00009OnewaylongTermGoalPeriod: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 短期目標
  mo00009OnewayShortTermGoal: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 短期目標期間
  mo00009OnewayShortTermGoalPeriod: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 短期目標期間年月日
  mo00009OnewayShortTermGoalPeriodYmd: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 短期目標期間月日
  mo00009OnewayShortTermGoalPeriodMd: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // サービス内容
  mo00009OnewayServiceContents: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,

  // サービス種別
  mo00009OnewayServiceKbn: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 保険サービス
  mo00009OnewayInsService: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 利用票取込
  mo00009OnewayUseSlipImport: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // ※2
  mo00009OnewayStar2: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 担当者
  mo00009OnewayManager: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 担当者取込
  mo00009OnewayManagerImport: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 頻度
  mo00009OnewayFrequency: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 頻度曜日
  mo00009OnewayFrequencyDayOfWeek: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 頻度（取込）
  mo00009OnewayFrequencyImport: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 期間
  mo00009OnewayPeriod: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 期間年月日
  mo00009OnewayPeriodYmd: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 間月日
  mo00009OnewayPeriodMd: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 画面メニューマスタ他設定
  mo00009OnewayMasterConfig: {
    density: 'compact',
    rounded: 'sm',
    size: 'medium',
    btnIcon: 'database',
  } as Mo00009OnewayType,
  // 1編集入力アイコンボタン
  mo00009OnewayStart1Edit: {
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,
  // 週間日課
  mo00009OnewayWeekDailyRoutineEdit: {
    btnIcon: 'edit_square',
    density: 'compact',
    readonly: false,
  } as Mo00009OnewayType,

  // 計画書複写］（計画書（2））計画書（2）複写詳細リスト
  orX0073Oneway: {
    tableItem: [] as Keikasyo2[],
    cpyFlg: false,
    periodManageFlag: '',
    planTargetPeriodId: '',
  } as OrX0073OnewayType,
  // 選択列一括上書ボタン
  mo00611BatchOverriteScOneway: {
    prependIcon: 'file_copy',
    btnLabel: t('btn.select-columns-bundle-overrite'),
    class: 'btn-selected-columns-bundle-overrite',
    style: 'white-space: break-spaces !important',
  } as Mo00611OnewayType,
  // データ-ページング
  mo01338Oneway: {
    itemLabel: 0 + SPACE_FORWARD_SLASH + 0,
    customClass: {
      outerClass: 'page-label-center',
    },
  } as Mo01338OnewayType,
  // 下アイコンボタン
  mo00009OnewayDown: {
    btnIcon: 'keyboard_arrow_down',
    variant: 'flat',
    color: 'transparent',
    rounded: '0',
    minWidth: '30px',
    minHeight: '30px',
  } as Mo00009OnewayType,
  // 上アイコンボタン
  mo00009OnewayUp: {
    btnIcon: 'keyboard_arrow_up',
    variant: 'flat',
    color: 'transparent',
    rounded: '0',
    minWidth: '30px',
    minHeight: '30px',
  } as Mo00009OnewayType,
  // GUI01021_担当者入力支援
  or27235OnewayType: {
    pattern: 0,
  } as Or27235OnewayType,
  // GUI01029_プロブレムリスト取込
  or28720OnewayType: {
    processFirstName: '',
  } as Or28720OnewayType,
  // GUI01023_課題整理総括取込画面
  or27362OnewayType: {
    organizingIssuesImportType: { svJigyoId: '', syubetsuId: '', userId: '', shisetuId: '' },
  } as Or27362OnewayType,
  // GUI00957_サービス種別入力支援
  or50429OnewayType: {
    screenDisplayMode: '0',
    serviceKind: '',
    officeName: '',
    processName: '',
    insuranceServiceImport: '',
    createDate: '',
    serviceContent: '',
    revisionFlg: '',
  } as Or50429OneWayType,
  // GUI01022_担当者・頻度画面
  or27043OnewayType: {
    occupationManegerList: [],
    dayOfWeekInfo: {
      dayOfWeek1: '',
      dayOfWeek2: '',
      dayOfWeek3: '',
      dayOfWeek4: '',
      dayOfWeek5: '',
      dayOfWeek6: '',
      dayOfWeek7: '',
      startTime: { value: '' },
      endTime: { value: '' },
    },
  } as Or27043OnewayType,
  // GUI01016	_ケース一覧
  or28256OnewayType: {
    houjinId: '',
    sisetsuId: '',
    bunruiCd: '',
    koumokuno: 0,
    syscd: '',
    kaisihi: { value: '' },
    syuuryouhi: { value: '' },
    targetItem: '',
    caseInformation: '',
    userId: '',
    sheetno: '',
    authen: '',
  } as Or28256OnewayType,
  // GUI00937_入力支援［ケアマネ］
  or51775OnewayType: {
    title: '',
    screenId: '',
    bunruiId: '',
    t1Cd: '',
    t2Cd: '',
    t3Cd: '',
    tableName: '',
    columnName: '',
    assessmentMethod: '',
    inputContents: '',
    userId: '',
    mode: '',
  } as Or51775OnewayType,
  // GUI01019_SDケアマスタ簡易登録
  or54215OnewayType: {
    houjinId: '',
    shisetuId: '',
    svJigyoId: '',
    choukiKnj: '',
    tankiKnj: '',
    needsKnj: '',
    serviceKnj: '',
  } as Or54215OnewayType,
  // GUI01018_SDケア作成
  or53186OnewayType: {
    ks21Id: '',
    termid: '',
    systemDate: '',
  } as Or53186OnewayType,
  // GUI01015_課題と短期目標取込
  or28567OnewayType: {
    issuesAndShortTermGoalImportType: {
      shisetuId: '',
      userId: '',
      svJigyoId: '',
      syubetsuId: '',
      cpnFlg: '',
      periodManagementFlag: '',
    },
  } as Or28567OnewayType,
  // GUI01026_課題と目標取込画面
  or10477OnewayType: {
    issuesOrGoalImportType: {
      shisetuId: '',
      svJigyoId: '',
      syubetsuId: '',
      userId: '',
      sc1Id: '',
      kanriFlg: '',
    },
  } as Or10477OnewayType,
  // GUI01027_フリーアセスメント取込
  or28650OnewayType: {
    importFreeAssessmentSelectType: {
      svJigyoId: '',
      userId: '',
      syubetsuId: '',
      shisetuId: '',
      kanriFlg: '',
      particularFlag: '1',
    },
  } as Or28650OnewayType,
  // GUI01032_表示順変更計画書（2）
  or10860OnewayType: {
    carePlanStyle: 0,
    numberFlag: 0,
  } as Or10860OnewayType,
  // GUI01028_課題とサービス内容取込画面
  or10475OnewayType: {
    issuesAndServiceContenImportType: {
      shisetuId: '',
      svJigyoId: '',
      syubetsuId: '',
      userId: '',
      sc1Id: '',
      kanriFlg: '',
    },
  } as Or10475OnewayType,
}

const localOneway = reactive({
  mo00009OnewayGutaitekiKnj: {
    ...defaultOneway.mo00009OnewayGutaitekiKnj,
  } as Mo00009OnewayType,
  mo00009OnewaylongTermGoal: {
    ...defaultOneway.mo00009OnewaylongTermGoal,
  } as Mo00009OnewayType,
  mo00009OnewaylongTermGoalYmd: {
    ...defaultOneway.mo00009OnewaylongTermGoalYmd,
  } as Mo00009OnewayType,
  mo00009OnewaylongTermGoalMd: {
    ...defaultOneway.mo00009OnewaylongTermGoalMd,
  } as Mo00009OnewayType,
  mo00009OnewaylongTermGoalPeriod: {
    ...defaultOneway.mo00009OnewaylongTermGoalPeriod,
  } as Mo00009OnewayType,
  mo00009OnewayShortTermGoal: {
    ...defaultOneway.mo00009OnewayShortTermGoal,
  } as Mo00009OnewayType,
  mo00009OnewayShortTermGoalPeriod: {
    ...defaultOneway.mo00009OnewayShortTermGoalPeriod,
  } as Mo00009OnewayType,
  mo00009OnewayShortTermGoalPeriodYmd: {
    ...defaultOneway.mo00009OnewayShortTermGoalPeriodYmd,
  } as Mo00009OnewayType,
  mo00009OnewayShortTermGoalPeriodMd: {
    ...defaultOneway.mo00009OnewayShortTermGoalPeriodMd,
  } as Mo00009OnewayType,
  mo00009OnewayServiceContents: {
    ...defaultOneway.mo00009OnewayServiceContents,
  } as Mo00009OnewayType,
  mo00009OnewayServiceKbn: {
    ...defaultOneway.mo00009OnewayServiceKbn,
  } as Mo00009OnewayType,
  mo00009OnewayInsService: {
    ...defaultOneway.mo00009OnewayInsService,
  } as Mo00009OnewayType,
  mo00009OnewayUseSlipImport: {
    ...defaultOneway.mo00009OnewayUseSlipImport,
  } as Mo00009OnewayType,
  mo00009OnewayStar2: {
    ...defaultOneway.mo00009OnewayStar2,
  } as Mo00009OnewayType,
  mo00009OnewayManager: {
    ...defaultOneway.mo00009OnewayManager,
  } as Mo00009OnewayType,
  mo00009OnewayManagerImport: {
    ...defaultOneway.mo00009OnewayManagerImport,
  } as Mo00009OnewayType,
  mo00009OnewayFrequency: {
    ...defaultOneway.mo00009OnewayFrequency,
  } as Mo00009OnewayType,
  mo00009OnewayFrequencyDayOfWeek: {
    ...defaultOneway.mo00009OnewayFrequencyDayOfWeek,
  } as Mo00009OnewayType,
  mo00009OnewayFrequencyImport: {
    ...defaultOneway.mo00009OnewayFrequencyImport,
  } as Mo00009OnewayType,
  mo00009OnewayPeriod: {
    ...defaultOneway.mo00009OnewayPeriod,
  } as Mo00009OnewayType,
  mo00009OnewayPeriodYmd: {
    ...defaultOneway.mo00009OnewayPeriodYmd,
  } as Mo00009OnewayType,
  mo00009OnewayPeriodMd: {
    ...defaultOneway.mo00009OnewayPeriodMd,
  } as Mo00009OnewayType,
  mo00009OnewayMasterConfig: {
    ...defaultOneway.mo00009OnewayMasterConfig,
  } as Mo00009OnewayType,
  mo00009OnewayStart1Edit: {
    ...defaultOneway.mo00009OnewayStart1Edit,
  } as Mo00009OnewayType,
  mo00009OnewayWeekDailyRoutineEdit: {
    ...defaultOneway.mo00009OnewayWeekDailyRoutineEdit,
  } as Mo00009OnewayType,
  orX0073Oneway: {} as OrX0073OnewayType,
  // ～
  mo01337OnewayWaveDash: {
    value: t('label.wave-dash'),
  } as Mo01337OnewayType,
  // 〇
  mo01337OnewayCircle: {
    value: t('label.circle'),
  } as Mo01337OnewayType,
  mo00611BatchOverriteScOneway: {
    ...defaultOneway.mo00611BatchOverriteScOneway,
  } as Mo00611OnewayType,
  mo01338Oneway: {
    ...defaultOneway.mo01338Oneway,
  } as Mo01338OnewayType,
  mo00009OnewayDown: {
    ...defaultOneway.mo00009OnewayDown,
  } as Mo00009OnewayType,
  mo00009OnewayUp: {
    ...defaultOneway.mo00009OnewayUp,
  } as Mo00009OnewayType,
  // GUI01007_課題検討用紙取込画面
  or00586Oneway: {} as Or00586OnewayType,
  // GUI01021_担当者入力支援
  or27235OnewayType: {
    ...defaultOneway.or27235OnewayType,
  } as Or27235OnewayType,
  // GUI01029_プロブレムリスト取込
  or28720OnewayType: {
    ...defaultOneway.or28720OnewayType,
  } as Or28720OnewayType,
  // GUI01023_課題整理総括取込画面
  or27362OnewayType: {
    ...defaultOneway.or27362OnewayType,
  } as Or27362OnewayType,
  // GUI00957_サービス種別入力支援
  or50429OnewayType: {
    ...defaultOneway.or50429OnewayType,
  } as Or50429OneWayType,
  // GUI01022_担当者・頻度画面
  or27043OnewayType: {
    ...defaultOneway.or27043OnewayType,
  } as Or27043OnewayType,
  // GUI01016_ケース一覧
  or28256OnewayType: {
    ...defaultOneway.or28256OnewayType,
  } as Or28256OnewayType,
  // GUI00937_入力支援［ケアマネ］
  or51775OnewayType: {
    ...defaultOneway.or51775OnewayType,
  } as Or51775OnewayType,
  // GUI01019_SDケアマスタ簡易登録
  or54215OnewayType: {
    ...defaultOneway.or54215OnewayType,
  } as Or54215OnewayType,
  // GUI01018_SDケア作成
  or53186OnewayType: {
    ...defaultOneway.or53186OnewayType,
  } as Or53186OnewayType,
  // GUI01015_課題と短期目標取込
  or28567OnewayType: {
    ...defaultOneway.or28567OnewayType,
  } as Or28567OnewayType,
  // GUI01026_課題と目標取込画面
  or10477OnewayType: {
    ...defaultOneway.or10477OnewayType,
  } as Or10477OnewayType,
  // GUI01027_フリーアセスメント取込
  or28650OnewayType: {
    ...defaultOneway.or28650OnewayType,
  } as Or28650OnewayType,
  // GUI01032_表示順変更計画書（2）
  or10860OnewayType: {
    ...defaultOneway.or10860OnewayType,
  } as Or10860OnewayType,
  // GUI01028_課題とサービス内容取込画面
  or10475OnewayType: {
    ...defaultOneway.or10475OnewayType,
  } as Or10475OnewayType,
})

const local = reactive({
  orX0073: {
    selectedItemIndex: 0,
    selectedItemTableIndex: -1,
    selectedColKey: '',
    tableItem: [],
    selectedItem: [],
  } as OrX0073Type,
  or00586: {} as Or00586Type,
  mo00018: { modelValue: false } as Mo00018Type,
  // GUI01021_担当者入力支援画面双方向バインドのインタフェース
  or27235Type: { mode: 0, userId: '', manager: '', result: '' } as Or27235Type,
  // GUI01023_課題整理総括取込画面双方向バインドのインタフェース
  or27362Type: { kadaiKnj: '', yusenNo: '' } as Or27362Type,
  // GUI01029_プロブレムリスト取込画面双方向バインドのインタフェース
  or28720Type: { operation_mode: 0, problemList: [], importItemList: [] } as Or28720Type,
  // GUI00957_サービス種別入力支援画面双方向バインドのインタフェース
  or50429Type: { serviceType: { value: '' }, offerOffice: { value: '' } } as Or50429Type,
  // GUI01022_担当者・頻度画面支援双方向バインドのインタフェース
  or27043Type: {
    occupationManegerList: [],
    selectedJobType: [],
    frequency: '',
    startTime: { value: '' },
    endTime: { value: '' },
  } as Or27043Type,
  // GUI01016_ケース一覧画面双方向バインドのインタフェース
  or28256Type: {
    value: '',
    startDate: '',
    endDate: '',
    caseInformation: '',
  } as Or28256DataType,
  // GUI00937_入力支援［ケアマネ］画面双方向バインドのインタフェース
  or51775Type: {
    modelValue: '',
  } as Or51775Type,
  // GUI01018_SDケア作成画面双方向バインドのインタフェース
  or53186Type: { carePlan2List: [], importMode: '' } as Or53186Type,
  // GUI01031_SDケア選択画面双方向バインドのインタフェース
  or51726Type: { selectResult: '' } as Or51726Type,
  // GUI01015_課題と短期目標取込画面双方向バインドのインタフェース
  or28567Type: { issues: '', longTermGoalIssues: '', shortTermGoal: '' } as Or28567Type,
  // GUI01026_課題と目標取込画面双方向バインドのインタフェース
  or10477Type: { kadaiKnj: '', choukiKnj: '', tankiKnj: '', careKnj: '' } as Or10477Type,
  // GUI01027_フリーアセスメント取込画面双方向バインドのインタフェース
  or28650Type: { issues: '', longTermGoals: '', shortTermGoal: '', careContent: '' } as Or28650Type,
  // GUI01032_表示順変更計画書（2）画面双方向バインドのインタフェース
  or10860Type: { sectionList: [] } as Or10860Type,
  // GUI01024_曜日取込画面双方向バインドのインタフェース
  orX1024Type: { listData: [], textData: '' } as { listData: boolean[]; textData: string },
  // GUI01028_課題とサービス内容取込画面双方向バインドのインタフェース
  or10475Type: { kadaiKnj: '', serviceMemoKnj: '' } as Or10475Type,
})

// 選択した行のindex
const selectedItemIndex = ref<number>(0)
// 選択した行のtableIndex
const selectedItemTableIndex = ref<number>(-1)
// 選択したColumnのキー
const selectedColKey = ref<string>('')
// 拡大・縮小
const lineZoomBigState = ref<boolean>(false)

// (長期目標期間)ヘッダColspan
const headerPeriodColspan = computed(() => {
  const defaultColspan = 3
  // 共通情報.計画書(2)マスタ.期間の管理＝0（日付）の場合、非表示
  return commonInfoData.carePlan2Mastr.periodManager === OrX0042Const.PERIOD_MANAGER.DATE
    ? String(defaultColspan - 2)
    : String(defaultColspan)
})
// (長期目標期間)ヘッダColspan
const dataPriodColspan = computed(() => {
  if (localOneway.orX0073Oneway.cpyFlg) {
    return String(Number(headerPeriodColspan.value))
  }
  return String(Number(headerPeriodColspan.value) + 1)
})
const headers = ref([
  // 課題番号
  { title: t('label.issues-number'), align: 'left', key: 'kadaiNo' },
  // 生活全般の解決すべき課題(ニーズ)
  {
    title: t('label.life-whole-solution-issues'),
    align: 'left',
    key: OrX0042Const.COLUMN_NAME.GUTAITEKI_KNJ_CAMEL,
  },
  // 長期目標
  {
    title: t('label.long-term-goal'),
    align: 'left',
    key: OrX0042Const.COLUMN_NAME.CHOUKI_KNJ_CAMEL,
  },
  // 長期目標期間
  {
    title: t('label.care-plan2-period'),
    align: 'left',
    key: 'longTermGoalYmd',
    children: [
      // 年月日
      { title: t('label.yyyy-mm-dd'), align: 'left', key: 'choSYmd' },
      // 月日
      {
        title: t('label.mm-dd'),
        align: 'left',
        key: 'longTermGoalStartMd',
      },
    ],
  },
  // 短期目標
  {
    title: t('label.short-term-goal'),
    align: 'left',
    key: OrX0042Const.COLUMN_NAME.TANKI_KNJ_CAMEL,
  },
  // 短期目標期間
  {
    title: t('label.care-plan2-period'),
    align: 'left',
    key: 'shortTermGoalYmd',
    children: [
      // 年月日
      {
        title: t('label.yyyy-mm-dd'),
        align: 'left',
      },
      // 月日
      {
        title: t('label.mm-dd'),
        align: 'left',
      },
    ],
  },
  // 番号
  {
    title: t('label.number'),
    align: 'left',
    key: 'number',
  },
  // サービス内容
  {
    title: t('label.service-contents'),
    align: 'left',
    key: OrX0042Const.COLUMN_NAME.KAIGO_KNJ_CAMEL,
  },
  // ※1
  {
    title: t('label.star-1'),
    align: 'left',
    key: 'hkyuKnj',
  },
  // サービス種別
  {
    title: t('label.service-kbn'),
    align: 'left',
    key: OrX0042Const.COLUMN_NAME.SV_SHU_KNJ_CAMEL,
    children: [
      // 保険サービス
      {
        title: t('label.insurance-service'),
        align: 'left',
      },
      // 利用票取込
      {
        title: t('label.use-slip-import'),
        align: 'left',
      },
    ],
  },
  // ※2
  {
    title: t('label.star-2'),
    align: 'left',
    key: OrX0042Const.COLUMN_NAME.JIGYO_NAME_KNJ_CAMEL,
  },
  // 担当者
  {
    title: t('label.care-plan2-manager'),
    align: 'left',
    key: OrX0042Const.COLUMN_NAME.SV_SHU_KNJ_CAMEL,
    children: [
      // （取込）
      {
        title: t('label.import'),
        align: 'left',
      },
    ],
  },
  // 頻度
  {
    title: t('label.frequency'),
    align: 'left',
    key: OrX0042Const.COLUMN_NAME.HINDO_KNJ_CAMEL,
    children: [
      // 曜日
      {
        title: t('label.day-of-week'),
        align: 'left',
      },
      // （取込）
      {
        title: t('label.import'),
        align: 'left',
      },
    ],
  },
  // 期間
  {
    title: t('label.care-plan2-period'),
    align: 'left',
    key: 'periodYmd',
    children: [
      // 年月日
      {
        title: t('label.yyyy-mm-dd'),
        align: 'left',
      },
      // 月日
      {
        title: t('label.mm-dd'),
        align: 'left',
      },
    ],
  },
  // 週間日課
  {
    title: t('label.week-daily-routine'),
    align: 'left',
    key: 'weekDailyRoutine',
  },
])
// 削除データfilter
const tableDataFilter = computed(() => {
  return tableItem.value?.filter((i: Keikasyo2) => i.updateKbn !== UPDATE_KBN.DELETE) ?? []
})

const columnFlag = ref({
  // 計画対象期間表示表示フラグ
  showPlanningPeriodFlg: false,
  // 履歴表示フラグ
  showHistoryFlg: false,
  // 作成者表示フラグ
  showAuthorFlg: false,
  // 作成日表示フラグ
  showCreateDateFlg: false,
  // 保険サービス
  showInsServiceFlg: false,
  //'期間月日表示フラグ
  showPeriodMdFlg: false,
  // 番号表示フラグ
  showNumberFlg: false,
  // ※1ラベル表示フラグ
  showStar1Flg: false,
  // サービス種別表示フラグ
  showServiceType: false,
  // (サービス種別)保険サービス
  showServiceTypeInsServiceFlg: false,
  // (サービス種別)利用票取込
  showServiceTypeUseSlipImportFlg: false,
  // ※2
  showStar2Flg: false,
  // 担当者
  showManagerFlg: false,
  // 担当者（取込）
  showManagerImportFlg: false,
  // 曜日
  showDayOfWeekFlg: false,
  // 頻度（取込）
  showFreqImportFlg: false,
  // 週間日課
  showWeekDailyRoutingFlg: false,
  // 長期目標期間開始年月日テキストボックス
  showPeriodStartYmdFlg: false,
  // 期間～
  showPeriodLabelFlg: false,
  // 期間終了年月日
  showPeriodEndYmdFlg: false,
})
// 選択列一括更新機能の対象
const batchColUpdateWhiteList = ref<ColumnInfo[]>([
  { key: OrX0042Const.COLUMN_NAME.SV_SHU_KNJ_CAMEL, title: t('label.service-kbn') },
  { key: OrX0042Const.COLUMN_NAME.HINDO_KNJ_CAMEL, title: t('label.frequency') },
  { key: OrX0042Const.COLUMN_NAME.JIGYO_NAME_KNJ_CAMEL, title: t('label.star-2') },
  { key: OrX0042Const.COLUMN_NAME.CHOUKI_KNJ_CAMEL, title: t('label.choukiKnj') },
  { key: OrX0042Const.COLUMN_NAME.TANKI_KNJ_CAMEL, title: t('label.tankiKnj') },
  { key: OrX0042Const.COLUMN_NAME.KIKAN_KNJ_CAMEL, title: t('label.kikanKnj') },
])
// ケース取込項目機能の対象
const caseImportWhiteList = ref<ColumnInfo[]>([
  {
    key: OrX0042Const.COLUMN_NAME.GUTAITEKI_KNJ_CAMEL,
    title: t('label.life-whole-solution-issues'),
  },
  { key: OrX0042Const.COLUMN_NAME.CHOUKI_KNJ_CAMEL, title: t('label.long-term-goal') },
  { key: OrX0042Const.COLUMN_NAME.CHO_KIKAN_KNJ_CAMEL, title: t('label.care-plan2-period') },
  { key: OrX0042Const.COLUMN_NAME.TANKI_KNJ_CAMEL, title: t('label.short-term-goal') },
  { key: OrX0042Const.COLUMN_NAME.TAN_KIKAN_KNJ_CAMEL, title: t('label.care-plan2-period') },
  { key: OrX0042Const.COLUMN_NAME.KAIGO_KNJ_CAMEL, title: t('label.service-contents') },
  { key: OrX0042Const.COLUMN_NAME.SV_SHU_KNJ_CAMEL, title: t('label.service-kbn') },
  { key: OrX0042Const.COLUMN_NAME.JIGYO_NAME_KNJ_CAMEL, title: t('label.star-2') },
  { key: OrX0042Const.COLUMN_NAME.HINDO_KNJ_CAMEL, title: t('label.frequency') },
  { key: OrX0042Const.COLUMN_NAME.KIKAN_KNJ_CAMEL, title: t('label.care-plan2-period') },
])
// 選択行ID
const selectedRows = ref<Keikasyo2[]>([])
const tableItem = ref<Keikasyo2[]>(localOneway.orX0073Oneway.tableItem)
let initFlg = false
/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['onWeekDailuRoutingClick', 'update:modelValue'])

/**************************************************
 * コンポーネント固有処理
 **************************************************/

function initColumnFlag() {
  // ②保険サービス② 共通情報.計画書様式フラグ=2(居宅) 且つ 事業者コードは"30010", "50010", "23031", "43031", "61084", "71084","61104"の場合表示、以外は非表示
  if (
    !(
      commonInfoData.cpStyleFlg === OrX0042Const.CARE_PLAN_STYLE_FLG.HOME &&
      OrX0042Const.OFFICE_CD_LIST.includes(commonInfoData.officeCd)
    )
  ) {
    columnFlag.value.showInsServiceFlg = false
  }
  // (長期目標期間)月日指定: 共通情報.計画書(2)マスタ.期間の管理＝0（日付）の場合、非表示
  if (commonInfoData.carePlan2Mastr.periodManager === OrX0042Const.PERIOD_MANAGER.DATE) {
    columnFlag.value.showPeriodMdFlg = false
  }
  // 番号: 共通情報.計画書(2)マスタ.番号フラグ＝０（表示しない）の場合、非表示
  if (commonInfoData.carePlan2Mastr.numberFlg === OrX0042Const.NUMBER_FLG.NOT_SHOW) {
    columnFlag.value.showNumberFlg = false
  }
  // 共通情報.システム略称＝'CMN' 又は 共通情報.計画書様式フラグ=2(居宅)の場合、表示
  // 以外非表示
  if (
    commonInfoData.sysAbbr === OrX0042Const.SYS_ABBR.CMN ||
    commonInfoData.cpStyleFlg === OrX0042Const.CARE_PLAN_STYLE_FLG.HOME
  ) {
    // ※1
    columnFlag.value.showStar1Flg = true
    // サービス種別
    columnFlag.value.showServiceType = true
    // ※2
    columnFlag.value.showStar2Flg = true
    // 曜日
    columnFlag.value.showDayOfWeekFlg = true
  }
  // 共通情報.計画書様式フラグ=2(居宅) 且つ
  // 事業者コードは"30010", "50010", "23031", "43031", "61084", "71084","61104"の場合表示
  // 、以外は非表示
  if (
    commonInfoData.cpStyleFlg === OrX0042Const.CARE_PLAN_STYLE_FLG.HOME &&
    OrX0042Const.OFFICE_CD_LIST.includes(commonInfoData.officeCd)
  ) {
    // (サービス種別)保険サービス
    columnFlag.value.showServiceTypeInsServiceFlg = true
    // (サービス種別)利用票取込
    columnFlag.value.showServiceTypeUseSlipImportFlg = true
  }
  // 共通情報.システム略称＝'CMN' 又は 共通情報.計画書様式フラグ=2(居宅)の場合、非表示
  if (
    commonInfoData.sysAbbr === OrX0042Const.SYS_ABBR.CMN ||
    commonInfoData.cpStyleFlg === OrX0042Const.CARE_PLAN_STYLE_FLG.HOME
  ) {
    // 担当者
    columnFlag.value.showManagerFlg = false
    // 担当者（取込）
    columnFlag.value.showManagerImportFlg = false
    // 頻度（取込）
    columnFlag.value.showFreqImportFlg = false
    // 週間日課
    columnFlag.value.showWeekDailyRoutingFlg = false
  } else {
    // 担当者
    columnFlag.value.showManagerFlg = true
    // 担当者（取込）
    columnFlag.value.showManagerImportFlg = true
    // 頻度（取込）
    columnFlag.value.showFreqImportFlg = true
    // 週間日課
    columnFlag.value.showWeekDailyRoutingFlg = true
  }
  // 共通情報.計画書(2)マスタ.期間の管理＝１（文章）の場合、非表示
  if (commonInfoData.carePlan2Mastr.periodManager === OrX0042Const.PERIOD_MANAGER.DOC) {
    columnFlag.value.showPeriodStartYmdFlg = false
  }
  // 共通情報.計画書(2)マスタ.期間の管理＝１（文章）
  // 又は（期間の管理=日付かつ日付フラグ=3（?月?日）または2（?/?））の場合、非表示
  if (
    commonInfoData.carePlan2Mastr.periodManager === OrX0042Const.PERIOD_MANAGER.DOC ||
    (commonInfoData.carePlan2Mastr.periodManager === OrX0042Const.PERIOD_MANAGER.DATE &&
      [OrX0042Const.DATE_FLG.MM_MO_DD_DAY, OrX0042Const.DATE_FLG.MM_DD].includes(
        commonInfoData.carePlan2Mastr.dateFlg
      ))
  ) {
    // 期間～ラベル
    columnFlag.value.showPeriodLabelFlg = false
    // 終了年月日テキストボックス
    columnFlag.value.showPeriodEndYmdFlg = false
  }
  // データ-ページング
  void resetMo01338OnewayItemLabel()
}
/**
 * 行選択
 *
 * @param index - 選択した行のindex
 *
 * @param tableIndex - 選択した行のtableIndex
 */
function onSelectRow(index: number, tableIndex: number) {
  selectedItemIndex.value = index
  selectedItemTableIndex.value = tableIndex
}
/**
 * 列が押下されました
 *
 * @param columnKey - 列のキー
 */
function onClickCol(columnKey?: string) {
  if (columnKey !== undefined) {
    selectedColKey.value = columnKey
  } else {
    selectedColKey.value = ''
  }
}

const cellStyles = ref<Record<string, CSSProperties>[]>([])
const mergeColKeys = [
  OrX0042Const.COLUMN_NAME.GUTAITEKI_KNJ_CAMEL,
  OrX0042Const.COLUMN_NAME.CHOUKI_KNJ_CAMEL,
  OrX0042Const.COLUMN_NAME.CHO_KIKAN_KNJ_CAMEL,
  OrX0042Const.COLUMN_NAME.TANKI_KNJ_CAMEL,
  OrX0042Const.COLUMN_NAME.TAN_KIKAN_KNJ_CAMEL,
]
const specialColKeys = [
  OrX0042Const.COLUMN_NAME.CHO_KIKAN_KNJ_CAMEL,
  OrX0042Const.COLUMN_NAME.TAN_KIKAN_KNJ_CAMEL,
]
const updateStyles = () => {
  const newStyles: Record<string, CSSProperties>[] = []
  for (let i = 0; i < tableItem.value.length; i++) {
    const rowStyles: Record<string, CSSProperties> = {}
    const currentRow = tableItem.value[i]
    mergeColKeys.forEach((colKey) => {
      const prevRow = tableItem.value[i - 1]
      const nextRow = tableItem.value[i + 1]
      if (!specialColKeys.includes(colKey)) {
        const currentValue = undefinedToEmpty(
          (currentRow as unknown as Record<string, { value: string }>)[colKey]?.value
        )
        const style: CSSProperties = {
          borderTop: '1px',
          borderBottom: '1px',
          color: 'inherit',
        }

        if (
          prevRow &&
          undefinedToEmpty(
            (prevRow as unknown as Record<string, { value: string }>)[colKey]?.value
          ) === currentValue
        ) {
          style.borderTop = '0 !important'
          style.color = '#fff !important'
        }
        if (
          nextRow &&
          undefinedToEmpty(
            (nextRow as unknown as Record<string, { value: string }>)[colKey]?.value
          ) === currentValue
        ) {
          style.borderBottom = '0 !important'
          style.color = 'inherit'
        }

        rowStyles[colKey] = style
      } else {
        let currentValue = ''
        let prevRowValue = ''
        let nextRowValue = ''
        if (colKey === OrX0042Const.COLUMN_NAME.CHO_KIKAN_KNJ_CAMEL) {
          // 期間の管理=文章の場合
          if (commonInfoData.carePlan2Mastr.periodManager === OrX0042Const.PERIOD_MANAGER.DOC) {
            currentValue = JSON.stringify(undefinedToEmpty(currentRow?.choKikanKnj?.value))
            prevRowValue = JSON.stringify(undefinedToEmpty(prevRow?.choKikanKnj?.value))
            nextRowValue = JSON.stringify(undefinedToEmpty(nextRow?.choKikanKnj?.value))
          } else if (
            commonInfoData.carePlan2Mastr.periodManager === OrX0042Const.PERIOD_MANAGER.DATE
          ) {
            // 期間の管理=日付
            currentValue =
              JSON.stringify(undefinedToEmpty(currentRow?.choKikanKnj?.value)) +
              JSON.stringify(undefinedToEmpty(currentRow?.choSYmd?.value)) +
              JSON.stringify(undefinedToEmpty(currentRow?.choEYmd?.value))
            prevRowValue =
              JSON.stringify(undefinedToEmpty(prevRow?.choKikanKnj?.value)) +
              JSON.stringify(undefinedToEmpty(prevRow?.choSYmd?.value)) +
              JSON.stringify(undefinedToEmpty(prevRow?.choEYmd?.value))
            nextRowValue =
              JSON.stringify(undefinedToEmpty(nextRow?.choKikanKnj?.value)) +
              JSON.stringify(undefinedToEmpty(nextRow?.choSYmd?.value)) +
              JSON.stringify(undefinedToEmpty(nextRow?.choEYmd?.value))
          }
        } else if (colKey === OrX0042Const.COLUMN_NAME.TAN_KIKAN_KNJ_CAMEL) {
          // 期間の管理=文章の場合
          if (commonInfoData.carePlan2Mastr.periodManager === OrX0042Const.PERIOD_MANAGER.DOC) {
            currentValue = JSON.stringify(undefinedToEmpty(currentRow?.tanKikanKnj?.value))
            prevRowValue = JSON.stringify(undefinedToEmpty(prevRow?.tanKikanKnj?.value))
            nextRowValue = JSON.stringify(undefinedToEmpty(nextRow?.tanKikanKnj?.value))
          } else if (
            commonInfoData.carePlan2Mastr.periodManager === OrX0042Const.PERIOD_MANAGER.DATE
          ) {
            // 期間の管理=日付
            currentValue =
              JSON.stringify(undefinedToEmpty(currentRow?.tanKikanKnj?.value)) +
              JSON.stringify(undefinedToEmpty(currentRow?.tanSYmd?.value)) +
              JSON.stringify(undefinedToEmpty(currentRow?.tanEYmd?.value))
            prevRowValue =
              JSON.stringify(undefinedToEmpty(prevRow?.tanKikanKnj?.value)) +
              JSON.stringify(undefinedToEmpty(prevRow?.tanSYmd?.value)) +
              JSON.stringify(undefinedToEmpty(prevRow?.tanEYmd?.value))
            nextRowValue =
              JSON.stringify(undefinedToEmpty(nextRow?.tanKikanKnj?.value)) +
              JSON.stringify(undefinedToEmpty(nextRow?.tanSYmd?.value)) +
              JSON.stringify(undefinedToEmpty(nextRow?.tanEYmd?.value))
          }
        }
        const style: CSSProperties = {
          borderTop: '1px',
          borderBottom: '1px',
        }

        if (prevRow && prevRowValue === currentValue) {
          style.borderTop = '0 !important'
        }
        if (nextRow && nextRowValue === currentValue) {
          style.borderBottom = '0 !important'
        }

        rowStyles[colKey] = style
      }
    })
    newStyles.push(rowStyles)
  }
  cellStyles.value = newStyles
}
function getCellStyle(colKey: string, rowIndex: number) {
  if (!mergeColKeys.includes(colKey)) return {}
  return cellStyles.value[rowIndex]?.[colKey] || {}
}
/**
 * undefinedを空の文字列に変換する
 *
 * @param el - 値
 */
function undefinedToEmpty(el: unknown) {
  if (el === undefined) {
    return ''
  }
  if (typeof el === 'object') return JSON.stringify(el) // 主动拦截对象
  return el
}

/**
 * 「選択列一括上書ボタン」押下
 */
async function onClickbatchOverriteSc() {
  // 選択された列は選択列一括更新機能の対象外の場合
  // 選択された列は最終行の場合
  const keyList = batchColUpdateWhiteList.value.map((colInfo) => colInfo.key)
  const title = batchColUpdateWhiteList.value.find(
    (colInfo) => colInfo.key === selectedColKey.value
  )?.title
  if (
    selectedColKey.value === '' ||
    selectedColKey.value === undefined ||
    !keyList.includes(selectedColKey.value) ||
    selectedItemIndex.value === tableDataFilter.value.length - 1
  ) {
    // error
    await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-20834'),
      // 第1ボタンタイプ
      firstBtnType: 'normal1',
      // 第1ボタンラベル
      firstBtnLabel: t('btn.yes'),
      // 第2ボタンボタンタイプ
      secondBtnType: 'blank',
      // 第3ボタンタイプ
      thirdBtnType: 'blank',
    })
    return
  }
  // 現在選択されている列
  const topLine = document.querySelectorAll<HTMLTextAreaElement>(
    `.${selectedColKey.value}-${selectedItemIndex.value}`
  )
  // 選択行の値(背景赤色)で
  topLine.forEach((col) => col.style.setProperty('background', 'rgb(255,200,210)', 'important'))
  // その他の列
  const otherLine = document.querySelectorAll<HTMLTextAreaElement>(
    `.${selectedColKey.value}:not(.${selectedColKey.value}-${selectedItemIndex.value})`
  )
  // 選択行以下の値(背景橙色)
  otherLine.forEach((col) => col.style.setProperty('background', 'rgb(255,200,100)', 'important'))
  if (title !== null && title !== undefined) {
    // 選択された列の列名を取得できた場合
    // 以下のメッセージを表示i.cmn.20835
    const rs = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-20835', [title, title]),
      // 第1ボタンタイプ
      firstBtnType: 'normal1',
      // 第1ボタンラベル
      firstBtnLabel: t('btn.yes'),
      // 第2ボタンボタンタイプ
      secondBtnType: 'normal3',
      // 第2ボタンボタンタイプ
      secondBtnLabel: t('btn.no'),
      // 第3ボタンタイプ
      thirdBtnType: 'blank',
    })
    if (rs.firstBtnClickFlg) {
      // はい：AC027-5 を実行
      confirmBatchSet()
    } else {
      cancelBatchSet()
    }
  } else {
    // 以下のメッセージを表示i.cmn.20835
    const rs = await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-20836'),
      // 第1ボタンタイプ
      firstBtnType: 'normal1',
      // 第1ボタンラベル
      firstBtnLabel: t('btn.yes'),
      // 第2ボタンボタンタイプ
      secondBtnType: 'normal3',
      // 第2ボタンボタンタイプ
      secondBtnLabel: t('btn.no'),
      // 第3ボタンタイプ
      thirdBtnType: 'blank',
    })
    if (rs.firstBtnClickFlg) {
      // はい：AC027-5 を実行
      confirmBatchSet()
    } else {
      cancelBatchSet()
    }
  }
}
/**
 * 「次の行選択」押下
 */
function nextLine() {
  // 選択した行のフォーカスを一つ下の行を選択する（フォーカスチェンジ）すでに最終行の場合、なにもしない
  if (selectedItemIndex.value + 1 < tableDataFilter.value.length) {
    selectedItemIndex.value++
  }
}
/**
 * 「前の行選択」押下
 */
function preLine() {
  // 選択した行のフォーカスを一つ上の行を選択する（フォーカスチェンジ）すでに最初行の場合、なにもしない
  if (selectedItemIndex.value - 1 >= 0) {
    selectedItemIndex.value--
  }
}
/**
 * テキストエリアの高さを調整する
 *
 * @param textarea - テキストエリア
 */
function adjustTextareaHeight(textarea: HTMLTextAreaElement) {
  if (lineZoomBigState.value) {
    textarea.style.setProperty('height', '100%', 'important')
  } else {
    textarea.style.setProperty('height', '265px', 'important')
  }
}
/**
 * 「拡大・縮小」押下
 */
function changeLineHeight() {
  // 行の高さが切り替わります
  const textareas = document.querySelectorAll<HTMLTextAreaElement>('.careplan2-table tbody > tr')
  textareas.forEach((textarea) => adjustTextareaHeight(textarea))
  lineZoomBigState.value = !lineZoomBigState.value
}
/**
 * 「行追加ボタン」押下
 */
async function addNewLine() {
  await nextTick()
  // 最終に新しい行を追加し、追加行を選択状態とする。
  const data: Keikasyo2 = {
    weekDailyRouting: t('label.absence'),
    updateKbn: UPDATE_KBN.CREATE,
    tableIndex: tableItem.value.length,
    svShuKnj: { value: '' },
    jigyoNameKnj: { value: '' },
    choSYmd: { value: '' },
    choEYmd: { value: '' },
    tanSYmd: { value: '' },
    tanEYmd: { value: '' },
    kikanEYmd: { value: '' },
    kikanSYmd: { value: '' },
    checked: { modelValue: false },
  }
  tableItem.value.push(data)
  selectedItemIndex.value = tableDataFilter.value.length - 1
  void nextTick(() => {
    if (lineZoomBigState.value) {
      const textareas = document.querySelectorAll<HTMLTextAreaElement>(
        '.careplan2-table tbody > tr'
      )
      textareas.forEach((textarea) => textarea.style.setProperty('height', '265px', 'important'))
    }
  })
}

/**
 * GUI01023_課題整理総括取込画面確定ボタン押下
 *
 * @param data - 課題整理総括取込画面情報
 */
async function onClickIssuesConfirm(data: Or27362Type[]) {
  if (data.length > 0) {
    for (const item of data) {
      await nextTick()
      // 最終に新しい行を追加し、追加行を選択状態とする。
      const data: Keikasyo2 = {
        weekDailyRouting: t('label.absence'),
        updateKbn: UPDATE_KBN.CREATE,
        tableIndex: tableItem.value.length,
        gutaitekiKnj: { value: item.kadaiKnj ?? '' },
        svShuKnj: { value: '' },
        jigyoNameKnj: { value: '' },
        choSYmd: { value: '' },
        choEYmd: { value: '' },
        tanSYmd: { value: '' },
        tanEYmd: { value: '' },
        kikanEYmd: { value: '' },
        kikanSYmd: { value: '' },
        checked: { modelValue: false },
      }
      addNewLineByData(data)
    }
  }
}

/**
 * 行追加
 *
 * @param data - 新行
 */
function addNewLineByData(data: Keikasyo2) {
  tableItem.value.push(data)
  selectedItemIndex.value = tableDataFilter.value.length - 1
  void nextTick(() => {
    if (lineZoomBigState.value) {
      const textareas = document.querySelectorAll<HTMLTextAreaElement>(
        '.careplan2-table tbody > tr'
      )
      textareas.forEach((textarea) => textarea.style.setProperty('height', '265px', 'important'))
    }
  })
}

/**
 * GUI01029_プロブレムリスト取込画面確定ボタン押下
 *
 * @param data - プロブレムリスト取込画面情報
 */
async function onClickPlConfirm(data: Or28720Type) {
  const problemList = data.problemList!
  for (const item of problemList) {
    await nextTick()
    // 最終に新しい行を追加し、追加行を選択状態とする。
    const newLine: Keikasyo2 = {
      weekDailyRouting: t('label.absence'),
      updateKbn: UPDATE_KBN.CREATE,
      tableIndex: tableItem.value.length,
      gutaitekiKnj: { value: '' },
      choukiKnj: { value: '' },
      tankiKnj: { value: '' },
      kaigoKnj: { value: '' },
      svShuKnj: { value: '' },
      jigyoNameKnj: { value: '' },
      choSYmd: { value: '' },
      choEYmd: { value: '' },
      tanSYmd: { value: '' },
      tanEYmd: { value: '' },
      kikanEYmd: { value: '' },
      kikanSYmd: { value: '' },
      checked: { modelValue: false },
    }
    if (data.importItemList !== undefined) {
      //取込先項目番号が100、文字列を「生活先般の解決すべき課題（ニーズ)」に設定する。
      if (data.importItemList.includes(Or28720Const.IMPORT_LiFE_WHOLE_SOLUTION_ISSUES_NEEDS)) {
        newLine.gutaitekiKnj!.value = item.titleKnj
      }
      //取込先項目番号が101、文字列を「長期目標」に設定する。
      if (data.importItemList.includes(Or28720Const.IMPORT_LONG_TERM_GOAL)) {
        newLine.choukiKnj!.value = item.titleKnj
      }
      //取込先項目番号が102、文字列を「短期目標」に設定する。
      if (data.importItemList.includes(Or28720Const.IMPORT_SHORT_TERM_GOAL)) {
        newLine.tankiKnj!.value = item.titleKnj
      }
      //取込先項目番号が103、文字列を「サービス内容」に設定する。
      if (data.importItemList.includes(Or28720Const.IMPORT_SERVICE_CONTENTS)) {
        newLine.kaigoKnj!.value = item.titleKnj
      }
    }
    addNewLineByData(newLine)
  }
}

/**
 * GUI00957_サービス種別入力支援画面確定ボタン押下
 *
 * @param data - サービス種別入力支援画面情報
 */
function onClickSvKbnConfirm(data: Or50429Type) {
  if (
    data !== undefined &&
    tableDataFilter.value !== undefined &&
    tableDataFilter.value.length > 0
  ) {
    //返却情報を画面の サービス種別に上書きする
    tableDataFilter.value[selectedItemIndex.value].svShuKnj!.value = data.serviceType.value!
    //返却情報を画面の ※２に上書きする
    tableDataFilter.value[selectedItemIndex.value].jigyoNameKnj!.value = data.offerOffice.value!
  }
}

/**
 * GUI01022_担当者・頻度画面確定ボタン押下
 *
 * @param data - 担当者・頻度画面情報
 */
function onClickManagerImportConfirm(data: Or27043Type) {
  if (
    data !== undefined &&
    tableDataFilter.value !== undefined &&
    tableDataFilter.value.length > 0
  ) {
    let hindoKnj = ''
    let hindoKnjNumber = 0
    if (data.frequency !== undefined && data.frequency !== null) {
      const rest = data.frequency.split('')
      if (rest.length === 7) {
        if (rest[0] === OrX0042Const.YOBI.SELECT) {
          hindoKnj += t('label.weekly-plan-day-short-sunday')
          hindoKnjNumber++
        }
        if (rest[1] === OrX0042Const.YOBI.SELECT) {
          hindoKnj += t('label.weekly-plan-day-short-monday')
          hindoKnjNumber++
        }
        if (rest[2] === OrX0042Const.YOBI.SELECT) {
          hindoKnj += t('label.weekly-plan-day-short-tuesday')
          hindoKnjNumber++
        }
        if (rest[3] === OrX0042Const.YOBI.SELECT) {
          hindoKnj += t('label.weekly-plan-day-short-wednesday')
          hindoKnjNumber++
        }
        if (rest[4] === OrX0042Const.YOBI.SELECT) {
          hindoKnj += t('label.weekly-plan-day-short-thursday')
          hindoKnjNumber++
        }
        if (rest[5] === OrX0042Const.YOBI.SELECT) {
          hindoKnj += t('label.weekly-plan-day-short-friday')
          hindoKnjNumber++
        }
        if (rest[6] === OrX0042Const.YOBI.SELECT) {
          hindoKnj += t('label.weekly-plan-day-short-saturday')
          hindoKnjNumber++
        }
      }
    }
    if (commonInfoData.cks2HindoFlg === 0) {
      if (
        data.startTime.value === OrX0042Const.FREQUENCY.TIME_ZERO &&
        data.endTime.value === OrX0042Const.FREQUENCY.TIME_ZERO
      ) {
        //返却情報を画面の 頻度に上書きする
        tableDataFilter.value[selectedItemIndex.value].hindoKnj!.value = hindoKnj
      } else {
        //返却情報を画面の 頻度に上書きする
        tableDataFilter.value[selectedItemIndex.value].hindoKnj!.value =
          t('label.left-parentheses‌') +
          hindoKnj +
          data.startTime.value +
          t('label.wavy-withoutblank') +
          data.endTime.value +
          t('label.right-parentheses‌')
      }
    }
    if (commonInfoData.cks2HindoFlg === 1) {
      const result = t('btn.week') + hindoKnjNumber + t('label.times')
      //1週回/月回
      if (
        data.startTime.value === OrX0042Const.FREQUENCY.TIME_ZERO &&
        data.endTime.value === OrX0042Const.FREQUENCY.TIME_ZERO
      ) {
        //返却情報を画面の 頻度に上書きする
        tableDataFilter.value[selectedItemIndex.value].hindoKnj!.value = result
      } else {
        //返却情報を画面の 頻度に上書きする
        tableDataFilter.value[selectedItemIndex.value].hindoKnj!.value =
          result +
          t('label.left-parentheses‌') +
          data.startTime.value +
          t('label.wavy-withoutblank') +
          data.endTime.value +
          t('label.right-parentheses‌')
      }
    }
    if (commonInfoData.cks2HindoFlg === 2) {
      //随時
      //返却情報を画面の 頻度に上書きする
      tableDataFilter.value[selectedItemIndex.value].hindoKnj!.value = t('label.at-any-time')
    }
    //返却情報を画面の 担当者に上書きする
    if (data.selectedJobType !== undefined && data.selectedJobType.length > 0) {
      let svShuKnj = ''
      for (const item of data.selectedJobType) {
        svShuKnj += item.shokushuKnj + OrX0042Const.ENTER
      }
      tableDataFilter.value[selectedItemIndex.value].svShuKnj!.value = svShuKnj
    }
    //返却情報を画面の 週間日課に上書きする
    if (
      (data.selectedJobType !== undefined && data.selectedJobType.length > 0) ||
      data.frequency !== OrX0042Const.YOBI.UN_SELECT ||
      data.startTime.value !== OrX0042Const.FREQUENCY.TIME_ZERO ||
      data.endTime.value !== OrX0042Const.FREQUENCY.TIME_ZERO
    ) {
      tableDataFilter.value[selectedItemIndex.value].weekDailyRouting = t('label.presence')
    } else {
      tableDataFilter.value[selectedItemIndex.value].weekDailyRouting = t('label.absence')
    }
  }
}

/**
 * GUI01032_表示順変更計画書（2）画面確定ボタン押下
 *
 * @param data - 表示順変更計画書（2）画面情報
 */
function onClickSortModifiedConfirm(data: Or10860Type) {
  if (data?.sectionList !== undefined && data.sectionList.length > 0) {
    // 削除データ
    const deleteData: Keikasyo2[] =
      tableItem.value?.filter((i: Keikasyo2) => i.updateKbn === UPDATE_KBN.DELETE) ?? []
    // 表示順変更データ
    const sortData: Keikasyo2[] = []
    data.sectionList.forEach((newOrder) => {
      sortData.push(tableDataFilter.value[Number(newOrder.orderBackup) - 1])
    })
    sortData.push(...deleteData)
    tableItem.value = sortData
  }
}

/**
 * GUI01016_「ケース取込の入力支援アイコンボタン」押下
 *
 * @param data - ケース取込の入力支援情報
 */
function onClickCaseImportConfirm(data: string) {
  if (
    data !== undefined &&
    tableDataFilter.value !== undefined &&
    tableDataFilter.value.length > 0
  ) {
    switch (caseImportColKey.value) {
      // 生活全般の解決すべき課題(ニーズ)
      case OrX0042Const.COLUMN_NAME.GUTAITEKI_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].gutaitekiKnj = { value: data }
        break
      // 長期目標
      case OrX0042Const.COLUMN_NAME.CHOUKI_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].choukiKnj = { value: data }
        break
      // 長期目標(期間)
      case OrX0042Const.COLUMN_NAME.CHO_KIKAN_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].choKikanKnj = { value: data }
        break
      // 短期目標
      case OrX0042Const.COLUMN_NAME.TANKI_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].tankiKnj = { value: data }
        break
      // 短期目標(期間)
      case OrX0042Const.COLUMN_NAME.TAN_KIKAN_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].tanKikanKnj = { value: data }
        break
      // サービス内容
      case OrX0042Const.COLUMN_NAME.KAIGO_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].kaigoKnj = { value: data }
        break
      // サービス種別
      case OrX0042Const.COLUMN_NAME.SV_SHU_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].svShuKnj = { value: data }
        break
      // ※２
      case OrX0042Const.COLUMN_NAME.JIGYO_NAME_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].jigyoNameKnj = { value: data }
        break
      // 頻度
      case OrX0042Const.COLUMN_NAME.HINDO_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].hindoKnj = { value: data }
        break
      // 期間
      case OrX0042Const.COLUMN_NAME.KIKAN_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].kikanKnj = { value: data }
        break
    }
  }
}

// ケース取込カラム
const caseImportColKey = ref<string>('')

/**
 * ケース取込カラム設定
 *
 * @param columnKey - ケース取込カラム
 */
function resetCaseImportCol(columnKey: string) {
  if (columnKey !== undefined) {
    caseImportColKey.value = columnKey
  } else {
    caseImportColKey.value = ''
  }
}

/**
 * 「生活全般の解決すべき課題(ニーズ)アイコンボタン」押下
 * 「長期目標アイコンボタン」押下
 * 「長期目標期間アイコンボタン」押下
 * 「短期目標アイコンボタン」押下
 * 「短期目標期間アイコンボタン」押下
 * 「サービス内容アイコンボタン」押下
 * 「頻度アイコンボタン」押下
 * 「期間アイコンボタン」押下
 * GUI00937_入力支援［ケアマネ］画面「確定」ボタンで戻った場合
 *
 * @param data - 入力支援［ケアマネ］の入力支援情報
 */
function onClickCareManagerConfirm(data: Or51775Type) {
  if (
    data !== undefined &&
    tableDataFilter.value !== undefined &&
    tableDataFilter.value.length > 0
  ) {
    switch (careManagerClickKey.value) {
      // 生活全般の解決すべき課題(ニーズ)
      case OrX0042Const.COLUMN_NAME.GUTAITEKI_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].gutaitekiKnj!.value = data.modelValue!
        break
      // 長期目標
      case OrX0042Const.COLUMN_NAME.CHOUKI_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].choukiKnj!.value = data.modelValue!
        break
      // 長期目標(期間)
      case OrX0042Const.COLUMN_NAME.CHO_KIKAN_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].choKikanKnj!.value = data.modelValue!
        break
      // 短期目標
      case OrX0042Const.COLUMN_NAME.TANKI_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].tankiKnj!.value = data.modelValue!
        break
      // 短期目標(期間)
      case OrX0042Const.COLUMN_NAME.TAN_KIKAN_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].tanKikanKnj!.value = data.modelValue!
        break
      // サービス内容
      case OrX0042Const.COLUMN_NAME.KAIGO_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].kaigoKnj!.value = data.modelValue!
        break
      // 頻度
      case OrX0042Const.COLUMN_NAME.HINDO_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].hindoKnj!.value = data.modelValue!
        break
      // 期間
      case OrX0042Const.COLUMN_NAME.KIKAN_KNJ_CAMEL:
        tableDataFilter.value[selectedItemIndex.value].kikanKnj!.value = data.modelValue!
        break
    }
  }
}

/**
 * GUI01018_「SDケアの入力支援アイコンボタン」押下
 *
 * @param data - SDケア作成の入力支援情報
 */
async function onClickCareCreateConfirm(data: Or53186Type) {
  if (
    data?.carePlan2List !== undefined &&
    tableDataFilter.value !== undefined &&
    tableDataFilter.value.length > 0
  ) {
    const res = data.carePlan2List
    if (res.length > 0) {
      //返却リスト.取込モード:追加の場合、選択行に上書。
      if (data.importMode === OrX0042Const.IMPORT_MODE.ADD) {
        //生活全般の解決すべき課題(ニーズ)
        tableDataFilter.value[selectedItemIndex.value].gutaitekiKnj!.value =
          res[0].lifeWholeSolution ?? ''
        //長期目標
        tableDataFilter.value[selectedItemIndex.value].choukiKnj!.value = res[0].longTermGoal ?? ''
        //短期目標
        tableDataFilter.value[selectedItemIndex.value].tankiKnj!.value = res[0].shortTermGoal ?? ''
        //サービス内容
        tableDataFilter.value[selectedItemIndex.value].kaigoKnj!.value =
          res[0].serviceContents ?? ''
        //サービス種別
        if (res[0].serviceKbn !== undefined) {
          let svShuKnj = ''
          for (const item of res[0].serviceKbn as unknown as {
            serviceId: string
            serviceName: string
          }[]) {
            svShuKnj += item.serviceName + OrX0042Const.ENTER
          }
          tableDataFilter.value[selectedItemIndex.value].svShuKnj!.value = svShuKnj
        }
      }
      //返却リスト.取込モード:行追加の場合、新しい行に値設定。
      if (data.importMode === OrX0042Const.IMPORT_MODE.ADD_LINE) {
        await nextTick()
        // 最終に新しい行を追加し、追加行を選択状態とする。
        const data: Keikasyo2 = {
          weekDailyRouting: t('label.absence'),
          updateKbn: UPDATE_KBN.CREATE,
          tableIndex: tableItem.value.length,
          gutaitekiKnj: { value: res[0].lifeWholeSolution ?? '' },
          svShuKnj: { value: '' },
          jigyoNameKnj: { value: '' },
          choukiKnj: { value: res[0].longTermGoal ?? '' },
          tankiKnj: { value: res[0].shortTermGoal ?? '' },
          kaigoKnj: { value: res[0].serviceContents ?? '' },
          choSYmd: { value: '' },
          choEYmd: { value: '' },
          tanSYmd: { value: '' },
          tanEYmd: { value: '' },
          kikanEYmd: { value: '' },
          kikanSYmd: { value: '' },
          checked: { modelValue: false },
        }
        //サービス種別
        if (res[0].serviceKbn !== undefined) {
          let svShuKnj = ''
          for (const item of res[0].serviceKbn as unknown as {
            serviceId: string
            serviceName: string
          }[]) {
            svShuKnj += item.serviceName + OrX0042Const.ENTER
          }
          data.svShuKnj!.value = svShuKnj
        }
        addNewLineByData(data)
      }
    }
  }
}

/**
 * GUI01031_「SDケアの入力支援アイコンボタン」押下
 *
 * @param data - SDケア選択画面の入力支援情報
 */
function onClickSdCareSelectConfirm(data: string) {
  if (data === Or51726Const.DEFAULT.SD_CARE_CREATE) {
    //SDケア選択画面で「SDケア作成」をクリックする場合
    openSdCareCreate()
  }
  if (data === Or51726Const.DEFAULT.MASTER_SIMPLE_REGISTRATION) {
    //SDケア選択画面で「マスタ簡易登録」をクリックする場合
    openSdCareMst()
  }
}

/**
 * GUI01015_課題と短期目標取込画面「確定」ボタン押下
 *
 * @param data - 課題と短期目標取込画面情報
 */
async function onClickIssuesTankiKnjConfirm(data: Or28567Type[]) {
  if (
    tableItem.value !== undefined &&
    tableItem.value.length >= 0 &&
    data !== undefined &&
    data.length > 0
  ) {
    //生活全般の解決すべき課題（ニーズ）,短期目標を設定する
    for (const item of data) {
      await nextTick()
      // 最終に新しい行を追加し、追加行を選択状態とする。
      const data: Keikasyo2 = {
        weekDailyRouting: t('label.absence'),
        updateKbn: UPDATE_KBN.CREATE,
        tableIndex: tableItem.value.length,
        gutaitekiKnj: { value: item.issues },
        svShuKnj: { value: '' },
        jigyoNameKnj: { value: '' },
        choukiKnj: { value: '' },
        tankiKnj: { value: item.shortTermGoal },
        kaigoKnj: { value: '' },
        choSYmd: { value: '' },
        choEYmd: { value: '' },
        tanSYmd: { value: '' },
        tanEYmd: { value: '' },
        kikanEYmd: { value: '' },
        kikanSYmd: { value: '' },
        checked: { modelValue: false },
      }
      addNewLineByData(data)
    }
  }
}

/**
 * GUI01028_課題とサービス内容取込画面「確定」ボタン押下
 *
 * @param data - 課題とサービス内容取込画面情報
 */
async function onClickIssuesKaigoKnjConfirm(data: Or10475Type[]) {
  if (
    tableItem.value !== undefined &&
    tableItem.value.length >= 0 &&
    data !== undefined &&
    data.length > 0
  ) {
    //生活全般の解決すべき課題（ニーズ）,サービス内容を設定する
    for (const item of data) {
      await nextTick()
      // 最終に新しい行を追加し、追加行を選択状態とする。
      const data: Keikasyo2 = {
        weekDailyRouting: t('label.absence'),
        updateKbn: UPDATE_KBN.CREATE,
        tableIndex: tableItem.value.length,
        gutaitekiKnj: { value: item.kadaiKnj },
        svShuKnj: { value: '' },
        jigyoNameKnj: { value: '' },
        choukiKnj: { value: '' },
        tankiKnj: { value: '' },
        kaigoKnj: { value: item.serviceMemoKnj },
        choSYmd: { value: '' },
        choEYmd: { value: '' },
        tanSYmd: { value: '' },
        tanEYmd: { value: '' },
        kikanEYmd: { value: '' },
        kikanSYmd: { value: '' },
        checked: { modelValue: false },
      }
      addNewLineByData(data)
    }
  }
}

/**
 * GUI01026_課題と目標取込画面「確定」ボタン押下
 *
 * @param data - 課題と目標取込画面情報
 */
async function onClickIssuesGoalImportConfirm(data: Or10477Type[]) {
  if (
    tableItem.value !== undefined &&
    tableItem.value.length >= 0 &&
    data !== undefined &&
    data.length > 0
  ) {
    //生活全般の解決すべき課題（ニーズ）,短期目標,長期目標,サービス内容を設定する
    for (const item of data) {
      await nextTick()
      // 最終に新しい行を追加し、追加行を選択状態とする。
      const data: Keikasyo2 = {
        weekDailyRouting: t('label.absence'),
        updateKbn: UPDATE_KBN.CREATE,
        tableIndex: tableItem.value.length,
        gutaitekiKnj: { value: item.kadaiKnj },
        svShuKnj: { value: '' },
        jigyoNameKnj: { value: '' },
        choukiKnj: { value: item.choukiKnj },
        tankiKnj: { value: item.tankiKnj },
        kaigoKnj: { value: item.careKnj },
        choSYmd: { value: '' },
        choEYmd: { value: '' },
        tanSYmd: { value: '' },
        tanEYmd: { value: '' },
        kikanEYmd: { value: '' },
        kikanSYmd: { value: '' },
        checked: { modelValue: false },
      }
      addNewLineByData(data)
    }
  }
}

/**
 * GUI01027_フリーアセスメント取込画面「確定」ボタン押下
 *
 * @param data - フリーアセスメント取込画面情報
 */
async function onClickFreeAssessmentImportConfirm(data: Or28650Type[]) {
  if (
    tableItem.value !== undefined &&
    tableItem.value.length >= 0 &&
    data !== undefined &&
    data.length > 0
  ) {
    //課題立案様式設定マスタで設定した連動項目に返却データを設定する。
    for (const item of data) {
      await nextTick()
      // 最終に新しい行を追加し、追加行を選択状態とする。
      const data: Keikasyo2 = {
        weekDailyRouting: t('label.absence'),
        updateKbn: UPDATE_KBN.CREATE,
        tableIndex: tableItem.value.length,
        gutaitekiKnj: { value: item.issues },
        svShuKnj: { value: '' },
        jigyoNameKnj: { value: '' },
        choukiKnj: { value: item.longTermGoals },
        tankiKnj: { value: item.shortTermGoal },
        kaigoKnj: { value: item.careContent },
        choSYmd: { value: '' },
        choEYmd: { value: '' },
        tanSYmd: { value: '' },
        tanEYmd: { value: '' },
        kikanEYmd: { value: '' },
        kikanSYmd: { value: '' },
        checked: { modelValue: false },
      }
      addNewLineByData(data)
    }
  }
}

/**
 * GUI01024_曜日取込画面「確定」ボタン押下
 *
 * @param data - 曜日取込画面情報
 */
function onClickYobiConfirm(data: { listData: boolean[]; textData: string }) {
  if (
    data !== undefined &&
    tableDataFilter.value !== undefined &&
    tableDataFilter.value.length > 0
  ) {
    let hindoKnj = ''
    if (data.listData !== undefined && data.listData.length === 7) {
      if (data.listData[0]) {
        hindoKnj += t('label.weekly-plan-day-short-sunday')
      }
      if (data.listData[1]) {
        hindoKnj += t('label.weekly-plan-day-short-monday')
      }
      if (data.listData[2]) {
        hindoKnj += t('label.weekly-plan-day-short-tuesday')
      }
      if (data.listData[3]) {
        hindoKnj += t('label.weekly-plan-day-short-wednesday')
      }
      if (data.listData[4]) {
        hindoKnj += t('label.weekly-plan-day-short-thursday')
      }
      if (data.listData[5]) {
        hindoKnj += t('label.weekly-plan-day-short-friday')
      }
      if (data.listData[6]) {
        hindoKnj += t('label.weekly-plan-day-short-saturday')
      }
    }
    if (hindoKnj !== '') {
      //返却情報を画面の頻度に上書きする。
      tableDataFilter.value[selectedItemIndex.value].hindoKnj!.value = hindoKnj
    } else {
      //返却情報を画面の頻度に上書きする。
      tableDataFilter.value[selectedItemIndex.value].hindoKnj!.value = data.textData
    }
  }
}

/**
 * 「行挿入ボタン」押下
 */
async function insertNewLine() {
  await nextTick()
  if (Array.isArray(tableItem.value) && tableItem.value.length > 0) {
    // 選択行の上に新しい行を追加し、追加行を選択状態とする。
    const insertLine: Keikasyo2 = {
      weekDailyRouting: t('label.absence'),
      updateKbn: UPDATE_KBN.CREATE,
      tableIndex: tableItem.value?.length ?? 0,
      svShuKnj: { value: '' },
      jigyoNameKnj: { value: '' },
      choSYmd: { value: '' },
      choEYmd: { value: '' },
      tanSYmd: { value: '' },
      tanEYmd: { value: '' },
      kikanEYmd: { value: '' },
      kikanSYmd: { value: '' },
      checked: { modelValue: false },
    }
    const index =
      tableItem.value?.findIndex(
        (e: { tableIndex: number }) => e.tableIndex === selectedItemTableIndex.value
      ) ?? 0
    tableItem.value.splice(index, 0, insertLine)
    localOneway.mo01338Oneway.itemLabel =
      selectedItemIndex.value + 1 + SPACE_FORWARD_SLASH + tableDataFilter.value.length
    void nextTick(() => {
      if (lineZoomBigState.value) {
        const textareas = document.querySelectorAll<HTMLTextAreaElement>(
          '.careplan2-table tbody > tr'
        )
        textareas.forEach((textarea) => textarea.style.setProperty('height', '265px', 'important'))
      }
    })
  }
}
/**
 * 「行複写ボタン」押下
 */
async function cpyLine() {
  await nextTick()
  if (Array.isArray(tableItem.value) && tableItem.value.length > 0) {
    // 選択行の情報をコピーして最終に新しい行を追加し、追加行を選択状態とする。
    const sourceLine = tableDataFilter.value.find(
      (item) => item.tableIndex === selectedItemTableIndex.value
    )
    if (sourceLine) {
      const cpyLine = { ...sourceLine }
      cpyLine.updateKbn = UPDATE_KBN.CREATE
      cpyLine.tableIndex = tableItem.value?.length ?? 0
      tableItem.value.push(cpyLine)
      selectedItemIndex.value = tableDataFilter.value.length - 1
      localOneway.mo01338Oneway.itemLabel =
        selectedItemIndex.value + 1 + SPACE_FORWARD_SLASH + tableDataFilter.value.length
      void nextTick(() => {
        if (lineZoomBigState.value) {
          const textareas = document.querySelectorAll<HTMLTextAreaElement>(
            '.careplan2-table tbody > tr'
          )
          textareas.forEach((textarea) =>
            textarea.style.setProperty('height', '265px', 'important')
          )
        }
      })
    }
  }
}

/**
 * 「行削除ボタン」押下
 */
async function deleteLine() {
  // 以下のメッセージを表示: i.cmn.10219
  const rs = await openConfirmDialog(or21814.value.uniqueCpId, {
    // ダイアログタイトル
    dialogTitle: t('label.confirm'),
    // ダイアログテキスト
    dialogText: t('message.i-cmn-10219'),
    // 第1ボタンタイプ
    firstBtnType: 'normal1',
    // 第1ボタンラベル
    firstBtnLabel: t('btn.yes'),
    // 第2ボタンボタンタイプ
    secondBtnType: 'normal3',
    // 第2ボタンラベル
    secondBtnLabel: t('btn.no'),
    // 第3ボタンタイプ
    thirdBtnType: 'blank',
  })
  if (rs.firstBtnClickFlg) {
    // AC028-2 を実行
    await confirmDeleteLine()
  }
}
/**
 * 「行削除ボタン」押下,はい場合後続処理
 */
async function confirmDeleteLine() {
  await nextTick()
  const data = tableItem.value.find(
    (e: { tableIndex: number }) => e.tableIndex === selectedItemTableIndex.value
  )
  if (data !== undefined) {
    data.updateKbn = UPDATE_KBN.DELETE
    if (selectedItemIndex.value >= tableDataFilter.value.length - 1) {
      selectedItemIndex.value = tableDataFilter.value.length - 1
    }
    selectedItemTableIndex.value = tableDataFilter.value[selectedItemIndex.value].tableIndex
    localOneway.mo01338Oneway.itemLabel =
      selectedItemIndex.value + 1 + SPACE_FORWARD_SLASH + tableDataFilter.value.length
  }
}
/**
 * 「選択列一括上書ボタン」押下: 現行列の値を取得し、選択行以下に反映する
 */
function confirmBatchSet() {
  // 期間の場合
  const srcLine = tableDataFilter.value[selectedItemIndex.value]
  switch (selectedColKey.value) {
    // 期間
    case OrX0042Const.COLUMN_NAME.KIKAN_KNJ_CAMEL:
      for (let i = selectedItemIndex.value + 1; i < tableDataFilter.value.length; i++) {
        tableDataFilter.value[i].kikanSYmd = srcLine.kikanSYmd
        tableDataFilter.value[i].kikanEYmd = srcLine.kikanEYmd
        tableDataFilter.value[i].kikanKnj = srcLine.kikanKnj
      }
      break
    // 短期目標期間
    case OrX0042Const.COLUMN_NAME.TANKI_KNJ_CAMEL:
      for (let i = selectedItemIndex.value + 1; i < tableDataFilter.value.length; i++) {
        tableDataFilter.value[i].tanKikanKnj = srcLine.tanKikanKnj
        tableDataFilter.value[i].tanSYmd = srcLine.tanSYmd
        tableDataFilter.value[i].tanEYmd = srcLine.tanEYmd
      }
      break
    // 長期目標期間
    case OrX0042Const.COLUMN_NAME.CHOUKI_KNJ_CAMEL:
      for (let i = selectedItemIndex.value + 1; i < tableDataFilter.value.length; i++) {
        tableDataFilter.value[i].choKikanKnj = srcLine.choKikanKnj
        tableDataFilter.value[i].choSYmd = srcLine.choSYmd
        tableDataFilter.value[i].choEYmd = srcLine.choEYmd
      }
      break
    // ※2
    case OrX0042Const.COLUMN_NAME.JIGYO_NAME_KNJ_CAMEL:
      for (let i = selectedItemIndex.value + 1; i < tableDataFilter.value.length; i++) {
        tableDataFilter.value[i].jigyoNameKnj = srcLine.jigyoNameKnj
      }
      break
    // 頻度
    case OrX0042Const.COLUMN_NAME.HINDO_KNJ_CAMEL:
      for (let i = selectedItemIndex.value + 1; i < tableDataFilter.value.length; i++) {
        tableDataFilter.value[i].hindoKnj = srcLine.hindoKnj
      }
      break
    // サービス種別
    case OrX0042Const.COLUMN_NAME.SV_SHU_KNJ_CAMEL:
      for (let i = selectedItemIndex.value + 1; i < tableDataFilter.value.length; i++) {
        tableDataFilter.value[i].svShuKnj = srcLine.svShuKnj
      }
      break
  }
  cancelBatchSet()
}

/**
 * 選択された列の背景色を削除する。
 */
function cancelBatchSet() {
  selectedColKey.value = ''
  const topLine = document.querySelectorAll<HTMLTextAreaElement>('.batch-able')
  // 選択行の値(背景赤色)で
  topLine.forEach((col) => col.style.setProperty('background', 'none', ''))
}
/**
 * 「アセスメントの入力支援アイコンボタン」押下
 */
function onClickAssessment() {
  // 共通情報.ケアプラン方式=1:包括的自立支援プログラム 且つ 計画書(2)マスタ.取込設定＝０:短期目標の場合
  if (
    commonInfoData.carePlanMethod ===
      OrX0042Const.CARE_PLAN_METHOD.COMPREHENSIVE_INDEPENDENCE_SUPPORT &&
    commonInfoData.carePlan2Mastr.importSettings === OrX0042Const.IMPORT_SETTINGS.TANKI_KNG
  ) {
    openIssuesTankiKnj()
  }
  // 共通情報.ケアプラン方式=1:包括的自立支援プログラム 且つ 計画書(2)マスタ.取込設定＝1:サービス内容の場合
  if (
    commonInfoData.carePlanMethod ===
      OrX0042Const.CARE_PLAN_METHOD.COMPREHENSIVE_INDEPENDENCE_SUPPORT &&
    commonInfoData.carePlan2Mastr.importSettings === OrX0042Const.IMPORT_SETTINGS.KAIGO_KNG
  ) {
    openIssuesKaigoKnj()
  }
  // TODO 共通情報.ケアプラン方式=2:居宅サービス計画の場合
  // 共通情報.ケアプラン方式=7:フリーアセスメントの場合
  if (commonInfoData.carePlanMethod === OrX0042Const.CARE_PLAN_METHOD.FREE_ASSESSMENT) {
    openFreeAssessmentImport()
  }
  // 共通情報.ケアプラン方式=8:情報収集の場合
  if (commonInfoData.carePlanMethod === OrX0042Const.CARE_PLAN_METHOD.COLLECT_INFO) {
    // GUI01007_課題検討用紙取込画面をポップアップで起動する。
    // 入力パラメータ: 共通情報.種別ID
    localOneway.or00586Oneway.issuesConsiderBlankFormInfo = {
      syubetsuId: commonInfoData.shubetsuId,
      svJigyoId: commonInfoData.svJigyoId,
      shisetuId: commonInfoData.shisetsuId,
      userId: useSystemCommonsStore().getUserId ?? '',
      kikanFlg: localOneway.orX0073Oneway.periodManageFlag ?? '',
      sc1Id: localOneway.orX0073Oneway.planTargetPeriodId,
      parentViewType: '2',
      mstKbn: '1',
    }
    Or00586Logic.state.set({
      uniqueCpId: or00586.value.uniqueCpId,
      state: { isOpen: true },
    })
  }
  //共通情報.ケアプラン方式=9:インタラインの場合
  if (commonInfoData.carePlanMethod === OrX0042Const.CARE_PLAN_METHOD.INTER_LINE) {
    openIssuesGoalImport()
  }
}
/**
 * 入力支援ポップアップの表示状態を返すComputed
 */
const showDialogOr00586 = computed(() => {
  return Or00586Logic.state.get(or00586.value.uniqueCpId)?.isOpen ?? false
})
/**
 * GUI01007_課題検討用紙取込画面閉じるの場合
 */
async function onOr00586Close() {
  // AC024と同じ（行追加）
  await addNewLine()
  // AC032-5の場合
  // 生活全般の解決すべき課題（ニーズ）,サービス内容を設定する
  const gutaitekiKnjArr = []
  for (const data of local.or00586.issuesConsiderBlankFormList) {
    gutaitekiKnjArr.push(data.kadaiKnj)
  }
  const gutaitekiKnj = gutaitekiKnjArr.join('\r\n')
  tableItem.value[tableItem.value.length - 1].gutaitekiKnj = {
    value: gutaitekiKnj,
  }
  tableItem.value[tableItem.value.length - 1].kaigoKnj = {
    value: gutaitekiKnj,
  }
}
/**
 * 「週間日課アイコンボタン」押下
 */
async function onWeekDailyRoutingClick() {
  // 選択行.週間日課が【無】の場合
  const line = tableDataFilter.value[selectedItemIndex.value]
  if (line.weekDailyRouting === t('label.absence')) {
    // 以下のメッセージを表示: i.cmn.11281
    await openConfirmDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-11281'),
      // 第1ボタンタイプ
      firstBtnType: 'normal1',
      // 第1ボタンラベル
      firstBtnLabel: t('btn.yes'),
      // 第2ボタンボタンタイプ
      secondBtnType: 'blank',
      // 第3ボタンタイプ
      thirdBtnType: 'blank',
    })
  }
}

onMounted(async () => {
  initColumnFlag()
  await nextTick()
})
/**
 * ページの再設定
 */
async function resetMo01338OnewayItemLabel() {
  await nextTick()
  if (tableDataFilter.value.length > 0) {
    // データ-ページング
    selectedRows.value = []
    local.mo00018.modelValue = false
    local.mo00018.modelValue = true
    allCheck(local.mo00018)
    localOneway.mo01338Oneway.itemLabel =
      selectedItemIndex.value + 1 + SPACE_FORWARD_SLASH + tableDataFilter.value.length
  } else {
    localOneway.mo01338Oneway.itemLabel =
      tableDataFilter.value.length + SPACE_FORWARD_SLASH + tableDataFilter.value.length
  }
}

/**
 * 全選択
 *
 * @param mo00018 - チェックボックス
 */
const allCheck = (mo00018: Mo00018Type) => {
  if (tableDataFilter.value.length > 0) {
    if (mo00018.modelValue) {
      tableItem.value?.forEach((item: Keikasyo2) => {
        item.checked = { modelValue: true }
      })
      selectedRows.value = tableItem.value
    } else {
      tableItem.value?.forEach((item: Keikasyo2) => {
        item.checked = { modelValue: false }
      })
      selectedRows.value = []
    }
  } else {
    local.mo00018.modelValue = false
  }
}

/**
 * 行選択チェックボックスチェックした時の処理
 */
const toggleSelectRow = () => {
  selectedRows.value = []
  tableItem.value?.forEach((item: Keikasyo2) => {
    if (item.checked.modelValue === true) {
      selectedRows.value.push(item)
    }
  })
  if (selectedRows.value.length === tableDataFilter.value.length) {
    local.mo00018.modelValue = true
  } else {
    local.mo00018.modelValue = false
  }
}

/**
 * 確認ダイアログ開く
 *
 * @param uniqueCpId - uniqueCpId
 *
 * @param state - 確認ダイアログOneWayBind領域
 */
function openConfirmDialog(uniqueCpId: string, state: Or21814OnewayType) {
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise<Or21814EventType>((resolve) => {
    or21814ResolvePromise = resolve
  })
}

/**
 * テーブルデータの初期化
 *
 * @param orX0073Oneway - データ
 */
function initData(orX0073Oneway: OrX0073OnewayType) {
  initFlg = true
  tableItem.value = orX0073Oneway.tableItem
  localOneway.orX0073Oneway.cpyFlg = orX0073Oneway.cpyFlg
  localOneway.orX0073Oneway.periodManageFlag = orX0073Oneway.periodManageFlag
  localOneway.orX0073Oneway.planTargetPeriodId = orX0073Oneway.planTargetPeriodId
  localOneway.orX0073Oneway.columnMinWidth = orX0073Oneway.columnMinWidth
  if (tableDataFilter.value.length > 0) {
    localOneway.mo01338Oneway.itemLabel =
      selectedItemIndex.value + 1 + SPACE_FORWARD_SLASH + tableDataFilter.value.length
  }
  initFlg = false
}

/**
 * 「担当者アイコンボタン」押下
 *
 */
function onClickManager() {
  //モード：1
  local.or27235Type.mode = 1
  //利用者ID:共通情報.利用者ID
  local.or27235Type.userId = commonInfoData.userId
  //担当者：画面.担当者
  local.or27235Type.manager = tableDataFilter.value[selectedItemIndex.value].svShuKnj!.value
  // GUI01021_担当者入力支援ポップアップを開く
  Or27235Logic.state.set({
    uniqueCpId: or27235.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI01021_担当者入力支援確定ボタン押下
 *
 * @param value - 担当者入力支援情報
 */
function handleOr27235Confirm(value: string) {
  tableDataFilter.value[selectedItemIndex.value].svShuKnj!.value = value
}

/**
 * GUI01021_担当者入力支援ポップアップの表示状態を返す
 */
const showDialogOr27235 = computed(() => {
  return Or27235Logic.state.get(or27235.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 「PL取込の入力支援アイコンボタン」押下
 *
 */
function onClickPl() {
  // 共通情報.処理名
  localOneway.or28720OnewayType.processFirstName = commonInfoData.processFirstName
  // GUI01029_プロブレムリスト取込入力支援ポップアップを開く
  Or28720Logic.state.set({
    uniqueCpId: or28720.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI01029_プロブレムリスト取込入力支援ポップアップの表示状態を返す
 */
const showDialogOr28720 = computed(() => {
  return Or28720Logic.state.get(or28720.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 「課題整理総括の入力支援アイコンボタン」押下
 *
 */
function onClickIssues() {
  // 共通情報.施設ID
  localOneway.or27362OnewayType.organizingIssuesImportType.shisetuId = commonInfoData.shisetsuId
  // 共通情報.利用者ID
  localOneway.or27362OnewayType.organizingIssuesImportType.userId = commonInfoData.userId
  // 共通情報.事業所ID
  localOneway.or27362OnewayType.organizingIssuesImportType.svJigyoId = commonInfoData.officeId
  // 共通情報.種別ID
  localOneway.or27362OnewayType.organizingIssuesImportType.syubetsuId = commonInfoData.shubetsuId
  // GUI01023_課題整理総括取込画面入力支援ポップアップを開く
  Or27362Logic.state.set({
    uniqueCpId: or27362.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI01023_課題整理総括取込画面入力支援ポップアップの表示状態を返す
 */
const showDialogOr27362 = computed(() => {
  return Or27362Logic.state.get(or27362.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 「サービス種別アイコンボタン」「(サービス種別)利用票取込アイコンボタン」「※2アイコンボタン」押下
 *
 * @param screenDisplayMode - 画面表示モード
 */
function onClickSvKbn(screenDisplayMode: string) {
  // 画面表示モード 1：サービス種別
  localOneway.or50429OnewayType.screenDisplayMode = screenDisplayMode
  // サービス種別：画面.サービス種別
  localOneway.or50429OnewayType.serviceKind =
    tableDataFilter.value[selectedItemIndex.value].svShuKnj!.value
  // 事業所名：画面.※２
  localOneway.or50429OnewayType.officeName =
    tableDataFilter.value[selectedItemIndex.value].jigyoNameKnj!.value
  //「(サービス種別)利用票取込アイコンボタン」押下
  if (screenDisplayMode === '3') {
    // 基準年月：画面.作成年月
    localOneway.or50429OnewayType.createDate = commonInfoData.commonDate.substring(
      0,
      commonInfoData.commonDate.lastIndexOf('/')
    )
  }
  // GUI00957_サービス種別入力支援入力支援ポップアップを開く
  Or50429Logic.state.set({
    uniqueCpId: or50429.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI00957_サービス種別入力支援ポップアップの表示状態を返す
 */
const showDialogOr50429 = computed(() => {
  return Or50429Logic.state.get(or50429.value.uniqueCpId)?.isOpen ?? false
})

// 担当者・頻度フラグ
const managerImportFlg = ref<boolean>(false)

/**
 * 「担当者（取込）アイコンボタン」押下
 *
 */
function onClickManagerImport() {
  //計画書（２）行データID
  const itemKs22Id = tableDataFilter.value[selectedItemIndex.value].ks22Id
  const selectedJobType = []
  if (
    tableDataFilter.value[selectedItemIndex.value].tantoList !== undefined &&
    tableDataFilter.value[selectedItemIndex.value].tantoList!.length > 0
  ) {
    for (const item of tableDataFilter.value[selectedItemIndex.value].tantoList ?? []) {
      if (item.ks22Id === itemKs22Id) {
        selectedJobType.push({ shokushuId: item.shokushuId })
      }
    }
  }
  //職種（担当者）リスト
  local.or27043Type.selectedJobType = selectedJobType
  if (
    tableDataFilter.value[selectedItemIndex.value].yobiList !== undefined &&
    tableDataFilter.value[selectedItemIndex.value].yobiList!.length > 0
  ) {
    for (const item of tableDataFilter.value[selectedItemIndex.value].yobiList ?? []) {
      if (item.ks22Id === itemKs22Id) {
        if (item.youbi !== undefined) {
          local.or27043Type.frequency = item.youbi
        }
        if (item.kaishiJikan !== undefined) {
          // 計画書（２）サービス、曜日情報.開始時間
          local.or27043Type.startTime.value = item.kaishiJikan
        }
        if (item.shuuryouJikan !== undefined) {
          // 計画書（２）サービス、曜日情報.終了時間
          local.or27043Type.endTime.value = item.shuuryouJikan
        }
      }
    }
  }
  // GUI01022_担当者・頻度画面入力支援ポップアップを開く
  Or27043Logic.state.set({
    uniqueCpId: or27043.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI01022_担当者・頻度画面入力支援ポップアップの表示状態を返す
 */
const showDialogOr27043 = computed(() => {
  return Or27043Logic.state.get(or27043.value.uniqueCpId)?.isOpen ?? false
})

// 表示順変更計画書（2）フラグ
const sortModifiedFlg = ref<boolean>(false)

/**
 * 「表示順変更アイコンボタン」押下
 *
 */
function onClickSortModified() {
  //共通情報.計画書様式フラグ
  localOneway.or10860OnewayType.carePlanStyle = Number(OrX0042Const.SYS_ABBR.CPN)
  //共通情報.計画書(2)マスタ.番号フラグ
  localOneway.or10860OnewayType.numberFlag = Number(commonInfoData.carePlan2Mastr.numberFlg)
  //画面．計画書（２）データリスト
  const sectionList: sectionItem[] = []
  let num = 1
  for (const item of tableDataFilter.value) {
    const section: sectionItem = {
      displayOrder: num + '',
      orderBackup: num + '',
      issuesNumber: item.kadaiNo?.value,
      lifeWholeSolutionIssuesNeeds: item.gutaitekiKnj?.value,
      longTermGoal: item.choukiKnj?.value,
      longTermGoalPeriod: '',
      shortTermGoal: item.tankiKnj?.value,
      shortTermGoalPeriod: '',
      number: item.kaigoNo?.value,
      serviceContents: item.kaigoKnj?.value,
      manager: item.svShuKnj?.value,
      frequency: item.hindoKnj?.value,
      period: '',
      weekDailyRoutine: item.weekDailyRouting,
      asterisk1: t('label.circle'),
      serviceType: item.svShuKnj?.value,
      asterisk2: item.jigyoNameKnj?.value,
    }
    if (columnFlag.value.showPeriodStartYmdFlg) {
      //長期目標期間
      section.longTermGoalPeriod = item.choSYmd?.value
      //短期目標期間
      section.shortTermGoalPeriod = item.tanSYmd?.value
      //期間
      section.period = item.kikanSYmd?.value
    }
    if (columnFlag.value.showPeriodLabelFlg && columnFlag.value.showPeriodEndYmdFlg) {
      //長期目標期間
      section.longTermGoalPeriod =
        section.longTermGoalPeriod + t('label.wave-dash') + item.choEYmd?.value
      //短期目標期間
      section.shortTermGoalPeriod =
        section.shortTermGoalPeriod + t('label.wave-dash') + item.tanEYmd?.value
      //期間
      section.period = section.period + t('label.wave-dash') + item.kikanEYmd?.value
    }
    //長期目標期間
    section.longTermGoalPeriod = section.longTermGoalPeriod + (item.choKikanKnj?.value ?? '')
    //短期目標期間
    section.shortTermGoalPeriod = section.shortTermGoalPeriod + (item.tanKikanKnj?.value ?? '')
    //期間
    section.period = section.period + (item.kikanKnj?.value ?? '')
    sectionList.push(section)
    num++
  }
  local.or10860Type.sectionList = sectionList
  // GUI01032_表示順変更計画書（2）入力支援ポップアップを開く
  Or10860Logic.state.set({
    uniqueCpId: or10860.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI01032_表示順変更計画書（2）画面入力支援ポップアップの表示状態を返す
 */
const showDialogOr10860 = computed(() => {
  return Or10860Logic.state.get(or10860.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 「ケース取込の入力支援アイコンボタン」押下
 *
 */
function onClickCaseImport() {
  //ポップアップ画面起動の判断。
  //選択された取込項目の種類は「テキストエリア」の場合、処理続行。以外の場合、処理終了。
  const keyList = caseImportWhiteList.value.map((colInfo) => colInfo.key)
  if (
    caseImportColKey.value === '' ||
    caseImportColKey.value === undefined ||
    !keyList.includes(caseImportColKey.value)
  ) {
    return
  }
  //共通情報.システム略称≠'CPN'の場合
  if (commonInfoData.sysAbbr !== OrX0042Const.SYS_ABBR.CPN) {
    //GUI02259_適用事業所の選択画面をポップアップで起動する。
  } else {
    //共通情報.システム略称='CPN'の場合
    //サービス事業者コード情報を取得する。
    // 共通情報.法人ID
    localOneway.or28256OnewayType.houjinId = commonInfoData.houjinId
    // 共通情報.施設ID
    localOneway.or28256OnewayType.sisetsuId = commonInfoData.shisetsuId
    // 共通情報.利用者ID
    localOneway.or28256OnewayType.userId = commonInfoData.userId
    // 画面抽出期間開始日
    localOneway.or28256OnewayType.kaisihi.value =
      tableDataFilter.value[selectedItemIndex.value].kikanSYmd?.value ?? ''
    // 画面抽出期間終了日
    localOneway.or28256OnewayType.syuuryouhi.value =
      tableDataFilter.value[selectedItemIndex.value].kikanEYmd?.value ?? ''
    // 選択されている日誌系マスタID
    localOneway.or28256OnewayType.sheetno = '1'
    // 選択されている分類コード
    localOneway.or28256OnewayType.bunruiCd = '1'
    // 選択されている項目番号
    localOneway.or28256OnewayType.koumokuno = 0
    // 共通情報.システムコード
    localOneway.or28256OnewayType.syscd = commonInfoData.syscd
    switch (caseImportColKey.value) {
      // 生活全般の解決すべき課題(ニーズ)
      case OrX0042Const.COLUMN_NAME.GUTAITEKI_KNJ_CAMEL:
        // 取込項目のタイトル
        localOneway.or28256OnewayType.targetItem = t('label.life-whole-solution-issues')
        // 取込項目のデータ
        localOneway.or28256OnewayType.caseInformation =
          tableDataFilter.value[selectedItemIndex.value].gutaitekiKnj?.value ?? ''
        break
      // 長期目標
      case OrX0042Const.COLUMN_NAME.CHOUKI_KNJ_CAMEL:
        // 取込項目のタイトル
        localOneway.or28256OnewayType.targetItem = t('label.long-term-goal')
        // 取込項目のデータ
        localOneway.or28256OnewayType.caseInformation =
          tableDataFilter.value[selectedItemIndex.value].choukiKnj?.value ?? ''
        break
      // 長期目標(期間)
      case OrX0042Const.COLUMN_NAME.CHO_KIKAN_KNJ_CAMEL:
        // 取込項目のタイトル
        localOneway.or28256OnewayType.targetItem = t('label.care-plan2-period')
        // 取込項目のデータ
        localOneway.or28256OnewayType.caseInformation =
          tableDataFilter.value[selectedItemIndex.value].choKikanKnj?.value ?? ''
        break
      // 短期目標
      case OrX0042Const.COLUMN_NAME.TANKI_KNJ_CAMEL:
        // 取込項目のタイトル
        localOneway.or28256OnewayType.targetItem = t('label.short-term-goal')
        // 取込項目のデータ
        localOneway.or28256OnewayType.caseInformation =
          tableDataFilter.value[selectedItemIndex.value].tankiKnj?.value ?? ''
        break
      // 短期目標(期間)
      case OrX0042Const.COLUMN_NAME.TAN_KIKAN_KNJ_CAMEL:
        // 取込項目のタイトル
        localOneway.or28256OnewayType.targetItem = t('label.care-plan2-period')
        // 取込項目のデータ
        localOneway.or28256OnewayType.caseInformation =
          tableDataFilter.value[selectedItemIndex.value].tanKikanKnj?.value ?? ''
        break
      // サービス内容
      case OrX0042Const.COLUMN_NAME.KAIGO_KNJ_CAMEL:
        // 取込項目のタイトル
        localOneway.or28256OnewayType.targetItem = t('label.service-contents')
        // 取込項目のデータ
        localOneway.or28256OnewayType.caseInformation =
          tableDataFilter.value[selectedItemIndex.value].kaigoKnj?.value ?? ''
        break
      // サービス種別
      case OrX0042Const.COLUMN_NAME.SV_SHU_KNJ_CAMEL:
        // 取込項目のタイトル
        localOneway.or28256OnewayType.targetItem = t('label.service-kbn')
        // 取込項目のデータ
        localOneway.or28256OnewayType.caseInformation =
          tableDataFilter.value[selectedItemIndex.value].svShuKnj?.value ?? ''
        break
      // ※２
      case OrX0042Const.COLUMN_NAME.JIGYO_NAME_KNJ_CAMEL:
        // 取込項目のタイトル
        localOneway.or28256OnewayType.targetItem = t('label.star-2')
        // 取込項目のデータ
        localOneway.or28256OnewayType.caseInformation =
          tableDataFilter.value[selectedItemIndex.value].jigyoNameKnj?.value ?? ''
        break
      // 頻度
      case OrX0042Const.COLUMN_NAME.HINDO_KNJ_CAMEL:
        // 取込項目のタイトル
        localOneway.or28256OnewayType.targetItem = t('label.frequency')
        // 取込項目のデータ
        localOneway.or28256OnewayType.caseInformation =
          tableDataFilter.value[selectedItemIndex.value].hindoKnj?.value ?? ''
        break
      // 期間
      case OrX0042Const.COLUMN_NAME.KIKAN_KNJ_CAMEL:
        // 取込項目のタイトル
        localOneway.or28256OnewayType.targetItem = t('label.period')
        // 取込項目のデータ
        localOneway.or28256OnewayType.caseInformation =
          tableDataFilter.value[selectedItemIndex.value].kikanKnj?.value ?? ''
        break
    }
    // GUI01016_ケース一覧入力支援ポップアップを開く
    Or28256Logic.state.set({
      uniqueCpId: or28256.value.uniqueCpId,
      state: {
        isOpen: true,
        items: [],
      },
    })
  }
}

/**
 * GUI01016_ケース一覧画面ポップアップの表示状態を返す
 */
const showDialogOr28256 = computed(() => {
  return Or28256Logic.state.get(or28256.value.uniqueCpId)?.isOpen ?? false
})

//入力支援［ケアマネ］画面押下区分
const careManagerClickKey = ref<string>('')
// 入力支援［ケアマネ］フラグ
const careManagerFlg = ref<boolean>(false)

/**
 * 「生活全般の解決すべき課題(ニーズ)アイコンボタン」押下
 * 「長期目標アイコンボタン」押下
 * 「長期目標期間アイコンボタン」押下
 * 「短期目標アイコンボタン」押下
 * 「短期目標期間アイコンボタン」押下
 * 「サービス内容アイコンボタン」押下
 * 「頻度アイコンボタン」押下
 * 「期間アイコンボタン」押下
 *
 * @param colKey - 入力支援［ケアマネ］画面押下区分
 */
function onClickCareManager(colKey: string) {
  careManagerClickKey.value = colKey
  // 画面ID:"GUI01014"
  localOneway.or51775OnewayType.screenId = OrX0042Const.SCREEN_ID
  // 分類ID:""
  // 大分類CD:820
  localOneway.or51775OnewayType.t1Cd = OrX0042Const.CLASSIFICATION_CD.BIG_820
  // 小分類CD:0
  localOneway.or51775OnewayType.t3Cd = OrX0042Const.CLASSIFICATION_CD.SMALL_0
  // テーブル名:"cpn_tuc_cks22"
  localOneway.or51775OnewayType.tableName = OrX0042Const.TABLE_NAME
  // アセスメント方式:親画面.アセスメント方式
  localOneway.or51775OnewayType.assessmentMethod = commonInfoData.carePlanMethod
  // 利用者ID:共通情報.利用者ID
  localOneway.or51775OnewayType.userId = commonInfoData.userId
  switch (careManagerClickKey.value) {
    // 生活全般の解決すべき課題(ニーズ)
    case OrX0042Const.COLUMN_NAME.GUTAITEKI_KNJ_CAMEL:
      // タイトル:"生活全般の解決すべき課題（ニーズ）"
      localOneway.or51775OnewayType.title = t('label.life-whole-solution-issues-needs')
      // 中分類CD:1
      localOneway.or51775OnewayType.t2Cd = OrX0042Const.CLASSIFICATION_CD.CENTRE_1
      // カラム名:"gutaiteki_knj"
      localOneway.or51775OnewayType.columnName = OrX0042Const.COLUMN_NAME.GUTAITEKI_KNJ_SNAKE
      // 文章内容:【変数】入力支援項目の既存内容
      local.or51775Type.modelValue =
        tableDataFilter.value[selectedItemIndex.value].gutaitekiKnj!.value
      break
    // 長期目標
    case OrX0042Const.COLUMN_NAME.CHOUKI_KNJ_CAMEL:
      // タイトル:"長期目標"
      localOneway.or51775OnewayType.title = t('label.long-term-goal')
      // 中分類CD:2
      localOneway.or51775OnewayType.t2Cd = OrX0042Const.CLASSIFICATION_CD.CENTRE_2
      // カラム名:"chouki_knj"
      localOneway.or51775OnewayType.columnName = OrX0042Const.COLUMN_NAME.CHOUKI_KNJ_SNAKE
      // 文章内容:【変数】入力支援項目の既存内容
      local.or51775Type.modelValue = tableDataFilter.value[selectedItemIndex.value].choukiKnj!.value
      break
    // 長期目標(期間)
    case OrX0042Const.COLUMN_NAME.CHO_KIKAN_KNJ_CAMEL:
      //タイトル:"（期間）"
      localOneway.or51775OnewayType.title = t('label.care-plan2-period')
      // 中分類CD:3
      localOneway.or51775OnewayType.t2Cd = OrX0042Const.CLASSIFICATION_CD.CENTRE_3
      //カラム名:"cho_kikan_knj"
      localOneway.or51775OnewayType.columnName = OrX0042Const.COLUMN_NAME.CHO_KIKAN_KNJ_SNAKE
      // 文章内容:【変数】入力支援項目の既存内容
      local.or51775Type.modelValue =
        tableDataFilter.value[selectedItemIndex.value].choKikanKnj!.value
      break
    // 短期目標
    case OrX0042Const.COLUMN_NAME.TANKI_KNJ_CAMEL:
      //タイトル:"短期目標"
      localOneway.or51775OnewayType.title = t('label.short-term-goal')
      // 中分類CD:4
      localOneway.or51775OnewayType.t2Cd = OrX0042Const.CLASSIFICATION_CD.CENTRE_4
      //カラム名:"tanki_knj"
      localOneway.or51775OnewayType.columnName = OrX0042Const.COLUMN_NAME.TANKI_KNJ_SNAKE
      // 文章内容:【変数】入力支援項目の既存内容
      local.or51775Type.modelValue = tableDataFilter.value[selectedItemIndex.value].tankiKnj!.value
      break
    // 短期目標(期間)
    case OrX0042Const.COLUMN_NAME.TAN_KIKAN_KNJ_CAMEL:
      //タイトル:"（期間）"
      localOneway.or51775OnewayType.title = t('label.care-plan2-period')
      // 中分類CD:5
      localOneway.or51775OnewayType.t2Cd = OrX0042Const.CLASSIFICATION_CD.CENTRE_5
      //カラム名:"tan_kikan_knj"
      localOneway.or51775OnewayType.columnName = OrX0042Const.COLUMN_NAME.TAN_KIKAN_KNJ_SNAKE
      // 文章内容:【変数】入力支援項目の既存内容
      local.or51775Type.modelValue =
        tableDataFilter.value[selectedItemIndex.value].tanKikanKnj!.value
      break
    // サービス内容
    case OrX0042Const.COLUMN_NAME.KAIGO_KNJ_CAMEL:
      //タイトル:"サービス内容"
      localOneway.or51775OnewayType.title = t('label.service-contents')
      // 中分類CD:6
      localOneway.or51775OnewayType.t2Cd = OrX0042Const.CLASSIFICATION_CD.CENTRE_6
      //カラム名:"kaigo_knj"
      localOneway.or51775OnewayType.columnName = OrX0042Const.COLUMN_NAME.KAIGO_KNJ_SNAKE
      // 文章内容:【変数】入力支援項目の既存内容
      local.or51775Type.modelValue = tableDataFilter.value[selectedItemIndex.value].kaigoKnj!.value
      break
    // 頻度
    case OrX0042Const.COLUMN_NAME.HINDO_KNJ_CAMEL:
      //タイトル:"頻度"
      localOneway.or51775OnewayType.title = t('label.frequency')
      // 中分類CD:10
      localOneway.or51775OnewayType.t2Cd = OrX0042Const.CLASSIFICATION_CD.CENTRE_10
      //カラム名:"hindo_knj"
      localOneway.or51775OnewayType.columnName = OrX0042Const.COLUMN_NAME.HINDO_KNJ_SNAKE
      // 文章内容:【変数】入力支援項目の既存内容
      local.or51775Type.modelValue = tableDataFilter.value[selectedItemIndex.value].hindoKnj!.value
      break
    // 期間
    case OrX0042Const.COLUMN_NAME.KIKAN_KNJ_CAMEL:
      //タイトル:"期間"
      localOneway.or51775OnewayType.title = t('label.period')
      // 中分類CD:11
      localOneway.or51775OnewayType.t2Cd = OrX0042Const.CLASSIFICATION_CD.CENTRE_11
      //カラム名:"kikan_knj"
      localOneway.or51775OnewayType.columnName = OrX0042Const.COLUMN_NAME.KIKAN_KNJ_SNAKE
      // 文章内容:【変数】入力支援項目の既存内容
      local.or51775Type.modelValue = tableDataFilter.value[selectedItemIndex.value].kikanKnj!.value
      break
  }
  // GUI00937_入力支援［ケアマネ］入力支援ポップアップを開く
  Or51775Logic.state.set({
    uniqueCpId: or51775.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI00937_入力支援［ケアマネ］ポップアップの表示状態を返す
 */
const showDialogOr51775 = computed(() => {
  return Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 「SDケアの入力支援アイコンボタン」押下
 *
 */
function onClickSdCare() {
  local.or51726Type = { selectResult: '' }
  // GUI01031_SDケア選択入力支援ポップアップを開く
  Or51726Logic.state.set({
    uniqueCpId: or51726.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI01031_SDケア選択画面入力支援ポップアップの表示状態を返す
 */
const showDialogOr51726 = computed(() => {
  return Or51726Logic.state.get(or51726.value.uniqueCpId)?.isOpen ?? false
})

/**
 * GUI01019_SDケアマスタ簡易登録画面をポップアップで起動する。
 *
 */
function openSdCareMst() {
  // 共通情報.法人ID
  localOneway.or54215OnewayType.houjinId = commonInfoData.houjinId
  // 共通情報.施設ID
  localOneway.or54215OnewayType.shisetuId = commonInfoData.shisetsuId
  // 共通情報.事業者ID
  localOneway.or54215OnewayType.svJigyoId = commonInfoData.svJigyoId
  // 画面.計画書（２）データ部.長期目標
  localOneway.or54215OnewayType.choukiKnj =
    tableDataFilter.value[selectedItemIndex.value].choukiKnj!.value
  // 画面.計画書（２）データ部.生活全般の解決すべき課題（ニーズ）
  localOneway.or54215OnewayType.needsKnj =
    tableDataFilter.value[selectedItemIndex.value].gutaitekiKnj!.value
  // 画面.計画書（２）データ部.サービス内容
  localOneway.or54215OnewayType.serviceKnj =
    tableDataFilter.value[selectedItemIndex.value].kaigoKnj!.value
  // 画面.計画書（２）データ部.短期目標
  localOneway.or54215OnewayType.tankiKnj =
    tableDataFilter.value[selectedItemIndex.value].tankiKnj!.value
  // 入力支援ポップアップを開く
  Or54215Logic.state.set({
    uniqueCpId: or54215.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI01019_SDケアマスタ簡易登録画面入力支援ポップアップの表示状態を返す
 */
const showDialogOr54215 = computed(() => {
  return Or54215Logic.state.get(or54215.value.uniqueCpId)?.isOpen ?? false
})

/**
 * GUI01018_SDケア作成画面をポップアップで起動する。
 *
 */
function openSdCareCreate() {
  // 画面.計画書(2)ID
  localOneway.or53186OnewayType.ks21Id = commonInfoData.ks21Id
  // 画面.有効期間ID
  localOneway.or53186OnewayType.termid = commonInfoData.termid
  // API取得システム基準日
  localOneway.or53186OnewayType.systemDate = commonInfoData.systemDate
  // 入力支援ポップアップを開く
  Or53186Logic.state.set({
    uniqueCpId: or53186.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI01018_SDケア作成画面入力支援ポップアップの表示状態を返す
 */
const showDialogOr53186 = computed(() => {
  return Or53186Logic.state.get(or53186.value.uniqueCpId)?.isOpen ?? false
})

/**
 * GUI01015_課題と短期目標取込画面をポップアップで起動する。
 *
 */
function openIssuesTankiKnj() {
  // 共通情報.種別ID
  localOneway.or28567OnewayType.issuesAndShortTermGoalImportType.syubetsuId =
    commonInfoData.shubetsuId
  // 共通情報.利用者ID
  localOneway.or28567OnewayType.issuesAndShortTermGoalImportType.userId = commonInfoData.userId
  // 共通情報.施設ID
  localOneway.or28567OnewayType.issuesAndShortTermGoalImportType.shisetuId =
    commonInfoData.shisetsuId
  // 共通情報.事業者ID
  localOneway.or28567OnewayType.issuesAndShortTermGoalImportType.svJigyoId =
    commonInfoData.svJigyoId
  // 共通情報.アセスメント方式
  localOneway.or28567OnewayType.issuesAndShortTermGoalImportType.cpnFlg =
    commonInfoData.carePlanMethod
  // 共通情報.期間管理フラグ
  localOneway.or28567OnewayType.issuesAndShortTermGoalImportType.periodManagementFlag =
    commonInfoData.periodManagementFlag
  // 入力支援ポップアップを開く
  Or28567Logic.state.set({
    uniqueCpId: or28567.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI01015_課題と短期目標取込画面ポップアップの表示状態を返す
 */
const showDialogOr28567 = computed(() => {
  return Or28567Logic.state.get(or28567.value.uniqueCpId)?.isOpen ?? false
})

/**
 * GUI01028_課題とサービス内容取込画面をポップアップで起動する。
 *
 */
function openIssuesKaigoKnj() {
  // 共通情報.種別ID
  localOneway.or10475OnewayType.issuesAndServiceContenImportType.syubetsuId =
    commonInfoData.shubetsuId
  // 入力支援ポップアップを開く
  Or10475Logic.state.set({
    uniqueCpId: or10475.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI01028_課題とサービス内容取込画面ポップアップの表示状態を返す
 */
const showDialogOr10475 = computed(() => {
  return Or10475Logic.state.get(or10475.value.uniqueCpId)?.isOpen ?? false
})

/**
 * GUI01026_課題と目標取込画面をポップアップで起動する。
 *
 */
function openIssuesGoalImport() {
  // 共通情報.種別ID
  localOneway.or10477OnewayType.issuesOrGoalImportType.syubetsuId = commonInfoData.shubetsuId
  // 入力支援ポップアップを開く
  Or10477Logic.state.set({
    uniqueCpId: or10477.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI01026_課題と目標取込画面ポップアップの表示状態を返す
 */
const showDialogOr10477 = computed(() => {
  return Or10477Logic.state.get(or10477.value.uniqueCpId)?.isOpen ?? false
})

/**
 * GUI01027_フリーアセスメント取込画面をポップアップで起動する。
 *
 */
function openFreeAssessmentImport() {
  // 共通情報.種別ID
  localOneway.or28650OnewayType.importFreeAssessmentSelectType.syubetsuId =
    commonInfoData.shubetsuId
  // 入力支援ポップアップを開く
  Or28650Logic.state.set({
    uniqueCpId: or28650.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI01027_フリーアセスメント取込画面ポップアップの表示状態を返す
 */
const showDialogOr28650 = computed(() => {
  return Or28650Logic.state.get(or28650.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 「曜日アイコンボタン」押下
 *
 */
function onClickYobi() {
  // GUI01024_曜日取込入力支援ポップアップを開く
  OrX1024Logic.state.set({
    uniqueCpId: orX1024.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * GUI01024_曜日取込画面ポップアップの表示状態を返す
 */
const showDialogOrX1024 = computed(() => {
  return OrX1024Logic.state.get(orX1024.value.uniqueCpId)?.isOpen ?? false
})

defineExpose({
  resetMo01338OnewayItemLabel,
  initData,
})

/**************************************************
 * ウォッチャー
 **************************************************/
// 行の結合のウォッチャー
watch(
  () => tableItem.value,
  () => {
    if (
      initFlg ||
      tableItem.value === null ||
      !Array.isArray(tableItem.value) ||
      tableItem.value.length === 0
    ) {
      return
    }
    updateStyles()
  },
  { deep: true, immediate: true }
)
/**
 * Or21814のイベントを監視
 *
 * @description
 * またOr21814のボタン押下フラグをリセットする。
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  async (newValue) => {
    await nextTick()
    if (or21814ResolvePromise !== undefined && newValue !== undefined) {
      or21814ResolvePromise(newValue)
    }
    return
  }
)

watch(
  () => props.modelValue,
  () => {
    selectedItemIndex.value = props.modelValue.selectedItemIndex
    selectedItemTableIndex.value = props.modelValue.selectedItemTableIndex
    selectedColKey.value = props.modelValue.selectedColKey
    tableItem.value = props.modelValue.tableItem
  }
)
watch(
  () => tableItem.value,
  () => {
    if (initFlg) {
      return
    }
    const modelValue = {
      selectedItemIndex: selectedItemIndex.value,
      selectedItemTableIndex: selectedItemTableIndex.value,
      selectedColKey: selectedColKey.value,
      tableItem: tableItem.value,
    }
    emit('update:modelValue', modelValue)
  },
  { deep: true, immediate: true }
)
watch(
  () => selectedRows.value,
  async () => {
    const modelValue = {
      selectedItemIndex: selectedItemIndex.value,
      selectedItemTableIndex: selectedItemTableIndex.value,
      selectedColKey: selectedColKey.value,
      tableItem: tableItem.value,
      selectedItem: selectedRows.value,
    }
    await nextTick()
    // 全選択
    if (local.mo00018.modelValue) {
      modelValue.selectedItem = tableItem.value
    }
    emit('update:modelValue', modelValue)
  }
)
// 「拡大・縮小」押下
watch(
  () => Or21817Logic.event.get(or21817.value.uniqueCpId),
  (newValue) => {
    if (newValue?.eventFlg) {
      // 「拡大・縮小」押下
      changeLineHeight()
    }
  }
)
// 'データ-ページングの監視
watch(
  () => selectedItemIndex.value,
  (newValue) => {
    localOneway.mo01338Oneway.itemLabel =
      newValue + 1 + SPACE_FORWARD_SLASH + tableDataFilter.value.length
  }
)
// 「行追加ボタン」押下
watch(
  () => Or21735Logic.event.get(or21735.value.uniqueCpId),
  async () => {
    await nextTick()
    await addNewLine()
  }
)
// 「行挿入ボタン」押下
watch(
  () => Or21736Logic.event.get(or21736.value.uniqueCpId),
  async () => {
    await nextTick()
    await insertNewLine()
  }
)
// 「行複写ボタン」押下
watch(
  () => Or21737Logic.event.get(or21737.value.uniqueCpId),
  async () => {
    await nextTick()
    await cpyLine()
  }
)
// 「行削除ボタン」押下
watch(
  () => Or21738Logic.event.get(or21738.value.uniqueCpId),
  async () => {
    await deleteLine()
  }
)

// 「アセスメントの入力支援アイコンボタン」押下
watch(
  () => OrX0031Logic.event.get(orX0031.value.uniqueCpId),
  (value) => {
    if (value?.eventFlg) {
      // 「アセスメントの入力支援アイコンボタン」押下
      onClickAssessment()
    }
  }
)
// GUI01007_課題検討用紙取込画面「確定」ボタンで戻った場合
watch(
  () => local.or00586,
  () => {
    void onOr00586Close()
  }
)
// GUI01023_課題整理総括取込画面「確定」ボタンで戻った場合
watch(
  () => local.or27362Type,
  async (newValue) => {
    await onClickIssuesConfirm(newValue as unknown as Or27362Type[])
  },
  { deep: true, immediate: true }
)
// GUI01029_プロブレムリスト取込画面「確定」ボタンで戻った場合
watch(
  () => local.or28720Type,
  async (newValue) => {
    // 入力支援ポップアップを閉く
    Or28720Logic.state.set({
      uniqueCpId: or28720.value.uniqueCpId,
      state: { isOpen: false },
    })
    await onClickPlConfirm(newValue)
  },
  { deep: true, immediate: true }
)
// GUI00957_サービス種別入力支援画面「確定」ボタンで戻った場合
watch(
  () => local.or50429Type,
  (newValue) => {
    // 入力支援ポップアップを閉く
    Or50429Logic.state.set({
      uniqueCpId: or50429.value.uniqueCpId,
      state: { isOpen: false },
    })
    onClickSvKbnConfirm(newValue)
  },
  { deep: true, immediate: true }
)
// GUI01022_担当者・頻度画面「確定」ボタンで戻った場合
watch(
  () => local.or27043Type,
  (newValue) => {
    if (managerImportFlg.value) {
      onClickManagerImportConfirm(newValue)
    }
  },
  { deep: true, immediate: true }
)
watch(
  () => Or27043Logic.state.get(or27043.value.uniqueCpId)?.isOpen,
  (newValue) => {
    managerImportFlg.value = newValue ?? false
  },
  { deep: true, immediate: true }
)
// GUI01032_表示順変更計画書（2）画面「確定」ボタンで戻った場合
watch(
  () => local.or10860Type,
  (newValue) => {
    if (sortModifiedFlg.value) {
      onClickSortModifiedConfirm(newValue)
    }
  },
  { deep: true, immediate: true }
)
watch(
  () => Or10860Logic.state.get(or10860.value.uniqueCpId)?.isOpen,
  (newValue) => {
    sortModifiedFlg.value = newValue ?? false
  },
  { deep: true, immediate: true }
)
// GUI01016_ケース一覧画面「確定」ボタンで戻った場合
watch(
  () => local.or28256Type,
  (newValue) => {
    onClickCaseImportConfirm(newValue as unknown as string)
  },
  { deep: true, immediate: true }
)
// GUI00937_入力支援［ケアマネ］画面「確定」ボタンで戻った場合
watch(
  () => local.or51775Type,
  (newValue) => {
    if (careManagerFlg.value) {
      // 入力支援ポップアップを閉く
      Or51775Logic.state.set({
        uniqueCpId: or51775.value.uniqueCpId,
        state: { isOpen: false },
      })
      onClickCareManagerConfirm(newValue)
    }
  },
  { deep: true, immediate: true }
)
watch(
  () => Or51775Logic.state.get(or51775.value.uniqueCpId)?.isOpen,
  (newValue) => {
    careManagerFlg.value = newValue ?? false
  },
  { deep: true, immediate: true }
)
// GUI01018_SDケア作成画面「確定」ボタンで戻った場合
watch(
  () => local.or53186Type,
  async (newValue) => {
    await onClickCareCreateConfirm(newValue)
  },
  { deep: true, immediate: true }
)
// GUI01031_SDケア選択画面「選択」ボタンで戻った場合
watch(
  () => local.or51726Type,
  (newValue) => {
    onClickSdCareSelectConfirm(newValue as unknown as string)
  },
  { deep: true, immediate: true }
)
// GUI01015_課題と短期目標取込画面「確定」ボタンで戻った場合
watch(
  () => local.or28567Type,
  async (newValue) => {
    await onClickIssuesTankiKnjConfirm(newValue as unknown as Or28567Type[])
  },
  { deep: true, immediate: true }
)
// GUI01028_課題とサービス内容取込画面「確定」ボタンで戻った場合
watch(
  () => local.or10475Type,
  async (newValue) => {
    await onClickIssuesKaigoKnjConfirm(newValue as unknown as Or10475Type[])
  },
  { deep: true, immediate: true }
)
// GUI01026_課題と目標取込画面「確定」ボタンで戻った場合
watch(
  () => local.or10477Type,
  async (newValue) => {
    await onClickIssuesGoalImportConfirm(newValue as unknown as Or10477Type[])
  },
  { deep: true, immediate: true }
)
// GUI01027_フリーアセスメント取込画面「確定」ボタンで戻った場合
watch(
  () => local.or28650Type,
  async (newValue) => {
    await onClickFreeAssessmentImportConfirm(newValue as unknown as Or28650Type[])
  },
  { deep: true, immediate: true }
)
// GUI01024_曜日取込画面「確定」ボタンで戻った場合
watch(
  () => local.orX1024Type,
  (newValue) => {
    onClickYobiConfirm(newValue)
  },
  { deep: true, immediate: true }
)
</script>

<template>
  <!-- 行3 -->
  <c-v-row class="ma-0">
    <div class="flex-col">
      <div class="line3-flex-box">
        <!-- 行追加ボタン: Or21735 -->
        <div v-if="!localOneway.orX0073Oneway.cpyFlg">
          <g-base-or-21735 v-bind="or21735"></g-base-or-21735>
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.care-plan2-newline-btn')"
          ></c-v-tooltip>
        </div>
        <!-- 行挿入ボタン: Or21736 -->
        <div v-if="!localOneway.orX0073Oneway.cpyFlg">
          <g-base-or-21736 v-bind="or21736"></g-base-or-21736>
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.care-plan2-insertline-btn')"
          ></c-v-tooltip>
        </div>
        <!-- 行複写ボタン: Or21737 -->
        <div v-if="!localOneway.orX0073Oneway.cpyFlg">
          <g-base-or-21737 v-bind="or21737"></g-base-or-21737>
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.care-plan2-cpyline-btn')"
          ></c-v-tooltip>
        </div>
        <!-- 選択列一括上書ボタン: Mo00611 -->
        <base-mo00611
          v-if="!localOneway.orX0073Oneway.cpyFlg"
          :oneway-model-value="localOneway.mo00611BatchOverriteScOneway"
          @click="onClickbatchOverriteSc"
        >
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.care-plan2-boscline-btn')"
          />
        </base-mo00611>
        <!-- 行削除ボタン: Or21738 -->
        <div v-if="!localOneway.orX0073Oneway.cpyFlg">
          <g-base-or-21738 v-bind="or21738"></g-base-or-21738>
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.care-plan2-deleteline-btn')"
          ></c-v-tooltip>
        </div>
      </div>
      <div class="line3-flex-box">
        <!-- PL取込 -->
        <g-custom-or-x-0028
          v-if="!localOneway.orX0073Oneway.cpyFlg"
          @on-click-icon-btn="onClickPl"
        />
        <!-- ケース取込 -->
        <g-custom-or-x-0029
          v-if="!localOneway.orX0073Oneway.cpyFlg"
          @on-click-icon-btn="onClickCaseImport"
        />
        <!-- 表示順 -->
        <g-custom-or-x-0030
          v-if="!localOneway.orX0073Oneway.cpyFlg"
          @on-click-icon-btn="onClickSortModified"
        />
        <!-- 課題整理総括 -->
        <g-custom-or-x-0033
          v-if="true && !localOneway.orX0073Oneway.cpyFlg"
          @on-click-icon-btn="onClickIssues"
        />
        <!-- アセスメント -->
        <g-custom-or-x-0031
          v-if="!localOneway.orX0073Oneway.cpyFlg"
          v-bind="orX0031"
        />
        <!-- SDケアラ -->
        <g-custom-or-x-0032
          v-if="!localOneway.orX0073Oneway.cpyFlg"
          @on-click-icon-btn="onClickSdCare"
        />
        <!-- 拡大・縮小 -->
        <div>
          <g-base-or-21817 v-bind="or21817" />
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :text="$t('tooltip.care-plan2-line-zoom-btn')"
          ></c-v-tooltip>
        </div>
        <!-- データ-ページング -->
        <base-mo01338 :oneway-model-value="localOneway.mo01338Oneway"></base-mo01338>
        <!-- 行選択上下アイコンボタン -->
        <div>
          <!-- 下アイコン:分子：アイコンボタン -->
          <base-mo00009
            :oneway-model-value="localOneway.mo00009OnewayDown"
            @click="nextLine"
          >
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.care-plan2-select-next')"
            ></c-v-tooltip>
          </base-mo00009>
          <!-- 上アイコン:分子：アイコンボタン -->
          <base-mo00009
            :oneway-model-value="localOneway.mo00009OnewayUp"
            @click="preLine"
          >
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :text="$t('tooltip.care-plan2-select-up')"
            ></c-v-tooltip>
          </base-mo00009>
        </div>
      </div>
    </div>
  </c-v-row>
  <!-- 入力内容詳細表 -->
  <c-v-row class="ma-0">
    <c-v-col
      cols="auto"
      class="pa-0 mt-3"
    >
      <c-v-data-table
        v-resizable-grid="localOneway.orX0073Oneway.columnMinWidth"
        :headers="headers"
        class="table-wrapper overflow-x-auto careplan2-table"
        hide-default-footer
        :items-per-page="-1"
        :items="tableDataFilter"
        :max-height="localOneway.orX0073Oneway.cpyFlg ? '480px' : '565px'"
        fixed-header
        return-object
        :show-select="localOneway.orX0073Oneway.cpyFlg"
      >
        <!-- ヘッダ -->
        <template #headers>
          <tr>
            <!-- チェックボックス -->
            <th
              v-if="localOneway.orX0073Oneway.cpyFlg"
              rowspan="2"
              class="width-80 table-checkbox"
            >
              <base-mo00018
                v-model="local.mo00018"
                :oneway-model-value="{
                  showItemLabel: false,
                  customClass: new CustomClass({ outerClass: '', labelClass: 'ma-1' }),
                  indeterminate:
                    tableDataFilter.length > 0 &&
                    selectedRows.length > 0 &&
                    selectedRows.length != tableDataFilter.length,
                }"
                click.stop
                @update:model-value="allCheck"
              ></base-mo00018>
            </th>
            <!-- 課題番号 -->
            <th
              rowspan="2"
              class="width-80"
            >
              {{ t('label.issues-number') }}
            </th>
            <!-- 生活全般の解決すべき課題(ニーズ) -->
            <th
              rowspan="2"
              class="width-240"
            >
              <div class="thead-with-iconbtn">
                <div>{{ t('label.life-whole-solution-issues') }}</div>
                <div
                  v-if="!localOneway.orX0073Oneway.cpyFlg"
                  class="divider-div"
                >
                  <c-v-divider
                    vertical
                    inset
                  />
                  <base-mo00009
                    :oneway-model-value="localOneway.mo00009OnewayGutaitekiKnj"
                    @click="onClickCareManager(OrX0042Const.COLUMN_NAME.GUTAITEKI_KNJ_CAMEL)"
                  />
                </div>
              </div>
            </th>
            <!-- 長期目標 -->
            <th
              rowspan="2"
              class="width-180"
            >
              <div class="thead-with-iconbtn">
                <div>{{ t('label.long-term-goal') }}</div>
                <div
                  v-if="!localOneway.orX0073Oneway.cpyFlg"
                  class="divider-div"
                >
                  <c-v-divider
                    vertical
                    inset
                  />
                  <base-mo00009
                    :oneway-model-value="localOneway.mo00009OnewaylongTermGoal"
                    @click="onClickCareManager(OrX0042Const.COLUMN_NAME.CHOUKI_KNJ_CAMEL)"
                  />
                </div>
              </div>
            </th>
            <!-- 長期目標期間 -->
            <th
              :colspan="headerPeriodColspan"
              rowspan="1"
              class="width-190"
            >
              <div>{{ t('label.care-plan2-period') }}</div>
            </th>
            <th
              v-if="!localOneway.orX0073Oneway.cpyFlg"
              class="table-open-gui-btn"
            >
              <base-mo00009
                :oneway-model-value="localOneway.mo00009OnewaylongTermGoalPeriod"
                @click="onClickCareManager(OrX0042Const.COLUMN_NAME.CHO_KIKAN_KNJ_CAMEL)"
              />
            </th>
            <!-- 短期目標 -->
            <th
              rowspan="2"
              class="width-180"
            >
              <div class="thead-with-iconbtn">
                <div>{{ t('label.short-term-goal') }}</div>
                <div
                  v-if="!localOneway.orX0073Oneway.cpyFlg"
                  class="divider-div"
                >
                  <c-v-divider
                    vertical
                    inset
                  />
                  <base-mo00009
                    :oneway-model-value="localOneway.mo00009OnewayShortTermGoal"
                    @click="onClickCareManager(OrX0042Const.COLUMN_NAME.TANKI_KNJ_CAMEL)"
                  />
                </div>
              </div>
            </th>
            <!-- 短期目標期間 -->
            <th
              :colspan="headerPeriodColspan"
              rowspan="1"
              class="width-190"
            >
              {{ t('label.care-plan2-period') }}
            </th>
            <th
              v-if="!localOneway.orX0073Oneway.cpyFlg"
              class="table-open-gui-btn"
            >
              <base-mo00009
                :oneway-model-value="localOneway.mo00009OnewayShortTermGoalPeriod"
                @click="onClickCareManager(OrX0042Const.COLUMN_NAME.TAN_KIKAN_KNJ_CAMEL)"
              />
            </th>
            <!-- 番号 -->
            <th
              v-if="columnFlag.showNumberFlg"
              rowspan="2"
              class="width-80"
            >
              {{ t('label.number') }}
            </th>
            <!-- サービス内容 -->
            <th
              rowspan="2"
              class="width-180"
            >
              <div class="thead-with-iconbtn">
                <div>{{ t('label.service-contents') }}</div>
                <div
                  v-if="!localOneway.orX0073Oneway.cpyFlg"
                  class="divider-div"
                >
                  <c-v-divider
                    vertical
                    inset
                  />
                  <base-mo00009
                    :oneway-model-value="localOneway.mo00009OnewayServiceContents"
                    @click="onClickCareManager(OrX0042Const.COLUMN_NAME.KAIGO_KNJ_CAMEL)"
                  />
                </div>
              </div>
            </th>
            <!-- ※1 -->
            <th
              v-if="columnFlag.showStar1Flg"
              rowspan="2"
              class="width-80"
            >
              {{ t('label.star-1') }}
            </th>
            <!-- サービス種別 -->
            <th
              v-if="columnFlag.showServiceType"
              :colspan="localOneway.orX0073Oneway.cpyFlg ? '2' : '3'"
              rowspan="1"
              class="width-270"
            >
              {{ t('label.service-kbn') }}
            </th>
            <th
              v-if="columnFlag.showServiceType && !localOneway.orX0073Oneway.cpyFlg"
              class="table-open-gui-btn"
            >
              <base-mo00009
                :oneway-model-value="localOneway.mo00009OnewayServiceKbn"
                @click="onClickSvKbn('1')"
              />
            </th>
            <!-- ※2 -->
            <th
              v-if="columnFlag.showStar2Flg"
              rowspan="2"
              class="width-140"
            >
              <div class="thead-with-iconbtn">
                <div>{{ t('label.star-2') }}</div>
                <div
                  v-if="!localOneway.orX0073Oneway.cpyFlg"
                  class="divider-div"
                >
                  <c-v-divider
                    vertical
                    inset
                  />
                  <base-mo00009
                    :oneway-model-value="localOneway.mo00009OnewayStar2"
                    @click="onClickSvKbn('2')"
                  />
                </div>
              </div>
            </th>
            <!-- 担当者 -->
            <th
              v-if="columnFlag.showManagerFlg"
              rowspan="1"
              class="width-140"
            >
              {{ t('label.care-plan2-manager') }}
            </th>
            <th
              v-if="columnFlag.showManagerFlg && !localOneway.orX0073Oneway.cpyFlg"
              class="table-open-gui-btn"
            >
              <base-mo00009
                :oneway-model-value="localOneway.mo00009OnewayManager"
                @click="onClickManager()"
              />
            </th>
            <!-- 頻度 -->
            <th
              rowspan="1"
              class="width-140"
            >
              {{ t('label.frequency') }}
            </th>
            <th
              v-if="!localOneway.orX0073Oneway.cpyFlg"
              class="table-open-gui-btn"
            >
              <base-mo00009
                :oneway-model-value="localOneway.mo00009OnewayFrequency"
                @click="onClickCareManager(OrX0042Const.COLUMN_NAME.HINDO_KNJ_CAMEL)"
              />
            </th>
            <!-- 期間 -->
            <th
              :colspan="headerPeriodColspan"
              rowspan="1"
              class="width-190"
            >
              {{ t('label.care-plan2-period') }}
            </th>
            <th
              v-if="!localOneway.orX0073Oneway.cpyFlg"
              class="table-open-gui-btn"
            >
              <base-mo00009
                :oneway-model-value="localOneway.mo00009OnewayPeriod"
                @click="onClickCareManager(OrX0042Const.COLUMN_NAME.KIKAN_KNJ_CAMEL)"
              />
            </th>
            <!-- 週間日課 -->
            <th
              v-if="columnFlag.showWeekDailyRoutingFlg"
              rowspan="2"
              class="width-80"
            >
              {{ t('label.week-daily-routine') }}
            </th>
          </tr>
          <!-- ROW 2 -->
          <tr>
            <!-- 長期目標期間年月日 -->
            <th>
              <div>{{ t('label.yyyy-mm-dd') }}</div>
            </th>
            <th
              v-if="!localOneway.orX0073Oneway.cpyFlg"
              class="table-open-gui-btn"
            >
              <base-mo00009 :oneway-model-value="localOneway.mo00009OnewaylongTermGoalYmd" />
            </th>
            <!-- 長期目標期間月日 -->
            <th v-if="columnFlag.showPeriodMdFlg">
              <div>{{ t('label.mm-dd') }}</div>
            </th>
            <th
              v-if="columnFlag.showPeriodMdFlg && !localOneway.orX0073Oneway.cpyFlg"
              class="table-open-gui-btn"
            >
              <base-mo00009 :oneway-model-value="localOneway.mo00009OnewaylongTermGoalMd" />
            </th>

            <!-- 短期目標期間年月日 -->
            <th>
              {{ t('label.yyyy-mm-dd') }}
            </th>
            <th
              v-if="!localOneway.orX0073Oneway.cpyFlg"
              class="table-open-gui-btn"
            >
              <base-mo00009 :oneway-model-value="localOneway.mo00009OnewayShortTermGoalPeriodYmd" />
            </th>
            <!-- 短期目標期間月日 -->
            <th v-if="columnFlag.showPeriodMdFlg">
              {{ t('label.mm-dd') }}
            </th>
            <th
              v-if="columnFlag.showPeriodMdFlg && !localOneway.orX0073Oneway.cpyFlg"
              class="table-open-gui-btn"
            >
              <base-mo00009 :oneway-model-value="localOneway.mo00009OnewayShortTermGoalPeriodMd" />
            </th>
            <!-- サービス種別: 保険サービス-->
            <th v-if="columnFlag.showServiceTypeInsServiceFlg">
              {{ t('label.insurance-service') }}
            </th>
            <th
              v-if="columnFlag.showServiceTypeInsServiceFlg && !localOneway.orX0073Oneway.cpyFlg"
              class="table-open-gui-btn"
            >
              <base-mo00009 :oneway-model-value="localOneway.mo00009OnewayInsService" />
            </th>
            <!-- サービス種別: 利用票取込-->
            <th v-if="columnFlag.showServiceTypeInsServiceFlg">
              {{ t('label.use-slip-import') }}
            </th>
            <th
              v-if="columnFlag.showServiceTypeInsServiceFlg && !localOneway.orX0073Oneway.cpyFlg"
              class="table-open-gui-btn"
            >
              <base-mo00009
                :oneway-model-value="localOneway.mo00009OnewayUseSlipImport"
                @click="onClickSvKbn('3')"
              />
            </th>
            <!-- 担当者取込 -->
            <th v-if="columnFlag.showManagerImportFlg">
              {{ t('label.import') }}
            </th>
            <th
              v-if="columnFlag.showManagerImportFlg && !localOneway.orX0073Oneway.cpyFlg"
              class="table-open-gui-btn"
            >
              <base-mo00009
                :oneway-model-value="localOneway.mo00009OnewayManagerImport"
                @click="onClickManagerImport()"
              />
            </th>
            <!-- 頻度: 曜日  -->
            <th v-if="columnFlag.showDayOfWeekFlg">
              {{ t('label.day-of-week') }}
            </th>
            <th
              v-if="columnFlag.showDayOfWeekFlg && !localOneway.orX0073Oneway.cpyFlg"
              class="table-open-gui-btn"
            >
              <base-mo00009
                :oneway-model-value="localOneway.mo00009OnewayFrequencyDayOfWeek"
                @click="onClickYobi"
              />
            </th>
            <!-- 頻度: 取込  -->
            <th v-if="columnFlag.showFreqImportFlg">
              {{ t('label.import') }}
            </th>
            <th
              v-if="columnFlag.showFreqImportFlg && !localOneway.orX0073Oneway.cpyFlg"
              class="table-open-gui-btn"
            >
              <base-mo00009 :oneway-model-value="localOneway.mo00009OnewayFrequencyImport" />
            </th>
            <!-- 期間年月日 -->
            <th>
              {{ t('label.yyyy-mm-dd') }}
            </th>
            <th
              v-if="!localOneway.orX0073Oneway.cpyFlg"
              class="table-open-gui-btn"
            >
              <base-mo00009 :oneway-model-value="localOneway.mo00009OnewayPeriodYmd" />
            </th>
            <!-- 期間月日 -->
            <th v-if="columnFlag.showPeriodMdFlg">
              {{ t('label.mm-dd') }}
            </th>
            <th
              v-if="columnFlag.showPeriodMdFlg && !localOneway.orX0073Oneway.cpyFlg"
              class="table-open-gui-btn"
            >
              <base-mo00009 :oneway-model-value="localOneway.mo00009OnewayPeriodMd" />
            </th>
          </tr>
        </template>
        <!-- BODY -->
        <template #item="{ index: index }">
          <tr
            :class="{ 'select-row': selectedItemIndex === index }"
            @click="onSelectRow(index, tableDataFilter[index].tableIndex)"
          >
            <!-- チェックボックス -->
            <td
              v-if="localOneway.orX0073Oneway.cpyFlg"
              class="table-checkbox"
            >
              <base-mo00018
                v-model="tableDataFilter[index].checked"
                class="ml-1"
                :oneway-model-value="{
                  showItemLabel: false,
                }"
                @change="toggleSelectRow"
                @click.stop
              ></base-mo00018>
            </td>
            <!-- 課題番号テキストボックス -->
            <td>
              <base-mo01274
                v-model="tableDataFilter[index].kadaiNo"
                :readonly="localOneway.orX0073Oneway.cpyFlg"
                @click="resetCaseImportCol('kadaiNo')"
              ></base-mo01274>
            </td>
            <!-- 生活全般の解決すべき課題(ニーズ) -->
            <td :style="getCellStyle(OrX0042Const.COLUMN_NAME.GUTAITEKI_KNJ_CAMEL, index)">
              <base-mo01280
                v-model="tableDataFilter[index].gutaitekiKnj"
                max-length="4000"
                :class="`textarea-min-height `"
                :readonly="localOneway.orX0073Oneway.cpyFlg"
                @click="resetCaseImportCol(OrX0042Const.COLUMN_NAME.GUTAITEKI_KNJ_CAMEL)"
              ></base-mo01280>
            </td>
            <!-- 長期目標 -->
            <td :style="getCellStyle(OrX0042Const.COLUMN_NAME.CHOUKI_KNJ_CAMEL, index)">
              <base-mo01280
                v-model="tableDataFilter[index].choukiKnj"
                max-length="4000"
                :readonly="localOneway.orX0073Oneway.cpyFlg"
                @click="resetCaseImportCol(OrX0042Const.COLUMN_NAME.CHOUKI_KNJ_CAMEL)"
              ></base-mo01280>
            </td>
            <!-- 長期目標期間 -->
            <td
              :style="getCellStyle(OrX0042Const.COLUMN_NAME.CHO_KIKAN_KNJ_CAMEL, index)"
              :colspan="dataPriodColspan"
              @click="onClickCol(OrX0042Const.COLUMN_NAME.CHOUKI_KNJ_CAMEL)"
            >
              <div class="h-100">
                <div
                  v-if="columnFlag.showPeriodStartYmdFlg"
                  class="period-start"
                >
                  <base-mo01276
                    v-model="tableDataFilter[index].choSYmd"
                    :class="`batch-able choukiKnj choukiKnj-${index}`"
                    :readonly="localOneway.orX0073Oneway.cpyFlg"
                    @click="resetCaseImportCol('choSYmd')"
                  ></base-mo01276>
                </div>
                <!-- 長期目標期間～ラベル -->
                <base-mo01337
                  v-if="columnFlag.showPeriodLabelFlg"
                  :oneway-model-value="localOneway.mo01337OnewayWaveDash"
                ></base-mo01337>
                <!-- 長期目標期間終了年月日テキストボックス -->
                <div
                  v-if="columnFlag.showPeriodEndYmdFlg"
                  class="period-end"
                >
                  <base-mo01276
                    v-model="tableDataFilter[index].choEYmd"
                    :class="`batch-able choukiKnj choukiKnj-${index}`"
                    :readonly="localOneway.orX0073Oneway.cpyFlg"
                    @click="resetCaseImportCol('choEYmd')"
                  ></base-mo01276>
                </div>
                <!-- 長期目標期間テキストエリア -->
                <base-mo01280
                  v-model="tableDataFilter[index].choKikanKnj"
                  max-length="4000"
                  :readonly="localOneway.orX0073Oneway.cpyFlg"
                  :class="
                    `batch-able choukiKnj choukiKnj-${index} ` +
                    `${columnFlag.showPeriodEndYmdFlg ? 'period-text-area period-text-area-border' : ''}`
                  "
                  @click="resetCaseImportCol(OrX0042Const.COLUMN_NAME.CHO_KIKAN_KNJ_CAMEL)"
                ></base-mo01280>
              </div>
            </td>
            <!-- 短期目標テキストエリア -->
            <td :style="getCellStyle(OrX0042Const.COLUMN_NAME.TANKI_KNJ_CAMEL, index)">
              <base-mo01280
                v-model="tableDataFilter[index].tankiKnj"
                max-length="4000"
                :readonly="localOneway.orX0073Oneway.cpyFlg"
                @click="resetCaseImportCol(OrX0042Const.COLUMN_NAME.TANKI_KNJ_CAMEL)"
              ></base-mo01280>
            </td>
            <!-- 短期目標期間開始年月日テキストボックス -->
            <td
              :style="getCellStyle(OrX0042Const.COLUMN_NAME.TAN_KIKAN_KNJ_CAMEL, index)"
              :colspan="dataPriodColspan"
              @click="onClickCol(OrX0042Const.COLUMN_NAME.TANKI_KNJ_CAMEL)"
            >
              <div class="h-100">
                <div
                  v-if="columnFlag.showPeriodStartYmdFlg"
                  class="period-start"
                >
                  <base-mo01276
                    v-model="tableDataFilter[index].tanSYmd"
                    :class="`batch-able tankiKnj tankiKnj-${index}`"
                    :readonly="localOneway.orX0073Oneway.cpyFlg"
                    @click="resetCaseImportCol('tanSYmd')"
                  ></base-mo01276>
                </div>
                <!-- 短期目標期間～ラベル -->
                <base-mo01337
                  v-if="columnFlag.showPeriodLabelFlg"
                  :oneway-model-value="localOneway.mo01337OnewayWaveDash"
                ></base-mo01337>
                <!-- 短期目標期間終了年月日テキストボックス -->
                <div
                  v-if="columnFlag.showPeriodEndYmdFlg"
                  class="period-end"
                >
                  <base-mo01276
                    v-model="tableDataFilter[index].tanEYmd"
                    :class="`batch-able tankiKnj tankiKnj-${index}`"
                    :readonly="localOneway.orX0073Oneway.cpyFlg"
                    @click="resetCaseImportCol('tanEYmd')"
                  ></base-mo01276>
                </div>
                <!-- 短期目標期間テキストエリア -->
                <base-mo01280
                  v-model="tableDataFilter[index].tanKikanKnj"
                  max-length="4000"
                  :readonly="localOneway.orX0073Oneway.cpyFlg"
                  :class="
                    `batch-able tankiKnj tankiKnj-${index} ` +
                    `${columnFlag.showPeriodEndYmdFlg ? 'period-text-area period-text-area-border' : ''}`
                  "
                  @click="resetCaseImportCol(OrX0042Const.COLUMN_NAME.TAN_KIKAN_KNJ_CAMEL)"
                ></base-mo01280>
              </div>
            </td>
            <!-- 番号 -->
            <td v-if="columnFlag.showNumberFlg">
              <base-mo01274
                v-model="tableDataFilter[index].kaigoNo"
                class="table-align-right"
                :readonly="localOneway.orX0073Oneway.cpyFlg"
                @click="resetCaseImportCol('kaigoNo')"
              ></base-mo01274>
            </td>
            <!-- サービス内容 -->
            <td>
              <base-mo01280
                v-model="tableDataFilter[index].kaigoKnj"
                max-length="4000"
                :readonly="localOneway.orX0073Oneway.cpyFlg"
                @click="resetCaseImportCol(OrX0042Const.COLUMN_NAME.KAIGO_KNJ_CAMEL)"
              ></base-mo01280>
            </td>
            <!-- ※1 -->
            <td v-if="columnFlag.showStar1Flg">
              <div class="divider-div divider-div-align">
                <!-- 〇 -->
                <base-mo01337 :oneway-model-value="localOneway.mo01337OnewayCircle"></base-mo01337>
                <c-v-divider
                  vertical
                  inset
                />
                <!-- 1編集入力アイコンボタン -->
                <base-mo00009
                  v-if="!localOneway.orX0073Oneway.cpyFlg"
                  :oneway-model-value="localOneway.mo00009OnewayStart1Edit"
                ></base-mo00009>
              </div>
            </td>
            <!-- サービス種別 -->
            <td
              v-if="columnFlag.showServiceType"
              :colspan="localOneway.orX0073Oneway.cpyFlg ? '2' : '4'"
              @click="onClickCol(OrX0042Const.COLUMN_NAME.SV_SHU_KNJ_CAMEL)"
            >
              <!-- サービス種別テキストエリア -->
              <base-mo01280
                v-model="tableDataFilter[index].svShuKnj"
                max-length="4000"
                :readonly="localOneway.orX0073Oneway.cpyFlg"
                :class="`batch-able svShuKnj svShuKnj-${index}`"
                @click="resetCaseImportCol(OrX0042Const.COLUMN_NAME.SV_SHU_KNJ_CAMEL)"
              ></base-mo01280>
            </td>
            <!-- ※2 -->
            <td
              v-if="columnFlag.showStar2Flg"
              @click="onClickCol(OrX0042Const.COLUMN_NAME.JIGYO_NAME_KNJ_CAMEL)"
            >
              <base-mo01280
                v-model="tableDataFilter[index].jigyoNameKnj"
                max-length="4000"
                :readonly="localOneway.orX0073Oneway.cpyFlg"
                :class="`batch-able jigyoNameKnj jigyoNameKnj-${index}`"
                @click="resetCaseImportCol(OrX0042Const.COLUMN_NAME.JIGYO_NAME_KNJ_CAMEL)"
              ></base-mo01280>
            </td>
            <!-- 担当者 -->
            <td
              v-if="columnFlag.showManagerFlg"
              :colspan="localOneway.orX0073Oneway.cpyFlg ? '1' : '2'"
            >
              <base-mo01280
                v-model="tableDataFilter[index].svShuKnj"
                max-length="4000"
                :readonly="localOneway.orX0073Oneway.cpyFlg"
                @click="resetCaseImportCol(OrX0042Const.COLUMN_NAME.SV_SHU_KNJ_CAMEL)"
              ></base-mo01280>
            </td>
            <!-- 頻度 -->
            <td
              :colspan="localOneway.orX0073Oneway.cpyFlg ? '1' : '2'"
              @click="onClickCol(OrX0042Const.COLUMN_NAME.HINDO_KNJ_CAMEL)"
            >
              <base-mo01280
                v-model="tableDataFilter[index].hindoKnj"
                max-length="4000"
                :class="`batch-able hindoKnj hindoKnj-${index}`"
                :readonly="localOneway.orX0073Oneway.cpyFlg"
                @click="resetCaseImportCol(OrX0042Const.COLUMN_NAME.HINDO_KNJ_CAMEL)"
              ></base-mo01280>
            </td>
            <!-- 期間 -->
            <td
              :colspan="dataPriodColspan"
              @click="onClickCol(OrX0042Const.COLUMN_NAME.KIKAN_KNJ_CAMEL)"
            >
              <div class="h-100">
                <!-- 期間開始年月日テキストボックス -->
                <div
                  v-if="columnFlag.showPeriodStartYmdFlg"
                  class="period-start"
                >
                  <base-mo01276
                    v-model="tableDataFilter[index].kikanSYmd"
                    :class="`batch-able kikanKnj kikanKnj-${index}`"
                    :readonly="localOneway.orX0073Oneway.cpyFlg"
                    @click="resetCaseImportCol('kikanSYmd')"
                  ></base-mo01276>
                </div>
                <!-- 期間～ラベル -->
                <base-mo01337
                  v-if="columnFlag.showPeriodLabelFlg"
                  :oneway-model-value="localOneway.mo01337OnewayWaveDash"
                ></base-mo01337>
                <!-- 期間終了年月日テキストボックス -->
                <div
                  v-if="columnFlag.showPeriodEndYmdFlg"
                  class="period-end"
                  :class="`batch-able kikanKnj kikanKnj-${index}`"
                >
                  <base-mo01276
                    v-model="tableDataFilter[index].kikanEYmd"
                    :readonly="localOneway.orX0073Oneway.cpyFlg"
                    @click="resetCaseImportCol('kikanEYmd')"
                  ></base-mo01276>
                </div>
                <!-- 期間テキストエリア -->
                <base-mo01280
                  v-model="tableDataFilter[index].kikanKnj"
                  max-length="4000"
                  :readonly="localOneway.orX0073Oneway.cpyFlg"
                  :class="
                    `batch-able kikanKnj kikanKnj-${index} ` +
                    `${columnFlag.showPeriodEndYmdFlg ? 'period-text-area period-text-area-border' : ''}`
                  "
                  @click="resetCaseImportCol(OrX0042Const.COLUMN_NAME.KIKAN_KNJ_CAMEL)"
                ></base-mo01280>
              </div>
            </td>
            <!-- 週間日課 -->
            <td v-if="columnFlag.showWeekDailyRoutingFlg">
              <div class="divider-div divider-div-align">
                <!-- 有Or無 -->
                <base-mo01337
                  :oneway-model-value="{
                    itemLabel: tableDataFilter[index].weekDailyRouting,
                  }"
                  :readonly="localOneway.orX0073Oneway.cpyFlg"
                  class="divider-div divider-div-align"
                ></base-mo01337>
                <c-v-divider
                  vertical
                  inset
                />
                <!-- 1編集入力アイコンボタン -->
                <base-mo00009
                  v-if="!localOneway.orX0073Oneway.cpyFlg"
                  :oneway-model-value="localOneway.mo00009OnewayWeekDailyRoutineEdit"
                  @click="onWeekDailyRoutingClick"
                ></base-mo00009>
              </div>
            </td>
          </tr>
        </template>
      </c-v-data-table>
    </c-v-col>
  </c-v-row>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814 v-bind="or21814" />
  <!-- GUI01011 履歴選択画面のポップアップ -->
  <g-custom-or-00586
    v-if="showDialogOr00586"
    v-bind="or00586"
    v-model="local.or00586"
    :oneway-model-value="localOneway.or00586Oneway"
  />
  <!-- Or27235: 有機体: GUI01021_担当者入力支援画面をポップアップで起動する。 -->
  <g-custom-or-27235
    v-if="showDialogOr27235"
    v-bind="or27235"
    v-model="local.or27235Type"
    :oneway-model-value="localOneway.or27235OnewayType"
    @on-confirm="handleOr27235Confirm"
  >
  </g-custom-or-27235>
  <!-- Or28720: 有機体: GUI01029_プロブレムリスト取込画面をポップアップで起動する。 -->
  <g-custom-or-28720
    v-if="showDialogOr28720"
    v-bind="or28720"
    v-model="local.or28720Type"
    :oneway-model-value="localOneway.or28720OnewayType"
  >
  </g-custom-or-28720>
  <!-- Or27362: 有機体: GUI01023_課題整理総括取込画面をポップアップで起動する。 -->
  <g-custom-or-27362
    v-if="showDialogOr27362"
    v-bind="or27362"
    v-model="local.or27362Type"
    :oneway-model-value="localOneway.or27362OnewayType"
  >
  </g-custom-or-27362>
  <!-- Or50429: 有機体: GUI00957_サービス種別入力支援画面をポップアップで起動する。 -->
  <g-custom-or-50429
    v-if="showDialogOr50429"
    v-bind="or50429"
    v-model="local.or50429Type"
    :oneway-model-value="localOneway.or50429OnewayType"
  >
  </g-custom-or-50429>
  <!-- Or27043: 有機体: GUI01022_担当者・頻度画面をポップアップで起動する。 -->
  <g-custom-or-27043
    v-if="showDialogOr27043"
    v-bind="or27043"
    v-model="local.or27043Type"
    :oneway-model-value="localOneway.or27043OnewayType"
  >
  </g-custom-or-27043>
  <!-- Or10860: 有機体: GUI01032_表示順変更計画書（2）画面をポップアップで起動する。 -->
  <g-custom-or-10860
    v-if="showDialogOr10860"
    v-bind="or10860"
    v-model="local.or10860Type"
    :oneway-model-value="localOneway.or10860OnewayType"
  >
  </g-custom-or-10860>
  <!-- Or28256: 有機体: GUI01016_ケース一覧画面をポップアップで起動する。 -->
  <g-custom-or-28256
    v-if="showDialogOr28256"
    v-bind="or28256"
    v-model="local.or28256Type"
    :oneway-model-value="localOneway.or28256OnewayType"
  >
  </g-custom-or-28256>
  <!-- Or51775: 有機体: GUI00937_入力支援［ケアマネ］画面をポップアップで起動する。 -->
  <g-custom-or-51775
    v-if="showDialogOr51775"
    v-bind="or51775"
    v-model="local.or51775Type"
    :oneway-model-value="localOneway.or51775OnewayType"
  >
  </g-custom-or-51775>
  <!-- Or54215: 有機体: GUI01019_SDケアマスタ簡易登録画面をポップアップで起動する。 -->
  <g-custom-or-54215
    v-if="showDialogOr54215"
    v-bind="or54215"
    :oneway-model-value="localOneway.or54215OnewayType"
  >
  </g-custom-or-54215>
  <!-- Or53186: 有機体: GUI01018_SDケア作成画面をポップアップで起動する。 -->
  <g-custom-or-53186
    v-if="showDialogOr53186"
    v-bind="or53186"
    v-model="local.or53186Type"
    :oneway-model-value="localOneway.or53186OnewayType"
  >
  </g-custom-or-53186>
  <!-- Or51726: 有機体: GUI01031_SDケア選択画面をポップアップで起動する。 -->
  <g-custom-or-51726
    v-if="showDialogOr51726"
    v-bind="or51726"
    v-model="local.or51726Type"
  >
  </g-custom-or-51726>
  <!-- Or28567: 有機体: GUI01015_課題と短期目標取込画面をポップアップで起動する。 -->
  <g-custom-or-28567
    v-if="showDialogOr28567"
    v-bind="or28567"
    v-model="local.or28567Type"
    :oneway-model-value="localOneway.or28567OnewayType"
  >
  </g-custom-or-28567>
  <!-- Or10475: 有機体: GUI01028_課題とサービス内容取込画面をポップアップで起動する。 -->
  <g-custom-or-10475
    v-if="showDialogOr10475"
    v-bind="or10475"
    v-model="local.or10475Type"
    :oneway-model-value="localOneway.or10475OnewayType"
  >
  </g-custom-or-10475>
  <!-- Or10477: 有機体: GUI01026_課題と目標取込画面をポップアップで起動する。 -->
  <g-custom-or-10477
    v-if="showDialogOr10477"
    v-bind="or10477"
    v-model="local.or10477Type"
    :oneway-model-value="localOneway.or10477OnewayType"
  >
  </g-custom-or-10477>
  <!-- Or28650: 有機体: GUI01027_フリーアセスメント取込画面をポップアップで起動する。 -->
  <g-custom-or-28650
    v-if="showDialogOr28650"
    v-bind="or28650"
    v-model="local.or28650Type"
    :oneway-model-value="localOneway.or28650OnewayType"
  >
  </g-custom-or-28650>
  <!-- OrX1024: 有機体: GUI01024_曜日取込画面をポップアップで起動する。 -->
  <g-custom-or-X1024
    v-if="showDialogOrX1024"
    v-bind="orX1024"
    v-model="local.orX1024Type"
  >
  </g-custom-or-X1024>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/page-data-table.scss';

.thead-with-iconbtn {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.divider-div {
  display: flex;
}
.divider-div-align {
  justify-content: center;
  align-items: center;
}
// テーブルのアイコンボタン
.table-open-gui-btn {
  width: 48px;
  min-width: 48px;
  padding: 8px !important;
}
// 最小幅: 40px
.width-40 {
  width: 40px;
}
// 最小幅: 50px
.width-50 {
  width: 50px;
}
// 最小幅: 80px
.width-80 {
  width: 80px;
}
// 最小幅: 120px
.width-120 {
  width: 120px;
}
// 最小幅: 140px
.width-140 {
  width: 140px;
}
// 最小幅: 180px
.width-180 {
  width: 180px;
}
// 最小幅: 190px
.width-190 {
  width: 190px;
}
// 最小幅: 240px
.width-240 {
  width: 240px;
}
// 最小幅: 270px
.width-270 {
  width: 270px;
}
.table-align-right {
  text-align: right;
}
:deep(.input-wrapper.full-height-cell) {
  height: 40px !important;
}
// 期間開始の枠線
.period-start {
  border-bottom: 1px solid rgb(var(--v-theme-black-200));
}
// 期間終了の枠線
.period-end {
  border-top: 1px solid rgb(var(--v-theme-black-200));
}
// 期間テキストエリアの枠線
.period-text-area-border {
  border-top: 1px solid rgb(var(--v-theme-black-200));
}
// 期間テキストエリアの高さ
.period-text-area {
  height: auto !important;
}
// 期間テキストエリアの最小高さ
.textarea-min-height {
  min-height: 100px;
}
// 担当者列幅
.tanto-col {
  width: 220px;
}
.text-white {
  color: #fff !important;
}
.btn-selected-columns-bundle-overrite {
  font-size: 12px;
  white-space: break-spaces !important;

  :deep(.v-btn__content) {
    white-space: break-spaces !important;
  }
}
// col flex
.flex-col {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  flex-shrink: 0;
}
.line3-flex-box {
  display: flex;
  column-gap: 8px;
  align-items: center;
  flex-shrink: 0;
  flex-wrap: wrap;
}
.page-label-center {
  display: flex;
  align-items: center;
}
:deep(.table-wrapper .v-table__wrapper table) {
  table-layout: fixed;
}
:deep(.table-container) {
  :deep(div:nth-child(2) > .v-row) {
    margin: 0px !important;
    .flex-col {
      padding: 0px !important;
    }
  }
  :deep(div:nth-child(2) > div:nth-child(2)) {
    margin: 8px 0px !important;
  }
}
:deep(.table-checkbox > div) {
  display: flex;
  align-items: center;
  justify-content: center;
}
:deep(.v-table__wrapper table) {
  border: none !important;
}
</style>
