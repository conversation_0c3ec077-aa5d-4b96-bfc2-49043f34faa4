<script setup lang="ts">
/**
 * Or30918: 週間表（一覧）モーダル
 * GUI00979_週間表（一覧）
 *
 * <AUTHOR>
 */
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or26280Logic } from '../Or26280/Or26280.logic'
import { Or27696Logic } from '../Or27696/Or27696.logic'
import { Or26280Const } from '../Or26280/Or26280.constants'
import { Or27696Const } from '../Or27696/Or27696.constants'
import { Or26273Logic } from '../Or26273/Or26273.logic'
import { Or26273Const } from '../Or26273/Or26273.constants'
import { Gui00038Const } from '../Gui00038/Gui00038.constants'
import { Gui00038Logic } from '../Gui00038/Gui00038.logic'
import type { Gui00038EventType } from '../Gui00038/Gui00038.type'
import type { DataTableData, Or30918StateType, WeeklyPlan } from './Or30918.type'
import { Or30918Const } from './Or30918.constants'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import { useScreenOneWayBind, useScreenTwoWayBind, useSetupChildProps } from '#imports'
import type { Or30918Type } from '~/types/cmn/business/components/Or30918Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01278OnewayType } from '~/types/business/components/Mo01278Type'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { OrX0021OnewayType } from '~/types/cmn/business/components/OrX0021Type'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import type { Or26280OnewayType, Or26280Type } from '~/types/cmn/business/components/Or26280Type'
import type { Mo00046Type } from '~/types/business/components/Mo00046Type'
import type { Or27696Type } from '~/types/cmn/business/components/Gui01037Type'
import type { Or26273OnewayType, Or26273Type } from '~/types/cmn/business/components/Or26273Type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import type { Or27696OnewayType } from '~/types/cmn/business/components/Or27696Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  WeekTableImageInitSelectInEntity,
  WeekTableImageInitSelectOutEntity,
} from '~/repositories/cmn/entities/WeekTableImageInitSelectEntity'

const { t } = useI18n()

/**************************************************
 * Props
 **************************************************/
interface Props {
  modelValue: Or30918Type
  uniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()

// 元のテーブルデータ
const orgTableData = ref<string>('')

const defaultModelValue: Or30918Type = {
  // 期間管理フラグ
  carePlanStyle: Or30918Const.DEFAULT.PLAN_STYLE.FACILITY,
  // パターンモード
  patternMode: false,
  // 新規モード
  newMode: false,

  index: -1,
}

const or26280 = ref({ uniqueCpId: Or26280Const.CP_ID(0) })
const or27696 = ref({ uniqueCpId: Or27696Const.CP_ID(0) })
const or26273 = ref({ uniqueCpId: Or26273Const.CP_ID(0) })
const gui00038 = ref({ uniqueCpId: Gui00038Const.CP_ID(0) })
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })

const local = reactive({
  or30918: {
    ...defaultModelValue,
    ...props.modelValue,
  } as Or30918Type,
  or26280: {
    selectedAreaValue: '1',
    rangeSelectionFlag: { modelValue: false },
    frequencyPreview: { value: '' } as Mo00046Type,
    specificDateSelections: [] as number[],
  } as Or26280Type,
  or26273: {
    item: {
      id: '',
    },
  } as Or26273Type,
  or27696: {
    cks55jyouhoList: [],
  } as Or27696Type,
  gui00038: {
    cks55jyouhoList: [],
  } as Gui00038EventType,
})

const localOneway = reactive({
  // 加算情報ダイアログ
  mo00024Oneway: {
    width: '1250px',
    maxWidth: '1900px',
    height: '670px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or30918',
      toolbarTitle: t('label.weekly-plan-input'),
      toolbarName: 'Or30918ToolBar',
      // ツールバータイトルの左寄せ
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  si016OneWay: {
    showItemLabel: false,
    hideDetails: true,
  } as Mo00018OnewayType,
  si039OneWay: {
    items: [] as CodeType[],
    itemTitle: 'label',
    itemValue: 'value',
  },
  si040OneWay: {
    mo00045Oneway: {
      maxLength: '4',
      width: '40px',
    } as Mo00045OnewayType,
  } as Mo01278OnewayType,
  si041OneWay: {
    items: [] as CodeType[],
    itemTitle: 'label',
    itemValue: 'value',
  },
  si042OneWay: {
    items: [] as CodeType[],
    itemTitle: 'label',
    itemValue: 'value',
  },
  si047OneWay: {
    items: [] as CodeType[],
    itemTitle: 'label',
    itemValue: 'value',
  },
  si048OneWay: {
    items: [] as CodeType[],
    itemTitle: 'label',
    itemValue: 'value',
  },
  mo00024WarningDialog: {
    name: '',
    width: '300px',
    mo01344Oneway: {
      showToolbar: false,
      showCardActions: true,
      name: '',
      toolbarName: '',
    },
  } as Mo00024OnewayType,
  mo00609BtnOkOneWay: {
    btnLabel: t('btn.ok'),
  } as Mo00611OnewayType,
  mo00024DeleteRowDialogOneway: {
    name: '',
    width: '300px',
    message: t('message.i-cmn-11275'),
    mo01344Oneway: {
      showToolbar: false,
      cardTitle: t('label.confirm'),
      showCardActions: true,
      name: '',
      toolbarName: '',
    },
  } as Mo00024OnewayType,
  // 確認ダイアログ
  orX0021ErrorDialogOneWay: {
    message: t('message.e-cmn-40740'),
    btnNoDisplay: false,
    mo00024Oneway: {
      class: 'mr-1',
      name: '',
      width: '350px',
    } as Mo00024OnewayType,
    mo00609OnewayOk: {
      class: 'mr-1',
      name: '',
      color: 'red',
    } as Mo00609OnewayType,
  } as OrX0021OnewayType,
  mo00009OneWay1: {
    btnIcon: 'history',
  } as Mo00009OnewayType,
  mo00009OneWay2: {
    btnIcon: 'edit_square',
  } as Mo00009OnewayType,
  mo01227Oneway: {
    isRequired: true,
    hideDetails: true,
    maxLength: '5',
    class: 'text-center',
    width: '40px',
    // rules: [isTimeFormatValid],
  },
  si017OneWay: {
    labelColor: 'rgb(var(--v-theme-black-800))',
    width: '20px',
    minWidth: '20px',
    minHeight: '40px',
  } as Mo00611OnewayType,
  or26280Oneway: {
    officeId: '1',
    sc1Id: 1,
    userId: '1',
    showSelectDate: true,
    nonWeeklyServiceCategory: '',
  } as Or26280OnewayType,
  or26273OnewayModel: {
    dataKbn: '0',
  } as Or26273OnewayType,
  or27696Oneway: {
    ks51Id: '',
    ks52Id: '',
    termId: '',
    weekPlans: [],
    parentUniqueCpId: '',
  } as Or27696OnewayType,
})

/**************************************************
 * 変数定義
 **************************************************/
// 選択した行のindex
const selectedItemIndex = ref<number>(-1)

// テーブルヘッダ
const headers = ref<unknown[]>([])

// 週間計画パターンタイトル情報
const tableData = ref<DataTableData>({
  dataList: [],
})

const mo00024 = ref<Mo00024Type>({
  isOpen: Or30918Const.DEFAULT.IS_OPEN,
})

const mo00024Delete = ref<Mo00024Type>({
  isOpen: Or30918Const.DEFAULT.IS_OPEN,
})

const or26184 = ref({
  selectedIndex: -1,
  totalLine: 0,
})
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or30918StateType>({
  cpId: Or30918Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or30918Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or26280Const.CP_ID(0)]: or26280.value,
  [Gui00038Const.CP_ID(0)]: gui00038.value,
})

onMounted(async () => {
  init()
  await getInitDataInfo()
})

/************************************************
 * ウォッチャー
 ************************************************/
/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      onClose()
    }
  }
)

/**
 * 選択された週間計画詳細データを削除する
 */
watch(
  () => Or21814Logic.event.get(or21814.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.secondBtnClickFlg) {
      // 注意ダイアログの確認ボタン押下時
      if (selectedItemIndex.value !== null) {
        tableData.value.dataList.splice(selectedItemIndex.value, 1)
        if (selectedItemIndex.value > tableData.value.dataList.length - 1)
          selectRow(selectedItemIndex.value - 1)
      }
    } else {
      return
    }
  }
)

/**
 * 選択された行番号
 */
watch(
  () => selectedItemIndex.value,
  (newValue) => {
    or26184.value.selectedIndex = newValue
  }
)

/**
 * or26280
 */
watch(
  () => local.or26280,
  (newValue) => {
    tableData.value.dataList[selectedItemIndex.value].frequency = newValue.previewData
  }
)

/**
 * or26273
 */
watch(
  () => local.or26273,
  (newValue) => {
    tableData.value.dataList[selectedItemIndex.value].contents.modelValue = newValue.item.id
  }
)

/**
 * or27696
 */
watch(
  () => local.or27696,
  (newValue) => {
    tableData.value.dataList[selectedItemIndex.value].insuranceAddition =
      newValue.cks55jyouhoList.length > 0
        ? Or30918Const.DEFAULT.ADDITION_STATUS.PRESENCE
        : Or30918Const.DEFAULT.ADDITION_STATUS.ABSENCE
  }
)

/**
 * 総行数
 */
watch(
  () => tableData.value.dataList,
  (newValue) => {
    or26184.value.totalLine = newValue.length
    isEdit.value = orgTableData.value !== JSON.stringify(refValue.value)
  },
  { deep: true }
)
/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<WeeklyPlan[]>({
  cpId: Or30918Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

const columnMinWidth = ref<number[]>([216,300,127,187,187,100,48,60,70,70,48,420])
/**
 * 週間計画パターンタイトル情報取得
 */
function init() {
  localOneway.mo00024Oneway.width = '1330px'

  headers.value = [
    {
      title: t('label.weekly-plan-timezone'),
      key: 'startTime',
      minWidth: '216px',
      width: '216px',
      sortable: false,
    },
    {
      title: t('label.weekly-plan-day-of-week'),
      key: 'dayOfWeek',
      minWidth: '300px',
      width: '300px',
      sortable: false,
    },
    {
      title: t('label.weekly-plan-contents'),
      key: 'content',
      minWidth: '127px',
      width: '127px',
      sortable: false,
    },
    {
      title: t('label.memo'),
      key: 'memo',
      minWidth: '187px',
      width: '187px',
      sortable: false,
    },
    {
      title: t('label.frequency'),
      key: 'frequency',
      minWidth: '187px',
      width: '187px',
      sortable: false,
    },
    {
      title: t('label.weekly-plan-letter-size'),
      key: 'letterSize',
      minWidth: '100px',
      width: '100px',
      align: 'center',
      sortable: false,
    },
    {
      title: t('label.weekly-plan-letter-position'),
      key: 'textPosition',
      minWidth: '48px',
      width: '48px',
      sortable: false,
    },
    {
      title: t('label.weekly-plan-omitted'),
      key: 'omission',
      minWidth: '60px',
      width: '60px',
      sortable: false,
    },
    {
      title: t('label.weekly-plan-letter-color'),
      key: 'color',
      minWidth: '70px',
      width: '70px',
      sortable: false,
    },
    {
      title: t('label.weekly-plan-background-color'),
      key: 'background',
      minWidth: '70px',
      width: '70px',
      sortable: false,
    },
    {
      title: t('label.weekly-plan-time-display'),
      key: 'timeDisplay',
      minWidth: '48px',
      width: '48px',
      sortable: false,
    },
    {
      title: t('label.weekly-plan-insurance-services'),
      children: [
        {
          title: t('label.weekly-plan-type'),
          key: 'insuranceType',
          minWidth: '120px',
          width: '120px',
          sortable: false,
        },
        {
          title: t('label.weekly-plan-office'),
          key: 'insuranceBussiness',
          minWidth: '100px',
          width: '100px',
          sortable: false,
        },
        {
          title: t('label.weekly-plan-services'),
          key: 'insuranceServices',
          minWidth: '120px',
          width: '120px',
          sortable: false,
        },
        {
          title: t('label.weekly-plan-add'),
          key: 'insuranceDelete',
          minWidth: '80px',
          width: '80px',
          sortable: false,
        },
        {
          title: t('label.weekly-plan-delete'),
          key: 'insuranceDelete',
          minWidth: '50px',
          width: '50px',
          sortable: false,
        },
      ],
    },
  ]

  // // 週間計画パターンタイトル情報取得(IN)
  // tableData.value.dataList = refValue.value ?? []
  // orgTableData.value = JSON.stringify(refValue.value)
  // //引継情報.新規モード =TRUEの場合
  // if (local.or30918.newMode) {
  //   //週間計画入力情報一覧の最後に新規行を追加する
  //   tableData.value.dataList.push({ ...Or30918Const.DEFAULT.WEEKLY_PLAN })
  //   //新規行を選択状態にする
  //   selectRow(tableData.value.dataList.length - 1)
  // } else {
  //   //呼び出し元から指定されたID（引継情報.選択された週間計画詳細データID）に該当する行を選択状態とする
  //   const detailIndexSelected = tableData.value.dataList.findIndex(
  //     (x) => x.id === local.or30918.selectWeekPlanDetailDataId
  //   )
  //   if (detailIndexSelected !== -1) selectRow(detailIndexSelected)
  // }
}

/**
 *  汎用コードマスタデータを取得し初期化
 */
async function getInitDataInfo() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_LETTER_SIZE },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_LETTER_POSITION },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_OMITTED },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_KBN_TIME_DISPLAY },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_EVERYDAYLIFEACTIVITIES },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_INDEPENDENCESUPPORT },
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_PREVENTIVECARE },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  // コード取得
  localOneway.si039OneWay.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_LETTER_SIZE
  )
  localOneway.si041OneWay.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_LETTER_POSITION
  )
  localOneway.si042OneWay.items = CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_OMITTED)
  localOneway.si047OneWay.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_KBN_TIME_DISPLAY
  )
  localOneway.si048OneWay.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_EVERYDAYLIFEACTIVITIES
  )
    .concat(CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_INDEPENDENCESUPPORT))
    .concat(CmnSystemCodeRepository.filter(CmnMCdKbnId.M_CD_KBN_ID_PREVENTIVECARE))

  // 期間内履歴選択初期情報取得(IN)
  const inputData: WeekTableImageInitSelectInEntity = {
    shisetuId: '4',
    userId: '6',
    svJigyoId: '5',
    syubetsuId: '1',
    sc1Id: '2',
    jigyoList: ['3'],
    jigyoGpId: '7',
  }
  // 期間内履歴選択初期情報取得
  const res: WeekTableImageInitSelectOutEntity = await ScreenRepository.select(
    'weekTableImageInitSelect',
    inputData
  )

  console.log(res)

  // 戻り値はテーブルデータとして処理されます
  // ret.data.kikanNaiRirekiList?.forEach(() => {

  // })
}

/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
function onClose() {
  setState({ isOpen: false })
}

/**
 * 行選択
 *
 * @param index - 選択した行のindex
 */
function selectRow(index: number) {
  selectedItemIndex.value = index
}

/**
 * onChangeFontSize
 *
 * @param event - data
 *
 * @param index - data
 */
function onChangeFontSize(event: unknown, index: number) {
  tableData.value.dataList[index].fontSizeTitle = {
    value: tableData.value.dataList[index].fontSize.modelValue,
  }
}

/**
 * isCheckWeekOther
 *
 * @param value - value
 */
function isCheckWeekOther(value: boolean) {
  return value
}

/**
 * 次へアイコン
 */
function moveUp() {
  if (selectedItemIndex.value > 0) selectRow(selectedItemIndex.value - 1)
}

/**
 * 前へアイコン
 */
function moveDown() {
  if (selectedItemIndex.value < tableData.value.dataList.length - 1)
    selectRow(selectedItemIndex.value + 1)
}

/**
 * 保険サービス削除画面
 *
 * @param item - WeeklyPlan item data
 *
 * @param index - WeeklyPlan item index
 */
function onShowDeleteInsurance(item: WeeklyPlan, index: number) {
  mo00024Delete.value.isOpen = true
  selectedItemIndex.value = index
}

/**
 * 行追加
 */
function onAddItem() {
  tableData.value.dataList.push({ ...Or30918Const.DEFAULT.WEEKLY_PLAN })
  selectRow(tableData.value.dataList.length - 1)
}

/**
 * 行複写
 */
function onCloneItem() {
  tableData.value.dataList.push({ ...tableData.value.dataList[selectedItemIndex.value] })
  selectRow(tableData.value.dataList.length - 1)
}

/**
 * 行削除
 */
function onDelete() {
  if (selectedItemIndex.value !== -1) {
    showOr21814Msg(t('message.i-cmn-10219'))
  }
}

/**
 * 確認メッセージを表示する
 *
 * @param confirmMsg - エラー内容
 */
function showOr21814Msg(confirmMsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: confirmMsg,
      firstBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.no'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 全て削除
 *
 */
function onDeleteAll() {
  tableData.value.dataList[selectedItemIndex.value].insuranceType = ''
  tableData.value.dataList[selectedItemIndex.value].insuranceBussiness = ''
  tableData.value.dataList[selectedItemIndex.value].insuranceServices = ''
  tableData.value.dataList[selectedItemIndex.value].insuranceAddition = ''
  mo00024Delete.value.isOpen = false
}

/**
 * 事業者を削除
 *
 */
function onDeleteOperator() {
  tableData.value.dataList[selectedItemIndex.value].insuranceBussiness = ''
  tableData.value.dataList[selectedItemIndex.value].insuranceServices = ''
  tableData.value.dataList[selectedItemIndex.value].insuranceAddition = ''
  mo00024Delete.value.isOpen = false
}

/**
 * サービスを削除
 *
 */
function onDeleteServices() {
  tableData.value.dataList[selectedItemIndex.value].insuranceServices = ''
  mo00024Delete.value.isOpen = false
}

/**
 * キャンセルボタン
 *
 */
function onCancelDelete() {
  mo00024Delete.value.isOpen = false
}

/**
 *「曜日-全選択ボタン」押下
 *
 * @param item - データ
 *
 * @param flag - アクションフラグ
 * 1 - 曜日-全選択ボタン
 * 0 - 週単位以外(他)チェックボックス
 */
function onCheckAllDay(item: WeeklyPlan, flag: number) {
  let checked = true
  if (flag === 1) {
    // 曜日-全選択ボタン
    // 月～日は全部チェックONの場合	-	月～日は全部チェックOFFにする
    if (
      ((((((item.dayOfWeek1 === item.dayOfWeek2) === item.dayOfWeek3) === item.dayOfWeek4) ===
        item.dayOfWeek5) ===
        item.dayOfWeek6) ===
        item.dayOfWeek7) ===
      true
    ) {
      checked = false
    }
    // 月～日は全部チェックON以外の場合	-	月～日は全部チェックONにする
    else if (
      ((((((item.dayOfWeek1 === item.dayOfWeek2) === item.dayOfWeek3) === item.dayOfWeek4) ===
        item.dayOfWeek5) ===
        item.dayOfWeek6) ===
        item.dayOfWeek7) ===
      false
    ) {
      checked = true
      item.dayOfWeekOther = false
    }
  } else if (flag === 0) {
    // 週単位以外(他)
    checked = false
  }
  item.dayOfWeek1 = checked
  item.dayOfWeek2 = checked
  item.dayOfWeek3 = checked
  item.dayOfWeek4 = checked
  item.dayOfWeek5 = checked
  item.dayOfWeek6 = checked
  item.dayOfWeek7 = checked
}

/**
 * onChangeDay
 *
 * @param item - データ
 */
function onChangeDay(item: WeeklyPlan) {
  if (
    (item.dayOfWeek1 ||
      item.dayOfWeek2 ||
      item.dayOfWeek3 ||
      item.dayOfWeek4 ||
      item.dayOfWeek5 ||
      item.dayOfWeek6 ||
      item.dayOfWeek7) === true
  ) {
    item.dayOfWeekOther = false
  }
}

const isEdit = ref<boolean>(false)

// ダイアログ表示フラグ
const showDialogOr26280 = computed(() => {
  // Or00100のダイアログ開閉状態
  return Or26280Logic.state.get(or26280.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogOr26273 = computed(() => {
  // Or26273 cks_flg=1 のダイアログ開閉状態
  return Or26273Logic.state.get(or26273.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 頻度選択画面アイコンボタン
 *
 * @param index - インデックス
 */
function onShowHindo(index: number) {
  selectedItemIndex.value = index
  localOneway.or26280Oneway.allowAlternateWeekSelection = true
  localOneway.or26280Oneway.serviceType = '21'
  Or26280Logic.state.set({
    uniqueCpId: or26280.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 内容選択画面アイコンボタン
 *
 * @param index - インデックス
 */
const onShowContents = (index: number) => {
  selectedItemIndex.value = index
  // 引継情報.計画書様式を設定する。
  Or26273Logic.state.set({
    uniqueCpId: or26273.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * メモ選択画面アイコンボタン
 *
 * @param index - インデックス
 */
// const onShowMemo = (index: number) => {
//   selectedItemIndex.value = index
//   // 引継情報.計画書様式を設定する。
//   Gui00038Logic.state.set({
//     uniqueCpId: gui00038.value.uniqueCpId,
//     state: { isOpen: true },
//   })
// }

/**
 * 加算情報画面起動
 *
 * @param index - インデックス
 */
function onShowAdditionInfo(index: number) {
  selectedItemIndex.value = index
  // Or27696のダイアログ開閉状態を更新する
  Or27696Logic.state.set({
    uniqueCpId: or27696.value.uniqueCpId,
    state: { isOpen: true },
  })
}

// ダイアログ表示フラグ
const showDialogOr27696 = computed(() => {
  // Or27696のダイアログ開閉状態
  return Or27696Logic.state.get(or27696.value.uniqueCpId)?.isOpen ?? false
})

// ダイアログ表示フラグ
const showDialogGui00038 = computed(() => {
  return Gui00038Logic.state.get(gui00038.value.uniqueCpId)?.isOpen ?? false
})

// 前回正しい時間
let lastValidTime: string | null = null

/**
 * 前回正しい時間
 *
 * @param index - インデックス
 *
 * @param kbn - 区分
 */
function focusTime(index: number, kbn: number) {
  lastValidTime =
    kbn === 1 ? tableData.value.dataList[index].startTime : tableData.value.dataList[index].endTime
}

/**
 * 時間フォーマット
 *
 * @param index - インデックス
 *
 * @param kbn - 区分
 */
function formatTime(index: number, kbn: number) {
  const timeStr =
    kbn === 1 ? tableData.value.dataList[index].startTime : tableData.value.dataList[index].endTime
  const parts = timeStr.split(':')
  if (parts.length !== 2) {
    if (kbn === 1) {
      tableData.value.dataList[index].startTime = lastValidTime ?? timeStr
    } else {
      tableData.value.dataList[index].endTime = lastValidTime ?? timeStr
    }
    return
  }

  const [hh, mm] = parts
  const isNumeric = (str: string) => /^\d+$/.test(str)
  const isValid = isNumeric(hh) && isNumeric(mm)

  if (!isValid) {
    if (kbn === 1) {
      tableData.value.dataList[index].startTime = lastValidTime ?? timeStr
    } else {
      tableData.value.dataList[index].endTime = lastValidTime ?? timeStr
    }
    return
  }

  const hour = parseInt(hh, 10) % 24
  const minute = parseInt(mm, 10) % 60
  const formatted = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`

  lastValidTime = formatted
  if (kbn === 1) {
    tableData.value.dataList[index].startTime = lastValidTime
  } else {
    tableData.value.dataList[index].endTime = lastValidTime
  }
}

const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})
</script>

<template>
  <div class="title-container">
    <c-v-row no-gutters>
      <c-v-col style="padding-top: 8px">
        <!-- 行追加ボタン -->
        <g-custom-or-26168 @click="onAddItem" />
        <!-- 行複写ボタン -->
        <g-custom-or-26170
          class="ml-1"
          :disabled="selectedItemIndex == -1"
          @click="onCloneItem"
        />
        <!-- 削除ボタン -->
        <g-custom-or-26171
          class="ml-1"
          :disabled="selectedItemIndex == -1"
          @click="onDelete"
        />
      </c-v-col>

      <c-v-col
        cols="1"
        class="flex-end"
      >
        <g-custom-or-26184
          v-model="or26184"
          @up="moveUp"
          @down="moveDown"
        />
      </c-v-col>

      <c-v-col cols="12" style="padding-top: 8px;">
        <!-- データテーブル -->
        <c-v-data-table
          v-resizable-grid="{ columnWidths: columnMinWidth }"
          fixed-header
          :headers="headers"
          :items="tableData.dataList"
          class="table-header"
          hide-default-footer
          items-per-page="-1"
          height="500px"
        >
          <!-- *表示順 -->
          <template #[`header.startTime`]>
            <span>
              <span style="color: red">{{ Or30918Const.DEFAULT.REQUIRED }}</span>
              {{ t('label.weekly-plan-timezone') }}
            </span>
          </template>

          <template #[`header.dayOfWeek`]>
            <span>
              <span style="color: red">{{ Or30918Const.DEFAULT.REQUIRED }}</span>
              {{ t('label.weekly-plan-day-of-week') }}
            </span>
          </template>

          <template #[`header.letterSize`]>
            <span>
              <span style="padding-right: 62px">{{ t('label.weekly-plan-letter') }}</span>
              <br />
              <span style="padding-right: 48px">{{ t('label.weekly-plan-size') }}</span>
            </span>
          </template>
          <!-- 一覧 -->
          <template #item="{ item, index }">
            <tr
              :class="{ 'select-row': selectedItemIndex === index }"
              @click="selectRow(index)"
            >
              <td>
                <div class="flex-space-between">
                  <base-at-text-field
                    v-model="item.startTime"
                    v-bind="localOneway.mo01227Oneway"
                    @blur="formatTime(index, 1)"
                    @focus="focusTime(index, 1)"
                  ></base-at-text-field>
                  <span class="ml-2 mr-2">～</span>
                  <!-- 時間帯-終了時間テキストボックス -->
                  <base-at-text-field
                    v-model="item.endTime"
                    v-bind="localOneway.mo01227Oneway"
                    @blur="formatTime(index, 2)"
                    @focus="focusTime(index, 2)"
                  ></base-at-text-field>

                  <div class="d-flex">
                    <div class="devider ml-2 mr-1"></div>
                    <!-- TODO GUI00055_時間入力支援(範囲選択) ペンディングNo.136 -->
                    <!-- 時間帯-入力支援アイコン -->
                    <!-- <base-mo00009
                      :oneway-model-value="localOneway.mo00009OneWay1"
                      @click.stop="onShowTimeRange()"
                    ></base-mo00009> -->
                  </div>
                </div>
              </td>
              <td>
                <div class="d-flex">
                  <!-- 曜日選択ボタン -->
                  <!-- 曜日-全選択ボタン -->
                  <base-mo00612
                    :oneway-model-value="localOneway.si017OneWay"
                    class="btn-chk-all"
                    @click.stop="onCheckAllDay(item, 1)"
                  >
                    <c-v-tooltip
                      activator="parent"
                      location="top"
                      :text="t('label.weekly-plan-full-select')"
                    />
                  </base-mo00612>
                  <div class="flex-wrap">
                    <!-- 月曜日チェックボックス -->
                    <base-at-checkbox
                      v-model="item.dayOfWeek1"
                      :checkbox-label="t('label.weekly-plan-day-short-sunday')"
                      color="key"
                      class="chk-custom"
                      @change="onChangeDay(item)"
                    />
                    <!-- 火曜日チェックボックス -->
                    <base-at-checkbox
                      v-model="item.dayOfWeek2"
                      :checkbox-label="t('label.weekly-plan-day-short-monday')"
                      color="key"
                      class="chk-custom"
                      @change="onChangeDay(item)"
                    />
                    <!-- 水曜日チェックボックス -->
                    <base-at-checkbox
                      v-model="item.dayOfWeek3"
                      :checkbox-label="t('label.weekly-plan-day-short-tuesday')"
                      color="key"
                      class="chk-custom"
                      @change="onChangeDay(item)"
                    />
                    <!-- 木曜日チェックボックス -->
                    <base-at-checkbox
                      v-model="item.dayOfWeek4"
                      :checkbox-label="t('label.weekly-plan-day-short-wednesday')"
                      color="key"
                      class="chk-custom"
                      @change="onChangeDay(item)"
                    />
                    <!-- 金曜日チェックボックス -->
                    <base-at-checkbox
                      v-model="item.dayOfWeek5"
                      :checkbox-label="t('label.weekly-plan-day-short-thursday')"
                      color="key"
                      class="chk-custom"
                      @change="onChangeDay(item)"
                    />
                    <!-- 土曜日チェックボックス -->
                    <base-at-checkbox
                      v-model="item.dayOfWeek6"
                      :checkbox-label="t('label.weekly-plan-day-short-friday')"
                      color="key"
                      class="chk-custom"
                      @change="onChangeDay(item)"
                    />
                    <!-- 日曜日チェックボックス -->
                    <base-at-checkbox
                      v-model="item.dayOfWeek7"
                      :checkbox-label="t('label.weekly-plan-day-short-saturday')"
                      color="key"
                      class="chk-custom"
                      @change="onChangeDay(item)"
                    />
                    <!-- 週単位以外(他)チェックボックス -->
                    <base-at-checkbox
                      v-model="item.dayOfWeekOther"
                      :checkbox-label="t('label.weekly-plan-day-short-other')"
                      color="key"
                      class="chk-custom chk-other"
                      @click.stop="onCheckAllDay(item, 0)"
                    />
                  </div>
                </div>
              </td>
              <td class="pa-0">
                <div class="flex-space-between">
                  <!-- 内容プールダウンリスト -->
                  <base-mo01282
                    v-model="item.contents"
                    :oneway-model-value="localOneway.si048OneWay"
                    class="cell-select cell-input w-120"
                    @change="onChangeFontSize($event, index)"
                  ></base-mo01282>
                  <div class="devider"></div>
                  <base-mo00009
                    v-model="item.contents"
                    :oneway-model-value="localOneway.mo00009OneWay2"
                    @click.stop="onShowContents(index)"
                  />
                </div>
              </td>
              <td class="pa-0">
                <div class="flex-space-between">
                  <!-- メモ -->
                  <base-mo01280
                    v-model="item.memo"
                    class="itemContent"
                  >
                  </base-mo01280>
                  <!-- <div class="devider"></div> -->
                  <!-- TODO GUI00038_入力支援 ペンディングNo.97 -->
                  <!-- <base-mo00009
                    :oneway-model-value="localOneway.mo00009OneWay2"
                    @click.stop="onShowMemo(index)"
                  ></base-mo00009> -->
                </div>
              </td>
              <td :class="{ 'cell-disabled': !isCheckWeekOther(item.dayOfWeekOther) }">
                <div class="flex-space-between">
                  <!-- 頻度 -->
                  <base-mo01280
                    class="itemContent"
                    :model-value="{ value: item.frequency }"
                    readonly="true"
                  >
                  </base-mo01280>
                  <div class="devider"></div>
                  <base-mo00009
                    :oneway-model-value="localOneway.mo00009OneWay2"
                    :disabled="!isCheckWeekOther(item.dayOfWeekOther)"
                    @click="onShowHindo(index)"
                  ></base-mo00009>
                </div>
              </td>
              <td :class="{ 'cell-disabled': isCheckWeekOther(item.dayOfWeekOther) }">
                <div class="d-flex">
                  <!-- 文字サイズセレクトボックス -->
                  <base-mo01282
                    v-model="item.fontSize"
                    :oneway-model-value="localOneway.si039OneWay"
                    :disabled="isCheckWeekOther(item.dayOfWeekOther)"
                    class="cell-select cell-input w-50"
                    @change="onChangeFontSize($event, index)"
                  ></base-mo01282>

                  <!-- 文字サイズテキストボックス -->
                  <base-mo01278
                    v-model="item.fontSizeTitle"
                    :oneway-model-value="localOneway.si040OneWay"
                    :disabled="isCheckWeekOther(item.dayOfWeekOther)"
                    class="cell-input deviders"
                  ></base-mo01278>
                </div>
              </td>
              <td :class="{ 'cell-disabled': isCheckWeekOther(item.dayOfWeekOther) }">
                <!-- 文字位置セレクトボックス -->
                <base-mo01282
                  v-model="item.textPosition"
                  :oneway-model-value="localOneway.si041OneWay"
                  :disabled="isCheckWeekOther(item.dayOfWeekOther)"
                  class="cell-select"
                ></base-mo01282>
              </td>
              <td :class="{ 'cell-disabled': isCheckWeekOther(item.dayOfWeekOther) }">
                <!-- 省略セレクトボックス -->
                <base-mo01282
                  v-model="item.omission"
                  :oneway-model-value="localOneway.si042OneWay"
                  :disabled="isCheckWeekOther(item.dayOfWeekOther)"
                  class="cell-select"
                ></base-mo01282>
              </td>
              <td :class="{ 'cell-disabled': isCheckWeekOther(item.dayOfWeekOther) }">
                <!-- 文字色ラベル -->
                <g-custom-or-26281
                  v-model="item.color"
                  :oneway-model-value="{ disabled: isCheckWeekOther(item.dayOfWeekOther) }"
                />
              </td>
              <td :class="{ 'cell-disabled': isCheckWeekOther(item.dayOfWeekOther) }">
                <!-- 背景色ラベル -->
                <g-custom-or-26281
                  v-model="item.background"
                  :oneway-model-value="{ disabled: isCheckWeekOther(item.dayOfWeekOther) }"
                />
              </td>
              <td>
                <!-- 時間表示セレクトボックス -->
                <base-mo01282
                  v-model="item.timeDisplay"
                  :oneway-model-value="localOneway.si047OneWay"
                  class="cell-select"
                ></base-mo01282>
              </td>
              <!-- サービス選択 -->
              <td class="pa-1">
                <div class="flex-space-between">
                  <!-- 種類ラベル -->
                  <span>{{ item.insuranceType }}</span>
                  <div class="d-flex">
                    <div class="devider"></div>
                    <!-- 保険サービス-入力支援アイコン -->
                    <base-mo00009 :oneway-model-value="localOneway.mo00009OneWay2"></base-mo00009>
                    <!-- TODO QA #138097 -->
                  </div>
                </div>
              </td>
              <td class="pa-1">
                <!-- 事業者 -->
                <span>{{ item.insuranceBussiness }}</span>
              </td>
              <td class="pa-1">
                <!-- サービス -->
                <span>{{ item.insuranceServices }}</span>
              </td>
              <td class="pa-1">
                <div class="flex-space-between">
                  <!-- 加算 -->
                  <span :class="{ red: item.insuranceAddition !== '' ? true : false }">{{
                    item.insuranceAddition !== '' ? '有' : '无'
                  }}</span>
                  <div class="d-flex">
                    <div class="devider"></div>
                    <!-- 保険サービス-入力支援アイコン -->
                    <base-mo00009
                      :oneway-model-value="localOneway.mo00009OneWay2"
                      @click.stop="onShowAdditionInfo(index)"
                    ></base-mo00009>
                  </div>
                </div>
              </td>
              <td>
                <base-mo00009
                  :oneway-model-value="localOneway.mo00009OneWay2"
                  @click="onShowDeleteInsurance(item, index)"
                ></base-mo00009>
              </td>
            </tr>
          </template>
        </c-v-data-table>
        <!-- 内容 -->
        <g-custom-or-26273
          v-if="showDialogOr26273"
          v-bind="or26273"
          v-model="local.or26273"
          :oneway-model-value="localOneway.or26273OnewayModel"
        />
        <!-- 頻度 -->
        <g-custom-or-26280
          v-if="showDialogOr26280"
          v-bind="or26280"
          v-model="local.or26280"
          :oneway-model-value="localOneway.or26280Oneway"
          :unique-cp-id="or26280.uniqueCpId"
          :parent-cp-id="props.uniqueCpId"
        />
        <!-- 加算 -->
        <g-custom-or-27696
          v-if="showDialogOr27696"
          v-bind="or27696"
          v-model="local.or27696"
          :oneway-model-value="localOneway.or27696Oneway"
        />
        <!-- 入力支援画面 -->
        <g-custom-gui-00038
          v-if="showDialogGui00038"
          v-bind="gui00038"
          :unique-cp-id="gui00038.uniqueCpId"
          :parent-cp-id="props.uniqueCpId"
        />
      </c-v-col>
    </c-v-row>
  </div>

  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  >
  </g-base-or21814>

  <!-- 保険サービスの削除モーダル -->
  <g-custom-or-26702
    v-model="mo00024Delete"
    @delete-all="onDeleteAll"
    @delete-operator="onDeleteOperator"
    @delete-services="onDeleteServices"
    @cancel="onCancelDelete"
  >
  </g-custom-or-26702>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/page-data-table.scss';

:deep(.v-table.v-table--fixed-header > .v-table__wrapper > table > thead > tr > th) {
  background: rgb(var(--v-theme-surface));
  box-shadow: inset 0 0px 0 rgba(var(--v-border-color), var(--v-border-opacity));
  z-index: 1;
}

.flex-wrap {
  display: flex;
  flex-wrap: wrap;
  position: relative;
}

.flex-space-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-end {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}

.devider {
  width: 1px !important;
  height: 40px !important;
  background-color: rgb(var(--v-theme-black-200));
}

:deep(.text-center) {
  input {
    text-align: center;
  }
}

.v-table .v-table__wrapper {
  table {
    tbody {
      td {
        padding: 0 4px;
      }
    }
  }
}

.table-header :deep(.v-table__wrapper th) {
  background-color: rgb(var(--v-theme-black-100)) !important;
  // border: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-top: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-left:1px rgb(var(--v-theme-black-200)) solid !important;
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
  font-size: 14px;
  padding: 0 4px;
  font-weight: bold;
}

.table-header :deep(.v-table__wrapper):last-child {
  border-right: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
}

.table-header :deep(.v-table__wrapper) th:last-child{
  border-bottom: none !important;
}

.table-header :deep(.v-table__wrapper) th:first-child{
  border-left: none !important;
}

.table-header :deep(.v-table__wrapper) thead tr:last-child th:first-child{
  border-left: 1px rgb(var(--v-theme-black-200)) solid !important;
}



.table-header :deep(.v-table__wrapper):first-child {
  border-left: 1px rgb(var(--v-theme-black-200)) solid !important;
}

:deep(.table-header td) {
  border-left:1px rgb(var(--v-theme-black-200)) solid !important;
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
  border-color: rgb(var(--v-theme-black-100));
  max-height: 40px;
  font-size: 14px;
}

:deep(.table-header td):has(input),
:deep(.table-header td):has(select) {
  padding: 0px !important;
}

.cell-disabled {
  background-color: rgb(var(--v-theme-black-50));

  .content {
    text-indent: 100%;
    white-space: nowrap;
    overflow: hidden;
  }
}

// 選択した行のCSS
.select-row {
  background: rgb(var(--v-theme-blue-100));
}

.cell-select {
  padding: 0 !important;
}

.cell-input {
  height: 50px !important;

  &.deviders {
    border-left: 1px solid rgb(var(--v-theme-black-200)) !important;
  }
}

.w-50 {
  width: 50px !important;
}

.devider {
  width: 1px !important;
  height: 30px !important;
  background-color: rgb(var(--v-theme-black-200));
}

.flex-wrap {
  display: flex;
  flex-wrap: wrap;
  position: relative;
}

.btn-chk-all {
  height: 50px;
  background-color: rgb(230,230,230) !important;
}

.chk-other {
  position: absolute;
  bottom: 0;
  right: 7px;
}

.v-checkbox :deep(.v-checkbox-btn) {
  min-height: 25px;
  height: 25px;
}

.red {
  color: rgb(var(--v-theme-red-600));
}
</style>
