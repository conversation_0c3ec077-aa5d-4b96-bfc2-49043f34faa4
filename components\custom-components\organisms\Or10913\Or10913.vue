<script setup lang="ts">
/**
 * Or10913:(要因取込)ダイアログ
 * GUI00918_［要因取込］画面
 *
 * @description
 * (要因取込)ダイアログ
 *
 * <AUTHOR> DAM XUAN HIEU
 */

import { computed, onMounted, reactive, ref, watch, watchEffect } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or10913Const } from './Or10913.constants'
import type { Or10913StateType } from './Or10913.type'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type {
  Or10913OneWayType,
  Or10913Data,
  PlanPeriodInfoList,
  HistoryInfoList,
  DetailInfoObj,
  PlnDetailList,
  AbstractDetailList,
} from '~/types/cmn/business/components/Or10913Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import { useScreenOneWayBind, useSetupChildProps } from '#imports'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { YouinMaster } from '~/components/custom-components/organisms/Or26392/Or26392.type'
import type { Or26392OnewayType } from '~/types/cmn/business/components/Or26392Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type {
  FactorImportPeriodInfoSelectInEntity,
  FactorImportInfoInitSelectOutEntity,
} from '~/repositories/cmn/entities/factorImportPeriodInfoSelectEntity'
import type {
  FactorImportHistoryInfoSelectInEntity,
  FactorImportHistoryInfoSelectOutEntity,
} from '~/repositories/cmn/entities/factorImportHistoryInfoSelectEntity'
import type {
  FactorImportDetailInfoSelectInEntity,
  FactorImportDetailInfoSelectOutEntity,
} from '~/repositories/cmn/entities/factorImportDetailInfoSelectEntity'
import type { FactorImportScreenCloseSelectInEntity } from '~/repositories/cmn/entities/factorImportScreenCloseSelectEntity'

import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or26392Const } from '~/components/custom-components/organisms/Or26392/Or26392.constants'
import { Or26392Logic } from '~/components/custom-components/organisms/Or26392/Or26392.logic'
import { CmnMCdKbnId } from '~/constants/cmn/system-code-constants'

/**
 * 国際化インスタンス（翻訳用）
 */
const { t } = useI18n()

/**************************************************
 * プロパティ
 **************************************************/
/**
 * コンポーネントのプロパティインスタンス
 */
interface Props {
  /**
   * OneWayBindモデル値
   */
  onewayModelValue: Or10913OneWayType
  /**
   * 一意のコンポーネントID
   */
  uniqueCpId: string
}
/**
 * コンポーネントのプロパティインスタンス
 */
const props = defineProps<Props>()

/**
 * Emit
 */
const emit = defineEmits(['update:modelValue'])

/**
 * or21814子コンポーネント用変数
 */
const or21814 = ref({ uniqueCpId: '' })

/**
 * フォーム値のローカル状態
 */
const localOneway = reactive({
  mo00043OneWay: {
    tabItems: [
      {
        id: 'studyForm',
        title: t('label.study-form'),
        tooltipText: t('label.study-form'),
      },
      {
        id: 'summaryTable',
        title: t('label.summary-table'),
        tooltipText: t('label.summary-table'),
      },
      {
        id: 'considerationTable',
        title: t('label.consideration-table'),
        tooltipText: t('label.consideration-table'),
      },
    ],
  } as Mo00043OnewayType,

  mo00611AboveBtnOneWay: {
    btnLabel: t('btn.overwrite-down'),
    tooltipText: t('tooltip.overwrite'),
  } as Mo00611OnewayType,
  mo00611AddBtnOneWay: {
    btnLabel: t('btn.add-down'),
    tooltipText: t('tooltip.add-confirm'),
  } as Mo00611OnewayType,
  mo00009OneWay: {
    btnIcon: 'edit_square',
  },
  mo00045Oneway: {
    maxLength: '84',
    width: '',
    showItemLabel: false,
  } as Mo00045OnewayType,
  //ダイアログ
  mo00024Oneway: {
    width: '1000px',
    height: '800px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      toolbarTitle: t('label.factor-incorporation'),
      toolbarName: 'Or10913ToolBar',
      toolbarTitleCenteredFlg: false,
      showCardActions: true,
    },
  } as Mo00024OnewayType,
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.close-btn'),
  } as Mo00611OnewayType,
  mo00609ConfirmOneway: {
    btnLabel: t('btn.confirm'),
    tooltipText: t('tooltip.confirm-btn'),
  } as Mo00609OnewayType,
})

/**
 * グループ化されたアイテムを計算する
 */
const defaultData: Or10913Data = {
  /**
   * 事業者ID
   */
  svJigyoId: '1',
  /**
   * 利用者ID
   */
  userid: '1',
  /**
   * 種別ID
   */
  syubetsuId: '1',
  /**
   * 施設ID
   */
  shisetuId: '1',
  /**
   * 職員ID
   */
  shokuId: '1',
  /**
   * 期間管理フラグ
   */
  kikanFlg: '1',
  /**
   * システム略称
   */
  sysRyaku: '1',
  /**
   * 検討表（インターライ）_様式
   */
  kentouFlg: '1',
  /**
   * 阻害要因１
   */
  youin1Knj: 'abc',
  /**
   * 阻害要因２
   */
  youin2Knj: 'def',
  /**
   * 阻害要因３
   */
  youin3Knj: 'xyz',
  /**
   * 阻害要因４
   */
  youin4Knj: '',
  /**
   * 阻害要因５
   */
  youin5Knj: '',
  /**
   * 阻害要因６
   */
  youin6Knj: '',
}

/**
 * フォーム値のローカル状態
 */
const valueLocal = reactive({
  or10913: {
    ...defaultData,
    ...props.onewayModelValue,
  },
  mo00043: { id: props.onewayModelValue.tab ?? 'studyForm' } as Mo00043Type,
  assessmentKindList: [] as CodeType[],
  importFactors: [
    {
      label: '①',
      detail: {
        value: 'abc',
      },
    },
    {
      label: '②',
      detail: {
        value: 'xyz',
      },
    },
    {
      label: '③',
      detail: {
        value: '333',
      },
    },
    {
      label: '④',
      detail: {
        value: '',
      },
    },
    {
      label: '⑤',
      detail: {
        value: '',
      },
    },
    {
      label: '⑥',
      detail: {
        value: '',
      },
    },
  ],
  planPeriodInfoList: [] as PlanPeriodInfoList[],
  historyInfoList: [] as HistoryInfoList[],
  detailInfoObj: {
    plnDetailList: [] as PlnDetailList[],
    abstractDetailList: [] as AbstractDetailList[],
  } as DetailInfoObj,
})
/**
 * or26392子コンポーネント用変数
 */
const or26392 = ref({ uniqueCpId: '' })
/**
 * or26392子コンポーネント用変数
 */
const modelValue26392 = ref<YouinMaster>({
  houjinId: '',
  shisetuId: '',
  svJigyoId: '',
  youinCd: '',
  youinKnj: '',
  sort: '',
  modifiedCnt: '',
})
/**
 * or26392子コンポーネント用変数
 */
const or26392OnewayModel: Or26392OnewayType = {
  /**
   * 事業者ID
   */
  svJigyoId: '',
}
/**
 * 選択したインポート要因
 */
const clickIndex = ref(-1)
/**
 * 選択した計画期間
 */
const selectedPlanPeriod = ref(-1)

/**
 * 選択した計画期間
 */
const selectedHistory = ref(-1)

/**
 * 選択した計画期間
 */
const selectedPlnDetail = ref(-1)
/**
 * 選択した計画期間
 */
const selectedAbstractDetail = ref(-1)
/**
 * 選択した計画期間
 */
const selectedImportFactor = ref(0)

/**
 * 選択した計画期間
 */
const selectedSc1Id = ref('')

/**
 * 選択した計画期間
 */
const selectedRaiId = ref('')

/**
 * ヘッダー
 */
const headersTablePlanPeriod = [
  {
    title: t('label.plan-period'),
    key: 'planPeriod',
    align: 'left',
  },
  {
    title: t('label.within-the-period-number-of-history'),
    key: 'sc1Id',
    align: 'left',
  },
]

/**
 * テーブル2のヘッダー
 */
const headersTableHistoryInfo = [
  {
    title: t('label.create_ymd'),
    key: 'plnDateYmd',
    align: 'left',
  },
  {
    title: t('label.author'),
    key: 'author',
    align: 'left',
  },
  {
    title: t('label.assessment-kind'),
    key: 'assTypeName',
    align: 'left',
  },
]

/**
 * テーブル3のヘッダー
 */
const headersTableDetailInfoObj = computed(() => [
  {
    title:
      valueLocal.mo00043.id === 'studyForm'
        ? t('label.caps')
        : valueLocal.mo00043.id === 'summaryTable'
          ? t('label.cap')
          : valueLocal.mo00043.id === 'considerationTable'
            ? t('label.cap-inducement-project')
            : t(''),
    key: 'cap',
    align: 'left',
    width: '200px',
  },
  {
    title:
      valueLocal.mo00043.id === 'studyForm'
        ? t('label.problemKnj-studyForm')
        : valueLocal.mo00043.id === 'summaryTable'
          ? t('label.problemKnj-summaryTable')
          : valueLocal.mo00043.id === 'considerationTable'
            ? t('label.problemKnj-considerationTable')
            : t(''),
    key: 'problemKnj',
    align: 'left',
  },
])

/**
 * フォーム値のローカル状態
 */
const mo00043OneWay = computed(() => {
  if (valueLocal.or10913.tableForm === '1') {
    return {
      tabItems: [
        {
          id: 'studyForm',
          title: t('label.study-form'),
          tooltipText: t('label.study-form'),
        },
        {
          id: 'summaryTable',
          title: t('label.summary-table'),
          tooltipText: t('label.summary-table'),
        },
      ],
    }
  } else {
    return {
      tabItems: [
        {
          id: 'considerationTable',
          title: t('label.consideration-table'),
          tooltipText: t('label.consideration-table'),
        },
      ],
    }
  }
})

/**
 * モーダルの状態
 */
const mo00024 = ref<Mo00024Type>({
  isOpen: Or10913Const.DEFAULT.IS_OPEN,
})

/**
 * Piniaストアの状態管理
 */
const { setState } = useScreenOneWayBind<Or10913StateType>({
  cpId: Or10913Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    /**
     * モーダルの開閉状態を更新
     *
     * @param value - モーダルを開くかどうかを示すブール値
     */
    isOpen: (value?: boolean) => {
      mo00024.value.isOpen = value ?? Or10913Const.DEFAULT.IS_OPEN
    },
  },
})

// モーダルの開閉状態を監視
watch(
  () => mo00024.value.isOpen,
  async (newValue) => {
    if (!newValue) {
      const inputMockDataCloseScreen: FactorImportScreenCloseSelectInEntity = {
        shokuinId: valueLocal.or10913.userid,
        sysRyaku: valueLocal.or10913.sysRyaku,
        asSection: Or10913Const.VARIABLE.asSection,
        asKey: Or10913Const.VARIABLE.asKey,
        asDefault: valueLocal.mo00043.id === 'summaryTable' ? '2' : '1',
      }

      await ScreenRepository.select('factorImportScreenCloseSelect', inputMockDataCloseScreen)
      onCloseDialog()
    }
  }
)

watch(
  () => modelValue26392.value,
  (newValue) => {
    if (newValue.youinKnj === '') {
      return
    }
    if (clickIndex.value > -1) {
      valueLocal.importFactors[clickIndex.value].detail.value = newValue.youinKnj
    }
  }
)

watch(
  () => selectedPlanPeriod.value,
  async (newValue) => {
    if (newValue === -1) {
      return
    }

    selectedSc1Id.value = valueLocal.planPeriodInfoList[newValue].sc1Id
    const inputMockData2: FactorImportHistoryInfoSelectInEntity = {
      svJigyoId: valueLocal.or10913.svJigyoId,
      userid: valueLocal.or10913.userid,
      // svJigyoId: '21',
      // userid: '148',
      sc1Id: selectedSc1Id.value,
    }
    const resMock2 = await ScreenRepository.select('factorImportHistoryInfoSelect', inputMockData2)
    const resMockData2 = resMock2.data as FactorImportHistoryInfoSelectOutEntity
    valueLocal.historyInfoList = resMockData2.historyInfoList
    selectedHistory.value = 0
  }
)

watch(
  () => selectedHistory.value,
  (newValue) => {
    if (newValue === -1) {
      return
    }
    selectedRaiId.value = valueLocal.historyInfoList[newValue].raiId
  }
)

watchEffect(() => {
  ;(async () => {
    if (selectedSc1Id.value === '' || selectedRaiId.value === '') {
      return
    }
    const inputMockData3: FactorImportDetailInfoSelectInEntity = {
      sc1Id: selectedSc1Id.value,
      raiId: selectedRaiId.value,
      kentouFlg: valueLocal.or10913.kentouFlg,
    }
    const resMock3 = await ScreenRepository.select('factorImportDetailInfoSelect', inputMockData3)
    const resMockData3 = resMock3.data as FactorImportDetailInfoSelectOutEntity
    valueLocal.detailInfoObj = resMockData3.detailInfoObj
    if (valueLocal.detailInfoObj?.plnDetailList?.length > 0) {
      selectedPlnDetail.value = 0
    }
    if (valueLocal.detailInfoObj?.abstractDetailList?.length > 0) {
      selectedAbstractDetail.value = 0
    }
  })()
})

/**
 * ダイアログ表示フラグ
 */
const showOr21814Dialog = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(1)]: or21814.value,
  [Or26392Const.CP_ID(1)]: or26392.value,
})

// 初期化処理を実行
onMounted(async () => {
  await init()
})

/**
 * コンポーネントの状態を初期化し、初期データを読み込む
 */
async function init() {
  const selectCodeKbnList = [{ mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND }]
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  valueLocal.assessmentKindList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND
  )

  const inputMockData: FactorImportPeriodInfoSelectInEntity = {
    svJigyoId: valueLocal.or10913.svJigyoId,
    userid: valueLocal.or10913.userid,
    syubetsuId: valueLocal.or10913.syubetsuId,
    shisetuId: valueLocal.or10913.shisetuId,
    shokuId: valueLocal.or10913.shokuId,
    kikanFlg: valueLocal.or10913.kikanFlg,
    sysRyaku: valueLocal.or10913.sysRyaku,
    kentouFlg: valueLocal.or10913.kentouFlg,
    // svJigyoId: '21',
    // userid: '148',
    // shokuId: '1',
    // shisetuId: '6',
    // syubetsuId: '2',
    // kikanFlg: '1',
    // sysRyaku: '11',
    // kentouFlg: '1',
  }
  const resMock = await ScreenRepository.select('factorImportPeriodInfoSelect', inputMockData)
  const resMockData = resMock.data as FactorImportInfoInitSelectOutEntity
  valueLocal.planPeriodInfoList = resMockData.planPeriodInfoList
  selectedPlanPeriod.value = 0
}

/**
 * アセスメント種別名を取得
 *
 * @description
 * アセスメント種別名を取得
 *
 * @param assTypeName - アセスメント種別名
 *
 * @returns アセスメント種別名
 */
function getAssessmentKindName(assTypeName: string) {
  return valueLocal.assessmentKindList.find((code) => code.value === assTypeName)?.label
}

/**
 * 編集ボタンを押した時の処理
 *
 * @param i - インポート要因のインデックス
 */
function onClickEdit(i: number) {
  Or26392Logic.state.set({
    uniqueCpId: or26392.value.uniqueCpId,
    state: { isOpen: true },
  })
  clickIndex.value = i
  modelValue26392.value = {
    houjinId: '',
    shisetuId: '',
    svJigyoId: '',
    youinCd: '',
    youinKnj: '',
    sort: '',
    modifiedCnt: '',
  }
  selectedImportFactor.value = i
}

/**
 * モーダルを閉じる
 */
function onCloseDialog() {
  setState({ isOpen: false })
}

/**
 * 確定ボタンを押した時の処理
 */
function onConfirmDialog() {
  const data = {
    youin1Knj: valueLocal.importFactors[0].detail.value,
    youin2Knj: valueLocal.importFactors[1].detail.value,
    youin3Knj: valueLocal.importFactors[2].detail.value,
    youin4Knj: valueLocal.importFactors[3].detail.value,
    youin5Knj: valueLocal.importFactors[4].detail.value,
    youin6Knj: valueLocal.importFactors[5].detail.value,
  }
  emit('update:modelValue', data)
  onCloseDialog()
}

/**
 * 計画期間を選択した時の処理
 *
 * @param index - 計画期間のインデックス
 */
function onSelectPlanPeriod(index: number) {
  selectedPlanPeriod.value = index
}

/**
 * 計画期間を選択した時の処理
 *
 * @param index - 誘因項目のインデックス
 */
function onSelectPlnDetail(index: number) {
  selectedPlnDetail.value = index
}

/**
 * 誘因項目を選択した時の処理
 *
 * @param index - 誘因項目のインデックス
 */
function onSelectAbstractDetail(index: number) {
  selectedAbstractDetail.value = index
}

/**
 * 履歴を選択した時の処理
 *
 * @param index - 計画期間のインデックス
 */
function onSelectHistory(index: number) {
  selectedHistory.value = index
}

/**
 * 上書きボタンを押した時の処理
 *
 * @returns 誘因項目のデータ
 */
function getDataDetailInfoObj() {
  const currentTab = valueLocal.mo00043.id
  if (currentTab === 'studyForm') {
    return valueLocal.detailInfoObj.plnDetailList[selectedPlnDetail.value]
  } else {
    return valueLocal.detailInfoObj.abstractDetailList[selectedAbstractDetail.value]
  }
}

/**
 * 上書きボタンを押した時の処理
 *
 * @returns 確認ダイアログの結果
 */
async function onClickAboveBtn() {
  const getData = getDataDetailInfoObj()
  const getDataProblemKnj = getData.problemKnj
  const currentSelectedImportFactor =
    valueLocal.importFactors[selectedImportFactor.value].detail.value

  if (getDataProblemKnj === '') {
    return
  }

  if (currentSelectedImportFactor !== '') {
    const dialogResult = await openConfirmDialog()
    switch (dialogResult) {
      case 'yes':
        valueLocal.importFactors[selectedImportFactor.value].detail.value = getDataProblemKnj
        break
      case 'no':
        return
    }
  } else {
    valueLocal.importFactors[selectedImportFactor.value].detail.value = getDataProblemKnj
  }
}

/**
 * 追加ボタンを押した時の処理
 */
function onClickAddBtn() {
  const getData = getDataDetailInfoObj()
  const getDataProblemKnj = getData.problemKnj

  if (getDataProblemKnj === '') {
    return
  }
  valueLocal.importFactors[selectedImportFactor.value].detail.value =
    valueLocal.importFactors[selectedImportFactor.value].detail.value + getDataProblemKnj
  if (valueLocal.importFactors[selectedImportFactor.value].detail.value.length > 84) {
    valueLocal.importFactors[selectedImportFactor.value].detail.value = valueLocal.importFactors[
      selectedImportFactor.value
    ].detail.value.slice(0, 84)
  }
}

/**
 * インポート要因を選択した時の処理
 *
 * @param index - インポート要因のインデックス
 */
function onSelectImportFactor(index: number) {
  selectedImportFactor.value = index
}

/**
 * 確認ダイアログを開く
 *
 * @returns 確認ダイアログの結果
 */
async function openConfirmDialog(): Promise<'yes' | 'no'> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: t('message.i-cmn-10218'),
      firstBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.no'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)
        let result = 'no' as 'yes' | 'no'
        if (event?.secondBtnClickFlg) {
          result = 'yes'
        }
        if (event?.thirdBtnClickFlg) {
          result = 'no'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <base-mo00043
        v-model="valueLocal.mo00043"
        :oneway-model-value="mo00043OneWay"
      >
      </base-mo00043>
      <c-v-window v-model="valueLocal.mo00043.id">
        <c-v-window-item value="studyForm">
          <c-v-row
            no-gutters
            class="mt-2"
          >
            <c-v-col
              v-if="valueLocal.or10913.planningPeriod === '1'"
              cols="5"
            >
              <c-v-data-table
                :headers="headersTablePlanPeriod"
                hide-default-footer
                :items="valueLocal.planPeriodInfoList"
                class="table-header table-wrapper list-wrapper"
                height="102px"
                fixed-header
                hover
                :items-per-page="-1"
              >
                <template #item="{ item, index }">
                  <tr
                    :class="{ 'select-row': selectedPlanPeriod === index }"
                    @click="onSelectPlanPeriod(index)"
                  >
                    <td>
                      <base-mo01337
                        :oneway-model-value="{ value: `${item.startYmd} ～ ${item.endYmd}` }"
                      />
                    </td>
                    <td>
                      <base-mo01336 :oneway-model-value="{ value: item.sc1Id }" />
                    </td>
                  </tr>
                </template>
              </c-v-data-table>
            </c-v-col>
            <c-v-col cols="7">
              <c-v-data-table
                :headers="headersTableHistoryInfo"
                hide-default-footer
                :items="valueLocal.historyInfoList"
                class="table-header table-wrapper list-wrapper"
                height="102px"
                fixed-header
                hover
                :items-per-page="-1"
              >
                <template #item="{ item, index }">
                  <tr
                    :class="{ 'select-row': selectedHistory === index }"
                    @click="onSelectHistory(index)"
                  >
                    <td>
                      <base-mo01335 :oneway-model-value="{ value: item.plnDateYmd }" />
                    </td>
                    <td>
                      <base-mo01337 :oneway-model-value="{ value: item.plnShokuNm }" />
                    </td>
                    <td>
                      <base-mo01337
                        :oneway-model-value="{ value: getAssessmentKindName(item.plnType) }"
                      />
                    </td>
                  </tr>
                </template>
              </c-v-data-table>
            </c-v-col>
          </c-v-row>
          <c-v-divider class="my-2" />
          <c-v-row no-gutters>
            <c-v-col cols="10">
              <c-v-data-table
                :headers="headersTableDetailInfoObj"
                hide-default-footer
                :items="valueLocal.detailInfoObj.plnDetailList ?? []"
                class="table-header table-wrapper list-wrapper"
                fixed-header
                hover
                :items-per-page="-1"
                style="height: 148px"
              >
                <template #item="{ item, index }">
                  <tr
                    :class="{ 'select-row': selectedPlnDetail === index }"
                    @click="onSelectPlnDetail(index)"
                  >
                    <td>
                      <base-mo01337
                        :oneway-model-value="{ value: `${item.capId} ${item.capKnj}` }"
                      />
                    </td>
                    <td>
                      <base-mo01337 :oneway-model-value="{ value: item.problemKnj }" />
                    </td>
                  </tr>
                </template>
              </c-v-data-table>
            </c-v-col>
          </c-v-row>
          <c-v-divider class="my-2" />
        </c-v-window-item>
        <c-v-window-item value="summaryTable">
          <c-v-row
            no-gutters
            class="mt-2"
          >
            <c-v-col
              v-if="valueLocal.or10913.planningPeriod === '1'"
              cols="5"
            >
              <c-v-data-table
                :headers="headersTablePlanPeriod"
                hide-default-footer
                :items="valueLocal.planPeriodInfoList"
                class="table-header table-wrapper list-wrapper"
                height="102px"
                fixed-header
                hover
                :items-per-page="-1"
              >
                <template #item="{ item, index }">
                  <tr
                    :class="{ 'select-row': selectedPlanPeriod === index }"
                    @click="onSelectPlanPeriod(index)"
                  >
                    <td>
                      <base-mo01337
                        :oneway-model-value="{ value: `${item.startYmd} ～ ${item.endYmd}` }"
                      />
                    </td>
                    <td>
                      <base-mo01336 :oneway-model-value="{ value: item.sc1Id }" />
                    </td>
                  </tr>
                </template>
              </c-v-data-table>
            </c-v-col>
            <c-v-col cols="7">
              <c-v-data-table
                :headers="headersTableHistoryInfo"
                hide-default-footer
                :items="valueLocal.historyInfoList"
                class="table-header table-wrapper list-wrapper"
                height="102px"
                fixed-header
                hover
                :items-per-page="-1"
              >
                <template #item="{ item, index }">
                  <tr
                    :class="{ 'select-row': selectedHistory === index }"
                    @click="onSelectHistory(index)"
                  >
                    <td>
                      <base-mo01335 :oneway-model-value="{ value: item.plnDateYmd }" />
                    </td>
                    <td>
                      <base-mo01337 :oneway-model-value="{ value: item.plnShokuNm }" />
                    </td>
                    <td>
                      <base-mo01337
                        :oneway-model-value="{ value: getAssessmentKindName(item.plnType) }"
                      />
                    </td>
                  </tr>
                </template>
              </c-v-data-table>
            </c-v-col>
          </c-v-row>
          <c-v-divider class="my-2" />
          <c-v-row no-gutters>
            <c-v-col cols="10">
              <c-v-data-table
                :headers="headersTableDetailInfoObj"
                hide-default-footer
                :items="valueLocal.detailInfoObj.abstractDetailList ?? []"
                class="table-header table-wrapper list-wrapper"
                fixed-header
                hover
                :items-per-page="-1"
                style="height: 148px"
              >
                <template #item="{ item, index }">
                  <tr
                    :class="{ 'select-row': selectedAbstractDetail === index }"
                    @click="onSelectAbstractDetail(index)"
                  >
                    <td>
                      <base-mo01337
                        :oneway-model-value="{ value: `${item.capId} ${item.capKnj}` }"
                      />
                    </td>
                    <td>
                      <base-mo01337 :oneway-model-value="{ value: item.problemKnj }" />
                    </td>
                  </tr>
                </template>
              </c-v-data-table>
            </c-v-col>
          </c-v-row>
          <c-v-divider class="my-2" />
        </c-v-window-item>
        <c-v-window-item value="considerationTable">
          <c-v-row
            no-gutters
            class="mt-2"
          >
            <c-v-col
              v-if="valueLocal.or10913.planningPeriod === '1'"
              cols="5"
            >
              <c-v-data-table
                :headers="headersTablePlanPeriod"
                hide-default-footer
                :items="valueLocal.planPeriodInfoList"
                class="table-header table-wrapper list-wrapper"
                height="102px"
                fixed-header
                hover
                :items-per-page="-1"
              >
                <template #item="{ item, index }">
                  <tr
                    :class="{ 'select-row': selectedPlanPeriod === index }"
                    @click="onSelectPlanPeriod(index)"
                  >
                    <td>
                      <base-mo01337
                        :oneway-model-value="{ value: `${item.startYmd} ～ ${item.endYmd}` }"
                      />
                    </td>
                    <td>
                      <base-mo01336 :oneway-model-value="{ value: item.sc1Id }" />
                    </td>
                  </tr>
                </template>
              </c-v-data-table>
            </c-v-col>
            <c-v-col cols="7">
              <c-v-data-table
                :headers="headersTableHistoryInfo"
                hide-default-footer
                :items="valueLocal.historyInfoList"
                class="table-header table-wrapper list-wrapper"
                height="102px"
                fixed-header
                hover
                :items-per-page="-1"
              >
                <template #item="{ item, index }">
                  <tr
                    :class="{ 'select-row': selectedHistory === index }"
                    @click="onSelectHistory(index)"
                  >
                    <td>
                      <base-mo01335 :oneway-model-value="{ value: item.plnDateYmd }" />
                    </td>
                    <td>
                      <base-mo01337 :oneway-model-value="{ value: item.plnShokuNm }" />
                    </td>
                    <td>
                      <base-mo01337
                        :oneway-model-value="{ value: getAssessmentKindName(item.plnType) }"
                      />
                    </td>
                  </tr>
                </template>
              </c-v-data-table>
            </c-v-col>
          </c-v-row>
          <c-v-divider class="my-2" />
          <c-v-row no-gutters>
            <c-v-col cols="10">
              <c-v-data-table
                :headers="headersTableDetailInfoObj"
                hide-default-footer
                :items="valueLocal.detailInfoObj.abstractDetailList ?? []"
                class="table-header table-wrapper list-wrapper"
                fixed-header
                hover
                :items-per-page="-1"
                style="height: 148px"
              >
                <template #item="{ item, index }">
                  <tr
                    :class="{ 'select-row': selectedAbstractDetail === index }"
                    @click="onSelectAbstractDetail(index)"
                  >
                    <td>
                      <base-mo01337
                        :oneway-model-value="{ value: `${item.capId} ${item.capKnj}` }"
                      />
                    </td>
                    <td>
                      <base-mo01337 :oneway-model-value="{ value: item.problemKnj }" />
                    </td>
                  </tr>
                </template>
              </c-v-data-table>
            </c-v-col>
          </c-v-row>
          <c-v-divider class="my-2" />
        </c-v-window-item>
      </c-v-window>

      <div class="d-flex justify-start mb-2">
        <base-mo00611
          :oneway-model-value="localOneway.mo00611AboveBtnOneWay"
          class="mr-2"
          @click="onClickAboveBtn()"
        />
        <base-mo00611
          :oneway-model-value="localOneway.mo00611AddBtnOneWay"
          class="mr-2"
          @click="onClickAddBtn()"
        />
      </div>
      <c-v-row>
        <c-v-col cols="8">
          <c-v-data-table
            :headers="[]"
            :items="valueLocal.importFactors"
            :items-per-page="-1"
            hide-default-footer
            hide-default-header
            class="table-header table-wrapper list-wrapper border"
            fixed-header
            hover
          >
            <template #item="{ item, index }">
              <tr
                :class="{ 'select-row': selectedImportFactor === index }"
                style="max-height: 48px !important; height: 48px !important"
                @click="onSelectImportFactor(index)"
              >
                <td
                  class="d-flex align-center"
                  style="
                    max-height: 48px !important;
                    height: 48px !important;
                    border-right: 1px solid rgb(var(--v-theme-black-200)) !important;
                  "
                >
                  <span class="mr-2">
                    {{ item.label }}
                  </span>
                  <div class="d-flex align-center">
                    <c-v-divider
                      vertical
                      :thickness="1"
                      class="ml-1"
                    />
                    <base-mo00009
                      :oneway-model-value="localOneway.mo00009OneWay"
                      @click="onClickEdit(index)"
                    />
                  </div>
                </td>
                <td style="width: 600px">
                  <base-mo00045
                    v-model="valueLocal.importFactors[index].detail"
                    :oneway-model-value="localOneway.mo00045Oneway"
                    class="w-100"
                    style="margin-right: 0 !important"
                  />
                </td>
              </tr>
            </template>
          </c-v-data-table>
        </c-v-col>
        <g-custom-or-26392
          v-bind="or26392"
          v-model="modelValue26392"
          :oneway-model-value="or26392OnewayModel"
        />
      </c-v-row>
    </template>

    <template #cardActionRight>
      <c-v-row no-gutters>
        <c-v-spacer />
        <base-mo00611
          :oneway-model-value="localOneway.mo00611CloseBtnOneWay"
          class="mr-2"
          @click="onCloseDialog()"
        />
        <base-mo00609
          :oneway-model-value="localOneway.mo00609ConfirmOneway"
          class="mx-2"
          @click="onConfirmDialog()"
        />
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showOr21814Dialog"
    v-bind="or21814"
  />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';

.bordered-div {
  border-bottom: 1px solid rgb(var(--v-theme-black-200));
  border-left: 1px solid rgb(var(--v-theme-black-200));
  border-right: 1px solid rgb(var(--v-theme-black-200));
}
.bordered-div--first {
  border-top: 1px solid rgb(var(--v-theme-black-200));
}

.border-right {
  border-right: 1px solid rgb(var(--v-theme-black-200)) !important;
}

.border {
  border: 1px solid rgb(var(--v-theme-black-200));
}
</style>
