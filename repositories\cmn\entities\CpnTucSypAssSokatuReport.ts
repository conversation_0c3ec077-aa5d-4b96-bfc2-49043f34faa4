import type { InWebEntity } from '@/repositories/AbstructWebRepository'

/**
 * 事業者情報エンティティ
 */
export interface JigyoInfoEntity {
  /**
   * 法人ID
   */
  houjinId: string
  /**
   * 施設ID
   */
  shisetuId: string
  /**
   * サービス事業者ID
   */
  svJigyoId: string
  /**
   * サービス事業者コード
   */
  svJigyoCd: string
}

/**
 * 印刷設定エンティティ
 */
export interface PrintSetEntity {
  /**
   * 指定日印刷区分
   */
  shiTeiKubun: string
  /**
   * 指定日
   */
  shiTeiDate: string
}

/**
 * 印刷オプションエンティティ
 */
export interface PrintOptionEntity {
  /**
   * 記入用シートを印刷するフラグ
   */
  emptyFlg: string
  /**
   * 記入用シートアセスメント種別
   */
  kinyuAssType: string
  /**
   * 印刷時に色をつけるフラグ
   */
  colorFlg: string
}

/**
 * 出力帳票印刷情報エンティティ
 */
export interface ChoPrtEntity {
  /**
   * 職員ID
   */
  shokuId: string
  /**
   * システム略称
   */
  sysRyaku: string
  /**
   * セクション
   */
  section: string
  /**
   * プリントナンバー
   */
  prtNo: string
  /**
   * プロファイル
   */
  choPro: string
  /**
   * セクション名
   */
  sectionName: string
  /**
   * オブジェクト名
   */
  dwobject: string
  /**
   * 用紙向き
   */
  prtOrient: string
  /**
   * 用紙サイズ
   */
  prtSize: string
  /**
   * 帳票リスト名
   */
  listTitle: string
  /**
   * 帳票タイトル
   */
  prtTitle: string
  /**
   * 上余白
   */
  mTop: string
  /**
   * 下余白
   */
  mBottom: string
  /**
   * 左余白
   */
  mLeft: string
  /**
   * 右余白
   */
  mRight: string
  /**
   * ルーラ表示有無
   */
  ruler: string
  /**
   * 日付表示有無
   */
  prndate: string
  /**
   * 職員表示有無
   */
  prnshoku: string
  /**
   * シリアルフラグ
   */
  serialFlg: string
  /**
   * モードフラグ
   */
  modFlg: string
  /**
   * セクションフラグ
   */
  secFlg: string
  /**
   * パラメータ01
   */
  param01: string
  /**
   * パラメータ02
   */
  param02: string
  /**
   * パラメータ03
   */
  param03: string
  /**
   * パラメータ04
   */
  param04: string
  /**
   * パラメータ05
   */
  param05: string
  /**
   * パラメータ06
   */
  param06: string
  /**
   * パラメータ07
   */
  param07: string
  /**
   * パラメータ08
   */
  param08: string
  /**
   * パラメータ09
   */
  param09: string
  /**
   * パラメータ10
   */
  param10: string
  /**
   * 高さ
   */
  serialHeight: string
  /**
   * 印刷行数
   */
  serialPagelen: string
  /**
   * 法人or施設or事業所ID
   */
  hsjId: string
  /**
   * パラメータ11
   */
  param11: string
  /**
   * パラメータ12
   */
  param12: string
  /**
   * パラメータ13
   */
  param13: string
  /**
   * パラメータ14
   */
  param14: string
  /**
   * パラメータ15
   */
  param15: string
  /**
   * パラメータ16
   */
  param16: string
  /**
   * パラメータ17
   */
  param17: string
  /**
   * パラメータ18
   */
  param18: string
  /**
   * パラメータ19
   */
  param19: string
  /**
   * パラメータ20
   */
  param20: string
  /**
   * パラメータ21
   */
  param21: string
  /**
   * パラメータ22
   */
  param22: string
  /**
   * パラメータ23
   */
  param23: string
  /**
   * パラメータ24
   */
  param24: string
  /**
   * パラメータ25
   */
  param25: string
  /**
   * パラメータ26
   */
  param26: string
  /**
   * パラメータ27
   */
  param27: string
  /**
   * パラメータ28
   */
  param28: string
  /**
   * パラメータ29
   */
  param29: string
  /**
   * パラメータ30
   */
  param30: string
  /**
   * パラメータ31
   */
  param31: string
  /**
   * パラメータ32
   */
  param32: string
  /**
   * パラメータ33
   */
  param33: string
  /**
   * パラメータ34
   */
  param34: string
  /**
   * パラメータ35
   */
  param35: string
  /**
   * パラメータ36
   */
  param36: string
  /**
   * パラメータ37
   */
  param37: string
  /**
   * パラメータ38
   */
  param38: string
  /**
   * パラメータ39
   */
  param39: string
  /**
   * パラメータ40
   */
  param40: string
  /**
   * パラメータ41
   */
  param41: string
  /**
   * パラメータ42
   */
  param42: string
  /**
   * パラメータ43
   */
  param43: string
  /**
   * パラメータ44
   */
  param44: string
  /**
   * パラメータ45
   */
  param45: string
  /**
   * パラメータ46
   */
  param46: string
  /**
   * パラメータ47
   */
  param47: string
  /**
   * パラメータ48
   */
  param48: string
  /**
   * パラメータ49
   */
  param49: string
  /**
   * パラメータ50
   */
  param50: string
  /**
   * 法人ID
   */
  houjinId: string
  /**
   * 施設ID
   */
  shisetuId: string
  /**
   * サービス事業者ID
   */
  svJigyoId: string
  /**
   * 表示内拡大率
   */
  zoomRate: string
  /**
   * 更新回数
   */
  modifiedCnt: string
}

/**
 * 印刷対象履歴エンティティ
 */
export interface PrintSubjectHistoryEntity {
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 利用者名
   */
  userName: string
  /**
   * 期間ID
   */
  sc1Id: string
  /**
   * 開始日
   */
  startYmd: string
  /**
   * 終了日
   */
  endYmd: string
  /**
   * アセスメントID
   */
  raiId: string
  /**
   * 調査アセスメント種別
   */
  assType: string
  /**
   * 調査日
   */
  assDateYmd: string
  /**
   * 調査者
   */
  assShokuId: string
  /**
   * 結果
   */
  result: string
  /**
   * 出力帳票印刷情報リスト
   */
  choPrtList: ChoPrtEntity[]
}

/**
 * GUI01254_印刷設定帳票出力入力エンティティ
 */
export interface ICpnTucRaiAssReportSelectInEntity extends InWebEntity {
  /**
   * 事業者名
   */
  svJigyoKnj: string
  /**
   * システムコード
   */
  syscd: string
  /**
   * 印刷設定
   */
  printSet: PrintSetEntity
  /**
   * 印刷オプション
   */
  printOption: PrintOptionEntity
  /**
   * 印刷対象履歴リスト
   */
  printSubjectHistoryList: PrintSubjectHistoryEntity[]
}
