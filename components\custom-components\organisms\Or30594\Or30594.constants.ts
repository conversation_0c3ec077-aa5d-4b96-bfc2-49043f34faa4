import { getSequencedCpId } from '~/utils/useScreenUtils'

/**
 * Or30594Const:有機体:印刷設定モーダル
 * GUI01290_［印刷設定］画面
 * 静的データ
 *
 * @description
 * 静的データ
 *
 * <AUTHOR>
 */
export namespace Or30594Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or30594', seq)

  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false

    /**
     * 単一
     */
    export const TANI = '0'
    /**
     * 複数
     */
    export const HUKUSUU = '1'

    /**
     * 一件目選択状態
     */
    export const LEDGER_NUMBER_FIRST = '1'
    /**
     * 二件目選択状態
     */
    export const LEDGER_NUMBER_SECOND = '2'

    /**
     * YES
     */
    export const YES = 'yes'

    /**
     * チェックON
     */
    export const CHECK_ON = '1'

    /**
     * チェックOFF
     */
    export const CHECK_OFF = '0'
  }

  /**
   * セクション名
   */
  export const SECTION_NAME = '主治医意見書'

  /**
   * 正常
   */
  export const STATUS_CODE_SUCCESS = '200'

  /**
   * プロファイル empty
   */
  export const EMPTY = ''
}
