<script setup lang="ts">
/**
 * Or10280:アセスメント（インターライ）CSV出力結果モーダル
 * GUI00789_アセスメント（インターライ）CSV出力結果
 *
 * @description
 * アセスメント（インターライ）CSV出力結果画面
 *
 * <AUTHOR>
 */

import { onMounted, reactive, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or10280Const } from './Or10280.constants'
import type { Or10280StateType } from './Or10280.type'
import { useScreenOneWayBind } from '#build/imports'
import type { Or10280OnewayType } from '~/types/cmn/business/components/Or10280Type'
import type {
  Mo01334Headers,
  Mo01334Items,
  Mo01334OnewayType,
} from '~/types/business/components/Mo01334Type'

/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  onewayModelValue?: Or10280OnewayType
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const { t } = useI18n()

// ローカルTwoway
const local = reactive({
  // 本画面ダイアログ
  mo00024: {
    isOpen: Or10280Const.DEFAULT.IS_OPEN,
  },
})

// ローカルOneway
const localOneway = reactive({
  // 本画面ダイアログOneway
  mo00024Oneway: {
    width: '780',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      name: 'Or10280',
      toolbarTitle: t('label.assessment-interrai-csv-outputed'),
      toolbarTitleCenteredFlg: false,
      toolbarName: 'Or10280ToolBar',
      showCardActions: true,
    },
  },
  // 出力一覧
  mo01334Oneway: {
    headers: [] as Mo01334Headers[],
    items: [] as Mo01334Items[],
    height: '390px',
    rowHeight: '32',
  } as Mo01334OnewayType,
  // 閉じるボタン
  footerClosebtnOneway: {
    btnLabel: t('btn.close'),
    tooltipText: t('tooltip.screen-close'),
  },
})

/**************************************************
 * ライフサイクルフック
 **************************************************/
onMounted(() => {
  // コントロール設定
  initContorls()
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or10280StateType>({
  cpId: Or10280Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      local.mo00024.isOpen = value ?? Or10280Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * ウォッチャー
 **************************************************/
// 画面開閉フラグ
watch(
  () => local.mo00024.isOpen,
  () => {
    setState({ isOpen: local.mo00024.isOpen })
  }
)

watch(
  () => props.onewayModelValue,
  (newValue) => {
    newValue?.assessmentInterRaiCsvOutput.csvOutputList?.forEach((item, index) => {
      localOneway.mo01334Oneway.items.push({
        id: index.toString(),
        officeName: item.officeName,
        fileName: item.fileName,
        result: item.result,
      })
    })
  },
  { deep: true, immediate: true }
)

/**************************************************
 * イベント処理
 **************************************************/
/**
 *  閉じる/Xボタン押下時の処理
 */
function onClick_Close() {
  setState({ isOpen: false })
}

/**************************************************
 * 関数
 **************************************************/
/**
 *  コントロール初期化
 */
const initContorls = () => {
  //---------------header の定義---------------
  // CSV出力一覧
  localOneway.mo01334Oneway.headers = [
    {
      title: t('label.office-name'),
      key: 'officeName',
      sortable: false,
      minWidth: '180px',
    },
    {
      title: t('label.file-name'),
      key: 'fileName',
      sortable: false,
      minWidth: '265px',
    },
    {
      title: t('label.result'),
      key: 'result',
      sortable: false,
      minWidth: '265px',
    },
  ]
}
</script>

<template>
  <!-- ダイアログ -->
  <base-mo00024
    v-model="local.mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet class="view table-header">
        <base-mo-01334
          :oneway-model-value="localOneway.mo01334Oneway"
          class="list-wrapper"
        >
          <!-- 事業所名 -->
          <template #[`item.officeName`]="{ item }">
            <span style="font-size: 14px">{{ item.officeName }}</span>
          </template>
          <!-- ファイル名 -->
          <template #[`item.fileName`]="{ item }">
            <span class="overflowText">{{ item.fileName }}</span>
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :max-width="265"
              :text="item.fileName"
              open-delay="200"
            />
          </template>
          <!-- 結果 -->
          <template #[`item.result`]="{ item }">
            <span class="overflowText">{{ item.result }}</span>
            <c-v-tooltip
              activator="parent"
              location="bottom"
              :max-width="300"
              :text="item.result"
              open-delay="200"
            />
          </template>
          <!-- ページングを非表示 -->
          <template #bottom />
        </base-mo-01334>
      </c-v-sheet>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <div class="buttonArea">
        <!-- 閉じる -->
        <base-mo00611
          :oneway-model-value="localOneway.footerClosebtnOneway"
          @click="onClick_Close"
        ></base-mo00611>
      </div>
    </template>
  </base-mo00024>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table-list.scss';

.view {
  width: auto;
  display: flex;
  flex-direction: column;
}

.buttonArea {
  button:not(:nth-child(1)) {
    margin-left: 8px;
  }
}

.overflowText {
  font-size: 14px;
  text-wrap: auto;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  line-clamp: 1;
  -webkit-line-clamp: 1;
}
</style>
