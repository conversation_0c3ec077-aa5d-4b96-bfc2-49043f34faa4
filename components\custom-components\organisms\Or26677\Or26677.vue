<script setup lang="ts">
import { computed, nextTick, onBeforeUnmount, onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or26678Const } from '../Or26678/Or26678.constants'
import { Or51732Logic } from '../Or51732/Or51732.logic'
import { Or51732Const } from '../Or51732/Or51732.constants'
import { Or26678Logic } from '../Or26678/Or26678.logic'
import { Or26677Const } from './Or26677.constants'
import type { Or26677OneWayType, Or26677Param, Or26677StateType } from './Or26677.type'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo01344OnewayType } from '~/types/business/components/Mo01344Type'
import { hasRegistAuth, useScreenOneWayBind, useScreenStore, useSetupChildProps } from '#imports'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { useCmnCom } from '@/utils/useCmnCom'
import { useCmnRouteCom } from '~/utils/useCmnRouteCom'

/**
 * Or26677:実施モニタリングマスタ
 *
 * @description
 * 実施モニタリングマスタ
 *
 * <AUTHOR>
 */
/************************************************
 * Props
 ************************************************/

interface Props {
  uniqueCpId: string
  onewayModelValue: Or26677OneWayType
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
// route共有情報
const cmnRouteCom = useCmnRouteCom()
const { t } = useI18n()

// 画面ID
const screenId = 'GUI01249'
// ルーティング
const routing = 'GUI01249/pinia'
// ケアマネ画面を初期化する
await useCmnCom().initialize({
  screenId,
  routing,
})

// 共通処理の編集権限チェック
const editFlg = ref<boolean>(true)
// ローカルで保持する共通情報
const commonInfo = {
  cpnFlg: cmnRouteCom.getInitialSettingMaster()?.cpnFlg ?? '',
}

// 有機体:実施モニタリングマスタ
const or26678 = ref({ uniqueCpId: '' })
const or51732 = ref({ uniqueCpId: '' })
//Or21814_有機体:確認ダイアログ
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })

// ダイアログ
const mo00024Oneway = ref<Mo00024OnewayType>({
  width: '800px',
  height: '690',
  persistent: true,
  showCloseBtn: true,
  mo01344Oneway: {
    name: 'Or26677',
    // '実施モニタリングマスタ'
    toolbarTitle: t('label.implementation-monitoring-master'),
    toolbarName: 'Or26677ToolBar',
    toolbarTitleCenteredFlg: false,
    showCardActions: true,
    cardTextClass: 'pa-0 pt-2 pb-2',
  } as Mo01344OnewayType,
})

const mo00024 = ref<Mo00024Type>({
  isOpen: Or26677Const.DEFAULT.IS_OPEN,
})

const local = reactive({
  // タブ
  mo00043: {
    id: '',
  } as Mo00043Type,
})
// 閉じるフラグ
const isClose = ref(false)

const defaultOneway = reactive({
  // タブ
  mo00043OnewayType: {
    tabItems: [],
    minWidth: '58px',
  } as Mo00043OnewayType,
  // 閉じるコンポーネント
  mo00611CloseBtnOneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  // 保存コンポーネント
  mo00609SaveOneway: {
    btnLabel: t('btn.save'),
    disabled: false,
  } as Mo00609OnewayType,
})

const localOneWay = reactive({
  or26677: {
    ...props.onewayModelValue,
  },
  or26678: {
    editFlg: true,
    shisetuId: props.onewayModelValue.shisetuId,
    svJigyoId: props.onewayModelValue.svJigyoId,
    cpnFlg: commonInfo.cpnFlg,
  },
})

// ダイアログ表示フラグ
const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * Pinia
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or26678Const.CP_ID(0)]: or26678.value,
  [Or51732Const.CP_ID(0)]: or51732.value,
})

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  switch (local.mo00043.id) {
    case Or26678Const.DEFAULT.TAB_ID:
      return useScreenStore().isEditByUniqueCpId(or26678.value.uniqueCpId)
  }
  return false
})

const { setState } = useScreenOneWayBind<Or26677StateType>({
  cpId: Or26677Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or26677Const.DEFAULT.IS_OPEN
    },
  },
})
/**************************************************
 * コンポーネント固有処理
 **************************************************/
onMounted(async () => {
  editFlg.value = await hasRegistAuth()
  editFlg.value = true
  localOneWay.or26678.editFlg = editFlg.value
  init()
})

/**************************************************
 * 関数
 **************************************************/
/**
 * 初期化
 */
const init = () => {
  defaultOneway.mo00043OnewayType.tabItems = [
    {
      id: '1',
      // '実施モニタリング'
      title: t('label.implementation-monitoring'),
      tooltipText: t('tooltip.implementation-monitoring'),
      tooltipLocation: 'bottom',
    },
    {
      id: '2',
      // '実施モニタリング記号'
      title: t('label.implementation-monitoring-mark'),
      tooltipText: t('tooltip.implementation-monitoring-mark'),
      tooltipLocation: 'bottom',
    },
  ]
  // 編集権限により、保存ボタンを制御
  defaultOneway.mo00609SaveOneway.disabled = !editFlg.value
  // 共通情報.ケアプラン方式を設定
  localOneWay.or26678.cpnFlg = '1'
}

/**
 * 保存ボタン」押下
 *
 *  @param tabChanged -タブ切り替えフラグ
 */
const save = (tabChanged?: boolean) => {
  // 画面入力データ変更があるかどうかを判定する
  if (!isEdit.value) {
    // メッセージ内容：
    //「変更されている項目がないため、保存を行うことは出来ません。
    // 項目を入力変更してから、再度保存を行ってください。」
    showOr21814MsgOneBtn(t('message.i-cmn-21800'))
    return
  } else {
    switch (local.mo00043.id) {
      // タブ4
      case '1':
        // or26678のダイアログ状態を更新する
        Or26678Logic.state.set({
          uniqueCpId: or26678.value.uniqueCpId,
          state: {
            param: {
              executeFlag: 'save',
              shisetuId: localOneWay.or26677.shisetuId,
              svJigyoId: localOneWay.or26677.svJigyoId,
              isClose: isClose.value,
              tabChanged,
            } as Or26677Param,
          },
        })
        break
      // タブ2
      case '2':
        // Or51732のダイアログ状態を更新する
        Or51732Logic.state.set({
          uniqueCpId: or51732.value.uniqueCpId,
          state: {
            param: {
              executeFlag: 'save',
              kbnFlg: localOneWay.or26677.kbnFlg,
              isClose: isClose.value,
            } as Or26677Param,
          },
        })
        break
      default:
        break
    }
  }
}

/**
 * 画面最新情報を取得する
 */
const getTabsData = () => {
  switch (local.mo00043.id) {
    case '1':
      // Or26678のダイアログ状態を更新する
      Or26678Logic.state.set({
        uniqueCpId: or26678.value.uniqueCpId,
        state: {
          param: {
            executeFlag: 'getData',
            shisetuId: localOneWay.or26677.shisetuId,
            svJigyoId: localOneWay.or26677.svJigyoId,
          } as Or26677Param,
        },
      })
      break
    // タブ2
    case '2':
      // Or51732のダイアログ状態を更新する
      Or51732Logic.state.set({
        uniqueCpId: or51732.value.uniqueCpId,
        state: {
          param: {
            executeFlag: 'getData',
            kbnFlg: localOneWay.or26677.kbnFlg,
          } as Or26677Param,
        },
      })
      break
    default:
      break
  }
}

/**
 * 画面が閉じます
 */
/**
 * ダイアログを閉じる処理
 *
 * @description
 * 自身のOneWayBind領域のフラグを更新する。
 */
const close = async () => {
  // 画面データ変更なし
  if (!isEdit.value) {
    // 画面を閉じる。
    setState({ isOpen: false })
  } else {
    isClose.value = true
    const res = await showDialogOr21814Fun()
    // 編集権限なし
    if (!editFlg.value) {
      if (res === 'yes') {
        setState({ isOpen: false })
      }
      return
    }
    // 編集権限あり
    switch (res) {
      case 'yes':
        // AC003(保存処理)を実行し
        save()
        break
      case 'no':
        // 処理続き
        setState({ isOpen: false })
        break
      case 'cancel':
        // 処理終了
        return
    }
  }
}

/**
 * 共通処理の編集権限チェックを行う。
 */
const showDialogOr21814Fun = async () => {
  // TODO 共通処理の編集権限チェックを行う。
  if (!editFlg.value) {
    // 変更がある場合、確認ダイアログを表示する。(保存権限がない場合)
    // メッセージ内容：情報を保存する権限がありません。入力内容は破棄されますが、処理を続けますか？
    return await showOr21814MsgTwoBtn(t('message.i-cmn-10006'))
  } else {
    // 変更がある場合、確認ダイアログを表示する。(保存権限がある場合)
    // 他の項目を選択すると、変更内容は失われます。「改行」変更を保存しますか？
    return await showOr21814MsgThreeBtn(t('message.i-cmn-10430'))
  }
}

/**
 * 閉じるボタン押下_保存権限がない場合の開閉
 *
 * @param errormsg - Message
 */
function showOr21814MsgTwoBtn(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: errormsg,
      firstBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.no'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = 'cancel'

        if (event?.secondBtnClickFlg) {
          result = 'yes'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })
        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 閉じるボタン押下_保存権限がある場合の開閉
 *
 * @param errormsg - Message
 */
function showOr21814MsgThreeBtn(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト0
      dialogText: errormsg,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result = 'cancel'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }
        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })
        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 保存ボタン押下
 * 変更されている項目がないため、保存を行うことは出来ません。「改行」項目を入力変更してから、再度保存を行ってください。
 *
 * @param errormsg - Message
 */
function showOr21814MsgOneBtn(errormsg: string) {
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト
      dialogText: errormsg,
      firstBtnType: 'blank',
      thirdBtnType: 'blank',
      secondBtnType: 'normal1',
      secondBtnLabel: t('btn.yes'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * タコンテンツエリアタブ切替
 *
 * @param param - タブ選択ID
 */
const updateModelValue = async (param: Mo00043Type) => {
  // 画面データは変更がある
  if (isEdit.value) {
    const res = await showDialogOr21814Fun()
    // 権限なし
    if (!editFlg.value) {
      if (res === 'yes') {
        local.mo00043.id = param.id
        // pinia値設定を待つ
        await nextTick()
      }
      return
    }
    switch (res) {
      case 'yes':
        // AC003(保存処理)を実行し
        save(true)
        local.mo00043.id = param.id
        break
      case 'no':
        // 処理続き
        local.mo00043.id = param.id
        // pinia値設定を待つ
        await nextTick()
        break
      case 'cancel':
        // 処理終了
        return
    }
  } else {
    local.mo00043.id = param.id
    getTabsData()
  }
}
/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * @description
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    mo00024.value.isOpen = true
    // 組織dialog自動クローズを手動判定に変更
    if (!newValue) {
      void close()
    }
  }
)

watch(
  () => local.mo00043.id,
  (newVal) => {
    mo00024Oneway.value.mo01344Oneway!.toolbarTitle =
      defaultOneway.mo00043OnewayType.tabItems.find((item) => item.id === newVal)?.title +
      t('label.master')
    // タブIdは変更がある場合、初期化を実行
    getTabsData()
  }
)

/**
 * 権限変更
 *
 * @description
 * 権限変更
 */
watch(
  () => editFlg.value,
  (newVal) => {
    defaultOneway.mo00609SaveOneway.disabled = !newVal
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="mo00024Oneway"
  >
    <template #cardItem>
      <base-mo00043
        :model-value="local.mo00043"
        :oneway-model-value="defaultOneway.mo00043OnewayType"
        @update:model-value="updateModelValue"
      >
      </base-mo00043>
      <c-v-window v-model="local.mo00043.id">
        <c-v-window-item value="1">
          <!-- 実施モニタリングマスタ -->
          <g-custom-or26678
            v-bind="or26678"
            :parent-unique-cp-id="props.uniqueCpId"
            :oneway-model-value="localOneWay.or26678"
          />
        </c-v-window-item>
        <c-v-window-item value="2">
          <g-custom-or51732
            ref="or51732Ref"
            v-bind="or51732"
          />
        </c-v-window-item>
      </c-v-window>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row no-gutters>
        <c-v-spacer />
        <!-- 閉じるボタン Mo00611 -->
        <base-mo00611
          v-bind="defaultOneway.mo00611CloseBtnOneWay"
          @click="close()"
        >
          <!--ツールチップ表示："画面を閉じます"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.screen-close')"
            open-delay="200"
          />
        </base-mo00611>
        <!-- 保存ボタン Mo00611 -->
        <base-mo00609
          v-bind="defaultOneway.mo00609SaveOneway"
          class="ml-2 pr-0"
          @click="save()"
        >
          <!--ツールチップ表示："保存します"-->
          <c-v-tooltip
            activator="parent"
            location="bottom"
            :max-width="600"
            :text="t('tooltip.save')"
            open-delay="200"
          />
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814
    v-if="showDialogOr21814"
    v-bind="or21814"
  />
</template>
