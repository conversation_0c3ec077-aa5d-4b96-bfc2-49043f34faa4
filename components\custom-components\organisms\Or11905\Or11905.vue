<script setup lang="ts">
import { onMounted, ref, watch, onUnmounted } from 'vue'
import type { Or11905Type } from './Or11905.type'
import type { Or11905OnewayType } from '~/types/cmn/business/components/Or11905Type'

/**
 * Or11905:有機体：セレクト
 *
 * @description
 * セレクトボックスコンポーネント。
 * データがプルダウンの選択肢に存在しない場合、その値を直接表示します。
 * 一度選択すると、その値は戻せません。
 *
 * <AUTHOR>
 */

/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId?: string
  modelValue: Or11905Type
  onewayModelValue: Or11905OnewayType
}

const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
/**
 * 選択したデータ
 */
const or11905Data = ref<Or11905Type>({ modelValue: '' })

/**
 * 選択肢表示フラグ
 */
const showSelectItemsFlg = ref(false)

/**
 * 選択したラベル
 */
const selectItemLabel = ref('')

/**
 * 選択肢リスト
 */
const optionsList = ref<Or11905OnewayType['optionsList']>([])

/**
 * Ref
 */
const or11905Ref = ref<HTMLElement | null>(null)

/**************************************************
 * 変数定義
 **************************************************/
/**
 * 選択したデータ変更
 *
 * @param selectItem - 選択したデータ
 */
const selectItemsClick = (selectItem: { value: string; title: string }) => {
  or11905Data.value.modelValue = selectItem.title
  selectItemLabel.value = selectItem.title
  setTimeout(() => {
    showSelectItemsFlg.value = false
  }, 200)
}

/**
 * 選択肢表示フラグ変更
 */
const changeSelectItemsFlg = () => {
  showSelectItemsFlg.value = !showSelectItemsFlg.value
}

/**
 * イベント監視
 *
 * @param event - イベント対象
 */
const handleClickOutside = (event: MouseEvent) => {
  if (or11905Ref.value && !or11905Ref.value.contains(event.target as Node)) {
    showSelectItemsFlg.value = false
  }
}

/**
 * ランダムID作成
 */
const getRandomInputId = () => {
  return `select_field_${new Date().getTime()}`
}
/**
 * 初期化処理
 */
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

/**
 * 画面が離れ前にイベントリスナーをアンインストールする
 */
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
/**************************************************
 * データ変更監視
 **************************************************/
watch(
  () => props.modelValue,
  (newValue) => {
    if (!newValue) return
    or11905Data.value = newValue
    selectItemLabel.value = newValue.modelValue
    // selectItemLabel.value =
    //   props.onewayModelValue.optionsList.find((item) => item.value === newValue.modelValue)
    //     ?.title ?? newValue.modelValue
  },
  { deep: true, immediate: true }
)

watch(
  () => props.onewayModelValue,
  (newValue) => {
    if (!newValue) return
    optionsList.value = newValue.optionsList
  },
  { deep: true }
)
</script>

<template>
  <div
    ref="or11905Ref"
    v-bind="{ ...$attrs }"
    class="w-100 h-100 position-relative"
  >
    <div
      :class="[
        'position-absolute select-box d-flex align-center w-100 h-100 pa-2',
        { selected: showSelectItemsFlg },
      ]"
      @click="changeSelectItemsFlg"
    >
      <div
        class="flex-1-1 d-flex justify-start"
        style="overflow: hidden; white-space: nowrap"
      >
        {{ selectItemLabel }}
      </div>
      <base-at-icon
        :icon="'chevron_left'"
        :class="['select-icon', { 'selected-icon': showSelectItemsFlg }]"
      />
    </div>
    <c-v-list
      v-if="showSelectItemsFlg"
      class="select-options w-100 position-absolute"
    >
      <!-- セレクト選択肢 -->
      <c-v-list-item
        v-for="(item, index) in optionsList"
        :key="index"
        :class="['text-left', { 'selected-items': item.title === selectItemLabel }]"
        @click="selectItemsClick(item)"
      >
        {{ item.title }}
      </c-v-list-item>
    </c-v-list>
    <!-- データ変換用インプット -->
    <input
      :id="getRandomInputId()"
      v-model="or11905Data.modelValue"
      type="text"
      class="opcaity-input position-absolute"
      autocomplete="false"
      @focus="
        () => {
          showSelectItemsFlg = true
        }
      "
      @blur="
        () => {
          showSelectItemsFlg = false
        }
      "
    />
  </div>
</template>

<style scoped lang="scss">
.opcaity-input {
  width: 1px;
  height: 1px;
  caret-color: transparent;
  user-select: none;
  opacity: 0;
  left: 0;
  top: 0;
  z-index: -1;
  cursor: pointer;
}
.select-box {
  left: 0;
  top: 0;
  z-index: 10;
  border-radius: 5px;
  transition: all 0.1s;
  cursor: pointer;
  user-select: none;
}
.select-icon {
  opacity: var(--v-medium-emphasis-opacity);
  transform: rotate(-90deg);
  transition: all 0.1s;
}
.select-options {
  top: 105%;
  z-index: 15;
  border-radius: 5px;
  box-shadow: 0px 2px 7px -2px rgba(0, 0, 0, 0.6);
  user-select: none;
}
.selected-items {
  background-color: rgba(0, 0, 0, 0.1);
}
.selected {
  box-shadow: 0 0px 3px 2px rgb(7, 96, 230);
}
.selected-icon {
  transform: rotate(90deg);
  color: rgb(7, 96, 230);
}
</style>
