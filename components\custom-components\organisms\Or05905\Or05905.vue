<script setup lang="ts">
/**
 * Or05905: (選定表(アセスメント(インターライ)))選定表
 * GUI00848: 選定表(アセスメント(インターライ))
 *
 * @description
 * (選定表(アセスメント(インターライ)))選定表
 *
 * <AUTHOR> DAM XUAN HIEU
 */
import { reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or05905Const } from './Or05905.constants'
import type { cpnTucRaiList, Or05905StateType } from './Or05905.type'
import { useScreenOneWayBind, useSetupChildProps } from '#imports'
import InterRAI from '@/assets/cmn/image/inter_RAI.png'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type {
  SelectionTableAssessmentInterraiInEntity,
  SelectionTableAssessmentInterraiOuyEntity,
} from '~/repositories/cmn/entities/SelectionTableAssessmentInterraiSelectEntity'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00024OnewayType, Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Mo00039OnewayType } from '~/types/cmn/business/components/Mo00039Type'
import type {
  HistoryInfo,
  PlanPeriodInfo,
  TransmitParam,
} from '~/types/cmn/business/components/TeX0003Type'
import type { OrCpGroupDefinitionInputFormDeleteDialogOnewayType } from '~/types/cmn/business/generator-components/OrCpGroupDefinitionInputFormDeleteDialog'
import { CmnMCdKbnId } from '~/constants/system-code-constants'

/**************************************************
 * Props
 **************************************************/
interface Props {
  onewayModelValue: {
    surveyAssessmentKind: string
  }
  uniqueCpId: string
}

// 引継情報を取得する
const props: Props = defineProps<Props>()

// i18nの翻訳関数
const { t } = useI18n()

const cpnTucRaiListData: cpnTucRaiList = reactive({
  raiId: 0,
  scaleBmi: 0,
  scaleDrs: 0,
  scalePs: 0,
  scaleCps: 0,
  scaleCpsNm: '',
  scaleAdlh: 0,
  scaleAdlhNm: '',
  cap1: 0,
  cap1Nm: '',
  cap2: 0,
  cap2Nm: '',
  cap3: 0,
  cap3Nm: '',
  cap4: 0,
  cap4Nm: '',
  cap5: 0,
  cap5Nm: '',
  cap6: 0,
  cap6Nm: '',
  cap7: 0,
  cap7Nm: '',
  cap8: 0,
  cap8Nm: '',
  cap9: 0,
  cap9Nm: '',
  cap10: 0,
  cap10Nm: '',
  cap11: 0,
  cap11Nm: '',
  cap12: 0,
  cap12Nm: '',
  cap13: 0,
  cap13Nm: '',
  cap14: 0,
  cap14Nm: '',
  cap15: 0,
  cap15Nm: '',
  cap16: 0,
  cap16Nm: '',
  cap17: 0,
  cap17Nm: '',
  cap18: 0,
  cap18Nm: '',
  cap19: 0,
  cap19Nm: '',
  cap20: 0,
  cap20Nm: '',
  cap21: 0,
  cap21Nm: '',
  cap22: 0,
  cap22Nm: '',
  cap23: 0,
  cap23Nm: '',
  cap24: 0,
  cap24Nm: '',
  cap25: 0,
  cap25Nm: '',
  cap26: 0,
  cap26Nm: '',
  cap27: 0,
  cap27Nm: '',
})

// フッターの注釈データを定義
const footer = reactive({
  annotation: {
    value: t('label.footer-annotation'),
    customClass: new CustomClass({
      outerClass: 'm-0 pa-2',
      labelClass: '',
      itemClass: '',
    }),
  } as Mo01338OnewayType,
})

const defaultModelValue = {
  assessmentKindList: [] as CodeType[],
}

// 共通情報を格納するオブジェクト
const commonInfo: TransmitParam = {
  deleteBtnValue: '',
  executeFlag: '',
  historyInfo: {} as HistoryInfo,
  historyModifiedCnt: '',
  houjinId: '',
  kijunbiYmd: '',
  kikanKanriFlg: '',
  planPeriodInfo: {} as PlanPeriodInfo,
  sakuseiId: '',
  shisetuId: '',
  svJigyoId: '',
  userId: '',
}

const updateKbn = ref('')
const historyUpdateKbn = ref('')
const defaultOneway = reactive({
  mo01338SurveyAssessmentTypeOneway: {
    value: '',
    customClass: {
      outerClass: 'mr-4',
      outerStyle: 'background: transparent',
      labelClass: '',
      labelStyle: '',
      itemClass: 'contentTitle',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  // General
  mo00039Oneway: {
    name: '',
    itemLabel: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
})
const local = reactive({
  officeId: '',
  planPeriodId: '',
  historyId: '',
  // General
  msgDialog10180: {
    emitType: '',
    mo00024: {
      isOpen: false,
    } as Mo00024Type,
  },
  msgDialogOneway41717: {
    msg: t('message.e-cmn-41717'),
    btnNoDisplay: false,
    mo00024Oneway: {
      class: 'mr-1',
      name: '',
      width: '360px',
    } as Mo00024OnewayType,
  } as OrCpGroupDefinitionInputFormDeleteDialogOnewayType,
})

// useScreenOneWayBind関数を使用して、画面の状態をバインドし、更新処理を実行する設定
useScreenOneWayBind<Or05905StateType>({
  cpId: Or05905Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    param: (value) => {
      if (value) {
        Object.assign(commonInfo, value)
      }
      void (async () => {
        switch (value?.executeFlag) {
          case 'save':
            void onUpdateData()
            break
          case 'reload':
            void onReloadData()
            break
          case 'add':
            updateKbn.value = Or05905Const.DEFAULT.UPDATE_KBN_C
            historyUpdateKbn.value = Or05905Const.DEFAULT.UPDATE_KBN_C
            onAdd()
            break
          case 'copy':
            //TODO
            updateKbn.value = Or05905Const.DEFAULT.UPDATE_KBN_C
            historyUpdateKbn.value = Or05905Const.DEFAULT.UPDATE_KBN_C
            onUpdateData()
            break
          case 'delete':
            //TODO
            updateKbn.value = Or05905Const.DEFAULT.UPDATE_KBN_D
            historyUpdateKbn.value =
              commonInfo.deleteBtnValue === '2' ? Or05905Const.DEFAULT.UPDATE_KBN_D : ''
            onUpdateData()
            break
          case 'getData':
            await onGetData()
            break
          default:
            break
        }
      })()
    },
  },
})

// 子コンポーネントのプロパティを設定するためのuseSetupChildProps関数の呼び出し
useSetupChildProps(props.uniqueCpId, {})

// msgDialog10180のemitTypeを監視し、選択された値に応じて処理を実行する処理
watch(
  () => local.msgDialog10180.emitType,
  (newValue) => {
    if (!newValue) return
    // はい
  }
)

const onUpdateData = () => {
  // console.log('onUpdateData', cpnTucRaiListData)
}

function onReloadData() {
  // console.log('onReloadData', cpnTucRaiListData)
}

// 画面項目にデフォルトのサブ情報を設定する関数
const onAdd = () => {}

// アセスメント種別コードを初期化する非同期関数
async function initCodes() {
  const cmnMCdKbnId = CmnMCdKbnId as { M_CD_KBN_ID_ASSESSMENT_KIND: number }
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 回数区分
    { mCdKbnId: cmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // アセスメント種別
  defaultModelValue.assessmentKindList = CmnSystemCodeRepository.filter(
    cmnMCdKbnId.M_CD_KBN_ID_ASSESSMENT_KIND
  )
  for (const item of defaultModelValue.assessmentKindList) {
    if (props.onewayModelValue?.surveyAssessmentKind === item.value) {
      defaultOneway.mo01338SurveyAssessmentTypeOneway.value = item.label
      break
    }
  }
}

// 画面初期化
const onGetData = async () => {
  await initCodes()
  const inputMockData: SelectionTableAssessmentInterraiInEntity = {
    raiId: commonInfo.historyInfo.raiId,
    position: 123,
  }
  const resMockData: SelectionTableAssessmentInterraiOuyEntity = await ScreenRepository.select(
    'selectionTableAssessmentInterrai',
    inputMockData
  )
  Object.assign(cpnTucRaiListData, resMockData.data.cpnTucRaiList)
}
</script>

<template>
  <c-v-col style="overflow-x: hidden">
    <c-v-row
      no-gutters
      class="mt-4"
    >
      <c-v-col
        cols="9"
        class="mx-auto rounded-lg pa-2"
        style="background-color: white; height: 600px; overflow-y: auto; overflow-x: hidden"
      >
        <g-custom-or05906 :cpn-tuc-rai-list-data="cpnTucRaiListData" />
        <g-custom-or05907
          class="mt-4"
          :cpn-tuc-rai-list-data="cpnTucRaiListData"
        />
        <c-v-row class="row-footer">
          <c-v-col
            cols="12"
            class="mx-auto rounded-lg px-2 py-0 footer"
          >
            <base-mo01338
              :oneway-model-value="footer.annotation"
              class="annotation"
            />
            <c-v-img
              width="100"
              aspect-ratio="16/9"
              cover
              :src="InterRAI"
              style="float: right"
              class="px-2"
            />
          </c-v-col>
        </c-v-row>
      </c-v-col>
    </c-v-row>
  </c-v-col>
</template>
<style scoped lang="scss">
.row-footer {
  padding-bottom: 16px;
  background-color: rgb(var(--v-theme-background)) !important;

  .annotation {
    background-color: transparent !important;
    :deep(.item-label) {
      font-size: 12px !important;
      font-weight: bold !important;
    }
  }
}
</style>
