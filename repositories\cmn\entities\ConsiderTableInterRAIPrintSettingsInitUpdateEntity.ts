import type { InWebEntity, OutWebEntity } from '@/repositories/AbstructWebRepository'

/**
 * 帳票名称エンティティ
 */
export interface PrtEntity {
  /**
   * インデックス
   */
  index: string
  /**
   * 帳票名
   */
  defPrtTitle: string
  /**
   * 帳票タイトル
   */
  prtTitle: string
  /**
   * セクション番号
   */
  sectionNo: string
  /**
   * 帳票番号
   */
  prtNo: string
  /**
   * プロファイル
   */
  profile: string
  /**
   * 日付表示有無
   */
  prnDate: string
  /**
   * 職員表示有無
   */
  prnshoku: string
  /**
   * オブジェクト名
   */
  dwobject: string
  /**
   * 用紙向き
   */
  prtOrient: string
  /**
   * 用紙サイズ
   */
  prtSize: string
  /**
   * 帳票リスト名
   */
  listTitle: string
  /**
   * 上余白
   */
  mtop: string
  /**
   * 下余白
   */
  mbottom: string
  /**
   * 左余白
   */
  mleft: string
  /**
   * 右余白
   */
  mright: string
  /**
   * ルーラ表示有無
   */
  ruler: string
  /**
   * シリアルフラグ
   */
  serialFlg: string
  /**
   * モードフラグ
   */
  modFlg: string
  /**
   * セクションフラグ
   */
  secFlg: string
  /**
   * 高さ
   */
  serialHeight: string
  /**
   * 印刷行数
   */
  serialPagelen: string
  /**
   * 表示内拡大率
   */
  zoomRate: string
  /**
   * パラメータ01
   */
  param01: string
  /**
   * パラメータ02
   */
  param02: string
  /**
   * パラメータ03
   */
  param03: string
  /**
   * パラメータ04
   */
  param04: string
  /**
   * パラメータ05
   */
  param05: string
  /**
   * パラメータ06
   */
  param06: string
  /**
   * パラメータ07
   */
  param07: string
  /**
   * パラメータ08
   */
  param08: string
  /**
   * パラメータ09
   */
  param09: string
  /**
   * パラメータ10
   */
  param10: string
  /**
   * パラメータ11
   */
  param11: string
  /**
   * パラメータ12
   */
  param12: string
  /**
   * パラメータ13
   */
  param13: string
  /**
   * パラメータ14
   */
  param14: string
  /**
   * パラメータ15
   */
  param15: string
  /**
   * パラメータ16
   */
  param16: string
  /**
   * パラメータ17
   */
  param17: string
  /**
   * パラメータ18
   */
  param18: string
  /**
   * パラメータ19
   */
  param19: string
  /**
   * パラメータ20
   */
  param20: string
  /**
   * パラメータ21
   */
  param21: string
  /**
   * パラメータ22
   */
  param22: string
  /**
   * パラメータ23
   */
  param23: string
  /**
   * パラメータ24
   */
  param24: string
  /**
   * パラメータ25
   */
  param25: string
  /**
   * パラメータ26
   */
  param26: string
  /**
   * パラメータ27
   */
  param27: string
  /**
   * パラメータ28
   */
  param28: string
  /**
   * パラメータ29
   */
  param29: string
  /**
   * パラメータ30
   */
  param30: string
  /**
   * パラメータ31
   */
  param31: string
  /**
   * パラメータ32
   */
  param32: string
  /**
   * パラメータ33
   */
  param33: string
  /**
   * パラメータ34
   */
  param34: string
  /**
   * パラメータ35
   */
  param35: string
  /**
   * パラメータ36
   */
  param36: string
  /**
   * パラメータ37
   */
  param37: string
  /**
   * パラメータ38
   */
  param38: string
  /**
   * パラメータ39
   */
  param39: string
  /**
   * パラメータ40
   */
  param40: string
  /**
   * パラメータ41
   */
  param41: string
  /**
   * パラメータ42
   */
  param42: string
  /**
   * パラメータ43
   */
  param43: string
  /**
   * パラメータ44
   */
  param44: string
  /**
   * パラメータ45
   */
  param45: string
  /**
   * パラメータ46
   */
  param46: string
  /**
   * パラメータ47
   */
  param47: string
  /**
   * パラメータ48
   */
  param48: string
  /**
   * パラメータ49
   */
  param49: string
  /**
   * パラメータ50
   */
  param50: string
  /**
   * 更新回数
   */
  modifiedCnt: string
}

/**
 * 印刷設定情報エンティティ
 */
export interface SysIniInfoEntity {
  /**
   * 氏名伏字設定フラグ
   */
  amikakeFlg: string
  /**
   * 氏名伏字更新回数
   */
  amikakeModifiedCnt: string
  /**
   * 文章管理設定フラグ
   */
  iso9001Flg: string
  /**
   * 文章管理更新回数
   */
  iso9001ModifiedCnt: string
  /**
   * 個人情報設定フラグ
   */
  kojinhogoFlg: string
  /**
   * 個人情報更新回数
   */
  kojinhogoModifiedCnt: string
}

/**
 * 期間履歴情報エンティティ
 */
export interface considerTableInterRAIHistoryEntity {
  /**
   * 期間ID
   */
  sc1Id: string
  /**
   * 開始日
   */
  startYmd: string
  /**
   * 終了日
   */
  endYmd: string
  /**
   * 選択
   */
  sel: string
  /**
   * 利用者ID
   */
  // userId: string
  /**
   * アセスメントID
   */
  raiId: string
  /**
   * 検討アセスメント種別(1：居宅版、2：施設版、3：高齢者住宅版)
   */
  plnType: string
  /**
   * 検討日
   */
  plnDateYmd: string
  /**
   * 検討者
   */
  plnShokuId: string
  /**
   * 作成者
   */
  authorKnj: string
}
/**
 * 期間履歴情報エンティティ
 */
export interface ConsiderTableInterRAIHistoryEntity {
  /**
   * 期間ID
   */
  sc1Id: string
  /**
   * 開始日
   */
  startYmd: string
  /**
   * 終了日
   */
  endYmd: string
  /**
   * 選択
   */
  sel: string
  /**
   * 利用者ID
   */
  // userId: string
  /**
   * アセスメントID
   */
  raiId: string
  /**
   * 検討アセスメント種別(1：居宅版、2：施設版、3：高齢者住宅版)
   */
  plnType: string
  /**
   * 調査日
   */
  plnDateYmd: string
  /**
   * 調査者
   */
  plnShokuId: string
  /**
   * 担当者名
   */
  authorKnj: string
}

/**
 * 利用者エンティティ
 */
export interface UserEntity {
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 利用者名
   */
  userName: string
}

/**
 * 印刷対象履歴エンティティ
 */
export interface PrtHistoryEntity {
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 利用者名
   */
  userName: string
  /**
   * 期間ID
   */
  sc1Id: string
  /**
   * 開始日
   */
  startYmd: string
  /**
   * 終了日
   */
  endYmd: string
  /**
   * アセスメントID
   */
  raiId: string
  /**
   * 調査アセスメント種別
   */
  plnType: string
  /**
   * 調査日
   */
  plnDateYmd: string
  /**
   * 調査者
   */
  plnShokuId: string
  /**
   * 結果
   */
  result: string
}

/**
 * 印刷設定初期情報取得入力エンティティ
 */
export interface ConsiderTableInterRAIPrintSettingsInitSelectInEntity extends InWebEntity {
  /**
   * システムコード
   */
  sysCd: string
  /**
   * システム略称
   */
  sysRyaku: string
  /**
   * 法人ID
   */
  houjinId: string
  /**
   * 施設ID
   */
  shisetuId: string
  /**
   * 事業者ID
   */
  svJigyoId: string
  /**
   * 職員ID
   */
  shokuId: string
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 担当者ID
   */
  tantoId: string
  /**
   * セクション名
   */
  sectionName: string
  /**
   * インデックス(ディフォルト値：0)
   */
  index: string
  /**
   * 種別ID
   */
  syubetsuId: string
  /**
   * 個人情報使用フラグ(0：不使用、1：使用)
   */
  kojinhogoUsedFlg: string
  /**
   * 個人情報番号(0：主に日誌以外、1：主に日誌系)
   */
  sectionAddNo: string
}

/**
 * 印刷設定初期情報取得出力エンティティ
 */
export interface ConsiderTableInterRAIPrintSettingsInitSelectOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /**
     * 印刷設定情報リスト
     */
    prtList: PrtEntity[]
    /**
     * システムINI情報
     */
    sysIniInfo: SysIniInfoEntity
    /**
     * 担当ケアマネ
     */
    tantoKnj: string
    /**
     * 期間管理フラグ
     */
    kikanFlg: string
    /**
     * アセスメント履歴リスト
     */
    considerTableInterRAIHistoryList: ConsiderTableInterRAIHistoryEntity[]
  }
}

/**
 * 印刷設定対象一覧情報取得入力エンティティ
 */
export interface IAssessmentInterRAIPrintSettingsSubjectSelectInEntity extends InWebEntity {
  /**
   * 帳票番号
   */
  prtNo: string
  /**
   * 事業者ID
   */
  svJigyoId: string
  /**
   * 基準日
   */
  kijunbi: string
  /**
   * 利用者リスト
   */
  userList: UserEntity[]
}

/**
 * 印刷設定対象一覧情報取得出力エンティティ
 */
export interface IAssessmentInterRAIPrintSettingsSubjectSelectOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /**
     * 印刷対象履歴リスト
     */
    prtHistoryList: PrtHistoryEntity[]
  }
}

/**
 * 印刷設定履歴リスト取得入力エンティティ
 */
export interface ConsiderTableInterRAIPrintSettingsHistorySelectInEntity extends InWebEntity {
  /**
   * 事業者ID
   */
  svJigyoId: string
  /**
   * 利用者ID
   */
  userId: string
  /**
   * 期間管理フラグ(管理しない／管理する)
   */
  kikanFlg: string
}

/**
 * 印刷設定履歴リスト取得出力エンティティ
 */
export interface ConsiderTableInterRAIPrintSettingsHistorySelectOutEntity extends OutWebEntity {
  /**
   * データ
   */
  data: {
    /**
     * アセスメント履歴リスト
     */
    considerTableInterRAIHistoryList: considerTableInterRAIHistoryEntity[]
  }
}
