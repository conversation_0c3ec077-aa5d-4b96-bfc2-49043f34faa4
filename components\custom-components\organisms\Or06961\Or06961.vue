<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { ref, type Ref, watch, reactive, computed, nextTick } from 'vue'
import { Or10883Logic } from '../Or10883/Or10883.logic'
import { Or10883Const } from '../Or10883/Or10883.constants'
import { Or06961Const } from './Or06961.constants'
import type { TableData, Or06961Type } from './Or06961.type'
import type {
  Or06961OnewayType,
  Or06961TwowayType,
} from '~/types/cmn/business/components/Or06961Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import { SPACE_FORWARD_SLASH } from '~/constants/classification-constants'
import { useSetupChildProps, useSystemCommonsStore, useScreenTwoWayBind } from '#imports'
import { Or21816Const } from '~/components/base-components/organisms/Or21816/Or21816.constants'
import { Or21816Logic } from '~/components/base-components/organisms/Or21816/Or21816.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or27347Const } from '~/components/custom-components/organisms/Or27347/Or27347.constants'
import type { Or27347OnewayType, Or27347Type } from '~/types/cmn/business/components/Or27347Type'
import { Or27347Logic } from '~/components/custom-components/organisms/Or27347/Or27347.logic'
import type { ITab3Data } from '~/repositories/cmn/entities/PreventionPlanSelectEntity'
import { useScreenEventStatus } from '~/composables/useComponentVue'
import type {
  Or10883OnewayType,
  Or10883TwowayType,
} from '~/types/cmn/business/components/Or10883Type'
import type { Mo01280Type } from '@/types/business/components/Mo01280Type'

/**
 * Or06961Logic:有機体:総合的課題と目標表一覧
 * GUI01094_総合的課題と目標（予防計画書）
 *
 * @description
 * 総合的課題と目標コンテンツエリアタブ
 *
 * <AUTHOR>
 */
const { t } = useI18n()
// システム共有情報ストア
const systemCommonsStore = useSystemCommonsStore()
/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  onewayModelValue: Or06961OnewayType
}
const props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/
const headers = [
  {
    title: t('label.issue-number'),
    key: Or06961Const.TABEL_COLUMN_NAME_KADAINO,
    width: '80px',
    sortable: false,
    required: true,
    mo00009Oneway: {
      // デフォルト値の設定
      btnIcon: 'edit_square',
      density: 'compact',
      disabled: false,
    },
    click: () => {
      if (props.onewayModelValue.operaFlg === Or06961Const.ACTION_KBN_DELETE) {
        return
      }
      local.Or27347.issuesList = []
      localOneway.or27347Oneway.processType = (
        props.onewayModelValue.itakuKkakPrtFlg === Or06961Const.ITAKU_KKAK_PRT_FLG_0
      ).toString()
      localOneway.or27347Oneway.issuesList = tab3DataList.value.map((item) => {
        return {
          issuesNum: Number(item.kadaiNo.modelValue.value),
          comprehensiveIs: item.sogoKadaiKnj.modelValue.value,
          issGoalProposal: item.sogoTeianKnj.modelValue.value,
          specificIntention: item.sogoIkouKnj.modelValue.value,
          goal: item.sogoMokuhyoKnj.modelValue.value,
        }
      })
      // Or27347のダイアログ開閉状態を更新する
      Or27347Logic.state.set({
        uniqueCpId: or27347.value.uniqueCpId,
        state: { isOpen: true },
      })
    },
  },
  {
    title: t('label.comprehensive-issues'),
    key: Or06961Const.TABEL_COLUMN_NAME_KADAINO,
    width: '150px',
    sortable: false,
    required: false,
    mo00009Oneway: {
      // デフォルト値の設定
      btnIcon: 'edit_square',
      density: 'compact',
      disabled: false,
    },
    click: () => {
      if (
        props.onewayModelValue.operaFlg === Or06961Const.ACTION_KBN_DELETE ||
        selectRowIndex.value < 0
      ) {
        return
      }
      localOneway.or10883Oneway.t2Cd = Or06961Const.T2CD_6
      localOneway.or10883Oneway.columnName = Or06961Const.TABEL_COLUMN_NAME_SOGOKADAIKNJ
      localOneway.or10883Oneway.title = Or06961Const.TABEL_COLUMN_LABEL_SOGOKADAIKNJ
      localOneway.or10883Oneway.inputContents =
        tab3DataList.value[selectRowIndex.value].sogoKadaiKnj.modelValue.value
      local.Or10883.naiyo = ''
      // GUI01109 入力支援【目標とする生活（１年）】画面をポップアップで起動する
      Or10883Logic.state.set({
        uniqueCpId: or10883.value.uniqueCpId,
        state: { isOpen: true },
      })
    },
  },
  {
    title: t('label.issues-resolution-plan'),
    key: Or06961Const.TABEL_COLUMN_NAME_SOGOTEIANKNJ,
    width: '200px',
    sortable: false,
    required: false,
    mo00009Oneway: {
      // デフォルト値の設定
      btnIcon: 'edit_square',
      density: 'compact',
      disabled: false,
    },
    click: () => {
      if (
        props.onewayModelValue.operaFlg === Or06961Const.ACTION_KBN_DELETE ||
        selectRowIndex.value < 0
      ) {
        return
      }
      localOneway.or10883Oneway.t2Cd = Or06961Const.T2CD_7
      localOneway.or10883Oneway.columnName = Or06961Const.TABEL_COLUMN_NAME_SOGOTEIANKNJ
      localOneway.or10883Oneway.title = Or06961Const.TABEL_COLUMN_LABEL_SOGOTEIANKNJ
      localOneway.or10883Oneway.inputContents =
        tab3DataList.value[selectRowIndex.value].sogoTeianKnj.modelValue.value
      local.Or10883.naiyo = ''
      // GUI01109 入力支援【目標とする生活（１年）】画面をポップアップで起動する
      Or10883Logic.state.set({
        uniqueCpId: or10883.value.uniqueCpId,
        state: { isOpen: true },
      })
    },
  },
  {
    title: t('label.user-family-feedback-on-measures'),
    key: Or06961Const.TABEL_COLUMN_NAME_SOGOIKOUKNJ,
    width: '200px',
    sortable: false,
    required: false,
    mo00009Oneway: {
      // デフォルト値の設定
      btnIcon: 'edit_square',
      density: 'compact',
      disabled: false,
    },
    click: () => {
      if (
        props.onewayModelValue.operaFlg === Or06961Const.ACTION_KBN_DELETE ||
        selectRowIndex.value < 0
      ) {
        return
      }
      localOneway.or10883Oneway.t2Cd = Or06961Const.T2CD_8
      localOneway.or10883Oneway.columnName = Or06961Const.TABEL_COLUMN_NAME_SOGOIKOUKNJ
      localOneway.or10883Oneway.title = Or06961Const.TABEL_COLUMN_LABEL_SOGOIKOUKNJ
      localOneway.or10883Oneway.inputContents =
        tab3DataList.value[selectRowIndex.value].sogoIkouKnj.modelValue.value
      local.Or10883.naiyo = ''
      // GUI01109 入力支援【目標とする生活（１年）】画面をポップアップで起動する
      Or10883Logic.state.set({
        uniqueCpId: or10883.value.uniqueCpId,
        state: { isOpen: true },
      })
    },
  },
  {
    title: t('label.goal'),
    key: Or06961Const.TABEL_COLUMN_NAME_SOGOMOKUHYOKNJ,
    width: '200px',
    sortable: false,
    required: false,
    mo00009Oneway: {
      // デフォルト値の設定
      btnIcon: 'edit_square',
      density: 'compact',
      disabled: false,
    },
    click: () => {
      if (
        props.onewayModelValue.operaFlg === Or06961Const.ACTION_KBN_DELETE ||
        selectRowIndex.value < 0
      ) {
        return
      }
      localOneway.or10883Oneway.t2Cd = Or06961Const.T2CD_9
      localOneway.or10883Oneway.columnName = Or06961Const.TABEL_COLUMN_NAME_SOGOMOKUHYOKNJ
      localOneway.or10883Oneway.title = Or06961Const.TABEL_COLUMN_LABEL_SOGOMOKUHYOKNJ
      localOneway.or10883Oneway.inputContents =
        tab3DataList.value[selectRowIndex.value].sogoMokuhyoKnj.modelValue.value
      local.Or10883.naiyo = ''
      // GUI01109 入力支援【目標とする生活（１年）】画面をポップアップで起動する
      Or10883Logic.state.set({
        uniqueCpId: or10883.value.uniqueCpId,
        state: { isOpen: true },
      })
    },
  },
]
const local = reactive({
  Or06961: {
    tab3DataList: [],
    deleteTab3DataList: [],
    tab4DataList: [],
    deleteTab4DataList: [],
    selectRowIndex: -1,
  } as Or06961TwowayType,
  Or27347: {
    issuesList: [],
  } as Or27347Type,
  Or10883: {
    naiyo: '',
    confirmation: '',
  } as Or10883TwowayType,
})
const localOneway = reactive({
  // 行追加ボタン
  mo00611OnewayAdd: {
    btnLabel: t('btn.add-row'),
    prependIcon: 'add',
  } as Mo00611OnewayType,
  // 行削除ボタン
  mo00611OnewayDelete: {
    btnLabel: t('btn.delete-row'),
    prependIcon: 'delete',
    color: 'error',
    labelColor: 'error',
    disabled: false,
  } as Mo00611OnewayType,
  mo00615OnewayRow: {
    itemLabel: '0' + SPACE_FORWARD_SLASH + '0',
    customClass: {
      outerClass: 'page-label-center',
    },
  } as Mo00615OnewayType,
  or27347Oneway: {
    processType: '',
    issuesList: [],
  } as Or27347OnewayType,
  or10883Oneway: {
    userId: systemCommonsStore.getUserId ?? '',
    t1Cd: '40',
    t2Cd: '',
    t3Cd: '0',
    tableName: Or06961Const.TABLE_NAME,
    columnName: '',
    title: '',
    inputContents: '',
  } as Or10883OnewayType,
  // 詳細ボタン
  mo00611DetailBtnOneWay: {
    btnLabel: t('label.detail'),
  } as Mo00611OnewayType,
})
// 対象期間リスト
const tab3DataList: Ref<TableData[]> = ref([])
// 選択した行のindex
const selectRowIndex = ref<number>(-1)
const isDetail = ref<boolean>(props.onewayModelValue.isDetail)
const or21816 = ref({ uniqueCpId: Or21816Const.CP_ID(1) })
const or21815 = ref({ uniqueCpId: Or21815Const.CP_ID(1) })
const or27347 = ref({ uniqueCpId: Or27347Const.CP_ID(1) })
const or10883 = ref({ uniqueCpId: Or10883Const.CP_ID(1) })

Or21816Logic.state.set({
  uniqueCpId: or21816.value.uniqueCpId,
  state: {
    tooltipTextUp: t('tooltip.care-plan2-select-up'),
    tooltipTextDown: t('tooltip.care-plan2-select-next'),
  },
})

/**************************************************
 * Pinia
 **************************************************/
const { setEvent } = useScreenEventStatus<Or06961Type>({
  cpId: Or06961Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
const { refValue } = useScreenTwoWayBind<Or06961TwowayType>({
  cpId: Or06961Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue', 'updateIsDetail'])

/**************************************************
 * コンポーネント固有処理
 **************************************************/

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21816Const.CP_ID(1)]: or21816.value,
  [Or21815Const.CP_ID(1)]: or21815.value,
  [Or27347Const.CP_ID(1)]: or27347.value,
  [Or10883Const.CP_ID(1)]: or10883.value,
})

/**
 * 行選択
 *
 * @param index - 行インデックス
 */
const selectRow = (index: number) => {
  selectRowIndex.value = index
  if (selectRowIndex.value === local.Or06961.selectRowIndex) {
    return
  }
  local.Or06961.selectRowIndex = selectRowIndex.value
  formatDisplayPageNumber()
  refValue.value = local.Or06961
}

/**
 * ポップアップ画面での返回値
 */
watch(
  () => local.Or10883.naiyo,
  (newValue) => {
    if (newValue && selectRowIndex.value > -1) {
      const keyOfTab3Data = removeUnderscoreAndCapitalize(
        localOneway.or10883Oneway.columnName
      ) as keyof ITab3Data
      ;(tab3DataList.value[selectRowIndex.value][keyOfTab3Data].modelValue as Mo01280Type).value =
        newValue
      local.Or06961.tab3DataList = translateTableData(tab3DataList.value)
      refValue.value = local.Or06961
    }
  }
)

/**
 * ［課題番号変更］画面をポップアップ画面での返回値
 */
watch(
  () => local.Or27347.issuesList,
  (newValue) => {
    if (newValue.length > 0) {
      tab3DataList.value.forEach((row) => {
        const issues = newValue.find((x) => x.issuesNum.toString() === row.sogoKadaiNo)
        row.kadaiNo.modelValue.value = issues.setIssuesNum.toString()
      })
      tab3DataList.value = tab3DataList.value.sort(
        (a, b) => Number(a.kadaiNo.modelValue.value) - Number(b.kadaiNo.modelValue.value)
      )
      local.Or06961.tab3DataList = translateTableData(tab3DataList.value)
      selectRow(0)
    }
  }
)

/**
 * 表示用「総合的課題と目標」リストデータ再取得フラグの監視
 */
watch(
  () => refValue.value,
  (newValue) => {
    selectRowIndex.value = newValue?.selectRowIndex ?? -1
    local.Or06961 = newValue!
    initTable()
  }
)

/**
 * 行選択上下アイコンボタンを監視
 */
watch(
  () => Or21816Logic.event.get(or21816.value.uniqueCpId),
  (newValue) => {
    if (newValue === undefined) {
      return
    }
    if (newValue.upEventFlg) {
      onLongTermUpClick()
      Or21816Logic.event.set({
        uniqueCpId: or21816.value.uniqueCpId,
        events: { upEventFlg: false },
      })
    }
    if (newValue.downEventFlg) {
      onLongTermDownClick()
      Or21816Logic.event.set({
        uniqueCpId: or21816.value.uniqueCpId,
        events: { downEventFlg: false },
      })
    }
  }
)

/**
 * 「一つ上の行に移動アイコン」押下
 */
const onLongTermUpClick = () => {
  // 操作区分 = 3:削除の場合
  if (props.onewayModelValue.operaFlg !== Or06961Const.ACTION_KBN_DELETE) {
    if (selectRowIndex.value > 0) {
      selectRow(selectRowIndex.value - 1)
    }
  }
}
/**
 * 「一つ下の行に移動アイコン」押下
 */
const onLongTermDownClick = () => {
  // 操作区分 = 3:削除の場合
  if (props.onewayModelValue.operaFlg !== Or06961Const.ACTION_KBN_DELETE) {
    if (selectRowIndex.value > -1 && selectRowIndex.value < tab3DataList.value.length - 1) {
      selectRow(selectRowIndex.value + 1)
    }
  }
}

/**
 * テーブル初期化
 */
const initTable = () => {
  tab3DataList.value = []
  local.Or06961.tab3DataList.forEach((item) => {
    tab3DataList.value.push({
      sogoKadaiNo: item.sogoKadaiNo,
      plan13Id: item.plan13Id,
      modifiedCnt: item.modifiedCnt,
      kadaiNo: {
        modelValue: {
          value: item.kadaiNo,
        },
        onewayModelValue: {
          max: 99,
          min: -9,
          maxlength: Or06961Const.DEFAULT.MAX_LENGTH_NUMBER,
        },
      },
      sogoKadaiKnj: {
        modelValue: {
          value: item.sogoKadaiKnj,
        },
        onewayModelValue: {
          maxlength: Or06961Const.DEFAULT.MAX_LENGTH,
        },
      },
      sogoTeianKnj: {
        modelValue: {
          value: item.sogoTeianKnj,
        },
        onewayModelValue: {
          maxlength: Or06961Const.DEFAULT.MAX_LENGTH,
        },
      },
      sogoIkouKnj: {
        modelValue: {
          value: item.sogoIkouKnj,
        },
        onewayModelValue: {
          maxlength: Or06961Const.DEFAULT.MAX_LENGTH,
        },
      },
      sogoMokuhyoKnj: {
        modelValue: {
          value: item.sogoMokuhyoKnj,
        },
        onewayModelValue: {
          maxlength: Or06961Const.DEFAULT.MAX_LENGTH,
        },
      },
    })
  })
  if (selectRowIndex.value > -1 && selectRowIndex.value < tab3DataList.value.length) {
    formatDisplayPageNumber()
  } else {
    if (tab3DataList.value.length > 0 && selectRowIndex.value < 0) {
      selectRow(0)
    }
  }
  localOneway.mo00611OnewayDelete.disabled = tab3DataList.value.length === 0
}

/**
 * 表示用「行選択」情報をフォーマット 01 / 01
 *
 */
const formatDisplayPageNumber = () => {
  const value = String(selectRowIndex.value + 1)
    .padStart(2, '0')
    .concat(SPACE_FORWARD_SLASH)
    .concat(String(tab3DataList.value.length).padStart(2, '0'))
  localOneway.mo00615OnewayRow.itemLabel = value
}

/**
 * 行追加
 */
const addItem = () => {
  // 操作区分 = 3:削除の場合
  // 表示用「計画対象期間」情報.ページング区分が0:なしの場合
  if (
    props.onewayModelValue.operaFlg === Or06961Const.ACTION_KBN_DELETE ||
    props.onewayModelValue.pagingFlg === Or06961Const.PAGING_FLG_NONE
  ) {
    return
  }
  const kadaiNoList = tab3DataList.value
    .map((x) => Number(x.kadaiNo.modelValue.value))
    .sort()
    .reverse()
  const maxKadaiNo = kadaiNoList[0] ?? 0
  if (maxKadaiNo === 99) {
    showOr21815Msg(t('message.e-cmn-41354'))
  } else {
    const sogoKadaiNoList = tab3DataList.value
      .map((x) => Number(x.sogoKadaiNo))
      .sort()
      .reverse()
    const maxSogoKadaiNo = sogoKadaiNoList[0] ?? 0
    tab3DataList.value.push({
      sogoKadaiNo: (maxSogoKadaiNo + 1).toString(),
      sogoKadaiKnj: {
        modelValue: {
          value: '',
        },
        onewayModelValue: {
          maxlength: Or06961Const.DEFAULT.MAX_LENGTH,
        },
      },
      sogoTeianKnj: {
        modelValue: {
          value: '',
        },
        onewayModelValue: {
          maxlength: Or06961Const.DEFAULT.MAX_LENGTH,
        },
      },
      sogoIkouKnj: {
        modelValue: {
          value: '',
        },
        onewayModelValue: {
          maxlength: Or06961Const.DEFAULT.MAX_LENGTH,
        },
      },
      sogoMokuhyoKnj: {
        modelValue: {
          value: '',
        },
        onewayModelValue: {
          maxlength: Or06961Const.DEFAULT.MAX_LENGTH,
        },
      },
      kadaiNo: {
        modelValue: {
          value: (maxKadaiNo + 1).toString(),
        },
        onewayModelValue: {
          max: 99,
          min: -9,
          maxlength: Or06961Const.DEFAULT.MAX_LENGTH_NUMBER,
        },
      },
      plan13Id: '',
      modifiedCnt: '',
    })
    selectRow(tab3DataList.value.length - 1)
    local.Or06961.tab3DataList = translateTableData(tab3DataList.value)
    refValue.value = local.Or06961
  }
}

/**
 * 行削除
 */
const deleteItem = async () => {
  // 操作区分 = 3:削除の場合
  // 表示用「計画対象期間」情報.ページング区分が0:なしの場合
  // 削除行を指定しない
  if (
    props.onewayModelValue.operaFlg === Or06961Const.ACTION_KBN_DELETE ||
    props.onewayModelValue.operaFlg === Or06961Const.PAGING_FLG_NONE ||
    selectRowIndex.value < 0
  ) {
    return
  }
  const dialogResult = await openWarnDialog()
  switch (dialogResult) {
    case Or06961Const.DEFAULT.DIALOG_RESULT_YES: {
      const deleteData = tab3DataList.value[selectRowIndex.value]
      if (deleteData.plan13Id && deleteData.sogoKadaiNo) {
        const tab3DeleteDataList = translateTableData([deleteData])
        local.Or06961.deleteTab3DataList.push(...tab3DeleteDataList)
        // 表示用「支援計画」リストから総合的課題番号に紐づく行を削除する
        const tab4DeleteDataList = local.Or06961.tab4DataList.filter(
          (item) => item.sogoKadaiNo === deleteData.sogoKadaiNo
        )
        local.Or06961.deleteTab4DataList.push(...tab4DeleteDataList)
        local.Or06961.tab4DataList = local.Or06961.tab4DataList.filter(
          (item) => item.sogoKadaiNo !== deleteData.sogoKadaiNo
        )
      }
      tab3DataList.value.splice(selectRowIndex.value, 1)
      if (selectRowIndex.value >= tab3DataList.value.length) {
        selectRow(tab3DataList.value.length - 1)
      }
      local.Or06961.tab3DataList = translateTableData(tab3DataList.value)
      refValue.value = local.Or06961
      break
    }
    case Or06961Const.DEFAULT.DIALOG_RESULT_NO:
      break
  }
}

/**
 * 確認ダイアログ
 *
 * @param message - Message
 */
const showOr21815Msg = (message: string) => {
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.caution'),
      // ダイアログテキスト
      iconName: 'warning',
      dialogText: message,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })
}

/**
 * 選択行削除確認ダイアログを閉じたタイミングで結果を返却
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
const openWarnDialog = async (): Promise<string> => {
  // 選択行削除確認ダイアログを開く
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.caution'),
      // ダイアログテキスト
      iconName: 'warning',
      dialogText: t('message.i-cmn-10852'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
      thirdBtnLabel: 'blank',
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(or21815.value.uniqueCpId)

        let result = Or06961Const.DEFAULT.DIALOG_RESULT_CANCEL

        if (event?.firstBtnClickFlg) {
          result = Or06961Const.DEFAULT.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or06961Const.DEFAULT.DIALOG_RESULT_NO
        }

        // 選択行削除確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: or21815.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
          },
        })
        resolve(result)
      },
      { once: true }
    )
  })
}

// ダイアログ表示フラグ
const showDialogOr27347 = computed(() => {
  // Or27347のダイアログ開閉状態
  return Or27347Logic.state.get(or27347.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr10883 = computed(() => {
  // Or10883のダイアログ開閉状態
  return Or10883Logic.state.get(or10883.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 数値入力イベント
 *
 *@param reSetIndex - reSetIndex
 */
const onInputNumber = async (reSetIndex: number) => {
  // 数値以外の文字を削除
  const value = tab3DataList.value[reSetIndex].kadaiNo.modelValue.value
  tab3DataList.value[reSetIndex].kadaiNo.modelValue.value = value.replace(/[^0-9]/g, '')
  await nextTick()
}

/**
 * 「総合的課題と目標表一覧の明細部」値変更
 */
const changeTableData = () => {
  local.Or06961.tab3DataList = translateTableData(tab3DataList.value)
  refValue.value = local.Or06961
}

/**
 * TableData[] を ITab3Data[] に変換する
 *
 * @param table - 変換元のTableData配列
 *
 * @returns 変換後のTab3Data配列
 */
const translateTableData = (table: TableData[]): ITab3Data[] => {
  const tab3DataList: ITab3Data[] = []
  table.forEach((x) => {
    tab3DataList.push({
      sogoKadaiNo: x.sogoKadaiNo,
      sogoKadaiKnj: x.sogoKadaiKnj.modelValue.value,
      sogoTeianKnj: x.sogoTeianKnj.modelValue.value,
      sogoIkouKnj: x.sogoIkouKnj.modelValue.value,
      sogoMokuhyoKnj: x.sogoMokuhyoKnj.modelValue.value,
      kadaiNo: x.kadaiNo.modelValue.value,
      plan13Id: x.plan13Id,
      modifiedCnt: x.modifiedCnt,
    })
  })
  return tab3DataList
}

/**
 * 「課題番号」値変更
 *
 * @param index - index
 */
const checkKadaiNo = (index: number) => {
  const newValue = tab3DataList.value[index].kadaiNo.modelValue.value
  if (newValue === '') {
    tab3DataList.value[index].kadaiNo.modelValue.value = local.Or06961.tab3DataList[index].kadaiNo
  }
  const values = tab3DataList.value.map((item) => item.kadaiNo.modelValue.value)
  const hasDuplicates = new Set(values).size !== values.length
  if (hasDuplicates) {
    showOr21815Msg(t('message.i-cmn-11264'))
    tab3DataList.value[index].kadaiNo.modelValue.value = local.Or06961.tab3DataList[index].kadaiNo
  } else {
    changeTableData()
  }
}

/**
 * 選択されている項目
 *
 * @param selectCell - 選択されている項目
 */
const handleFocus = (selectCell: string) => {
  setEvent({
    selectCell: selectCell,
  })
}

/**
 * 「詳細ボタン」押下
 */
const setDetail = () => {
  isDetail.value = !isDetail.value
  emit('updateIsDetail', isDetail.value)
}

/**
 * 文字列中のアンダースコア `_` を削除し、アンダースコアの直後の文字を大文字に変換する（キャメルケース変換）
 *
 * @param input - アンダースコアを含む可能性のある入力文字列
 *
 * @returns 変換後のキャメルケース形式の文字列
 */
const removeUnderscoreAndCapitalize = (input: string): string => {
  // アンダースコアで分割して配列に変換
  return (
    input
      .split('_')
      // 空文字列を除去（先頭や連続するアンダースコアを処理）
      .filter((part) => part !== '')
      .map((part, index) => {
        if (index === 0) {
          // 最初の単語は小文字のまま（先頭のアンダースコアを無視）
          return part.toLowerCase()
        } else {
          // 2番目以降の単語は頭文字を大文字に変換
          return part.charAt(0).toUpperCase() + part.slice(1).toLowerCase()
        }
      })
      // 配列を結合して最終的な文字列を生成
      .join('')
  )
}
</script>
<template>
  <c-v-row
    v-if="!props.onewayModelValue.isCopyMode"
    class="top-row"
  >
    <div>
      <!-- 行追加ボタン -->
      <base-mo00611
        v-if="!props.onewayModelValue.isShowDetailBtn"
        :oneway-model-value="localOneway.mo00611OnewayAdd"
        class="mx-2"
        @click="addItem"
      >
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :text="$t('tooltip.add-row')"
        />
      </base-mo00611>
      <!-- 行削除ボタン -->
      <base-mo00611
        v-if="!props.onewayModelValue.isShowDetailBtn"
        :oneway-model-value="localOneway.mo00611OnewayDelete"
        class="mx-2"
        @click="deleteItem"
      >
        <c-v-tooltip
          activator="parent"
          location="bottom"
          :text="$t('tooltip.delete-row')"
        />
      </base-mo00611>
      <!-- 詳細ボタン Mo00611 -->
      <base-mo00611
        v-if="props.onewayModelValue.isShowDetailBtn"
        v-bind="localOneway.mo00611DetailBtnOneWay"
        @click="setDetail"
      />
    </div>
    <div class="flex-row">
      <!--行選択ラベル-->
      <base-mo00615 :oneway-model-value="localOneway.mo00615OnewayRow" />
      <!--行選択上下アイコンボタン-->
      <g-base-or21816 v-bind="or21816" />
    </div>
  </c-v-row>
  <!-- 総合的課題と目標表一覧 -->
  <c-v-form class="form">
    <c-v-data-table
      fixed-header
      :headers="headers"
      :items="tab3DataList"
      class="table-wrapper mt-2"
      hide-default-footer
      :items-per-page="-1"
    >
      <!-- ヘッダ Part -->
      <template #headers="{ columns }">
        <tr>
          <template
            v-for="column in columns"
            :key="column.key"
          >
            <th
              v-if="isDetail || column.key === Or06961Const.TABEL_COLUMN_NAME_SOGOMOKUHYOKNJ"
              :style="`min-width:${column.width}`"
            >
              <c-v-row class="header-text">
                <div class="flex-row">
                  <div
                    v-if="column.required"
                    class="required"
                  >
                    *
                  </div>
                  {{ $t(column.title ?? '') }}
                </div>
                <div
                  v-if="!props.onewayModelValue.isCopyMode"
                  class="flex-row"
                >
                  <c-v-divider
                    vertical
                    inset
                  />
                  <base-mo00009
                    :oneway-model-value="column.mo00009Oneway"
                    @click="column.click"
                  />
                </div>
              </c-v-row>
            </th>
          </template>
        </tr>
      </template>
      <!-- 一覧 -->
      <template #item="{ item, index }">
        <tr
          :class="{ 'select-row': selectRowIndex === index }"
          @click="selectRow(index)"
        >
          <!-- 課題番号 -->
          <td v-if="isDetail">
            <base-mo01278
              v-model="item.kadaiNo.modelValue"
              :oneway-model-value="item.kadaiNo.onewayModelValue"
              :readonly="props.onewayModelValue.isCopyMode"
              @input="onInputNumber(index)"
              @update:model-value="checkKadaiNo(index)"
              @click.stop="handleFocus(Or06961Const.TABEL_COLUMN_NAME_KADAINO)"
            >
            </base-mo01278>
          </td>
          <!-- 総合的課題 -->
          <td v-if="isDetail">
            <base-mo01280
              v-model="item.sogoKadaiKnj.modelValue"
              :oneway-model-value="item.sogoKadaiKnj.onewayModelValue"
              :readonly="props.onewayModelValue.isCopyMode"
              @change="changeTableData()"
              @focus="handleFocus(Or06961Const.TABEL_COLUMN_NAME_SOGOKADAIKNJ)"
            >
            </base-mo01280>
          </td>
          <!-- 課題に対する目標と具体策の提案 -->
          <td v-if="isDetail">
            <base-mo01280
              v-model="item.sogoTeianKnj.modelValue"
              :oneway-model-value="item.sogoTeianKnj.onewayModelValue"
              :readonly="props.onewayModelValue.isCopyMode"
              @change="changeTableData()"
              @focus="handleFocus(Or06961Const.TABEL_COLUMN_NAME_SOGOTEIANKNJ)"
            >
            </base-mo01280>
          </td>
          <!-- 具体策についての意向 本人・家族 -->
          <td v-if="isDetail">
            <base-mo01280
              v-model="item.sogoIkouKnj.modelValue"
              :oneway-model-value="item.sogoIkouKnj.onewayModelValue"
              :readonly="props.onewayModelValue.isCopyMode"
              @change="changeTableData()"
              @focus="handleFocus(Or06961Const.TABEL_COLUMN_NAME_SOGOIKOUKNJ)"
            >
            </base-mo01280>
          </td>
          <!-- 目標 -->
          <td>
            <base-mo01280
              v-model="item.sogoMokuhyoKnj.modelValue"
              :oneway-model-value="item.sogoMokuhyoKnj.onewayModelValue"
              :readonly="props.onewayModelValue.isCopyMode"
              @change="changeTableData()"
              @focus="handleFocus(Or06961Const.TABEL_COLUMN_NAME_SOGOMOKUHYOKNJ)"
            >
            </base-mo01280>
          </td>
        </tr>
      </template>
    </c-v-data-table>
  </c-v-form>

  <!-- 確認ダイアログ -->
  <g-base-or21815 v-bind="or21815" />
  <!-- ［課題番号変更］画面をポップアップ -->
  <g-custom-or-27347
    v-if="showDialogOr27347"
    v-bind="or27347"
    v-model="local.Or27347"
    :oneway-model-value="localOneway.or27347Oneway"
  />
  <!--GUI01109 入力支援【目標とする生活（１年）】画面-->
  <g-custom-or-10883
    v-if="showDialogOr10883"
    v-bind="or10883"
    v-model="local.Or10883"
    :oneway-model-value="localOneway.or10883Oneway"
  />
</template>
<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table-list.scss';
// 選択した行のCSS
.select-row {
  background: rgb(var(--v-theme-blue-100));
}

.required {
  color: rgb(var(--v-theme-orange-400));
}
.header-text {
  white-space: break-spaces;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.top-row {
  margin: 8px 0px;
  display: flex;
  justify-content: space-between;
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.table-wrapper :deep(.v-table__wrapper th) {
  background-color: rgb(var(--v-theme-black-100)) !important;
  border: 1px rgb(var(--v-theme-black-200)) solid !important;
  font-size: 14px;
  font-weight: bold;
  height: 42px;
}
.table-wrapper .v-table__wrapper td {
  border: 1px rgb(var(--v-theme-black-200)) solid !important;
  padding: 0;
  font-size: 14px;
}
.form {
  margin: 0px 8px;
}
.page-label-center {
  display: flex;
  align-items: center;
  background: transparent !important;
}
</style>
