import { getSequencedCpId } from '~/utils/useScreenUtils'
/**
 * Or27592:支援経過記録一覧印刷設定モーダル
 * GUI01264_印刷設定
 *
 * @description
 * 静的データ
 *
 * <AUTHOR> PHAM TIEN THANH
 */
export namespace Or27592Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or27592', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false
    /**
     * 0
     */
    export const ZERO_NUMBER = 0
  }

  /**
   * 選択インデックス値
   */
  export namespace CHOICE_INDEX {
    /** 通常モード */
    export const NORMAL_MODE = '1'
    /** シンプルモード（介護支援経過日別一覧モード） */
    export const SUPPORT_RECORD_DAILY_LIST_MODE = '2'
  }

  /**
   * パラメータ値
   */
  export namespace PARAMETER {
    /** 有効 */
    export const ACTIVE = '1'
    /** 無効 */
    export const INACTIVE = '0'
  }

  /**
   * プリント番号
   */
  export namespace PRINT_NUMBER {
    /** 通常モード用プリント番号 */
    export const NORMAL_MODE = '1'
  }

  /**
   * 利用者選択タイプ
   */
  export namespace USER_SELECT_TYPE {
    /** 単体選択 */
    export const TANI = '0'
    /** 複数選択 */
    export const HUKUSUU = '1'
  }

  /**
   * 印刷カテゴリ
   */
  export namespace PRINT_CATEGORY {
    /** 日付印刷 */
    export const DATE_PRINT = '2'
  }

  /**
   * 印刷タイプ
   */
  export namespace PRINT_TYPE {
    /** 登録日区分印刷 */
    export const REGISTRATION_DATE = '1'
  }

  /**
   * ケアマネージャー設定フラグ
   */
  export namespace CARE_MANAGER_FLAG {
    /** 無効 */
    export const DISABLED = '0'
    /** 有効 */
    export const ENABLED = '1'
  }

  /**
   * 管理者ID
   */
  export namespace MANAGER_ID {
    /** デフォルト値（無効） */
    export const DEFAULT = '0'
  }

  /**
   * ダイアログソース
   */
  export namespace DIALOG_SOURCE {
    /** 記録者フィルター */
    export const FILTER_BY_RECORDER = 'filterByRecorder'
    /** 作成者 */
    export const AUTHOR = 'author'
    /** 空文字 */
    export const EMPTY = ''
  }

  /**
   * システム略称
   */
  export namespace SYSTEM_ABBREVIATION {
    /** CMN */
    export const CMN = 'CMN'
  }

  /**
   * 特別な事業所ID
   */
  export namespace SPECIAL_OFFICE_ID {
    /** 介護予防ケアマネジメント様式で印刷する */
    export const ID_50010 = '50010'
  }

  /**
   * 空文字値
   */
  export namespace EMPTY_VALUES {
    /** 空文字 */
    export const EMPTY_STRING = ''
  }

  /**
   * 50音
   */
  export namespace GOJUON {
    /** 全 */
    export const ALL = '0'
    /** 他 */
    export const OTHER = '99'
  }

  /**
   * ダイアログ結果
   */
  export namespace DIALOG_RESULT {
    /** はい */
    export const YES = 'yes'
    /** いいえ */
    export const NO = 'no'
  }
  /**
   * 支援経過記録様式により判断
   */
  export namespace RECORD_FORM {
    /** 改訂前（通常）の場合 */
    export const BEFORE_REVISION = '1'
    /** R3/4改訂版の場合 */
    export const AFTER_REVISION = '2'
  }
}
