import { Or06680Const } from './Or06680.constants'
import type { Or06680StateType } from './Or06680.type'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'
/**
 * Or06680:有機体:入退所利用情報
 * GUI01308_ 入退所利用情報画面
 *
 * @description
 * initialize処理とpinia領域操作用の処理を提供する
 *
 * <AUTHOR>
 */
export namespace Or06680Logic {
  /**
   * initialize
   *
   * @description
   * コンポーネントの初期化処理。
   * 共通関数を呼びpiniaの領域を初期化する。
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or06680Const.CP_ID(0),
      uniqueCpId,
      childCps: [],
    })
    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }
  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or06680StateType>(Or06680Const.CP_ID(0))
}
