<script setup lang="ts">
import { computed, nextTick, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or51734Logic } from '../Or51734/Or51734.logic'
import { Or51733Const } from '../Or51733/Or51733.constants'
import type { TableData } from '../Or51733/Or51733.type'
import { Or51734Const } from '../Or51734/Or51734.constants'
import { Or51732Const } from './Or51732.constants'
import type { AsyncFunction, Or51732StateType } from './Or51732.type'
import { useScreenOneWayBind, useSetupChildProps, useScreenUtils } from '#imports'
import type { Or51733Type } from '~/types/cmn/business/components/Or51733Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { UPDATE_KBN } from '~/constants/classification-constants'
import type {
  MaskMastSelectInEntity,
  MaskMastSelectOutEntity,
} from '~/repositories/cmn/entities/maskMastSelectEntity'
import type {
  MaskMastUpdateInEntity,
  MaskMastUpdateOutEntity,
} from '~/repositories/cmn/entities/maskMastUpdateEntity'
import type { Mo01265OnewayType } from '~/types/business/components/Mo01265Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'

/**
 * Or51732:有機体:実施モニタリング記号マスタ タブ コンポーネント
 * GUI01250_実施モニタリング記号マスタ
 *
 * @description
 * 実施モニタリング記号マスタ タブ コンポーネント
 *
 * <AUTHOR>
 */

/************************************************
 * Props
 ************************************************/
interface Props {
  uniqueCpId: string
  parentUniqueCpId: string
}
// 引継情報を取得する
const props = defineProps<Props>()
/**************************************************
 * 変数定義
 **************************************************/
const { getChildCpBinds } = useScreenUtils()

const { t } = useI18n()
const local = reactive({
  or51733: {
    editFlg: false,
    delBtnDisabled: false,
    focusIndex: '',
    focusType: '',
    maskMastInfoList: [],
  } as Or51733Type,
  kbnFlg: '',
  focusIndex: '',
  //  行追加ボタン
  mo00611OnewayAdd: {
    btnLabel: t('btn.add-row'),
    prependIcon: 'add',
    width: '90px',
  } as Mo00611OnewayType,
  btnAddItemTooltip: {
    memoInputIconBtn: t('tooltip.add-row'),
    auxiliaryInputDialogBtn: t('tooltip.add-row'),
  },
  // 行複写ボタン
  mo00611OnewayCopy: {
    btnLabel: t('btn.duplicate-row'),
    prependIcon: 'file_copy',
    width: '90px',
  } as Mo00611OnewayType,
  btnDuplicateRowTooltip: {
    memoInputIconBtn: t('tooltip.duplicate-row'),
    auxiliaryInputDialogBtn: t('tooltip.duplicate-row'),
  },
  mo01265OneWay: {
    btnLabel: t('btn.delete-row'),
    prependIcon: 'delete',
    tooltipText: t('tooltip.delete-row'),
    disabled: true,
  } as Mo01265OnewayType,
})

// 実施モニタリング記号一覧
const or51733 = ref({ uniqueCpId: '' })

// Or21813_有機体:エラーダイアログ
const or21813 = ref({ uniqueCpId: '' })

// 閉じるフラグ
const isClose = ref(false)

// ダイアログ表示フラグ
const showDialogOr21813 = computed(() => {
  // Or21813のダイアログ開閉状態
  return Or21813Logic.state.get(or21813.value.uniqueCpId)?.isOpen ?? false
})

// 保存Type
let saveType = ''

// Or51733 Ref
const or51733Ref = ref<{
  createRow(): AsyncFunction
  copyRow(): AsyncFunction
  deleteRow(): AsyncFunction
  init(): AsyncFunction
}>()

/**************************************************
 * コンポーネント固有処理
 **************************************************/
useSetupChildProps(props.uniqueCpId, {
  [Or51733Const.CP_ID(0)]: or51733.value,
})

useScreenOneWayBind<Or51732StateType>({
  cpId: Or51732Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    param: (value) => {
      local.kbnFlg = value!.kbnFlg
      switch (value?.executeFlag) {
        // 保存
        case 'save':
          if (value.isClose) {
            isClose.value = value.isClose
          }
          saveType = value?.saveType ?? ''

          void save()

          if (value?.saveType === Or51734Const.DEFAULT.CLOSEANDSAVE) {
            Or51734Logic.state.set({
              uniqueCpId: props.parentUniqueCpId,
              state: {
                isOpen: false,
              },
            })
          }
          break
        // データ再取得
        case 'getData':
          void init()
          break
        default:
          break
      }
    },
  },
})
/**
 * 保存ボタン押下_保存権限がある場合の開閉
 *
 * @param errormsg - Message
 */
function showOr21813Msg(errormsg: string) {
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト0
      dialogText: errormsg,
      firstBtnType: 'normal1',
      firstBtnLabel: 'OK',
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })
  // エラーダイアログをオープン
  Or21813Logic.state.set({
    uniqueCpId: or21813.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
}

/**
 * 行追加ボタン
 */
function onAddItem() {
  or51733Ref.value?.createRow()
}
/**
 * 行複写ボタン
 */
function onCloneItem() {
  or51733Ref.value?.copyRow()
}
/**
 * 行削除ボタン
 */
function onDelete() {
  or51733Ref.value?.deleteRow()
}

/**
 * 行削除活性
 *
 * @param disabled - 活性
 */
function delBtnDisable(disabled: boolean) {
  // 行削除活性
  local.mo01265OneWay.disabled = disabled
  // ツールチップ表示
  local.mo01265OneWay.tooltipText = t('tooltip.delete-row')
}

/**
 * 初期情報取得
 */
const init = async () => {
  // 実施モニタリング記号マスタ情報取得(IN)
  const inputData: MaskMastSelectInEntity = {
    kbnFlg: local.kbnFlg,
    cf1Flg: Or51732Const.DEFAULT.CF1FLG,
  }
  // 実施モニタリング記号マスタ初期情報取得
  const res: MaskMastSelectOutEntity = await ScreenRepository.select('maskMastSelect', inputData)
  local.or51733.maskMastInfoList = res.data.maskMastInfoList.map((item, index) => {
    return {
      ...item,
      id: String(index + 1),
      updateKbn: '',
    }
  })
  await nextTick()
  or51733Ref.value?.init()
}

/**
 * 保存
 */
async function save() {
  local.focusIndex = ''
  local.or51733.focusIndex = ''
  const nameMap = new Map<string, number>()

  const childCpBindsData = getChildCpBinds(props.uniqueCpId, {
    // 一覧データ
    [Or51733Const.CP_ID(0)]: { cpPath: Or51733Const.CP_ID(0), twoWayFlg: true },
  })

  // 一覧データを取得
  const tableData = childCpBindsData[Or51733Const.CP_ID(0)].twoWayBind?.value as TableData[]

  for (let index = 0; index < tableData.length; index++) {
    const data = tableData[index]

    //「区分番号」或いは「内容」に空白があるの場合(更新区分'D'除外)
    if (
      (data.kbnCd.value === '' || data.textKnj.value === '') &&
      data.updateKbn !== UPDATE_KBN.DELETE
    ) {
      local.focusIndex = index + ''
      local.or51733.focusType = data.kbnCd
        ? Or51733Const.DEFAULT.TEXT_KNJ
        : Or51733Const.DEFAULT.KBN_CD
      showOr21813Msg(t('message.e-cmn-41708'))
      break
    }

    // 「区分番号」の入力値は1000以下の場合(更新区分'D'除外)

    if (Number(data.kbnCd) < 1000 && data.updateKbn !== UPDATE_KBN.DELETE) {
      local.focusIndex = index + ''
      local.or51733.focusType = Or51733Const.DEFAULT.KBN_CD
      showOr21813Msg(t('message.e-cmn-41711', [t('label.category-number')]))
      break
    }

    //「区分番号」は重複の場合(更新区分'D'除外)
    if (nameMap.get(data.kbnCd.value)! >= 0 && data.updateKbn !== UPDATE_KBN.DELETE) {
      local.focusIndex = index + ''
      local.or51733.focusType = Or51733Const.DEFAULT.KBN_CD
      showOr21813Msg(t('message.e-cmn-41731', [t('label.category-number')]))
      break
    }
    if (data.updateKbn !== UPDATE_KBN.DELETE) {
      nameMap.set(data.kbnCd.value, index)
    }
  }

  // エラーがある場合、処理中止
  if (local.focusIndex) {
    return
  }

  const param: MaskMastUpdateInEntity = {
    maskMastInfoList: tableData.map((item) => {
      return {
        kbnCd: item.kbnCd.value,
        textKnj: item.textKnj.value,
        cf1Id: item.cf1Id,
        cf1Flg: item.cf1Flg,
        kbnFlg: item.kbnFlg,
        changeF: item.changeF,
        modifiedCnt: item.modifiedCnt,
        updateKbn: item.updateKbn,
      }
    }),
  }

  // 情報保存
  const res: MaskMastUpdateOutEntity = await ScreenRepository.update('maskMastUpdate', param)
  if (isClose.value) {
    Or51734Logic.state.set({
      uniqueCpId: props.parentUniqueCpId,
      state: {
        isOpen: false,
      },
    })
  }
  if (res.statusCode === 'success') {
    // 画面情報再取得
    if (saveType === Or51734Const.DEFAULT.ONLYSAVE) {
      void init()
    }
    if (saveType === Or51734Const.DEFAULT.CHANGETABSAVE) {
      Or51734Logic.event.set({
        uniqueCpId: props.parentUniqueCpId,
        state: {
          isSave: true,
        },
      })
    }
  }
}

/**
 * 行選択処理
 *
 * @param selectedId - 選択行ID
 */
function onSelect(selectedId?: string) {
  if (selectedId) {
    if (selectedId.length > 0) {
      delBtnDisable(false)
      return
    }
  }

  delBtnDisable(true)
}

/**
 * Or21813のイベントを監視
 *
 * @description
 * またOr21813のボタン押下フラグをリセットする。
 */
watch(
  () => Or21813Logic.event.get(or21813.value.uniqueCpId),
  () => {
    local.or51733.focusIndex = local.focusIndex
  }
)
</script>

<template>
  <c-v-row no-gutters>
    <c-v-col class="h-100">
      <c-v-row no-gutters>
        <!-- 実施モニタリング記号一覧および操作ボタン -->
        <c-v-row
          no-gutters
          class="align-center"
        >
          <div class="btn-container mt-2">
            <!-- 行追加ボタン -->
            <base-mo00611
              :oneway-model-value="local.mo00611OnewayAdd"
              class="ml-0 pl-0 mr-2"
              @click="onAddItem()"
            >
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="local.btnAddItemTooltip.memoInputIconBtn"
              />
            </base-mo00611>
            <!-- 行複写ボタン -->
            <base-mo00611
              class="ml-0 pl-0 mr-2"
              :oneway-model-value="local.mo00611OnewayCopy"
              @click="onCloneItem()"
            >
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="local.btnDuplicateRowTooltip.memoInputIconBtn"
              />
            </base-mo00611>
            <!-- 削除ボタン -->
            <base-mo01265
              v-bind="local.mo01265OneWay"
              @click="onDelete()"
            >
              <c-v-tooltip
                v-if="local.mo01265OneWay.tooltipText"
                :text="local.mo01265OneWay.tooltipText"
                location="bottom"
                activator="parent"
              />
            </base-mo01265>
          </div>
        </c-v-row>
      </c-v-row>
      <c-v-row
        no-gutters
        class="flex-1-1 mt-2"
      >
        <!-- 実施モニタリング記号一覧 -->
        <g-custom-or-51733
          ref="or51733Ref"
          v-bind="or51733"
          :model-value="local.or51733"
          :parent-unique-cp-id="props.uniqueCpId"
          @update:model-value="onSelect"
        />
      </c-v-row>
    </c-v-col>
  </c-v-row>
  <!-- Or21813:有機体:エラーダイアログ -->
  <g-base-or21813
    v-if="showDialogOr21813"
    v-bind="or21813"
  />
</template>
