<script setup lang="ts">
/**
 * Or01533:有機体:(日課表)日課表イメージ
 * GUI00989_日課表イメージ
 *
 * @description
 * 日課表イメージ）メイン画面の処理
 *
 * <AUTHOR> 呉李彪
 */
import { onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { isUndefined } from 'lodash'
import { v4 as uuidv4 } from 'uuid'
import { Or01533Const } from '../Or01533/Or01533.constants'
import type { Or01533Type, TokkiKnjInfo } from '../Or01533/Or01533.type'
import { OrX0114Const } from '../OrX0114/OrX0114.constants'
import { OrX0114Logic } from '../OrX0114/OrX0114.logic'
import { Or28153Const } from '../Or28153/Or28153.constants'
import { Or28153Logic } from '../Or28153/Or28153.logic'
import {
  computed,
  useCommonProps,
  useSetupChildProps,
  useScreenUtils,
  useColorUtils,
  useScreenStore,
} from '#imports'
import { Or27539Const } from '~/components/custom-components/organisms/Or27539/Or27539.constants'
import { Or27539Logic } from '~/components/custom-components/organisms/Or27539/Or27539.logic'
import { Or28528Const } from '~/components/custom-components/organisms/Or28528/Or28528.constants'
import { Or28528Logic } from '~/components/custom-components/organisms/Or28528/Or28528.logic'
import { Or28220Const } from '~/components/custom-components/organisms/Or28220/Or28220.constants'
import { Or28220Logic } from '~/components/custom-components/organisms/Or28220/Or28220.logic'
import { Or28221Const } from '~/components/custom-components/organisms/Or28221/Or28221.constants'
import { Or28221Logic } from '~/components/custom-components/organisms/Or28221/Or28221.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Or27539OnewayType, Or27539Type } from '~/types/cmn/business/components/Or27539Type'
import type { Or28528OnewayType, Or28528Type } from '~/types/cmn/business/components/Or28528Type'
import type { Or28220OnewayType, Or28220Type } from '~/types/cmn/business/components/Or28220Type'
import type { Or28221OnewayType, Or28221Type } from '~/types/cmn/business/components/Or28221Type'
import type { OrX0114OnewayType, OrX0114Type, SvType  } from '~/types/cmn/business/components/OrX0114Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import { CustomClass } from '~/types/CustomClassType'
import { CmnMCdKbnId } from '~/constants/cmn/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import {  UPDATE_KBN } from '~/constants/classification-constants'
import type { DblClickResult, OrX0099SegmentedEvent } from '~/types/cmn/business/components/OrX0099Type'
import type { Or28153Type } from '~/types/cmn/business/components/Or28153Type'
import type { DailyTablePatternConfigInitSelectInEntity, DailyTablePatternConfigInitSelectOutEntity } from '~/repositories/cmn/entities/DailyTablePatternConfigInitSelectEntity'
import type { Mo00040OnewayType, Mo00040Type } from '~/types/business/components/Mo00040Type'
import type { Mo00610OnewayType } from '~/types/business/components/Mo00610Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type { OrX0044Type } from '~/types/cmn/business/components/OrX0044Type'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import type { DailyTablePatternConfigUpdateInEntity } from '~/repositories/cmn/entities/DailyTablePatternConfigUpdateEntity'


const { convertDecimalToHex ,convertHexToDecimal} = useColorUtils()
const { t } = useI18n()
const { setChildCpBinds } = useScreenUtils()
/**************************************************
 * Props
 **************************************************/
const props = defineProps(useCommonProps())
/**************************************************
 * 変数定義
 **************************************************/

const local = reactive({
  or01533: {} as Or01533Type,
  // 日課表入力
  or27539: {} as Or27539Type,
  // 日課表
  or28153: {} as Or28153Type,
  // 日常生活活動等
  or28528: { rowNum: 0, totalCount: 0, items: [] } as Or28528Type,
  // 自立支援に関する処遇
  or28220: { rowNum: 0, totalCount: 0, items: [] } as Or28220Type,
  // 介護(介護予防)サービス
  or28221: { rowNum: 0, totalCount: 0, items: [] } as Or28221Type,
  // 特記事項、サービス例エリアタブ
  orX0114: { tokkiKnj: {}, sonotaKnj: {}, svList: [] } as OrX0114Type,
  mo00043: { id: Or01533Const.TAB.TAB_ID_DAILY_TABLE_IMAGE } as Mo00043Type,
  mo00043Transfer: { id: Or01533Const.TAB.TAB_ID_DAILY_TABLE_IMAGE } as Mo00043Type,
  mo00043Change: { id: Or01533Const.TAB.TAB_ID_DAILY_TABLE_IMAGE } as Mo00043Type,
  clickEdUUid: '',
  processFlg: Or01533Const.PROCESS_FLG_INIT,
  mo00040OneOld: '',
  mo00040TwoOld: '',
  mo00040One: { modelValue: '' } as Mo00040Type,
  mo00040Two: { modelValue: '' } as Mo00040Type,
  titlelist: [] as {
    /** ＩＤ */
    day1Id: string
    /** 特記事項 */
    groupCd: string
    /** 随時実施するその他のサービス */
    titleKnj: string
    /** 改訂フラグ */
    ,
  }[],
})
const localOneway = reactive({
  mo00610OneWay: {
    btnLabel: t('btn.import'),
  } as Mo00610OnewayType,
  // タブ
  mo00043OneWay: {
    tabItems: [
      { id: Or01533Const.TAB.TAB_ID_DAILY_TABLE_IMAGE, title: t('label.daily-table-image') },
      {
        id: Or01533Const.TAB.TAB_ID_DAILY_LIFE_ACTIVITIES,
        title: t('label.daily-life-activities'),
      },
      {
        id: Or01533Const.TAB.TAB_ID_TREATMENT_RELATED_TO_SELF_RELIANCE_SUPPORT,
        title: t('label.treatment-related-to-self-reliance-support'),
      },
      {
        id: Or01533Const.TAB.TAB_ID_NURSING_PREVENTIVE_CARE_SERVICES,
        title: t('label.nursing-preventive-care-services'),
      },
      {
        id: Or01533Const.TAB.TAB_ID_SPECIAL_NOTES_SERVICES_EXAMOLES,
        title: t('label.special-notes-services-examples'),
      },
    ],
    minWidth: '100px',
  } as Mo00043OnewayType,
  // 日課表入力
  or27539Oneway: {} as Or27539OnewayType,
  // 日常生活活動等
  or28528Oneway: {} as Or28528OnewayType,
  // 自立支援に関する処遇
  or28220Oneway: {} as Or28220OnewayType,
  // 介護(介護予防)サービス
  or28221Oneway: {} as Or28221OnewayType,
  // 特記事項、サービス例エリアタブ
  orX0114Oneway: {} as OrX0114OnewayType,
  // 文字サイズ
  letterSize: { radioItemsList: [] as CodeType[] },
  // 文字位置
  letterPosition: { radioItemsList: [] as CodeType[] },
  // 時間表示
  timeDisplay: { radioItemsList: [] as CodeType[] },
  mo00040OnewayOne: {
    itemLabel:t('label.print-settings-title'),
    showItemLabel: true,
    isRequired: false,
    itemValue: 'day1Id',
    itemTitle: 'titleKnj',
    width: '430px',
    items: [],
  } as Mo00040OnewayType,
  mo00040OnewayTwo: {
    itemLabel:t('label.group'),
    showItemLabel: true,
    isRequired: false,
    itemValue: 'groupCd',
    itemTitle: 'groupKnj',
    width: '430px',
    items: [],
  } as Mo00040OnewayType,
})

const or28153_1 = ref({ uniqueCpId: '' })
// 日常生活活動等
const or28528_1 = ref({ uniqueCpId: '' })
// 自立支援に関する処遇
const or28220_1 = ref({ uniqueCpId: '' })
// 介護(介護予防)サービス
const or28221_1 = ref({ uniqueCpId: '' })
// 特記事項、サービス例エリアタブ
const orX0114_1 = ref({ uniqueCpId: '' })
// 日課表入力
const or27539_1 = ref({ uniqueCpId: '' })
const or21813_1 = ref({ uniqueCpId: '' })
const or21814_1 = ref({ uniqueCpId: '' })


/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or27539Const.CP_ID(1)]: or27539_1.value,
  [Or28153Const.CP_ID(1)]: or28153_1.value,
  // 日常生活活動等
  [Or28528Const.CP_ID(1)]: or28528_1.value,
  // 自立支援に関する処遇
  [Or28220Const.CP_ID(1)]: or28220_1.value,
  // 介護(介護予防)サービス
  [Or28221Const.CP_ID(1)]: or28221_1.value,
  // 特記事項、サービス例エリアタブ
  [OrX0114Const.CP_ID(1)]: orX0114_1.value,
  [Or21813Const.CP_ID(1)]: or21813_1.value,
  [Or21814Const.CP_ID(1)]: or21814_1.value,
})

onMounted(async () => {
  // 初期情報取得
  await initData()
})

// ダイアログ表示フラグ
const showDialogOr27539 = computed(() => {
  // Or27539のダイアログ開閉状態
  return Or27539Logic.state.get(or27539_1.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
/**
 * 初期化処理
 */
const initData = async () => {
  // 汎用コードマスタデータ
  await initCodes()
  //データ取得
  await getCarePlanTable()
}

/**
 * 保存処理を行う。
 */
async function insert() {
  const inputParam: DailyTablePatternConfigUpdateInEntity = {
    day1Id: local.mo00040One?.modelValue ?? '',
    tokkiKnjInfo: {} as TokkiKnjInfo,
    patternList: [],
    svList:[],
  }

  if (local.or01533.dailyInfoList) {
    local.or01533.dailyInfoList.forEach((item) => {
       inputParam.patternList?.push({
        // カウンタ
        id: item.id,
        /** 区分 */
        dataKbn: item.dataKbn,
        /** 開始時間 */
        startTime: item.startTime,
        /** 終了時間 */
        endTime: item.endTime,
        /** 内容CD */
        naiyoCd: item.naiyoCd,
        /** 日常処遇サービスメモ */
        memoKnj: item.memoKnj,
        /** 担当者 */
        tantoKnj: item.tantoKnj,
        /** 文字サイズ */
        fontSize: item.fontSize,
        /** 文字位置 */
        alignment: item.alignment,
        /** 文字カラー */
        fontColor: item.fontColor ? convertHexToDecimal(item.fontColor) : '',
        /** 背景カラー */
        backColor: item.fontColor ? convertHexToDecimal(item.backColor): '',
        /** 時間表示区分 */
        timeKbn: item.timeKbn,
        /** 更新回数 */
        modifiedCnt: item.modifiedCnt,
        /** 更新区分 */
        updateKbn: item.updateKbn,
       })
     })
  }
  inputParam.tokkiKnjInfo = {
      // 特記事項
      tokkiKnj: local.or01533.tokkiKnj.tokkiKnj,
      // 随時実施するその他のサービス.
      sonotaKnj: local.or01533.tokkiKnj.sonotaKnj,
      // 更新回数
      modifiedCnt: local.or01533.tokkiKnj.modifiedCnt,
  }
  if (local.or01533.svInfoList) {
    local.or01533.svInfoList.forEach((item) => {
       inputParam.svList?.push({
        // カウンタ
        svId: item.svId,
        /** 区分 */
        dataKbn: item.dataKbn,
        /** 適用フラグ */
        tekiyoFlg: item.tekiyoFlg,
        /** 更新回数 */
        modifiedCnt: item.modifiedCnt,
        /** 内容CD */
        updateKbn: item.updateKbn,
       })
     })
   }
  //画面情報の保存処理を行う。
  await ScreenRepository.update('dailyTablePatternConfigUpdate', inputParam)
}



/**
 *  汎用コードマスタデータを取得し初期化
 */
async function initCodes() {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // コード区分マスタID: 文字サイズ
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DAILY_LETTER_SIZE },
    // コード区分マスタID: 文字位置
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_DAILY_LETTER_POSITION },
    // コード区分マスタID: 時間表示
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TIME_DISPLAY },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 文字サイズ
  localOneway.letterSize.radioItemsList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_DAILY_LETTER_SIZE
  )
  // 文字位置
  localOneway.letterPosition.radioItemsList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_DAILY_LETTER_POSITION
  )
  // 時間表示
  localOneway.timeDisplay.radioItemsList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TIME_DISPLAY
  )
}

/**
 * 画面表示のデータを取得
 */
const getCarePlanTable = async () => {
  const inputParam: DailyTablePatternConfigInitSelectInEntity = {
    processFlg: local.processFlg,
    day1Id: local.mo00040One?.modelValue ?? ''
  }

  const ret: DailyTablePatternConfigInitSelectOutEntity = await ScreenRepository.select(
    'dailyTablePatternConfigInitSelect',
    inputParam
  )
  // 画面コントロール表示設定
  initOr01533(ret)
  // 日常生活活動等
  initGui00991()
  // 自立支援に関する処遇
  initGui00992()
  // 介護(介護予防)サービス
  initGui00993()
  // 特記事項、サービス例エリアタブ
  initGui00994()
  //Input
  setChildCpBinds(props.uniqueCpId, {
    [Or28153Const.CP_ID(1)]: {
      twoWayValue: {
        dailyInfoList:local.or28153.dailyInfoList,
      },
    },
    [Or28528Const.CP_ID(1)]: {
      twoWayValue: {
        items:local.or28528.items,
      },
     },
    [Or28220Const.CP_ID(1)]: {
      twoWayValue: {
        items: local.or28220.items,
      },
    },
    [Or28221Const.CP_ID(1)]: {
      twoWayValue: {
        items: local.or28221.items,
      },
    },
    [OrX0114Const.CP_ID(1)]: {
      oneWayState: localOneway.orX0114Oneway,
      twoWayValue: local.orX0114,
    },
  })
}

/**
 * 画面コントロール表示設定
 *
 * @param ret - apiデータ
 */
function initOr01533(ret: DailyTablePatternConfigInitSelectOutEntity) {
  localOneway.mo00040OnewayOne.items = ret.data.titleList
  if (ret.data.titleList && ret.data.titleList.length > 0 && (isUndefined(local.mo00040One.modelValue) || local.mo00040One.modelValue === '')) {
    local.mo00040One.modelValue = ret.data.titleList[0].day1Id
    local.mo00040OneOld = ret.data.titleList[0].day1Id
    local.titlelist = ret.data.titleList
  }
  localOneway.mo00040OnewayTwo.items = ret.data.groupList
  local.or01533.tokkiKnjInfo = ret.data.tokkiKnjInfo
  local.or01533.naiyoList = ret.data.naiyoList
  local.or01533.dailyList = ret.data.dailyList
  local.or01533.svList = ret.data.svList
  local.or01533.svDailyList = ret.data.svDailyList
  local.or01533.svCaregiveList = ret.data.svCaregiveList
  local.or01533.svEntrustedList = ret.data.svEntrustedList
  // グループ情報リスト
  // タイトル情報リスト
  local.or01533.dailyInfoList = [];
  // 「内容情報」リスト
  ret.data.dailyList.forEach((item) => {
    local.or01533.dailyInfoList.push({
      // カウンタ
      id: item.id,
      uuId: uuidv4(),
      // 開始時間
      startTime: item.startTime,
      // 終了時間
      endTime: item.endTime,
      // 区分
      dataKbn: item.dataKbn,
      // 内容CD
      naiyoCd: item.naiyoCd ,
      // 内容
      naiyoKnj: item.naiyoKnj ,
      // 日常処遇サービスメモ
      memoKnj:  item.memoKnj ,
      // 担当者
      tantoKnj: item.tantoKnj ,
      // 文字サイズ
      fontSize: item.fontSize ,
      // 文字サイズ
      fontSizeTitle: item.fontSize ,
      // 文字位置
      alignment: item.alignment ,
      // 文字カラー
      fontColor: convertDecimalToHex(Number(item.fontColor)),
      // 背景カラー
      backColor: convertDecimalToHex(Number(item.backColor)),
      // 時間表示区分
      timeKbn:  item.timeKbn ,
      /** 内容担当更新区分 */
      updateKbn: '',
      /** 更新回数 */
      modifiedCnt: item.modifiedCnt,
      zIndex:1000,
    })
  })
  if (local.or01533.tokkiKnjInfo) {
    local.or01533.tokkiKnj = {
      tokkiKnj: local.or01533.tokkiKnjInfo.tokkiKnj,
      sonotaKnj: local.or01533.tokkiKnjInfo.sonotaKnj,
      kaiteiFlg: local.or01533.tokkiKnjInfo.kaiteiFlg,
      modifiedCnt : local.or01533.tokkiKnjInfo.modifiedCnt,
    } as TokkiKnjInfo
  }
  local.or01533.svInfoList = [];
  if (ret.data.svList) {
    ret.data.svList.forEach((item) => {
      local.or01533.svInfoList.push({
        svId: item.svId,
        dataKbn: item.dataKbn,
        tekiyoFlg: item.tekiyoFlg,
        modifiedCnt: item.modifiedCnt,
        updateKbn: ''
      })
    })
  }
}

/**
 * 日常生活活動等 initData
 *
 */
function initGui00991() {
  //  表示用「計画対象期間」情報.ページング区分
  localOneway.or28528Oneway.pagingFlg = Or01533Const.OPERA_FLG_0
  // 文字サイズ
  localOneway.or28528Oneway.letterSize = localOneway.letterSize

  // 文字位置
  localOneway.or28528Oneway.letterPosition = localOneway.letterPosition
  // 文字サイズ
  localOneway.or28528Oneway.timeDisplay = localOneway.letterPosition
  // 初期設定マスタの情報
  localOneway.or28528Oneway.initMasterObj = {
    pdayTimeFlg: '1',
    cpnFlg: '',
    keishoFlg: '',
    keishoKnj:''
  }
  initGui00991TableData();
}

function initGui00991TableData() {
  // 「内容情報」リスト
  localOneway.or28528Oneway.naiyoList = []
  local.or01533.naiyoList
    .filter((item) => item.dataKbn === Or01533Const.DATA_KBN_DAILY_LIFE)
    .forEach((item) => {
      localOneway.or28528Oneway.naiyoList.push({
        // 内容CD
        naiyoCd: item.naiyoCd,
        //区分
        dataKbn: item.dataKbn,
        // 内容
        naiyoKnj: item.naiyoKnj,
        // 表示順
        seq: item.seq,
      })
    })

  local.or28528.items = []
  local.or01533.dailyInfoList
    .filter((item) => item.dataKbn === Or01533Const.DATA_KBN_DAILY_LIFE)
    .forEach((item) => {
      local.or28528.items.push({
        // カウンタ
        id: item.uuId,
        dataId: item.id,
        // 時間帯
        time: {
          // 開始時間
          start: item.startTime,
          // 終了時間
          end: item.endTime,
          // 随時
          // TODO
          atAnyTime: {values : []} ,
        },
        // 区分
        dataKbn: Or01533Const.DATA_KBN_DAILY_LIFE,
        // 内容CD
        naiyoCd: { modelValue: item.naiyoCd },
        // 内容
        naiyoKnj: { value: item.naiyoKnj },
        // 日常処遇サービスメモ
        memoKnj: { value: item.memoKnj },
        // 担当者
        tantoKnj: { value: item.tantoKnj },
        // 文字サイズ
        fontSize: { modelValue: item.fontSize },
        // 文字サイズ
        fontSizeTitle: { value: item.fontSize },
        // 文字位置
        alignment: { modelValue: item.alignment },
        // 文字カラー
        fontColor: item.fontColor,
        // 背景カラー
        backColor: item.backColor,
        // 時間表示区分
        timeKbn: { modelValue: item.timeKbn },
        /** 内容担当更新区分 */
        updateKbn: '',
        /** 更新回数 */
        modifiedCnt: item.modifiedCnt,
      })
    })
}
/**
 * 日常生活活動等変更の監視
 */
watch(
  () => Or28528Logic.data.get(or28528_1.value.uniqueCpId),
  (newValue) => {
    if (!newValue) {
      return
    }
    newValue.items.forEach((item) => {
      const oldData = local.or01533.dailyInfoList.find((newItem) => newItem.uuId === item.id)
      if (oldData) {
        // 開始時間
        oldData.startTime = item.time.start
        // 終了時間
        oldData.endTime = item.time.end
        // 内容CD
        oldData.naiyoCd = item.naiyoCd.modelValue
        // 内容
        oldData.naiyoKnj = item.naiyoKnj.value
        // 日常処遇サービスメモ
        oldData.memoKnj = item.memoKnj.value
        // 担当者
        oldData.tantoKnj = item.tantoKnj.value
        // 文字サイズ
        oldData.fontSize = item.fontSize.modelValue
        // 文字位置
        oldData.alignment = item.alignment.modelValue
        // 文字カラー
        oldData.fontColor = item.fontColor
        // 背景カラー
        oldData.backColor = item.backColor
        // 時間表示区分
        oldData.timeKbn = item.timeKbn.modelValue
        // 更新回数
        oldData.modifiedCnt = item.modifiedCnt
      } else {
        local.or01533.dailyInfoList.push({
          // カウンタ
          id: '',
          uuId: item.id,
          // 区分
          dataKbn: Or01533Const.DATA_KBN_DAILY_LIFE,
          // 開始時間
          startTime: item.time.start,
          // 終了時間
          endTime: item.time.end,
          // 内容CD
          naiyoCd: item.naiyoCd.modelValue,
          // 内容
          naiyoKnj: item.naiyoKnj.value,
          // 日常処遇サービスメモ
          memoKnj: item.memoKnj.value,
          // 担当者
          tantoKnj: item.tantoKnj.value,
          // 文字サイズ
          fontSize: item.fontSize.modelValue,
          // 文字サイズ
          fontSizeTitle: item.fontSize.modelValue,
          // 文字位置
          alignment: item.alignment.modelValue,
          // 文字カラー
          fontColor: item.fontColor,
          // 背景カラー
          backColor: item.backColor,
          // 時間表示区分
          timeKbn: item.timeKbn.modelValue,
          // 更新回数
          modifiedCnt: '',
          // 内容担当更新区分
          updateKbn: UPDATE_KBN.CREATE,
          zIndex:1000,
        })
      }
    })
    set28153()
  }
)
/**
 * 自立支援に関する処遇 initData
 *
 */
function initGui00992() {
  // 介護(自立支援に関する処遇)サービス
  //  表示用「計画対象期間」情報.ページング区分
  localOneway.or28220Oneway.pagingFlg = Or01533Const.OPERA_FLG_0
  // 文字サイズ
  localOneway.or28220Oneway.letterSize = localOneway.letterSize
  // 文字位置
  localOneway.or28220Oneway.letterPosition = localOneway.letterPosition
  // 文字サイズ
  localOneway.or28220Oneway.timeDisplay = localOneway.letterPosition
  // 初期設定マスタの情報
  localOneway.or28220Oneway.initMasterObj = {
    pdayTimeFlg: '1',
    cpnFlg: '',
    keishoFlg: '',
    keishoKnj:''
  }
  initGui00992TableData();
}

function initGui00992TableData() {
  // 「内容情報」リスト
  localOneway.or28220Oneway.naiyoList = []
  if (local.or01533.naiyoList) {
    local.or01533.naiyoList
    .filter(
      (item) => item.dataKbn === Or01533Const.DATA_KBN_TREATMENT_RELATED_TO_SELF_RELIANCE_SUPPORT
    )
    .forEach((item) => {
      localOneway.or28220Oneway.naiyoList.push({
        // 内容CD
        naiyoCd: item.naiyoCd,
        //区分
        dataKbn: item.dataKbn,
        // 内容
        naiyoKnj: item.naiyoKnj,
        // 表示順
        seq: item.seq,
      })
    })
  }

  local.or28220.items = []
  local.or01533.dailyInfoList
    .filter(
      (item) => item.dataKbn === Or01533Const.DATA_KBN_TREATMENT_RELATED_TO_SELF_RELIANCE_SUPPORT
    )
    .forEach((item) => {
      local.or28220.items.push({
        // カウンタ
        id: item.uuId,
        dataId: item.id,
        // 時間帯
        time: {
          // 開始時間
          start: item.startTime,
          // 終了時間
          end: item.endTime,
          // 随時
          atAnyTime: {values : []} ,
        },
        // 区分
        dataKbn: Or01533Const.DATA_KBN_TREATMENT_RELATED_TO_SELF_RELIANCE_SUPPORT,
        // 内容CD
        naiyoCd: { modelValue: item.naiyoCd },
        // 内容
        naiyoKnj: { value: item.naiyoKnj },
        // 日常処遇サービスメモ
        memoKnj: { value: item.memoKnj },
        // 担当者
        tantoKnj: { value: item.tantoKnj },
        // 文字サイズ
        fontSize: { modelValue: item.fontSize },
        // 文字サイズ
        fontSizeTitle: { value: item.fontSize },
        // 文字位置
        alignment: { modelValue: item.alignment },
        // 文字カラー
        fontColor: item.fontColor,
        // 背景カラー
        backColor: item.backColor,
        // 時間表示区分
        timeKbn: { modelValue: item.timeKbn },
        /** 内容担当更新区分 */
        updateKbn: '',
        /** 更新回数 */
        modifiedCnt: item.modifiedCnt,
      })
    })
}

/**
 *  自立支援に関する処遇等変更の監視
 */
watch(
  () => Or28220Logic.data.get(or28220_1.value.uniqueCpId),
  (newValue) => {
    if (!newValue) {
      return
    }
    newValue.items.forEach((item) => {
      const oldData = local.or01533.dailyInfoList.find((newItem) => newItem.uuId === item.id)
      if (oldData) {
        // 開始時間
        oldData.startTime = item.time.start
        // 終了時間
        oldData.endTime = item.time.end
        // 内容CD
        oldData.naiyoCd = item.naiyoCd.modelValue
        // 内容
        oldData.naiyoKnj = item.naiyoKnj.value
        // 日常処遇サービスメモ
        oldData.memoKnj = item.memoKnj.value
        // 担当者
        oldData.tantoKnj = item.tantoKnj.value
        // 文字サイズ
        oldData.fontSize = item.fontSize.modelValue
        // 文字位置
        oldData.alignment = item.alignment.modelValue
        // 文字カラー
        oldData.fontColor = item.fontColor
        // 背景カラー
        oldData.backColor = item.backColor
        // 時間表示区分
        oldData.timeKbn = item.timeKbn.modelValue
        // 更新回数
        oldData.modifiedCnt = item.modifiedCnt
      } else {
        local.or01533.dailyInfoList.push({
          // カウンタ
          id: '',
          uuId: item.id,
          // 区分
          dataKbn: Or01533Const.DATA_KBN_DAILY_LIFE,
          // 開始時間
          startTime: item.time.start,
          // 終了時間
          endTime: item.time.end,
          // 内容CD
          naiyoCd: item.naiyoCd.modelValue,
          // 内容
          naiyoKnj: item.naiyoKnj.value,
          // 日常処遇サービスメモ
          memoKnj: item.memoKnj.value,
          // 担当者
          tantoKnj: item.tantoKnj.value,
          // 文字サイズ
          fontSize: item.fontSize.modelValue,
          // 文字サイズ
          fontSizeTitle: item.fontSize.modelValue,
          // 文字位置
          alignment: item.alignment.modelValue,
          // 文字カラー
          fontColor: item.fontColor,
          // 背景カラー
          backColor: item.backColor,
          // 時間表示区分
          timeKbn: item.timeKbn.modelValue,
          // 更新回数
          modifiedCnt: '',
          // 内容担当更新区分
          updateKbn: UPDATE_KBN.CREATE,
          zIndex:1000,
        })
      }
    })
    set28153()
  }
)

/**
 * 介護(介護予防)サービス initData
 *
 */
function initGui00993() {
  // 介護(介護予防)サービス
  //  表示用「計画対象期間」情報.ページング区分
  localOneway.or28221Oneway.pagingFlg = Or01533Const.OPERA_FLG_0
  // 文字サイズ
  localOneway.or28221Oneway.letterSize = localOneway.letterSize
  // 文字位置
  localOneway.or28221Oneway.letterPosition = localOneway.letterPosition
  // 文字サイズ
  localOneway.or28221Oneway.timeDisplay = localOneway.letterPosition
  // 初期設定マスタの情報
  localOneway.or28221Oneway.initMasterObj = {
    pdayTimeFlg: '1',
    cpnFlg: '',
    keishoFlg: '',
    keishoKnj:''
  }

  initGui00993TableData();
}

function initGui00993TableData() {
  // 「内容情報」リスト
  localOneway.or28221Oneway.naiyoList = []
  if (local.or01533.naiyoList) {
    local.or01533.naiyoList
    .filter((item) => item.dataKbn === Or01533Const.DATA_KBN_NURSING_PREVENTIVE_CARE_SERVICES)
    .forEach((item) => {
      localOneway.or28221Oneway.naiyoList.push({
        // 内容CD
        naiyoCd: item.naiyoCd,
        //区分
        dataKbn: item.dataKbn,
        // 内容
        naiyoKnj: item.naiyoKnj,
        // 表示順
        seq: item.seq,
      })
    })
  }
  local.or28221.items = []
  local.or01533.dailyInfoList
    .filter((item) => item.dataKbn === Or01533Const.DATA_KBN_NURSING_PREVENTIVE_CARE_SERVICES)
    .forEach((item) => {
      local.or28221.items.push({
        // カウンタ
        id: item.uuId,
        dataId: item.id,
        // 時間帯
        time: {
          // 開始時間
          start: item.startTime,
          // 終了時間
          end: item.endTime,
          // 随時
          atAnyTime: {values : []} ,
        },
        // 区分
        dataKbn: Or01533Const.DATA_KBN_NURSING_PREVENTIVE_CARE_SERVICES,
        // 内容CD
        naiyoCd: { modelValue: item.naiyoCd },
        // 内容
        naiyoKnj: { value: item.naiyoKnj },
        // 日常処遇サービスメモ
        memoKnj: { value: item.memoKnj },
        // 担当者
        tantoKnj: { value: item.tantoKnj },
        // 文字サイズ
        fontSize: { modelValue: item.fontSize },
        // 文字サイズ
        fontSizeTitle: { value: item.fontSize },
        // 文字位置
        alignment: { modelValue: item.alignment },
        // 文字カラー
        fontColor: item.fontColor,
        // 背景カラー
        backColor: item.backColor,
        // 時間表示区分
        timeKbn: { modelValue: item.timeKbn },
        /** 内容担当更新区分 */
        updateKbn: '',
        /** 更新回数 */
        modifiedCnt: item.modifiedCnt,
      })
    })
}

/**
 * 介護(介護予防)サービスの監視
 */
watch(
  () => Or28221Logic.data.get(or28221_1.value.uniqueCpId),
  (newValue) => {
    if (!newValue) {
      return
    }
    newValue.items.forEach((item) => {
      const oldData = local.or01533.dailyInfoList.find((newItem) => newItem.uuId === item.id)
      if (oldData && oldData) {
        // 開始時間
        oldData.startTime = item.time.start
        // 終了時間
        oldData.endTime = item.time.end
        // 内容CD
        oldData.naiyoCd = item.naiyoCd.modelValue
        // 内容
        oldData.naiyoKnj = item.naiyoKnj.value
        // 日常処遇サービスメモ
        oldData.memoKnj = item.memoKnj.value
        // 担当者
        oldData.tantoKnj = item.tantoKnj.value
        // 文字サイズ
        oldData.fontSize = item.fontSize.modelValue
        // 文字位置
        oldData.alignment = item.alignment.modelValue
        // 文字カラー
        oldData.fontColor = item.fontColor
        // 背景カラー
        oldData.backColor = item.backColor
        // 時間表示区分
        oldData.timeKbn = item.timeKbn.modelValue
        // 更新回数
        oldData.modifiedCnt = item.modifiedCnt
      } else {
        local.or01533.dailyInfoList.push({
          // カウンタ
          id: '',
          uuId: item.id,
          // 区分
          dataKbn: Or01533Const.DATA_KBN_DAILY_LIFE,
          // 開始時間
          startTime: item.time.start,
          // 終了時間
          endTime: item.time.end,
          // 内容CD
          naiyoCd: item.naiyoCd.modelValue,
          // 内容
          naiyoKnj: item.naiyoKnj.value,
          // 日常処遇サービスメモ
          memoKnj: item.memoKnj.value,
          // 担当者
          tantoKnj: item.tantoKnj.value,
          // 文字サイズ
          fontSize: item.fontSize.modelValue,
          // 文字サイズ
          fontSizeTitle: item.fontSize.modelValue,
          // 文字位置
          alignment: item.alignment.modelValue,
          // 文字カラー
          fontColor: item.fontColor,
          // 背景カラー
          backColor: item.backColor,
          // 時間表示区分
          timeKbn: item.timeKbn.modelValue,
          // 更新回数
          modifiedCnt: '',
          // 内容担当更新区分
          updateKbn: UPDATE_KBN.CREATE,
          zIndex:1000,
        })
      }
    })
    set28153()
  }
)

function initGui00994() {
  localOneway.orX0114Oneway.initMasterObj = {cpnFlg:''}
  if (local.or01533.tokkiKnj) {
    local.orX0114.tokkiKnj = { value: local.or01533.tokkiKnj.tokkiKnj }
    local.orX0114.sonotaKnj =  { value: local.or01533.tokkiKnj.sonotaKnj }
  }
  local.orX0114.svList = getSvList()
}
/**
 * 「日課表サービス例」リストを取得
 */
function getSvList() {
  const svList: SvType[] = []
  const list = [
    ...local.or01533.svDailyList,
    ...local.or01533.svCaregiveList,
    ...local.or01533.svEntrustedList,
  ]
  for (const element of list) {
    const data = local.or01533.svList
      .filter((x) => x.dataKbn === element.dataKbn && x.svId === element.svId)
      ?.at(0)
    svList.push({
      svId: element.svId,
      dataKbn: element.dataKbn,
      mo00018: {
        modelValue: data?.tekiyoFlg === '1',
      },
      mo00018Oneway: {
        checkboxLabel: element.svKnj,
        showItemLabel: true,
        isVerticalLabel: false,
        customClass: new CustomClass({
          outerClass: '',
          labelClass: 'ma-1',
        }),
      },
      modifiedCnt: data? data.modifiedCnt : '',
      updateKbn: data ? UPDATE_KBN.NONE : UPDATE_KBN.CREATE
    })
  }
  return svList
}

/**
 * 日課表サービス例の監視
 */
 watch(
  () => OrX0114Logic.data.get(orX0114_1.value.uniqueCpId)?.tokkiKnj,
  (newValue) => {
    if (!newValue) {
      return
    }
    local.or01533.tokkiKnj.tokkiKnj = newValue.value
    local.or01533.tokkiKnj.updateKbn = UPDATE_KBN.UPDATE
  }
)
/**
 * 日課表サービス例の監視
 */
 watch(
  () => OrX0114Logic.data.get(orX0114_1.value.uniqueCpId)?.sonotaKnj,
   (newValue) => {
    if (!newValue) {
      return
    }
    local.or01533.tokkiKnj.sonotaKnj = newValue.value
    local.or01533.tokkiKnj.updateKbn = UPDATE_KBN.UPDATE
  }
)
/**
 * 日課表サービス例の監視
 */
 watch(
  () => OrX0114Logic.data.get(orX0114_1.value.uniqueCpId)?.svList,
   (newValue) => {
    if (!newValue) {
      return
    }
    for (const element of newValue) {
      const oldData = local.or01533.svInfoList
        .filter((x) => x.dataKbn === element.dataKbn && x.svId === element.svId)
        ?.at(0)
      if (!oldData) {
        local.or01533.svInfoList.push({
          svId: element.svId,
          dataKbn: element.dataKbn,
          tekiyoFlg: element.mo00018.modelValue ? '1' : '0',
          modifiedCnt: element.modifiedCnt,
          updateKbn: UPDATE_KBN.CREATE
        })
      } else {
        const tekiyoFlg = element.mo00018.modelValue ? '1' : '0'
        if (tekiyoFlg !== oldData.tekiyoFlg && oldData.updateKbn !== UPDATE_KBN.CREATE) {
          oldData.tekiyoFlg = tekiyoFlg
          oldData.updateKbn = UPDATE_KBN.UPDATE
        } else if (tekiyoFlg !== oldData.tekiyoFlg && oldData.updateKbn === UPDATE_KBN.CREATE) {
          oldData.tekiyoFlg = tekiyoFlg
        }
      }
    }
    local.or01533.tokkiKnj.updateKbn = UPDATE_KBN.UPDATE
  }
)

/**
 * AC030_カレンダーをダブルクリック
 *
 * @param data - ダブルクリックイベントの戻りパラメータ
 */
function handleDoubleClick(data: DblClickResult) {
  local.clickEdUUid = ''
  if (isUndefined(data.rowIndex) || data.rowIndex < 0) {
    const event = data.event
    if (!event) {
      return
    }
    const dailyData = local.or01533.dailyInfoList.find((newItem) => newItem.uuId === event.customId)
    if (!dailyData) {
      return
    }
    const startTime = new Date(event.segmentStart)
    const endTime = new Date(event.segmentEnd)
    localOneway.or27539Oneway = {
      // 区分
      category: dailyData.dataKbn,
      // 内容(ボタン)
      contentsButton: '',
      // 担当(ボタン)
      chargeButton: '',
      // 開始時間
      startTime: `${startTime.getHours().toString().padStart(2, '0')}:${startTime.getMinutes().toString().padStart(2, '0')}`,
      // 終了時間
      endTime: `${endTime.getHours().toString().padStart(2, '0')}:${endTime.getMinutes().toString().padStart(2, '0')}`,
      // 日常処遇サービスメモ
      memoKnj: dailyData.memoKnj,
      // 担当者
      tantoKnj: dailyData.tantoKnj,
      // 文字サイズ
      fontSize: dailyData.fontSize,
      // 文字位置
      alignment: dailyData.alignment,
      // 文字色
      fontColor: dailyData.fontColor,
      // 背景カラー
      backColor: dailyData.backColor,
      // 時間表示区分
      timeKbn: dailyData.timeKbn,
      // 内容
      contents: dailyData.naiyoCd,
    }
    local.clickEdUUid = dailyData.uuId
  } else {
    localOneway.or27539Oneway = {
      // 区分
      category:
        data.lineIndex === 0
          ? Or01533Const.DATA_KBN_DAILY_LIFE
          : data.lineIndex === 1 || data.lineIndex === 2
            ? Or01533Const.DATA_KBN_TREATMENT_RELATED_TO_SELF_RELIANCE_SUPPORT
            : Or01533Const.DATA_KBN_NURSING_PREVENTIVE_CARE_SERVICES,
      // 内容(ボタン)
      contentsButton:'',
      // 担当(ボタン)
      chargeButton:'',
      // 開始時間
      startTime: Or01533Const.FIRST_SLOTS[data.rowIndex].time,
      // 終了時間
      endTime: Or01533Const.FIRST_SLOTS[data.rowIndex + 1].time,
      // 日常処遇サービスメモ
      memoKnj: '',
      // 担当者
      tantoKnj:'',
      // 文字サイズ
      fontSize: Or01533Const.FONT_SIZE,
      // 文字位置
      alignment: Or01533Const.ALIGNMENT,
      // 文字色
      fontColor: Or01533Const.FONT_COLOR,
      // 背景カラー
      backColor:  Or01533Const.BACK_COLOR,
      // 時間表示区分
      timeKbn: '1',
      // 内容
      contents: '1',
    }
  }

    if (data.lineIndex === 0) {
      localOneway.or27539Oneway.contentsButton = '日常生活活動等'
    } else if (data.lineIndex === 1 || data.lineIndex === 2) {
      localOneway.or27539Oneway.contentsButton = '自立支援に関する「改行」処遇'
      localOneway.or27539Oneway.chargeButton = '提供担当者「改行」(職種)'
    } else if (data.lineIndex === 3 || data.lineIndex === 4) {
      localOneway.or27539Oneway.contentsButton = '介護(介護予防)「改行」サービス'
      localOneway.or27539Oneway.chargeButton = '提供担当者(職種)「改行」(受託居宅サービス)'
    }
    Or27539Logic.state.set({
      uniqueCpId: or27539_1.value.uniqueCpId,
      state: { isOpen: true },
    })
}
/**
 * 日課情報オブジェクトドラッグ＆ドロップ
 *
 * @param event - 日課情報オブジェクトドラッグ＆ドロップ
 */
function handelMousedown(event: OrX0099SegmentedEvent) {
  const or01533Data = local.or01533.dailyInfoList.find((item) => item.uuId === event.customId)
  const startTime = new Date(event.start)
  const endTime = new Date(event.end)
  if (or01533Data) {
    or01533Data.startTime = `${startTime.getHours().toString().padStart(2, '0')}:${startTime.getMinutes().toString().padStart(2, '0')}`
    or01533Data.endTime = `${endTime.getHours().toString().padStart(2, '0')}:${endTime.getMinutes().toString().padStart(2, '0')}`
    or01533Data.zIndex = event.zIndex ?? 1000
  }

  if (event.headerIndex === 0) {
    const uuIddata = local.or28528.items.find((item) => item.id === event.customId)
    if (uuIddata) {
      uuIddata.time = {
        // 開始時間
        start: or01533Data ? or01533Data.startTime : '',
        // 終了時間
        end:  or01533Data ? or01533Data.endTime: '',
        // 随時
        atAnyTime: {values : []},
      }
    }

    Or28528Logic.data.set({
      uniqueCpId: or28528_1.value.uniqueCpId,
      value: {
        items:local.or28528.items,
      },
    })
  } else if (event.headerIndex === 1 || event.headerIndex === 2) {
    const uuIddata = local.or28220.items.find((item) => item.id === event.customId)
    if (uuIddata) {
      uuIddata.time = {
        // 開始時間
        start:  or01533Data ? or01533Data.startTime: '',
        // 終了時間
        end:  or01533Data ? or01533Data.endTime: '',
        // 随時
        atAnyTime: {values : []},
      }
    }

    Or28220Logic.data.set({
      uniqueCpId: or28220_1.value.uniqueCpId,
      value: {
        items:local.or28220.items,
      },
    })
  } else {
    const uuIddata = local.or28221.items.find((item) => item.id === event.customId)
    if (uuIddata) {
      uuIddata.time = {
        // 開始時間
        start:  or01533Data ? or01533Data.startTime: '',
        // 終了時間
        end:  or01533Data ? or01533Data.endTime: '',
        // 随時
        atAnyTime: {values : []},
      }
    }

    Or28221Logic.data.set({
      uniqueCpId: or28221_1.value.uniqueCpId,
      value: {
        items:local.or28221.items,
      },
    })
  }
}
/**
 * 日課表入力の監視
 */
watch(
  () => local.or27539,
  (newValue) => {
    if (newValue) {
      const or01533Data = local.or01533.dailyInfoList.find((item) => item.uuId === local.clickEdUUid)
      let uuid = local.clickEdUUid
      if (or01533Data) {
        // 開始時間
        or01533Data.startTime = newValue.startTime
        // 終了時間
        or01533Data.endTime = newValue.endTime
        // 内容CD
        or01533Data.naiyoCd = newValue.contents
        // 内容
        or01533Data.memoKnj = newValue.memoKnj ? newValue.memoKnj : ''
        // 日常処遇サービスメモ
        or01533Data.tantoKnj = newValue.tantoKnj ? newValue.tantoKnj : ''
        // 担当者
        or01533Data.fontSize = newValue.fontSize
        // 文字サイズ
        or01533Data.fontSizeTitle = newValue.fontSize
        // 文字サイズ
        or01533Data.alignment = newValue.alignment
        // 文字カラー
        or01533Data.fontColor = newValue.fontColor
        // 背景カラー
        or01533Data.backColor = newValue.backColor
        // 時間表示区分
        or01533Data.timeKbn = newValue.timeKbn
        // 内容担当更新区分
        or01533Data.updateKbn = UPDATE_KBN.UPDATE
      } else {
        uuid = uuidv4()
        local.or01533.dailyInfoList.push({
          // カウンタ
          id: '',
          uuId: uuid,
          // 区分
          dataKbn: localOneway.or27539Oneway.category === '1'
              ? Or01533Const.DATA_KBN_DAILY_LIFE
              : localOneway.or27539Oneway.category === '2'
                ? Or01533Const.DATA_KBN_TREATMENT_RELATED_TO_SELF_RELIANCE_SUPPORT
                : Or01533Const.DATA_KBN_NURSING_PREVENTIVE_CARE_SERVICES,
          // 開始時間
          startTime: newValue.startTime,
          // 終了時間
          endTime: newValue.endTime,
          // 内容CD
          naiyoCd: newValue.contents,
          // 内容
          naiyoKnj: '',
          // 日常処遇サービスメモ
          memoKnj: newValue.memoKnj ? newValue.memoKnj : '',
          // 担当者
          tantoKnj: newValue.tantoKnj ? newValue.tantoKnj : '',
          // 文字サイズ
          fontSize: newValue.fontSize,
          // 文字サイズ
          fontSizeTitle: newValue.fontSize,
          // 文字位置
          alignment: newValue.alignment,
          // 文字カラー
          fontColor: newValue.fontColor,
          // 背景カラー
          backColor: newValue.backColor,
          // 時間表示区分
          timeKbn: newValue.timeKbn,
          // 内容担当更新区分
          updateKbn:UPDATE_KBN.CREATE,
          // 更新回数
          modifiedCnt: '',
          zIndex:1000,
        })
      }
      if (localOneway.or27539Oneway.category === '1') {
        const or28528Old = local.or28528.items.find((item) => item.id === uuid)
        if (or28528Old) {
          // 開始時間
          or28528Old.time = {
              // 開始時間
              start:  newValue.startTime,
              // 終了時間
              end: newValue.endTime,
              // 随時
              atAnyTime: {values : []},
          }
          // 内容CD
          or28528Old.naiyoCd = { modelValue: newValue.contents }
          or28528Old.naiyoKnj = { value: '' }
          // 内容
          or28528Old.memoKnj = { value: newValue.memoKnj ? newValue.memoKnj : '' }
          // 日常処遇サービスメモ
          or28528Old.tantoKnj = { value: newValue.tantoKnj ? newValue.tantoKnj : '' }
          // 担当者
          or28528Old.fontSize = { modelValue: newValue.fontSize }
          // 文字サイズ
          or28528Old.fontSizeTitle = { value: newValue.fontSize }
          // 文字サイズ
          or28528Old.alignment = { modelValue: newValue.alignment }
          // 文字カラー
          or28528Old.fontColor = newValue.fontColor
          // 背景カラー
          or28528Old.backColor = newValue.backColor
          // 時間表示区分
          or28528Old.timeKbn = { modelValue: newValue.timeKbn }
          // 内容担当更新区分
          or28528Old.updateKbn = UPDATE_KBN.UPDATE
        } else {
          local.or28528.items.push({
            // カウンタ
            id: uuid,
            dataId: '',
            // 時間帯
            time: {
              // 開始時間
              start:  newValue.startTime,
              // 終了時間
              end: newValue.endTime,
              // 随時
              // TODO
              atAnyTime: {values : []},
            },
            // 区分
            dataKbn: Or01533Const.DATA_KBN_DAILY_LIFE,
            // 内容CD
            naiyoCd: { modelValue: newValue.contents },
            // 内容
            naiyoKnj: { value: '' },
            // 日常処遇サービスメモ
            memoKnj: { value: newValue.memoKnj ? newValue.memoKnj : '' },
            // 担当者
            tantoKnj: { value: newValue.tantoKnj ? newValue.tantoKnj : '' },
            // 文字サイズ
            fontSize: { modelValue: newValue.fontSize },
            // 文字サイズ
            fontSizeTitle: { value: newValue.fontSize },
            // 文字位置
            alignment: { modelValue: newValue.alignment },
            // 文字カラー
            fontColor: newValue.fontColor,
            // 背景カラー
            backColor: newValue.backColor,
            // 時間表示区分
            timeKbn: { modelValue: newValue.timeKbn },
            /** 内容担当更新区分 */
            updateKbn: UPDATE_KBN.CREATE,
            /** 更新回数 */
            modifiedCnt: '',
          })
        }
        Or28528Logic.data.set({
          uniqueCpId: or28528_1.value.uniqueCpId,
          value: {
            items:local.or28528.items,
          },
        })
      } else if (localOneway.or27539Oneway.category === '2') {
        const or28220Old = local.or28220.items.find((item) => item.id === uuid)
        if (or28220Old) {
          // 開始時間
          or28220Old.time = {
            // 開始時間
            start: newValue.startTime,
            // 終了時間
            end: newValue.endTime,
            // 随時
            atAnyTime: { values: [] },
          }
          // 内容CD
          or28220Old.naiyoCd = { modelValue: newValue.contents }
          or28220Old.naiyoKnj = { value: '' }
          // 内容
          or28220Old.memoKnj = { value: newValue.memoKnj ? newValue.memoKnj : '' }
          // 日常処遇サービスメモ
          or28220Old.tantoKnj = { value: newValue.tantoKnj ? newValue.tantoKnj : '' }
          // 担当者
          or28220Old.fontSize = { modelValue: newValue.fontSize }
          // 文字サイズ
          or28220Old.fontSizeTitle = { value: newValue.fontSize }
          // 文字サイズ
          or28220Old.alignment = { modelValue: newValue.alignment }
          // 文字カラー
          or28220Old.fontColor = newValue.fontColor
          // 背景カラー
          or28220Old.backColor = newValue.backColor
          // 時間表示区分
          or28220Old.timeKbn = { modelValue: newValue.timeKbn }
          // 内容担当更新区分
          or28220Old.updateKbn = UPDATE_KBN.UPDATE
        } else {
          local.or28220.items.push({
            // カウンタ
            id: uuid,
            dataId: '',
            // 時間帯
            time: {
              // 開始時間
              start: newValue.startTime,
              // 終了時間
              end: newValue.endTime,
              // 随時
              // TODO
              atAnyTime: { values: [] },
            },
            // 区分
            dataKbn: Or01533Const.DATA_KBN_TREATMENT_RELATED_TO_SELF_RELIANCE_SUPPORT,
            // 内容CD
            naiyoCd: { modelValue: newValue.contents },
            // 内容
            naiyoKnj: { value: '' },
            // 日常処遇サービスメモ
            memoKnj: { value: newValue.memoKnj ? newValue.memoKnj : '' },
            // 担当者
            tantoKnj: { value: newValue.tantoKnj ? newValue.tantoKnj : '' },
            // 文字サイズ
            fontSize: { modelValue: newValue.fontSize },
            // 文字サイズ
            fontSizeTitle: { value: newValue.fontSize },
            // 文字位置
            alignment: { modelValue: newValue.alignment },
            // 文字カラー
            fontColor: newValue.fontColor,
            // 背景カラー
            backColor: newValue.backColor,
            // 時間表示区分
            timeKbn: { modelValue: newValue.timeKbn },
            /** 内容担当更新区分 */
            updateKbn: UPDATE_KBN.CREATE,
            /** 更新回数 */
            modifiedCnt: '',
          })
        }
        Or28220Logic.data.set({
          uniqueCpId: or28220_1.value.uniqueCpId,
          value: {
            items:local.or28220.items,
          },
        })
      } else {
        const or28221Old = local.or28221.items.find((item) => item.id === uuid)
        if (or28221Old) {
          // 開始時間
          or28221Old.time = {
            // 開始時間
            start: newValue.startTime,
            // 終了時間
            end: newValue.endTime,
            // 随時
            atAnyTime: { values: [] },
          }
          // 内容CD
          or28221Old.naiyoCd = { modelValue: newValue.contents }
          or28221Old.naiyoKnj = { value: '' }
          // 内容
          or28221Old.memoKnj = { value: newValue.memoKnj ? newValue.memoKnj : '' }
          // 日常処遇サービスメモ
          or28221Old.tantoKnj = { value: newValue.tantoKnj ? newValue.tantoKnj : '' }
          // 担当者
          or28221Old.fontSize = { modelValue: newValue.fontSize }
          // 文字サイズ
          or28221Old.fontSizeTitle = { value: newValue.fontSize }
          // 文字サイズ
          or28221Old.alignment = { modelValue: newValue.alignment }
          // 文字カラー
          or28221Old.fontColor = newValue.fontColor
          // 背景カラー
          or28221Old.backColor = newValue.backColor
          // 時間表示区分
          or28221Old.timeKbn = { modelValue: newValue.timeKbn }
          // 内容担当更新区分
          or28221Old.updateKbn = UPDATE_KBN.UPDATE
        } else {
          local.or28221.items.push({
            // カウンタ
            id: uuid,
            dataId: '',
            // 時間帯
            time: {
              // 開始時間
              start:  newValue.startTime,
              // 終了時間
              end: newValue.endTime,
              // 随時
              // TODO
              atAnyTime: {values : []},
            },
            // 区分
            dataKbn: Or01533Const.DATA_KBN_NURSING_PREVENTIVE_CARE_SERVICES,
            // 内容CD
            naiyoCd: { modelValue: newValue.contents },
            // 内容
            naiyoKnj: { value: '' },
            // 日常処遇サービスメモ
            memoKnj: { value: newValue.memoKnj ? newValue.memoKnj : '' },
            // 担当者
            tantoKnj: { value: newValue.tantoKnj ? newValue.tantoKnj : '' },
            // 文字サイズ
            fontSize: { modelValue: newValue.fontSize },
            // 文字サイズ
            fontSizeTitle: { value: newValue.fontSize },
            // 文字位置
            alignment: { modelValue: newValue.alignment },
            // 文字カラー
            fontColor: newValue.fontColor,
            // 背景カラー
            backColor: newValue.backColor,
            // 時間表示区分
            timeKbn: { modelValue: newValue.timeKbn },
            /** 内容担当更新区分 */
            updateKbn: UPDATE_KBN.CREATE,
            /** 更新回数 */
            modifiedCnt: '',
          })
        }
        Or28221Logic.data.set({
          uniqueCpId: or28221_1.value.uniqueCpId,
          value: {
            items:local.or28221.items,
          },
        })
      }
      set28153()
    }
  }
)
/**
 * 取込
 */
function importClick() {
  console.log('1111111');
}

// タブ切替
watch(
  () => local.mo00043Transfer.id,
  async (newValue) => {
    if (local.mo00043.id !== newValue) {
      local.mo00043Change.id = newValue
      local.mo00043Transfer.id = local.mo00043.id
      // 日常
      if (local.mo00043.id === Or01533Const.TAB.TAB_ID_DAILY_LIFE_ACTIVITIES
        && local.or28528.items && local.or28528.items.length > 0
      ) {
        for (const dailyinfo of local.or28528.items) {
          if (!await checkTime(dailyinfo.time)) {
            return;
          }
        }
        local.mo00043.id = newValue
        local.mo00043Transfer.id = newValue
      }

      // 処遇
      if (local.mo00043.id === Or01533Const.TAB.TAB_ID_DAILY_LIFE_ACTIVITIES
        && local.or28220.items && local.or28220.items.length > 0
      ) {
        for (const dailyinfo of local.or28220.items) {
          if (!await checkTime(dailyinfo.time)) {
            return;
          }
        }
        local.mo00043.id = newValue
        local.mo00043Transfer.id = newValue
      }

      // 介護
      if (local.mo00043.id === Or01533Const.TAB.TAB_ID_DAILY_LIFE_ACTIVITIES
        && local.or28221.items && local.or28221.items.length > 0
      ) {
        for (const dailyinfo of local.or28221.items) {
          if (!await checkTime(dailyinfo.time)) {
            return;
          }
        }
        local.mo00043.id = newValue
        local.mo00043Transfer.id = newValue
      }
    }
  }
)
/**
 * 時間帯チェック処理を行う。
 *
 * @param time - 時間帯
 */
async function checkTime(time: OrX0044Type) {
  const hourStart = time.start.split(':')[0]
  const minutesStart = time.start.split(':')[1]
  const hourEnd = time.end.split(':')[0]
  const minutesEnd = time.end.split(':')[1]
  // 画面.時間帯_終了時(HH)が24以降の場合、且つ、画面.時間帯_終了分(MM)が15より後ろの場合
  // e.cmn.40734
  if (Number(hourStart) >= 24 && Number(minutesStart) >= 15) {
    await openErrorDialog(t('message.e-cmn-40734'))
    return false
  }
  // 画面.時間帯_開始時(HH)が24以降の場合、且つ、 画面.時間帯_開始分(MM)が14より後ろの場合
  // e.cmn.40733
  if (Number(hourStart) >= 24 && Number(minutesStart) >= 14) {
    await openErrorDialog(t('message.e-cmn-40733'))
    return false
  }
  // 画面.時間帯_開始時間(HHMM)が画面.時間帯_終了時間(HHMM)より後ろの場合 e.cmn.40740
  if (Number(hourStart) > Number(hourEnd)
    || (Number(hourStart) === Number(hourEnd) && Number(minutesStart) > Number(minutesEnd))) {
    await openErrorDialog(t('message.e-cmn-40740'))
    return false
  }
  // 画面.時間帯_開始時間(HHMM)が画面.時間帯_終了時間(HHMM)の場合 e.cmn.40735
    if (Number(hourStart) === Number(hourEnd) && Number(minutesStart) === Number(minutesEnd)) {
    await openErrorDialog(t('message.e-cmn-40735'))
    return false
  }
  return true;
}

/**
 * 「タイトルプルダウン」選択値変更
 */
async function mo00040OneChange() {
  if (isEdit.value) {
    const dialogResult = await openEditDialog()
    switch (dialogResult) {
      case Or01533Const.DEFAULT.DIALOG_RESULT_YES:
        await insert()
        await initData()
        local.mo00040OneOld = local.mo00040One?.modelValue ?? ''
        break
      case Or01533Const.DEFAULT.DIALOG_RESULT_NO:
        await initData()
        local.mo00040OneOld = local.mo00040One?.modelValue ?? ''
        break
      case Or01533Const.DEFAULT.DIALOG_RESULT_CANCEL:
        local.mo00040One.modelValue = local.mo00040OneOld
        break
    }
  } else {
    await initData()
    local.mo00040OneOld = local.mo00040One?.modelValue  ?? ''
  }
}

/**
 * 「グループプルダウン」選択値変更
 */
async function mo00040TwoChange() {
  if (isEdit.value) {
    const dialogResult = await openEditDialog()
    switch (dialogResult) {
      case Or01533Const.DEFAULT.DIALOG_RESULT_YES:
        await insert()
        // 選択値が空の場合
        if (isUndefined(local.mo00040Two.modelValue) || local.mo00040Two.modelValue === '') {
          localOneway.mo00040OnewayOne.items = local.titlelist
          local.mo00040One.modelValue = local.titlelist[0].day1Id
          local.mo00040OneOld = local.titlelist[0].day1Id
        } else {
          const list = local.titlelist.filter(x => x.groupCd === local.mo00040Two.modelValue)
          localOneway.mo00040OnewayOne.items = list;
          local.mo00040One.modelValue = list[0].day1Id
          local.mo00040OneOld = local.mo00040One.modelValue
        }
        await initData()
        break
      case Or01533Const.DEFAULT.DIALOG_RESULT_NO:
        if (isUndefined(local.mo00040Two.modelValue) || local.mo00040Two.modelValue === '') {
          localOneway.mo00040OnewayOne.items = local.titlelist
          local.mo00040One.modelValue = local.titlelist[0].day1Id
          local.mo00040OneOld = local.titlelist[0].day1Id
        } else {
          const list = local.titlelist.filter(x => x.groupCd === local.mo00040Two.modelValue)
          localOneway.mo00040OnewayOne.items = list;
          local.mo00040One.modelValue = list[0].day1Id
          local.mo00040OneOld = local.mo00040One.modelValue
        }
        await initData()
        break
      case Or01533Const.DEFAULT.DIALOG_RESULT_CANCEL:
        local.mo00040One.modelValue = local.mo00040OneOld
        break
    }
  } else {
    if (isUndefined(local.mo00040Two.modelValue) || local.mo00040Two.modelValue === '') {
      localOneway.mo00040OnewayOne.items = local.titlelist
      local.mo00040One.modelValue = local.titlelist[0].day1Id
      local.mo00040OneOld = local.titlelist[0].day1Id
     } else {
      const list = local.titlelist.filter(x => x.groupCd === local.mo00040Two.modelValue)
      localOneway.mo00040OnewayOne.items = list;
      local.mo00040One.modelValue = list[0].day1Id
      local.mo00040OneOld = local.mo00040One.modelValue
    }
    await initData()
  }
}

/**
 * 日課表詳細の監視
 */
watch(
  () => local.or01533.dailyInfoList,
  (newValue) => {
    if (!newValue) {
      return
    }
    set28153()
  }
)

function set28153() {
  local.or28153.dailyInfoList = local.or01533.dailyInfoList
  Or28153Logic.data.set({
    uniqueCpId: or28153_1.value.uniqueCpId,
    value: {
      dailyInfoList: local.or28153.dailyInfoList
    }
  })
}

defineExpose({
  insert,
  initData
})

// ナビゲーション制御領域のいずれかの編集フラグがON
const isEdit = computed(() => {
  return useScreenStore().isEditNavControl()
})

/**
 * ダイアログ表示フラグ
 */
const showDialogOr21814 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen ?? false
})
/**
 * ダイアログ表示フラグ
 */
const showDialogOr21813 = computed(() => {
  // Or21814のダイアログ開閉状態
  return Or21813Logic.state.get(or21813_1.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 編集破棄ダイアログ表示
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
async function openEditDialog(): Promise<string> {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_1.value.uniqueCpId)

        let result = Or01533Const.DEFAULT.DIALOG_RESULT_CANCEL

        if (event?.firstBtnClickFlg) {
          result = Or01533Const.DEFAULT.DIALOG_RESULT_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or01533Const.DEFAULT.DIALOG_RESULT_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = Or01533Const.DEFAULT.DIALOG_RESULT_CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * errorダイアログを閉じたタイミングで結果を返却
 *
 * @param msg - msg
 *
 * @returns ダイアログの選択結果（yes, no, cancel）
 */
 async function openErrorDialog(msg: string): Promise<string> {
  // 選択行削除確認ダイアログを開く
  Or21813Logic.state.set({
    uniqueCpId: or21813_1.value.uniqueCpId,
    state: {
      isOpen: true,
      // ダイアログタイトル
      dialogTitle: t('label.top-btn-title'),
      // ダイアログテキスト
      dialogText: msg,
      firstBtnType: 'blank',
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.yes'),
      thirdBtnType: 'blank',
    },
  })

  /**
   * 選択行削除確認ダイアログを閉じたタイミングで結果を返却
   *
   * @returns ダイアログの選択結果（yes, no）
   */
  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(or21813_1.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(or21813_1.value.uniqueCpId)

        let result = Or01533Const.DEFAULT.DIALOG_RESULT_YES

        if (event?.firstBtnClickFlg) {
          result = Or01533Const.DEFAULT.DIALOG_RESULT_YES
        }

        // 選択行削除確認ダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: or21813_1.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

</script>

<template>
  <c-v-sheet class="view">
    <c-v-row no-gutters class="pr-2 pt-2 justify-end">
      <c-v-col cols="auto">
        <base-mo00610
          v-bind="localOneway.mo00610OneWay"
          class="mx-2"
          @click="importClick"
        >
        </base-mo00610>
      </c-v-col>
    </c-v-row>
    <c-v-row no-gutters>
      <c-v-col cols="auto" class="pl-2">
        <base-mo00040
        v-model="local.mo00040One"
        :oneway-model-value="localOneway.mo00040OnewayOne"
        v-bind="{ ...$attrs }"
        @change="mo00040OneChange"
      />
      </c-v-col>
      <c-v-col cols="auto">
        <base-mo00040
          v-model="local.mo00040Two"
          :oneway-model-value="localOneway.mo00040OnewayTwo"
          v-bind="{ ...$attrs }"
          @change="mo00040TwoChange"
        />
      </c-v-col>
    </c-v-row>
    <c-v-row
      no-gutters
      class="content-area"
    >
      <c-v-col class="hidden-scroll pl-2">
          <base-mo00043
            v-model="local.mo00043Transfer"
            :oneway-model-value="localOneway.mo00043OneWay"
            style="width:900px"
          ></base-mo00043>
          <c-v-window
            v-model="local.mo00043.id"
            class="h-100 overflow-y-hidden"
            style="width:900px"
          >
            <c-v-window-item value="dailyTableImage">
              <g-custom-or-28153
                v-bind="or28153_1"
                @double-click="handleDoubleClick"
                @mouse-down="handelMousedown"
              ></g-custom-or-28153>
            </c-v-window-item>
            <!--日常生活活動等-->
            <c-v-window-item
              class="h-100 overflow-y-hidden"
              value="dailyLifeActivities"
            >
              <g-custom-Or28528
                v-bind="or28528_1"
                :oneway-model-value="localOneway.or28528Oneway"
                :unique-cp-id="or28528_1.uniqueCpId"
              />
            </c-v-window-item>
            <!-- 自立支援に関する処遇 -->
            <c-v-window-item
              class="h-100 overflow-y-hidden"
              value="treatmentRelatedToSelfRelianceSupport"
            >
              <g-custom-Or28220
                v-bind="or28220_1"
                :oneway-model-value="localOneway.or28220Oneway"
                :unique-cp-id="or28220_1.uniqueCpId"
              />
            </c-v-window-item>
            <!--介護(介護予防)サービス-->
            <c-v-window-item
              class="h-100 overflow-y-hidden"
              value="nursingPreventiveCareServices"
            >
              <g-custom-Or28221
                v-bind="or28221_1"
                :oneway-model-value="localOneway.or28221Oneway"
                :unique-cp-id="or28221_1.uniqueCpId"
              />
            </c-v-window-item>
            <!--GUI00994_特記事項、サービス例-->
            <c-v-window-item
              class="h-100 overflow-y-hidden"
              value="specialNotesServicesExamples"
            >
              <g-custom-or-x-0114
                :oneway-model-value="localOneway.orX0114Oneway"
                v-bind="orX0114_1"
              />
            </c-v-window-item>
          </c-v-window>
          <c-v-row no-gutters>
            <g-custom-or-27539
              v-if="showDialogOr27539"
              :key="or27539_1"
              v-bind="or27539_1"
              v-model="local.or27539"
              :unique-cp-id="or27539_1.uniqueCpId"
              :oneway-model-value="localOneway.or27539Oneway"
            />
            <!-- スロットの使用例 -->
            <g-base-or21814
              v-if="showDialogOr21814"
              v-bind="or21814_1"
            >
            </g-base-or21814>
            <!-- スロットの使用例 -->
            <g-base-or21813
              v-if="showDialogOr21813"
              v-bind="or21813_1"
            >
            </g-base-or21813>
          </c-v-row>
      </c-v-col>
    </c-v-row>
  </c-v-sheet>
</template>

<style scoped lang="scss">
.divider-class {
  border-width: thin;
  margin: 8px 0px;
}
.divider-noLine-class {
  border: none;
  margin: 32px 0px;
  border-color: white;
}
.view {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: transparent;
}
.content-area {
  display: flex;
  flex-grow: 1;
  overflow: hidden;
  height: 100%;
}

:deep(.v-sheet) {
  background-color: transparent !important;
}

</style>
