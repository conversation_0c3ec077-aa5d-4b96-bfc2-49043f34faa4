/**
 * Or00725:有機体:(内容マスタ)アクションエリア
 * GUI00935_内容マスタ
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR>
 */
export interface Or00725StateType {
  /**
   * 開閉フラグ
   */
  isOpen?: boolean
}
/**
 * ContentMasterListItem
 */
export interface ContentMasterItem {
  /**
   * id
   */
  id: string

  /**
   * CD
   */
  naiyoCd: string

  /**
   * 区分
   */
  dataKbn: string

  /**
   * 内容
   */
  naiyoKnj: { value: string }

  /**
   * 表示順
   */
  seq: { value: string }

  /**
   * 更新区分
   */
  updateKbn: string

  /**
   * 更新回数
   */
  modifiedCnt: string

  /**
   * 区分
   */
  dataKbn: number
}

/**
 * Async Function
 */
type AsyncFunction = () => Promise<boolean>
