<script setup lang="ts">
/**
 * Or36647
 * GUI01149_利用票
 *
 * @description
 * 利用票（画面コンポーネント）
 *
 * <AUTHOR> 董永強
 */
import { nextTick, onMounted, reactive, ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { isEmpty } from 'lodash'
import { OrX0004Const } from '../OrX0004/OrX0004.constants'
import { OrX0005Const } from '../OrX0005/OrX0005.constants'
import { OrX0014Const } from '../OrX0014/OrX0014.constants'
import { OrX0042Const } from '../OrX0042/OrX0042.constants'
import { Or27635Const } from '../Or27635/Or27635.constants'
import { Or27635Logic } from '../Or27635/Or27635.logic'
import { Or36647Const } from './Or36647.constants'
import type {
  DayOfWeek,
  UsingSlipDetailInfo,
  UsingSlipOthrtTable,
  Using<PERSON>lipSyafuku,
  Using<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TimeModelValue,
} from './Or36647.type'
import { Or21828Const } from '~/components/base-components/organisms/Or21828/Or21828.constants'
import { Or21830Const } from '~/components/base-components/organisms/Or21830/Or21830.constants'
import { Or00248Const } from '~/components/base-components/organisms/Or00248/Or00248.constants'
import { OrHeadLineConst } from '~/components/base-components/organisms/OrHeadLine/OrHeadLine.constants'
import { useScreenTwoWayBind, useSetupChildProps } from '~/composables/useComponentVue'
import type { OrHeadLineType } from '~/components/base-components/organisms/OrHeadLine/OrHeadLine.type'
import { Or00249Const } from '~/components/base-components/organisms/Or00249/Or00249.constants'
import { useScreenStore, useSystemCommonsStore } from '#imports'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo00043OnewayType, Mo00043Type } from '~/types/business/components/Mo00043Type'
import type { Mo00020OnewayType } from '~/types/business/components/Mo00020Type'
import type { Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import type { Mo00039OnewayType } from '~/types/cmn/business/components/Mo00039Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import type { Mo00009OnewayType } from '~/types/business/components/Mo00009Type'
import { CustomClass } from '~/types/CustomClassType'
import type { Mo00037OnewayType } from '~/types/business/components/Mo00037Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import { Or21735Const } from '~/components/base-components/organisms/Or21735/Or21735.constants'
import { Or21737Const } from '~/components/base-components/organisms/Or21737/Or21737.constants'
import { Or21738Const } from '~/components/base-components/organisms/Or21738/Or21738.constants'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Or10923OnewayType } from '~/types/cmn/business/components/Or10923Type'
import { Or10923Logic } from '~/components/custom-components/organisms/Or10923/Or10923.logic'
import { Or10923Const } from '~/components/custom-components/organisms/Or10923/Or10923.constants'
import type {
  UseSlipInitInfoSelectInEntity,
  UseSlipInitInfoSelectOutEntity,
  RiyouInfo,
  Kohi,
  SumData,
  KbnShikyuuGendoData,
  SyuruiGendoData,
  RiyouBeppyo,
  RiyourshaData,
  Riyou,
  Riyouryou,
  Syafuku,
} from '~/repositories/cmn/entities/UseSlipInitInfoSelectEntity'
import type { Mo01274OnewayType } from '~/types/business/components/Mo01274Type'
import type { Mo01282OnewayType } from '~/types/business/components/Mo01282Type'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import type { Mo01278OnewayType } from '~/types/business/components/Mo01278Type'
import type { NavControlJsonType } from '~/types/business/core/DefinitionJsonType'
import { hasViewAuth, hasRegistAuth } from '~/utils/useCmnAuthz'
import type {
  UseSlipInfoUpdateBefCheckInfoSelectInEntity,
  UseSlipInfoUpdateBefCheckInfoSelectOutEntity,
} from '~/repositories/cmn/entities/UseSlipInfoUpdateBefCheckInfoSelectEntity'
import type {
  Riyou702Info,
  TaihiData,
  UseSlipInfoUpdateInEntity,
  UseSlipInfoUpdateOutEntity,
} from '~/repositories/cmn/entities/UseSlipInfoUpdateEntity'
import type { Or36647OnewayType, Or36647Type } from '~/types/cmn/business/components/Or36647Type'
import type {
  UseSlipInfoRecalculationSelectInEntity,
  UseSlipInfoRecalculationSelectOutEntity,
} from '~/repositories/cmn/entities/UseSlipInfoRecalculationSelectEntity'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import type { Or26817OnewayType } from '~/types/cmn/business/components/Or26817Type'
import { Or26817Const } from '~/components/custom-components/organisms/Or26817/Or26817.constants'
import { Or27736Const } from '~/components/custom-components/organisms/Or27736/Or27736.constants'
import { Or07212Const } from '~/components/custom-components/organisms/Or07212/Or07212.constants'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or26828Const } from '~/components/custom-components/organisms/Or26828/Or26828.constants'
import { Or30707Const } from '~/components/custom-components/organisms/Or30707/Or30707.constants'
import type { Or26828OnewayType } from '~/types/cmn/business/components/Or26828Type'
import type { Or30707OnewayType } from '~/types/cmn/business/components/Or30707Type'
import type { Or27736OnewayType } from '~/types/cmn/business/components/Or27736Type'
import type { Or07212OnewayType } from '~/types/cmn/business/components/Or07212Type'
import { Or26817Logic } from '~/components/custom-components/organisms/Or26817/Or26817.logic'
import { Or26828Logic } from '~/components/custom-components/organisms/Or26828/Or26828.logic'
import { Or30707Logic } from '~/components/custom-components/organisms/Or30707/Or30707.logic'
import { Or27736Logic } from '~/components/custom-components/organisms/Or27736/Or27736.logic'
import { Or07212Logic } from '~/components/custom-components/organisms/Or07212/Or07212.logic'
import { Or21735Logic } from '~/components/base-components/organisms/Or21735/Or21735.logic'
import { Or21737Logic } from '~/components/base-components/organisms/Or21737/Or21737.logic'
import { Or21738Logic } from '~/components/base-components/organisms/Or21738/Or21738.logic'
import type {
  UseSlipInfoDeleteInEntity,
  UseSlipInfoDeleteOutEntity,
} from '~/repositories/cmn/entities/UseSlipInfoDeleteEntity'
import type {
  UseSlipInfoDeleteRowAftProcSelectInEntity,
  UseSlipInfoDeleteRowAftProcSelectOutEntity,
} from '~/repositories/cmn/entities/UseSlipInfoDeleteRowAftProcSelectEntity'
import type { Or27635OnewayType } from '~/types/cmn/business/components/Or27635Type'
import type {
  UseSlipInfoDeleteBefSelectInEntity,
  UseSlipInfoDeleteBefSelectOutEntity,
} from '~/repositories/cmn/entities/UseSlipInfoDeleteBefSelectEntity'
import { Or35687Const } from '~/components/custom-components/organisms/Or35687/Or35687.constants'
import { Or35687Logic } from '~/components/custom-components/organisms/Or35687/Or35687.logic'
import type { Or35687OnewayType } from '~/types/cmn/business/components/Or35687Type'
import type {
  UseSlipInfoPredictionChangeRowAftProcSelectInEntity,
  UseSlipInfoPredictionChangeRowAftProcSelectOutEntity,
} from '~/repositories/cmn/entities/UseSlipInfoPredictionChangeRowAftProcSelectEntity'
import { Or27016Const } from '~/components/custom-components/organisms/Or27016/Or27016.constants'
import { Or27016Logic } from '~/components/custom-components/organisms/Or27016/Or27016.logic'
import type { MeisaiList, Or27016OnewayType } from '~/types/cmn/business/components/Or27016Type'
import type {
  UseSlipInfoNumberOfTimesModifiedBefCheckSelectInEntity,
  UseSlipInfoNumberOfTimesModifiedBefCheckSelectOutEntity,
} from '~/repositories/cmn/entities/UseSlipInfoNumberOfTimesModifiedBefCheckSelectEntity'
import type {
  UseSlipInfoNumberOfTimesModifiedBefSelectInEntity,
  UseSlipInfoNumberOfTimesModifiedBefSelectOutEntity,
} from '~/repositories/cmn/entities/UseSlipInfoNumberOfTimesModifiedBefSelectEntity'
import { Or35745Const } from '~/components/custom-components/organisms/Or35745/Or35745.constants'
import { Or35745Logic } from '~/components/custom-components/organisms/Or35745/Or35745.logic'
import { Or00233Const } from '~/components/custom-components/organisms/Or00233/Or00233.constants'
import { Or00233Logic } from '~/components/custom-components/organisms/Or00233/Or00233.logic'
import type { Or00233SelectInfoType } from '~/types/cmn/business/components/Or00233Type'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or27943Const } from '~/components/custom-components/organisms/Or27943/Or27943.constants'
import { Or27943Logic } from '~/components/custom-components/organisms/Or27943/Or27943.logic'
import type { Or27943OnewayType } from '~/types/cmn/business/components/Or27943Type'
import type {
  UseSlipInfoWeekImportSelectInEntity,
  UseSlipInfoWeekImportSelectOutEntity,
} from '~/repositories/cmn/entities/UseSlipInfoWeekImportSelectEntity'
import type {
  UseSlipInfoHopeBurdenSelectInEntity,
  UseSlipInfoHopeBurdenSelectOutEntity,
} from '~/repositories/cmn/entities/UseSlipInfoHopeBurdenSelectEntity'
import { Or27852Logic } from '~/components/custom-components/organisms/Or27852/Or27852.logic'
import { Or27852Const } from '~/components/custom-components/organisms/Or27852/Or27852.constants'
import type {
  UseSlipInfoDuplicateRowAftProcSelectInEntity,
  UseSlipInfoDuplicateRowAftProcSelectOutEntity,
} from '~/repositories/cmn/entities/UseSlipInfoDuplicateRowAftProcSelectEntity'
import { Or35957Const } from '~/components/custom-components/organisms/Or35957/Or35957.constants'
import { Or35957Logic } from '~/components/custom-components/organisms/Or35957/Or35957.logic'
import type { Or35957OnewayType } from '~/types/cmn/business/components/Or35957Type'
import { Or26676Const } from '~/components/custom-components/organisms/Or26676/Or26676.constants'
import { Or26676Logic } from '~/components/custom-components/organisms/Or26676/Or26676.logic'
import type { Or26676OnewayType } from '~/types/cmn/business/components/Or26676Type'
import { Or27856Const } from '~/components/custom-components/organisms/Or27856/Or27856.constants'
import { Or27856Logic } from '~/components/custom-components/organisms/Or27856/Or27856.logic'
import type {
  Or27856SelectInfoType,
  Or27856Type,
} from '~/types/cmn/business/components/Or27856Type'
import type { Or27856OutputItem } from '~/components/custom-components/organisms/Or27856/Or27856.type'
import { Or29139Const } from '~/components/custom-components/organisms/Or29139/Or29139.constants'
import { Or29139Logic } from '~/components/custom-components/organisms/Or29139/Or29139.logic'
import type { Or29139OnewayType } from '~/types/cmn/business/components/Or29139Type'
import type {
  UseSlipOtherDtlInfoRecalculationSelectInEntity,
  UseSlipOtherDtlInfoRecalculationSelectOutEntity,
} from '~/repositories/cmn/entities/UseSlipOtherDtlInfoRecalculationSelectEntity'
import type {
  UseSlipOtherDtlInfoSocialWelfareOfficeChangeSelectInEntity,
  UseSlipOtherDtlInfoSocialWelfareOfficeChangeSelectOutEntity,
} from '~/repositories/cmn/entities/UseSlipOtherDtlInfoSocialWelfareOfficeChangeSelectEntity'
import { Or28534Const } from '~/components/custom-components/organisms/Or28534/Or28534.constants'
import { Or28534Logic } from '~/components/custom-components/organisms/Or28534/Or28534.logic'
import type { Or28534Type, Or28534OnewayType } from '~/types/cmn/business/components/Or28534Type'
import type {
  UseSlipInfoTmpImportSelectInEntity,
  UseSlipInfoTmpImportSelectOutEntity,
} from '~/repositories/cmn/entities/UseSlipInfoTmpImportSelectEntity'
import { Or28334Const } from '~/components/custom-components/organisms/Or28334/Or28334.constants'
import { Or28334Logic } from '~/components/custom-components/organisms/Or28334/Or28334.logic'
import type {
  Or28334OnewayType,
  Or28334Type,
  SpecialInstructionsPeriodTableData,
} from '~/types/cmn/business/components/Or28334Type'
import { Or26835Const } from '~/components/custom-components/organisms/Or26835/Or26835.constants'
import { Or26835Logic } from '~/components/custom-components/organisms/Or26835/Or26835.logic'
import type {
  Or26835OnewayType,
  Or26835Type,
  TankiTeisyobiList,
} from '~/types/cmn/business/components/Or26835Type'
import { Or27946Logic } from '~/components/custom-components/organisms/Or27946/Or27946.logic'
import { Or27946Const } from '~/components/custom-components/organisms/Or27946/Or27946.constants'
import type {
  Or27946TowWayType,
  Or27946OneWayType,
} from '~/types/cmn/business/components/Or27946Type'
import type {
  UseSlipInfoRecalculationBefSelectInEntity,
  UseSlipInfoRecalculationBefSelectOutEntity,
} from '~/repositories/cmn/entities/UseSlipInfoRecalculationBefSelectEntity'
import type {
  UseSlipInfoDailyRateCalculationSelectInEntity,
  UseSlipInfoDailyRateCalculationSelectOutEntity,
} from '~/repositories/cmn/entities/UseSlipInfoDailyRateCalculationSelectEntity'
import type {
  UseSlipInfoPersonInsuredPersonSelectInEntity,
  UseSlipInfoPersonInsuredPersonSelectOutEntity,
} from '~/repositories/cmn/entities/UseSlipInfoPersonInsuredPersonSelectEntity'
import type {
  UseSlipInfoRecalculationBef2SelectInEntity,
  UseSlipInfoRecalculationBef2SelectOutEntity,
} from '~/repositories/cmn/entities/UseSlipInfoRecalculationBef2SelectEntity'
import type {
  UseSlipInfoEditRowBefProcSelectInEntity,
  UseSlipInfoEditRowBefProcSelectOutEntity,
  AdsInfo,
} from '~/repositories/cmn/entities/UseSlipInfoEditRowBefProcSelectEntity'
import type {
  UseSlipInfoPredictionChangeBtnAftProcSelectInEntity,
  UseSlipInfoPredictionChangeBtnAftProcSelectOutEntity,
} from '~/repositories/cmn/entities/UseSlipInfoPredictionChangeBtnAftProcSelectEntity'
import type {
  UseSlipInfoSortSelectInEntity,
  UseSlipInfoSortSelectOutEntity,
} from '~/repositories/cmn/entities/UseSlipInfoSortSelectEntity'
import type {
  UseSlipInfoPredictionLendingChangeRowAftProcSelectInEntity,
  UseSlipInfoPredictionLendingChangeRowAftProcSelectOutEntity,
} from '~/repositories/cmn/entities/UseSlipInfoPredictionLendingChangeRowAftProcSelectEntity'
import { Or57151Const } from '~/components/custom-components/organisms/Or57151/Or57151.constants'
import { Or57151Logic } from '~/components/custom-components/organisms/Or57151/Or57151.logic'
import type { Or57151OnewayType } from '~/types/cmn/business/components/Or57151Type'
import { Or41179Const } from '~/components/base-components/organisms/Or41179/Or41179.constants'
import { Or41179Logic } from '~/components/base-components/organisms/Or41179/Or41179.logic'

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
/** props */
interface Props {
  modelValue: Or36647Type
  onewayModelValue: Or36647OnewayType
  uniqueCpId: string
}

// 引継情報を取得する
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const local = reactive({
  mo00043: { id: Or36647Const.TAB_ID_USING_SLIP } as Mo00043Type,
  or36647: {} as Or36647OnewayType,
  offeryYearMonth: {
    value: '',
  },
  createDate: {
    value: '',
  },
  inputWorkSchedule: {
    modelValue: false,
  },
  inputWorkAchievements: {
    modelValue: false,
  },
  compute: '',
  computeItemList: [] as CodeType[],
  selectedUsingSlipItemIndex: 0,
  selectedColumnIndex: 0,
  sompoFlg: false,
  confirmFlg: false,
  insuredIdBgColor: 'transparent',
  levelOfCareBgColor: 'transparent',
  usingSlipInfo: {
    riyourshaData: [
      {
        rCompBase: '',
      },
    ] as RiyourshaData[],
    kbnShikyuuGendoData: [
      {
        dmyKbnGendo: '0',
        tKGendo: '',
      },
    ] as KbnShikyuuGendoData[],
    syuruiGendoData: [{}] as SyuruiGendoData[],
    sumData: [] as SumData[],
  } as RiyouInfo,
  usingSlipList: [] as UsingSlipDetailInfo[],
  selectedUsingSlipOtherTableItemIndex: 0,
  selectedForehead: -1,
  selectedPublic: -1,
  selectedShort: -1,
  selectedUnit: -1,
  selectedDivision: -1,
  selectedBurden: -1,
  selectedInsurance: -1,
  usingSlipOthrtTableList: [] as UsingSlipOthrtTable[],
  kohiList: [] as Kohi[],
  selectedRiyouryouItemIndex: -1,
  riyouryouList: [] as UsingSlipRiyouryou[],
  selectedSyafukuItemIndex: -1,
  syafukuList: [] as UsingSlipSyafuku[],
  adsKihon: [] as AdsInfo[],
  or26828: {
    historySelectionList: [],
    plan1Id: '',
  },
  or30707: {
    historySelectionList: [],
    plan1Id: '',
  },
  or27736: {
    periodStartDate: { value: '' },
    periodEndDate: { value: '' },
  },
  or27635: {
    modifiedDay: '',
  },
  or35687: {
    /** 処理フラグ */
    processFlg: '',
    /** 利用票予定実績リスト一覧明細*/
    usingSlipDetailInfo: [] as UsingSlipDetailInfo[],
  },
  or27016: {
    // モード
    provideYm: '',
    // 利用票リスト
    meisaiList: [] as MeisaiList[],
  },
  or00233: {
    reSearchFlg: '',
  },
  or27943: {},
  or26676: {},
  or27856: {
    outputItem: {} as Or27856OutputItem,
  } as Or27856Type,
  or29139: {
    shienId: '',
    userid: '',
    yymmYm: '',
    yymmD: '',
  } as Or29139OnewayType,
  or28534: {
    yymmYm: '',
    userid: '',
  } as Or28534Type,
  or28334: {
    specialInstructionsPeriodList: [] as SpecialInstructionsPeriodTableData[],
  } as Or28334Type,
  or26835: {
    day: '',
    tankiTeisyobiList: [] as TankiTeisyobiList[],
  } as Or26835Type,
  or27946: {} as Or27946TowWayType,
  or35957: { retValue: '' },
  or27852: { processFlg: '' },
})

const localOneway = reactive({
  or36647: {
    ...props.onewayModelValue,
  },
  // 複写
  mo00611CopyOneway: {
    name: 'copyBtn',
    btnLabel: t('btn.copy'),
    labelColor: 'rgb(var(--v-theme-key))',
    width: '70px',
    class: 'btn-copy',
    tooltipText: t('tooltip.care-plan2-copy-btn'),
  } as Mo00611OnewayType,
  // 再計算
  mo00611RecomputeOneway: {
    name: 'recomputeBtn',
    btnLabel: t('btn.recompute'),
    labelColor: 'rgb(var(--v-theme-key))',
    width: '70px',
    class: 'btn-recompute',
    tooltipText: t('tooltip.recompute'),
  } as Mo00611OnewayType,
  mo00043Oneway: {
    tabItems: [
      {
        id: 'usingSlip',
        title: t('label.using-slip'),
        tooltipText: t('label.using-slip'),
        tooltipLocation: 'bottom',
      },
      {
        id: 'usingSlipOtherTable',
        title: t('label.using-slip-other-table'),
        tooltipText: t('label.using-slip-other-table'),
        tooltipLocation: 'bottom',
      },
    ],
  } as Mo00043OnewayType,
  // 提供年月
  mo01338Oneway: {
    value: t('label.offer-ym'),
  } as Mo01338OnewayType,
  mo00037Oneway: {} as Mo00037OnewayType,
  backmo00009Oneway: {
    variant: 'text',
    color: 'black-800',
    btnIcon: 'chevron_left',
  } as Mo00009OnewayType,
  forwardmo00009Oneway: {
    variant: 'text',
    color: 'black-800',
    btnIcon: 'chevron_right',
  } as Mo00009OnewayType,
  mo00020Oneway: {
    showItemLabel: false,
    customClass: new CustomClass({ outerClass: 'offery-ym' }),
  } as Mo00020OnewayType,
  // 作成年月日
  mo00020YmdOneway: {
    itemLabel: t('label.create-ymd'),
    showItemLabel: true,
    isVerticalLabel: false,
    isRequired: true,
    hideDetails: true,
    disabled: false,
    width: '135',
  } as Mo00020OnewayType,
  // 入力作業
  mo00018ScheduleOneway: {
    itemLabel: t('label.input-work'),
    showItemLabel: true,
    hideDetails: true,
    isVerticalLabel: false,
    checkboxLabel: t('label.schedule-ok'),
  } as Mo00018OnewayType,
  mo00018AchievementsOneway: {
    showItemLabel: false,
    hideDetails: true,
    isVerticalLabel: false,
    checkboxLabel: t('label.achievements-ok'),
  } as Mo00018OnewayType,
  // 計算
  mo00039Oneway: {
    name: t('label.compute'),
    itemLabel: t('label.compute'),
    showItemLabel: true,
    hideDetails: true,
    inline: true,
  } as Mo00039OnewayType,
  // 保険者
  mo00615InsurOneway: {
    itemLabel: t('label.insurer'),
    customClass: new CustomClass({ outerClass: 'mr-2' }),
  } as Mo00615OnewayType,
  mo00009Oneway: {
    // デフォルト値の設定
    btnIcon: 'edit_square',
    density: 'compact',
    disabled: false,
  } as Mo00009OnewayType,
  mo01338InsurOneway: {
    customClass: new CustomClass({
      outerClass: 'mr-2',
      itemClass: 'ml-2 align-center mt-1',
    }),
  } as Mo01338OnewayType,
  // 介護度
  mo00615LevelOfCareOneway: {
    itemLabel: t('label.level-of-care'),
    customClass: new CustomClass({ outerClass: 'mr-0' }),
  } as Mo00615OnewayType,
  mo01338LevelOfCareOneway: {
    customClass: new CustomClass({
      outerClass: 'mr-2',
      itemClass: 'ml-2 align-center mt-1',
    }),
  } as Mo01338OnewayType,
  // 変更日
  mo01338UpdateDateOneway: {
    itemLabel: t('label.update-date'),
    itemLabelFontWeight: 'normal',
    customClass: new CustomClass({
      outerClass: 'mr-2',
      itemClass: 'ml-7 align-center mt-1 inline-block',
      labelClass: 'inline-block',
    }),
  } as Mo01338OnewayType,
  // 支援事業所
  mo01338SupportOfficeOneway: {
    itemLabel: t('label.support-office'),
    itemLabelFontWeight: 'normal',
    customClass: new CustomClass({
      outerClass: 'mr-2',
      itemClass: 'ml-4 align-center mt-1 inline-block',
      labelClass: 'inline-block',
    }),
  } as Mo01338OnewayType,
  // 被保険者
  mo00615InsuredOneway: {
    itemLabel: t('label.insured'),
    customClass: new CustomClass({ outerClass: 'mr-2' }),
  } as Mo00615OnewayType,
  mo01338InsuredOneway: {
    customClass: new CustomClass({
      outerClass: 'mr-2',
      itemClass: 'ml-2 align-center mt-1',
    }),
  } as Mo01338OnewayType,
  // 変更後
  mo01338UpdateAfterOneway: {
    itemLabel: t('label.update-after'),
    itemLabelFontWeight: 'normal',
    customClass: new CustomClass({
      outerClass: 'mr-2',
      itemClass: 'align-center mt-1 inline-block',
      labelClass: 'inline-block',
      itemStyle: 'margin-left: 22px',
    }),
  } as Mo01338OnewayType,
  // 支給限度
  mo01338PaymentLimitOneway: {
    itemLabel: t('label.payment-limit'),
    itemLabelFontWeight: 'normal',
    customClass: new CustomClass({
      outerClass: 'mr-2',
      itemClass: 'ml-4 align-center mt-1 inline-block',
      labelClass: 'inline-block',
    }),
  } as Mo01338OnewayType,
  // 担当者
  mo01338ManagerOneway: {
    itemLabel: t('label.using-manager'),
    itemLabelFontWeight: 'normal',
    customClass: new CustomClass({
      outerClass: 'mr-2',
      itemClass: 'ml-4 align-center mt-1 inline-block',
      labelClass: 'inline-block',
    }),
  } as Mo01338OnewayType,
  // 希望額
  mo01338HopeAmountOneway: {
    itemLabel: t('label.hope-amount'),
    itemLabelFontWeight: 'normal',
    customClass: new CustomClass({
      outerClass: 'mr-2',
      itemClass: 'ml-4 align-center mt-1 inline-block',
      labelClass: 'inline-block',
    }),
  } as Mo01338OnewayType,
  // 要確認
  mo00611ConfirmOneway: {
    name: 'confrimBtn',
    btnLabel: t('btn.requiredーconfirm'),
    width: '70px',
    class: 'btn-confrim',
  } as Mo00611OnewayType,
  // 下アイコンボタン
  mo00009OnewayDown: {
    btnIcon: 'keyboard_arrow_down',
    variant: 'flat',
    color: 'transparent',
    rounded: '0',
    minWidth: '30px',
    minHeight: '30px',
    tooltipText: t('tooltip.move-next'),
  } as Mo00009OnewayType,
  // 上アイコンボタン
  mo00009OnewayUp: {
    btnIcon: 'keyboard_arrow_up',
    variant: 'flat',
    color: 'transparent',
    rounded: '0',
    minWidth: '30px',
    minHeight: '30px',
    tooltipText: t('tooltip.move-pre'),
  } as Mo00009OnewayType,
  // 修正
  mo00611EditOneway: {
    name: 'editBtn',
    btnLabel: t('btn.edit'),
    width: '70px',
    class: 'btn-edit',
    tooltipText: t('tooltip.update'),
  } as Mo00611OnewayType,
  // 予実変換
  mo00611PredictionOneway: {
    name: 'predictionBtn',
    btnLabel: t('btn.prediction'),
    width: '70px',
    class: 'btn-prediction',
    tooltipText: t('tooltip.prediction'),
  } as Mo00611OnewayType,
  // 日割利用期間
  mo00611DailyCalculationOneway: {
    name: 'predictionBtn',
    btnLabel: t('btn.daily-rate-using-period'),
    width: '100px',
    class: 'btn-daily-calculation',
  } as Mo00611OnewayType,
  // AI予測
  mo00611AiForecastOneway: {
    name: 'aiForecastBtn',
    btnLabel: t('btn.ai-forecast'),
    class: 'btn-recompute',
  } as Mo00611OnewayType,
  mo00018TblScheduleOneway: {
    showItemLabel: false,
    hideDetails: true,
    isVerticalLabel: false,
    checkboxLabel: t('label.sched'),
    customClass: new CustomClass({ outerClass: 'ma-0 pa-0' }),
  } as Mo00018OnewayType,
  mo00018TblAchievementsOneway: {
    showItemLabel: false,
    hideDetails: true,
    isVerticalLabel: false,
    checkboxLabel: t('label.achv'),
    customClass: new CustomClass({ outerClass: 'ma-0 pa-0' }),
  } as Mo00018OnewayType,
  // 提供時間時間テキスト
  mo01274Oneway: {
    type: 'time',
  } as Mo01274OnewayType,
  // 提供時間プルダウン
  mo01282Oneway: {
    itemTitle: 'label',
    itemValue: 'value',
  } as Mo01282OnewayType,
  // カレンダーアイコンボタン
  mo00009CalendarOneway: {
    btnIcon: 'calendar_today',
    variant: 'flat',
    color: 'transparent',
    rounded: '0',
    minWidth: '30px',
    minHeight: '30px',
    size: '20',
  } as Mo00009OnewayType,
  // 予定/実施回数
  mo01278Oneway: {
    min: -9,
    max: 99,
    maxLength: '2',
  } as Mo01278OnewayType,
  mo01272Oneway: {
    name: 'time',
    width: '105px',
    hideDetails: true,
    showItemLabel: false,
    customClass: new CustomClass({ itemClass: 'ma-0' }),
  } as Mo00045OnewayType,
  // 種類限度外/区分支給限度外単位数
  mo01278TensuOverOneway: {
    isEditCamma: false,
    maxLength: '5',
  } as Mo01278OnewayType,
  // 設定
  mo00611SettingOneway: {
    name: 'settingsBtn',
    btnLabel: t('btn.settings'),
    class: 'btn-settings',
    tooltipText: t('tooltip.social-welfare-alleviation-regist'),
  } as Mo00611OnewayType,
  // 利用票・別表画面で警告情報ダイアログ
  or10923Oneway: {
    warningMessageInfo: [],
  } as Or10923OnewayType,
  // 事業所プルダウン
  mo01282OfficeOneway: {
    itemTitle: 'jigyoNameKnj',
    itemValue: 'svJigyoId',
  } as Mo01282OnewayType,
  // 数
  mo01278NumberOneway: {
    isEditCamma: false,
    maxLength: '2',
    min: 0,
  } as Mo01278OnewayType,
  // 対象額
  mo01278TargetAmountOneway: {
    isEditCamma: false,
    maxLength: '6',
    min: 0,
  } as Mo01278OnewayType,
  // 項目名
  mo01274ItemOneway: {} as Mo01274OnewayType,
  // 週間計画取り込みダイアログ
  or26817Oneway: {} as Or26817OnewayType,
  // 利用票別表メンテナンスダイアログ
  or26828Oneway: {} as Or26828OnewayType,
  // 福祉用具貸与単位数一括設定ダイアログ
  or30707Oneway: {} as Or30707OnewayType,
  // 有効期間外サービス検索ダイアログ
  or27736Oneway: {} as Or27736OnewayType,
  // 日割利用期間ダイアログ
  or07212Oneway: {
    userId: props.onewayModelValue.userId,
  } as Or07212OnewayType,
  // 保険者選択ダイアログ
  or27635Oneway: {} as Or27635OnewayType,
  // 利用票予定→実績変換画面ダイアログ
  or35687Oneway: {} as Or35687OnewayType,
  // カレンダー入力ダイアログ
  or27016Oneway: {} as Or27016OnewayType,
  // 希望負担額登録ダイアログ
  or00233Oneway: {} as Or00233SelectInfoType,
  // 横出しサービス単位再設定ダイアログ
  or27943Oneway: {} as Or27943OnewayType,
  // 日割算定確認ダイアログ
  or35957Oneway: {} as Or35957OnewayType,
  // シミュレーション雛形選択ダイアログ
  or28534Oneway: {} as Or28534OnewayType,
  // 特別指示期間ダイアログ
  or28334Oneway: {} as Or28334OnewayType,
  // 短期退所日登録ダイアログ
  or26835Oneway: {} as Or26835OnewayType,
  // 総合事業サービス単位再設定ダイアログ
  or27946Oneway: {} as Or27946OneWayType,
  // 社福軽減登録ダイアログ
  or26676Oneway: {} as Or26676OnewayType,
  // 認定期間中の短期入所利用日数ダイアログ
  or27856Oneway: {} as Or27856SelectInfoType,
  // 備考欄画面ダイアログ
  or29139Oneway: {} as Or29139OnewayType,
  // 印刷設定ダイアログ
  or57151Oneway: {} as Or57151OnewayType,
})

const or11871 = ref({ uniqueCpId: Or11871Const.CP_ID })
const or21828 = ref({ uniqueCpId: Or21828Const.CP_ID })
const or21830 = ref({ uniqueCpId: Or21830Const.CP_ID })
const or00248 = ref({ uniqueCpId: '' })
const orX0004 = ref({ uniqueCpId: OrX0004Const.CP_ID(0) })
const orX0014 = ref({ uniqueCpId: OrX0014Const.CP_ID(0) })
const orX0005 = ref({ uniqueCpId: OrX0005Const.CP_ID(0) })
const orX0042 = ref({ uniqueCpId: OrX0042Const.CP_ID(0) })
const orHeadLine = ref({ uniqueCpId: '' })
const or00249 = ref({ uniqueCpId: '' })
const or21735 = ref({ uniqueCpId: Or21735Const.CP_ID(0) })
const or21737 = ref({ uniqueCpId: Or21737Const.CP_ID(0) })
const or21738 = ref({ uniqueCpId: Or21738Const.CP_ID(0) })
const or10923 = ref({ uniqueCpId: Or10923Const.CP_ID(0) })
const or21814 = ref({ uniqueCpId: Or21814Const.CP_ID(0) })
const or26817 = ref({ uniqueCpId: Or26817Const.CP_ID(0) })
const or26828 = ref({ uniqueCpId: Or26828Const.CP_ID(0) })
const or30707 = ref({ uniqueCpId: Or30707Const.CP_ID(0) })
const or27736 = ref({ uniqueCpId: Or27736Const.CP_ID(0) })
const or07212 = ref({ uniqueCpId: Or07212Const.CP_ID(0) })
const or27635 = ref({ uniqueCpId: Or27635Const.CP_ID(0) })
const or35687 = ref({ uniqueCpId: Or35687Const.CP_ID(0) })
const or27016 = ref({ uniqueCpId: Or27016Const.CP_ID(0) })
const or35745 = ref({ uniqueCpId: Or35745Const.CP_ID(0) })
const or00233 = ref({ uniqueCpId: Or00233Const.CP_ID(0) })
const or21815 = ref({ uniqueCpId: Or21815Const.CP_ID(0) })
const or27943 = ref({ uniqueCpId: Or27943Const.CP_ID(0) })
const or27852 = ref({ uniqueCpId: Or27852Const.CP_ID(0) })
const or35957 = ref({ uniqueCpId: Or35957Const.CP_ID(0) })
const or21735_2 = ref({ uniqueCpId: Or21735Const.CP_ID(2) })
const or21735_3 = ref({ uniqueCpId: Or21735Const.CP_ID(3) })
const or21738_2 = ref({ uniqueCpId: Or21738Const.CP_ID(2) })
const or21738_3 = ref({ uniqueCpId: Or21738Const.CP_ID(3) })
const or26676 = ref({ uniqueCpId: Or26676Const.CP_ID(0) })
const or27856 = ref({ uniqueCpId: Or27856Const.CP_ID(0) })
const or29139 = ref({ uniqueCpId: Or29139Const.CP_ID(0) })
const or28534 = ref({ uniqueCpId: Or28534Const.CP_ID(0) })
const or28334 = ref({ uniqueCpId: Or28334Const.CP_ID(0) })
const or26835 = ref({ uniqueCpId: Or26835Const.CP_ID(0) })
const or27946 = ref({ uniqueCpId: Or27946Const.CP_ID(0) })
const or57151 = ref({ uniqueCpId: Or57151Const.CP_ID(0) })
const or41179 = ref({ uniqueCpId: '' })

// 閲覧権限
const hasView: boolean = await hasViewAuth(Or36647Const.ROUTING)
// 閲覧権限がない場合 非表示
// 保存権限
const hasRegist: boolean = await hasRegistAuth(Or36647Const.ROUTING)
// 編集フラグ
const isEdit = computed(() => {
  return isEditNavControl([props.uniqueCpId])
})
// 電子保存の3原則
const electronicSavePrinciple = ref(false)
// 利用票出力フラグ
const usingSlipPrintFlg = ref(Or36647Const.USING_SLIP_PRINT_FLG_0)
// 利用票別表出力フラグ
const usingSlipOtherTablePrintFlg = ref(Or36647Const.USING_SLIP_OTHER_TABLE_PRINT_FLG_0)
// 削除履歴出力区分
const deleteHistoryOutputKbn = ref(Or36647Const.DELETE_HISTORY_OUTPUT_KBN_0)
// 新規計算フラグ
const newComputeFlg = ref(true)
// 利用票明細一覧hover
const hoverdIndex = ref(-1)
// システム共有領域の状態管理
const systemCommonsStore = useSystemCommonsStore()
// 公費集計欄一覧ヘッダ
const publiclyFundedSumHeaders = [
  { title: t('label.table-maintenance-header-17'), key: 'dmyJigyoNameKnj', sortable: false },
  { title: t('label.publicly-funded-amount'), key: 'kohiGaku', width: '80px', sortable: false },
  {
    title: t('label.table-maintenance-header-18'),
    key: 'honninHutan',
    width: '80px',
    sortable: false,
  },
]
// 利用料集計欄一覧ヘッダ
const columnMinWidth = ref<number[]>([110,120,80,80,80])
const usingFeeSumHeaders = [
  { title: t('label.application-business-establishment'), key: 'dmyJigyoNameKnj', sortable: false },
  { title: t('label.using-fee-item-name'), key: 'riyouryouItem', width: '120px', sortable: false },
  {
    title: t('label.using-slip-unit-price'),
    key: 'riyouryouTanka',
    width: '80px',
    sortable: false,
  },
  { title: t('label.using-fee-umber'), key: 'riyouryouSu', width: '80px', sortable: false },
  { title: t('label.amount-1'), key: 'riyouryouKingaku', width: '80px', sortable: false },
]
// 社福軽減集計欄一覧ヘッダ
const socialWelfareAlleviationSumHeaders = [
  { title: t('label.application-business-establishment'), key: 'dmyJigyoNameKnj', sortable: false },
  { title: t('label.serviceType'), key: 'syafukuNm', width: '120px', sortable: false },
  { title: t('label.target-amount'), key: 'syafukuObj', width: '80px', sortable: false },
  { title: t('label.reduction-rate'), key: 'syafukuRitu', width: '80px', sortable: false },
  { title: t('label.reduction-amount'), key: 'syafukuGaku', width: '80px', sortable: false },
]

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or11871Const.CP_ID]: or11871.value,
  [Or21828Const.CP_ID]: or21828.value,
  [Or21830Const.CP_ID]: or21830.value,
  [Or00248Const.CP_ID(0)]: or00248.value,
  [OrX0004Const.CP_ID(0)]: orX0004.value,
  [OrX0014Const.CP_ID(0)]: orX0014.value,
  [OrX0005Const.CP_ID(0)]: orX0005.value,
  [OrX0042Const.CP_ID(0)]: orX0042.value,
  [Or21735Const.CP_ID(0)]: or21735.value,
  [Or21737Const.CP_ID(0)]: or21737.value,
  [Or21738Const.CP_ID(0)]: or21738.value,
  [Or10923Const.CP_ID(0)]: or10923.value,
  [Or21814Const.CP_ID(2)]: or21814.value,
  [Or26817Const.CP_ID(0)]: or26817.value,
  [Or26828Const.CP_ID(0)]: or26828.value,
  [Or30707Const.CP_ID(0)]: or30707.value,
  [Or07212Const.CP_ID(0)]: or07212.value,
  [Or27635Const.CP_ID(1)]: or27635.value,
  [Or35687Const.CP_ID(1)]: or35687.value,
  [Or27016Const.CP_ID(1)]: or27016.value,
  [Or35745Const.CP_ID(1)]: or35745.value,
  [Or00233Const.CP_ID(1)]: or00233.value,
  [Or21815Const.CP_ID(1)]: or21815.value,
  [Or27943Const.CP_ID(1)]: or27943.value,
  [Or27852Const.CP_ID(0)]: or27852.value,
  [Or35957Const.CP_ID(0)]: or35957.value,
  [Or21735Const.CP_ID(2)]: or21735_2.value,
  [Or21735Const.CP_ID(3)]: or21735_3.value,
  [Or21738Const.CP_ID(2)]: or21738_2.value,
  [Or21738Const.CP_ID(3)]: or21738_3.value,
  [Or26676Const.CP_ID(0)]: or26676.value,
  [Or27856Const.CP_ID(0)]: or27856.value,
  [Or29139Const.CP_ID(0)]: or29139.value,
  [Or28534Const.CP_ID(0)]: or28534.value,
  [Or28334Const.CP_ID(0)]: or28334.value,
  [Or26835Const.CP_ID(0)]: or26835.value,
  [Or27946Const.CP_ID(0)]: or27946.value,
  [Or57151Const.CP_ID(0)]: or57151.value,
  [Or41179Const.CP_ID(1)]: or41179.value,
})
useSetupChildProps(or00248.value.uniqueCpId, {
  [OrHeadLineConst.CP_ID]: orHeadLine.value,
  [Or00249Const.CP_ID(0)]: or00249.value,
})

onMounted(async () => {
  // 利用者を全選択です。
  await nextTick(() => {
    useScreenTwoWayBind<OrHeadLineType>({
      cpId: OrHeadLineConst.CP_ID,
      uniqueCpId: orHeadLine.value.uniqueCpId,
    }).setValue({ value: Or36647Const.STR_ALL })
  })

  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 計算区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_COMPUTE_CATEGORY },
  ]
  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  // コード取得
  // 計算区分
  local.computeItemList = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_COMPUTE_CATEGORY
  ).sort((a, b) => b.value.localeCompare(a.value))

  // 福祉用具貸与フラグ（予定）、福祉用具貸与フラグ（実績）
  selectCodeKbnList[0].mCdKbnId = CmnMCdKbnId.M_CD_KBN_ID_WELFARE_EQUIP_LENDING
  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })
  // コード取得
  // 福祉用具貸与フラグ（予定）、福祉用具貸与フラグ（実績）
  localOneway.mo01282Oneway.items = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_WELFARE_EQUIP_LENDING
  )

  // 事業所選択プルダウンの検索条件設定
  if (systemCommonsStore.getUserSelectSelfId()) {
    Or41179Logic.state.set({
      uniqueCpId: or41179.value.uniqueCpId,
      state: {
        searchCriteria: {
          selfId: systemCommonsStore.getUserSelectSelfId(),
        },
      },
    })
  }

  // 利用票情報情報取得
  await useSlipInitInfoSelect()
})

// 画面メニュー
Or11871Logic.state.set({
  uniqueCpId: or11871.value.uniqueCpId,
  state: {
    showFavorite: false,
    showViewSelect: false,
    showCreateBtn: false,
    showCreateMenuCopy: false,
    showPrintBtn: true,
    showOptionMenuBtn: true,
    viewSelectItems: [],
    showMasterBtn: true,
    showOptionMenuDelete: false,
    showSaveBtn: true,
    tooltipTextSaveBtn: t('tooltip.save'),
    tooltipTextPrintBtn: t('tooltip.care-plan2-print-setting-btn'),
    tooltipTextMasterBtn: '',
    tooltipTextOptionMenuBtn: '',
  },
})

// 提供年月情報
const offeryYearMonthDayList = computed(() => {
  const offeryYearMonthDays = [] as DayOfWeek[]
  const offeryYearMonth = new Date(local.offeryYearMonth.value)
  const offeryYear = offeryYearMonth.getFullYear()
  const offeryMonth = offeryYearMonth.getMonth()
  const firstDay = new Date(offeryYear, offeryMonth, 1).getDate()
  const lastDay = new Date(offeryYear, offeryMonth + 1, 0).getDate()
  for (let i = firstDay; i <= lastDay; i++) {
    const dayOfWeek = getDayOfWeek(`${offeryYear}/${offeryMonth + 1}'/'${i}`)
    offeryYearMonthDays.push({
      day: i,
      dayOfWeek: dayOfWeek,
      color: local.usingSlipInfo.weekColor[i - 1],
    } as DayOfWeek)
  }
  return offeryYearMonthDays
})

// 利用票別表 給付管理単位数 表示/非表示
const showBenefitManagement = computed(() => {
  if (local.offeryYearMonth.value === Or36647Const.BASE_OFFER_YEAR_MONTH) {
    return true
  } else {
    const baseDate = new Date(Or36647Const.BASE_OFFER_YEAR_MONTH)
    const offeryYearMonth = new Date(local.offeryYearMonth.value)
    return offeryYearMonth > baseDate
  }
})

// 割り振りの残り単位数
const allocationRestUnitNumber = computed(() => {
  let allocationRestUnitNumber = 0
  // 区分支給限度情報.区分限度 > 0の場合
  if (parseInt(local.usingSlipInfo.kbnShikyuuGendoData[0].dmyKbnGendo) > 0) {
    // 区分支給限度情報.区分支給限度_基準内の合計 - 区分支給限度情報.区分限度
    allocationRestUnitNumber =
      parseInt(local.usingSlipInfo.kbnShikyuuGendoData[0].cKTensuSum) -
      parseInt(local.usingSlipInfo.kbnShikyuuGendoData[0].dmyKbnGendo)
  }
  return allocationRestUnitNumber
})

// （列フッター）割り振りの残り単位数 表示/非表示
const showAllocationRestUnitNumber = computed(() => {
  let showFlg = true
  const kbnShikyuuGendoData = local.usingSlipInfo.kbnShikyuuGendoData[0]
  // 区分支給限度情報.区分限度 > 0の場合
  if (parseInt(kbnShikyuuGendoData.dmyKbnGendo) > 0) {
    // 区分支給限度情報.区分限度 >= 区分支給限度情報.サービス単位/金額の合計の場合
    if (parseInt(kbnShikyuuGendoData.dmyKbnGendo) > parseInt(kbnShikyuuGendoData.cSvTensuSum)) {
      // 割り振りの残り単位数が非表示にする
      showFlg = false
    } else {
      // 区分支給限度情報.区分限度 < 区分支給限度情報.サービス単位/金額の合計の場合
      // 区分支給限度情報.区分限度 = 区分支給限度情報.区分支給限度_基準内の合計の場合
      if (kbnShikyuuGendoData.dmyKbnGendo === kbnShikyuuGendoData.cKTensuSum) {
        // 割り振りの残り単位数が非表示にする
        showFlg = false
      } else {
        // 区分支給限度情報.区分限度 <> 区分支給限度情報.区分支給限度_基準内の合計の場合
        // 割り振りの残り単位数が表示にする
        showFlg = true
      }
    }
  } else {
    // 区分支給限度情報.区分限度 <= 0の場合
    // 割り振りの残り単位数が非表示にする
    showFlg = false
  }
  return showFlg
})

// （列フッター）計_サービス単位/金額の色
const cSvTensuColor = computed(() => {
  let color = Or36647Const.COLOR_DEFAULT
  const riyourshaData = local.usingSlipInfo.riyourshaData[0]
  const kbnShikyuuGendoData = local.usingSlipInfo.kbnShikyuuGendoData[0]
  // 利用者情報.計算フラグ = nullの場合
  if (isEmpty(riyourshaData.rCompBase)) {
    // 利用者情報.サービス単位数（実績） > 利用者情報.区分支給限度基準額の場合
    if (parseInt(riyourshaData.cmnTucPlanSvTensuJ) > parseInt(kbnShikyuuGendoData.tKGendo)) {
      // 計_サービス単位/金額の色は「FF0000」を設定
      color = Or36647Const.COLOR_RED
    }
  } else {
    // 利用者情報.計算フラグ <> nullの場合
    // 利用者情報.サービス単位数（予定） > 利用者情報.区分支給限度基準額の場合
    if (parseInt(riyourshaData.cmnTucPlanSvTensuY) > parseInt(kbnShikyuuGendoData.tKGendo)) {
      // 計_サービス単位/金額の色は「FF0000」を設定
      color = Or36647Const.COLOR_RED
    }
  }
  return color
})

// （列フッター）計_給付管理単位数の色
const cKyufukanriTenColor = computed(() => {
  let color = Or36647Const.COLOR_DEFAULT
  const riyourshaData = local.usingSlipInfo.riyourshaData[0]
  const kbnShikyuuGendoData = local.usingSlipInfo.kbnShikyuuGendoData[0]
  const cKyufukanriTenTotal = getUsingSlipOtherTableTotal(Or36647Const.KEY_C_KYUFUKANRI_TEN)
  // 利用者情報.計算フラグ = nullの場合
  if (isEmpty(riyourshaData.rCompBase)) {
    // 利用者情報.給付管理単位数 > 利用者情報.区分支給限度基準額の場合
    if (cKyufukanriTenTotal > parseInt(kbnShikyuuGendoData.tKGendo)) {
      // 計_給付管理単位数の色は「FF0000」を設定
      color = Or36647Const.COLOR_RED
    }
  } else {
    // 利用者情報.計算フラグ <> nullの場合
    // 利用者情報.給付管理単位数 > 利用者情報.区分支給限度基準額の場合
    if (cKyufukanriTenTotal > parseInt(kbnShikyuuGendoData.tKGendo)) {
      // 計_給付管理単位数の色は「FF0000」を設定
      color = Or36647Const.COLOR_RED
    }
  }
  return color
})

// （列フッター）計_利用者負担_保険/事業 （列フッター）計_利用者負担_全額負担 の色
const hutanHHutanJColor = computed(() => {
  let color = Or36647Const.COLOR_DEFAULT
  const riyourshaData = local.usingSlipInfo.riyourshaData[0]
  // 利用者負担_保険/事業の合計
  const cHutanH = getUsingSlipOtherTableTotal(Or36647Const.KEY_C_HUTAN_H)
  // 利用者負担_全額負担の合計
  const hutanJ = getUsingSlipOtherTableTotal(Or36647Const.KEY_HUTAN_J)
  // 希望額 <> NULL、かつ、(利用者負担_保険/事業の合計 + 利用者負担_全額負担の合計) > 希望額の場合
  if (
    !isEmpty(riyourshaData.dmyKibouGaku) &&
    cHutanH + hutanJ > parseInt(riyourshaData.dmyKibouGaku)
  ) {
    // 計_利用者負担_保険/事業の色は「FF00FF」を設定
    color = Or36647Const.COLOR_RED
  }
  return color
})

// 種類別支給限度額欄 超過（合計）
const svOverTotal = computed(() => {
  let total = 0
  const syuruiGendoData = local.usingSlipInfo.syuruiGendoData[0]
  for (let i = 1; i <= 12; i++) {
    const value = syuruiGendoData[(Or36647Const.PREFIX_SVOVER + i) as keyof typeof syuruiGendoData]
    total += parseInt(value)
  }
  return total
})

// 利用料集計欄一覧
const riyouryouList = computed(() => {
  return local.riyouryouList.filter(
    (item) => item.updateKbn !== Or36647Const.UPDATE_CATEGORY_DELETE
  )
})

//社福軽減集計欄一覧
const syafukuList = computed(() => {
  return local.syafukuList.filter((item) => item.updateKbn !== Or36647Const.UPDATE_CATEGORY_DELETE)
})

// 確認ダイアログ
const showDialogOr21814 = computed(() => {
  // Or26814のダイアログ開閉状態
  return Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen ?? false
})

// 警告ダイアログ
const showDialogOr21815 = computed(() => {
  // Or26815のダイアログ開閉状態
  return Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen ?? false
})

// 週間計画取り込み画面
const showDialogOr26817 = computed(() => {
  // Or26817のダイアログ開閉状態
  return Or26817Logic.state.get(or26817.value.uniqueCpId)?.isOpen ?? false
})

// 利用票別表メンテナンス
const showDialogOr26828 = computed(() => {
  // Or26828のダイアログ開閉状態
  return Or26828Logic.state.get(or26828.value.uniqueCpId)?.isOpen ?? false
})

// 福祉用具貸与単位数一括設定
const showDialogOr30707 = computed(() => {
  // Or30707のダイアログ開閉状態
  return Or30707Logic.state.get(or30707.value.uniqueCpId)?.isOpen ?? false
})

// 有効期間外サービス検索
const showDialogOr27736 = computed(() => {
  // Or00100のダイアログ開閉状態
  return Or27736Logic.state.get(or27736.value.uniqueCpId)?.isOpen ?? false
})

// 利用票・別表画面で警告情報ダイアログ表示フラグ
const showDialogOr10923 = computed(() => {
  // Or10923のダイアログ開閉状態
  return Or10923Logic.state.get(or10923.value.uniqueCpId)?.isOpen ?? false
})

// 日割利用期間ダイアログ表示フラグ
const showDialogOr07212 = computed(() => {
  // Or07212のダイアログ開閉状態
  return Or07212Logic.state.get(or07212.value.uniqueCpId)?.isOpen ?? false
})

// 保険者選択ダイアログ表示フラグ
const showDialogOr27635 = computed(() => {
  // Or27635のダイアログ開閉状態
  return Or27635Logic.state.get(or27635.value.uniqueCpId)?.isOpen ?? false
})

// 利用票予定→実績変換ダイアログ表示フラグ
const showDialogOr35687 = computed(() => {
  // Or35687のダイアログ開閉状態
  return Or35687Logic.state.get(or35687.value.uniqueCpId)?.isOpen ?? false
})

// カレンダー入力ダイアログ表示フラグ
const showDialogOr27016 = computed(() => {
  // Or27016のダイアログ開閉状態
  return Or27016Logic.state.get(or27016.value.uniqueCpId)?.isOpen ?? false
})

// 計画複写ダイアログ表示フラグ
const showDialogOr35745 = computed(() => {
  // Or27016のダイアログ開閉状態
  return Or35745Logic.state.get(or35745.value.uniqueCpId)?.isOpen ?? false
})

// 希望負担額登録ダイアログ表示フラグ
const showDialogOr00233 = computed(() => {
  // Or00233のダイアログ開閉状態
  return Or00233Logic.state.get(or00233.value.uniqueCpId)?.isOpen ?? false
})

// 横出しサービス単位再設定ダイアログ表示フラグ
const showDialogOr27943 = computed(() => {
  // Or27943のダイアログ開閉状態
  return Or27943Logic.state.get(or27943.value.uniqueCpId)?.isOpen ?? false
})

// 確認画面ダイアログ表示フラグ
const showDialogOr27852 = computed(() => {
  // Or28824のダイアログ開閉状態
  return Or27852Logic.state.get(or27852.value.uniqueCpId)?.isOpen ?? false
})

// 日割算定確認ダイアログ表示フラグ
const showDialogOr35957 = computed(() => {
  // Or35957のダイアログ開閉状態
  return Or35957Logic.state.get(or35957.value.uniqueCpId)?.isOpen ?? false
})

// シミュレーション雛形選択ダイアログ表示フラグ
const showDialogOr28534 = computed(() => {
  // Or28534のダイアログ開閉状態
  return Or28534Logic.state.get(or28534.value.uniqueCpId)?.isOpen ?? false
})

// 特別指示期間ダイアログ表示フラグ
const showDialogOr28334 = computed(() => {
  // Or28334のダイアログ開閉状態
  return Or28334Logic.state.get(or28334.value.uniqueCpId)?.isOpen ?? false
})

// 短期退所日登録ダイアログ表示フラグ
const showDialogOr26835 = computed(() => {
  // Or26835のダイアログ開閉状態
  return Or26835Logic.state.get(or26835.value.uniqueCpId)?.isOpen ?? false
})

// 総合事業サービス単位再設定ダイアログ表示フラグ
const showDialogOr27946 = computed(() => {
  // Or10558のダイアログ開閉状態
  return Or27946Logic.state.get(or27946.value.uniqueCpId)?.isOpen ?? false
})

// 社福軽減登録ダイアログ表示フラグ
const showDialogOr26676 = computed(() => {
  // Or26676のダイアログ開閉状態
  return Or26676Logic.state.get(or26676.value.uniqueCpId)?.isOpen ?? false
})

// 認定期間中の短期入所利用日数ダイアログ表示フラグ
const showDialogOr27856 = computed(() => {
  // Or28824のダイアログ開閉状態
  return Or27856Logic.state.get(or27856.value.uniqueCpId)?.isOpen ?? false
})

// 備考欄画面ダイアログ表示フラグ
const showDialogOr29139 = computed(() => {
  // Or29139のダイアログ開閉状態
  return Or29139Logic.state.get(or29139.value.uniqueCpId)?.isOpen ?? false
})

// 印刷設定ダイアログ表示フラグ
const showDialogOr57151 = computed(() => {
  // Or00100のダイアログ開閉状態
  return Or57151Logic.state.get(or57151.value.uniqueCpId)?.isOpen ?? false
})

/**************************************************
 * ウォッチャー
 **************************************************/
/**
 * 画面メニューのイベントを監視
 */
watch(
  () => Or11871Logic.event.get(or11871.value.uniqueCpId),
  async (newValue) => {
    if (newValue === undefined) {
      return
    }
    // 「保存ボタン」押下
    if (newValue.saveEventFlg) {
      // 保存処理を行う
      await usingSlipSave()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { saveEventFlg: false },
      })
    }
    // 「印刷ボタン」押下
    if (newValue.printEventFlg) {
      // 印刷処理を行う
      await usingSlipPrint()
      Or11871Logic.event.set({
        uniqueCpId: or11871.value.uniqueCpId,
        events: { printEventFlg: false },
      })
    }
  }
)

/**
 * タブ切替
 */
watch(
  () => local.mo00043.id,
  async (newValue) => {
    // 「利用票」タブ押下
    if (newValue === Or36647Const.TAB_ID_USING_SLIP) {
      // 画面項目の値を再設定する
      await useSlipInitInfoSelect()
    } else {
      // 「利用票別表」タブ押下
      // 画面入力データの変更がある場合
      if (isEdit.value) {
        // 再計算処理
        await recompute()
        // 新規計算フラグを「false」に設定する
        newComputeFlg.value = false
      }
      // 画面項目の値を再設定する
      await useSlipInitInfoSelect()
    }
  }
)

/**
 * 「事業所選択」閉じる
 */
watch(
  () => Or41179Logic.data.get(or41179.value.uniqueCpId)?.modelValue,
  async (newValue, oldValue) => {
    if (!isEmpty(oldValue)) {
      // 利用票情報情報取得
      await useSlipInitInfoSelect()
    }
  }
)

/**
 * 「提供年月」変更
 */
watch(
  () => local.offeryYearMonth,
  async () => {
    // 画面入力データに変更がある場合
    if (isEdit.value) {
      // 複写
      await usingSlipCopy()
    } else {
      // 利用票情報情報取得
      await useSlipInitInfoSelect()
    }
  }
)

/**
 * 「予定済」チェックボックス選択値変更
 */
watch(
  () => local.inputWorkSchedule,
  async (newValue) => {
    // ONの場合
    if (newValue.modelValue && local.inputWorkAchievements.modelValue) {
      // 利用票明細情報.「予」ボタンを選択する
      local.usingSlipList.forEach((item) => (item.yoteiZumiFlg.modelValue = newValue.modelValue))
    } else {
      // OFFの場合
      // 利用者情報.実績済みフラグ = 1の場合
      if (local.inputWorkAchievements.modelValue) {
        await openConfirmDialog(t('message.i-cmn-10067'))
      }
      local.inputWorkAchievements.modelValue = true
      // 利用票明細情報.「予」ボタンをクリアする
      local.usingSlipList.forEach((item) => (item.jissekiZumiFlg.modelValue = newValue.modelValue))
    }
  }
)

/**
 * 「実績済」チェックボックス選択値変更
 */
watch(
  () => local.inputWorkAchievements,
  async (newValue) => {
    // ONの場合
    if (newValue.modelValue) {
      // 予定済みフラグ = 0の場合
      if (local.inputWorkSchedule.modelValue) {
        await openConfirmDialog(t('message.i-cmn-10068'))
        local.inputWorkAchievements.modelValue = false
      } else if (local.confirmFlg) {
        // 確定済みフラグ = 1の場合
        await openConfirmDialog(t('message.i-cmn-11357'))
        local.inputWorkAchievements.modelValue = false
        // 利用票明細情報.「実」ボタンを選択する
        local.usingSlipList.forEach((item) => (item.jissekiZumiFlg.modelValue = true))
      }
    } else {
      // OFFの場合
      // 確定済みフラグ = 1の場合
      if (local.confirmFlg) {
        await openConfirmDialog(t('message.i-cmn-11357'))
        local.inputWorkAchievements.modelValue = true
      }
      // 利用票明細情報.「実」ボタンをクリアする
      local.usingSlipList.forEach((item) => (item.jissekiZumiFlg.modelValue = false))
    }
  }
)

/**
 * 計算ラジオボタン選択値変更
 */
watch(
  () => local.compute,
  async (newValue, oldValue) => {
    if (isEmpty(oldValue)) {
      return false
    }
    // 「予定」ラジオボタンを選択
    if (newValue === Or36647Const.COMPUTE_SCHED) {
      // 利用者情報または利用票明細情報が変更した場合
      if (isEdit.value) {
        await openConfirmDialog(t('message.i-cmn-11358'))
      } else {
        // 再計算処理
        await recompute()
        // 新規計算フラグを「false」に設定する
        newComputeFlg.value = false
      }
    } else {
      // 「実績」ラジオボタンを選択
      // 利用者情報または利用票明細情報が変更した場合
      if (isEdit.value) {
        await openConfirmDialog(t('message.i-cmn-11359'))
      } else {
        // 再計算処理
        await recompute()
      }
    }
  }
)

/**
 * 計画複写閉じる
 */
watch(
  () => Or35745Logic.state.get(or35745.value.uniqueCpId)?.isOpen,
  async (newValue) => {
    if (!newValue) {
      const copyFlg = Or35745Logic.state.get(or35745.value.uniqueCpId)?.inParam?.copyFlg
      // 複写結果 = 1 の場合
      if (copyFlg === Or36647Const.USING_SLIP_COPY_RESULT_1) {
        // 利用票の初期情報を取得する。
        await useSlipInitInfoSelect()
      }
    }
  }
)

/**
 * 利用票別表メンテナンス閉じる
 */
watch(
  () => Or26828Logic.state.get(or26828.value.uniqueCpId)?.isOpen,
  async (newValue) => {
    if (!newValue) {
      // 再検索処理
      await useSlipInitInfoSelect()
      // 別表明細情報がなし場合
      if (local.usingSlipOthrtTableList.length === 0) {
        await openConfirmDialog(t('message.i-cmn-11344'))
      }
    }
  }
)

/**
 * 福祉用具貸与単位数一括設定閉じる
 */
watch(
  () => Or30707Logic.state.get(or30707.value.uniqueCpId)?.isOpen,
  async (newValue) => {
    if (!newValue) {
      // 再検索処理
      await useSlipInitInfoSelect()
    }
  }
)

/**
 * 横出しサービス単位再設定閉じる
 */
watch(
  () => Or27943Logic.state.get(or27943.value.uniqueCpId)?.isOpen,
  async (newValue) => {
    if (!newValue) {
      // 再検索処理
      await useSlipInitInfoSelect()
    }
  }
)

/**
 * 有効期間外サービス検索閉じる
 */
watch(
  () => Or27736Logic.state.get(or27736.value.uniqueCpId)?.isOpen,
  async (newValue) => {
    if (!newValue) {
      // 再検索処理
      await useSlipInitInfoSelect()
    }
  }
)

/**
 * 日割利用期間閉じる
 */
watch(
  () => Or07212Logic.state.get(or07212.value.uniqueCpId)?.isOpen,
  async (newValue) => {
    if (!newValue) {
      // 再検索処理
      await useSlipInitInfoSelect()
    }
  }
)

/**
 * 週間取込閉じる
 */
watch(
  () => Or26817Logic.state.get(or26817.value.uniqueCpId)?.isOpen,
  async (newValue) => {
    if (!newValue) {
      // 週間取込後の処理を行う
      const inputData: UseSlipInfoWeekImportSelectInEntity = {
        // 提供年月：画面.提供年月
        teikyouYm: local.offeryYearMonth.value,
        // 提供月（日）：画面退避情報.提供月（日）
        teikyouYmD: local.usingSlipInfo.teikyouYmD,
        // 利用票明細情報：画面.利用票明細情報
        riyouList: getRiyouList(),
        // 利用票画面処理用構造体：画面退避情報.利用票画面処理用構造体
        riyouProcessObject: local.usingSlipInfo.riyouProcessObject,
      }
      const response: UseSlipInfoWeekImportSelectOutEntity = await ScreenRepository.select(
        'useSlipInfoWeekImportSelect',
        inputData
      )
      // 入力フォームの各項目に返却情報を設定する。
      setUsingSlipInputForm(response.data.riyou704Info[0].riyouList)
    }
  }
)

/**
 * 希望負担閉じる
 */
watch(
  () => Or00233Logic.state.get(or00233.value.uniqueCpId)?.isOpen,
  async (newValue) => {
    if (!newValue) {
      // 利用者情報の希望額を取得する
      const inputData: UseSlipInfoHopeBurdenSelectInEntity = {
        // 利用者ID：親画面.利用者ID
        userId: localOneway.or36647.userId,
      }
      const response: UseSlipInfoHopeBurdenSelectOutEntity = await ScreenRepository.select(
        'useSlipInfoHopeBurdenSelect',
        inputData
      )
      // 画面.希望額 = 返却情報.希望額
      localOneway.mo01338HopeAmountOneway.value = getDisplayAmount(response.data.dmyKibouGaku)
    }
  }
)

// 「行追加ボタン」押下
watch(
  () => Or21735Logic.event.get(or21735.value.uniqueCpId),
  async () => {
    // 利用者情報.実績済みフラグ = 1の場合
    if (local.inputWorkAchievements.modelValue) {
      await openConfirmDialog(t('message.i-cmn-10681'))
      // 処理終了
      return false
    }
    // 利用者情報.予定済みフラグ = 1の場合
    if (local.inputWorkSchedule.modelValue) {
      await openConfirmDialog(t('message.i-cmn-10682'))
      // 処理終了
      return false
    }
    // TODO GUI01151_サービス選択画面をポップアップで起動する
  }
)

// 「行複写ボタン」押下
watch(
  () => Or21737Logic.event.get(or21737.value.uniqueCpId),
  async () => {
    // 利用者情報.実績済みフラグ = 1の場合
    if (local.inputWorkAchievements.modelValue) {
      await openConfirmDialog(t('message.i-cmn-10681'))
      // 処理終了
      return false
    }
    // 利用者情報.予定済みフラグ = 1の場合
    if (local.inputWorkSchedule.modelValue) {
      await openConfirmDialog(t('message.i-cmn-10682'))
      // 処理終了
      return false
    }
    if (local.usingSlipList.length > 0) {
      let rowCount = 1
      const selectedItem = local.usingSlipList[local.selectedUsingSlipItemIndex]
      const result = local.usingSlipList.filter(
        (item, index) =>
          index !== local.selectedUsingSlipItemIndex && item.oyaLineNo === selectedItem.oyaLineNo
      )
      // 選択行の親レコード番号 <> 0 且つ 利用票明細情報には、選択行の親レコード番号と同じデータがある場合
      if (selectedItem.oyaLineNo !== Or36647Const.DEFAULT_PARENT_NO && result.length > 0) {
        // 件数 = 選択行の親レコード番号と同じデータの件数
        rowCount = result.length
      }
      // 件数 > 1の場合
      if (rowCount > 1) {
        // GUI01175_確認ダイアログをポップアップで起動する
        Or27852Logic.state.set({
          uniqueCpId: or27852.value.uniqueCpId,
          state: { isOpen: true },
        })
      } else {
        // 件数 <= 1の場合
        // 行複写処理
        await lineCopy(Or36647Const.PROCESS_FLG_1)
      }
    }
  }
)

// 「行削除ボタン」押下
watch(
  () => Or21738Logic.event.get(or21738.value.uniqueCpId),
  async () => {
    // 利用者情報.実績済みフラグ = 1の場合
    if (local.inputWorkAchievements.modelValue) {
      await openConfirmDialog(t('message.i-cmn-10681'))
      // 処理終了
      return false
    }
    // 利用者情報.予定済みフラグ = 1の場合
    if (local.inputWorkSchedule.modelValue) {
      await openConfirmDialog(t('message.i-cmn-10682'))
      // 処理終了
      return false
    }
    // 画面.利用者情報.予定済みフラグ = 1 or 削除の行.予定済みフラグ = 1の場合
    const selectedItem = local.usingSlipList[local.selectedUsingSlipItemIndex]
    if (local.inputWorkSchedule.modelValue || selectedItem.yoteiZumiFlg.modelValue) {
      await openConfirmDialog(t('message.i-cmn-10066'))
      // 処理終了
      return false
    }
    // 行削除の前のチェック処理を行う
    const deleteBefSelectInputData: UseSlipInfoDeleteBefSelectInEntity = {
      // 利用票明細情報：画面.利用票明細情報
      riyouList: getRiyouList(),
      // 提供年月：画面.提供年月
      teikyouYm: local.offeryYearMonth.value,
      // 削除行idx：選択された利用票明細情報のインデックス
      delIdx: local.selectedUsingSlipItemIndex.toString(),
    }
    const deleteBefSelectRet: UseSlipInfoDeleteBefSelectOutEntity = await ScreenRepository.select(
      'useSlipInfoDeleteBefSelect',
      deleteBefSelectInputData
    )
    const ret = await openConfirmDialog(
      t('message.i-cmn-11290', [
        deleteBefSelectRet.data.parentFlag === Or36647Const.PARENT_FLAG_1
          ? t('label.now-and-related-row')
          : t('label.now-row'),
      ]),
      true
    )
    if (ret === Or36647Const.CONFIRM_BTN_YES) {
      // 利用票画面行削除後処理を行う
      const deleteRowAftProcSelecInputData: UseSlipInfoDeleteRowAftProcSelectInEntity = {
        // 利用票明細情報：画面.利用票明細情報
        riyouList: getRiyouList(),
        // 利用票画面処理用構造体：画面退避情報.利用票画面処理用構造体
        riyouProcessObject: local.usingSlipInfo.riyouProcessObject,
      }
      const deleteRowAftProcSelecRet: UseSlipInfoDeleteRowAftProcSelectOutEntity =
        await ScreenRepository.select(
          'useSlipInfoDeleteRowAftProcSelect',
          deleteRowAftProcSelecInputData
        )
      // 入力フォームの各項目へ設定する
      setUsingSlipInputForm(deleteRowAftProcSelecRet.data.riyou895Info[0].riyouList)
    }
  }
)

/**
 * 保険者選択閉じる
 */
watch(
  () => Or27635Logic.state.get(or27635.value.uniqueCpId)?.isOpen,
  async (newValue) => {
    if (!newValue) {
      // 返却情報.提供年月 <> null or "" 且つ 返却情報.提供月（日）<> 画面退避情報.提供月（日）の場合
      if (
        !isEmpty(local.or27635.modifiedDay) &&
        local.or27635.modifiedDay !== local.usingSlipInfo.teikyouYmD
      ) {
        // 画面退避情報.提供月（日） = 返却情報.提供月（日）
        local.usingSlipInfo.teikyouYmD = local.or27635.modifiedDay
        // 利用票・別表履歴を検索する
        const inputData: UseSlipInfoPersonInsuredPersonSelectInEntity = {
          // 事業所ＩＤ：親画面.事業所ID
          svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
          // 利用者ＩＤ：親画面.利用者ＩＤ
          userId: localOneway.or36647.userId,
          // システム年月：画面.提供年月
          teikyouYm: local.offeryYearMonth.value,
          // 利用票画面処理用構造体：画面退避情報.利用票画面処理用構造体
          riyouProcessObject: local.usingSlipInfo.riyouProcessObject,
          // 事業所情報リスト
          jigyoList: local.usingSlipInfo.jigyoList,
        }
        const response: UseSlipInfoPersonInsuredPersonSelectOutEntity =
          await ScreenRepository.select('useSlipInfoPersonInsuredPersonSelect', inputData)
        // 入力フォームの各項目へ設定する
        setInputForm(response.data.riyouInfo[0])
      }
    }
  }
)

/**
 * カレンダー入力画面閉じる
 */
watch(
  () => Or27016Logic.state.get(or27016.value.uniqueCpId)?.isOpen,
  async (newValue) => {
    if (!newValue) {
      // 返却情報.提供年月 = "9999/99"の場合-
      if (local.or27016.provideYm === Or36647Const.MAX_YEAR_MONTH) {
        // 返却情報.利用票明細情報から予定回数と実績回数を取得して、画面.利用票明細情報にセットする
        for (let i = 0; i < local.or27016.meisaiList.length; i++) {
          const meisaiItem = local.or27016.meisaiList[i]
          const riyouItem = local.usingSlipList[i]
          riyouItem.yDay01.value = meisaiItem.yday01
          riyouItem.yDay02.value = meisaiItem.yday02
          riyouItem.yDay03.value = meisaiItem.yday03
          riyouItem.yDay04.value = meisaiItem.yday04
          riyouItem.yDay05.value = meisaiItem.yday05
          riyouItem.yDay06.value = meisaiItem.yday06
          riyouItem.yDay07.value = meisaiItem.yday07
          riyouItem.yDay08.value = meisaiItem.yday08
          riyouItem.yDay09.value = meisaiItem.yday09
          riyouItem.yDay10.value = meisaiItem.yday10
          riyouItem.yDay11.value = meisaiItem.yday11
          riyouItem.yDay12.value = meisaiItem.yday12
          riyouItem.yDay13.value = meisaiItem.yday13
          riyouItem.yDay14.value = meisaiItem.yday14
          riyouItem.yDay15.value = meisaiItem.yday15
          riyouItem.yDay16.value = meisaiItem.yday16
          riyouItem.yDay17.value = meisaiItem.yday17
          riyouItem.yDay18.value = meisaiItem.yday18
          riyouItem.yDay19.value = meisaiItem.yday19
          riyouItem.yDay20.value = meisaiItem.yday20
          riyouItem.yDay21.value = meisaiItem.yday21
          riyouItem.yDay22.value = meisaiItem.yday22
          riyouItem.yDay23.value = meisaiItem.yday23
          riyouItem.yDay24.value = meisaiItem.yday24
          riyouItem.yDay25.value = meisaiItem.yday25
          riyouItem.yDay26.value = meisaiItem.yday26
          riyouItem.yDay27.value = meisaiItem.yday27
          riyouItem.yDay28.value = meisaiItem.yday28
          riyouItem.yDay29.value = meisaiItem.yday29
          riyouItem.yDay30.value = meisaiItem.yday30
          riyouItem.yDay31.value = meisaiItem.yday31
          riyouItem.jDay01.value = meisaiItem.jday01
          riyouItem.jDay02.value = meisaiItem.jday02
          riyouItem.jDay03.value = meisaiItem.jday03
          riyouItem.jDay04.value = meisaiItem.jday04
          riyouItem.jDay05.value = meisaiItem.jday05
          riyouItem.jDay06.value = meisaiItem.jday06
          riyouItem.jDay07.value = meisaiItem.jday07
          riyouItem.jDay08.value = meisaiItem.jday08
          riyouItem.jDay09.value = meisaiItem.jday09
          riyouItem.jDay10.value = meisaiItem.jday10
          riyouItem.jDay11.value = meisaiItem.jday11
          riyouItem.jDay12.value = meisaiItem.jday12
          riyouItem.jDay13.value = meisaiItem.jday13
          riyouItem.jDay14.value = meisaiItem.jday14
          riyouItem.jDay15.value = meisaiItem.jday15
          riyouItem.jDay16.value = meisaiItem.jday16
          riyouItem.jDay17.value = meisaiItem.jday17
          riyouItem.jDay18.value = meisaiItem.jday18
          riyouItem.jDay19.value = meisaiItem.jday19
          riyouItem.jDay20.value = meisaiItem.jday20
          riyouItem.jDay21.value = meisaiItem.jday21
          riyouItem.jDay22.value = meisaiItem.jday22
          riyouItem.jDay23.value = meisaiItem.jday23
          riyouItem.jDay24.value = meisaiItem.jday24
          riyouItem.jDay25.value = meisaiItem.jday25
          riyouItem.jDay26.value = meisaiItem.jday26
          riyouItem.jDay27.value = meisaiItem.jday27
          riyouItem.jDay28.value = meisaiItem.jday28
          riyouItem.jDay29.value = meisaiItem.jday29
          riyouItem.jDay30.value = meisaiItem.jday30
          riyouItem.jDay31.value = meisaiItem.jday31
        }
      }
      // 利用票画面行削除後処理を行う
      const inputData: UseSlipInfoDeleteRowAftProcSelectInEntity = {
        // 利用票明細情報：画面.利用票明細情報
        riyouList: getRiyouList(),
        // 利用票画面処理用構造体：画面退避情報.利用票画面処理用構造体
        riyouProcessObject: local.usingSlipInfo.riyouProcessObject,
      }
      const response: UseSlipInfoDeleteRowAftProcSelectOutEntity = await ScreenRepository.select(
        'useSlipInfoDeleteRowAftProcSelect',
        inputData
      )
      // 入力フォームの各項目へ設定する
      setUsingSlipInputForm(response.data.riyou895Info[0].riyouList)
    }
  }
)

/**
 * 確認画面閉じる
 */
watch(
  () => Or27852Logic.state.get(or27852.value.uniqueCpId)?.isOpen,
  async (newValue) => {
    if (!newValue) {
      // 処理フラグ <> 3の場合
      if (local.or27852.processFlg !== Or36647Const.PROCESS_FLG_3) {
        // 行複写処理
        await lineCopy(local.or27852.processFlg)
      }
    }
  }
)

/**
 * 日割算定確認画面閉じる
 */
watch(
  () => Or35957Logic.state.get(or35957.value.uniqueCpId)?.isOpen,
  async (newValue) => {
    if (!newValue) {
      // 返却情報.戻り値 = 1111の場合
      if (local.or35957.retValue === Or36647Const.RETURN_VALUE_1111) {
        // 日割算定確認処理後の算定確認情報を再検索する
        const inputData: UseSlipInfoDailyRateCalculationSelectInEntity = {
          // 事業所ＩＤ：親画面.事業所ID
          svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
          // 利用者ＩＤ：親画面.利用者ID
          userId: localOneway.or36647.userId,
          // 提供年月：画面.提供年月
          teikyouYm: local.offeryYearMonth.value,
          // 提供月（日）：画面退避情報.提供月（日）
          teikyouDay: local.usingSlipInfo.teikyouYmD,
        }
        const response: UseSlipInfoDailyRateCalculationSelectOutEntity =
          await ScreenRepository.select('useSlipInfoDailyRateCalculationSelect', inputData)
        // 画面退避情報.算定確認情報 = AC013-3の返却情報.算定確認情報
        local.usingSlipInfo.riyouKakuninList = response.data.riyouKakuninList
      }
    }
  }
)

/**
 * シミュレーション雛形選択画面閉じる
 */
watch(
  () => Or28534Logic.state.get(or28534.value.uniqueCpId)?.isOpen,
  async (newValue) => {
    if (!newValue) {
      // 返却情報.利用者ID = nullの場合
      if (isEmpty(local.or28534.userid)) {
        // 処理終了
        return false
      }
      // 雛形取込後の画面表示処理を行う
      const inputData: UseSlipInfoTmpImportSelectInEntity = {
        svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
        userId: localOneway.or36647.userId,
        teikyouYm: local.offeryYearMonth.value,
        teikyouYmD: local.usingSlipInfo.teikyouYmD,
        lineNo: local.selectedUsingSlipItemIndex.toString(),
        week: local.usingSlipInfo.week,
        weekColor: local.usingSlipInfo.weekColor,
        weekBgColor: local.usingSlipInfo.weekBgColor,
        riyouList: getRiyouList(),
        riyouProcessObject: local.usingSlipInfo.riyouProcessObject,
        jissekiZumiFlg: local.inputWorkAchievements
          ? Or36647Const.ACHV_FLG_OK
          : Or36647Const.ACHV_FLG_NG,
      }
      const response: UseSlipInfoTmpImportSelectOutEntity = await ScreenRepository.select(
        'useSlipInfoTmpImportSelect',
        inputData
      )
      // 再計算処理
      await recompute()
      // 新規計算フラグを「false」に設定する
      newComputeFlg.value = false
      // 入力フォームの各項目に返却情報を設定する。
      setUsingSlipInputForm(response.data.riyou705Info[0].riyouList)
    }
  }
)

/**
 * 利用料集計欄一覧  「行追加ボタン」押下
 */
watch(
  () => Or21735Logic.event.get(or21735_2.value.uniqueCpId),
  () => {
    // 利用料集計欄を行追加する
    const riyouryou = {
      svJigyoId: {
        modelValue: '',
      },
      riyouryouItem: {
        value: '',
      },
      riyouryouTanka: {
        value: '',
      },
      riyouryouSu: {
        value: '',
      },
      riyouryouKingaku: Or36647Const.DEFAULT_AMOUNT,
      updateKbn: Or36647Const.UPDATE_CATEGORY_NEW,
    } as UsingSlipRiyouryou
    local.riyouryouList.push(riyouryou)
    local.selectedRiyouryouItemIndex = riyouryouList.value.length - 1
  }
)

/**
 * 利用料集計欄一覧  「行削除ボタン」押下
 */
watch(
  () => Or21738Logic.event.get(or21738_2.value.uniqueCpId),
  async () => {
    const ret = await openConfirmDialog(t('message.i-cmn-10287'), true)
    if (ret === Or36647Const.CONFIRM_BTN_YES) {
      // 利用料集計欄を行削除する
      const selectedItem = riyouryouList.value[local.selectedRiyouryouItemIndex]
      selectedItem.updateKbn = Or36647Const.UPDATE_CATEGORY_DELETE
      if (local.selectedRiyouryouItemIndex >= riyouryouList.value.length - 1) {
        local.selectedRiyouryouItemIndex = riyouryouList.value.length - 1
      } else if (local.selectedRiyouryouItemIndex > 0) {
        local.selectedRiyouryouItemIndex--
      }
      // 利用料合計
      const riyouryouSum = riyouryouList.value.reduce(
        (sum, item) => sum + parseInt(item.riyouryouKingaku),
        0
      )
      local.usingSlipInfo.riyouryouSum = riyouryouSum.toString()
    }
  }
)

/**
 * 社福軽減集計欄一覧  「行追加ボタン」押下
 */
watch(
  () => Or21735Logic.event.get(or21735_3.value.uniqueCpId),
  () => {
    // 利用料集計欄を行追加する
    const syafuku = {
      svJigyoId: {
        modelValue: '',
      },
      syafukuObj: {
        value: '',
      },
      syafukuRitu: {
        value: '',
      },
      syafukuGaku: Or36647Const.DEFAULT_AMOUNT,
      updateKbn: Or36647Const.UPDATE_CATEGORY_NEW,
      sakuKbn: Or36647Const.SAKU_KBN_0,
    } as UsingSlipSyafuku
    local.syafukuList.push(syafuku)
    local.selectedSyafukuItemIndex = syafukuList.value.length - 1
  }
)

/**
 * 社福軽減集計欄一覧  「行削除ボタン」押下
 */
watch(
  () => Or21738Logic.event.get(or21738_3.value.uniqueCpId),
  async () => {
    const selectedItem = syafukuList.value[local.selectedSyafukuItemIndex]
    // 選択行.作成区分 = 0の場合
    if (selectedItem.sakuKbn === Or36647Const.SAKU_KBN_0) {
      await openConfirmDialog(t('message.i-cmn-10074'))
    } else {
      const ret = await openConfirmDialog(t('message.i-cmn-10287'), true)
      if (ret === Or36647Const.CONFIRM_BTN_YES) {
        // 社福軽減集計欄を行削除する
        selectedItem.updateKbn = Or36647Const.UPDATE_CATEGORY_DELETE
        if (local.selectedSyafukuItemIndex >= syafukuList.value.length - 1) {
          local.selectedSyafukuItemIndex = syafukuList.value.length - 1
        } else if (local.selectedSyafukuItemIndex > 0) {
          local.selectedSyafukuItemIndex--
        }
        // 利用料合計
        const syafukuSum = syafukuList.value.reduce(
          (sum, item) => sum + parseInt(item.syafukuGaku),
          0
        )
        local.usingSlipInfo.syafukuSum = syafukuSum.toString()
      }
    }
  }
)

/**
 * 認定期間中の短期入所利用日数画面閉じる
 */
watch(
  () => Or27856Logic.state.get(or27856.value.uniqueCpId)?.isOpen,
  (newValue) => {
    if (!newValue) {
      // 短期入所利用日数.前月迄 = 短期入所利用日数情報.前月までの利用日数
      local.usingSlipInfo.sumData[0].zenGetuSum = local.or27856.outputItem.upToPrevMonth
      // 短期入所利用日数.当月 = 短期入所利用日数情報.当月の利用日数
      local.usingSlipInfo.sumData[0].cmnTucPlanTTougetu = local.or27856.outputItem.planForThisMonth
      // 短期入所利用日数.累積 = 短期入所利用日数情報.累計利用日数
      local.usingSlipInfo.sumData[0].computeRuikeiSum = local.or27856.outputItem.accumulation
    }
  }
)

/**
 * 備考欄画面閉じる
 */
watch(
  () => Or29139Logic.state.get(or29139.value.uniqueCpId)?.isOpen,
  async (newValue) => {
    if (!newValue) {
      // 利用票情報情報取得
      await useSlipInitInfoSelect()
    }
  }
)

/**
 * 予実変換画面閉じる
 */
watch(
  () => Or35687Logic.state.get(or35687.value.uniqueCpId)?.isOpen,
  async (newValue) => {
    if (!newValue) {
      if (local.or35687.processFlg === Or36647Const.PROCESS_FLG_1) {
        // 予実変換後画面の再設定処理
        const inputData: UseSlipInfoPredictionChangeBtnAftProcSelectInEntity = {
          // 利用票明細情報：利用票明細情報
          riyouList: getRiyouList(),
          // 提供年月：画面.提供年月
          teikyouYm: local.offeryYearMonth.value,
          // 提供月（日）：画面退避情報.提供月（日）
          teikyouYmD: local.usingSlipInfo.teikyouYmD,
          // 利用者情報：画面退避情報.利用者情報
          riyourshaData: local.usingSlipInfo.riyourshaData,
        }
        const response: UseSlipInfoPredictionChangeBtnAftProcSelectOutEntity =
          await ScreenRepository.select('useSlipInfoPredictionChangeBtnAftProcSelect', inputData)
        // 入力フォームの各項目へ設定する
        setUsingSlipInputForm(response.data.riyou894Info[0].riyouList)
      }
    }
  }
)

/**
 * 指定された日付文字列から曜日を取得する
 *
 * @param dateString - 日付を表す文字列（例: "YYYY-MM-DD"形式）
 *
 * @returns 曜日の文字列表現（例: "(月)"、"(火)"など）。無効な日付の場合、空文字を返す。
 */
const getDayOfWeek = (dateString: string): string => {
  const date = new Date(dateString)
  const daysOfWeek = [
    t('label.day-short-sunday'),
    t('label.day-short-monday'),
    t('label.day-short-tuesday'),
    t('label.day-short-wednesday'),
    t('label.day-short-thursday'),
    t('label.day-short-friday'),
    t('label.day-short-saturday'),
  ]

  if (isNaN(date.getTime())) {
    return ''
  }

  const dayIndex = date.getDay()
  return daysOfWeek[dayIndex]
}

/**
 * 年月の加減処理
 *
 * @param b - 加減フラグ
 */
const addMonth = (b: boolean) => {
  // YYYY/MM形式の文字列をDateオブジェクトに変換
  const [year, month] = local.offeryYearMonth.value.split('/').map(Number)
  const date = new Date(year, month - 1)

  if (b) {
    date.setMonth(date.getMonth() + 1)
  } else {
    date.setMonth(date.getMonth() - 1)
  }

  // 加減後の年月をYYYY/MM形式で返す
  const newYear = date.getFullYear()
  const newMonth = (date.getMonth() + 1).toString().padStart(2, '0')
  local.offeryYearMonth.value = `${newYear}/${newMonth}`
}

/**
 * 行選択
 *
 * @param index - 選択した行のindex
 */
const selectUsingSlipRow = (index: number) => {
  local.selectedUsingSlipItemIndex = index
}

/**
 * 列選択
 *
 * @param day - 選択した列のindex
 */
const selectColumn = (day: number) => {
  local.selectedColumnIndex = day
}

/**
 * 列選択クリア
 */
const cleanSelectedColumn = () => {
  local.selectedColumnIndex = 0
}

/**
 * 予定/実績背景色
 *
 * @param isChecked -予定済/実績済
 *
 * @param index -index
 */
const getZumiFlgBgColor = (isChecked: boolean, index: number) => {
  if (isChecked) {
    return Or36647Const.COLOR_FFC896
  } else if (local.selectedUsingSlipItemIndex === index) {
    return Or36647Const.COLOR_SELECTED_ROW
  } else {
    return Or36647Const.COLOR_FFFFFF
  }
}

/**
 * 予定/実施回数の背景色
 *
 * @param index - index
 *
 * @param day -day
 *
 * @param schedAchvFlg -予定/実績
 */
const getDayBgColor = (index: number, day: DayOfWeek, schedAchvFlg: string) => {
  // 行選択
  if (local.selectedUsingSlipItemIndex === index) {
    return Or36647Const.COLOR_SELECTED_ROW
  } else {
    // 列選択
    if (local.selectedColumnIndex === day.day) {
      return Or36647Const.COLOR_SELECTED_COL
    } else {
      if (hoverdIndex.value === index) {
        return ''
      } else {
        if (schedAchvFlg === Or36647Const.SCHED_FLG_OK) {
          return local.usingSlipList[index].dmyBgColorY[day.day - 1]
        } else {
          return local.usingSlipList[index].dmyBgColorJ[day.day - 1]
        }
      }
    }
  }
}

/**
 * 「修正」押下
 */
const editLine = async () => {
  // 利用者情報.実績済みフラグ = 1の場合
  if (local.inputWorkAchievements.modelValue) {
    await openConfirmDialog(t('message.i-cmn-11360'))
    // 処理終了
    return false
  }
  // 利用者情報.予定済みフラグ = 1の場合
  if (local.inputWorkSchedule.modelValue) {
    await openConfirmDialog(t('message.i-cmn-11361'))
    // 処理終了
    return false
  }
  // 利用票画面修正前処理を行う
  const inputData: UseSlipInfoEditRowBefProcSelectInEntity = {
    // 利用票明細情報：画面.利用票明細情報
    riyouList: getRiyouList(),
    // 提供年月：画面.提供年月
    teikyouYm: local.offeryYearMonth.value,
    // 削除行idx：選択された利用票明細情報のインデックス
    delIdx: local.selectedUsingSlipItemIndex.toString(),
  }
  const response: UseSlipInfoEditRowBefProcSelectOutEntity = await ScreenRepository.select(
    'useSlipInfoEditRowBefProcSelect',
    inputData
  )
  local.adsKihon = response.data.riyou1112Info[0].adsKihon
  // TODO GUI01151_サービス選択画面をポップアップで起動する
}

/**
 * 「予実変換」押下
 */
const schedAchvChange = async () => {
  // 利用者情報.実績済みフラグ = 1の場合
  if (local.inputWorkAchievements.modelValue) {
    await openConfirmDialog(t('message.i-cmn-10681'))
    // 処理終了
    return false
  }
  // 利用者情報.予定済みフラグ = 1の場合
  if (local.inputWorkSchedule.modelValue) {
    await openConfirmDialog(t('message.i-cmn-10682'))
    // 処理終了
    return false
  }
  local.or35687.usingSlipDetailInfo = local.usingSlipList
  localOneway.or35687Oneway = {
    targetRange: local.offeryYearMonth.value,
    selectedIndex: local.selectedUsingSlipItemIndex,
    startDay: '',
    endDay: '',
  } as Or35687OnewayType
  // GUI01156_利用票予定→実績変換画面をポップアップで起動する
  Or35687Logic.state.set({
    uniqueCpId: or35687.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「次の行選択」押下
 */
const nextLine = () => {
  // 選択した行のフォーカスを一つ下の行を選択する（フォーカスチェンジ）すでに最終行の場合、なにもしない
  if (local.selectedUsingSlipItemIndex + 1 < local.usingSlipList.length) {
    const selectedItem = local.usingSlipList[local.selectedUsingSlipItemIndex]
    // 選択の行の親レコード番号 = 0の場合
    if (selectedItem.oyaLineNo !== Or36647Const.DEFAULT_PARENT_NO) {
      // 親子まとめて繰り下げ
      const moveItemCount = local.usingSlipList.filter(
        (item) => item.oyaLineNo === selectedItem.oyaLineNo
      ).length
      const firstItemIndex = local.usingSlipList.findIndex(
        (item) => item.oyaLineNo === selectedItem.oyaLineNo
      )
      if (firstItemIndex + moveItemCount === local.usingSlipList.length) {
        return false
      }
      const moveItems = local.usingSlipList.slice(firstItemIndex, firstItemIndex + moveItemCount)
      const resultItems = local.usingSlipList.filter(
        (item) => !moveItems.map((move) => move.scode).includes(item.scode)
      )
      const targetIndex = firstItemIndex + moveItemCount - 1
      resultItems.splice(targetIndex, 0, ...moveItems)
      local.usingSlipList = resultItems
      local.selectedUsingSlipItemIndex = local.usingSlipList.findIndex(
        (item) => item.scode === selectedItem.scode
      )
    } else {
      // 1行のみ繰り下げ
      const moveItem = local.usingSlipList.splice(local.selectedUsingSlipItemIndex, 1)[0]
      const targetIndex = local.selectedUsingSlipItemIndex + 1
      local.usingSlipList.splice(targetIndex, 0, moveItem)
      local.selectedUsingSlipItemIndex++
    }
  }
}

/**
 * 「前の行選択」押下
 */
const preLine = () => {
  // 選択した行のフォーカスを一つ上の行を選択する（フォーカスチェンジ）すでに最初行の場合、なにもしない
  if (local.selectedUsingSlipItemIndex - 1 >= 0) {
    const selectedItem = local.usingSlipList[local.selectedUsingSlipItemIndex]
    // 選択の行の親レコード番号 = 0の場合
    if (selectedItem.oyaLineNo !== Or36647Const.DEFAULT_PARENT_NO) {
      // 親子まとめて繰り下げ
      const moveItemCount = local.usingSlipList.filter(
        (item) => item.oyaLineNo === selectedItem.oyaLineNo
      ).length
      const firstItemIndex = local.usingSlipList.findIndex(
        (item) => item.oyaLineNo === selectedItem.oyaLineNo
      )
      if (firstItemIndex === 0) {
        return false
      }
      const moveItems = local.usingSlipList.slice(firstItemIndex, firstItemIndex + moveItemCount)
      const resultItems = local.usingSlipList.filter(
        (item) => !moveItems.map((move) => move.scode).includes(item.scode)
      )
      const targetIndex = firstItemIndex - 1
      resultItems.splice(targetIndex, 0, ...moveItems)
      local.usingSlipList = resultItems
      local.selectedUsingSlipItemIndex = local.usingSlipList.findIndex(
        (item) => item.scode === selectedItem.scode
      )
    } else {
      // 1行のみ繰り下げ
      const moveItem = local.usingSlipList.splice(local.selectedUsingSlipItemIndex, 1)[0]
      const targetIndex = local.selectedUsingSlipItemIndex - 1
      local.usingSlipList.splice(targetIndex, 0, moveItem)
      local.selectedUsingSlipItemIndex = targetIndex
    }
  }
}

/**
 * 提供時間_開始/終了時間 表示／非表示
 *
 * @param time -時間
 */
const showOfferHoursTime = (time: string) => {
  return (
    time !== Or36647Const.SERVICE_TIME_99 &&
    time !== Or36647Const.SERVICE_TIME_88 &&
    time !== Or36647Const.SERVICE_TIME_77
  )
}

/**
 * 福祉用具貸与_予定/実績 表示／非表示
 *
 * @param item -利用票明細情報
 */
const showOfferHoursDropdown = (item: UsingSlipDetailInfo) => {
  return (
    item.svtype === Or36647Const.SERVICE_TYPECODE_17 ||
    item.svtype === Or36647Const.SERVICE_TYPECODE_67
  )
}

/**
 * 予定回数合計取得
 *
 * @param item -利用票明細情報
 */
const getSchedTotal = (item: UsingSlipDetailInfo): number => {
  // (利用票明細情報.サービス種別コード = "17" or 利用票明細情報.サービス種別コード = "67") and
  // (利用票明細情報.福祉用具貸与フラグ（予定） = null or 利用票明細情報.福祉用具貸与フラグ（予定） = 0 or 利用票明細情報.福祉用具貸与フラグ（予定） = 2)の場合
  if (
    (item.svtype === Or36647Const.SERVICE_TYPECODE_17 ||
      item.svtype === Or36647Const.SERVICE_TYPECODE_67) &&
    (isEmpty(item.yRentalF.modelValue) ||
      item.yRentalF.modelValue === Or36647Const.WELFARE_EQUIP_LENDING_0 ||
      item.yRentalF.modelValue === Or36647Const.WELFARE_EQUIP_LENDING_2)
  ) {
    // 予定回数合計 = 利用票の初期情報.利用票明細情報.予定合計
    return parseInt(item.yTotal)
  } else {
    // 上記以外の場合
    // 予定回数合計 = 予定回数(01)から予定回数(31)まで合計する
    let schedTotal = 0
    const dayList = offeryYearMonthDayList.value.map((item) =>
      getSchedAchvDayName(Or36647Const.PREFIX_SCHED, item.day)
    )
    // 予定回数1～予定回数31の合計
    for (const key in item) {
      if (dayList.includes(key)) {
        const value = item[key as keyof typeof item] as TimeModelValue
        schedTotal += parseInt(value.value)
      }
    }
    return schedTotal
  }
}

/**
 * 実績回数合計取得
 *
 * @param item -利用票明細情報
 */
const getAchvTotal = (item: UsingSlipDetailInfo): number => {
  // (利用票明細情報.サービス種別コード = "17" or 利用票明細情報.サービス種別コード = "67") and
  // 利用票明細情報.福祉用具貸与フラグ（実績） = null or 利用票明細情報.福祉用具貸与フラグ（実績） = 0 or 利用票明細情報.福祉用具貸与フラグ（実績） = 2の場合、
  if (
    (item.svtype === Or36647Const.SERVICE_TYPECODE_17 ||
      item.svtype === Or36647Const.SERVICE_TYPECODE_67) &&
    (isEmpty(item.jRentalF.modelValue) ||
      item.jRentalF.modelValue === Or36647Const.WELFARE_EQUIP_LENDING_0 ||
      item.jRentalF.modelValue === Or36647Const.WELFARE_EQUIP_LENDING_2)
  ) {
    // 実績回数合計 = 利用票明細情報.実績合計
    return parseInt(item.jTotal)
  } else {
    // 上記以外の場合
    // 実績合計 = 実績回数(01)から実績回数(31)まで合計する
    let achvTotal = 0
    const dayList = offeryYearMonthDayList.value.map((item) =>
      getSchedAchvDayName(Or36647Const.PREFIX_ACHV, item.day)
    )
    // 実績回数1～実績回数31の合計
    for (const key in item) {
      if (dayList.includes(key)) {
        const value = item[key as keyof typeof item] as TimeModelValue
        achvTotal += parseInt(value.value)
      }
    }
    return achvTotal
  }
}

/**
 * 予定金額合計取得
 *
 * @param item -利用票明細情報
 */
const getSchedAmountTotal = (item: UsingSlipDetailInfo) => {
  // 利用票明細情報.合計表示フラグ = 0の場合
  if (item.dmySvTaniVisible === Or36647Const.DMY_SV_TANI_VISIBLE_NG) {
    // 予定金額合計が非表示にする
    return ''
  }
  // 予定単位回数を計算する
  // 予定単位回数
  let schedNuitNumber = 0
  // 予定回数合計
  const schedTotal = getSchedTotal(item)
  // 算定回数
  const calculationNumber = parseInt(item.dmyOneOfMonth)
  // 予定回数合計 > 0 and 利用票明細情報.算定回数 > 0 and 予定回数合計 > 利用票明細情報.算定回数の場合、
  if (schedTotal > 0 && calculationNumber > 0 && schedTotal > calculationNumber) {
    // 予定単位回数 = 利用票明細情報.算定回数
    schedNuitNumber = calculationNumber
  } else {
    // 上記以外の場合
    // 予定単位回数 = 予定回数合計
    schedNuitNumber = schedTotal
  }
  // 予定金額合計を取得する
  let schedAmountTotal = 0
  // 利用票明細情報.サービス種別コード <> "17" and 利用票明細情報.サービス種別コード <> "67"の場合、
  if (
    item.svtype !== Or36647Const.SERVICE_TYPECODE_17 &&
    item.svtype !== Or36647Const.SERVICE_TYPECODE_67
  ) {
    // 予定金額合計 = 利用票明細情報.単価（予定） * 予定単位回数
    schedAmountTotal = parseInt(item.dmy0SvTani) * schedNuitNumber
  } else {
    // 利用票明細情報.小数点以下の端数処理フラグ = 1の場合
    if (item.dmyRHasu === Or36647Const.DECIMAL_PROCESS_FLG_1) {
      // 切り捨て
      // 予定金額合計 = 利用票明細情報.単価（予定） * 予定単位回数
      schedAmountTotal = parseInt(item.dmy0SvTani) * schedNuitNumber
    } else if (item.dmyRHasu === Or36647Const.DECIMAL_PROCESS_FLG_2) {
      // 利用票明細情報.小数点以下の端数処理フラグ = 2の場合
      // 切り上げ
      // 予定金額合計 = 利用票明細情報.単価（予定） * 予定単位回数
      schedAmountTotal = Math.ceil(parseFloat(item.dmy0SvTani)) * schedNuitNumber
    } else {
      // 四捨五入
      // 予定金額合計 = 利用票明細情報.単価（予定） * 予定単位回数
      schedAmountTotal = Math.round(parseFloat(item.dmy0SvTani)) * schedNuitNumber
    }
  }
  return schedAmountTotal
}

/**
 * 実績金額合計取得
 *
 * @param item -利用票明細情報
 */
const getAchvAmountTotal = (item: UsingSlipDetailInfo) => {
  // 利用票明細情報.合計表示フラグ = 0の場合
  if (item.dmySvTaniVisible === Or36647Const.DMY_SV_TANI_VISIBLE_NG) {
    // 実績金額合計が非表示にする
    return ''
  }
  // 実績単位回数を計算する
  // 実績単位回数
  let achvNuitNumber = 0
  // 実績回数合計
  const achvTotal = getAchvTotal(item)
  // 算定回数
  const calculationNumber = parseInt(item.dmyOneOfMonth)
  // 実績回数合計 > 0 and 利用票明細情報.算定回数 > 0 and 実績回数合計 > 利用票明細情報.算定回数の場合
  if (achvTotal > 0 && calculationNumber > 0 && achvTotal > calculationNumber) {
    // 実績単位回数 = 利用票明細情報.算定回数
    achvNuitNumber = calculationNumber
  } else {
    // 実績単位回数 = 実績回数合計
    achvNuitNumber = achvTotal
  }
  // 実績金額合計を取得する
  let achvAmountTotal = 0
  // 利用票明細情報.サービス種別コード <> "17" and 利用票明細情報.サービス種別コード <> "67"の場合
  if (
    item.svtype !== Or36647Const.SERVICE_TYPECODE_17 &&
    item.svtype !== Or36647Const.SERVICE_TYPECODE_67
  ) {
    // 実績金額合計 = 利用票の初期情報.利用票明細情報.単価（実績） * 実績単位回数
    achvAmountTotal = parseInt(item.dmy0SvTanj) * achvNuitNumber
  } else {
    // 利用票明細情報.小数点以下の端数処理フラグ = 1の場合
    if (item.dmyRHasu === Or36647Const.DECIMAL_PROCESS_FLG_1) {
      // 実績金額合計 = 利用票明細情報.単価（実績） * 実績単位回数
      achvAmountTotal = parseInt(item.dmy0SvTanj) * achvNuitNumber
    } else if (item.dmyRHasu === Or36647Const.DECIMAL_PROCESS_FLG_2) {
      // 利用票明細情報.小数点以下の端数処理フラグ = 2の場合
      // 実績金額合計 = 利用票明細情報.単価（実績） * 実績単位回数
      achvAmountTotal = Math.ceil(parseFloat(item.dmy0SvTanj)) * achvNuitNumber
    } else {
      // 実績金額合計 = 利用票明細情報.単価（実績） * 実績単位回数
      achvAmountTotal = Math.round(parseFloat(item.dmy0SvTanj)) * achvNuitNumber
    }
  }
  return achvAmountTotal
}

/**
 * 利用票・別表画面で警告情報ダイアログ
 */
const openDialogOr10923 = () => {
  // or10923のダイアログ開閉状態を更新する
  Or10923Logic.state.set({
    uniqueCpId: or10923.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 提供時間 keydown
 *
 * @param event - event
 */
const handleKeydown = (event: KeyboardEvent) => {
  // サービス時間 = null  or ""の場合
  if (event.key === Or36647Const.KEYBOARD_BACKSPACE || event.key === Or36647Const.KEYBOARD_DELETE) {
    // 処理終了
    event.preventDefault()
  }
}

/**
 * 利用票画面初期情報取得
 */
const useSlipInitInfoSelect = async () => {
  // 利用票画面初期情報取得
  const inputData: UseSlipInitInfoSelectInEntity = {
    /** 事業所ID */
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    /** 利用者ID */
    userId: localOneway.or36647.userId,
    /** 提供年月 */
    teikyouYm: local.offeryYearMonth.value,
    /** 事業者グループ適用ID */
    tekiyoFlg: localOneway.or36647.officeGroupApplyId,
    /** 適用事業所ＩＤリスト */
    svJigyoIdList: localOneway.or36647.applyOfficeIdList.map((item) => {
      return {
        svJigyoId: item,
      }
    }),
  }
  const response: UseSlipInitInfoSelectOutEntity = await ScreenRepository.select(
    'useSlipInitInfoSelect',
    inputData
  )
  // 事業所情報リスト
  localOneway.mo01282OfficeOneway.items = response.data.riyou701Info[0].jigyoList
  // 入力フォームの各項目設定
  setInputForm(response.data.riyou701Info[0])
  // 警告メッセージ一覧
  localOneway.or10923Oneway.warningMessageInfo = response.data.riyou701Info[0].messageList.map(
    (item) => {
      return {
        messageContent: item.msgText,
      }
    }
  )
}

/**
 * 利用者情報設定
 *
 * @param riyouInfo -利用票画面情報
 */
const setRiyourshaData = (riyouInfo: RiyouInfo) => {
  // 利用者情報
  const riyourshaData = riyouInfo.riyourshaData[0]
  local.usingSlipInfo = riyouInfo
  // 提供年月
  local.offeryYearMonth.value = riyouInfo.teikyouYm
  // 作成日
  local.createDate.value = riyourshaData.createYmd
  // 予定済みフラグ
  local.inputWorkSchedule.modelValue = riyourshaData.yoteiZumiFlg === Or36647Const.SCHED_FLG_OK
  // 確定済みフラグ
  local.inputWorkAchievements.modelValue = riyourshaData.jissekiZumiFlg === Or36647Const.ACHV_FLG_OK
  // 計算フラグ
  local.compute = riyourshaData.rCompBase
  // SOMPOフラグ
  local.sompoFlg = riyourshaData.sompoFlg === Or36647Const.SOMPO_FLG_OK
  // 確定済みフラグ
  local.confirmFlg = riyourshaData.kakuteiZumiFlg === Or36647Const.CONFIRM_FLG_OK
  // 利用者情報.予定済みフラグ = 1 OR 利用者情報.実績済みフラグ = 1の場合
  if (local.inputWorkSchedule.modelValue || local.inputWorkAchievements.modelValue) {
    // 作成年月日入力不可
    localOneway.mo00020YmdOneway.disabled = true
  }
  // 利用者情報.保険者・被保険者の制御フラグがfalseの場合、使用不可
  if (riyourshaData.bHokenFlg !== Or36647Const.HOKEN_FLG_OK) {
    localOneway.mo00009Oneway.disabled = true
  }
  const offeryYearMonth = new Date(riyouInfo.teikyouYm)
  const limitAmountApplyStart = new Date(riyourshaData.dmyKikanFr)
  const limitAmountApplyEnd = new Date(riyourshaData.dmyKikanTo)
  // 利用者情報.保険者番号 = nullの場合
  if (isEmpty(riyourshaData.dmyKHokenNo)) {
    local.insuredIdBgColor = Or36647Const.COLOR_RED
    local.levelOfCareBgColor = Or36647Const.COLOR_RED
  } else if (
    isEmpty(riyourshaData.dmyKaigodo) ||
    riyourshaData.dmyKaigodo === Or36647Const.LEVELOF_CARE_0
  ) {
    // 利用者情報.要介護度 = null OR 利用者情報.要介護度 = 0の場合
    local.levelOfCareBgColor = Or36647Const.COLOR_RED
  } else if (limitAmountApplyStart > offeryYearMonth || limitAmountApplyEnd < offeryYearMonth) {
    // 利用者情報.限度額適用期間（開始） > 提供年月 OR 利用者情報.限度額適用期間（終了） < 提供年月の場合
    local.insuredIdBgColor = Or36647Const.COLOR_RED
    local.levelOfCareBgColor = Or36647Const.COLOR_RED
  } else {
    local.insuredIdBgColor = 'transparent !important'
    local.levelOfCareBgColor = 'transparent !important'
  }
  // 保険者番号
  localOneway.mo01338InsurOneway.value = riyourshaData.dmyKHokenNo
  // 要介護度
  localOneway.mo01338LevelOfCareOneway.value = riyourshaData.dmyKaigodo
  // 要介護度変更日
  localOneway.mo01338UpdateDateOneway.value = riyourshaData.dmyHenDate
  // 支援事業所
  localOneway.mo01338SupportOfficeOneway.value =
    riyouInfo.cmnTucPlanShienList.find((item) => item.svJigyoId === riyourshaData.shienId)
      ?.jigyoNameKnj ?? ''
  // 被保険者番号
  localOneway.mo01338InsuredOneway.value = riyourshaData.dmyHHokenNo
  // 要介護度（変更後）
  localOneway.mo01338UpdateAfterOneway.value = riyourshaData.dmyHenKaigodo
  // 支給限度
  localOneway.mo01338PaymentLimitOneway.value = getDisplayAmount(riyourshaData.dmyShikyuGaku)
  // 担当者
  localOneway.mo01338ManagerOneway.value = riyourshaData.dmyTanto
  // 希望額
  localOneway.mo01338HopeAmountOneway.value = getDisplayAmount(riyourshaData.dmyKibouGaku)
}

/**
 * 入力フォームの各項目設定
 *
 * @param riyouInfo -利用票画面情報
 */
const setInputForm = (riyouInfo: RiyouInfo) => {
  // 利用者情報設定
  setRiyourshaData(riyouInfo)
  // 利用票明細情報入力フォームの各項目設定
  setUsingSlipInputForm(riyouInfo.riyouList)
  // 別表明細情報入力フォームの各項目設定
  setUsingSlipOtherTableInputForm(
    riyouInfo.riyouBeppyoList,
    riyouInfo.kohiList,
    riyouInfo.riyouryouList,
    riyouInfo.syafukuList
  )
}

/**
 * 利用票明細情報入力フォームの各項目設定
 *
 * @param riyouList -利用票明細情報
 */
const setUsingSlipInputForm = (riyouList: Riyou[]) => {
  const welfareEquipLendingList = localOneway.mo01282Oneway.items as CodeType[]
  // 利用票明細一覧
  local.usingSlipList.length = 0
  for (const riyou of riyouList) {
    const usingSlipItem = {
      ...riyou,
      yoteiZumiFlg: {
        modelValue: riyou.yoteiZumiFlg === Or36647Const.SCHED_FLG_OK,
      },
      jissekiZumiFlg: {
        modelValue: riyou.jissekiZumiFlg === Or36647Const.ACHV_FLG_OK,
      },
      svStartTime: {
        value: riyou.svStartTime,
      },
      svEndTime: {
        value: riyou.svEndTime,
      },
      yRentalF: {
        modelValue: riyou.yRentalF,
      },
      jRentalF: {
        modelValue: riyou.jRentalF,
      },
      yDay01: {
        value: riyou.yDay01,
      },
      yDay02: {
        value: riyou.yDay02,
      },
      yDay03: {
        value: riyou.yDay03,
      },
      yDay04: {
        value: riyou.yDay04,
      },
      yDay05: {
        value: riyou.yDay05,
      },
      yDay06: {
        value: riyou.yDay06,
      },
      yDay07: {
        value: riyou.yDay07,
      },
      yDay08: {
        value: riyou.yDay08,
      },
      yDay09: {
        value: riyou.yDay09,
      },
      yDay10: {
        value: riyou.yDay10,
      },
      yDay11: {
        value: riyou.yDay11,
      },
      yDay12: {
        value: riyou.yDay12,
      },
      yDay13: {
        value: riyou.yDay13,
      },
      yDay14: {
        value: riyou.yDay14,
      },
      yDay15: {
        value: riyou.yDay15,
      },
      yDay16: {
        value: riyou.yDay16,
      },
      yDay17: {
        value: riyou.yDay17,
      },
      yDay18: {
        value: riyou.yDay18,
      },
      yDay19: {
        value: riyou.yDay19,
      },
      yDay20: {
        value: riyou.yDay20,
      },
      yDay21: {
        value: riyou.yDay21,
      },
      yDay22: {
        value: riyou.yDay22,
      },
      yDay23: {
        value: riyou.yDay23,
      },
      yDay24: {
        value: riyou.yDay24,
      },
      yDay25: {
        value: riyou.yDay25,
      },
      yDay26: {
        value: riyou.yDay26,
      },
      yDay27: {
        value: riyou.yDay27,
      },
      yDay28: {
        value: riyou.yDay28,
      },
      yDay29: {
        value: riyou.yDay29,
      },
      yDay30: {
        value: riyou.yDay30,
      },
      yDay31: {
        value: riyou.yDay31,
      },
      jDay01: {
        value: riyou.jDay01,
      },
      jDay02: {
        value: riyou.jDay02,
      },
      jDay03: {
        value: riyou.jDay03,
      },
      jDay04: {
        value: riyou.jDay04,
      },
      jDay05: {
        value: riyou.jDay05,
      },
      jDay06: {
        value: riyou.jDay06,
      },
      jDay07: {
        value: riyou.jDay07,
      },
      jDay08: {
        value: riyou.jDay08,
      },
      jDay09: {
        value: riyou.jDay09,
      },
      jDay10: {
        value: riyou.jDay10,
      },
      jDay11: {
        value: riyou.jDay11,
      },
      jDay12: {
        value: riyou.jDay12,
      },
      jDay13: {
        value: riyou.jDay13,
      },
      jDay14: {
        value: riyou.jDay14,
      },
      jDay15: {
        value: riyou.jDay15,
      },
      jDay16: {
        value: riyou.jDay16,
      },
      jDay17: {
        value: riyou.jDay17,
      },
      jDay18: {
        value: riyou.jDay18,
      },
      jDay19: {
        value: riyou.jDay19,
      },
      jDay20: {
        value: riyou.jDay20,
      },
      jDay21: {
        value: riyou.jDay21,
      },
      jDay22: {
        value: riyou.jDay22,
      },
      jDay23: {
        value: riyou.jDay23,
      },
      jDay24: {
        value: riyou.jDay24,
      },
      jDay25: {
        value: riyou.jDay25,
      },
      jDay26: {
        value: riyou.jDay26,
      },
      jDay27: {
        value: riyou.jDay27,
      },
      jDay28: {
        value: riyou.jDay28,
      },
      jDay29: {
        value: riyou.jDay29,
      },
      jDay30: {
        value: riyou.jDay30,
      },
      jDay31: {
        value: riyou.jDay31,
      },
      yRentalFName: welfareEquipLendingList.find((item) => item.value === riyou.yRentalF)?.label,
      jRentalFName: welfareEquipLendingList.find((item) => item.value === riyou.jRentalF)?.label,
    } as UsingSlipDetailInfo
    local.usingSlipList.push(usingSlipItem)
  }
}

/**
 * 別表明細情報入力フォームの各項目設定
 *
 * @param riyouBeppyoList -別表明細情報
 *
 * @param kohiList -公費集計欄情報
 *
 * @param riyouryouList -利用料集計欄情報
 *
 * @param syafukuList -社福軽減集計欄情報
 *
 * @param replaceFlg -置換フラグ
 */
const setUsingSlipOtherTableInputForm = (
  riyouBeppyoList: RiyouBeppyo[],
  kohiList: Kohi[],
  riyouryouList: Riyouryou[],
  syafukuList: Syafuku[],
  replaceFlg = true
) => {
  // 利用票別表明細一覧
  if (riyouBeppyoList.length > 0 && replaceFlg) {
    local.usingSlipOthrtTableList.length = 0
    for (const riyouBeppyo of riyouBeppyoList) {
      const usingSlipOthrtTable = {
        ...riyouBeppyo,
        cScode: riyouBeppyo.cScode.slice(0, 6),
        sTensuOver: {
          value: riyouBeppyo.sTensuOver,
        },
        kTensuOver: {
          value: riyouBeppyo.kTensuOver,
        },
      } as UsingSlipOthrtTable
      local.usingSlipOthrtTableList.push(usingSlipOthrtTable)
    }
  }
  // 公費集計欄一覧
  if (kohiList.length > 0 && replaceFlg) {
    local.kohiList.length = 0
    local.kohiList = kohiList
  }
  // 利用料集計欄一覧
  if (riyouryouList.length > 0 && replaceFlg) {
    local.riyouryouList.length = 0
    for (const riyouryou of riyouryouList) {
      local.riyouryouList.push({
        ...riyouryou,
        svJigyoId: {
          modelValue: riyouryou.svJigyoId,
        },
        riyouryouItem: {
          value: riyouryou.riyouryouItem,
        },
        riyouryouTanka: {
          value: riyouryou.riyouryouTanka,
        },
        riyouryouSu: {
          value: riyouryou.riyouryouSu,
        },
        updateKbn: '',
      })
    }
  }
  // 社福軽減集計欄一覧
  if (syafukuList.length > 0 && replaceFlg) {
    local.syafukuList.length = 0
    for (const syafuku of syafukuList) {
      local.syafukuList.push({
        ...syafuku,
        svJigyoId: {
          modelValue: syafuku.svJigyoId,
        },
        syafukuObj: {
          value: syafuku.syafukuObj,
        },
        syafukuRitu: {
          value: (parseFloat(syafuku.syafukuRitu) * 100).toFixed(1) + Or36647Const.PERCENTAGE,
        },
        updateKbn: '',
      })
    }
  }
}

/**
 * 保存処理
 */
const usingSlipSave = async () => {
  // 利用者ID
  const userId = local.usingSlipInfo.riyourshaData[0].userid
  // 利用者IDが無い場合
  if (isEmpty(userId)) {
    // 処理終了
    return false
  }
  // 画面入力データの変更がなし場合
  if (isEdit.value === false) {
    // 保存処理前のチェック情報を取得する
    const updateBefCheckInputData: UseSlipInfoUpdateBefCheckInfoSelectInEntity = {
      /** 支援事業者ID */
      supportOfficeId: local.usingSlipInfo.riyourshaData[0].shienId,
      /** 利用者ID */
      userId: userId,
      /** 提供年月 */
      offerYearMonth: local.offeryYearMonth.value,
    }
    const updateBefCheckRet: UseSlipInfoUpdateBefCheckInfoSelectOutEntity =
      await ScreenRepository.select('useSlipInfoUpdateBefCheckInfoSelect', updateBefCheckInputData)
    // 利用票画面情報
    const riyouInfo = updateBefCheckRet.data.riyou711Info[0]
    // 画面.利用票明細情報がなし and 画面.別表明細情報があるの場合
    if (local.usingSlipList.length === 0 && local.usingSlipOthrtTableList.length > 0) {
      // 電電子ファイル保存設定フラグ = True の場合
      if (riyouInfo.bunshoKbn === Or36647Const.BUNSHO_KBN_1) {
        // 削除履歴出力区分 = 1
        deleteHistoryOutputKbn.value = Or36647Const.DELETE_HISTORY_OUTPUT_KBN_1
      }
      // 削除を行う
      await usingSlipDelete()
      // 処理終了
      return false
    }
    // 電子ファイル保存設定フラグ =  trueの場合
    if (riyouInfo.bunshoKbn === Or36647Const.BUNSHO_KBN_1) {
      // 印刷を行う
      await usingSlipPrint()
    }
    // 利用票情報件数 > 0の場合
    if (parseInt(riyouInfo.riyouCount) > 0) {
      await openWarningDialog(t('message.w-cmn-20874'))
      // 処理終了
      return false
    }
    // 別表データがある場合
    if (local.usingSlipOthrtTableList.length > 0) {
      const kbnShikyuuGendoData = local.usingSlipInfo.kbnShikyuuGendoData[0]
      // 区分支給限度情報.サービス単位/金額の合計
      const cSvTensuSum = parseInt(kbnShikyuuGendoData.cSvTensuSum)
      // 画面.区分支給限度情報.区分支給限度基準額 > 0 and 画面.区分支給限度情報.サービス単位/金額の合計 > 0の場合
      if (parseInt(kbnShikyuuGendoData.tKGendo) > 0 && cSvTensuSum > 0) {
        // 区分支給限度情報.種類支給限度_超過の合計
        const cSTensuOverSum = parseInt(kbnShikyuuGendoData.cSTensuOverSum)
        // 区分支給限度情報.区分支給限度_超過の合計
        const cKTensuOverSum = parseInt(kbnShikyuuGendoData.cKTensuOverSum)
        // 区分支給限度情報.区分支給限度_基準内の合計
        const cSTensuSum = parseInt(kbnShikyuuGendoData.cSTensuSum)
        // 画面.区分支給限度情報.種類支給限度_超過の合計 + 画面.区分支給限度情報.区分支給限度_超過の合計 + 画面.区分支給限度情報.区分支給限度_基準内の合計
        const total = cSTensuOverSum + cKTensuOverSum + cSTensuSum
        // 画面.区分支給限度情報.サービス単位/金額の合計 <> 画面.区分支給限度情報.種類支給限度_超過の合計 + 画面.区分支給限度情報.区分支給限度_超過の合計 + 画面.区分支給限度情報.区分支給限度_基準内の合計 or
        // 画面.区分支給限度情報.区分支給限度_基準内の合計 < 画面.区分支給限度情報.区分支給限度_基準内の合計の場合
        if (parseInt(kbnShikyuuGendoData.cSvTensuSum) !== total || cSTensuSum < cSTensuSum) {
          await openConfirmDialog(t('message.i-cmn-10504'))
          // 処理終了
          return false
        }
      }
    }
    const riyouryouSvJigyoIdList = riyouryouList.value.filter((item) =>
      isEmpty(item.svJigyoId.modelValue)
    )
    // 画面.利用料集計欄情報は、いずれかサービス事業者IDが存在しない場合
    if (riyouryouSvJigyoIdList.length > 0) {
      await openConfirmDialog(t('message.i-cmn-10505'))
      // 処理終了
      return false
    }
    const syafukuSvJigyoIdList = syafukuList.value.filter((item) =>
      isEmpty(item.svJigyoId.modelValue)
    )
    // 画面.社福軽減集計欄情報は、いずれかサービス事業者IDが存在しない場合
    if (syafukuSvJigyoIdList.length > 0) {
      await openConfirmDialog(t('message.i-cmn-10506'))
      // 処理終了
      return false
    }
    // 同一事業所のレコードがある場合
    const svJigyoIdList = syafukuList.value.map((item) => item.svJigyoId.modelValue)
    if (new Set(svJigyoIdList).size < syafukuList.value.length) {
      await openConfirmDialog(t('message.i-cmn-10507'))
      // 処理終了
      return false
    }

    // 再計算処理を行う
    if ((await recompute()) === Or36647Const.CONFIRM_BTN_YES) {
      // 新規計算フラグを「false」に設定する
      newComputeFlg.value = false

      // 保存処理
      const updateInputData: UseSlipInfoUpdateInEntity = {
        // 事業所ID：親画面.事業所ID
        svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
        // 事業者グループ適用ID：親画面.事業者グループ適用ID
        tekiyoFlg: localOneway.or36647.officeGroupApplyId,
        // 適用事業所IDリスト：親画面.適用事業所IDリスト
        svJigyoIdList: localOneway.or36647.applyOfficeIdList.map((item) => {
          return { svJigyoId: item }
        }),
        // 利用票画面情報
        riyou702Info: [
          {
            // 提供年月：画面.提供年月
            teikyouYm: local.offeryYearMonth.value,
            // 提供月（日）：画面.提供月（日）
            teikyouYmD: local.usingSlipInfo.teikyouYmD,
            // 利用者情報：画面.利用者情報
            riyourshaData: local.usingSlipInfo.riyourshaData,
            // 利用票明細情報：画面.利用票明細情報
            riyouList: getRiyouList(),
            // 別表明細情報：画面.別表明細情報
            riyouBeppyoList: getRiyouBeppyoList(),
            // 種類別限度情報：画面.種類別限度情報
            syuruiGendoData: local.usingSlipInfo.syuruiGendoData,
            // 公費集計欄情報：画面.公費集計欄情報
            kohiList: local.usingSlipInfo.kohiList,
            // 利用料集計欄情報：画面.利用料集計欄情報
            riyouryouList: getRiyouryouList(),
            // 社福軽減集計欄情報：画面.社福軽減集計欄情報
            syafukuList: getSyafukuList(),
            // 合計表示欄情報：画面.合計表示欄情報
            sumData: local.usingSlipInfo.sumData,
            // 短期入所連続利用30日超過情報：画面退避情報.短期入所連続利用30日超過情報
            planOv30List: local.usingSlipInfo.planOv30List,
            // 短期利用日数保持情報：画面退避情報.短期利用日数保持情報
            svplanShortList: local.usingSlipInfo.svplanShortList,
            // 提供事業所毎小計情報：画面退避情報.提供事業所毎小計情報
            servicePointList: local.usingSlipInfo.servicePointList,
            // 算定確認情報：画面退避情報.算定確認情報
            riyouKakuninList: local.usingSlipInfo.riyouKakuninList,
            // 退避用情報
            taihiData: [
              {
                // 退避用情報.利用票明細情報(初期化処理で取得された情報)
                riyouList: local.usingSlipInfo.riyouList,
                // 退避用情報.別表明細情報(初期化処理で取得された情報)
                riyouBeppyoList: local.usingSlipInfo.riyouBeppyoList,
                // 退避用情報.種類別限度情報(初期化処理で取得された情報)
                syuruiGendoData: local.usingSlipInfo.syuruiGendoData,
                // 退避用情報.公費集計欄情報(初期化処理で取得された情報)
                kohiList: local.usingSlipInfo.kohiList,
                // 退避用情報.利用料集計欄情報(初期化処理で取得された情報)
                riyouryouList: local.usingSlipInfo.riyouryouList,
                // 退避用情報.社福軽減集計欄情報(初期化処理で取得された情報)
                syafukuList: local.usingSlipInfo.syafukuList,
                // 退避用情報.短期入所連続利用30日超過情報(初期化処理で取得された情報)
                planOv30List: local.usingSlipInfo.planOv30List,
                // 退避用情報.短期利用日数保持情報(初期化処理で取得された情報)
                svplanShortList: local.usingSlipInfo.svplanShortList,
                // 退避用情報.提供事業所毎小計情報(初期化処理で取得された情報)
                servicePointList: local.usingSlipInfo.servicePointList,
                // 退避用情報.介護予防短期入所利用状況情報(初期化処理で取得された情報)
                tlcSvplanShortList: local.usingSlipInfo.tlcSvplanShortList,
                // 退避用情報.算定確認情報(初期化処理で取得された情報)
                riyouKakuninList: local.usingSlipInfo.riyouKakuninList,
              } as TaihiData,
            ],
          } as Riyou702Info,
        ],
      }
      const updateRet: UseSlipInfoUpdateOutEntity = await ScreenRepository.update(
        'useSlipInfoUpdate',
        updateInputData
      )

      // 利用票情報件数 > 0 の場合
      if (parseInt(updateRet.data.riyouCount)) {
        await openWarningDialog(t('message.w-cmn-20874'))
      }

      // 電子ファイル保存設定フラグ = True の場合
      if (riyouInfo.bunshoKbn === Or36647Const.BUNSHO_KBN_1) {
        // 削除履歴出力区分 = 0
        deleteHistoryOutputKbn.value = Or36647Const.DELETE_HISTORY_OUTPUT_KBN_0
      }

      // 入力フォームの各項目に返却情報を設定する。
      setInputForm(updateRet.data.riyouInfo[0])
    }
  }

  return Or36647Const.CONFIRM_BTN_YES
}

/**
 * 「複写ボタン」押下
 */
const usingSlipCopy = async () => {
  // 画面入力データの変更がある場合
  if (isEdit.value) {
    const ret = await openConfirmDialog(t('message.i-cmn-10430'), true)
    // はい
    if (ret === Or36647Const.CONFIRM_BTN_YES) {
      // 保存処理実行
      const saveResult = await usingSlipSave()
      if (saveResult !== Or36647Const.CONFIRM_BTN_YES) {
        // 更新異常の場合、処理終了
        return false
      }
    } else if (ret === Or36647Const.CONFIRM_BTN_NO) {
      // いいえ：ダイアログ画面を閉じる、処理続き
    } else {
      // キャンセル：処理終了
      return false
    }
  }
  // GUI01150_計画複写画面をポップアップで起動する。
  Or35745Logic.state.set({
    uniqueCpId: or35745.value.uniqueCpId,
    state: {
      isOpen: true,
      inParam: {
        // 利用者リスト：親画面.利用者リスト
        userIds: localOneway.or36647.userIds,
        // 50音フィルタ：親画面.50音フィルタ
        gojuonFilter: [localOneway.or36647.gojuonFilter],
        // 画面名称："利用票"
        screenName1: Or36647Const.SCREEN_NAME_1,
        // 機能ID："GUI01149"
        kinouId: Or36647Const.KINOU_ID,
        // システムコード：親画面.システムコード
        sysCode: localOneway.or36647.sysCode,
        // 事業所CD：親画面.事業所CD
        svJigyoCd: localOneway.or36647.svJigyoCd,
        // 法人ID：親画面.法人ID
        houjinId: localOneway.or36647.corporationId,
        // 施設ＩＤ：親画面.施設ＩＤ
        shisetuId: localOneway.or36647.facilityId,
        // 事業所ＩＤ：親画面.事業所ＩＤ
        svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
        // システム年月日：親画面.システム年月日
        sysYmd: localOneway.or36647.sysYearMonth,
        // 適用事業所ＩＤリスト：親画面.適用事業所ＩＤリスト
        svJigyoIds: localOneway.or36647.applyOfficeIdList,
        // 自由パラメータ.利用者ID：画面.利用者情報.利用者ID
        userId: localOneway.or36647.userId,
        // 自由パラメータ.複写元事業者ID：画面.利用者情報.支援事業者ID
        shienId: localOneway.or36647.shienId,
        // 自由パラメータ.担当ケアマネID：画面.利用者情報.担当者ID_hidden
        tantoId: local.usingSlipInfo.riyourshaData[0].dmyTantoIdHidden,
        // 自由パラメータ.複写元年月：画面.提供年月
        fromYm: local.offeryYearMonth.value,
        // 自由パラメータ.画面名："利用・提供票"
        screenName2: Or36647Const.SCREEN_NAME_2,
        // システムCD：親画面.システムCD
        sysCd: localOneway.or36647.sysCode,
        // ライセンス配列：親画面.ライセンス配列
        licenses: localOneway.or36647.licenseList,
        // 和暦の元号開始年リスト：親画面.和暦の元号開始年リスト
        // 機能情報List：親画面.機能情報List
        kinouNames: localOneway.or36647.functionInfoList,
      },
    },
  })
}

/**
 * 「再計算ボタン」押下
 */
const recompute = async () => {
  if (local.usingSlipList.length > 0) {
    // サービス事業者ID配列
    const svJigyoIdList = local.usingSlipList
      .map((item) => item.svJigyoId)
      .filter((item) => isEmpty(item))

    // サービス事業者ID配列すべて値がNULLの場合
    if (svJigyoIdList.length === local.usingSlipList.length) {
      await openConfirmDialog(t('message.i-cmn-10508'))
      // 処理終了
      return Or36647Const.CONFIRM_BTN_YES
    } else {
      // 利用票画面情報再計算前利用票明細データチェック及び補正を行う
      const recalculationBefInputData: UseSlipInfoRecalculationBefSelectInEntity = {
        // 利用者情報：画面.利用者情報
        riyourshaData: local.usingSlipInfo.riyourshaData,
        // 利用票明細情報：画面.利用票明細情報
        riyouList: getRiyouList(),
        // 利用票画面処理用構造体：画面退避情報.利用票画面処理用構造体
        riyouProcessObject: local.usingSlipInfo.riyouProcessObject,
      }
      const recalculationBefRet: UseSlipInfoRecalculationBefSelectOutEntity =
        await ScreenRepository.select(
          'useSlipInfoRecalculationBefSelect',
          recalculationBefInputData
        )
      // リターンコード
      let retCode = recalculationBefRet.data.riyou1003Info[0].retCode
      // 対象行
      let targetRow = -1
      // リターンコード = 1 の場合
      if (retCode === Or36647Const.PROCESS_FLG_1) {
        await openConfirmDialog(t('message.i-cmn-10065'))
      } else if (retCode === Or36647Const.PROCESS_FLG_2) {
        // リターンコード = 2 の場合
        // 取得した表示メッセージリストで、順番にメッセージを呼び出す
        for (const message of recalculationBefRet.data.riyou1003Info[0].hyojiMsgList) {
          const ret = await openConfirmDialog(message.msgText, true)
          // いいえ
          if (ret === Or36647Const.CONFIRM_BTN_NO) {
            // 該当メッセージ情報.対象行を対応する明細行を選択状態にする。処理を異常終了する。
            local.selectedUsingSlipItemIndex = parseInt(message.msgRowIndex)
            // 処理を異常終了する
            return Or36647Const.CONFIRM_BTN_NO
          }
        }
      } else if (retCode === Or36647Const.PROCESS_FLG_6) {
        // リターンコード = 6 の場合
        await openWarningDialog(t('message.w-cmn-20097'))
      } else {
        // 利用票画面情報再計算前利用票明細データチェック及び補正2を行う
        const recalculationBef2InputData: UseSlipInfoRecalculationBef2SelectInEntity = {
          // 利用者情報：画面.利用者情報
          riyourshaData: local.usingSlipInfo.riyourshaData,
          // 利用票明細情報：画面.利用票明細情報
          riyouList: getRiyouList(),
          // 利用票画面処理用構造体：画面退避情報.利用票画面処理用構造体
          riyouProcessObject: local.usingSlipInfo.riyouProcessObject,
          // 再計算前引数情報：返却情報.再計算前引数情報
          calcBefObj: recalculationBefRet.data.riyou1003Info[0].calcBefObj,
        }
        const recalculationBef2Ret: UseSlipInfoRecalculationBef2SelectOutEntity =
          await ScreenRepository.select(
            'useSlipInfoRecalculationBef2Select',
            recalculationBef2InputData
          )
        // リターンコード
        retCode = recalculationBef2Ret.data.retCode
        // 対象行
        targetRow = parseInt(recalculationBef2Ret.data.rowIndex)
        if (retCode === Or36647Const.PROCESS_FLG_3) {
          // 処理結果 = 3 の場合
          await openConfirmDialog(t('message.i-cmn-11336'))
        } else if (retCode === Or36647Const.PROCESS_FLG_4) {
          // 処理結果 = 4 の場合
          await openConfirmDialog(t('message.i-cmn-11337'))
        } else if (retCode === Or36647Const.PROCESS_FLG_5) {
          // 処理結果 = 5 の場合
          await openConfirmDialog(recalculationBef2Ret.data.msg)
        } else if (retCode === Or36647Const.PROCESS_FLG_7) {
          // 処理結果 = 7 の場合
          await openConfirmDialog(t('message.i-cmn-10355'))
        } else if (retCode === Or36647Const.PROCESS_FLG_8) {
          // 返却情報.リターンコード = 8 の場合
          // メッセージ
          const msg = recalculationBef2Ret.data.msg
          // 返却情報.メッセージの先頭２桁が"3:" ro "4:" or "7:"の場合
          if (
            msg.startsWith(Or36647Const.MESSAGE_PREFIX_3) ||
            msg.startsWith(Or36647Const.MESSAGE_PREFIX_4) ||
            msg.startsWith(Or36647Const.MESSAGE_PREFIX_7)
          ) {
            // 返却情報.対象行を「0」に設定する。
            targetRow = 0
          } else {
            // 返却情報.メッセージの先頭２桁が"3:" ro "4:" or "7:" が以外の場合
            await openConfirmDialog(t('message.i-cmn-10356'))
          }
        } else if (retCode === Or36647Const.PROCESS_FLG_9) {
          // 返却情報.リターンコード = 9 の場合
          await openConfirmDialog(t('message.i-cmn-10357'))
        }
      }
      // 返却情報.リターンコード<>0 AND 返却情報.対象行>0 の場合
      if (retCode !== Or36647Const.PROCESS_FLG_0 && targetRow > 0) {
        // 返却情報.表示メッセージリスト.対象行を対応する明細行を選択状態にする。
        local.selectedUsingSlipItemIndex = targetRow
      }

      // 利用票画面情報再計算
      const recomputeInputData: UseSlipInfoRecalculationSelectInEntity = {
        // 新規計算フラグ：TRUE
        fullCalc: Or36647Const.FULL_CALC_TRUE,
        // 提供年月：画面.提供年月
        teikyouYm: local.offeryYearMonth.value,
        // 提供月（日）：画面退避情報.提供月（日）
        teikyouYmD: local.usingSlipInfo.teikyouYmD,
        // 利用者ＩＤ
        userid: localOneway.or36647.userId,
        // 事業所ＩＤ
        svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
        // 適用事業所ＩＤリスト：親画面.適用事業所id配列
        svJigyoIdList: localOneway.or36647.applyOfficeIdList.map((item) => {
          return {
            svJigyoId: item,
          }
        }),
        // 利用者情報：画面.利用者情報
        riyourshaData: local.usingSlipInfo.riyourshaData,
        // 利用票明細情報：画面.利用票明細情報
        riyouList: getRiyouList(),
        // 別表明細情報：画面.別表明細情報
        riyouBeppyoList: getRiyouBeppyoList(),
        // 算定確認情報：画面.算定確認情報
        riyouKakuninList: local.usingSlipInfo.riyouKakuninList,
        // 種類別限度情報：画面.種類別限度情報
        syuruiGendoData: local.usingSlipInfo.syuruiGendoData,
        // 合計表示欄情報：画面.合計表示欄情報
        sumData: local.usingSlipInfo.sumData,
        // 公費集計欄情報：画面.公費集計欄情報
        kohiList: local.usingSlipInfo.kohiList,
        // 利用料集計欄情報
        riyouryouList: getRiyouryouList(),
        // 社福軽減集計欄情報：画面.社福軽減集計欄情報
        syafukuList: getSyafukuList(),
        // 短期入所連続利用30日超過情報：画面退避情報.短期入所連続利用30日超過情報
        planOv30List: local.usingSlipInfo.planOv30List,
        // 短期利用日数保持情報：画面退避情報.短期利用日数保持情報
        svplanShortList: local.usingSlipInfo.svplanShortList,
        // 提供事業所毎小計情報：画面退避情報.提供事業所毎小計情報
        servicePointList: local.usingSlipInfo.servicePointList,
        // 利用票画面処理用構造体：画面退避情報.利用票画面処理用構造体
        riyouProcessObject: local.usingSlipInfo.riyouProcessObject,
      }
      const recomputeRet: UseSlipInfoRecalculationSelectOutEntity = await ScreenRepository.select(
        'useSlipInfoRecalculationSelect',
        recomputeInputData
      )
      const riyou703Info = recomputeRet.data.riyou703Info[0]
      // 利用票明細情報入力フォームの各項目設定
      setUsingSlipInputForm(riyou703Info.riyouList)
      // 別表明細情報入力フォームの各項目設定
      setUsingSlipOtherTableInputForm(
        riyou703Info.riyouBeppyoList,
        riyou703Info.kohiList,
        [],
        [],
        false
      )
    }
  }
  return Or36647Const.CONFIRM_BTN_YES
}

/**
 * 印刷処理
 */
const usingSlipPrint = async () => {
  // 電子保存の3原則が適用しない AND 画面入力データの変更がある場合
  if (electronicSavePrinciple.value === false && isEdit.value) {
    const ret = await openConfirmDialog(t('message.i-cmn-10430'), true)
    // はい
    if (ret === Or36647Const.CONFIRM_BTN_YES) {
      // 保存処理実行
      const saveResult = await usingSlipSave()
      if (saveResult !== Or36647Const.CONFIRM_BTN_YES) {
        // 更新異常の場合、処理終了
        return false
      }
    } else if (ret === Or36647Const.CONFIRM_BTN_NO) {
      // いいえ：ダイアログ画面を閉じる、処理続き
    } else {
      // キャンセル：処理終了
      return false
    }
  }
  // GUI01147 印刷設定画面をポップアップで起動する。
  localOneway.or57151Oneway = {
    // svJigyoId: localOneway.or36245.officeId.modelValue,
    // 法人ID：親画面.法人ID
    houjinId: localOneway.or36647.corporationId,
    // 施設ID：親画面.施設ID
    shisetuId: localOneway.or36647.facilityId,
    // 事業者ID ：親画面.事業者ID
    // 利用者ID：親画面.利用者ID
    userId: localOneway.or36647.userId,
    // デフォルト値：1
    // セクション名：利用者毎印刷
    sectionName: Or36647Const.SECTION_NAME,
    // 50音行番号：画面で選択した50音行番号
    // 50音母音：画面で選択した50音母音
    // 提供年月：画面.提供年月
    teiYm: local.offeryYearMonth.value,
    // 担当ケアマネID：画面退避情報.利用者情報.担当者ID_hidden
    // e-文書の履歴保存フラグ：共通情報.e-文書の履歴保存フラグ
    historyFlg: localOneway.or36647.historySaveFlg,
    // 職員ID：親画面.職員ID
    shokuId: localOneway.or36647.staffId,
    // システムコード：親画面.システムコード
    sysCd: localOneway.or36647.sysCode,
    // 事業名（略称）：親画面.事業名（略称）
    // ロケーション：親画面.ロケーション
    local: localOneway.or36647.local,
    // 支援事業所ID：画面.利用者情報.支援事業者ID
    shienId: localOneway.or36647.shienId,
    // 適用事業所ＩＤリスト：親画面.適用事業所ＩＤリスト
  } as Or57151OnewayType
  // GUI01147 印刷設定画面をポップアップで起動する。
  Or57151Logic.state.set({
    uniqueCpId: or57151.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「マスタボタン」押下
 */
const master = () => {
  // TODO GUI00066_ポップアップ共通をポップアップで起動する
}

/**
 * 「メンテナンスボタン」押下
 */
const maintenance = () => {
  // GUI01169_利用票別表メンテナンスをポップアップで起動する
  localOneway.or26828Oneway = {} as Or26828OnewayType
  // Or26828のダイアログ開閉状態を更新する
  Or26828Logic.state.set({
    uniqueCpId: or26828.value.uniqueCpId,
    state: {
      isOpen: true,
      decisionMakingClickValue: Or36647Const.DECISION_MAKING_CLICK_VALUE_1,
    },
  })
}

/**
 * 「特別指示期間ボタン」押下
 */
const specialInstructionsPeriod = () => {
  localOneway.or28334Oneway = {
    // 支援事業所ID：親画面.事業所ＩＤ
    shienId: localOneway.or36647.shienId,
    // 利用者ID：親画面.利用者ＩＤ
    userId: localOneway.or36647.userId,
    // 提供年月：画面.提供年月
    yymmYm: local.offeryYearMonth.value,
  } as Or28334OnewayType
  // GUI01145_特別指示期間をポップアップで起動する
  Or28334Logic.state.set({
    uniqueCpId: or28334.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「短期退所登録ボタン」押下
 */
const shortTermLeavingRegist = () => {
  localOneway.or26835Oneway = {
    shienId: localOneway.or36647.shienId,
    userId: localOneway.or36647.userId,
    yymmYm: local.offeryYearMonth.value,
  } as Or26835OnewayType
  // GUI01161_短期退所日登録をポップアップで起動する
  Or26835Logic.state.set({
    uniqueCpId: or26835.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「福祉用具単位一括設定ボタン」押下
 */
const welfareEquipmentUnitBundleSettings = () => {
  // GUI01170_福祉用具貸与単位数一括設定をポップアップで起動する
  localOneway.or30707Oneway = {
    // 適用事業所ＩＤリスト
    svJigyoId: localOneway.or36647.officeInfoList.map((item) => item.officeId),
    // 支援事業所ID
    shienId: localOneway.or36647.svJigyoCd,
    // 提供年月
    teiYm: local.offeryYearMonth.value,
  } as Or30707OnewayType
  // Or30707のダイアログ開閉状態を更新する
  Or30707Logic.state.set({
    uniqueCpId: or30707.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「横出しサービス単位再設定ボタン」押下
 */
const horizontalServiceUnitReconfigure = () => {
  localOneway.or27943Oneway = {
    systemDate: localOneway.or36647.sysYearMonth,
    staffId: localOneway.or36647.staffId,
    systemCode: localOneway.or36647.sysCode,
    applicableOfficeIdList: localOneway.or36647.applyOfficeIdList,
  } as Or27943OnewayType
  // GUI01171_横出しサービス単位再設定をポップアップで起動する
  Or27943Logic.state.set({
    uniqueCpId: or27943.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「総合事業単位再設定ボタン」押下
 */
const comprehensiveProjectUnitReconfigure = () => {
  localOneway.or27946Oneway = {
    sysYm: localOneway.or36647.sysYearMonth,
    shokuId: localOneway.or36647.staffId,
    sysCd: localOneway.or36647.sysCode,
    applicableOfficeIdList: localOneway.or36647.applyOfficeIdList,
  } as Or27946OneWayType
  // GUI01172_総合事業サービス単位再設定をポップアップで起動する
  Or27946Logic.state.set({
    uniqueCpId: or27946.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「有効期間外サービスボタン」押下
 */
const validPeriodOtherThanService = () => {
  // GUI01048_有効期間外サービス検索をポップアップで起動する
  localOneway.or27736Oneway = {
    officeId: systemCommonsStore.getSvJigyoId ?? '',
    shokuinId: localOneway.or36647.staffId,
    sysCd: localOneway.or36647.sysCode,
    kinouId: localOneway.or36647.functionId,
    kenFlg: '',
    jgyouLst: [],
    showCsvBtn: true,
    periodStartDate: {
      value: '',
    },
    periodEndDate: {
      value: '',
    },
  } as Or27736OnewayType
  // Or27736のダイアログ開閉状態を更新する
  Or27736Logic.state.set({
    uniqueCpId: or27736.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「日割利用期間ボタン」押下
 */
const dailyRateUsingPeriod = () => {
  //［日割利用期間］画面を起動する
  // Or07212のダイアログ開閉状態を更新する
  Or07212Logic.state.set({
    uniqueCpId: or07212.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 週間取込
 */
const weekImport = async () => {
  // 利用者ID
  const userId = localOneway.or36647.userId
  // 利用者ID = null or 0の場合
  if (isEmpty(userId) || userId === Or36647Const.DEFAULT_USER_ID) {
    // 処理終了
    return false
  }
  // 利用者情報.実績済みフラグ = 1の場合
  if (local.inputWorkAchievements.modelValue) {
    await openConfirmDialog(t('message.i-cmn-10681'))
    // 処理終了
    return false
  }

  // 利用者情報.予定済みフラグ = 1の場合
  if (local.inputWorkSchedule.modelValue) {
    await openConfirmDialog(t('message.i-cmn-10682'))
    // 処理終了
    return false
  }

  localOneway.or26817Oneway = {
    //  利用者ID
    userId: userId,
    //  支援事業所
    svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
    //  職員ID
    shokuinId: localOneway.or36647.staffId,
    //  画面名称
    screenName: localOneway.or36647.screenName,
    //  開始年
    startYear: Or36647Const.START_YEAR,
    // 提供年月
    yymmYm: local.offeryYearMonth.value,
    // システムコード
    sysCd: localOneway.or36647.sysCode,
    // 利用票情報リスト
    usingSlipList: local.usingSlipList as unknown,
    // 確定済フラグ
    isConfirmFlg: local.confirmFlg ? 1 : 0,
    // 実績済フラグ
    isAchievementsFlg: local.inputWorkSchedule.modelValue ? 1 : 0,
    // 予定済フラグ
    isScheduleFlg: local.inputWorkAchievements.modelValue ? 1 : 0,
    // 処理年月を優先する
    processYymmPri: Or36647Const.PROCESS_YYMM_PRI,
    // 処理年月を指定する
    processYymmDes: Or36647Const.PROCESS_YYMM_DES,
    // yymm_d を入れる
    yymmD: Or36647Const.YYMM_D,
  } as Or26817OnewayType

  // GUI01152_週間計画取り込み画面をポップアップで起動する
  Or26817Logic.state.set({ uniqueCpId: or26817.value.uniqueCpId, state: { isOpen: true } })
}

/**
 * 雛形取込
 */
const prototypeImport = async () => {
  // 利用者情報.実績済みフラグ = 1の場合
  if (local.inputWorkAchievements.modelValue) {
    await openConfirmDialog(t('message.i-cmn-10681'))
    // 処理終了
    return false
  }
  // 利用者情報.予定済みフラグ = 1の場合
  if (local.inputWorkSchedule.modelValue) {
    await openConfirmDialog(t('message.i-cmn-10682'))
    // 処理終了
    return false
  }
  // 別表タブの場合
  if (local.mo00043.id === Or36647Const.TAB_ID_USING_SLIP_OTHER_TABLE) {
    await openWarningDialog(t('message.w-cmn-20096'))
    // 処理終了
    return false
  }
  localOneway.or28534Oneway = {
    shienId: localOneway.or36647.shienId,
    yymmYm: local.offeryYearMonth.value,
    ymIndicatedFlg: Or36647Const.YM_INDICATED_FLG,
  } as Or28534OnewayType
  // GUI01214_シミュレーション雛形選択画面をポップアップで起動する
  Or28534Logic.state.set({
    uniqueCpId: or28534.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 計画実績
 */
const planResult = async () => {
  // 画面入力データの変更がある場合
  if (isEdit.value) {
    // 複写
    await usingSlipCopy()
  }
  // TODO GUI01153_［計画転送・実績取込］（利用票）画面をポップアップで起動する
}

/**
 * 希望負担
 */
const hopeBurden = () => {
  localOneway.or00233Oneway = {
    // 利用者ID：親画面.利用者ID
    userId: localOneway.or36647.userId,
    // システム年月日：親画面.システム年月日
    sysYmd: localOneway.or36647.sysYearMonth,
  } as Or00233SelectInfoType
  //  GUI01174_[希望負担額登録]ダイアログをポップアップで起動する
  Or00233Logic.state.set({
    uniqueCpId: or00233.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 日割算定
 */
const dailyCalculation = async () => {
  // 画面入力データの変更がある場合
  if (isEdit.value) {
    // 複写
    await usingSlipCopy()
  }
  // 画面.別表明細情報がなし場合
  if (local.usingSlipOthrtTableList.length === 0) {
    await openConfirmDialog(t('message.i-cmn-11344'))
    // 処理終了
    return false
  }
  // 画面退避情報.算定確認情報がなし場合
  if (local.usingSlipInfo.riyouKakuninList.length === 0) {
    await openConfirmDialog(t('message.i-cmn-11345'))
    // 処理終了
    return false
  }
  // 別表明細情報がある 且つ 算定確認情報がある場合
  localOneway.or35957Oneway = {
    // 提供年月：画面.提供年月
    yymmYm: local.offeryYearMonth.value,
    // 作成年月日：画面.作成年月日
    yymmD: local.usingSlipInfo.riyourshaData[0].createYmd,
    // 被保険者名：画面.保険者名
    insuredName: local.usingSlipInfo.riyourshaData[0].dmyUserName,
    // 利用者ID：画面.利用者情報.利用者ID
    userid: local.usingSlipInfo.riyourshaData[0].userid,
    // 支援事業者ID：画面.利用者情報.支援事業者ID
    svJigyoId: localOneway.or36647.shienId,
  } as Or35957OnewayType
  //  GUI01154_[日割算定確認]画面をポップアップで起動する
  Or35957Logic.state.set({
    uniqueCpId: or35957.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 削除
 */
const usingSlipDelete = async () => {
  // 利用者情報.実績済みフラグ = 1の場合
  if (local.inputWorkAchievements.modelValue) {
    await openConfirmDialog(t('message.i-cmn-10681'))
    // 処理終了
    return false
  }
  // 利用者情報.予定済みフラグ = 1の場合
  if (local.inputWorkSchedule.modelValue) {
    await openConfirmDialog(t('message.i-cmn-10682'))
    // 処理終了
    return false
  }
  // 削除確認画面ダイアログを表示する
  const ret = await openConfirmDialog(
    t('message.i-cmn-11343', [t('label.using-slip-other-table-warning-message')]),
    true
  )
  // はい
  if (ret === Or36647Const.CONFIRM_BTN_YES) {
    // 利用票画面情報削除を行う
    const inputData: UseSlipInfoDeleteInEntity = {
      // 保険者番号：画面退避情報.利用者情報.保険者番号
      cmnTucPlanKHokenCd: local.usingSlipInfo.riyourshaData[0].dmyKHokenNo,
      // 事業所ID：親画面.提供年月
      svJigyoId: systemCommonsStore.getSvJigyoId ?? '',
      // 利用者ID：親画面.利用者ID
      userId: localOneway.or36647.userId,
      // 提供年月：画面.提供年月
      teikyouYm: local.offeryYearMonth.value,
      // 提供月（日）：画面退避情報.提供月（日）
      teikyouYmD: local.usingSlipInfo.teikyouYmD,
      // 処理名：親画面.画面設定用パラメータ.画面名称
      shoriname: Or36647Const.SCREEN_NAME_1,
      // 利用者情報存在フラグ：
      riyourshaDataExitFlg: (local.usingSlipInfo.riyourshaData.length > 0).toString(),
      // 退避用情報
      taihiData: [
        {
          // 退避用情報.利用票明細情報(初期化処理で取得された情報)
          riyouList: local.usingSlipInfo.riyouList,
          // 退避用情報.別表明細情報(初期化処理で取得された情報)
          riyouBeppyoList: local.usingSlipInfo.riyouBeppyoList,
          // 退避用情報.種類別限度情報(初期化処理で取得された情報)
          syuruiGendoData: local.usingSlipInfo.syuruiGendoData,
          // 退避用情報.公費集計欄情報(初期化処理で取得された情報)
          kohiList: local.usingSlipInfo.kohiList,
          // 退避用情報.利用料集計欄情報(初期化処理で取得された情報)
          riyouryouList: local.usingSlipInfo.riyouryouList,
          // 退避用情報.社福軽減集計欄情報(初期化処理で取得された情報)
          syafukuList: local.usingSlipInfo.syafukuList,
          // 退避用情報.短期入所連続利用30日超過情報(初期化処理で取得された情報)
          planOv30List: local.usingSlipInfo.planOv30List,
          // 退避用情報.短期利用日数保持情報(初期化処理で取得された情報)
          svplanShortList: local.usingSlipInfo.svplanShortList,
          // 退避用情報.提供事業所毎小計情報(初期化処理で取得された情報)
          servicePointList: local.usingSlipInfo.servicePointList,
          // 退避用情報.介護予防短期入所利用状況情報(初期化処理で取得された情報)
          tlcSvplanShortList: local.usingSlipInfo.tlcSvplanShortList,
          // 退避用情報.算定確認情報(初期化処理で取得された情報)
          riyouKakuninList: local.usingSlipInfo.riyouKakuninList,
        } as TaihiData,
      ],
    }
    const response: UseSlipInfoDeleteOutEntity = await ScreenRepository.select(
      'useSlipInfoDelete',
      inputData
    )
    // 返却情報.処理結果 = -1の場合
    if (parseInt(response.data.result) === -1) {
      // 処理終了
      return false
    }
    // 返却情報.電子ファイル保存設定フラグ = trueの場合
    if (response.data.bunshoKbn === Or36647Const.BUNSHO_KBN_1) {
      const retData = {
        ...localOneway.or36647,
      } as Or36647Type
      // 親画面.被保険者番号 = 画面退避情報.利用者情報.被保険者番号
      retData.insuredNo = local.usingSlipInfo.riyourshaData[0].dmyHHokenNo
      // 親画面.保険者番号 = 削除前のチェック情報の保険者番号
      retData.insurNo = response.data.kHokenNo
      // 親画面.保険者名 = 削除前のチェック情報の保険者名称
      retData.insurName = response.data.kHokenKnj
      // 情報値戻り
      emit('update:modelValue', retData)

      // 利用者情報がなしの場合
      if (local.usingSlipInfo.riyourshaData.length === 0) {
        // 電子保存の3原則適用 = True
        electronicSavePrinciple.value = true
        // 利用票出力フラグ
        usingSlipPrintFlg.value = Or36647Const.USING_SLIP_PRINT_FLG_0
        // 利用票別表出力フラグ
        usingSlipOtherTablePrintFlg.value = Or36647Const.USING_SLIP_OTHER_TABLE_PRINT_FLG_0
      }
    } else {
      // 利用者情報がある場合
      // 電子保存の3原則適用 = True
      electronicSavePrinciple.value = true
      // 削除履歴出力区分 = 0：更新 の場合
      if (deleteHistoryOutputKbn.value === Or36647Const.DELETE_HISTORY_OUTPUT_KBN_0) {
        // 利用票出力フラグ
        usingSlipPrintFlg.value = Or36647Const.USING_SLIP_PRINT_FLG_2
        // 利用票別表出力フラグ
        usingSlipOtherTablePrintFlg.value = Or36647Const.USING_SLIP_OTHER_TABLE_PRINT_FLG_2
      }
    }
    // 印刷処理
    await usingSlipPrint()
    // 利用票出力フラグ
    usingSlipPrintFlg.value = Or36647Const.USING_SLIP_PRINT_FLG_0
    // 利用票別表出力フラグ
    usingSlipOtherTablePrintFlg.value = Or36647Const.USING_SLIP_OTHER_TABLE_PRINT_FLG_0
    // 利用票情報情報取得
    await useSlipInitInfoSelect()
  }
}

/**
 * 「保険者選択アイコンボタン」押下
 */
const openInsurDialog = async () => {
  // 画面入力データの変更がある場合
  if (isEdit.value) {
    // 複写
    await usingSlipCopy()
  }
  localOneway.or27635Oneway = {
    // 提供日：画面退避情報.提供月（日）
    offerDay: local.usingSlipInfo.teikyouYmD,
    // 利用票履歴リスト：画面退避情報.利用票画面処理用構造体
    historyList: local.usingSlipInfo.riyouProcessObject[0].hokenInfo[0].planRireki.map((item) => {
      return {
        modifiedDay: item.teiYmD,
        insurerNo: item.kHokenNo,
        insurer: item.kHokenKnj,
        insuredPersonNo: item.hHokenNo,
        serialNo: item.seqNo,
      }
    }),
  } as Or27635OnewayType
  // GUI01168 保険者選択をポップアップで起動する
  Or27635Logic.state.set({
    uniqueCpId: or27635.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 利用票明細一覧ソート
 *
 * @param sortItem -ソート項目
 */
const usingSlipSort = async (sortItem: string) => {
  // 利用票・別表のソート処理
  const inputData: UseSlipInfoSortSelectInEntity = {
    // 利用票明細情報：画面.利用票明細情報
    riyouList: getRiyouList(),
    // ソート項目
    sortItem: sortItem,
  }
  const response: UseSlipInfoSortSelectOutEntity = await ScreenRepository.select(
    'useSlipInfoSortSelect',
    inputData
  )
  // 入力フォームの各項目へ設定する
  setUsingSlipInputForm(response.data.riyou1110Info[0].riyouList)
}

/**
 * 「予」チェックボックス選択
 *
 * @param index -index
 */
const schedChange = async (index: number) => {
  if (local.usingSlipList[index].yoteiZumiFlg.modelValue) {
    // 利用者情報.実績済みフラグ = 1の場合
    if (local.inputWorkAchievements.modelValue) {
      await openConfirmDialog(t('message.i-cmn-11362'))
      local.usingSlipList[index].yoteiZumiFlg.modelValue = false
      // 処理終了
      return false
    }
    // 利用者情報.予定済みフラグ = 1の場合
    if (local.inputWorkSchedule.modelValue) {
      await openConfirmDialog(t('message.i-cmn-11363'))
      local.usingSlipList[index].yoteiZumiFlg.modelValue = false
      // 処理終了
      return false
    }
    // 選択の明細行.実 = 1の場合
    if (local.usingSlipList[index].jissekiZumiFlg.modelValue) {
      await openConfirmDialog(t('message.i-cmn-11364'))
      local.usingSlipList[index].yoteiZumiFlg.modelValue = false
      // 処理終了
      return false
    }
    // 予実変換後画面の再設定処理
    const inputData: UseSlipInfoPredictionChangeRowAftProcSelectInEntity = {
      // 処理区分：0:予
      processKbn: Or36647Const.PROCESS_KBN_SCHED,
      // 選択の行番号：選択の行番号
      lineNo: index.toString(),
      // 利用者情報：画面.利用者情報
      riyourshaData: local.usingSlipInfo.riyourshaData,
      // 利用票明細情報：画面.利用票明細情報
      riyouList: getRiyouList(),
    }
    const response: UseSlipInfoPredictionChangeRowAftProcSelectOutEntity =
      await ScreenRepository.select('useSlipInfoPredictionChangeRowAftProcSelect', inputData)
    // 返却情報.予定済 = 1の場合
    if (response.data.riyou896Info[0].yoteiZumi === Or36647Const.SCHED_FLG_OK) {
      // 全て利用票明細データの予定済フラグが1の場合
      const schedOkUsingSlipList = local.usingSlipList.filter(
        (item) => item.yoteiZumiFlg.modelValue === true
      )
      if (schedOkUsingSlipList.length === local.usingSlipList.length) {
        // 利用者情報.予定済を1で設定する
        local.inputWorkSchedule.modelValue = true
      }
    } else {
      // 返却情報.予定済 = 0の場合
      // 全て利用票明細データの予定済フラグが1の場合
      const schedUsingSlipList = local.usingSlipList.filter(
        (item) => item.yoteiZumiFlg.modelValue === false
      )
      if (schedUsingSlipList.length === local.usingSlipList.length) {
        // 利用者情報.予定済を0で設定する
        local.inputWorkSchedule.modelValue = false
      }
    }
  }
}

/**
 * 「実」チェックボックス選択
 *
 * @param index -index
 */
const achvChange = async (index: number) => {
  if (local.usingSlipList[index].jissekiZumiFlg.modelValue) {
    // 利用者情報.実績済みフラグ = 1の場合
    if (local.inputWorkAchievements.modelValue) {
      await openConfirmDialog(t('message.i-cmn-11365'))
      local.usingSlipList[index].jissekiZumiFlg.modelValue = false
      // 処理終了
      return false
    }
    // 利用者情報.予定済みフラグ = 1の場合
    if (local.inputWorkSchedule.modelValue) {
      await openConfirmDialog(t('message.i-cmn-11366'))
      local.usingSlipList[index].jissekiZumiFlg.modelValue = false
      // 処理終了
      return false
    }
    // 選択の明細行.予 = 1の場合
    if (local.usingSlipList[index].yoteiZumiFlg.modelValue) {
      await openConfirmDialog(t('message.i-cmn-11364'))
      local.usingSlipList[index].jissekiZumiFlg.modelValue = false
      // 処理終了
      return false
    }
    // 予実変換後画面の再設定処理
    const inputData: UseSlipInfoPredictionChangeRowAftProcSelectInEntity = {
      // 処理区分：1:実
      processKbn: Or36647Const.PROCESS_KBN_ACHV,
      // 選択の行番号：選択の行番号
      lineNo: index.toString(),
      // 利用者情報：画面.利用者情報
      riyourshaData: local.usingSlipInfo.riyourshaData,
      // 利用票明細情報：画面.利用票明細情報
      riyouList: getRiyouList(),
    }
    const response: UseSlipInfoPredictionChangeRowAftProcSelectOutEntity =
      await ScreenRepository.select('useSlipInfoPredictionChangeRowAftProcSelect', inputData)
    // 返却情報.実績済 = 1の場合
    if (response.data.riyou896Info[0].jissekeiZumi === Or36647Const.ACHV_FLG_OK) {
      // 全て利用票明細データの実績済みフラグが1の場合
      const achvOkUsingSlipList = local.usingSlipList.filter(
        (item) => item.jissekiZumiFlg.modelValue === true
      )
      if (achvOkUsingSlipList.length === local.usingSlipList.length) {
        // 利用者情報.実績済みを1で設定する
        local.inputWorkAchievements.modelValue = true
      }
    } else {
      // 返却情報.実績済 = 0の場合
      // 全て利用票明細データの実績済みフラグが1の場合
      const achvUsingSlipList = local.usingSlipList.filter(
        (item) => item.jissekiZumiFlg.modelValue === false
      )
      if (achvUsingSlipList.length === local.usingSlipList.length) {
        // 利用者情報.実績済みを0で設定する
        local.inputWorkAchievements.modelValue = false
      }
    }
  }
}

/**
 * 「提供時間(サービス開始時間)」変更入力
 *
 * @param index -index
 */
const offerHoursStartTimeChange = (index: number) => {
  if (local.usingSlipList[index].oyaLineNo !== Or36647Const.DEFAULT_PARENT_NO) {
    local.usingSlipList
      .filter((item) => item.oyaLineNo === local.usingSlipList[index].oyaLineNo)
      .forEach((item) => {
        item.dmyStartTime = local.usingSlipList[index].svStartTime.value
      })
  }
}

/**
 * 「提供時間(サービス終了時間)」変更入力
 *
 * @param index -index
 */
const offerHoursEndTimeChange = (index: number) => {
  if (local.usingSlipList[index].oyaLineNo !== Or36647Const.DEFAULT_PARENT_NO) {
    local.usingSlipList
      .filter((item) => item.oyaLineNo === local.usingSlipList[index].oyaLineNo)
      .forEach((item) => {
        item.dmyEndTime = local.usingSlipList[index].svEndTime.value
      })
  }
}

/**
 * 「予定貸与フラグ」変更
 *
 * @param index -index
 */
const schedCodeChange = async (index: number) => {
  const item = local.usingSlipList[index]
  item.schedChangeFlg = true
  let schedTotal = 1
  // 福祉用具貸与フラグ（予定） = 1：日割 or 3：30日割 or 4：31日割 の場合
  if (
    item.yRentalF.modelValue === Or36647Const.WELFARE_EQUIP_LENDING_1 ||
    item.yRentalF.modelValue === Or36647Const.WELFARE_EQUIP_LENDING_3 ||
    item.yRentalF.modelValue === Or36647Const.WELFARE_EQUIP_LENDING_4
  ) {
    const dayList = offeryYearMonthDayList.value.map(
      (item) =>
        Or36647Const.PREFIX_SCHED + item.day.toString().padStart(2, Or36647Const.PREFIX_ZERO)
    )
    // 予定回数1～予定回数31の合計
    for (const key in item) {
      if (dayList.includes(key)) {
        const value = item[key as keyof typeof item] as TimeModelValue
        schedTotal += parseInt(value.value)
      }
    }
  }
  item.schedChangeValue = schedTotal
  // 利用票画面予実貸与フラグ変更後の再設定処理を行う
  const inputData: UseSlipInfoPredictionLendingChangeRowAftProcSelectInEntity = {
    // 利用票明細情報：画面.利用票明細情報
    riyouList: getRiyouList(),
    // 提供年月：画面.提供年月
    teikyouYm: local.offeryYearMonth.value,
    // 提供月（日）：画面退避情報.提供月（日）
    teikyouYmD: local.usingSlipInfo.teikyouYmD,
  }
  const response: UseSlipInfoPredictionLendingChangeRowAftProcSelectOutEntity =
    await ScreenRepository.select('useSlipInfoPredictionLendingChangeRowAftProcSelect', inputData)
  // 入力フォームの各項目へ設定する
  setUsingSlipInputForm(response.data.riyou897Info[0].riyouList)
}

/**
 * 「実績貸与フラグ」変更
 *
 * @param index -index
 */
const achvCodeChange = async (index: number) => {
  const item = local.usingSlipList[index]
  item.achvChangeFlg = true
  let achvTotal = 1
  // 福祉用具貸与フラグ（予定） = 1：日割 or 3：30日割 or 4：31日割 の場合
  if (
    item.jRentalF.modelValue === Or36647Const.WELFARE_EQUIP_LENDING_1 ||
    item.jRentalF.modelValue === Or36647Const.WELFARE_EQUIP_LENDING_3 ||
    item.jRentalF.modelValue === Or36647Const.WELFARE_EQUIP_LENDING_4
  ) {
    const dayList = offeryYearMonthDayList.value.map(
      (item) => Or36647Const.PREFIX_ACHV + item.day.toString().padStart(2, Or36647Const.PREFIX_ZERO)
    )
    // 予定回数1～予定回数31の合計
    for (const key in item) {
      if (dayList.includes(key)) {
        const value = item[key as keyof typeof item] as TimeModelValue
        achvTotal += parseInt(value.value)
      }
    }
  }
  item.achvChangeValue = achvTotal
  // 利用票画面予実貸与フラグ変更後の再設定処理を行う
  const inputData: UseSlipInfoPredictionLendingChangeRowAftProcSelectInEntity = {
    // 利用票明細情報：画面.利用票明細情報
    riyouList: getRiyouList(),
    // 提供年月：画面.提供年月
    teikyouYm: local.offeryYearMonth.value,
    // 提供月（日）：画面退避情報.提供月（日）
    teikyouYmD: local.usingSlipInfo.teikyouYmD,
  }
  const response: UseSlipInfoPredictionLendingChangeRowAftProcSelectOutEntity =
    await ScreenRepository.select('useSlipInfoPredictionLendingChangeRowAftProcSelect', inputData)
  // 入力フォームの各項目へ設定する
  setUsingSlipInputForm(response.data.riyou897Info[0].riyouList)
}

/**
 * 「予定カレンダーアイコンボタン」押下
 *
 * @param index -index
 */
const schedCalendarHandler = async (index: number) => {
  // 利用者情報.実績済みフラグ = 1の場合
  if (local.inputWorkAchievements.modelValue) {
    await openConfirmDialog(t('message.i-cmn-11368'))
    // 処理終了
    return false
  }
  // 利用者情報.予定済みフラグ = 1の場合
  if (local.inputWorkSchedule.modelValue) {
    await openConfirmDialog(t('message.i-cmn-11369'))
    // 処理終了
    return false
  }
  // GUI01158_カレンダー入力画面をポップアップで起動する
  localOneway.or27016Oneway = {
    // 予定・実績
    scheduleOrReal: Or36647Const.SCHEDULE_OR_REAL,
    // 予定済
    scheduled: Or36647Const.SCHED_FLG_NG,
    // 提供年月
    provideYm: local.offeryYearMonth.value,
    // ロケーション
    location: localOneway.or36647.local,
    // カレント行
    currentRow: index.toString(),
    // 変更日
    updateDate: local.usingSlipInfo.teikyouYmD,
  }
  // Or27487のダイアログ開閉状態を更新する
  Or27016Logic.state.set({
    uniqueCpId: or27016.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「実績カレンダーアイコンボタン」押下
 *
 * @param index -index
 */
const achvCalendarHandler = async (index: number) => {
  // 利用者情報.実績済みフラグ = 1の場合
  if (local.inputWorkAchievements.modelValue) {
    await openConfirmDialog(t('message.i-cmn-11368'))
    // 処理終了
    return false
  }
  // GUI01158_カレンダー入力画面をポップアップで起動する
  localOneway.or27016Oneway = {
    // 予定・実績
    scheduleOrReal: Or36647Const.SCHEDULE_OR_REAL,
    // 予定済
    scheduled: local.inputWorkSchedule.modelValue
      ? Or36647Const.SCHED_FLG_OK
      : Or36647Const.SCHED_FLG_NG,
    // 提供年月
    provideYm: local.offeryYearMonth.value,
    // ロケーション
    location: localOneway.or36647.local,
    // カレント行
    currentRow: index.toString(),
    // 変更日
    updateDate: local.usingSlipInfo.teikyouYmD,
  }
  // Or27487のダイアログ開閉状態を更新する
  Or27016Logic.state.set({
    uniqueCpId: or27016.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「予定数」変更入力
 *
 * @param index -index
 *
 * @param day -day
 */
const schedDayChange = async (index: number, day: number) => {
  const selectedItem = local.usingSlipList[index]
  const proItem = getSchedAchvDayName(Or36647Const.PREFIX_SCHED, day)
  // 予定回数が""を入力する場合
  const schedDay = selectedItem[proItem as keyof typeof selectedItem] as TimeModelValue
  if (schedDay.value === '') {
    schedDay.value = Or36647Const.DEFAULT_KAISU
  }
  // 回数入力変更前のチェック情報を取得する
  const inputData: UseSlipInfoNumberOfTimesModifiedBefCheckSelectInEntity = {
    // 提供年月：画面.提供年月
    teikyouYm: local.offeryYearMonth.value,
    // 利用票明細情報：画面.利用票明細情報
    riyouList: getRiyouList(),
    // 選択の行番号：選択の行番号
    lineNo: local.selectedUsingSlipItemIndex.toString(),
    // 選択の項目名：選択の項目名
    proItem: proItem,
    // 選択の項目の値：選択の項目の値
    proValue: schedDay.value,
    // 利用票画面処理用構造体：画面退避情報.利用票画面処理用構造体
    riyouProcessObject: local.usingSlipInfo.riyouProcessObject,
  }
  const resp: UseSlipInfoNumberOfTimesModifiedBefCheckSelectOutEntity =
    await ScreenRepository.select('useSlipInfoNumberOfTimesModifiedBefCheckSelect', inputData)
  // 返却情報.メッセージ有無フラグ = 1の場合
  if (resp.data.riyou1109Info[0].messageFlag === Or36647Const.HAS_MESSAGE_FLG_1) {
    await openConfirmDialog(t('message.i-cmn-10073'))
    // 処理終了
    return false
  } else if (resp.data.riyou1109Info[0].messageFlag === Or36647Const.HAS_MESSAGE_FLG_2) {
    // 返却情報.メッセージ有無フラグ = 2の場合
    await openConfirmDialog(t('message.i-cmn-10064'))
    // 処理終了
    return false
  } else {
    // 返却情報.メッセージ有無フラグ <>1 and 返却情報.メッセージ有無フラグ <> 2
    // 処理対象 = 利用票明細情報で、親レコード番号 = 選択行の親レコード番号 and 合成識別区分 <> "3"のデータ、１件目が処理対象として（親サービスの中の１件目）
    const processTarget = local.usingSlipList.find(
      (item) =>
        item.oyaLineNo === selectedItem.oyaLineNo &&
        item.gouseiSikKbn === Or36647Const.GOUSEI_SIK_KBN_3
    )
    // 予定回数
    let targetSchedDay = ''
    if (processTarget !== undefined) {
      targetSchedDay = (processTarget[proItem as keyof typeof processTarget] as TimeModelValue)
        .value
    }
    // 加算サービスフラグ = true and 処理対象.予定回数 = 0の場合
    if (
      resp.data.riyou1109Info[0].kasanService === Or36647Const.KASAN_SERVICE_1 &&
      targetSchedDay === Or36647Const.DEFAULT_KAISU
    ) {
      await openConfirmDialog(t('message.i-cmn-10069'))
      // 処理終了
      return false
    }
    // 入力フォームの各項目へ設定
    setUsingSlipInputForm(resp.data.riyou1109Info[0].riyouList)
  }
}

/**
 * 「実績数」変更入力
 *
 * @param index -index
 *
 * @param day -day
 */
const achvDayChange = async (index: number, day: number) => {
  const selectedItem = local.usingSlipList[index]
  const proItem = getSchedAchvDayName(Or36647Const.PREFIX_ACHV, day)
  // 実績回数が""を入力する場合
  const achvDay = selectedItem[proItem as keyof typeof selectedItem] as TimeModelValue
  if (achvDay.value === '') {
    achvDay.value = Or36647Const.DEFAULT_KAISU
  }
  // 回数入力変更前のチェック情報を取得する
  const inputData: UseSlipInfoNumberOfTimesModifiedBefCheckSelectInEntity = {
    // 提供年月：画面.提供年月
    teikyouYm: local.offeryYearMonth.value,
    // 利用票明細情報：画面.利用票明細情報
    riyouList: getRiyouList(),
    // 選択の行番号：選択の行番号
    lineNo: local.selectedUsingSlipItemIndex.toString(),
    // 選択の項目名：選択の項目名
    proItem: proItem,
    // 選択の項目の値：選択の項目の値
    proValue: achvDay.value,
    // 利用票画面処理用構造体：画面退避情報.利用票画面処理用構造体
    riyouProcessObject: local.usingSlipInfo.riyouProcessObject,
  }
  const resp: UseSlipInfoNumberOfTimesModifiedBefCheckSelectOutEntity =
    await ScreenRepository.select('useSlipInfoNumberOfTimesModifiedBefCheckSelect', inputData)
  // 返却情報.メッセージ有無フラグ = 1の場合
  if (resp.data.riyou1109Info[0].messageFlag === Or36647Const.HAS_MESSAGE_FLG_1) {
    await openConfirmDialog(t('message.i-cmn-10073'))
    // 処理終了
    return false
  } else if (resp.data.riyou1109Info[0].messageFlag === Or36647Const.HAS_MESSAGE_FLG_2) {
    // 返却情報.メッセージ有無フラグ = 2の場合
    await openConfirmDialog(t('message.i-cmn-10064'))
    // 処理終了
    return false
  } else {
    // 処理対象 = 利用票明細情報で、親レコード番号 = 選択行の親レコード番号 and 合成識別区分 <> "3"のデータ、１件目が処理対象として（親サービスの中の１件目）
    const processTarget = local.usingSlipList.find(
      (item) =>
        item.oyaLineNo === selectedItem.oyaLineNo &&
        item.gouseiSikKbn === Or36647Const.GOUSEI_SIK_KBN_3
    )
    // 予定回数
    let targetAchvDay = ''
    if (processTarget !== undefined) {
      targetAchvDay = (processTarget[proItem as keyof typeof processTarget] as TimeModelValue).value
    }
    // 加算サービスフラグ = true and 処理対象.予定回数 = 0の場合
    if (
      resp.data.riyou1109Info[0].kasanService === Or36647Const.KASAN_SERVICE_1 &&
      targetAchvDay === Or36647Const.DEFAULT_KAISU
    ) {
      await openConfirmDialog(t('message.i-cmn-10069'))
      // 処理終了
      return false
    }
    // 入力フォームの各項目へ設定
    setUsingSlipInputForm(resp.data.riyou1109Info[0].riyouList)
  }
}

/**
 * 「予定数」ダブルクリック
 *
 * @param index -index
 *
 * @param day -day
 *
 * @param event -Event
 */
const doubleSchedDayClick = async (index: number, day: number, event: Event) => {
  const proItem = getSchedAchvDayName(Or36647Const.PREFIX_SCHED, day)
  let rowNum = 0
  const selectedItem = local.usingSlipList[index]
  // 処理対象 = 利用票明細情報で、親レコード番号 = 選択行の親レコード番号 and 合成識別区分 <> "3"のデータ、１件目が処理対象として（親サービスの中の１件目）
  const processTarget = local.usingSlipList.find(
    (item) =>
      item.oyaLineNo === selectedItem.oyaLineNo &&
      item.gouseiSikKbn === Or36647Const.GOUSEI_SIK_KBN_3
  )
  // 選択の予定回数
  const schedDay = selectedItem[proItem as keyof typeof selectedItem] as TimeModelValue
  // 回数ダブルクリック変更前のチェック情報を取得する
  const inputData: UseSlipInfoNumberOfTimesModifiedBefSelectInEntity = {
    // 提供年月：画面.提供年月
    teikyouYm: local.offeryYearMonth.value,
    // 利用票明細情報：画面.利用票明細情報
    riyouList: getRiyouList(),
    // 選択の行番号：選択の行番号
    lineNo: local.selectedUsingSlipItemIndex.toString(),
    // 選択の項目名：選択の項目名
    proItem: proItem,
    // 選択の項目の値：選択の項目の値
    proValue: schedDay.value,
    // 利用票画面処理用構造体：画面退避情報.利用票画面処理用構造体
    riyouProcessObject: local.usingSlipInfo.riyouProcessObject,
  }
  const resp: UseSlipInfoNumberOfTimesModifiedBefSelectOutEntity = await ScreenRepository.select(
    'useSlipInfoNumberOfTimesModifiedBefSelect',
    inputData
  )
  // 選択の予定回数 = 0 and 一時変数.行数 = 選択の行番号 and 返却情報.加算サービスフラグ = true
  if (
    schedDay.value === Or36647Const.DEFAULT_KAISU &&
    rowNum === index &&
    resp.data.riyou898Info[0].kasanService === Or36647Const.KASAN_SERVICE_1
  ) {
    let targetSchedDay = ''
    if (processTarget !== undefined) {
      targetSchedDay = (processTarget[proItem as keyof typeof processTarget] as TimeModelValue)
        .value
      // 一時変数.行数 = 処理対象の行番号
      rowNum = local.usingSlipList.findIndex((item) => item.scode === processTarget.scode)
    }
    // 処理対象.予定回数 = 0の場合
    if (targetSchedDay === '0') {
      await openConfirmDialog(t('message.i-cmn-10069'))
      // 処理終了
      return false
    }
  }
  // 選択の予定回数 = 0 and 一時変数.行数 > 0 and 選択行のサービス種別コード = "14" or "64" and 処理対象.サービスコード <> "00000000"の場合
  if (
    schedDay.value === Or36647Const.DEFAULT_KAISU &&
    rowNum > 0 &&
    (selectedItem.svtype === Or36647Const.SERVICE_TYPECODE_14 ||
      selectedItem.svtype === Or36647Const.SERVICE_TYPECODE_64) &&
    processTarget?.scode !== Or36647Const.SERVICE_CD_DEFAULT
  ) {
    // 選択行の予定回数 = 返却情報.提供時間より得た回数
    schedDay.value = resp.data.riyou898Info[0].kaisu
  }
  // 選択の予定回数 = 0 and 一時変数.行数 <= 0の場合
  if (schedDay.value === Or36647Const.DEFAULT_KAISU && rowNum <= 0) {
    // 画面.選択行の予定回数 = 1
    schedDay.value = Or36647Const.KAISU_1
  }
  // 選択の予定回数 <> 0の場合
  if (schedDay.value !== Or36647Const.DEFAULT_KAISU) {
    // 画面.選択行の予定回数 = 0
    schedDay.value = Or36647Const.DEFAULT_KAISU
  }
  // 返却情報.メッセージ有無フラグ = 1の場合
  if (resp.data.riyou898Info[0].messageFlag === Or36647Const.HAS_MESSAGE_FLG_1) {
    await openConfirmDialog(t('message.i-cmn-10073'))
    // 処理終了
    return false
  }
  // 入力フォームの各項目へ設定
  setUsingSlipInputForm(resp.data.riyou898Info[0].riyouList)
  event.stopPropagation()
}

/**
 * 「実績数」ダブルクリック
 *
 * @param index -index
 *
 * @param day -day
 *
 * @param event -Event
 */
const doubleAchvDayClick = async (index: number, day: number, event: Event) => {
  const proItem = getSchedAchvDayName(Or36647Const.PREFIX_ACHV, day)
  let rowNum = 0
  const selectedItem = local.usingSlipList[index]
  // 処理対象 = 利用票明細情報で、親レコード番号 = 選択行の親レコード番号 and 合成識別区分 <> "3"のデータ、１件目が処理対象として（親サービスの中の１件目）
  const processTarget = local.usingSlipList.find(
    (item) =>
      item.oyaLineNo === selectedItem.oyaLineNo &&
      item.gouseiSikKbn === Or36647Const.GOUSEI_SIK_KBN_3
  )
  // 選択の実績回数
  const achvDay = selectedItem[proItem as keyof typeof selectedItem] as TimeModelValue
  // 回数ダブルクリック変更前のチェック情報を取得する
  const inputData: UseSlipInfoNumberOfTimesModifiedBefSelectInEntity = {
    // 提供年月：画面.提供年月
    teikyouYm: local.offeryYearMonth.value,
    // 利用票明細情報：画面.利用票明細情報
    riyouList: getRiyouList(),
    // 選択の行番号：選択の行番号
    lineNo: local.selectedUsingSlipItemIndex.toString(),
    // 選択の項目名：選択の項目名
    proItem: proItem,
    // 選択の項目の値：選択の項目の値
    proValue: achvDay.value,
    // 利用票画面処理用構造体：画面退避情報.利用票画面処理用構造体
    riyouProcessObject: local.usingSlipInfo.riyouProcessObject,
  }
  const resp: UseSlipInfoNumberOfTimesModifiedBefSelectOutEntity = await ScreenRepository.select(
    'useSlipInfoNumberOfTimesModifiedBefSelect',
    inputData
  )
  // 選択の実績回数 = 0 and 一時変数.行数 = 選択の行番号 and 返却情報.加算サービスフラグ = true
  if (
    achvDay.value === Or36647Const.DEFAULT_KAISU &&
    rowNum === index &&
    resp.data.riyou898Info[0].kasanService === Or36647Const.KASAN_SERVICE_1
  ) {
    let targetAchvDay = ''
    if (processTarget !== undefined) {
      targetAchvDay = (processTarget[proItem as keyof typeof processTarget] as TimeModelValue).value
      // 一時変数.行数 = 処理対象の行番号
      rowNum = local.usingSlipList.findIndex((item) => item.scode === processTarget.scode)
    }
    // 処理対象.実績回数 = 0の場合
    if (targetAchvDay === Or36647Const.DEFAULT_KAISU) {
      await openConfirmDialog(t('message.i-cmn-10069'))
      // 処理終了
      return false
    }
  }
  // 選択の実績回数 = 0 and 一時変数.行数 > 0 and 選択行のサービス種別コード = "14" or "64" and 処理対象.サービスコード <> "00000000"の場合
  if (
    achvDay.value === Or36647Const.DEFAULT_KAISU &&
    rowNum > 0 &&
    (selectedItem.svtype === Or36647Const.SERVICE_TYPECODE_14 ||
      selectedItem.svtype === Or36647Const.SERVICE_TYPECODE_64) &&
    processTarget?.scode !== Or36647Const.SERVICE_CD_DEFAULT
  ) {
    // 選択行の実績回数 = 返却情報.提供時間より得た回数
    achvDay.value = resp.data.riyou898Info[0].kaisu
  }
  // 選択の実績回数 = 0 and 一時変数.行数 <= 0の場合
  if (achvDay.value === Or36647Const.DEFAULT_KAISU && rowNum <= 0) {
    // 画面.選択行の実績回数 = 1
    achvDay.value = Or36647Const.KAISU_1
  }
  // 選択の実績回数 <> 0の場合
  if (achvDay.value !== Or36647Const.DEFAULT_KAISU) {
    // 画面.選択行の実績回数 = 0
    achvDay.value = Or36647Const.DEFAULT_KAISU
  }
  // 返却情報.メッセージ有無フラグ = 1の場合
  if (resp.data.riyou898Info[0].messageFlag === Or36647Const.HAS_MESSAGE_FLG_1) {
    await openConfirmDialog(t('message.i-cmn-10073'))
    // 処理終了
    return false
  }
  // 入力フォームの各項目へ設定
  setUsingSlipInputForm(resp.data.riyou898Info[0].riyouList)
  event.stopPropagation()
}

/**
 * 親子関係にあるサービスのサービス事業者項目とサービス内容項目の背景色
 *
 * @param item -利用票明細情報
 */
const isParentRow = (item: UsingSlipDetailInfo) => {
  const selectedItem = local.usingSlipList[local.selectedUsingSlipItemIndex]
  if (selectedItem.oyaLineNo) {
    return (
      item.oyaLineNo !== Or36647Const.DEFAULT_PARENT_NO && item.oyaLineNo === selectedItem.oyaLineNo
    )
  } else {
    return false
  }
}

/**
 * 行複写処理
 *
 * @param processFlg -処理フラグ
 */
const lineCopy = async (processFlg: string) => {
  // 行複写処理を行う
  const inputData: UseSlipInfoDuplicateRowAftProcSelectInEntity = {
    // 利用票明細情報：画面.利用票明細情報
    riyouList: getRiyouList(),
    // 処理フラグ：一時変数.処理フラグ
    proFlag: processFlg,
    // 選択行インデックス：選択された利用票明細情報のインデックス
    selIdxList: local.selectedUsingSlipItemIndex.toString(),
  }
  const response: UseSlipInfoDuplicateRowAftProcSelectOutEntity = await ScreenRepository.select(
    'useSlipInfoDuplicateRowAftProcSelect',
    inputData
  )
  // 入力フォームの各項目へ設定する。
  setUsingSlipInputForm(response.data.riyou892Info[0].riyouList)
}

/**
 * 利用票別表行選択
 *
 * @param index - 選択した行のindex
 */
const selectUsingSlipOtherTableRow = (index: number) => {
  local.selectedUsingSlipOtherTableItemIndex = index
}

/**
 *  種類別支給限度額欄
 *
 * @param index - 選択した行のindex
 */
const selectForeheadTableRow = (index: number) => {
  local.selectedForehead = index
}

/**
 *  公費適用事業所
 *
 * @param index - 選択した行のindex
 */
const selectPublicTableRow = (index: number) => {
  local.selectedPublic = index
}

/**
 * 短期入所利用日数
 *
 * @param index - 選択した行のindex
 */
const selectShortTableRow = (index: number) => {
  local.selectedShort = index
}
/**
 * 単位数計算結果
 *
 * @param index - 選択した行のindex
 */
const selectUnitTableRow = (index: number) => {
  local.selectedUnit = index
}
/**
 * 区分支給限度
 *
 * @param index - 選択した行のindex
 */
const selectDivisionTableRow = (index: number) => {
  local.selectedDivision = index
}
/**
 * 利用者負担額
 *
 * @param index - 選択した行のindex
 */
const selectBurdenTableRow = (index: number) => {
  local.selectedBurden = index
}
/**
 * 保険外利用料
 *
 * @param index - 選択した行のindex
 */
const selectInsuranceTableRow = (index: number) => {
  local.selectedInsurance = index
}

/**
 * 利用票別表合計
 *
 * @param key -合計フィールド
 */
const getUsingSlipOtherTableTotal = (key: string) => {
  let total = 0
  if (local.usingSlipOthrtTableList.length > 0) {
    for (const usingSlipOthrtTable of local.usingSlipOthrtTableList) {
      const value = usingSlipOthrtTable[key as keyof typeof usingSlipOthrtTable] as string
      total += parseInt(value)
    }
  }
  return total
}

/**
 * 利用料集計欄一覧行選択
 *
 * @param index - 選択した行のindex
 */
const selectusingFeeSumRow = (index: number) => {
  local.selectedRiyouryouItemIndex = index
}

/**
 * 利用票別表行選択
 *
 * @param index - 選択した行のindex
 */
const selectSocialWelfareAlleviationSumRow = (index: number) => {
  local.selectedSyafukuItemIndex = index
}

/**
 * 単位数/割引後_率％/割引後_単位数/回数 表示/非表示
 *
 * @param item -別表明細情報
 */
const showTensuCellValue = (item: RiyouBeppyo) => {
  return (
    item.svType !== Or36647Const.SERVICE_TYPECODE_17 &&
    item.svType !== Or36647Const.SERVICE_TYPECODE_67 &&
    !item.svType.startsWith(Or36647Const.SERVICE_TYPECODE_PREFIX_3320) &&
    !item.svType.startsWith(Or36647Const.SERVICE_TYPECODE_PREFIX_3519)
  )
}

/**
 * 利用票別表 種類支給限度_超過 活性/非活性
 *
 * @param item -別表明細情報
 */
const sTensuOverDisabled = (item: RiyouBeppyo) => {
  let disabled = false
  // 利用者情報
  const riyourshaData = local.usingSlipInfo.riyourshaData[0]
  // 利用者情報.計算フラグ = nullの場合
  if (isEmpty(riyourshaData.rCompBase)) {
    // 利用者情報.サービス単位数（実績） > 利用者情報.区分支給限度基準額の場合
    if (parseInt(riyourshaData.cmnTucPlanSvTensuJ) > parseInt(riyourshaData.dmyShikyuGaku)) {
      const sumData = local.usingSlipInfo.sumData[0]
      const kbnShikyuuGendoData = local.usingSlipInfo.kbnShikyuuGendoData[0]
      // 合計表示欄情報.種類限度基準超過 > 0の場合
      if (parseInt(sumData.hShuOver) > 0) {
        // 別表明細情報.合計フラグ = 1 AND
        // 別表明細情報.加算フラグ = 0 AND
        // 別表明細情報.30超フラグ = 0 AND
        // 種類支給限度_基準内の合計 >0の場合
        if (
          item.totalF === Or36647Const.TOTAL_FLG_TOTAL &&
          item.kasanF === Or36647Const.KASAN_FLG_USUALLY &&
          item.ov30Fl === Or36647Const.OV30_FLG_USUALLY &&
          parseInt(kbnShikyuuGendoData.cSTensuSum) > 0
        ) {
          // 明細.種類支給限度_超過は入力可能にする
          disabled = false
        } else {
          // 明細.種類支給限度_超過は入力不可にする
          disabled = true
        }
      } else {
        // 合計表示欄情報.種類限度基準超過 <= 0の場合
        // 別表明細情報.合計フラグ = 1 AND
        // 別表明細情報.加算フラグ = 0 AND
        // サービス単位/金額 = 種類支給限度_超過 + 種類支給限度_基準内
        // 種類支給限度_基準内の合計 > 0 AND
        // 種類支給限度_超過の合計 <> 0の場合
        if (
          item.totalF === Or36647Const.TOTAL_FLG_TOTAL &&
          item.kasanF === Or36647Const.KASAN_FLG_USUALLY &&
          parseInt(kbnShikyuuGendoData.cSvTensuSum) ===
            parseInt(kbnShikyuuGendoData.cSTensuOverSum) +
              parseInt(kbnShikyuuGendoData.cSTensuSum) &&
          parseInt(kbnShikyuuGendoData.cSTensuSum) > 0 &&
          parseInt(kbnShikyuuGendoData.cSTensuOverSum) !== 0
        ) {
          // 明細.種類支給限度_超過は入力可能にする
          disabled = true
        } else {
          // 明細.種類支給限度_超過は入力可能にする
          disabled = false
        }
      }
    }
  } else {
    // 利用者情報.計算フラグ <> nullの場合
    // 利用者情報.サービス単位数（予定） > 利用者情報.区分支給限度基準額の場合
    if (parseInt(riyourshaData.cmnTucPlanSvTensuY) > parseInt(riyourshaData.dmyShikyuGaku)) {
      // 明細.種類支給限度_超過が入力不可にする
      disabled = true
    }
  }
  return disabled
}

/**
 * 利用票別表 区分支給限度_超過 活性/非活性
 *
 * @param item -別表明細情報
 */
const kTensuOverDisabled = (item: RiyouBeppyo) => {
  let disabled = false
  // 利用者情報
  const riyourshaData = local.usingSlipInfo.riyourshaData[0]
  // 利用者情報.計算フラグ = nullの場合
  if (isEmpty(riyourshaData.rCompBase)) {
    // 利用者情報.サービス単位数（実績） > 利用者情報.区分支給限度基準額の場合
    if (parseInt(riyourshaData.cmnTucPlanSvTensuJ) > parseInt(riyourshaData.dmyShikyuGaku)) {
      const sumData = local.usingSlipInfo.sumData[0]
      const kbnShikyuuGendoData = local.usingSlipInfo.kbnShikyuuGendoData[0]
      // 合計表示欄情報.超過_区分支給限度 > 0の場合
      if (parseInt(sumData.kbnOverSum) > 0) {
        // 別表明細情報.合計フラグ = 1 AND
        // 別表明細情報.加算フラグ = 0 AND
        // 別表明細情報.30超フラグ = 0の場合
        if (
          item.totalF === Or36647Const.TOTAL_FLG_TOTAL &&
          item.kasanF === Or36647Const.KASAN_FLG_USUALLY &&
          item.ov30Fl === Or36647Const.OV30_FLG_USUALLY
        ) {
          // 明細.種類支給限度_超過は入力可能にする
          disabled = false
        } else {
          // 明細.種類支給限度_超過は入力不可にする
          disabled = true
        }
      } else {
        // 合計表示欄情報.超過_区分支給限度 <= 0の場合
        // 別表明細情報.合計フラグ = 1 AND
        // 別表明細情報.加算フラグ = 0 AND
        // 区分支給限度_超過の合計 <> 0の場合
        if (
          item.totalF === Or36647Const.TOTAL_FLG_TOTAL &&
          item.kasanF === Or36647Const.KASAN_FLG_USUALLY &&
          parseInt(kbnShikyuuGendoData.cKTensuOverSum) !== 0
        ) {
          // 明細.種類支給限度_超過は入力可能にする
          disabled = true
        } else {
          // 明細.種類支給限度_超過は入力可能にする
          disabled = false
        }
      }
    }
  } else {
    // 利用者情報.計算フラグ <> nullの場合
    // 利用者情報.サービス単位数（予定） > 利用者情報.区分支給限度基準額の場合
    if (parseInt(riyourshaData.cmnTucPlanSvTensuY) > parseInt(riyourshaData.dmyShikyuGaku)) {
      // 明細.種類支給限度_超過が入力不可にする
      disabled = true
    }
  }
  return disabled
}

/**
 * 単位数単価 表示/非表示
 *
 * @param item -別表明細情報
 */
const showTanka = (item: RiyouBeppyo) => {
  let showFlg = true
  //   別表明細情報.加算フラグ = 1（合計行）の場合
  if (item.totalF === Or36647Const.TOTAL_FLG_TOTAL) {
    // 別表明細情報.給付率差異フラグ=1 or 別表明細情報.給付率差異フラグ=3
    if (
      item.kyufuDiffF === Or36647Const.KYUFU_DIFF_FLG_1 ||
      item.kyufuDiffF === Or36647Const.KYUFU_DIFF_FLG_3
    ) {
      // 明細.単位数単価が表示にする
      showFlg = true
    } else {
      // 上記以外の場合
      // 明細.単位数単価が非表示にする
      showFlg = false
    }
  } else {
    // 別表明細情報.加算フラグ <> 1（合計行）の場合
    // 明細.単位数単価が非表示にする
    showFlg = false
  }
  return showFlg
}

/**
 * 給付率(％) 表示/非表示
 *
 * @param item -別表明細情報
 */
const showKyufuRitu = (item: RiyouBeppyo) => {
  let showFlg = true
  // 別表明細情報.総合サービス区分 <> 2の場合
  if (item.cSougouKbn !== Or36647Const.SOUGOU_KBN_2) {
    // 別表明細情報.合計フラグ = 1の場合
    if (item.totalF === Or36647Const.TOTAL_FLG_TOTAL) {
      // 別表明細情報.給付率差異フラグ = 1の場合
      if (item.kyufuDiffF === Or36647Const.KYUFU_DIFF_FLG_1) {
        // 明細.給付率(％)が非表示にする
        showFlg = false
      } else {
        // 別表明細情報.給付率差異フラグ <> 1の場合
        // 明細.給付率(％)が表示にする
        showFlg = true
      }
    } else {
      // 別表明細情報.合計フラグ <> 1の場合
      // 別表明細情報.給付率差異フラグ = 0 or 別表明細情報.給付率差異フラグ = 2の場合
      if (
        item.kyufuDiffF === Or36647Const.KYUFU_DIFF_FLG_0 ||
        item.kyufuDiffF === Or36647Const.KYUFU_DIFF_FLG_2
      ) {
        // 明細.給付率(％)が非表示にする
        showFlg = false
      } else {
        // 別表明細情報.給付率差異フラグ <> 0 and 別表明細情報.給付率差異フラグ <> 2の場合
        // 明細.給付率(％)が表示にする
        showFlg = true
      }
    }
  } else {
    // 別表明細情報.総合サービス区分 = 2の場合
    // 明細.給付率(％)が非表示にする
    showFlg = false
  }
  return showFlg
}

/**
 * 保険/事業費請求額 表示/非表示
 *
 * @param item -別表明細情報
 */
const showHKyufugaku = (item: RiyouBeppyo) => {
  let showFlg = true
  // 別表明細情報.合計フラグ = 1の場合
  if (item.totalF === Or36647Const.TOTAL_FLG_TOTAL) {
    // 明細.保険/事業費請求額が表示にする
    showFlg = true
  } else {
    // 利用票別表の初期情報.別表明細情報.合計フラグ <> 1の場合
    // 別表明細情報.給付率差異フラグ = 1の場合
    if (item.kyufuDiffF === Or36647Const.KYUFU_DIFF_FLG_1) {
      //   明細.保険/事業費請求額が表示にする
      showFlg = true
    } else {
      // 別表明細情報.給付率差異フラグ <> 1の場合
      // 明細.保険/事業費請求額が非表示にする
      showFlg = false
    }
  }
  return showFlg
}

/**
 * 利用者負担_保険/事業 表示/非表示
 *
 * @param item -別表明細情報
 */
const showHutanH = (item: RiyouBeppyo) => {
  let showFlg = true
  // 別表明細情報.合計フラグ = 1の場合
  if (item.totalF === Or36647Const.TOTAL_FLG_TOTAL) {
    showFlg = true
    // 明細.利用者負担_保険/事業が表示にする
  } else {
    // 別表明細情報.合計フラグ <> 1の場合
    // 別表明細情報.総合サービス区分 = 2の場合
    if (item.cSougouKbn === Or36647Const.SOUGOU_KBN_2) {
      // 別表明細情報.給付率差異フラグ = 2の場合
      if (item.kyufuDiffF === Or36647Const.KYUFU_DIFF_FLG_2) {
        // 明細.利用者負担_保険/事業が非表示にする
        showFlg = false
      } else {
        // 別表明細情報.給付率差異フラグ  <> 2の場合
        // 明細.利用者負担_保険/事業が表示にする
        showFlg = true
      }
    } else {
      // 別表明細情報.総合サービス区分 <> 2の場合
      //  明細.利用者負担_保険/事業が非表示にする
      showFlg = false
    }
  }
  return showFlg
}

/**
 * 利用料集計欄の「適用事業所」変更
 */
const updRiyouryou = () => {
  const selectedItem = riyouryouList.value[local.selectedRiyouryouItemIndex]
  if (selectedItem.updateKbn === '') {
    selectedItem.updateKbn = Or36647Const.UPDATE_CATEGORY_UPDATE
  }
}

/**
 * 利用料集計欄金額再計算
 */
const getRiyouryouKingaku = () => {
  const selectedItem = riyouryouList.value[local.selectedRiyouryouItemIndex]
  // 単価
  const riyouryouTanka = parseInt(selectedItem.riyouryouTanka.value)
  // 数
  const riyouryouSu = parseInt(selectedItem.riyouryouSu.value)
  if (!isNaN(riyouryouTanka) && !isNaN(riyouryouSu)) {
    // 金額
    const riyouryouKingaku = riyouryouTanka * riyouryouSu
    selectedItem.riyouryouKingaku = riyouryouKingaku.toString()
    // 利用料合計
    const riyouryouSum = riyouryouList.value.reduce(
      (sum, item) => sum + parseInt(item.riyouryouKingaku),
      0
    )
    local.usingSlipInfo.riyouryouSum = riyouryouSum.toString()
    updRiyouryou()
  }
}

/**
 * 社福軽減集計欄の「社福事業所」変更
 */
const updSyafuku = async () => {
  const selectedItem = syafukuList.value[local.selectedSyafukuItemIndex]
  if (selectedItem.updateKbn === '') {
    selectedItem.updateKbn = Or36647Const.UPDATE_CATEGORY_UPDATE
  }
  // 社福事業所情報を取得する
  const inputData: UseSlipOtherDtlInfoSocialWelfareOfficeChangeSelectInEntity = {
    svJigyoId: selectedItem.svJigyoId.modelValue,
  }
  const response: UseSlipOtherDtlInfoSocialWelfareOfficeChangeSelectOutEntity =
    await ScreenRepository.select('useSlipOtherDtlInfoSocialWelfareOfficeChangeSelect', inputData)
  // 社福軽減集計欄情報.サービス種類コード = 上記取得したサービス種類コード
  selectedItem.syafukuSv = response.data.svKindCd
}

/**
 * 社福軽減集計欄軽減額再計算
 */
const getSyafukuGaku = () => {
  const selectedItem = syafukuList.value[local.selectedSyafukuItemIndex]
  // 対象額
  const syafukuObj = parseInt(selectedItem.syafukuObj.value)
  // 軽減率
  let syafukuRituText = selectedItem.syafukuRitu.value.replace(Or36647Const.PERCENTAGE, '')
  if (isNaN(parseInt(syafukuRituText))) {
    syafukuRituText = Or36647Const.DEFAULT_AMOUNT
    selectedItem.syafukuRitu.value = Or36647Const.DEFAULT_AMOUNT
  }
  let syafukuRitu = parseFloat(syafukuRituText) / 100
  selectedItem.syafukuRitu.value = (syafukuRitu * 100).toFixed(1) + Or36647Const.PERCENTAGE
  if (isNaN(syafukuObj)) {
    return false
  }
  // 1~100
  if (syafukuRitu > 1) {
    syafukuRitu = 1
  } else if (syafukuRitu < 0) {
    syafukuRitu = 0
  } else {
    syafukuRitu = Math.round(syafukuRitu * 1000) / 1000
  }
  // 軽減額 = 対象額 * (軽減率 * 100) / 100
  const syafukuGaku = (syafukuObj * (syafukuRitu * 100)) / 100
  selectedItem.syafukuGaku = syafukuGaku.toFixed(0)
  // 利用料合計
  const syafukuSum = syafukuList.value.reduce((sum, item) => sum + parseFloat(item.syafukuGaku), 0)
  local.usingSlipInfo.syafukuSum = syafukuSum.toString()
  if (selectedItem.updateKbn === '') {
    selectedItem.updateKbn = Or36647Const.UPDATE_CATEGORY_UPDATE
  }
}

/**
 * 種類支給限度/区分支給限度 超過 変更入力
 *
 * @param index -index
 *
 * @param tensuOverFlg -超過フラグ
 */
const tensuOverChanged = async (index: number, tensuOverFlg: string) => {
  const selectedItem = local.usingSlipOthrtTableList[index]
  // 種類支給限度_超過
  if (tensuOverFlg === Or36647Const.TENSU_OVER_FLG_S) {
    const sTensuOver = parseInt(selectedItem.sTensuOver.value)
    if (sTensuOver < 0) {
      await openConfirmDialog(t('message.i-cmn-10509'))
      selectedItem.sTensuOver.value = local.usingSlipInfo.riyouBeppyoList[index].sTensuOver
      // 処理終了
      return false
    }
  } else {
    const kTensuOver = parseInt(selectedItem.kTensuOver.value)
    if (kTensuOver < 0) {
      await openConfirmDialog(t('message.i-cmn-10509'))
      selectedItem.kTensuOver.value = local.usingSlipInfo.riyouBeppyoList[index].kTensuOver
      // 処理終了
      return false
    }
  }
  // 利用票・別表の別表明細入力後の再計算処理を行う
  const inputData: UseSlipOtherDtlInfoRecalculationSelectInEntity = {
    lineNo: index.toString(),
    proItem: tensuOverFlg,
    riyouBeppyoList: getRiyouBeppyoList(),
    kbnShikyuuGendoData: local.usingSlipInfo.kbnShikyuuGendoData,
  }
  const response: UseSlipOtherDtlInfoRecalculationSelectOutEntity = await ScreenRepository.select(
    'useSlipOtherDtlInfoRecalculationSelect',
    inputData
  )
  // 返却情報.再計算フラグ = 1の場合
  if (response.data.riyou1111Info[0].calcFlag === Or36647Const.CALC_FLAG_1) {
    // 再計算処理、画面を再表示する。
    await recompute()
  }
  // 新規計算フラグを「false」に設定する
  newComputeFlg.value = false
}

/**
 * 社福軽減集計欄「設定ボタン」押下
 */
const syafukuSetting = () => {
  localOneway.or26676Oneway = {
    userId: localOneway.or36647.userId,
    shienId: localOneway.or36647.shienId,
    teiYm: local.offeryYearMonth.value,
  } as Or26676OnewayType
  // GUI01159_社福軽減登録画面をポップアップで起動する
  Or26676Logic.state.set({
    uniqueCpId: or26676.value.uniqueCpId,
    state: { isOpen: true, decisionMakingClickValue: Or36647Const.DECISION_MAKING_CLICK_VALUE_0 },
  })
}

/**
 * 累積_短期入所利用日数の色
 *
 * @param item -合計表示欄情報
 */
const getComputeRuikeiSumColor = (item: SumData) => {
  let color = Or36647Const.COLOR_DEFAULT
  const jissekiDays = parseInt(local.usingSlipInfo.riyourshaData[0].jissekiDays)
  const computeRuikeiSum = parseInt(item.computeRuikeiSum.replace(',', ''))
  // 合計表示欄情報.累積_短期入所利用日数 > 実績日数 AND 実績日数 > 0の場合
  if (computeRuikeiSum > jissekiDays && jissekiDays > 0) {
    // 画面項目.合計表示欄の累積の色は「FF0000」を設定
    color = Or36647Const.COLOR_RED
  }
  return color
}

/**
 * 「短期入所利用日数ボタン」押下
 */
const shortTermadmissionUsingDay = () => {
  localOneway.or27856Oneway = {
    shienId: local.usingSlipInfo.riyourshaData[0].shienId,
    userId: local.usingSlipInfo.riyourshaData[0].userid,
    upToPrevMonth: local.usingSlipInfo.sumData[0].zenGetuSum,
    planForThisMonth: local.usingSlipInfo.sumData[0].cmnTucPlanTTougetu,
    accumulation: local.usingSlipInfo.sumData[0].computeRuikeiSum,
    // 月またぎ前月分日数
    teiYm: local.offeryYearMonth.value,
    teiYmD: local.usingSlipInfo.teikyouYmD,
    hokenNo: local.usingSlipInfo.riyourshaData[0].cmnTucPlanHHokenNoHidden,
    hokenCd: local.usingSlipInfo.riyourshaData[0].cmnTucPlanKHokenCdHidden,
    dmyKikanFr: local.usingSlipInfo.riyourshaData[0].dmyKikanFr,
    screenKbn: Or36647Const.SCREEN_KBN,
  } as Or27856SelectInfoType
  // GUI01162_認定期間中の短期入所利用日数画面をポップアップで起動する
  Or27856Logic.state.set({
    uniqueCpId: or27856.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * 「備考ボタン」押下
 */
const openBiko = async () => {
  // 画面入力データの変更がある場合
  if (isEdit.value) {
    const ret = await openConfirmDialog(t('message.i-cmn-10430'), true)
    if (ret === Or36647Const.CONFIRM_BTN_YES) {
      // 保存処理
      await usingSlipSave()
    }
  }
  localOneway.or29139Oneway = {
    shienId: local.usingSlipInfo.riyourshaData[0].shienId,
    userid: local.usingSlipInfo.riyourshaData[0].userid,
    yymmYm: local.offeryYearMonth.value,
    yymmD: local.usingSlipInfo.teikyouYmD,
  } as Or29139OnewayType
  // GUI01163_備考欄画面をポップアップで起動する
  Or29139Logic.state.set({
    uniqueCpId: or29139.value.uniqueCpId,
    state: { isOpen: true, decisionMakingClickValue: Or36647Const.DECISION_MAKING_CLICK_VALUE_0 },
  })
}

/**
 * 画面利用票明細情報取得
 */
const getRiyouList = () => {
  const riyouList = [] as Riyou[]
  for (const usingSlip of local.usingSlipList) {
    const riyou = {
      ...usingSlip,
      yoteiZumiFlg: usingSlip.yoteiZumiFlg ? Or36647Const.SCHED_FLG_OK : Or36647Const.SCHED_FLG_NG,
      jissekiZumiFlg: usingSlip.jissekiZumiFlg.modelValue
        ? Or36647Const.ACHV_FLG_OK
        : Or36647Const.ACHV_FLG_NG,
      svStartTime: usingSlip.svStartTime.value,
      svEndTime: usingSlip.svEndTime.value,
      yRentalF: usingSlip.yRentalF.modelValue,
      jRentalF: usingSlip.jRentalF.modelValue,
      yDay01: usingSlip.yDay01.value,
      yDay02: usingSlip.yDay02.value,
      yDay03: usingSlip.yDay03.value,
      yDay04: usingSlip.yDay04.value,
      yDay05: usingSlip.yDay05.value,
      yDay06: usingSlip.yDay06.value,
      yDay07: usingSlip.yDay07.value,
      yDay08: usingSlip.yDay08.value,
      yDay09: usingSlip.yDay09.value,
      yDay10: usingSlip.yDay10.value,
      yDay11: usingSlip.yDay11.value,
      yDay12: usingSlip.yDay12.value,
      yDay13: usingSlip.yDay13.value,
      yDay14: usingSlip.yDay14.value,
      yDay15: usingSlip.yDay15.value,
      yDay16: usingSlip.yDay16.value,
      yDay17: usingSlip.yDay17.value,
      yDay18: usingSlip.yDay18.value,
      yDay19: usingSlip.yDay19.value,
      yDay20: usingSlip.yDay20.value,
      yDay21: usingSlip.yDay21.value,
      yDay22: usingSlip.yDay22.value,
      yDay23: usingSlip.yDay23.value,
      yDay24: usingSlip.yDay24.value,
      yDay25: usingSlip.yDay25.value,
      yDay26: usingSlip.yDay26.value,
      yDay27: usingSlip.yDay27.value,
      yDay28: usingSlip.yDay28.value,
      yDay29: usingSlip.yDay29.value,
      yDay30: usingSlip.yDay30.value,
      yDay31: usingSlip.yDay31.value,
      jDay01: usingSlip.jDay01.value,
      jDay02: usingSlip.jDay02.value,
      jDay03: usingSlip.jDay03.value,
      jDay04: usingSlip.jDay04.value,
      jDay05: usingSlip.jDay05.value,
      jDay06: usingSlip.jDay06.value,
      jDay07: usingSlip.jDay07.value,
      jDay08: usingSlip.jDay08.value,
      jDay09: usingSlip.jDay09.value,
      jDay10: usingSlip.jDay10.value,
      jDay11: usingSlip.jDay11.value,
      jDay12: usingSlip.jDay12.value,
      jDay13: usingSlip.jDay13.value,
      jDay14: usingSlip.jDay14.value,
      jDay15: usingSlip.jDay15.value,
      jDay16: usingSlip.jDay16.value,
      jDay17: usingSlip.jDay17.value,
      jDay18: usingSlip.jDay18.value,
      jDay19: usingSlip.jDay19.value,
      jDay20: usingSlip.jDay20.value,
      jDay21: usingSlip.jDay21.value,
      jDay22: usingSlip.jDay22.value,
      jDay23: usingSlip.jDay23.value,
      jDay24: usingSlip.jDay24.value,
      jDay25: usingSlip.jDay25.value,
      jDay26: usingSlip.jDay26.value,
      jDay27: usingSlip.jDay27.value,
      jDay28: usingSlip.jDay28.value,
      jDay29: usingSlip.jDay29.value,
      jDay30: usingSlip.jDay30.value,
      jDay31: usingSlip.jDay31.value,
    }
    riyouList.push(riyou)
  }
  return riyouList
}

/**
 * 画面別表明細情報取得
 */
const getRiyouBeppyoList = () => {
  const riyouBeppyoList = [] as RiyouBeppyo[]
  for (const usingSlipOthrtTable of local.usingSlipOthrtTableList) {
    const riyouBeppyo = {
      ...usingSlipOthrtTable,
      sTensuOver: usingSlipOthrtTable.sTensuOver.value,
      kTensuOver: usingSlipOthrtTable.kTensuOver.value,
    }
    riyouBeppyoList.push(riyouBeppyo)
  }
  return riyouBeppyoList
}

/**
 * 画面利用料集計欄情報取得
 */
const getRiyouryouList = () => {
  const list = [] as Riyouryou[]
  for (const riyouryou of riyouryouList.value) {
    list.push({
      ...riyouryou,
      svJigyoId: riyouryou.svJigyoId.modelValue,
      riyouryouItem: riyouryou.riyouryouItem.value,
      riyouryouTanka: riyouryou.riyouryouTanka.value,
      riyouryouSu: riyouryou.riyouryouSu.value,
    } as Riyouryou)
  }
  return list
}

/**
 * 画面社福軽減集計欄情報取得
 */
const getSyafukuList = () => {
  const list = [] as Syafuku[]
  for (const syafuku of syafukuList.value) {
    list.push({
      ...syafuku,
      svJigyoId: syafuku.svJigyoId.modelValue,
      syafukuObj: syafuku.syafukuObj.value,
      syafukuRitu: syafuku.syafukuRitu.value,
    } as Syafuku)
  }
  return list
}

/**
 * 金額ディスプレイ
 *
 * @param amount -金額
 */
const getDisplayAmount = (amount: string) => {
  let displayAmount = ''
  if (!isEmpty(amount)) {
    const amountNum = parseInt(amount)
    if (!isNaN(amountNum)) {
      displayAmount = amountNum.toLocaleString()
    }
  }
  return displayAmount
}

/**
 * 予定/実績回数name取得
 *
 * @param prefix -予定/実績
 *
 * @param day -day
 */
const getSchedAchvDayName = (prefix: string, day: number) => {
  return prefix + day.toString().padStart(2, Or36647Const.PREFIX_ZERO)
}

/**
 * 利用票別表 border 表示/非表示
 *
 * @param index -index
 */
const showUsingSlipOtherTableBottomBorder = (index: number) => {
  return index === local.usingSlipOthrtTableList.length - 1 ? 'border-bottom: none !important' : ''
}

/**
 * 公費集計欄一覧 border 表示/非表示
 *
 * @param index -index
 */
const showKohiTableBottomBorder = (index: number) => {
  return index === local.kohiList.length - 1 ? 'border-bottom: none !important' : ''
}

/**
 * 利用料集計欄一覧 border 表示/非表示
 *
 * @param index -index
 */
const showRiyouryouTableBottomBorder = (index: number) => {
  return index === riyouryouList.value.length - 1 ? 'border-bottom: none !important' : ''
}

/**
 * 社福軽減集計欄一覧 border 表示/非表示
 *
 * @param index -index
 */
const showSyafukuTableBottomBorder = (index: number) => {
  return index === syafukuList.value.length - 1 ? 'border-bottom: none !important' : ''
}

/**
 * 確認ダイアログ表示
 *
 * @param dialogText -ダイアログテキスト
 *
 * @param isChoose - ダイアログ種類
 */
const openConfirmDialog = (dialogText: string, isChoose = false) => {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: or21814.value.uniqueCpId,
    state: {
      dialogTitle: t('label.confirm'),
      dialogText: dialogText,
      firstBtnType: 'normal1',
      firstBtnLabel: isChoose ? t('btn.yes') : t('btn.ok'),
      secondBtnType: isChoose ? 'normal3' : 'blank',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)
        let result = 'no' as 'yes' | 'cancel'
        if (event?.firstBtnClickFlg) {
          result = Or36647Const.CONFIRM_BTN_YES
        }
        if (event?.thirdBtnClickFlg) {
          result = Or36647Const.CONFIRM_BTN_CANCEL
        }
        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 警告ダイアログをオープンする
 *
 * @param dialogText - ダイアログテキスト
 */
const openWarningDialog = (dialogText: string) => {
  Or21815Logic.state.set({
    uniqueCpId: or21815.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.warning'),
      // ダイアログテキスト
      dialogText: dialogText,
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(or21815.value.uniqueCpId)?.isOpen,
      () => {
        resolve(Or36647Const.CONFIRM_BTN_YES)
      }
    )
  })
}

/**
 * いずれかの編集フラグがONになっているかチェックする
 *
 * @param components - 変更のリスニングが必要なリスト
 *
 * @returns いずれかの編集有無
 */
const isEditNavControl = (components: string[]) => {
  const screenStore = useScreenStore()
  const screenNavControl = screenStore.getScreenNavControl<NavControlJsonType>()
  // ナビゲーション制御領域を走査
  for (const key in screenNavControl) {
    if (screenNavControl[key] === screenNavControl.components) {
      // ナビゲーション制御領域 - コンポーネント毎の領域を走査
      for (const cpKey in screenNavControl[key]) {
        if (components.includes(cpKey)) {
          if (screenNavControl[key][cpKey].editFlg) {
            // コンポーネント毎の編集フラグがON
            return true
          }
          if (screenNavControl[key][cpKey].items) {
            // ナビゲーション制御領域 - コンポーネント毎の領域にitemsが存在する場合は走査
            for (const itemKey in screenNavControl[key][cpKey].items) {
              if (screenNavControl[key][cpKey].items[itemKey].editFlg) {
                // コンポーネント - itemsの編集フラグがON
                return true
              }
            }
          }
        }
      }
    }
  }
  return false
}
</script>

<template>
  <c-v-sheet class="view pl-2 pr-2">
    <c-v-row
      no-gutters
      class="pa-2"
    >
      <!-- 利用票 画面タイトル -->
      <c-v-col class="title-area">
        <c-v-row
          align-center
          justify-start
          no-gutters
        >
          <h1 class="pl-3">
            {{ t('label.using-offer-slip') }}
          </h1>
          <!-- Or21828：有機体：お気に入りアイコンボタン -->
          <g-base-or21828
            v-bind="or21828"
            class="px-2"
          />
        </c-v-row>
      </c-v-col>
      <c-v-col cols="auto">
        <!-- Or11871：有機体：画面メニューエリア -->
        <g-base-or11871
          v-bind="or11871"
          class="top-menu"
        >
          <template #customButtons>
            <!-- 複写 -->
            <base-mo00611
              :oneway-model-value="localOneway.mo00611CopyOneway"
              style="margin-right: 8px"
              @click="usingSlipCopy"
            />
            <!-- 再計算 -->
            <base-mo00611
              :oneway-model-value="localOneway.mo00611RecomputeOneway"
              @click="recompute"
            />
          </template>
          <!-- マスタ設定 -->
          <template #optionMasterItems>
            <!-- マスタ -->
            <c-v-list-item
              :title="t('label.master')"
              @click="master"
            >
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="t('tooltip.others-function')"
              />
            </c-v-list-item>
            <!-- メンテナンス -->
            <c-v-list-item
              :title="t('label.maintenance')"
              @click="maintenance"
            >
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="t('tooltip.maintenance')"
              />
            </c-v-list-item>
            <!-- 特別指示期間 -->
            <c-v-list-item
              :title="t('label.special-instructions-period')"
              @click="specialInstructionsPeriod"
            >
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="t('tooltip.special-instructions-period')"
              />
            </c-v-list-item>
            <!-- 短期退所登録 -->
            <c-v-list-item
              :title="t('label.short-term-leaving-regist')"
              @click="shortTermLeavingRegist"
            >
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="t('tooltip.short-term-leaving-regist')"
              />
            </c-v-list-item>
            <!-- 福祉用具単位一括設定 -->
            <c-v-list-item
              :title="t('label.welfare-equipment-unit-bundle-settings')"
              @click="welfareEquipmentUnitBundleSettings"
            >
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="t('tooltip.welfare-equipment-unit-bundle-settings')"
              />
            </c-v-list-item>
            <!-- 横出しサービス単位再設定 -->
            <c-v-list-item
              :title="t('label.horizontal-service-unit-reconfigure')"
              @click="horizontalServiceUnitReconfigure"
            >
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="t('tooltip.horizontal-service-unit-reconfigure')"
              />
            </c-v-list-item>
            <!-- 総合事業単位再設定 -->
            <c-v-list-item
              :title="t('label.comprehensive-project-unit-reconfigure')"
              @click="comprehensiveProjectUnitReconfigure"
            >
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="t('tooltip.comprehensive-project-unit-reconfigure')"
              />
            </c-v-list-item>
            <!-- 有効期間外サービス -->
            <c-v-list-item
              :title="t('label.valid-period-other-than-service')"
              @click="validPeriodOtherThanService"
            >
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="t('tooltip.valid-period-other-than-service')"
              />
            </c-v-list-item>
            <!-- 日割利用期間 -->
            <c-v-list-item
              :title="t('label.daily-rate-using-period')"
              @click="dailyRateUsingPeriod"
            >
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="t('tooltip.daily-rate-using-period')"
              />
            </c-v-list-item>
          </template>
          <template #optionMenuItems>
            <!-- 週間取込 -->
            <c-v-list-item
              v-if="hasView"
              :disabled="!hasRegist"
              :title="t('label.week-import')"
              @click="weekImport"
            >
              <template #prepend>
                <base-at-icon
                  icon="folder fill"
                  style="opacity: 0"
                />
              </template>
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="$t('tooltip.week-import')"
              />
            </c-v-list-item>
            <!-- 雛形取込 -->
            <c-v-list-item
              :disabled="!hasRegist"
              :title="t('label.prototype-import')"
              @click="prototypeImport"
            >
              <template #prepend>
                <base-at-icon
                  icon="folder fill"
                  style="opacity: 0"
                />
              </template>
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="$t('tooltip.prototype-import')"
              />
            </c-v-list-item>
            <!-- 計画実績 -->
            <c-v-list-item
              :title="t('label.plan-result')"
              @click="planResult"
            >
              <template #prepend>
                <base-at-icon
                  icon="folder fill"
                  style="opacity: 0"
                />
              </template>
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="$t('tooltip.plan-result')"
              />
            </c-v-list-item>
            <!-- 希望負担 -->
            <c-v-list-item
              :disabled="!hasRegist"
              :title="t('label.hope-burden')"
              @click="hopeBurden"
            >
              <template #prepend>
                <base-at-icon
                  icon="folder fill"
                  style="opacity: 0"
                />
              </template>
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="$t('tooltip.hope-burden')"
              />
            </c-v-list-item>
            <!-- 日割算定 -->
            <c-v-list-item
              :title="t('label.daily-calculation')"
              @click="dailyCalculation"
            >
              <template #prepend>
                <base-at-icon
                  icon="folder fill"
                  style="opacity: 0"
                />
              </template>
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="$t('tooltip.daily-calculation')"
              />
            </c-v-list-item>
            <c-v-divider />
            <!-- 削除 -->
            <c-v-list-item
              :disabled="!hasRegist"
              :title="t('btn.delete')"
              prepend-icon="delete"
              @click="usingSlipDelete"
            >
              <c-v-tooltip
                activator="parent"
                location="bottom"
                :text="$t('btn.delete')"
              />
            </c-v-list-item>
          </template>
        </g-base-or11871>
      </c-v-col>
    </c-v-row>
    <c-v-row
      no-gutters
      class="content-area"
    >
      <c-v-col
        cols="2"
        class="hidden-scroll h-100"
      >
        <!-- Or00248：有機体：（利用者基本）利用者一覧詳細表示 -->
        <g-base-or00248 v-bind="or00248" />
      </c-v-col>
      <c-v-col class="hidden-scroll h-100 px-2">
        <c-v-sheet class="content">
          <c-v-container>
            <c-v-row>
              <c-v-col cols="auto">
                <!-- 事業所 -->
                <g-base-or-41179 v-bind="or41179" />
              </c-v-col>
            </c-v-row>
            <c-v-row class="second-row">
              <c-v-col cols="auto">
                <!-- 提供年月 -->
                <c-v-row no-gutters>
                  <base-mo01338
                    :oneway-model-value="localOneway.mo01338Oneway"
                    class="lbl1"
                  />
                  <base-mo00037
                    :oneway-model-value="localOneway.mo00037Oneway"
                    class="lbl2"
                  />
                  <c-v-row
                    no-gutters
                    class="align-center"
                  >
                    <base-mo00009
                      :oneway-model-value="localOneway.backmo00009Oneway"
                      @click="addMonth(false)"
                    />
                    <!-- 処理期間開始日 -->
                    <base-mo00020
                      v-model="local.offeryYearMonth"
                      :oneway-model-value="localOneway.mo00020Oneway"
                    />
                    <base-mo00009
                      :oneway-model-value="localOneway.forwardmo00009Oneway"
                      @click="addMonth(true)"
                    />
                  </c-v-row>
                </c-v-row>
              </c-v-col>
              <c-v-col
                cols="auto"
                style="padding: 8px 8px 0px 8px"
              >
                <!-- 作成年月日 -->
                <base-mo00020
                  :model-value="local.createDate"
                  :oneway-model-value="localOneway.mo00020YmdOneway"
                  style="background-color: #ffffff"
                />
              </c-v-col>
              <c-v-col cols="auto">
                <!-- 入力作業 -->
                <c-v-row
                  no-gutters
                  class="text-center"
                  style="padding: 0"
                >
                  <base-mo00018
                    v-model="local.inputWorkSchedule"
                    :oneway-model-value="localOneway.mo00018ScheduleOneway"
                  />
                  <base-mo00018
                    v-model="local.inputWorkAchievements"
                    :oneway-model-value="localOneway.mo00018AchievementsOneway"
                  />
                </c-v-row>
              </c-v-col>
              <c-v-col cols="auto">
                <!-- 計算 -->
                <c-v-row
                  no-gutters
                  class="text-center"
                >
                  <base-mo00039
                    v-model="local.compute"
                    :oneway-model-value="localOneway.mo00039Oneway"
                    class="d-flex"
                  >
                    <base-at-radio
                      v-for="item in local.computeItemList"
                      :key="item.value"
                      :name="item.label"
                      :radio-label="item.label"
                      :value="item.value"
                    />
                  </base-mo00039>
                </c-v-row>
              </c-v-col>
            </c-v-row>
            <c-v-divider class="divider-class mt-2" />
            <c-v-row
              no-gutters
              class="second-row mt-2"
            >
              <!-- 保険者 -->
              <c-v-col
                cols="2"
                class="d-flex"
              >
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615InsurOneway"
                  style="width: 55px"
                />
                <!-- 保険者選択アイコンボタン -->
                <base-mo00009
                  :oneway-model-value="localOneway.mo00009Oneway"
                  class="btn-insur-select"
                  @click="openInsurDialog"
                />
                <base-mo01338
                  :oneway-model-value="localOneway.mo01338InsurOneway"
                  :style="[
                    'width: 90px',
                    'background-color: ' + local.insuredIdBgColor + '!important',
                  ]"
                />
              </c-v-col>
              <!-- 介護度 -->
              <c-v-col
                cols="2"
                class="d-flex"
              >
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615LevelOfCareOneway"
                  style="width: 55px; height: 30px"
                />
                <base-mo01338
                  :oneway-model-value="localOneway.mo01338LevelOfCareOneway"
                  :style="[
                    'width: 90px',
                    'background-color: ' + local.levelOfCareBgColor + '!important',
                  ]"
                />
              </c-v-col>
              <!-- 変更日 -->
              <c-v-col cols="2">
                <base-mo01338 :oneway-model-value="localOneway.mo01338UpdateDateOneway" />
              </c-v-col>
              <!-- 支援事業所 -->
              <c-v-col cols="5">
                <base-mo01338 :oneway-model-value="localOneway.mo01338SupportOfficeOneway" />
              </c-v-col>
              <!-- 要確認 -->
              <c-v-col cols="1">
                <base-mo00611
                  v-if="localOneway.or10923Oneway.warningMessageInfo.length > 0"
                  :oneway-model-value="localOneway.mo00611ConfirmOneway"
                  style="position: relative; top: 18px"
                  @click="openDialogOr10923"
                />
                <!-- 利用票・別表画面で警告情報ダイアログ -->
                <g-custom-or10923
                  v-if="showDialogOr10923"
                  v-bind="or10923"
                  :oneway-model-value="localOneway.or10923Oneway"
                />
              </c-v-col>
            </c-v-row>
            <c-v-row
              no-gutters
              class="mt-2"
            >
              <!-- 被保険者 -->
              <c-v-col
                cols="2"
                class="d-flex"
              >
                <base-mo00615
                  :oneway-model-value="localOneway.mo00615InsuredOneway"
                  style="width: 55px"
                />
                <!-- 被保険者選択アイコンボタン -->
                <base-mo00009
                  :oneway-model-value="localOneway.mo00009Oneway"
                  style="visibility: hidden"
                />
                <base-mo01338
                  :oneway-model-value="localOneway.mo01338InsuredOneway"
                  class="text-left"
                  :style="[
                    'width: 90px',
                    'background-color: ' + local.insuredIdBgColor + '!important',
                  ]"
                />
              </c-v-col>
              <!-- 変更後 -->
              <c-v-col cols="2">
                <base-mo01338 :oneway-model-value="localOneway.mo01338UpdateAfterOneway" />
              </c-v-col>
              <!-- 支給限度 -->
              <c-v-col cols="2">
                <base-mo01338 :oneway-model-value="localOneway.mo01338PaymentLimitOneway" />
              </c-v-col>
              <!-- 担当者 -->
              <c-v-col cols="2">
                <base-mo01338 :oneway-model-value="localOneway.mo01338ManagerOneway" />
              </c-v-col>
              <!-- 希望額 -->
              <c-v-col cols="2">
                <base-mo01338 :oneway-model-value="localOneway.mo01338HopeAmountOneway" />
              </c-v-col>
            </c-v-row>
            <c-v-divider class="divider-class mt-3" />
            <c-v-row no-gutters>
              <base-mo00043
                v-model="local.mo00043"
                :oneway-model-value="localOneway.mo00043Oneway"
                class="mt-2 w-100"
                style="padding: 0 !important"
              >
              </base-mo00043>
            </c-v-row>
            <c-v-row
              no-gutters
              class="mt-2"
            >
              <c-v-col>
                <c-v-window v-model="local.mo00043.id">
                  <!-- 利用票 -->
                  <c-v-window-item :value="Or36647Const.TAB_ID_USING_SLIP">
                    <c-v-row>
                      <c-v-col>
                        <c-v-row class="ma-0">
                          <div class="flex-col">
                            <div class="line3-flex-box">
                              <!-- 行追加ボタン: Or21735 -->
                              <div>
                                <g-base-or-21735 v-bind="or21735" />
                                <c-v-tooltip
                                  activator="parent"
                                  location="bottom"
                                  :text="$t('tooltip.care-plan2-newline-btn')"
                                ></c-v-tooltip>
                              </div>
                              <!-- 行複写ボタン: Or21737 -->
                              <div>
                                <g-base-or-21737 v-bind="or21737" />
                                <c-v-tooltip
                                  activator="parent"
                                  location="bottom"
                                  :text="$t('tooltip.care-plan2-cpyline-btn')"
                                ></c-v-tooltip>
                              </div>
                              <!-- 行削除ボタン: Or21738 -->
                              <div>
                                <g-base-or-21738 v-bind="or21738" />
                                <c-v-tooltip
                                  activator="parent"
                                  location="bottom"
                                  :text="$t('tooltip.care-plan2-deleteline-btn')"
                                ></c-v-tooltip>
                              </div>
                              <!-- 修正ボタン -->
                              <base-mo00611
                                :oneway-model-value="localOneway.mo00611EditOneway"
                                class="ml-4"
                                @click="editLine"
                              />
                              <!-- 予実変換ボタン -->
                              <base-mo00611
                                :oneway-model-value="localOneway.mo00611PredictionOneway"
                                @click="schedAchvChange"
                              />
                              <!-- 行選択上下アイコンボタン -->
                              <div>
                                <!-- 下アイコン:分子：アイコンボタン -->
                                <base-mo00009
                                  :oneway-model-value="localOneway.mo00009OnewayDown"
                                  @click="nextLine"
                                />
                                <!-- 上アイコン:分子：アイコンボタン -->
                                <base-mo00009
                                  :oneway-model-value="localOneway.mo00009OnewayUp"
                                  @click="preLine"
                                />
                              </div>
                              <!-- AI予測 -->
                              <base-mo00611
                                :oneway-model-value="localOneway.mo00611AiForecastOneway"
                                class="ml-8"
                              />
                              <!-- 日割利用期間 -->
                              <base-mo00611
                                v-if="local.sompoFlg"
                                :oneway-model-value="localOneway.mo00611DailyCalculationOneway"
                                @click="dailyRateUsingPeriod"
                              />
                            </div>
                          </div>
                          <!-- 利用票明細一覧 -->
                          <c-v-data-table
                            class="table-header overflow-y-auto w-100 mt-2 h-100 table-wrapper tbl-using-slip"
                            hide-default-footer
                            fixed-header
                            :items-per-page="-1"
                            :items="local.usingSlipList"
                            return-object
                            hide-no-data
                          >
                            <template #headers>
                              <tr>
                                <!-- 入力済 -->
                                <th
                                  rowspan="2"
                                  class="pl-3 pr-3"
                                  style="min-width: 65px"
                                >
                                  {{ t('label.input') }}<br />{{ t('label.using-slip-completed') }}
                                </th>
                                <!-- サービス事業者 -->
                                <th
                                  rowspan="2"
                                  class="pl-2 pr-2"
                                  style="min-width: 200px"
                                  @click="usingSlipSort(Or36647Const.SORT_COL_DMY_JIGYO_NAME_KNJ)"
                                >
                                  {{ t('label.service-office-table') }}
                                </th>
                                <!-- サービス内容 -->
                                <th
                                  rowspan="2"
                                  class="pl-2 pr-2"
                                  style="min-width: 200px"
                                >
                                  {{ t('label.service-type-contents') }}
                                </th>
                                <!-- 提供時間 -->
                                <th
                                  rowspan="2"
                                  class="pl-2 pr-2"
                                  style="min-width: 80px"
                                  @click="usingSlipSort(Or36647Const.SORT_COL_SV_START_TIME)"
                                >
                                  {{ t('label.offer') }}<br />{{ t('label.hours') }}
                                </th>
                                <!-- 日 -->
                                <th
                                  class="pl-2 pr-2"
                                  style="min-width: 38px"
                                >
                                  {{ t('label.day-short-sunday') }}
                                </th>
                                <!-- 1~31ラベル -->
                                <th
                                  v-for="item in offeryYearMonthDayList"
                                  :key="item.day"
                                  class="pl-2 pr-2"
                                  style="min-width: 38px"
                                >
                                  {{ item.day }}
                                </th>
                                <!-- 合計 -->
                                <th
                                  rowspan="2"
                                  class="pl-2 pr-2"
                                  style="min-width: 50px; writing-mode: tb; text-align: center"
                                >
                                  {{ t('label.total') }}
                                </th>
                                <!-- 予定金額総合計 -->
                                <th
                                  class="pl-2 pr-2 text-right"
                                  style="min-width: 50px; background-color: #ffffff !important"
                                >
                                  {{ getDisplayAmount(local.usingSlipInfo.yoteiAllSum) }}
                                </th>
                              </tr>
                              <tr>
                                <!-- 曜 -->
                                <th
                                  class="pl-2 pr-2"
                                  style="min-width: 38px"
                                >
                                  {{ t('label.day-short-week') }}
                                </th>
                                <!-- 日~火ラベル -->
                                <th
                                  v-for="(item, index) in offeryYearMonthDayList"
                                  :key="index"
                                  class="pl-2 pr-2"
                                  style="min-width: 38px"
                                  :style="[
                                    'min-width: 40px',
                                    'max-width: 40px',
                                    'color:' + item.color + '!important',
                                  ]"
                                >
                                  {{ item.dayOfWeek }}
                                </th>
                                <!-- 実績金額総合計 -->
                                <th
                                  class="pl-2 pr-2 text-right"
                                  style="background-color: #ffffff !important"
                                >
                                  {{ getDisplayAmount(local.usingSlipInfo.jissekiAllSum) }}
                                </th>
                              </tr>
                            </template>
                            <template #item="{ item, index }">
                              <tr
                                :class="{
                                  'select-row': local.selectedUsingSlipItemIndex === index,
                                  hoverd: hoverdIndex === index,
                                }"
                                :style="[
                                  index === local.usingSlipList.length - 1
                                    ? 'border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important'
                                    : '',
                                ]"
                                @click="selectUsingSlipRow(index)"
                                @mouseover="hoverdIndex = index"
                                @mouseout="hoverdIndex = -1"
                              >
                                <td
                                  class="pa-0"
                                  rowspan="2"
                                  :style="[
                                    index === local.usingSlipList.length - 1
                                      ? 'border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important'
                                      : '',
                                  ]"
                                >
                                  <base-mo00018
                                    v-model="item.yoteiZumiFlg"
                                    :oneway-model-value="localOneway.mo00018TblScheduleOneway"
                                    :style="[
                                      'height: 31px',
                                      'background-color: ' +
                                        getZumiFlgBgColor(item.yoteiZumiFlg.modelValue, index),
                                      'color: ' +
                                        (item.yoteiZumiFlg.modelValue ? '#800000' : '#000000'),
                                    ]"
                                    @change="schedChange(index)"
                                  />
                                  <base-mo00018
                                    v-model="item.jissekiZumiFlg"
                                    :oneway-model-value="localOneway.mo00018TblAchievementsOneway"
                                    :style="[
                                      'height: 32px',
                                      'background-color: ' +
                                        getZumiFlgBgColor(item.jissekiZumiFlg.modelValue, index),
                                      'color: ' + (item.jissekiZumiFlg.modelValue ? '#800000' : ''),
                                    ]"
                                    @change="achvChange(index)"
                                  />
                                </td>
                                <td
                                  :class="['pa-2', isParentRow(item) ? 'select-row' : '']"
                                  :style="[
                                    index === local.usingSlipList.length - 1
                                      ? 'border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important'
                                      : '',
                                  ]"
                                  rowspan="2"
                                  @dblclick="editLine"
                                >
                                  {{ item.dmyJigyoNameKnj }}
                                </td>
                                <td
                                  :class="['pa-2', isParentRow(item) ? 'select-row' : '']"
                                  :style="[
                                    index === local.usingSlipList.length - 1
                                      ? 'border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important'
                                      : '',
                                  ]"
                                  rowspan="2"
                                  @dblclick="editLine"
                                >
                                  {{ item.dmyFormalnameKnj }}
                                </td>
                                <td style="padding: 0 !important">
                                  <base-mo01274
                                    v-if="showOfferHoursTime(item.svStartTime.value)"
                                    v-model="item.svStartTime"
                                    :disabled="
                                      item.yoteiZumiFlg.modelValue || item.jissekiZumiFlg.modelValue
                                    "
                                    :oneway-model-value="localOneway.mo01274Oneway"
                                    class="offer-hour-time"
                                    @keydown="handleKeydown"
                                    @change="offerHoursStartTimeChange(index)"
                                  />
                                  <base-mo01282
                                    v-if="showOfferHoursDropdown(item)"
                                    v-model="item.yRentalF"
                                    :oneway-model-value="localOneway.mo01282Oneway"
                                    :disabled="
                                      item.yoteiZumiFlg.modelValue || item.jissekiZumiFlg.modelValue
                                    "
                                    @change="schedCodeChange(index)"
                                  />
                                </td>
                                <td class="pa-0 text-center">
                                  <base-mo00009
                                    :oneway-model-value="localOneway.mo00009CalendarOneway"
                                    style="opacity: var(--v-medium-emphasis-opacity)"
                                    @click="schedCalendarHandler(index)"
                                  />
                                </td>
                                <td
                                  v-for="day in offeryYearMonthDayList"
                                  :key="day.day"
                                  style="padding: 0 !important"
                                >
                                  <base-mo01278
                                    v-model="
                                      item[getSchedAchvDayName(Or36647Const.PREFIX_SCHED, day.day)]
                                    "
                                    :oneway-model-value="localOneway.mo01278Oneway"
                                    :style="[
                                      'width: 40px',
                                      'border-radius: 0',
                                      'background:' +
                                        getDayBgColor(index, day, Or36647Const.SCHED_FLG_OK),
                                    ]"
                                    :disabled="item.yoteiZumiFlg.modelValue"
                                    @focus="selectColumn(day.day)"
                                    @blur="cleanSelectedColumn"
                                    @update:model-value="schedDayChange(index, day.day)"
                                    @dblclick="doubleSchedDayClick(index, day.day, $event)"
                                  />
                                </td>
                                <td class="pl-2 pr-2 text-right">
                                  {{
                                    item.schedChangeFlg
                                      ? item.schedChangeValue
                                      : getSchedTotal(item)
                                  }}
                                </td>
                                <td class="pl-2 pr-2 text-right">
                                  {{ getSchedAmountTotal(item) }}
                                </td>
                              </tr>
                              <tr
                                :class="{
                                  'select-row': local.selectedUsingSlipItemIndex === index,
                                  hoverd: hoverdIndex === index,
                                }"
                                @click="selectUsingSlipRow(index)"
                                @mouseover="hoverdIndex = index"
                                @mouseout="hoverdIndex = -1"
                              >
                                <td style="padding: 0 !important">
                                  <base-mo01274
                                    v-if="showOfferHoursTime(item.svEndTime.value)"
                                    v-model="item.svEndTime"
                                    :oneway-model-value="localOneway.mo01274Oneway"
                                    :disabled="
                                      item.yoteiZumiFlg.modelValue || item.jissekiZumiFlg.modelValue
                                    "
                                    class="offer-hour-time"
                                    @keydown="handleKeydown"
                                    @change="offerHoursEndTimeChange(index)"
                                  />
                                  <base-mo01282
                                    v-if="showOfferHoursDropdown(item)"
                                    v-model="item.jRentalF"
                                    :disabled="item.jissekiZumiFlg.modelValue"
                                    :oneway-model-value="localOneway.mo01282Oneway"
                                    @change="achvCodeChange(index)"
                                  />
                                </td>
                                <td class="pa-0 text-center">
                                  <base-mo00009
                                    :oneway-model-value="localOneway.mo00009CalendarOneway"
                                    style="opacity: var(--v-medium-emphasis-opacity)"
                                    @click="achvCalendarHandler(index)"
                                  />
                                </td>
                                <td
                                  v-for="day in offeryYearMonthDayList"
                                  :key="day.day"
                                  style="padding: 0 !important"
                                >
                                  <base-mo01278
                                    v-model="
                                      item[getSchedAchvDayName(Or36647Const.PREFIX_ACHV, day.day)]
                                    "
                                    :oneway-model-value="localOneway.mo01278Oneway"
                                    :style="[
                                      'width: 40px',
                                      'border-radius: 0',
                                      'background:' +
                                        getDayBgColor(index, day, Or36647Const.ACHV_FLG_OK),
                                    ]"
                                    :disabled="item.jissekiZumiFlg.modelValue"
                                    @focus="selectColumn(day.day)"
                                    @blur="cleanSelectedColumn"
                                    @update:model-value="achvDayChange(index, day.day)"
                                    @dblclick="doubleAchvDayClick(index, day.day, $event)"
                                  />
                                </td>
                                <td class="pl-2 pr-2 text-right">
                                  {{
                                    item.achvChangeFlg ? item.achvChangeValue : getAchvTotal(item)
                                  }}
                                </td>
                                <td class="pl-2 pr-2 text-right">{{ getAchvAmountTotal(item) }}</td>
                              </tr>
                            </template>
                          </c-v-data-table>
                        </c-v-row>
                      </c-v-col>
                    </c-v-row>
                  </c-v-window-item>
                  <!-- 利用票別表 -->
                  <c-v-window-item :value="Or36647Const.TAB_ID_USING_SLIP_OTHER_TABLE">
                    <c-v-row no-gutters>
                      <c-v-col>
                        <!-- 利用票別表一覧 -->
                        <c-v-data-table
                          class="overflow-y-auto w-100 h-100 table-wrapper tbl-using-slip-other-table"
                          fixed-header
                          fixed-footer
                          hide-default-footer
                          :items-per-page="-1"
                          :items="local.usingSlipOthrtTableList"
                          hover
                          hide-no-data
                        >
                          <template #headers>
                            <tr>
                              <!-- 事業所名 -->
                              <th
                                rowspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 150px"
                              >
                                {{ t('label.office-name') }}
                              </th>
                              <!-- 事業所番号 -->
                              <th
                                rowspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 130px"
                              >
                                {{ t('label.office-number') }}
                              </th>
                              <!-- サービス内容/種類 -->
                              <th
                                rowspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 150px"
                              >
                                {{ t('label.service-contents') }}<br />/{{ t('label.type') }}
                              </th>
                              <!-- ｻｰﾋﾞｽｺｰﾄ -->
                              <th
                                rowspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 80px"
                              >
                                {{ t('label.services-half-kana') }}<br />{{
                                  t('label.code-half-kana')
                                }}
                              </th>
                              <!-- 単位数 -->
                              <th
                                rowspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 80px"
                              >
                                {{ t('label.unit-number') }}
                              </th>
                              <!-- 割引後 -->
                              <th
                                colspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 120px"
                              >
                                {{ t('label.discount-after') }}
                              </th>
                              <!-- 回数 -->
                              <th
                                rowspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 35px"
                              >
                                {{ t('label.number-of-times') }}
                              </th>
                              <!-- サービス単位/金額 -->
                              <th
                                rowspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 80px"
                              >
                                {{ t('label.services') }}<br />
                                {{ t('label.unit') }}/{{ t('label.amount-1') }}
                              </th>
                              <!-- 給付管理単位数 -->
                              <th
                                v-if="showBenefitManagement"
                                rowspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 80px"
                              >
                                {{ t('label.benefit-management') }}<br />
                                {{ t('label.unit-number') }}
                              </th>
                              <!-- 種類支給限度 -->
                              <th
                                colspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 140px"
                              >
                                {{ t('label.type-payment-limit') }}
                              </th>
                              <!-- 区分支給限 -->
                              <th
                                colspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 140px"
                              >
                                {{ t('label.category-payment-limit') }}
                              </th>
                              <!-- 単位数単価 -->
                              <th
                                rowspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 80px"
                              >
                                {{ t('label.unit-number') }}<br />{{
                                  t('label.using-slip-unit-price')
                                }}
                              </th>
                              <!-- 費用総額保険/事業対象分 -->
                              <th
                                rowspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 90px"
                              >
                                {{ t('label.total-fee') }}<br />
                                {{ t('label.insurance-project') }}<br />
                                {{ t('label.target-division') }}
                              </th>
                              <!-- 給付率(％) -->
                              <th
                                rowspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 80px"
                              >
                                {{ t('label.benefit') }}<br />
                                {{ t('label.rate') }}<br />
                                ({{ t('label.percent-icon') }})
                              </th>
                              <!-- 保険/事業費請求額 -->
                              <th
                                rowspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 80px"
                              >
                                {{ t('label.usgin-slip-insurance') }}/<br />
                                {{ t('label.project-fee') }}<br />
                                {{ t('label.request-amount') }}
                              </th>
                              <!-- 定額利用負担単価金額 -->
                              <th
                                rowspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 80px"
                              >
                                {{ t('label.fixed-amount') }}<br />
                                {{ t('label.using-burden') }}<br />
                                {{ t('label.unit-price-amount') }}
                              </th>
                              <!-- 利用者負担 -->
                              <th
                                colspan="2"
                                class="pl-2 pr-2"
                                style="min-width: 160px"
                              >
                                {{ t('label.user-burden') }}
                              </th>
                            </tr>
                            <tr>
                              <!-- 割引後_率 -->
                              <th class="pl-2 pr-2">
                                {{ t('label.rate') }}{{ t('label.percent-icon') }}
                              </th>
                              <!-- 割引後_単位数 -->
                              <th class="pl-2 pr-2">{{ t('label.unit-number') }}</th>
                              <!-- 種類支給限度_超過 -->
                              <th class="pl-2 pr-2">{{ t('label.exceeded') }}</th>
                              <!-- 種類支給限度_基準内 -->
                              <th class="pl-2 pr-2">{{ t('label.criterion') }}</th>
                              <!-- 区分支給限度_超過 -->
                              <th class="pl-2 pr-2">{{ t('label.exceeded') }}</th>
                              <!-- 区分支給限度_基準内 -->
                              <th class="pl-2 pr-2">{{ t('label.criterion') }}</th>
                              <!-- 利用者負担_保険/事業 -->
                              <th class="pl-2 pr-2">{{ t('label.insurance-project') }}</th>
                              <!-- 利用者負担_全額負担 -->
                              <th class="pl-2 pr-2">{{ t('label.total-burden') }}</th>
                            </tr>
                          </template>
                          <template #item="{ item, index }">
                            <tr
                              :class="{
                                'select-row': local.selectedUsingSlipOtherTableItemIndex === index,
                              }"
                              @click="selectUsingSlipOtherTableRow(index)"
                            >
                              <td
                                class="pl-2 pr-2"
                                :style="[showUsingSlipOtherTableBottomBorder(index)]"
                              >
                                {{ item.dmyJigyoNameKnj }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                :style="[showUsingSlipOtherTableBottomBorder(index)]"
                              >
                                {{ item.dmyJigyoNumber }}
                              </td>
                              <td
                                class="pl-2 pr-2"
                                :style="[showUsingSlipOtherTableBottomBorder(index)]"
                              >
                                {{ item.dmyFormalnameKnj }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                :style="[showUsingSlipOtherTableBottomBorder(index)]"
                              >
                                {{ item.cScode }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                :style="[showUsingSlipOtherTableBottomBorder(index)]"
                              >
                                {{ showTensuCellValue(item) ? item.tensu : '' }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                :style="[showUsingSlipOtherTableBottomBorder(index)]"
                              >
                                {{ showTensuCellValue(item) ? item.waribikiRitu : '' }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                :style="[showUsingSlipOtherTableBottomBorder(index)]"
                              >
                                {{ showTensuCellValue(item) ? item.waribikiTen : '' }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                :style="[showUsingSlipOtherTableBottomBorder(index)]"
                              >
                                {{ showTensuCellValue(item) ? item.kaisu : '' }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                :style="[showUsingSlipOtherTableBottomBorder(index)]"
                              >
                                {{ item.svTensu }}
                              </td>
                              <td
                                v-if="showBenefitManagement"
                                class="pl-2 pr-2 text-right"
                                :style="[showUsingSlipOtherTableBottomBorder(index)]"
                              >
                                {{ item.benefitManagement }}
                              </td>
                              <td
                                style="padding: 0px !important"
                                :style="[showUsingSlipOtherTableBottomBorder(index)]"
                              >
                                <base-mo01278
                                  v-model="item.sTensuOver"
                                  :oneway-model-value="localOneway.mo01278TensuOverOneway"
                                  style="width: 65px; border-radius: 0"
                                  :disabled="sTensuOverDisabled(item)"
                                  @update:model-value="
                                    tensuOverChanged(index, Or36647Const.TENSU_OVER_FLG_S)
                                  "
                                />
                              </td>
                              <td
                                class="pl-2 pr-2"
                                :style="[showUsingSlipOtherTableBottomBorder(index)]"
                              >
                                {{ item.sTensu }}
                              </td>
                              <td
                                style="padding: 0px !important"
                                :style="[showUsingSlipOtherTableBottomBorder(index)]"
                              >
                                <base-mo01278
                                  v-model="item.kTensuOver"
                                  :oneway-model-value="localOneway.mo01278TensuOverOneway"
                                  style="width: 65px; border-radius: 0"
                                  :disabled="kTensuOverDisabled(item)"
                                  @update:model-value="
                                    tensuOverChanged(index, Or36647Const.TENSU_OVER_FLG_K)
                                  "
                                />
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                :style="[showUsingSlipOtherTableBottomBorder(index)]"
                              >
                                {{ item.kTensu }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                :style="[showUsingSlipOtherTableBottomBorder(index)]"
                              >
                                {{ showTanka(item) ? item.tanka : '' }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                :style="[showUsingSlipOtherTableBottomBorder(index)]"
                              >
                                {{ showTanka(item) ? item.hiyouSougaku : '' }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                :style="[showUsingSlipOtherTableBottomBorder(index)]"
                              >
                                {{ showKyufuRitu(item) ? item.kyufuRitu : '' }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                :style="[showUsingSlipOtherTableBottomBorder(index)]"
                              >
                                {{ showHKyufugaku(item) ? item.hKyufugaku : '' }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                :style="[showUsingSlipOtherTableBottomBorder(index)]"
                              >
                                {{
                                  item.cSougouKbn === Or36647Const.SOUGOU_KBN_2
                                    ? item.tHutanTanka
                                    : ''
                                }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                :style="[showUsingSlipOtherTableBottomBorder(index)]"
                              >
                                {{ showHutanH(item) ? item.hutanH : '' }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                :style="[showUsingSlipOtherTableBottomBorder(index)]"
                              >
                                {{
                                  item.totalF === Or36647Const.TOTAL_FLG_TOTAL ? item.hutanJ : ''
                                }}
                              </td>
                            </tr>
                          </template>
                          <template #tfoot>
                            <tr class="fixed-footer">
                              <!-- （列フッター）割り振りの残り単位数 -->
                              <td
                                class="pl-2 pr-2"
                                colspan="2"
                                style="border-right: none !important"
                              >
                                <c-v-row
                                  v-if="showAllocationRestUnitNumber"
                                  no-gutters
                                >
                                  <c-v-col cols="10">
                                    {{ t('label.table-maintenance-header-14') }}
                                  </c-v-col>
                                  <c-v-col cols="2">{{ allocationRestUnitNumber }}</c-v-col>
                                </c-v-row>
                              </td>
                              <!-- （列フッター）区分支給限度額基準(単位) -->
                              <td
                                colspan="5"
                                class="pl-2 pr-2"
                                style="border-right: none !important"
                              >
                                <c-v-row no-gutters>
                                  <c-v-col cols="10">
                                    {{ t('label.category-payment-limit-base-amount-unit') }}
                                  </c-v-col>
                                  <c-v-col cols="2">
                                    {{ local.usingSlipInfo.kbnShikyuuGendoData[0].tKGendo }}
                                  </c-v-col>
                                </c-v-row>
                              </td>
                              <!-- （列フッター）計 -->
                              <td
                                class="pl-2 pr-2"
                                style="border-right: none !important"
                              >
                                {{ t('label.using-slip-total-amount') }}
                              </td>
                              <!-- （列フッター）計_サービス単位/金額 -->
                              <td
                                class="pl-2 pr-2 text-right"
                                :style="['color: ' + cSvTensuColor]"
                                style="border-right: none !important"
                              >
                                {{ getUsingSlipOtherTableTotal(Or36647Const.KEY_C_SV_TENSU) }}
                              </td>
                              <!-- （列フッター）計_給付管理単位数 -->
                              <td
                                v-if="showBenefitManagement"
                                class="pl-2 pr-2 text-right"
                                :style="['color: ' + cKyufukanriTenColor]"
                                style="border-right: none !important"
                              >
                                {{ getUsingSlipOtherTableTotal(Or36647Const.KEY_C_KYUFUKANRI_TEN) }}
                              </td>
                              <!-- （列フッター）計_種類支給限度_超過 -->
                              <td
                                class="pl-2 pr-2 text-right font-red"
                                style="border-right: none !important"
                              >
                                {{ getUsingSlipOtherTableTotal(Or36647Const.KEY_C_S_TENSU_OVER) }}
                              </td>
                              <!-- （列フッター）計_種類支給限度_基準内 -->
                              <td
                                class="pl-2 pr-2 text-right"
                                style="border-right: none !important"
                              >
                                {{ getUsingSlipOtherTableTotal(Or36647Const.KEY_C_S_TENSU_) }}
                              </td>
                              <!-- （列フッター）計_区分支給限度_超過 -->
                              <td
                                class="pl-2 pr-2 text-right font-red"
                                style="border-right: none !important"
                              >
                                {{ getUsingSlipOtherTableTotal(Or36647Const.KEY_C_K_TENSU_OVER) }}
                              </td>
                              <!-- （列フッター）計_区分支給限度_基準内 -->
                              <td
                                class="pl-2 pr-2 text-right"
                                style="border-right: none !important"
                              >
                                {{ getUsingSlipOtherTableTotal(Or36647Const.KEY_C_K_TENSU) }}
                              </td>
                              <!-- スラッシュ -->
                              <td
                                class="pa-0"
                                style="border-right: none !important"
                              >
                                <div class="line"></div>
                              </td>
                              <!-- （列フッター）計_費用総額保険/事業対象分 -->
                              <td
                                class="pl-2 pr-2 text-right"
                                style="border-right: none !important"
                              >
                                {{ getUsingSlipOtherTableTotal(Or36647Const.KEY_C_HIYOU_SOUGAKU) }}
                              </td>
                              <!-- スラッシュ -->
                              <td
                                class="pa-0"
                                style="border-right: none !important"
                              >
                                <div class="line"></div>
                              </td>
                              <!-- （列フッター）計_保険/事業費請求額 -->
                              <td
                                class="pl-2 pr-2 text-right"
                                style="border-right: none !important"
                              >
                                {{ getUsingSlipOtherTableTotal(Or36647Const.KEY_C_H_KYUFUGAKU) }}
                              </td>
                              <!-- スラッシュ -->
                              <td
                                class="pa-0"
                                style="border-right: none !important"
                              >
                                <div class="line"></div>
                              </td>
                              <!-- （列フッター）計_利用者負担_保険/事業 -->
                              <td
                                class="pl-2 pr-2 text-right"
                                :style="['color: ' + hutanHHutanJColor]"
                                style="border-right: none !important"
                              >
                                {{ getUsingSlipOtherTableTotal(Or36647Const.KEY_C_HUTAN_H) }}
                              </td>
                              <!-- （列フッター）計_利用者負担_全額負担 -->
                              <td
                                class="pl-2 pr-2 text-right"
                                :style="['color: ' + hutanHHutanJColor]"
                              >
                                {{ getUsingSlipOtherTableTotal(Or36647Const.KEY_HUTAN_J) }}
                              </td>
                            </tr>
                          </template>
                        </c-v-data-table>
                      </c-v-col>
                    </c-v-row>
                    <c-v-row
                      v-if="local.usingSlipInfo.syuruiGendoFlg === Or36647Const.SYURUI_GENDO_FLG_ON"
                      no-gutters
                      class="list-table"
                    >
                      <c-v-col>
                        <!-- 種類別支給限度額欄 -->
                        <c-v-data-table
                          class="mt-2 table-wrapper"
                          style="background-color: transparent"
                          hover
                          fixed-header
                          hide-default-footer
                          :items-per-page="-1"
                          hide-no-data
                        >
                          <template #headers>
                            <tr>
                              <!-- サービス種類 -->
                              <th class="pl-2 pr-2 width-1">{{ t('label.serviceType') }}</th>
                              <!-- 限度(種) -->
                              <th class="pl-2 pr-2 width-1">{{ t('label.limit-type') }}</th>
                              <!-- 合計 -->
                              <th class="pl-2 pr-2 width-1">{{ t('label.day-total') }}</th>
                              <!-- 超過 -->
                              <th class="pl-2 pr-2 width-1">{{ t('label.exceeded') }}</th>
                              <!-- サービス種類 -->
                              <th class="pl-2 pr-2 width-1">{{ t('label.serviceType') }}</th>
                              <!-- 限度(種) -->
                              <th class="pl-2 pr-2 width-1">{{ t('label.limit-type') }}</th>
                              <!-- 合計 -->
                              <th class="pl-2 pr-2 width-1">{{ t('label.day-total') }}</th>
                              <!-- 超過 -->
                              <th class="pl-2 pr-2 width-1">{{ t('label.exceeded') }}</th>
                              <!-- サービス種類 -->
                              <th class="pl-2 pr-2 width-1">{{ t('label.serviceType') }}</th>
                              <!-- 限度(種) -->
                              <th class="pl-2 pr-2 width-1">{{ t('label.limit-type') }}</th>
                              <!-- 合計 -->
                              <th class="pl-2 pr-2 width-1">{{ t('label.day-total') }}</th>
                              <!-- 超過 -->
                              <th class="pl-2 pr-2 width-1">{{ t('label.exceeded') }}</th>
                            </tr>
                          </template>
                          <template #body>
                            <tr
                              :class="{
                                'select-row': local.selectedForehead === 1,
                              }"
                              @click="selectForeheadTableRow(1)"
                            >
                              <td
                                class="pl-2 pr-2"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svName1 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svGendo1 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svTotal1 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right font-red"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svOver1 }}
                              </td>
                              <td
                                class="pl-2 pr-2"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svName6 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svGendo6 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svTotal6 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right font-red"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svOver6 }}
                              </td>
                              <td
                                class="pl-2 pr-2"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svName11 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svGendo11 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svTotal11 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right font-red"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svOver11 }}
                              </td>
                            </tr>
                            <tr
                              :class="{
                                'select-row': local.selectedForehead === 2,
                              }"
                              @click="selectForeheadTableRow(2)"
                            >
                              <td
                                class="pl-2 pr-2"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svName2 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svGendo2 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svTotal2 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right font-red"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svOver2 }}
                              </td>
                              <td
                                class="pl-2 pr-2"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svName7 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svGendo7 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svTotal7 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right font-red"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svOver7 }}
                              </td>
                              <td
                                class="pl-2 pr-2"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svName12 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svGendo12 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svTotal12 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right font-red"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svOver12 }}
                              </td>
                            </tr>
                            <tr
                              :class="{
                                'select-row': local.selectedForehead === 3,
                              }"
                              @click="selectForeheadTableRow(3)"
                            >
                              <td
                                class="pl-2 pr-2"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svName3 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svGendo3 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svTotal3 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right font-red"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svOver3 }}
                              </td>
                              <td
                                class="pl-2 pr-2"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svName8 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svGendo8 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svTotal8 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right font-red"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svOver8 }}
                              </td>
                              <td
                                class="pl-2 pr-2"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ t('label.day-total') }}
                              </td>
                              <td
                                colspan="2"
                                class="pa-0"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                <div class="line"></div>
                              </td>
                              <td
                                class="pl-2 pr-2 text-right font-red"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ svOverTotal }}
                              </td>
                            </tr>
                            <tr
                              :class="{
                                'select-row': local.selectedForehead === 4,
                              }"
                              @click="selectForeheadTableRow(4)"
                            >
                              <td
                                class="pl-2 pr-2"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svName4 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svGendo4 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svTotal4 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right font-red"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svOver4 }}
                              </td>
                              <td
                                class="pl-2 pr-2"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svName9 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svGendo9 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svTotal9 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right font-red"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svOver9 }}
                              </td>
                            </tr>
                            <tr
                              :class="{
                                'select-row': local.selectedForehead === 5,
                              }"
                              @click="selectForeheadTableRow(5)"
                            >
                              <td
                                class="pl-2 pr-2"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svName5 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svGendo5 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svTotal5 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right font-red"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svOver5 }}
                              </td>
                              <td
                                class="pl-2 pr-2"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svName10 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svGendo10 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svTotal10 }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right font-red"
                                style="
                                  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
                                "
                              >
                                {{ local.usingSlipInfo.syuruiGendoData[0].svOver10 }}
                              </td>
                            </tr>
                          </template>
                        </c-v-data-table>
                      </c-v-col>
                    </c-v-row>
                    <c-v-row
                      v-if="local.usingSlipInfo.kohiFlg === Or36647Const.KOHI_FLG_ON"
                      no-gutters
                    >
                      <c-v-col
                        cols="3"
                        class="pr-2"
                      >
                        <!-- 公費集計欄一覧 -->
                        <c-v-data-table
                          class="mt-2 table-wrapper tbl-publicly-funded-sum"
                          fixed-header
                          hover
                          :headers="publiclyFundedSumHeaders"
                          :items="local.kohiList"
                          hide-default-footer
                          :items-per-page="-1"
                          hide-no-data
                        >
                          <template #item="{ item, index }">
                            <tr
                              :class="{
                                'select-row': local.selectedPublic === index,
                              }"
                              @click="selectPublicTableRow(index)"
                            >
                              <td
                                class="pl-2 pr-2"
                                :style="[showKohiTableBottomBorder(index)]"
                              >
                                {{ item.dmyJigyoNameKnj }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                :style="[showKohiTableBottomBorder(index)]"
                              >
                                {{ item.kohiGaku }}
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                :style="[showKohiTableBottomBorder(index)]"
                              >
                                {{ item.honninHutan }}
                              </td>
                            </tr>
                          </template>
                          <template #tfoot>
                            <tr class="fixed-footer">
                              <!-- （列フッター）公費額合計ラベル -->
                              <td
                                class="pl-2 pr-2 th-bg"
                                style="border-right: none !important"
                              >
                                {{ t('label.publicly-funded-amount-total') }}
                              </td>
                              <!-- （列フッター）公費額合計 -->
                              <td
                                class="pl-2 pr-2 text-right"
                                style="border-right: none !important"
                              >
                                {{ local.usingSlipInfo.kohiGakuSum }}
                              </td>
                              <!-- （列フッター）本人負担合計 -->
                              <td
                                class="pl-2 pr-2 text-right"
                                style="border-left: none !important"
                              >
                                {{ local.usingSlipInfo.honninHutanSum }}
                              </td>
                            </tr>
                          </template>
                        </c-v-data-table>
                      </c-v-col>
                      <c-v-col
                        cols="auto"
                        class="pr-2 pt-2"
                      >
                        <div
                          class="line3-flex-box"
                          style="margin-top: 20px"
                        >
                          <!-- 行追加ボタン: Or21735 -->
                          <div>
                            <g-base-or-21735 v-bind="or21735_2" />
                            <c-v-tooltip
                              activator="parent"
                              location="bottom"
                              :text="$t('tooltip.care-plan2-newline-btn')"
                            ></c-v-tooltip>
                          </div>
                          <!-- 行削除ボタン: Or21738 -->
                          <div>
                            <g-base-or-21738
                              v-bind="or21738_2"
                              :disabled="local.selectedRiyouryouItemIndex < 0"
                            />
                            <c-v-tooltip
                              activator="parent"
                              location="bottom"
                              :text="$t('tooltip.care-plan2-deleteline-btn')"
                            ></c-v-tooltip>
                          </div>
                        </div>
                        <!-- 利用料集計欄一覧 -->
                        <c-v-data-table
                          v-resizable-grid="{ columnWidths: columnMinWidth }"
                          class="mt-2 table-wrapper tbl-using-fee-sum"
                          fixed-header
                          hover
                          :headers="usingFeeSumHeaders"
                          :items="riyouryouList"
                          hide-default-footer
                          :items-per-page="-1"
                          hide-no-data
                        >
                          <template #item="{ item, index }">
                            <tr
                              :class="{
                                'select-row': local.selectedRiyouryouItemIndex === index,
                              }"
                              @click="selectusingFeeSumRow(index)"
                            >
                              <td
                                style="padding: 0 !important"
                                :style="[showRiyouryouTableBottomBorder(index)]"
                              >
                                <base-mo01282
                                  v-model="item.svJigyoId"
                                  :oneway-model-value="localOneway.mo01282OfficeOneway"
                                  @change="updRiyouryou"
                                />
                              </td>
                              <td
                                style="padding: 0px !important"
                                :style="[showRiyouryouTableBottomBorder(index)]"
                              >
                                <base-mo01274
                                  v-model="item.riyouryouItem"
                                  :oneway-model-value="localOneway.mo01274ItemOneway"
                                  style="width: 120px; border-radius: 0"
                                  maxlength="10"
                                  @change="updRiyouryou"
                                />
                              </td>
                              <td
                                style="padding: 0px !important"
                                :style="[showRiyouryouTableBottomBorder(index)]"
                              >
                                <base-mo01278
                                  v-model="item.riyouryouTanka"
                                  :oneway-model-value="localOneway.mo01278TensuOverOneway"
                                  style="width: 80px; border-radius: 0"
                                  @update:model-value="getRiyouryouKingaku"
                                />
                              </td>
                              <td
                                style="padding: 0px !important"
                                :style="[showRiyouryouTableBottomBorder(index)]"
                              >
                                <base-mo01278
                                  v-model="item.riyouryouSu"
                                  :oneway-model-value="localOneway.mo01278NumberOneway"
                                  style="width: 80px; border-radius: 0"
                                  @update:model-value="getRiyouryouKingaku"
                                />
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                :style="[showRiyouryouTableBottomBorder(index)]"
                              >
                                {{ item.riyouryouKingaku }}
                              </td>
                            </tr>
                          </template>
                          <template #tfoot>
                            <tr class="fixed-footer">
                              <td
                                class="pl-2 pr-2 th-bg"
                                style="border-right: none !important"
                              ></td>
                              <td
                                class="pl-2 pr-2 th-bg"
                                style="border-right: none !important"
                              ></td>
                              <!-- （列フッター）利用料合計ラベル -->
                              <td
                                colspan="2"
                                class="pl-2 pr-2 th-bg"
                                style="border-right: none !important"
                              >
                                {{ t('label.using-fee-amount-total') }}
                              </td>
                              <!-- （列フッター）利用料合計 -->
                              <td class="pl-2 pr-2 th-bg text-right">
                                {{ local.usingSlipInfo.riyouryouSum }}
                              </td>
                            </tr>
                          </template>
                        </c-v-data-table>
                      </c-v-col>
                      <c-v-col
                        cols="auto"
                        class="pt-2"
                      >
                        <div
                          class="line3-flex-box"
                          style="margin-top: 20px"
                        >
                          <!-- 行追加ボタン: Or21735 -->
                          <div>
                            <g-base-or-21735 v-bind="or21735_3" />
                            <c-v-tooltip
                              activator="parent"
                              location="bottom"
                              :text="$t('tooltip.care-plan2-newline-btn')"
                            ></c-v-tooltip>
                          </div>
                          <!-- 行削除ボタン: Or21738 -->
                          <div>
                            <g-base-or-21738
                              v-bind="or21738_3"
                              :disabled="local.selectedSyafukuItemIndex < 0"
                            />
                            <c-v-tooltip
                              activator="parent"
                              location="bottom"
                              :text="$t('tooltip.care-plan2-deleteline-btn')"
                            ></c-v-tooltip>
                          </div>
                          <!-- 設定ボタン -->
                          <base-mo00611
                            :oneway-model-value="localOneway.mo00611SettingOneway"
                            @click="syafukuSetting"
                          />
                        </div>
                        <!-- 社福軽減集計欄一覧 -->
                        <c-v-data-table
                          class="mt-2 table-wrapper tbl-social-welfare-alleviation-sum"
                          fixed-header
                          hover
                          :headers="socialWelfareAlleviationSumHeaders"
                          :items="syafukuList"
                          hide-default-footer
                          :items-per-page="-1"
                          hide-no-data
                        >
                          <template #item="{ item, index }">
                            <tr
                              :class="{
                                'select-row': local.selectedSyafukuItemIndex === index,
                              }"
                              @click="selectSocialWelfareAlleviationSumRow(index)"
                            >
                              <td
                                style="padding: 0 !important"
                                :style="[showSyafukuTableBottomBorder(index)]"
                              >
                                <base-mo01282
                                  v-model="item.svJigyoId"
                                  :oneway-model-value="localOneway.mo01282OfficeOneway"
                                  :disabled="item.sakuKbn === Or36647Const.SAKU_KBN_1"
                                  @change="updSyafuku"
                                />
                              </td>
                              <td
                                class="pl-2 pr-2"
                                :style="[showSyafukuTableBottomBorder(index)]"
                              >
                                {{ item.syafukuNm }}
                              </td>
                              <td
                                style="padding: 0px !important"
                                :style="[showSyafukuTableBottomBorder(index)]"
                              >
                                <base-mo01278
                                  v-model="item.syafukuObj"
                                  :oneway-model-value="localOneway.mo01278TargetAmountOneway"
                                  style="width: 80px; border-radius: 0"
                                  :disabled="item.sakuKbn === Or36647Const.SAKU_KBN_1"
                                  @update:model-value="getSyafukuGaku"
                                />
                              </td>
                              <td
                                style="padding: 0px !important"
                                :style="[showSyafukuTableBottomBorder(index)]"
                              >
                                <base-mo01274
                                  v-model="item.syafukuRitu"
                                  :oneway-model-value="localOneway.mo01274ItemOneway"
                                  style="width: 80px; border-radius: 0; text-align: right"
                                  :disabled="item.sakuKbn === Or36647Const.SAKU_KBN_1"
                                  maxlength="5"
                                  @change="getSyafukuGaku"
                                />
                              </td>
                              <td
                                class="pl-2 pr-2 text-right"
                                :style="[showSyafukuTableBottomBorder(index)]"
                              >
                                {{ item.syafukuGaku }}
                              </td>
                            </tr>
                          </template>
                          <template #tfoot>
                            <tr class="fixed-footer">
                              <td
                                class="pl-2 pr-2 th-bg"
                                style="border-right: none !important"
                              ></td>
                              <td
                                class="pl-2 pr-2 th-bg"
                                style="border-right: none !important"
                              ></td>
                              <!-- （列フッター）社福軽減合計ラベル -->
                              <td
                                colspan="2"
                                class="pl-2 pr-2 th-bg"
                                style="border-right: none !important"
                              >
                                {{ t('label.social-welfare-alleviation-amount-total') }}
                              </td>
                              <!-- （列フッター）社福軽減合計 -->
                              <td class="pl-2 pr-2 th-bg text-right">
                                {{ local.usingSlipInfo.syafukuSum }}
                              </td>
                            </tr>
                          </template>
                        </c-v-data-table>
                      </c-v-col>
                    </c-v-row>
                  </c-v-window-item>
                </c-v-window>
              </c-v-col>
            </c-v-row>
            <c-v-row
              no-gutters
              class="list-table mt-2"
            >
              <!--短期入所利用日数 -->
              <c-v-col
                cols="2"
                class="pr-2"
              >
                <c-v-data-table
                  class="table-wrapper"
                  hide-default-footer
                  :items="local.usingSlipInfo.sumData"
                  fixed-header
                  hover
                  :items-per-page="-1"
                  hide-no-data
                >
                  <template #headers>
                    <tr>
                      <th
                        colspan="3"
                        class="pl-2 pr-2"
                        style="border-bottom: unset !important"
                      >
                        <div
                          class="d-flex"
                          style="align-items: center; justify-content: center"
                        >
                          <div>{{ t('label.short-termadmission-using-day') }}</div>
                          <c-v-divider
                            vertical
                            class="ma-1"
                            style="position: absolute; right: 26px; top: 2px; height: 28px"
                          />
                          <base-mo00009
                            :oneway-model-value="localOneway.mo00009Oneway"
                            style="position: absolute; right: 0"
                            :disabled="!hasRegist"
                            @click="shortTermadmissionUsingDay"
                          >
                            <c-v-tooltip
                              activator="parent"
                              location="bottom"
                              :text="$t('tooltip.short-termadmission-using-day')"
                            ></c-v-tooltip>
                          </base-mo00009>
                        </div>
                      </th>
                    </tr>
                    <tr>
                      <th class="pl-2 pr-2 tbl-title-33">{{ t('label.pre-month') }}</th>
                      <th class="pl-2 pr-2 tbl-title-33">{{ t('label.current-month') }}</th>
                      <th class="pl-2 pr-2 tbl-title-33">{{ t('label.accumulation') }}</th>
                    </tr>
                  </template>
                  <template #item="{ item }">
                    <tr
                      :class="{
                        'select-row': local.selectedShort === 1,
                      }"
                      @click="selectShortTableRow(1)"
                    >
                      <td class="pl-2 pr-2 text-right">{{ item.zenGetuSum }}</td>
                      <td class="pl-2 pr-2 text-right">{{ item.cmnTucPlanTTougetu }}</td>
                      <td
                        class="pl-2 pr-2 text-right"
                        :style="['color: ' + getComputeRuikeiSumColor(item)]"
                      >
                        {{ item.computeRuikeiSum }}
                      </td>
                    </tr>
                  </template>
                </c-v-data-table>
              </c-v-col>
              <!--単位数計算結果 -->
              <c-v-col
                cols="2"
                class="pr-2"
              >
                <c-v-data-table
                  class="table-wrapper"
                  hide-default-footer
                  :items="local.usingSlipInfo.sumData"
                  fixed-header
                  hover
                  :items-per-page="-1"
                  hide-no-data
                >
                  <template #headers>
                    <tr>
                      <th
                        colspan="3"
                        class="pl-2 pr-2"
                        style="border-bottom: unset !important"
                      >
                        <div class="d-flex text-center">
                          {{ t('label.unit-count-calculation-result') }}
                        </div>
                      </th>
                    </tr>
                    <tr>
                      <th class="pl-2 pr-2 tbl-title-33">{{ t('label.schedule') }}</th>
                      <th class="pl-2 pr-2 tbl-title-33">{{ t('label.achievements') }}</th>
                      <th class="pl-2 pr-2 tbl-title-33">{{ t('label.difference') }}</th>
                    </tr>
                  </template>
                  <template #item="{ item }">
                    <tr
                      :class="{
                        'select-row': local.selectedUnit === 1,
                      }"
                      @click="selectUnitTableRow(1)"
                    >
                      <td class="pl-2 pr-2 text-right">{{ item.tensuYoteiSum }}</td>
                      <td class="pl-2 pr-2 text-right">{{ item.tensuJissekiSum }}</td>
                      <td class="pl-2 pr-2 text-right">{{ item.diffSum }}</td>
                    </tr>
                  </template>
                </c-v-data-table>
              </c-v-col>
              <!--区分支給限度 -->
              <c-v-col
                cols="2"
                class="pr-2"
              >
                <c-v-data-table
                  class="table-wrapper"
                  hide-default-footer
                  :items="local.usingSlipInfo.sumData"
                  fixed-header
                  hover
                  :items-per-page="-1"
                  hide-no-data
                >
                  <template #headers>
                    <tr>
                      <th
                        colspan="3"
                        class="pl-2 pr-2"
                        style="border-bottom: unset !important"
                      >
                        <div class="d-flex text-center">
                          {{ t('label.category-payment-limit') }}
                        </div>
                      </th>
                    </tr>
                    <tr>
                      <th class="pl-2 pr-2 tbl-title-50">{{ t('label.exceeded') }}</th>
                      <th class="pl-2 pr-2 tbl-title-50">{{ t('label.criterion') }}</th>
                    </tr>
                  </template>
                  <template #item="{ item }">
                    <tr
                      :class="{
                        'select-row': local.selectedDivision === 1,
                      }"
                      @click="selectDivisionTableRow(1)"
                    >
                      <td class="pl-2 pr-2 text-right">{{ item.kbnOverSum }}</td>
                      <td class="pl-2 pr-2 text-right">{{ item.kbnKijunSum }}</td>
                    </tr>
                  </template>
                </c-v-data-table>
              </c-v-col>
              <!--利用者負担額 -->
              <c-v-col
                cols="2"
                class="pr-2"
              >
                <c-v-data-table
                  class="table-wrapper"
                  hide-default-footer
                  :items="local.usingSlipInfo.sumData"
                  fixed-header
                  hover
                  :items-per-page="-1"
                  hide-no-data
                >
                  <template #headers>
                    <tr>
                      <th
                        colspan="3"
                        class="pl-2 pr-2"
                        style="border-bottom: unset !important"
                      >
                        <div class="d-flex text-center">{{ t('label.user-burden-amount') }}</div>
                      </th>
                    </tr>
                    <tr>
                      <th class="pl-2 pr-2 tbl-title-50">{{ t('label.insurance') }}</th>
                      <th class="pl-2 pr-2 tbl-title-50">{{ t('label.full-amount') }}</th>
                    </tr>
                  </template>
                  <template #item="{ item }">
                    <tr
                      :class="{
                        'select-row': local.selectedBurden === 1,
                      }"
                      @click="selectBurdenTableRow(1)"
                    >
                      <td class="pl-2 pr-2 text-right">{{ item.hknHutanSum }}</td>
                      <td class="pl-2 pr-2 text-right">{{ item.jihHutanSum }}</td>
                    </tr>
                  </template>
                </c-v-data-table>
              </c-v-col>
              <!--保険外利用料 -->
              <c-v-col
                cols="4"
                class="d-flex"
              >
                <c-v-data-table
                  class="table-wrapper"
                  hide-default-footer
                  :items="local.usingSlipInfo.sumData"
                  fixed-header
                  hover
                  :items-per-page="-1"
                  hide-no-data
                >
                  <template #headers>
                    <tr>
                      <th
                        rowspan="2"
                        class="pl-2 pr-2 tbl-title-18"
                      >
                        <div class="d-flex text-left">
                          {{ t('label.insurance-other-than') }}<br />{{ t('label.using-fee') }}
                        </div>
                      </th>
                      <th
                        colspan="2"
                        class="pl-2 pr-2 tbl-title-18"
                      >
                        <div class="d-flex text-left">
                          {{ t('label.applicable-publicly-funded') }}
                        </div>
                      </th>
                      <th
                        rowspan="2"
                        class="pl-2 pr-2"
                      >
                        <div class="d-flex text-left">
                          {{ t('label.social-welfare-alleviation') }}
                        </div>
                      </th>
                      <th
                        rowspan="2"
                        class="pl-2 pr-2"
                      >
                        <div class="d-flex text-left">
                          {{ t('label.discount-user') }}<br />{{ t('label.burden-amount') }}
                        </div>
                      </th>
                    </tr>
                    <tr>
                      <th class="pl-2 pr-2 tbl-title-18">
                        {{ t('label.publicly-funded-amount') }}
                      </th>
                      <th
                        class="pl-2 pr-2 tbl-title-18"
                        style="border-right: none !important"
                      >
                        {{ t('label.independence-burden') }}
                      </th>
                    </tr>
                  </template>
                  <template #item="{ item }">
                    <tr
                      :class="{
                        'select-row': local.selectedInsurance === 1,
                      }"
                      @click="selectInsuranceTableRow(1)"
                    >
                      <td class="pl-2 pr-2 text-right">{{ item.hokenRiyouSum }}</td>
                      <td class="pl-2 pr-2 text-right">{{ item.kouhiGakuSum }}</td>
                      <td class="pl-2 pr-2 text-right">{{ item.honninHutanGakuSum }}</td>
                      <td class="pl-2 pr-2 text-right">{{ item.keigenSum }}</td>
                      <td class="pl-2 pr-2 text-right">{{ item.sasihikiHutanGakuSum }}</td>
                    </tr>
                  </template>
                </c-v-data-table>
                <div v-if="local.usingSlipInfo.sumData.length > 0">
                  <div
                    class="d-flex flex-column justify-space-around align-center"
                    style="height: 64px"
                  >
                    <div class="d-flex">{{ t('label.biko-pre') }}</div>
                    <div class="d-flex">{{ t('label.biko-suffix') }}</div>
                  </div>
                  <div
                    class="d-flex"
                    style="height: 32px; align-items: center"
                  >
                    <base-mo00009
                      :oneway-model-value="localOneway.mo00009Oneway"
                      @click="openBiko"
                    >
                      <c-v-tooltip
                        activator="parent"
                        location="bottom"
                        :text="$t('tooltip.show-biko')"
                      ></c-v-tooltip>
                    </base-mo00009>
                  </div>
                </div>
              </c-v-col>
            </c-v-row>
          </c-v-container>
        </c-v-sheet>
      </c-v-col>
    </c-v-row>
    <g-base-or00051 />
    <!-- 確認ダイアログ -->
    <g-base-or-21814
      v-if="showDialogOr21814"
      v-bind="or21814"
    />
    <!-- 警告ダイアログ -->
    <g-base-or-21815
      v-if="showDialogOr21815"
      v-bind="or21815"
    />
    <!-- 週間計画取り込みモーダル -->
    <g-custom-or-26817
      v-if="showDialogOr26817"
      v-bind="or26817"
      v-model="local.usingSlipList"
      :oneway-model-value="localOneway.or26817Oneway"
    />
    <!-- 利用票別表メンテナンス -->
    <g-custom-or-26828
      v-if="showDialogOr26828"
      v-bind="or26828"
      v-model="local.or26828"
      :oneway-model-value="localOneway.or26828Oneway"
    />
    <!-- 福祉用具貸与単位数一括設定 -->
    <g-custom-or-30707
      v-if="showDialogOr30707"
      v-bind="or30707"
      v-model="local.or30707"
      :oneway-model-value="localOneway.or30707Oneway"
    />
    <!-- 有効期間外サービス検索 -->
    <g-custom-or-27736
      v-if="showDialogOr27736"
      v-bind="or27736"
      :oneway-model-value="localOneway.or27736Oneway"
      :unique-cp-id="or27736.uniqueCpId"
      :parent-cp-id="props.uniqueCpId"
    />
    <!-- 利用票・別表画面で警告情報ダイアログ -->
    <g-custom-or-10923
      v-if="showDialogOr10923"
      v-bind="or10923"
      :oneway-model-value="localOneway.or10923Oneway"
    />
    <!-- 日割利用期間ダイアログ -->
    <g-custom-or-07212
      v-if="showDialogOr07212"
      v-bind="or07212"
      :oneway-model-value="localOneway.or07212Oneway"
    />
    <!-- 保険者選択ダイアログ -->
    <g-custom-or-27635
      v-if="showDialogOr27635"
      v-bind="or27635"
      v-model="local.or27635"
      :oneway-model-value="localOneway.or27635Oneway"
    />
    <!-- 利用票予定→実績変換ダイアログ -->
    <g-custom-or35687
      v-if="showDialogOr35687"
      v-bind="or35687"
      v-model="local.or35687"
      :oneway-model-value="localOneway.or35687Oneway"
    />
    <!-- カレンダー入力ダイアログ -->
    <g-custom-or-27016
      v-if="showDialogOr27016"
      v-bind="or27016"
      v-model="local.or27016"
      :oneway-model-value="localOneway.or27016Oneway"
    />
    <!-- 計画複写ダイアログ -->
    <g-custom-or35745
      v-if="showDialogOr35745"
      v-bind="or35745"
    />
    <!-- 希望負担額登録ダイアログ -->
    <g-custom-or-00233
      v-if="showDialogOr00233"
      v-bind="or00233"
      v-model="local.or00233"
      :oneway-model-value="localOneway.or00233Oneway"
    />
    <!-- 横出しサービス単位再設定ダイアログ -->
    <g-custom-or-27943
      v-if="showDialogOr27943"
      v-bind="or27943"
      v-model="local.or27943"
      :oneway-model-value="localOneway.or27943Oneway"
    />
    <!-- 確認画面 -->
    <g-custom-or-27852
      v-if="showDialogOr27852"
      v-bind="or27852"
    />
    <!-- 日割算定確認 -->
    <g-custom-or35957
      v-if="showDialogOr35957"
      v-bind="or35957"
      :oneway-model-value="localOneway.or35957Oneway"
    />
    <!-- シミュレーション雛形選択 -->
    <g-custom-or-28534
      v-if="showDialogOr28534"
      v-bind="or28534"
      v-model="local.or28534"
      :oneway-model-value="localOneway.or28534Oneway"
    />
    <!-- 特別指示期間 -->
    <g-custom-or-28334
      v-if="showDialogOr28334"
      v-bind="or28334"
      v-model="local.or28334"
      :oneway-model-value="localOneway.or28334Oneway"
      :parent-unique-cp-id="props.uniqueCpId"
    />
    <!-- 短期退所日登録 -->
    <g-custom-or-26835
      v-if="showDialogOr26835"
      v-bind="or26835"
      v-model="local.or26835"
      :oneway-model-value="localOneway.or26835Oneway"
      :parent-unique-cp-id="props.uniqueCpId"
    />
    <!-- 総合事業サービス単位再設定 -->
    <g-custom-or-27946
      v-if="showDialogOr27946"
      v-bind="or27946"
      v-model="local.or27946"
      :oneway-model-value="localOneway.or27946Oneway"
      :unique-cp-id="or27946.uniqueCpId"
      :parent-cp-id="props.uniqueCpId"
    />
    <!-- 社福軽減登録 -->
    <g-custom-or-26676
      v-if="showDialogOr26676"
      v-bind="or26676"
      v-model="local.or26676"
      :oneway-model-value="localOneway.or26676Oneway"
    />
    <!-- 認定期間中の短期入所利用日数 -->
    <g-custom-or-27856
      v-if="showDialogOr27856"
      v-bind="or27856"
      v-model="local.or27856"
      :oneway-model-value="localOneway.or27856Oneway"
    />
    <!-- 備考欄画面 -->
    <g-custom-or-29139
      v-if="showDialogOr29139"
      v-bind="or29139"
      v-model="local.or29139"
      :oneway-model-value="localOneway.or29139Oneway"
    />
    <!-- 印刷設定 -->
    <g-custom-or-57151
      v-if="showDialogOr57151"
      v-bind="or57151"
      :oneway-model-value="localOneway.or57151Oneway"
      :unique-cp-id="or57151.uniqueCpId"
      :parent-cp-id="props.uniqueCpId"
    />
  </c-v-sheet>
</template>

<style scoped lang="scss">
@use '@/styles/cmn/page-data-table.scss';
.view {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: transparent;
}

.content-area {
  display: flex;
  flex-grow: 1;
  overflow: hidden;
  height: 100%;
}

.content {
  padding: 5px 0px;
  overflow-x: auto;
}

.second-row {
  margin-top: 0px;
  align-items: baseline;
}

:deep(.v-sheet) {
  background-color: transparent !important;
}

:deep(.top-menu) {
  padding: 0 !important;
}

:deep(.top-menu .v-divider:nth-child(3)) {
  display: none;
}

:deep(.btn-copy, .btn-recompute) {
  border-color: rgb(var(--v-theme-key));
}

:deep(.offery-ym .d-flex) {
  width: 130px !important;
}

:deep(.offery-ym) {
  background-color: #ffffff !important;
}

:deep(.lbl1) {
  margin-left: -12px;
}

:deep(.lbl2) {
  font-size: 12px;
  margin: 9px 0;
  color: rgb(var(--v-theme-orange-400));
}

:deep(.inline-block) {
  display: inline-block !important;
}

.btn-insur-select {
  position: relative;
  top: 18px;
  left: -5px;
}

.flex-col {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  flex-shrink: 0;
}

.line3-flex-box {
  display: flex;
  column-gap: 8px;
  align-items: center;
  flex-shrink: 0;
  flex-wrap: wrap;
}

.tbl-title-18 {
  width: 18%;
}

.tbl-title-33 {
  width: 30%;
}

.tbl-title-50 {
  width: 50%;
}

:deep(.tbl-using-slip) {
  min-height: 575px;
  max-height: 575px;
}

.offer-hour-time::-webkit-calendar-picker-indicator {
  display: none;
}

:deep(.txt:disabled) {
  background: unset !important;
  opacity: 0.6 !important;
}

:deep(.v-list-item__prepend) {
  width: 25px;
}

:deep(.offer-hour-time) {
  padding: 0 13px !important;
}

.full-width-field-select,
.full-width-field {
  border-radius: 0 !important;
}

.full-width-field-select,
.full-width-field {
  padding-left: 4px !important;
  padding-right: 4px !important;
}

.list-table :deep(.table-wrapper .v-table__wrapper td) {
  border: none !important;
}

.list-table :deep(.v-table__wrapper td:first-child) {
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
}

.list-table :deep(.v-table__wrapper td:not(:first-child)) {
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
}

.list-table :deep(.v-table__wrapper td:last-child) {
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
}

.tbl-using-slip-other-table {
  max-height: 447px;
}

.fixed-footer {
  position: sticky;
  bottom: 0;
  background-color: #ffffff;
}

.fixed-footer td {
  height: 32px;
}

.line {
  width: 100%;
  height: 31px;
  background: linear-gradient(
    to bottom right,
    transparent calc(50% - 1px),
    rgb(var(--v-theme-black-200)) 50%,
    transparent calc(50% + 1px)
  );
}

.width-1 {
  width: 8.3333333333%;
  max-width: 8.3333333333%;
  min-width: 8.3333333333%;
}

.font-red {
  color: red;
}

.tbl-publicly-funded-sum {
  overflow-y: auto;
  max-height: 229px;
}

.tbl-publicly-funded-sum :deep(.v-table__wrapper > table > thead > tr > th) {
  padding: 0 8px !important;
}

.tbl-publicly-funded-sum :deep(.v-table__wrapper > table > tbody > tr > td:first-child) {
  border: none !important;
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
}

.tbl-publicly-funded-sum :deep(.v-table__wrapper > table > tbody > tr > td:not(:first-child)) {
  border: none !important;
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
}

.tbl-publicly-funded-sum :deep(.v-table__wrapper > table > tbody > tr > td:last-child) {
  border: none !important;
  border-bottom: 1px rgb(var(--v-theme-black-200)) solid !important;
}

.tbl-using-fee-sum,
.tbl-social-welfare-alleviation-sum {
  width: 475px;
  overflow-y: auto;
  max-height: 166px;
}

.tbl-using-fee-sum :deep(.v-table__wrapper > table > thead > tr > th) {
  padding: 0 8px !important;
}

.tbl-social-welfare-alleviation-sum :deep(.v-table__wrapper > table > thead > tr > th) {
  padding: 0 8px !important;
}

.th-bg {
  background-color: rgb(var(--v-theme-black-100)) !important;
  font-weight: 700;
}

.hoverd {
  background-color: rgba(var(--v-border-color), var(--v-hover-opacity)) !important;
}

.tbl-using-slip.table-wrapper
  .v-table__wrapper
  tr:not(.row-selected):not(.select-row)
  td:not(:has(input, textarea, select)) {
  background-color: unset;
}

.tbl-using-slip.table-wrapper :deep(.v-checkbox-btn) {
  min-height: 32px !important;
  height: 32px !important;
}
</style>
