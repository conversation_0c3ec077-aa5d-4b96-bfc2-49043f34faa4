<script setup lang="ts">
/**
 * Or01084：有機体：(日課計画マスタ)文字位置入力
 */
import { reactive, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or01084Const } from './Or01084.constants'
import type { Mo00039OnewayType } from '~/types/cmn/business/components/Mo00039Type'
import {  useScreenTwoWayBind } from '#imports'
import type { Or01084OneWayType, Or01084Type } from '~/types/cmn/business/components/Or01084Type'

const { t } = useI18n()
/**************************************************
 * Props
 **************************************************/
interface Props {
  uniqueCpId: string
  onewayModelValue: Or01084OneWayType
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const localOneway = reactive({
  or01084OneWay: {
    ...props.onewayModelValue,
  } as Or01084OneWayType,
  mo00039Oneway: {
    // デフォルト値の設定
    name: Or01084Const.CP_ID(0),
    itemLabel: t('label.level-of-care-required'),
    showItemLabel: false,
    hideDetails: true,
  } as Mo00039OnewayType,
})
/**************************************************
 * Pinia
 **************************************************/
const { refValue } = useScreenTwoWayBind<Or01084Type>({
  cpId: Or01084Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
})
watch(
  () => props.onewayModelValue.radioItemsList,
  () => {
    localOneway.or01084OneWay.radioItemsList = props.onewayModelValue.radioItemsList
  }
)
</script>

<template>
  <c-v-row
    no-gutters
    class="text-center"
  >
    <base-mo00039
      v-if="refValue"
      v-model="refValue!.letterPositionFlg"
      :oneway-model-value="localOneway.mo00039Oneway"
    >
      <base-at-radio
        v-for="item in localOneway.or01084OneWay.radioItemsList"
        :key="item.value"
        v-model="refValue!.letterPositionFlg"
        style="width: 140px"
        :name="item.label"
        :radio-label="item.label"
        :value="item.value"
      />
    </base-mo00039>
  </c-v-row>
</template>

<style scoped lang="scss">
.text-center {
  align-items: baseline;
}
</style>
