import type { Mo01274Type } from '~/types/business/components/Mo01274Type'
import type { Mo01282OnewayType, Mo01282Type } from '~/types/business/components/Mo01282Type'
import type { Mo01336OnewayType } from '~/types/business/components/Mo01336Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'

/**
 * Or54587:有機体:モニタリング設定マスタ モニタリング（上段）表
 * GUI01220 モニタリング設定マスタ
 *
 * <AUTHOR>
 */
/**
 * 双方向バインド用インタフェース
 */
export interface Or54587Type {
  /**
   * モニタリング項目情報リスト
   */
  itemInfoList: ItemInfo[]
}
/**
 * 単方向バインド用インタフェース
 */
export interface Or54587OnewayType {
  /**
   * タイトル
   */
  titleLable: Mo01338OnewayType
  /** 項目名活性フラグ */
  nameKnjDisabledFlg: boolean
  /** 入力値活性フラグ */
  inputKbnDisabledFlg: boolean
  /** 連動項目活性フラグ */
  rendouKbnDisabledFlg: boolean
  /**
   * 入力方法Oneway
   */
  inputKbnOneway: Mo01282OnewayType
  /**
   * 連動区分Oneway
   */
  rendouKbnOneway: Mo01282OnewayType
  /**
   * コンポネント固有Id
   */
  componentId: string
  /**
   * フォーカスのインデックス
   */
  focusIndex: string
  /**
   * エラー有無
   */
  hasError: boolean
  /**
   * 現在のマスタヘッダID
   */
  currentFree1Id: string
}

/**
 * 項目情報
 */
export interface ItemInfo {
  /**
   * 項目順
   */
  itemNum: Mo01337OnewayType
  /**
   * 項目名
   */
  nameKnj: Mo01274Type
  /**
   * 入力方法
   */
  inputKbn: Mo01282Type
  /**
   * 連動区分
   */
  rendouKbn: Mo01282Type
  /**
   * 文字数
   */
  widthCnt: Mo01336OnewayType
  /**
   * 更新区分
   */
  updateKbn: string
  /**
   * 更新回数
   */
  modifiedCnt: string
  /**
   * データ固有Id
   */
  id: string
}
