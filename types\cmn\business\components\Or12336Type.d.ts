
/**
 * 課題立案区分一覧
 */
export interface IssuesPlanningCategoryMasterInfoList {
  /**
   * 区分番号
   */
  kbnCd: string
  /**
   * 内容
   */
  textKnj: string
  /**
   * 入力ID
   */
  cf1Id: string
  /**
   * 区分フラグ
   */
  kbnFlg: string
  /**
   * 更新回数
   */
  modifiedCnt: string
  /**
   * 更新区分
   */
  updateKbn: string
}
/**
 * Or12336：有機体：課題立案区分一覧
 * 双方向バインドのインタフェース
 */
export interface Or12336Type {
  /**
   * 行削除ボタン活性Flg
   */
  delBtnDisabled: boolean
  /**
   * エラー行のフォーカス位置を確認する
   */
  focusIndex: string
  /**
   * エラー行のフォーカス位置を確認する 1:区分番号 2:内容
   */
  focusType: string
  /**
   * 課題立案区分一覧
   */
  issuesPlanningCategoryMasterInfoList: IssuesPlanningCategoryMasterInfoList[]
}
