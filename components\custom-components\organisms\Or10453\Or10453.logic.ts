import { Or10453Const } from './Or10453.constants'
import type { Or10453StateType } from './Or10453.type'
import { Or00094Const } from '~/components/base-components/organisms/Or00094/Or00094.constants'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'
import { Or00094Logic } from '~/components/base-components/organisms/Or00094/Or00094.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import { OrX0130Logic } from '~/components/custom-components/organisms/OrX0130/OrX0130.logic'
import { OrX0128Const } from '~/components/custom-components/organisms/OrX0128/OrX0128.constants'
import { OrX0128Logic } from '~/components/custom-components/organisms/OrX0128/OrX0128.logic'
import { OrX0145Const } from '~/components/custom-components/organisms/OrX0145/OrX0145.constants'
import { OrX0145Logic } from '~/components/custom-components/organisms/OrX0145/OrX0145.logic'

/**
 * Or10453:処理ロジック
 * GUI00938_印刷設定
 *
 * @description
 * OneWayBind領域用の構造
 *
 * <AUTHOR> DAO VAN DUONG
 */
export namespace Or10453Logic {
  /**
   * initialize
   *
   * @description
   * コンポーネントの初期化処理。
   * 共通関数を呼びpiniaの領域を初期化する。
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or10453Const.CP_ID(0),
      uniqueCpId,
      initTwoWayValue: {
        choPrtList: [],
      },
      childCps: [
        { cpId: Or00094Const.CP_ID(0) },
        { cpId: Or21813Const.CP_ID(0) },
        { cpId: Or21815Const.CP_ID(0) },
        { cpId: OrX0130Const.CP_ID(0) },
        { cpId: OrX0128Const.CP_ID(0) },
        { cpId: OrX0145Const.CP_ID(0) },
      ],
      editFlgNecessity: false,
    })

    // 子コンポーネントのセットアップ
    Or00094Logic.initialize(childCpIds[Or00094Const.CP_ID(0)].uniqueCpId)
    Or21813Logic.initialize(childCpIds[Or21813Const.CP_ID(0)].uniqueCpId)
    Or21815Logic.initialize(childCpIds[Or21815Const.CP_ID(0)].uniqueCpId)
    OrX0130Logic.initialize(childCpIds[OrX0130Const.CP_ID(0)].uniqueCpId)
    OrX0128Logic.initialize(childCpIds[OrX0128Const.CP_ID(0)].uniqueCpId)
    OrX0145Logic.initialize(childCpIds[OrX0145Const.CP_ID(0)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }

  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or10453StateType>(Or10453Const.CP_ID(0))
}
