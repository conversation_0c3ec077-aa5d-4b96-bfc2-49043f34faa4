<script setup lang="ts">
/**
 * Or32462:有機体:印刷設定
 * GUI00902_印刷設定
 *
 * @description
 * 印刷設定
 *
 * <AUTHOR>
 */
import { useI18n } from 'vue-i18n'
import { reactive, ref, onMounted, watch, computed } from 'vue'
import { Or32462Const } from './Or32462.constants'
import type { Or32462StateType } from './Or32462.type'
import { useSetupChildProps, useScreenOneWayBind, useNuxtApp, dateUtils } from '#imports'
import type { Mo00024Type, Mo00024OnewayType } from '~/types/business/components/Mo00024Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01338OnewayType } from '@/types/business/components/Mo01338Type'
import type { Mo01344OnewayType } from '@/types/business/components/Mo01344Type'
import type { Mo00039Items, Mo00039OnewayType } from '@/types/business/components/Mo00039Type'
import type { Mo00020Type, Mo00020OnewayType } from '@/types/business/components/Mo00020Type'
import type { Mo00018Type, Mo00018OnewayType } from '~/types/business/components/Mo00018Type'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { UserEntity } from '~/repositories/cmn/entities/AssessmentInterRAIPrintSettingsSelectEntity'
import type {
  IFreeAssessmentFacePrintSettingsInitUpdateInEntity,
  IFreeAssessmentFacePrintSettingsInitUpdateOutEntity,
} from '~/repositories/cmn/entities/FreeAssessmentFacePrintSettingsInitUpdateEntity'
import type {
  SysIniInfoEntity,
  IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity,
  IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateOutEntity,
} from '~/repositories/cmn/entities/FreeAssessmentFacePrintSettingsPrtNoChangeUpdateEntity'
import type {
  IFreeAssessmentFacePrintSettingsUpdateInEntity,
  IFreeAssessmentFacePrintSettingsUpdateOutEntity,
} from '~/repositories/cmn/entities/FreeAssessmentFacePrintSettingsUpdateEntity'
import type {
  Mo01334OnewayType,
  Mo01334Type,
  Mo01334Items,
} from '~/types/business/components/Mo01334Type'
import { OrX0117Const } from '~/components/custom-components/organisms/OrX0117/OrX0117.constants'
import { OrX0117Logic } from '~/components/custom-components/organisms/OrX0117/OrX0117.logic'
import type { OrX0117History, OrX0117OnewayType } from '~/types/cmn/business/components/OrX0117Type'
import type { Mo01337OnewayType } from '~/types/business/components/Mo01337Type'
import type { CustomClass } from '~/types/CustomClassType'
import { CmnMCdKbnId } from '~/constants/system-code-constants'
import { CmnSystemCodeRepository } from '~/repositories/cmn/CmnSystemCodeRepository'
import type { CodeType } from '~/repositories/business/core/v1/SystemCodeRepository'
import { OrX0128Const } from '~/components/custom-components/organisms/OrX0128/OrX0128.constants'
import { OrX0130Const } from '~/components/custom-components/organisms/OrX0130/OrX0130.constants'
import { OrX0130Logic } from '~/components/custom-components/organisms/OrX0130/OrX0130.logic'
import type { OrX0130OnewayType } from '~/types/cmn/business/components/OrX0130Type'
import type {
  OrX0128OnewayType,
  OrX0128Items,
  OrX0128Headers,
} from '~/types/cmn/business/components/OrX0128Type'
import { OrX0128Logic } from '~/components/custom-components/organisms/OrX0128/OrX0128.logic'
import type {
  Or32462MsgBtnType,
  Or32462Param,
} from '~/components/custom-components/organisms/Or32462/Or32462.type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type { Or21814OnewayType } from '~/components/base-components/organisms/Or21814/Or21814.type'
import { Or53608Const } from '~/components/custom-components/organisms/Or53608/Or53608.constants'
import { Or53608Logic } from '~/components/custom-components/organisms/Or53608/Or53608.logic'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import type { Or21813StateType } from '~/components/base-components/organisms/Or21813/Or21813.type'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'
import type { Or21815StateType } from '~/components/base-components/organisms/Or21815/Or21815.type'
import { useSystemCommonsStore } from '~/stores/session/systemCommons'
import { useReportUtils, reportOutputType } from '~/utils/useReportUtils'
import type {
  PrintSetEntity,
  PrintOptionEntity,
  PrintSubjectHistoryEntity,
  ChoPrtEntity,
  ICpnTucRaiAssReportSelectInEntity,
} from '~/repositories/cmn/entities/CpnTucRaiAssReportSelectEntity'
import type { IFreeAssessmentFacePrintSettingsMemoUpdateInEntity } from '~/repositories/cmn/entities/FreeAssessmentFacePrintSettingsMemoUpdateEntity'
import type {
  PeriodHistoryEntity,
  IFreeAssessmentFacePrintSettingsUserChangeSelectInEntity,
  IFreeAssessmentFacePrintSettingsUserChangeSelectOutEntity,
} from '~/repositories/cmn/entities/FreeAssessmentFacePrintSettingsUserChangeSelectEntity'
import type { DebugLogPluginInterface } from '~/plugins/debug-log'
import { useCmnCom } from '@/utils/useCmnCom'
import { useCmnRouteCom } from '~/utils/useCmnRouteCom'
import type {
  IFreeAssessmentFacePrintSettingsHistorySelectInEntity,
  IFreeAssessmentFacePrintSettingsHistorySelectOutEntity,
} from '~/repositories/cmn/entities/FreeAssessmentFacePrintSettingsHistorySelectEntity'
import type { Mo00045Type, Mo00045OnewayType } from '@/types/business/components/Mo00045Type'
import type { Mo00615OnewayType } from '~/types/business/components/Mo00615Type'
import { OrX0145Const } from '~/components/custom-components/organisms/OrX0145/OrX0145.constants'
import type { OrX0145OnewayType, OrX0145Type } from '~/types/cmn/business/components/OrX0145Type'
import type { TantoCmnShokuin } from '~/repositories/cmn/entities/TantoCmnShokuinSelectEntity'
const systemCommonsStore = useSystemCommonsStore()
const cmnRouteCom = useCmnRouteCom()
const { reportOutput } = useReportUtils()
const { convertDateToSeireki } = dateUtils()
const $log = useNuxtApp().$log as DebugLogPluginInterface

const { t } = useI18n()
/************************************************
 * Props
 ************************************************/
interface Props {
  uniqueCpId: string
}

const props = defineProps<Props>()

// 子コンポーネント用変数
const orX0117 = ref({ uniqueCpId: '' })
const orX0128 = ref({ uniqueCpId: '' })
const orX0130 = ref({ uniqueCpId: '' })
const or21813 = ref({ uniqueCpId: '' })
const or21814 = ref({ uniqueCpId: '' })
const or21815 = ref({ uniqueCpId: '' })
const or53608 = ref({ uniqueCpId: '' })
const orx0145 = ref({ uniqueCpId: '' })

const localOneway = reactive({
  /**
   * 設定ボタン
   */
  mo00611OneWaySetting: {
    btnLabel: t('btn.settings'),
    color: 'key',
    labelColor: 'key',
    disabled: false,
  } as Mo00611OnewayType,
  /**
   * 基本設定
   */
  mo01338OneWayBasicSettings: {
    value: t('label.basic-settings'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 氏名等を伏字にする
   */
  mo000018NameOneWay: {
    checkboxLabel: t('label.name-censored'),
  } as Mo00018OnewayType,
  /**
   * 帳票タイトル
   */
  mo00045OneWay: {
    itemLabel: t('label.title'),
    showItemLabel: true,
  } as Mo00045OnewayType,
  /**
   * 日付印刷区分
   */
  mo00039OneWay: {
    name: '',
    showItemLabel: false,
    inline: false,
  } as Mo00039OnewayType,
  /**
   * 指定日
   */
  mo00020OneWay: {
    showItemLabel: false,
    showSelectArrow: false,
    mo00009OnewayBack: {},
    mo00009OnewayForward: {},
  } as Mo00020OnewayType,
  /**
   * メモ欄の印刷
   */
  mo00039OneWayMemoColumnPrinter: {
    name: '',
    showItemLabel: false,
    inline: false,
    disabled: true,
    items: [
      {
        label: '表示',
        value: '1',
      },
      {
        label: '非表示',
        value: '2',
      },
      {
        label: '文章があれば表示',
        value: '3',
      },
    ],
  } as Mo00039OnewayType,
  /**
   * 印刷オプション
   */
  mo01338OneWayPrinterOption: {
    value: t('label.printer-option'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 空欄で印刷
   */
  mo000018OneWay: {
    checkboxLabel: t('label.blank-column'),
    disabled: true,
  } as Mo00018OnewayType,
  /**
   * メモ欄の印刷
   */
  mo01338OneWayMemoColumnPrinter: {
    value: t('label.memo-column-print'),
    valueFontWeight: 'bolder',
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 利用者選択ラベル
   */
  mo01338OneWayPrinterUserSelect: {
    value: t('label.printer-user-select'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 履歴選択ラベル
   */
  mo01338OneWayPrinterHistorySelect: {
    value: t('label.printer-history-select'),
    customClass: {
      outerClass: '',
      outerStyle: '',
      labelClass: '',
      labelStyle: 'display: none',
      itemClass: '',
      itemStyle: '',
    } as CustomClass,
  } as Mo01338OnewayType,
  /**
   * 基準日
   */
  mo00615OneWayType: {
    itemLabel: t('label.base-date'),
    showItemLabel: true,
  } as Mo00615OnewayType,
  /**
   * 利用者選択
   */
  mo00039OneWayUserSelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  /**
   * 履歴選択
   */
  mo00039OneWayHistorySelectType: {
    name: '',
    showItemLabel: false,
    inline: true,
  } as Mo00039OnewayType,
  /**
   * 基準日: 表用テキストフィールド
   */
  mo00020KijunbiOneWay: {
    showItemLabel: false,
    showSelectArrow: true,
    width: '132px',
    totalWidth: '220px',
    mo00009OnewayBack: {},
    mo00009OnewayForward: {},
  } as Mo00020OnewayType,
  /**
   * 担当ケアマネプルダウン
   */
  orX0145Oneway: {
    itemLabel: t('label.earrings'),
    isVerticalLabel: true,
    multiple: false,
    selectedUserCounter: '2',
  } as OrX0145OnewayType,
  /**
   * 閉じるボタン
   */
  mo00611OneWay: {
    btnLabel: t('btn.close'),
  } as Mo00611OnewayType,
  /**
   * PDFダウンロードボタン
   */
  mo00609Oneway: {
    btnLabel: t('btn.pdf-download'),
    disabled: false,
  } as Mo00609OnewayType,
})

/**
 * 親画面の情報
 */
const local = {
  /**
   * 親画面.セクション名
   */
  sectionName: Or32462Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.職員ID
   */
  shokuinId: Or32462Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.施設ID
   */
  shisetuId: Or32462Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.事業所ID
   */
  svJigyoId: Or32462Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.利用者ID
   */
  userId: Or32462Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.担当者ID
   */
  tantoId: Or32462Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.基準日
   */
  kijunbi: Or32462Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.履歴ID
   */
  historyId: Or32462Const.DEFAULT.STR.EMPTY,
  /**
   * 親画面.フォーカス設定用イニシャル
   */
  focusSettingInitial: [] as string[],
  /**
   * 親画面.初期選択状態の担当者カウンタ値
   */
  selectedUserCounter: Or32462Const.DEFAULT.STR.EMPTY,
  /**
   * 期間管理フラグ
   */
  kikanFlg: Or32462Const.DEFAULT.STR.EMPTY,
  /**
   * 個人情報使用フラグ
   */
  kojinhogoUsedFlg: Or32462Const.DEFAULT.KOJINHOGO_USED_FLG,
  /**
   * 個人情報番号
   */
  sectionAddNo: Or32462Const.DEFAULT.SECTION_ADD_NO,
  /**
   * 選択行のセクション番号
   */
  currentSectionNo: Or32462Const.DEFAULT.SECTION_NO,
  /**
   * 選択された帳票のプロファイル
   */
  profile: Or32462Const.DEFAULT.STR.EMPTY,
  /**
   * 選択利用者ID
   */
  selectUserId: Or32462Const.DEFAULT.STR.EMPTY,
  /**
   * 出力帳票名一覧に選択行番号
   */
  index: Or32462Const.DEFAULT.STR.EMPTY,
  /**
   * システムINI情報
   */
  sysIniInfo: {} as SysIniInfoEntity,
  /**
   * 履歴一覧が0件選択の場合（※履歴リストが0件、複数件を含む）
   */
  historyNoSelect: false,
  /**
   * 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
   */
  userNoSelect: false,
  /**
   * 帳票番号
   */
  prtNo: Or32462Const.DEFAULT.STR.EMPTY,
  /**
   * 選択利用者リスト
   */
  userList: [] as UserEntity[],
  /**
   * 帳票ID
   */
  reportId: Or32462Const.DEFAULT.STR.EMPTY,
  /**
   * 履歴選択の明細
   */
  orX0128DetList: [] as OrX0128Items[],
}

const localData: IFreeAssessmentFacePrintSettingsInitUpdateOutEntity = {
  data: {},
} as IFreeAssessmentFacePrintSettingsInitUpdateOutEntity
/**************************************************
 * 変数定義
 **************************************************/
// ダイアログ
const mo00024Oneway = ref<Mo00024OnewayType>({
  width: '1300px',
  persistent: true,
  showCloseBtn: true,
  mo01344Oneway: {
    name: 'Or32462',
    toolbarTitle: t('label.print-set'),
    toolbarName: 'Or32462ToolBar',
    toolbarTitleCenteredFlg: false,
    showCardActions: true,
    cardTextClass: 'or32462_content',
  } as Mo01344OnewayType,
})

const mo00024 = ref<Mo00024Type>({
  isOpen: Or32462Const.DEFAULT.IS_OPEN,
})

/**
 * 出力帳票名一覧
 */
const mo01334Oneway = ref<Mo01334OnewayType>({
  headers: [
    {
      title: t('label.ledger'),
      key: 'ledgerName',
      sortable: false,
      minWidth: '100',
    },
  ],
  items: [],
  height: 655,
})

/**
 * 出力帳票名一覧
 */
const mo01334Type = ref<Mo01334Type>({
  value: '',
  values: [],
} as Mo01334Type)

/**
 * 日付印刷区分
 */
const mo00039Type = ref<string>('')

/**
 * 指定日
 */
const mo00020Type = ref<Mo00020Type>({
  value: convertDateToSeireki(undefined),
} as Mo00020Type)

/**
 * 帳票タイトル
 */
const mo00045Type = ref<Mo00045Type>({
  value: Or32462Const.DEFAULT.STR.EMPTY,
} as Mo00045Type)

/**
 * 氏名等を伏字にする
 */
const nameMo00018Type = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 空欄で印刷
 */
const mo00018Type = ref<Mo00018Type>({
  modelValue: false,
} as Mo00018Type)

/**
 * 利用者・履歴選択表示OR非表示
 */
const userHistoryShow = ref<boolean>(true)

/**
 * 列の幅
 */
const centerCols = ref<number>(3)

/**
 * メモ欄の印刷タイプ
 */
const mo00039OneWayMemoColumnPrinterType = ref<string>(Or32462Const.DEFAULT.STR.ONE)

/**
 * 利用者選択
 */
const mo00039OneWayUserSelectType = ref<string>('')

/**
 * 履歴選択
 */
const mo00039OneWayHistorySelectType = ref<string>('')

/**
 * 当ケアマネプルダウン選択値
 */
const orX0145Type = ref<OrX0145Type>({
  value: {} as TantoCmnShokuin,
} as OrX0145Type)

/**
 * 基準日
 */
const mo00020TypeKijunbi = ref<Mo00020Type>({
  value: convertDateToSeireki(undefined),
} as Mo00020Type)

/**
 * 指定日非表示/表示フラゲ
 */
const mo00020Flag = ref<boolean>(false)
/**
 * 基準日フラゲ
 */
const kijunbiFlag = ref<boolean>(false)
/**
 * 履歴一覧セクションフラゲ
 */
const mo01334TypeHistoryFlag = ref<boolean>(false)

/**
 * 利用者列幅
 */
const userCols = ref<number>(6)

const orX0128OnewayModel = reactive<OrX0128OnewayType>({
  /**
   * 期間管理フラグ
   */
  kikanFlg: '1',
  /**
   * 単一複数フラグ(0:単一,1:複数)
   */
  singleFlg: OrX0128Const.DEFAULT.TANI,
  tableStyle: 'width:370px;height: 510px',
  headers: [
    { title: t('label.create-date'), key: 'shoriYmd', minWidth: '180px', sortable: false },
    { title: t('label.author'), key: 'shokuinKnj', minWidth: '180px', sortable: false },
  ] as OrX0128Headers[],
  items: [],
})

/**
 * 利用者
 */
const orX0130Oneway = reactive<OrX0130OnewayType>({
  /**
   * 選択モート
   */
  selectMode: OrX0130Const.DEFAULT.TANI,
  /**
   * テーブルのスタイル
   */
  tableStyle: 'width:330px',
  /**
   * 指定行選択
   */
  userId: Or32462Const.DEFAULT.STR.EMPTY,
  /**
   * 複数選択時、50音の選択数を表示するか
   */
  showKanaSelectionCount: true,
  /**
   * フォーカス設定用イニシャル
   */
  focusSettingInitial: [] as string[],
})

/**
 * 担当ケアマネ選択アイコン
 */
const tantoIconBtn = ref<boolean>(false)

/**
 * 担当ケアマネ表示ラベル
 */
const tantoLabel = ref<boolean>(false)

/**
 * 印刷設定帳票出力
 */
const orX0117Oneway: OrX0117OnewayType = {
  type: Or32462Const.DEFAULT.TANI,
  historyList: [] as OrX0117History[],
} as OrX0117OnewayType

/**
 * 初期情報取得フラゲ
 */
const initFlag = ref<boolean>(false)

/**
 * 期間管理フラグ
 */
const kikanFlag = ref<string>(Or32462Const.DEFAULT.STR.EMPTY)
/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or32462StateType>({
  cpId: Or32462Const.CP_ID(1),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or32462Const.DEFAULT.IS_OPEN
    },
    param: (value) => {
      if (value) {
        local.sectionName = value.sectionName
        local.shokuinId = value.shokuinId
        local.shisetuId = value.shisetuId
        local.svJigyoId = value.svJigyoId
        local.userId = value.userId
        local.tantoId = value.tantoId
        local.kijunbi = value.kijunbi
        local.historyId = value.historyId
        local.focusSettingInitial = value.focusSettingInitial
        local.selectedUserCounter = value.selectedUserCounter
      }
    },
  },
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [OrX0117Const.CP_ID(0)]: orX0117.value,
  [OrX0128Const.CP_ID(0)]: orX0128.value,
  [OrX0130Const.CP_ID(0)]: orX0130.value,
  [Or21813Const.CP_ID(0)]: or21813.value,
  [Or21814Const.CP_ID(0)]: or21814.value,
  [Or21815Const.CP_ID(0)]: or21815.value,
  [Or53608Const.CP_ID(0)]: or53608.value,
  [OrX0145Const.CP_ID(0)]: orx0145.value,
})

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 画面ID
const screenId = 'GUI00902'
// ルーティング
const routing = 'GUI00902/pinia'
// ケアマネ画面を初期化する
await useCmnCom().initialize({
  screenId,
  routing,
})

onMounted(async () => {
  // 汎用コード取得API実行
  await initCodes()

  // 初期情報取得
  await init()

  // 初期選択データ設定
  selectRowDataSetting()
})

// ダイアログ表示フラグ
const showDialogOrX0117 = computed(() => {
  // Or32462のダイアログ開閉状態
  return OrX0117Logic.state.get(orX0117.value.uniqueCpId)?.isOpen ?? false
})
// ダイアログ表示フラグ
const showDialogOr53608 = computed(() => {
  // Or53608のダイアログ開閉状態
  return Or53608Logic.state.get(or53608.value.uniqueCpId)?.isOpen ?? false
})

/**
 * 初期選択データ設定
 */
const selectRowDataSetting = () => {
  // フォーカス設定用イニシャル設定
  orX0130Oneway.focusSettingInitial = local.focusSettingInitial
  // 初期選択状態の担当者カウンタ値設定
  localOneway.orX0145Oneway.selectedUserCounter = local.selectedUserCounter
  // 親画面.利用者情報リスト件数>0
  if (mo01334Oneway.value.items.length > 0) {
    // 利用者一覧明細に親画面.利用者IDが存在する場合
    if (local.userId) {
      // 利用者IDを対するレコードを選択状態にする
      orX0130Oneway.userId = local.userId
    }
    // 利用者一覧明細に親画面.利用者IDが存在しない場合
    else {
      orX0130Oneway.userId = Or32462Const.DEFAULT.STR.EMPTY
    }
  }
  // 利用者一覧明細に親画面.利用者IDが存在しない場合
  else {
    orX0130Oneway.userId = Or32462Const.DEFAULT.STR.EMPTY
  }
}

/**
 * 汎用コード取得API実行
 */
const initCodes = async () => {
  // 汎用コード取得APIのinEntity生成
  const selectCodeKbnList = [
    // 日付印刷区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY },
    // 単複数選択区分
    { mCdKbnId: CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY },
  ]

  // 汎用コード取得API実行
  await CmnSystemCodeRepository.getCodes({ selectCodeKbnList })

  // 日付印刷区分
  const bizukePrintCategoryCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_BIZUKE_PRINT_CATEGORY
  )
  if (bizukePrintCategoryCodeTypes?.length > 0) {
    mo00039Type.value = bizukePrintCategoryCodeTypes[0].value
    const list: Mo00039Items[] = []
    for (const item of bizukePrintCategoryCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    localOneway.mo00039OneWay.items = list
  }

  // 利用者選択
  const tanMultipleSelectCategoryCodeTypes: CodeType[] = CmnSystemCodeRepository.filter(
    CmnMCdKbnId.M_CD_KBN_ID_TAN_MULTIPLE_SELECT_CATEGORY
  )
  if (tanMultipleSelectCategoryCodeTypes?.length > 0) {
    mo00039OneWayUserSelectType.value = tanMultipleSelectCategoryCodeTypes[0].value
    mo00039OneWayHistorySelectType.value = tanMultipleSelectCategoryCodeTypes[0].value
    const list: Mo00039Items[] = []
    for (const item of tanMultipleSelectCategoryCodeTypes) {
      if (item) {
        list.push({
          label: item.label,
          value: item.value,
        } as Mo00039Items)
      }
    }
    localOneway.mo00039OneWayHistorySelectType.items = list
    localOneway.mo00039OneWayUserSelectType.items = list
  }
}

/**
 * 初期プロジェクト設定
 */
const initSetting = () => {
  // 基準日
  mo00020TypeKijunbi.value.value = local.kijunbi
  // 氏名等を伏字にする
  nameMo00018Type.value.modelValue =
    localData.data.sysIniInfo.amikakeFlg === Or32462Const.DEFAULT.STR.ONE
  // 期間管理フラグ
  local.kikanFlg = localData?.data.kikanFlg

  // 親画面.基準日がないの場合
  if (!local.kijunbi) {
    // 担当ケアマネ選択
    tantoIconBtn.value = false
    // 担当ケアマネ表示
    tantoLabel.value = false
  } else {
    // 担当ケアマネ選択
    tantoIconBtn.value = true
    // 担当ケアマネ表示
    tantoLabel.value = true
  }

  // 担当ケアマネ選択アイコンボタン非活性/活性設定
  const kkjTantoFlg: string =
    cmnRouteCom.getInitialSettingMaster()?.kkjTantoFlg ?? Or32462Const.DEFAULT.STR.EMPTY
  // 共通情報.担当ケアマネ設定フラグ ＝ 1、且つ、親画面.担当者IDがあるの場合
  if (local.tantoId && parseInt(kkjTantoFlg) === Or32462Const.DEFAULT.NUMBER.ONE) {
    // 活性
    localOneway.orX0145Oneway.disabled = false
  }
  // 共通情報.担当ケアマネ設定フラグ ＝ 0、または、親画面.担当者IDがない場合
  else if (parseInt(kkjTantoFlg) === Or32462Const.DEFAULT.NUMBER.ZERO || !local.tantoId) {
    // 非活性
    localOneway.orX0145Oneway.disabled = false
  }
}

/**
 * 初期情報取得
 */
const init = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: IFreeAssessmentFacePrintSettingsInitUpdateInEntity = {
    sysRyaku: Or32462Const.DEFAULT.SYS_RYAKU,
    sectionName: local.sectionName,
    shokuId: local.shokuinId,
    houjinId: systemCommonsStore.getHoujinId,
    shisetuId: local.shisetuId,
    svJigyoId: local.svJigyoId,
    svJigyoIds: systemCommonsStore.getSvJigyoIdList,
    gsyscd: systemCommonsStore.getSystemCode,
    userId: local.userId,
    menu2Knj: Or32462Const.DEFAULT.MENU_2_KNJ,
    menu3Knj: Or32462Const.DEFAULT.MENU_3_KNJ,
    index: Or32462Const.DEFAULT.INDEX,
    kojinhogoUsedFlg: Or32462Const.DEFAULT.KOJINHOGO_USED_FLG,
    sectionAddNo: Or32462Const.DEFAULT.SECTION_ADD_NO,
  } as IFreeAssessmentFacePrintSettingsInitUpdateInEntity
  const resp: IFreeAssessmentFacePrintSettingsInitUpdateOutEntity = await ScreenRepository.update(
    'freeAssessmentFacePrintSettingsInitUpdate',
    inputData
  )
  if (resp?.data) {
    localData.data = { ...resp?.data }
    const prtList: Mo01334Items[] = []

    // 期間管理フラグ
    kikanFlag.value = localData.data.kikanFlg

    // 氏名等を伏字にする
    nameMo00018Type.value.modelValue = local.sysIniInfo.amikakeFlg === Or32462Const.DEFAULT.STR.ONE

    // 担当ケアマネ
    orX0145Type.value.value = {
      counter: Or32462Const.DEFAULT.STR.EMPTY,
      chkShokuId: Or32462Const.DEFAULT.STR.EMPTY,
      houjinId: Or32462Const.DEFAULT.STR.EMPTY,
      shisetuId: Or32462Const.DEFAULT.STR.EMPTY,
      svJigyoId: Or32462Const.DEFAULT.STR.EMPTY,
      shokuin1Kana: Or32462Const.DEFAULT.STR.EMPTY,
      shokuin2Kana: Or32462Const.DEFAULT.STR.EMPTY,
      shokuin1Knj: Or32462Const.DEFAULT.STR.EMPTY,
      shokuin2Knj: Or32462Const.DEFAULT.STR.EMPTY,
      sex: Or32462Const.DEFAULT.STR.EMPTY,
      birthdayYmd: Or32462Const.DEFAULT.STR.EMPTY,
      zip: Or32462Const.DEFAULT.STR.EMPTY,
      kencode: Or32462Const.DEFAULT.STR.EMPTY,
      citycode: Or32462Const.DEFAULT.STR.EMPTY,
      areacode: Or32462Const.DEFAULT.STR.EMPTY,
      addressKnj: Or32462Const.DEFAULT.STR.EMPTY,
      tel: Or32462Const.DEFAULT.STR.EMPTY,
      kaikeiId: Or32462Const.DEFAULT.STR.EMPTY,
      kyuyoKbn: Or32462Const.DEFAULT.STR.EMPTY,
      partKbn: Or32462Const.DEFAULT.STR.EMPTY,
      inYmd: Or32462Const.DEFAULT.STR.EMPTY,
      outYmd: Or32462Const.DEFAULT.STR.EMPTY,
      shozokuId: Or32462Const.DEFAULT.STR.EMPTY,
      shokushuId: Or32462Const.DEFAULT.STR.EMPTY,
      shokuId: Or32462Const.DEFAULT.STR.EMPTY,
      timeStmp: Or32462Const.DEFAULT.STR.EMPTY,
      delFlg: Or32462Const.DEFAULT.STR.EMPTY,
      shokuNumber: Or32462Const.DEFAULT.STR.EMPTY,
      caremanagerKbn: Or32462Const.DEFAULT.STR.EMPTY,
      shokuType1: Or32462Const.DEFAULT.STR.EMPTY,
      shokuType2: Or32462Const.DEFAULT.STR.EMPTY,
      kGroupid: Or32462Const.DEFAULT.STR.EMPTY,
      bmpPath: Or32462Const.DEFAULT.STR.EMPTY,
      bmpYmd: Or32462Const.DEFAULT.STR.EMPTY,
      hankoPath: Or32462Const.DEFAULT.STR.EMPTY,
      kojinPath: Or32462Const.DEFAULT.STR.EMPTY,
      keitaitel: Or32462Const.DEFAULT.STR.EMPTY,
      eMail: Or32462Const.DEFAULT.STR.EMPTY,
      senmonNo: Or32462Const.DEFAULT.STR.EMPTY,
      sgfFlg: Or32462Const.DEFAULT.STR.EMPTY,
      srvSekiKbn: Or32462Const.DEFAULT.STR.EMPTY,
      shokushuId2: Or32462Const.DEFAULT.STR.EMPTY,
      shokushuId3: Or32462Const.DEFAULT.STR.EMPTY,
      shokushuId4: Or32462Const.DEFAULT.STR.EMPTY,
      shokushuId5: Or32462Const.DEFAULT.STR.EMPTY,
      shikakuId1: Or32462Const.DEFAULT.STR.EMPTY,
      shikakuId2: Or32462Const.DEFAULT.STR.EMPTY,
      shikakuId3: Or32462Const.DEFAULT.STR.EMPTY,
      shikakuId4: Or32462Const.DEFAULT.STR.EMPTY,
      shikakuId5: Or32462Const.DEFAULT.STR.EMPTY,
      kyuseiFlg: Or32462Const.DEFAULT.STR.EMPTY,
      kyuseiKana: Or32462Const.DEFAULT.STR.EMPTY,
      kyuseiKnj: Or32462Const.DEFAULT.STR.EMPTY,
      sort: Or32462Const.DEFAULT.STR.EMPTY,
      selfNumber: Or32462Const.DEFAULT.STR.EMPTY,
      ichiranShokushuIdNm: Or32462Const.DEFAULT.STR.EMPTY,
      shokushuId2Nm: Or32462Const.DEFAULT.STR.EMPTY,
      shokushuId3Nm: Or32462Const.DEFAULT.STR.EMPTY,
      shokushuId4Nm: Or32462Const.DEFAULT.STR.EMPTY,
      shokushuId5Nm: Or32462Const.DEFAULT.STR.EMPTY,
      stopFlg: Or32462Const.DEFAULT.STR.EMPTY,
      shokuinKnj: localData.data.tantoKnj,
      shokuinKana: Or32462Const.DEFAULT.STR.EMPTY,
      title: Or32462Const.DEFAULT.STR.EMPTY,
      value: Or32462Const.DEFAULT.STR.EMPTY,
    } as TantoCmnShokuin

    for (const item of resp.data.prtList) {
      if (item) {
        prtList.push({
          id: item.sectionNo,
          mo01337OnewayLedgerName: {
            value: item.prtTitle,
            unit: Or32462Const.DEFAULT.STR.EMPTY,
          } as Mo01337OnewayType,
          prnDate: item.prnDate === Or32462Const.DEFAULT.STR.TRUE,
          selectable: true,
          profile: item.profile,
          index: item.index,
          prtNo: item.prtNo,
        } as Mo01334Items)
      }
    }
    mo01334Oneway.value.items = prtList
    initFlag.value = true

    // 印刷設定情報リストの一件目が選択状態
    if (mo01334Oneway.value.items.length > Or32462Const.DEFAULT.NUMBER.ONE) {
      mo01334Type.value.value = mo01334Oneway.value.items[Or32462Const.DEFAULT.NUMBER.ONE].id
    }
    await outputLedgerName(local.prtNo)

    initSetting()
  }
}

/**
 * 画面印刷設定内容を保存
 */
const save = async (): Promise<IFreeAssessmentFacePrintSettingsUpdateOutEntity> => {
  // バックエンドAPIから印刷設定情報保存
  const inputData: IFreeAssessmentFacePrintSettingsUpdateInEntity = {
    sysRyaku: Or32462Const.DEFAULT.SYS_RYAKU,
    sectionName: local.sectionName,
    gsyscd: systemCommonsStore.getSystemCode,
    shokuId: local.shokuinId,
    houjinId: systemCommonsStore.getHoujinId,
    shisetuId: local.shisetuId,
    svJigyoId: local.svJigyoId,
    index: local.index,
    sysIniInfo: local.sysIniInfo,
    prtList: localData.data.prtList,
  } as IFreeAssessmentFacePrintSettingsUpdateInEntity

  const resp: IFreeAssessmentFacePrintSettingsUpdateOutEntity = await ScreenRepository.update(
    'freeAssessmentFacePrintSettingsUpdate',
    inputData
  )
  return resp
}

/**
 * 「閉じるボタン」押下
 */
const close = async () => {
  await save()
  setState({
    isOpen: false,
    param: {} as Or32462Param,
  })
}

/**
 * 「PDFダウンロード」ボタン押下
 */
const pdfDownload = async () => {
  // 選択された帳票のプロファイルが””の場合
  if (!local.profile) {
    // メッセージを表示
    const dialogResult = await openErrorDialog(or21814.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.error'),
      // ダイアログテキスト
      dialogText: t('message.e-cmn-40172'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    })
    // はい
    if (dialogResult === Or32462Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
      // 処理終了
      return
    }
  }

  // 画面.空欄で印刷チェックボックスがオフの場合
  if (!mo00018Type.value.modelValue) {
    // 利用者一覧明細にデータを選択しない
    if (local.userNoSelect) {
      // メッセージを表示
      const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
        // ダイアログタイトル
        dialogTitle: t('label.confirm'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-11393'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      })
      // はい
      if (dialogResult === Or32462Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
        // 処理終了
        return
      }
    }

    // 履歴一覧明細にデータを選択しない
    if (local.historyNoSelect) {
      // メッセージを表示
      const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
        // ダイアログタイトル
        dialogTitle: t('label.confirm'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-11455'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      })
      // はい
      if (dialogResult === Or32462Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
        // 処理終了
        return
      }
    }
  }

  // AC002-1と同じ => 印刷設定情報を保存する
  await save()

  // 利用者選択方法が「単一」
  if (Or32462Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
    // 履歴選択が「単一」
    if (Or32462Const.DEFAULT.TANI === mo00039OneWayHistorySelectType.value) {
      // 印刷ダイアログ画面を開かずに、帳票側の処理を呼び出す
      await reportOutputPdf()
      return
    }
    // 履歴選択方法が「複数」
    else if (Or32462Const.DEFAULT.HUKUSUU === mo00039OneWayHistorySelectType.value) {
      // 履歴情報リストにデータを選択する場合
      if (!local.historyNoSelect) {
        // 印刷設定情報リストを作成
        createReportOutputData(local.currentSectionNo)
      }
    }
  }

  // 利用者選択方法が「複数」の場合
  if (Or32462Const.DEFAULT.HUKUSUU === mo00039OneWayUserSelectType.value) {
    // 利用者一覧が0件選択の場合（※利用者リストが0件、複数件を含む）
    if (local.userNoSelect) {
      // メッセージを表示
      const dialogResult = await openConfirmDialog(or21814.value.uniqueCpId, {
        // ダイアログタイトル
        dialogTitle: t('label.confirm'),
        // ダイアログテキスト
        dialogText: t('message.i-cmn-11393'),
        firstBtnType: 'normal1',
        firstBtnLabel: t('btn.yes'),
        secondBtnType: 'blank',
        thirdBtnType: 'blank',
      })
      // はい
      if (dialogResult === Or32462Const.DEFAULT.MESSAGE_BTN_TYPE_YES) {
        // 処理終了
        return
      }
    }
    // 利用者情報リストにデータを選択する場合
    else {
      // 印刷設定情報リストを作成
      await PrintSettingsHistorySelect()
    }
  }

  // 「AC012-6」または「AC012-7-2」で取得した印刷用情報リスト＞0件の場合
  if (orX0117Oneway.historyList.length > 0) {
    // OrX0117のダイアログ開閉状態を更新する
    OrX0117Logic.state.set({
      uniqueCpId: orX0117.value.uniqueCpId,
      state: {
        isOpen: true,
      },
    })
  }
}

/**
 * 印刷設定情報リストを再取得(利用者選択が「複数」)
 */
const PrintSettingsHistorySelect = async () => {
  // 印刷設定情報リストを再取得
  const inputData: IFreeAssessmentFacePrintSettingsHistorySelectInEntity = {
    userlist: local.userList,
    svJigyoIds: systemCommonsStore.getSvJigyoIdList,
    kijunbiYmd: mo00020TypeKijunbi.value.value,
  } as IFreeAssessmentFacePrintSettingsHistorySelectInEntity
  const resp: IFreeAssessmentFacePrintSettingsHistorySelectOutEntity =
    await ScreenRepository.select('freeAssessmentFacePrintSettingsHistorySelect', inputData)

  const list: OrX0117History[] = []
  if (resp.data) {
    for (const data of resp.data.historyList) {
      if (data) {
        // TODO 印刷設定情報リストを作成
        const reportData: ICpnTucRaiAssReportSelectInEntity = {
          svJigyoKnj: '',
          syscd: systemCommonsStore.getSystemCode,
          printSet: {
            shiTeiKubun: mo00039Type.value,
            shiTeiDate: mo00020Type.value.value ? mo00020Type.value.value.split('/').join('-') : '',
          } as PrintSetEntity,
          printOption: {
            emptyFlg: String(mo00018Type.value.modelValue),
            kinyuAssType: '',
            colorFlg: '',
          } as PrintOptionEntity,
          printSubjectHistoryList: [] as PrintSubjectHistoryEntity[],
        } as ICpnTucRaiAssReportSelectInEntity

        const choPrtList: ChoPrtEntity[] = []
        for (const item of localData.data.prtList) {
          if (item) {
            //  単一帳票
            if (local.prtNo === item.prtNo) {
              choPrtList.push({
                shokuId: systemCommonsStore.getStaffId,
                sysRyaku: systemCommonsStore.getSystemAbbreviation,
                section: local.sectionName,
                prtNo: item.prtNo,
                choPro: item.profile,
                sectionName: item.defPrtTitle,
                dwobject: item.dwobject,
                prtOrient: item.prtOrient,
                prtSize: item.prtSize,
                listTitle: item.listTitle,
                prtTitle: item.prtTitle,
                mTop: item.mtop,
                mBottom: item.mbottom,
                mLeft: item.mleft,
                mRight: item.mright,
                ruler: item.ruler,
                prndate: item.prnDate,
                prnshoku: item.prnshoku,
                serialFlg: item.serialFlg,
                modFlg: item.modFlg,
                secFlg: item.secFlg,
                param01: item.param01,
                param02: item.param02,
                param03: item.param03,
                param04: item.param04,
                param05: item.param05,
                param06: item.param06,
                param07: item.param07,
                param08: item.param08,
                param09: item.param09,
                param10: item.param10,
                serialHeight: item.serialHeight,
                serialPagelen: item.serialPagelen,
                hsjId: systemCommonsStore.getHoujinId,
                param11: item.param11,
                param12: item.param12,
                param13: item.param13,
                param14: item.param14,
                param15: item.param15,
                param16: item.param16,
                param17: item.param17,
                param18: item.param18,
                param19: item.param19,
                param20: item.param20,
                param21: item.param21,
                param22: item.param22,
                param23: item.param23,
                param24: item.param24,
                param25: item.param25,
                param26: item.param26,
                param27: item.param28,
                param28: item.param28,
                param29: item.param29,
                param30: item.param30,
                param31: item.param31,
                param32: item.param32,
                param33: item.param33,
                param34: item.param34,
                param35: item.param35,
                param36: item.param36,
                param37: item.param37,
                param38: item.param38,
                param39: item.param39,
                param40: item.param40,
                param41: item.param41,
                param42: item.param42,
                param43: item.param43,
                param44: item.param44,
                param45: item.param45,
                param46: item.param46,
                param47: item.param47,
                param48: item.param48,
                param49: item.param49,
                param50: item.param50,
                houjinId: systemCommonsStore.getHoujinId,
                shisetuId: systemCommonsStore.getShisetuId,
                svJigyoId: systemCommonsStore.getSvJigyoId,
                zoomRate: item.zoomRate,
                modifiedCnt: item.modifiedCnt,
              } as ChoPrtEntity)
            }
          }
        }
        reportData.printSubjectHistoryList.push({
          userId: data.userId,
          userName: data.userName,
          sc1Id: '',
          startYmd: '',
          endYmd: '',
          raiId: '',
          assType: '',
          assDateYmd: '',
          assShokuId: '',
          result: data.result,
          choPrtList: choPrtList,
        } as PrintSubjectHistoryEntity)
        list.push({
          reportId: local.reportId,
          outputType: reportOutputType.DOWNLOAD,
          reportData: reportData,
          userName: data.userName,
          historyDate: '',
          result: data.result,
        } as OrX0117History)
      }
    }
  }
  orX0117Oneway.historyList = list
}

/**
 * 印刷ダイアログ画面を開
 */
const reportOutputPdf = async () => {
  try {
    // API処理失敗時のシステムエラーダイアログを非表示にする
    systemCommonsStore.setSystemErrorDialogType('hide')

    // TODO 帳票API設計が未完了
    const reportData: ICpnTucRaiAssReportSelectInEntity = {
      svJigyoKnj: '',
      syscd: systemCommonsStore.getSystemCode,
      printSet: {
        shiTeiKubun: mo00039Type.value,
        shiTeiDate: mo00020Type.value.value,
      } as PrintSetEntity,
      printOption: {
        emptyFlg: '',
        kinyuAssType: mo00039OneWayMemoColumnPrinterType.value,
        colorFlg: '',
      } as PrintOptionEntity,
      printSubjectHistoryList: [] as PrintSubjectHistoryEntity[],
    } as ICpnTucRaiAssReportSelectInEntity

    const choPrtList: ChoPrtEntity[] = []
    for (const item of localData.data.prtList) {
      if (item) {
        //  単一帳票
        if (local.currentSectionNo === item.sectionNo) {
          choPrtList.push({
            shokuId: systemCommonsStore.getStaffId,
            sysRyaku: systemCommonsStore.getSystemAbbreviation,
            section: local.sectionName,
            prtNo: item.prtNo,
            choPro: item.profile,
            sectionName: item.defPrtTitle,
            dwobject: item.dwobject,
            prtOrient: item.prtOrient,
            prtSize: item.prtSize,
            listTitle: item.listTitle,
            prtTitle: item.prtTitle,
            mTop: item.mtop,
            mBottom: item.mbottom,
            mLeft: item.mleft,
            mRight: item.mright,
            ruler: item.ruler,
            prndate: item.prnDate,
            prnshoku: item.prnshoku,
            serialFlg: item.serialFlg,
            modFlg: item.modFlg,
            secFlg: item.secFlg,
            param01: item.param01,
            param02: item.param02,
            param03: item.param03,
            param04: item.param04,
            param05: item.param05,
            param06: item.param06,
            param07: item.param07,
            param08: item.param08,
            param09: item.param09,
            param10: item.param10,
            serialHeight: item.serialHeight,
            serialPagelen: item.serialPagelen,
            hsjId: systemCommonsStore.getHoujinId,
            param11: item.param11,
            param12: item.param12,
            param13: item.param13,
            param14: item.param14,
            param15: item.param15,
            param16: item.param16,
            param17: item.param17,
            param18: item.param18,
            param19: item.param19,
            param20: item.param20,
            param21: item.param21,
            param22: item.param22,
            param23: item.param23,
            param24: item.param24,
            param25: item.param25,
            param26: item.param26,
            param27: item.param28,
            param28: item.param28,
            param29: item.param29,
            param30: item.param30,
            param31: item.param31,
            param32: item.param32,
            param33: item.param33,
            param34: item.param34,
            param35: item.param35,
            param36: item.param36,
            param37: item.param37,
            param38: item.param38,
            param39: item.param39,
            param40: item.param40,
            param41: item.param41,
            param42: item.param42,
            param43: item.param43,
            param44: item.param44,
            param45: item.param45,
            param46: item.param46,
            param47: item.param47,
            param48: item.param48,
            param49: item.param49,
            param50: item.param50,
            houjinId: systemCommonsStore.getHoujinId,
            shisetuId: systemCommonsStore.getShisetuId,
            svJigyoId: systemCommonsStore.getSvJigyoId,
            zoomRate: item.zoomRate,
            modifiedCnt: item.modifiedCnt,
          } as ChoPrtEntity)
        }
      }
    }

    // TODO 印刷設定情報リストパラメータを作成
    reportData.printSubjectHistoryList.push({
      userId: local.userList.length > 0 ? local.userList[0].userId : Or32462Const.DEFAULT.STR.EMPTY,
      userName:
        local.userList.length > 0 ? local.userList[0].userName : Or32462Const.DEFAULT.STR.EMPTY,
      sc1Id: '',
      startYmd: '',
      endYmd: '',
      raiId: '',
      assType: Or32462Const.DEFAULT.STR.EMPTY,
      assDateYmd: Or32462Const.DEFAULT.STR.EMPTY,
      assShokuId: '',
      result: Or32462Const.DEFAULT.STR.EMPTY,
      choPrtList: choPrtList,
    } as PrintSubjectHistoryEntity)
    // 帳票出力
    await reportOutput(local.reportId, reportData, reportOutputType.DOWNLOAD)
  } catch (e) {
    $log.debug('帳票の出力に失敗しました。', local.reportId, reportOutputType.DOWNLOAD, e)
  } finally {
    // API処理失敗時のシステムエラーダイアログの表示タイプをデフォルトに戻す
    systemCommonsStore.setSystemErrorDialogType('details')
  }
}

/**
 * 切替前の印刷設定を保存する
 *
 * @param selectId - 出力帳票ID
 */
const setBeforChangePrintData = (selectId: string) => {
  if (selectId) {
    for (const item of localData.data.prtList) {
      if (item) {
        if (selectId === item.prtNo) {
          // 日付表示有無
          item.prnDate = mo00039Type.value
          break
        }
      }
    }
  }
}

/**
 * 切替後の印刷設定を画面に設定する
 *
 * @param selectId - 出力帳票ID
 */
const setAfterChangePrintData = (selectId: string) => {
  if (selectId) {
    for (const item of localData.data.prtList) {
      if (item) {
        if (selectId === item.prtNo) {
          // 日付表示有無
          mo00039Type.value = item.prnDate
          break
        }
      }
    }
  }
}

/**
 * 「出力帳票名」選択
 *
 * @param selectId - 出力帳票ID
 */
const outputLedgerName = async (selectId: string) => {
  if (!selectId) {
    selectId = Or32462Const.DEFAULT.SECTION_NO
  }
  let label = Or32462Const.DEFAULT.STR.EMPTY
  for (const item of mo01334Oneway.value.items) {
    if (item) {
      if (selectId === item.id && item.mo01337OnewayLedgerName) {
        const data = item.mo01337OnewayLedgerName as Mo01337OnewayType
        label = data.value
        mo01334Type.value.value = item.id

        // プロファイル
        local.profile = item.profile as string

        // 出力帳票名一覧に選択行番号
        local.index = item.index as string

        // 帳票番号
        local.prtNo = item.prtNo as string

        // 帳票ID
        setReportId(item.id)
        break
      }
    }
  }
  mo00045Type.value.value = label

  local.currentSectionNo = selectId

  // 日付印刷区分設定
  for (const item of localData.data.prtList) {
    if (item) {
      if (item.sectionNo === selectId) {
        mo00039Type.value = item.prnDate
      }
    }
  }

  // 帳票イニシャライズデータを取得する
  await getSectionInitializeData()
}

/**
 * 帳票ID設定
 *
 * @param sectionNo - 帳票番号
 */
const setReportId = (sectionNo: string) => {
  switch (sectionNo) {
    case Or32462Const.DEFAULT.STR.ONE:
      local.reportId = Or32462Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.TYPE1
      break
    case Or32462Const.DEFAULT.STR.TWO:
      local.reportId = Or32462Const.DEFAULT.PDF_DOWNLOAD_REPORT_ID.TYPE2
      break
    default:
      local.reportId = Or32462Const.DEFAULT.STR.EMPTY
      break
  }
}

/**
 * 印刷設定情報リストを作成
 *
 * @param sectionNo - 帳票番号
 */
const createReportOutputData = (sectionNo: string) => {
  const list: OrX0117History[] = []
  for (const orX0128DetData of local.orX0128DetList) {
    // TODO 帳票API設計が未完了
    const reportData: ICpnTucRaiAssReportSelectInEntity = {
      svJigyoKnj: '',
      syscd: systemCommonsStore.getSystemCode,
      printSet: {
        shiTeiKubun: mo00039Type.value,
        shiTeiDate: mo00020Type.value.value,
      } as PrintSetEntity,
      printOption: {
        emptyFlg: '',
        kinyuAssType: mo00039OneWayMemoColumnPrinterType.value,
        colorFlg: '',
      } as PrintOptionEntity,
      printSubjectHistoryList: [] as PrintSubjectHistoryEntity[],
    } as ICpnTucRaiAssReportSelectInEntity

    const choPrtList: ChoPrtEntity[] = []
    for (const item of localData.data.prtList) {
      if (item) {
        //  単一帳票
        if (sectionNo === item.sectionNo) {
          choPrtList.push({
            shokuId: systemCommonsStore.getStaffId,
            sysRyaku: systemCommonsStore.getSystemAbbreviation,
            section: local.sectionName,
            prtNo: item.prtNo,
            choPro: item.profile,
            sectionName: item.defPrtTitle,
            dwobject: item.dwobject,
            prtOrient: item.prtOrient,
            prtSize: item.prtSize,
            listTitle: item.listTitle,
            prtTitle: item.prtTitle,
            mTop: item.mtop,
            mBottom: item.mbottom,
            mLeft: item.mleft,
            mRight: item.mright,
            ruler: item.ruler,
            prndate: item.prnDate,
            prnshoku: item.prnshoku,
            serialFlg: item.serialFlg,
            modFlg: item.modFlg,
            secFlg: item.secFlg,
            param01: item.param01,
            param02: item.param02,
            param03: item.param03,
            param04: item.param04,
            param05: item.param05,
            param06: item.param06,
            param07: item.param07,
            param08: item.param08,
            param09: item.param09,
            param10: item.param10,
            serialHeight: item.serialHeight,
            serialPagelen: item.serialPagelen,
            hsjId: systemCommonsStore.getHoujinId,
            param11: item.param11,
            param12: item.param12,
            param13: item.param13,
            param14: item.param14,
            param15: item.param15,
            param16: item.param16,
            param17: item.param17,
            param18: item.param18,
            param19: item.param19,
            param20: item.param20,
            param21: item.param21,
            param22: item.param22,
            param23: item.param23,
            param24: item.param24,
            param25: item.param25,
            param26: item.param26,
            param27: item.param28,
            param28: item.param28,
            param29: item.param29,
            param30: item.param30,
            param31: item.param31,
            param32: item.param32,
            param33: item.param33,
            param34: item.param34,
            param35: item.param35,
            param36: item.param36,
            param37: item.param37,
            param38: item.param38,
            param39: item.param39,
            param40: item.param40,
            param41: item.param41,
            param42: item.param42,
            param43: item.param43,
            param44: item.param44,
            param45: item.param45,
            param46: item.param46,
            param47: item.param47,
            param48: item.param48,
            param49: item.param49,
            param50: item.param50,
            houjinId: systemCommonsStore.getHoujinId,
            shisetuId: systemCommonsStore.getShisetuId,
            svJigyoId: systemCommonsStore.getSvJigyoId,
            zoomRate: item.zoomRate,
            modifiedCnt: item.modifiedCnt,
          } as ChoPrtEntity)
        }
      }
    }

    // TODO 印刷設定情報リストパラメータを作成
    reportData.printSubjectHistoryList.push({
      userId: local.userList.length > 0 ? local.userList[0].userId : Or32462Const.DEFAULT.STR.EMPTY,
      userName:
        local.userList.length > 0 ? local.userList[0].userName : Or32462Const.DEFAULT.STR.EMPTY,
      sc1Id: '',
      startYmd: '',
      endYmd: '',
      raiId: '',
      assType: orX0128DetData.assType as string,
      assDateYmd: orX0128DetData.assDateYmd as string,
      assShokuId: Or32462Const.DEFAULT.STR.EMPTY,
      result: Or32462Const.DEFAULT.STR.EMPTY,
      choPrtList: choPrtList,
    } as PrintSubjectHistoryEntity)
    list.push({
      reportId: local.reportId,
      outputType: reportOutputType.DOWNLOAD,
      reportData: reportData,
      userName:
        local.userList.length > 0 ? local.userList[0].userName : Or32462Const.DEFAULT.STR.EMPTY,
      historyDate: orX0128DetData.shoriYmd as string,
      result: Or32462Const.DEFAULT.STR.EMPTY,
    } as OrX0117History)
  }
  orX0117Oneway.historyList = list
}

/**
 * 帳票イニシャライズデータを取得する
 */
const getSectionInitializeData = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity = {
    profile: local.profile,
    gsyscd: systemCommonsStore.getSystemCode,
    shokuId: local.shokuinId,
    kojinhogoUsedFlg: Or32462Const.DEFAULT.KOJINHOGO_USED_FLG,
    sectionAddNo: Or32462Const.DEFAULT.SECTION_ADD_NO,
  } as IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateInEntity
  const resp: IFreeAssessmentFacePrintSettingsPrtNoChangeUpdateOutEntity =
    await ScreenRepository.update('freeAssessmentFacePrintSettingsPrtNoChangeUpdate', inputData)
  if (resp.data) {
    local.sysIniInfo = resp.data.sysIniInfo

    // 氏名等を伏字にする
    nameMo00018Type.value.modelValue = local.sysIniInfo.amikakeFlg === Or32462Const.DEFAULT.STR.ONE
  }
}

/**
 * 画面ボタン活性非活性設定
 */
const btnItemSetting = () => {
  // 利用者選択方法が「単一」の場合
  if (Or32462Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
    // 基準日を非表示にする
    // 履歴選択を活性表示にする
    kijunbiFlag.value = false
  }
  // 以外の場合
  else {
    // 基準日を活性表示にする
    // 履歴選択を非表示にする
    kijunbiFlag.value = true

    localOneway.mo00039OneWayMemoColumnPrinter.disabled = true
  }

  // 履歴一覧セクション
  if (Or32462Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
    userCols.value = 6
    mo01334TypeHistoryFlag.value = true
  } else {
    userCols.value = 12
    mo01334TypeHistoryFlag.value = false
  }
}

/**
 * 「設定」ボタン押下
 */
const settingBtnClick = () => {
  // GUI00901_フェースシート 印刷設定画面をポップアップで起動する
  Or53608Logic.state.set({
    uniqueCpId: or53608.value.uniqueCpId,
    state: { isOpen: true },
  })
}

/**
 * ・選択した利用者の期間履歴情報リストを取得
 */
const PrintSettingsUserChangeSelect = async () => {
  // バックエンドAPIから初期情報取得
  const inputData: IFreeAssessmentFacePrintSettingsUserChangeSelectInEntity = {
    svJigyoIds: systemCommonsStore.getSvJigyoIdList,
    userId: local.userId,
    kikanFlg: local.kikanFlg,
  } as IFreeAssessmentFacePrintSettingsUserChangeSelectInEntity

  const resp: IFreeAssessmentFacePrintSettingsUserChangeSelectOutEntity =
    await ScreenRepository.select('freeAssessmentFacePrintSettingsUserChangeSelect', inputData)
  if (resp.data) {
    // アセスメント履歴一覧データ
    getHistoryData(resp.data.periodHistoryList)
  }
}

/**
 * アセスメント履歴一覧データ
 *
 * @param periodHistoryList - アセスメント履歴リスト
 */
const getHistoryData = (periodHistoryList: PeriodHistoryEntity[]) => {
  if (Or32462Const.DEFAULT.KIKAN_FLG_1 === kikanFlag.value) {
    const tempList: string[] = [] as string[]
    let list: OrX0128Items[] = []
    for (const item of periodHistoryList) {
      if (item) {
        const planPeriod =
          t('label.plan-period') +
          Or32462Const.DEFAULT.STR.SPLIT_COLON +
          item.startYmd +
          Or32462Const.DEFAULT.STR.SPLIT_TILDE +
          item.endYmd
        if (!tempList.includes(planPeriod)) {
          const historyList: OrX0128Items[] = []
          for (const data of periodHistoryList) {
            const dataPlanPeriod =
              t('label.plan-period') +
              Or32462Const.DEFAULT.STR.SPLIT_COLON +
              data.startYmd +
              Or32462Const.DEFAULT.STR.SPLIT_TILDE +
              data.endYmd
            if (planPeriod === dataPlanPeriod) {
              historyList.push({
                id: data.sc1Id,
                sc1Id: data.sc1Id,
                startYmd: data.startYmd,
                endYmd: data.endYmd,
                sel: data.sel,
                savSeq: data.savSeq,
                shoriYmd: data.shoriYmd,
                shokuinKnj: data.shokuinKnj,
              } as OrX0128Items)
            }
          }
          if (historyList.length > 0) {
            list.push({
              sc1Id: item.sc1Id,
              startYmd: item.startYmd,
              endYmd: item.endYmd,
              isPeriodManagementMergedRow: true,
              planPeriod:
                t('label.plan-period') +
                Or32462Const.DEFAULT.STR.SPLIT_COLON +
                item.startYmd +
                Or32462Const.DEFAULT.STR.SPLIT_TILDE +
                item.endYmd,
              id: Or32462Const.DEFAULT.STR.EMPTY,
            } as OrX0128Items)
            list = list.concat(historyList)
            tempList.push(planPeriod)
          }
        }
      }
    }
    list.forEach((item, index) => {
      item.id = String(++index)
    })
    orX0128OnewayModel.items = list
    // 画面.利用者一覧明細に親画面.履歴IDが存在する場合
    if (local.historyId) {
      // 親画面.アセスメントIDを対するレコードを選択状態にする
      orX0128OnewayModel.initSelectId = (
        list.findIndex((item) => item.raiId === local.historyId) + 1
      ).toString()
    }
  } else {
    const list: OrX0128Items[] = []
    for (const data of periodHistoryList) {
      if (data) {
        list.push({
          id: data.sc1Id,
          sc1Id: data.sc1Id,
          startYmd: data.startYmd,
          endYmd: data.endYmd,
          sel: data.sel,
          savSeq: data.savSeq,
          shoriYmd: data.shoriYmd,
          shokuinKnj: data.shokuinKnj,
        } as OrX0128Items)
      }
    }
    orX0128OnewayModel.items = list
    // 履歴一覧明細に親画面.履歴IDが存在する場合
    if (local.historyId) {
      // 画面.履歴一覧明細に親画面.履歴IDを対するレコードを選択状態にする
      orX0128OnewayModel.initSelectId = (
        list.findIndex((item) => item.raiId === local.historyId) + 1
      ).toString()
    }
  }
}

/**
 * 帳票タイトル入力変更時
 *
 * @param param - 入力値
 */
const inputChange = async (param: Mo00045Type) => {
  if (!param.value) {
    // メッセージを表示
    await openWarnDialog(or21815.value.uniqueCpId, {
      // ダイアログタイトル
      dialogTitle: t('label.warning'),
      // ダイアログテキスト
      dialogText: t('message.w-cmn-20845'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    } as Or21815StateType)
  }
}

/**
 * 担当ケアマネプルダウン
 *
 * @param result - 戻り値
 */
const orx0145UpdateModelValue = (result: OrX0145Type) => {
  if (result) {
    if (result.value) {
      if (!Array.isArray(result.value) && 'chkShokuId' in result.value) {
        // TODO API疎通時に確認
        orX0130Oneway.tantouCareManager = result.value.chkShokuId
      }
    }

    // 画面.利用者選択が単一の場合
    if(Or32462Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
      // 画面.利用者一覧明細の1件目レコードを選択状態にする
      orX0130Oneway.userId = Or32462Const.DEFAULT.STR.EMPTY
    }
  }
}

/**
 * 確認ダイアログ表示
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openConfirmDialog = async (
  uniqueCpId: string,
  state: Or21814OnewayType
): Promise<Or32462MsgBtnType> => {
  // 確認ダイアログを開く
  Or21814Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })

  // 確認ダイアログを閉じたタイミングで結果を返却
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814.value.uniqueCpId)

        let result: Or32462MsgBtnType = Or32462Const.DEFAULT.STR.YES

        if (event?.firstBtnClickFlg) {
          result = Or32462Const.DEFAULT.STR.YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or32462Const.DEFAULT.STR.YES
        }
        if (event?.thirdBtnClickFlg) {
          result = Or32462Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL
        }
        if (event?.closeBtnClickFlg) {
          result = Or32462Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * エラーダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - エラーダイアログOneWayBind領域用の構造
 */
const openErrorDialog = async (
  uniqueCpId: string,
  state: Or21813StateType
): Promise<Or32462MsgBtnType> => {
  Or21813Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21813Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21813Logic.event.get(uniqueCpId)

        let result = Or32462Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL as Or32462MsgBtnType

        if (event?.firstBtnClickFlg) {
          result = Or32462Const.DEFAULT.MESSAGE_BTN_TYPE_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or32462Const.DEFAULT.MESSAGE_BTN_TYPE_NO
        }
        if (event?.thirdBtnClickFlg) {
          result = Or32462Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL
        }
        if (event?.closeBtnClickFlg) {
          result = Or32462Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL
        }

        // エラーダイアログのフラグをOFF
        Or21813Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 警告ーダイアログをオープンする
 *
 * @param uniqueCpId - ユニークコンポーネントID
 *
 * @param state - 警告ーダイアログOneWayBind領域用の構造
 */
const openWarnDialog = async (
  uniqueCpId: string,
  state: Or21815StateType
): Promise<Or32462MsgBtnType> => {
  Or21815Logic.state.set({
    uniqueCpId: uniqueCpId,
    state: {
      ...state,
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21815Logic.state.get(uniqueCpId)?.isOpen,
      () => {
        const event = Or21815Logic.event.get(uniqueCpId)

        let result: Or32462MsgBtnType = Or32462Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL

        if (event?.firstBtnClickFlg) {
          result = Or32462Const.DEFAULT.MESSAGE_BTN_TYPE_YES
        }
        if (event?.secondBtnClickFlg) {
          result = Or32462Const.DEFAULT.MESSAGE_BTN_TYPE_YES
        }
        if (event?.thirdBtnClickFlg) {
          result = Or32462Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL
        }
        if (event?.closeBtnClickFlg) {
          result = Or32462Const.DEFAULT.MESSAGE_BTN_TYPE_CANCEL
        }

        // 確認ダイアログのフラグをOFF
        Or21815Logic.event.set({
          uniqueCpId: uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
            closeBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 帳票セクション切替
 */
watch(
  () => mo01334Type.value.value,
  async (newValue, oldValue) => {
    setBeforChangePrintData(oldValue)
    setAfterChangePrintData(newValue)
    await outputLedgerName(newValue)

    // 日付印刷区分が2の場合
    if (Or32462Const.DEFAULT.STR.TWO === mo00039Type.value) {
      // 指定日を活性表示にする。
      mo00020Flag.value = true
    } else {
      // 指定日を非表示にする。
      mo00020Flag.value = false
    }
  }
)

/**
 * 空欄印刷切替
 */
watch(
  () => mo00018Type.value.modelValue,
  (newValue) => {
    // 画面.空欄印刷が「ON：1」の場合
    if (newValue) {
      // 利用者・履歴選択非表示
      userHistoryShow.value = false

      centerCols.value = Or32462Const.DEFAULT.NUMBER.TEN
    }
    // 画面.空欄印刷が「OFF：0」の場合
    else {
      // 利用者・履歴選択表示
      userHistoryShow.value = true

      centerCols.value = Or32462Const.DEFAULT.NUMBER.THREE
    }
  }
)

/**
 * 「日付印刷区分」ラジオボタン押下
 */
watch(
  () => mo00039Type.value,
  (newValue) => {
    if (Or32462Const.DEFAULT.STR.TWO === newValue) {
      // 指定日を活性表示にする
      mo00020Flag.value = true
    } else {
      // 指定日を非表示にする
      mo00020Flag.value = false
    }
  }
)

/**
 * 「利用者選択方法」ラジオボタン選択
 */
watch(
  () => mo00039OneWayUserSelectType.value,
  (newValue) => {
    // 画面ボタン活性非活性設定
    btnItemSetting()

    // 利用者選択方法が「単一」の場合
    if (Or32462Const.DEFAULT.TANI === newValue) {
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.TANI
      orX0130Oneway.tableStyle = 'width: 320px'

      if (OrX0130Logic.event.get(orX0130.value.uniqueCpId)) {
        if (
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList &&
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList.length > 0
        ) {
          local.userList = []
          for (const item of OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList) {
            if (item) {
              local.userList.push({
                userId: item.userId,
                userName: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
              } as UserEntity)
            }
          }
          local.userNoSelect = false
        } else {
          local.userList = []
          local.userNoSelect = true
        }
      } else {
        local.userList = []
        local.userNoSelect = true
      }

      // 履歴選択が"「単一"：0」の場合
      if (mo00039OneWayHistorySelectType.value === OrX0130Const.DEFAULT.TANI) {
        // 空欄で印刷チェックボックス
        localOneway.mo000018OneWay.disabled = false
      }
      // 履歴選択が「"複数"：1」の場合
      else {
        // 空欄で印刷チェックボックス
        localOneway.mo000018OneWay.disabled = true
      }

      // 利用者一覧明細に親画面.利用者IDが存在する場合
      if (local.userId) {
        // 利用者IDを対するレコードを選択状態にする
        orX0130Oneway.userId = local.userId
      }
      // 親画面.利用者IDが存在しない場合
      else {
        orX0130Oneway.userId = Or32462Const.DEFAULT.STR.EMPTY
      }
    }
    // 利用者選択が「"複数"：1」
    else {
      // 復元
      orX0117Oneway.type = Or32462Const.DEFAULT.STR.ONE
      orX0130Oneway.selectMode = OrX0130Const.DEFAULT.HUKUSUU
      orX0130Oneway.tableStyle = 'width: 430px'

      if (OrX0130Logic.event.get(orX0130.value.uniqueCpId)) {
        if (
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList &&
          OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList.length > 0
        ) {
          local.userList = []
          for (const item of OrX0130Logic.event.get(orX0130.value.uniqueCpId)!.userList) {
            if (item) {
              local.userList.push({
                userId: item.userId,
                userName: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
              } as UserEntity)
            }
          }
          local.userNoSelect = false
        } else {
          local.userList = []
          local.userNoSelect = true
        }
      } else {
        local.userList = []
        local.userNoSelect = true
      }

      // 空欄で印刷チェックボックス
      localOneway.mo000018OneWay.disabled = true
    }

    // 利用者一覧明細に親画面.利用者IDが存在する場合
    if (local.userId) {
      // 利用者IDを対するレコードを選択状態にする
      orX0130Oneway.userId = local.userId
    }
    // 利用者一覧明細に親画面.利用者IDが存在しない場合
    else {
      orX0130Oneway.userId = Or32462Const.DEFAULT.STR.EMPTY
    }
  }
)

/**
 * 「履歴選択方法」ラジオボタン押下
 */
watch(
  () => mo00039OneWayHistorySelectType.value,
  (newValue) => {
    // 画面ボタン活性非活性設定
    btnItemSetting()

    // 履歴選択方法が「単一」の場合
    if (Or32462Const.DEFAULT.TANI === newValue) {
      orX0128OnewayModel.singleFlg = OrX0128Const.DEFAULT.TANI

      // 利用者選択が「"単一"：0」
      if (Or32462Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
        // 空欄で印刷チェックボックス
        localOneway.mo000018OneWay.disabled = false
      }
      // 利用者選択が「"複数"：1」
      else {
        // 空欄で印刷チェックボックス
        localOneway.mo000018OneWay.disabled = true
      }
    }
    // 履歴選択が「"複数"：1」の場合
    else {
      orX0128OnewayModel.singleFlg = OrX0128Const.DEFAULT.HUKUSUU
      orX0117Oneway.type = Or32462Const.DEFAULT.STR.ZERO

      // 空欄で印刷チェックボックス
      localOneway.mo000018OneWay.disabled = true
    }
  }
)

/**
 * 「履歴選択」の監視
 */
watch(
  () => OrX0128Logic.event.get(orX0128.value.uniqueCpId),
  (newValue) => {
    // 画面ボタン活性非活性設定
    btnItemSetting()
    if (newValue) {
      if (newValue.historyDetClickFlg) {
        local.orX0128DetList = newValue.orX0128DetList
        if (newValue.orX0128DetList.length > 0) {
          local.historyNoSelect = false

          for (const item of newValue.orX0128DetList) {
            if (item) {
              // 記入用シート方式ラジオボタン(デフォルト値の設定)
              if (!mo00039OneWayMemoColumnPrinterType.value) {
                mo00039OneWayMemoColumnPrinterType.value = item.assType as string
              }
            }
          }
        } else {
          local.historyNoSelect = true
        }
      } else {
        local.historyNoSelect = true
      }
    }
  }
)

/**
 * 「利用者選択」の監視
 */
watch(
  () => OrX0130Logic.event.get(orX0130.value.uniqueCpId),
  async (newValue) => {
    if (newValue?.clickFlg) {
      if (Or32462Const.DEFAULT.TANI === mo00039OneWayUserSelectType.value) {
        await PrintSettingsUserChangeSelect()
      }

      if (newValue.userList.length > 0) {
        local.userNoSelect = false
        local.selectUserId = newValue.userList[0].userId

        local.userList = []
        for (const item of newValue.userList) {
          if (item) {
            local.userList.push({
              userId: item.userId,
              userName: item.name1Knj + OrX0130Const.DEFAULT.SAPCE + item.name2Knj,
            } as UserEntity)
          }
        }
      } else {
        local.userList = []
        local.userNoSelect = true
      }
    } else {
      local.userList = []
      local.userNoSelect = true
    }
  }
)

/**
 * メモ欄の選択切替の監視
 */
watch(
  () => mo00039OneWayMemoColumnPrinterType.value,
  async () => {
    // バックエンドAPIからメモ欄情報を保存
    const inputData: IFreeAssessmentFacePrintSettingsMemoUpdateInEntity = {
      shokuId: local.shokuinId,
      gsyscd: systemCommonsStore.getSystemCode,
      memoKbn: mo00039OneWayMemoColumnPrinterType.value,
      memoModifiedCnt: localData.data.memoModifiedCnt,
    } as IFreeAssessmentFacePrintSettingsMemoUpdateInEntity

    await ScreenRepository.update('freeAssessmentFacePrintSettingsMemoUpdate', inputData)
  }
)
</script>
<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="mo00024Oneway"
  >
    <template #cardItem>
      <c-v-row
        class="or32462_row"
        no-gutter
      >
        <c-v-col
          cols="12"
          sm="2"
          class="table-header"
        >
          <base-mo-01334
            v-model="mo01334Type"
            :oneway-model-value="mo01334Oneway"
            class="list-wrapper"
          >
            <!-- 帳票 -->
            <template #[`item.ledgerName`]="{ item }">
              <!-- 分子：一覧専用ラベル（文字列型） -->
              <base-mo01337 :oneway-model-value="item.mo01337OnewayLedgerName" />
            </template>
            <!-- ページングを非表示 -->
            <template #bottom />
          </base-mo-01334>
        </c-v-col>
        <c-v-col
          cols="12"
          :sm="centerCols"
          class="content_center"
        >
          <c-v-row
            no-gutter
            class="customCol or32462_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              style="padding-left: 0px"
            >
              <base-mo00611
                :oneway-model-value="localOneway.mo00611OneWaySetting"
                class="mx-2"
                @click="settingBtnClick"
              ></base-mo00611>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="printerOption customCol or32462_row"
          >
            <c-v-col
              cols="12"
              sm="12"
            >
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayBasicSettings"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="customCol or32462_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              style="padding-bottom: 0px"
            >
              <base-mo00045
                v-model="mo00045Type"
                :oneway-model-value="localOneway.mo00045OneWay"
                @update:model-value="inputChange"
              ></base-mo00045>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="customCol or32462_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              style="padding-left: 0px"
            >
              <base-mo00018
                v-model="nameMo00018Type"
                :oneway-model-value="localOneway.mo000018NameOneWay"
              ></base-mo00018>
            </c-v-col>
          </c-v-row>
          <c-v-divider></c-v-divider>
          <c-v-row
            no-gutter
            class="customCol or32462_row"
          >
            <c-v-col
              cols="12"
              sm="7"
              style="padding-left: 0px; padding-right: 0px"
            >
              <base-mo00039
                v-model="mo00039Type"
                :oneway-model-value="localOneway.mo00039OneWay"
              >
              </base-mo00039>
            </c-v-col>
            <c-v-col
              cols="12"
              sm="5"
              style="padding-left: 0px; padding-right: 8px"
            >
              <base-mo00020
                v-if="mo00020Flag"
                v-model="mo00020Type"
                :oneway-model-value="localOneway.mo00020OneWay"
              >
              </base-mo00020>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="printerOption customCol or32462_row"
          >
            <c-v-col
              cols="12"
              sm="12"
            >
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayPrinterOption"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="customCol or32462_row"
          >
            <c-v-col
              cols="12"
              sm="12"
              style="padding-left: 0px; padding-top: 0px"
            >
              <base-mo00018
                v-model="mo00018Type"
                :oneway-model-value="localOneway.mo000018OneWay"
              ></base-mo00018>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="printerOption customCol or32462_row"
          >
            <c-v-col
              cols="12"
              sm="12"
            >
              <base-mo01338
                :oneway-model-value="localOneway.mo01338OneWayMemoColumnPrinter"
                style="background-color: transparent"
              >
              </base-mo01338>
            </c-v-col>
          </c-v-row>
          <c-v-row
            no-gutter
            class="customCol or32462_row"
            style="padding-left: 0px"
          >
            <c-v-col
              cols="12"
              sm="12"
            >
              <base-mo00039
                v-model="mo00039OneWayMemoColumnPrinterType"
                :oneway-model-value="localOneway.mo00039OneWayMemoColumnPrinter"
              >
              </base-mo00039>
            </c-v-col>
          </c-v-row>
        </c-v-col>
        <c-v-col
          v-if="userHistoryShow"
          cols="12"
          sm="7"
          class="content_center"
        >
          <c-v-row
            class="or32462_row"
            no-gutter
            style="align-items: center; height: 90px"
          >
            <c-v-col
              cols="12"
              sm="4"
              style="padding: 0px"
            >
              <c-v-row
                class="or32462_row"
                no-gutter
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  class="or32462-pd-8"
                >
                  <base-mo01338
                    :oneway-model-value="localOneway.mo01338OneWayPrinterUserSelect"
                    style="background-color: transparent"
                  >
                  </base-mo01338>
                </c-v-col>
              </c-v-row>
              <c-v-row
                class="or32462_row"
                no-gutter
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  style="padding: 0px"
                >
                  <base-mo00039
                    v-model="mo00039OneWayUserSelectType"
                    :oneway-model-value="localOneway.mo00039OneWayUserSelectType"
                  >
                  </base-mo00039>
                </c-v-col>
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-if="kijunbiFlag"
              cols="12"
              sm="4"
              style="padding: 0px"
            >
              <c-v-row
                class="or32462_row"
                no-gutter
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  class="or32462-pd-8"
                >
                  <base-mo00615 :oneway-model-value="localOneway.mo00615OneWayType"></base-mo00615>
                </c-v-col>
              </c-v-row>
              <c-v-row
                class="or32462_row"
                no-gutter
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  style="padding: 0px"
                >
                  <base-mo00020
                    v-model="mo00020TypeKijunbi"
                    :oneway-model-value="localOneway.mo00020KijunbiOneWay"
                  />
                </c-v-col>
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-else
              cols="12"
              sm="4"
              style="padding: 0px"
            >
              <c-v-row
                class="or32462_row"
                no-gutter
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  class="or32462-pd-8"
                >
                  <base-mo01338
                    :oneway-model-value="localOneway.mo01338OneWayPrinterHistorySelect"
                    style="background-color: transparent"
                  >
                  </base-mo01338>
                </c-v-col>
              </c-v-row>
              <c-v-row
                class="or32462_row"
                no-gutter
              >
                <c-v-col
                  cols="12"
                  sm="12"
                  style="padding: 0px"
                >
                  <base-mo00039
                    v-model="mo00039OneWayHistorySelectType"
                    :oneway-model-value="localOneway.mo00039OneWayHistorySelectType"
                  >
                  </base-mo00039>
                </c-v-col>
              </c-v-row>
            </c-v-col>
            <c-v-col
              v-if="tantoIconBtn"
              cols="12"
              sm="4"
              class="or32462-pd-8"
            >
              <!-- 担当ケアマネプルダウン -->
              <g-custom-or-x-0145
                v-bind="orx0145"
                v-model="orX0145Type"
                :oneway-model-value="localOneway.orX0145Oneway"
                @update:model-value="orx0145UpdateModelValue"
              ></g-custom-or-x-0145>
            </c-v-col>
          </c-v-row>
          <c-v-divider></c-v-divider>
          <c-v-row
            class="or32462_row"
            no-gutter
          >
            <c-v-col
              :cols="userCols"
              class="or32462-pd-8"
            >
              <!-- 印刷設定画面利用者一覧 -->
              <g-custom-or-x-0130
                v-if="orX0130Oneway.selectMode && initFlag"
                v-bind="orX0130"
                :oneway-model-value="orX0130Oneway"
              >
              </g-custom-or-x-0130>
            </c-v-col>
            <c-v-col
              v-if="mo01334TypeHistoryFlag && initFlag"
              cols="6"
              style="overflow-x: hidden"
              class="or32462-pd-8"
            >
              <div style="height: 520px">
                <!-- 計画期間＆履歴一覧 -->
                <g-custom-or-x-0128
                  v-if="orX0128OnewayModel.singleFlg"
                  v-bind="orX0128"
                  :oneway-model-value="orX0128OnewayModel"
                ></g-custom-or-x-0128>
              </div>
            </c-v-col>
          </c-v-row>
        </c-v-col>
      </c-v-row>
    </template>
    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          v-bind="localOneway.mo00611OneWay"
          @click="close"
        >
        </base-mo00611>
        <!-- PDFダウンロードボタン -->
        <base-mo00609
          :oneway-model-value="localOneway.mo00609Oneway"
          class="mx-2"
          @click="pdfDownload()"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <!-- メッセージ エラー -->
  <g-base-or-21813 v-bind="or21813" />
  <!-- Or21814:有機体:確認ダイアログ -->
  <g-base-or21814 v-bind="or21814" />
  <!-- メッセージ 警告 -->
  <g-base-or-21815 v-bind="or21815" />
  <!-- 印刷設定帳票出力状態リスト画面 -->
  <g-custom-or-x-0117
    v-if="showDialogOrX0117"
    v-bind="orX0117"
    :oneway-model-value="orX0117Oneway"
  ></g-custom-or-x-0117>
  <!-- GUI00901_フェースシート印刷設定画面 -->
  <g-custom-or53608
    v-if="showDialogOr53608"
    v-bind="or53608"
  />
</template>
<style>
.or32462_content {
  padding: 0px !important;
}

.or32462_gokeiClass {
  label {
    color: #ffffff;
  }
}
</style>
<style scoped lang="scss">
@use '@/styles/cmn/mo-data-table.scss';

:deep(.v-table__wrapper) {
  overflow-x: auto !important;
}

.or32462_row {
  margin: 0px !important;
}

.table-header {
  padding: 8px;
}

.content_center {
  border-left: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  padding: 0px !important;

  .printerOption {
    background-color: #edf1f7;
  }

  :deep(.label-area-style) {
    display: none;
  }

  .customCol {
    margin-left: 0px;
    margin-right: 0px;
  }

  .or32462-pd-8 {
    padding: 8px;
  }
}
</style>
