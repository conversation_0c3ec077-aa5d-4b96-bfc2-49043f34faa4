<script setup lang="ts">
/**
 * GUI00793:有機体:印刷設定
 * GUI00793_印刷設定
 *
 * @description
 * 印刷設定
 *
 * <AUTHOR>
 */
import {
  computed,
  definePageMeta,
  ref,
  useInitialize,
  useScreenStore,
  useSetupChildProps,
} from '#imports'
import { Or10269Const } from '~/components/custom-components/organisms/Or10269/Or10269.constants'
import { Or10269Logic } from '~/components/custom-components/organisms/Or10269/Or10269.logic'
import type { Or10269Param } from '~/components/custom-components/organisms/Or10269/Or10269.type'

definePageMeta({
  middleware: ['auth', 'cache-handler'],
  layout: 'web-main',
})

/**************************************************
 * POP画面ポップアップ
 **************************************************/
// 画面ID
const screenId = 'GUI00793'
// ルーティング
const routing = 'GUI00793/pinia'
// 画面物理名
const screenName = 'GUI00793'

// 画面状態管理用操作変数
const screenStore = useScreenStore()

const or10269 = ref({ uniqueCpId: '' })

/**************************************************
 * 画面固有処理
 **************************************************/
// piniaの画面領域を初期化する
const isInit = screenStore.initialize({
  screenId,
  routing,
  screenName,
  pageCp: { cpId: 'GUI00793' },
})

/**************************************************
 * Props
 **************************************************/
// piniaから最上位の画面コンポーネント情報を取得する
// これによりブラウザバックで画面遷移した場合などでも、画面コンポーネントのユニークコンポーネントIDが利用できる
const pageComponent = screenStore.screen().supplement.pageComponent

// 子コンポーネントのユニークIDを設定する
// or10269.value.uniqueCpId = pageComponent.uniqueCpId

// 自身のPinia領域をセットアップ
useInitialize({
  cpId: 'GUI00793',
  uniqueCpId: pageComponent.uniqueCpId,
  childCps: [{ cpId: Or10269Const.CP_ID(1) }],
})

// 子コンポーネントのユニークIDを設定する
useSetupChildProps(pageComponent.uniqueCpId, {
  [Or10269Const.CP_ID(1)]: or10269.value,
})

// 画面情報領域に保持している初期化フラグの値により初期化要否を制御（「戻る」「進む」の場合は初期化しない）
if (isInit) {
  // コンポーネントの初期化処理を開始する
  Or10269Logic.initialize(or10269.value.uniqueCpId)
}

// ダイアログ表示フラグ
const showDialogOr10269 = computed(() => {
  // Or10269のダイアログ開閉状態
  return Or10269Logic.state.get(or10269.value.uniqueCpId)?.isOpen ?? false
})

/**
 *  ボタン押下時の処理(Or10269)--期間管理フラグが「管理する」の場合
 *
 */
function onClickOr10269_1() {
  // Or10269のダイアログ開閉状態を更新する
  Or10269Logic.state.set({
    uniqueCpId: or10269.value.uniqueCpId,
    state: {
      isOpen: true,
      param: {
        prtNo: '1',
        svJigyoId: '1',
        shisetuId: '1',
        tantoId: '1',
        syubetsuId: '2',
        sectionName: 'インターライ方式ケアアセスメント表',
        userId: '41',
        assessmentId: '4',
        svJigyoKnj: '1',
        processYmd: '2025/07/02',
        parentUserIdSelectDataFlag: false,
        focusSettingInitial: ['や', 'ゆ', 'よ'],
        selectedUserCounter: '2'
      } as Or10269Param,
    },
  })
}

/**************************************************
 * コンポーネント固有処理
 **************************************************/
</script>
<template>
  <c-v-row no-gutters
    ><c-v-col>ケアマネモックアップ開発ダイヤログ画面確認用ページ</c-v-col></c-v-row
  >
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        to="/mockup-dev-poc/mockup-dev-poc-top-cmn"
        >ケアマネモックアップ開発PoCの画面確認用ページへ遷移する
      </v-btn>
    </c-v-col>
  </c-v-row>
  <c-v-row no-gutters>
    <c-v-col>
      <v-btn
        variant="plain"
        @click="onClickOr10269_1"
        >GUI00793_印刷設定</v-btn
      >
      <g-custom-or-10269
        v-if="showDialogOr10269"
        v-bind="or10269"
      />
    </c-v-col>
  </c-v-row>
</template>
