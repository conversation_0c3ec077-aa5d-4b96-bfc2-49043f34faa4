<script setup lang="ts">
/**
 * Or00233:有機体:（希望負担額登録）ダイアログ
 * GUI01174_希望負担額登録
 *
 * <AUTHOR>
 */
import { onMounted, reactive, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { Or00233Const } from './Or00233.constants'
import type { Or00233StateType } from './Or00233.type'
import { useScreenOneWayBind, useSetupChildProps } from '#imports'
import { ScreenRepository } from '~/repositories/business/core/v1/ScreenRepository'
import type { Mo00024Type } from '~/types/business/components/Mo00024Type'
import type { Mo00038Type, Mo00038OnewayType } from '~/types/business/components/Mo00038Type'
import type { Mo00045Type, Mo00045OnewayType } from '~/types/business/components/Mo00045Type'
import type { Mo00609OnewayType } from '~/types/business/components/Mo00609Type'
import type { Mo00611OnewayType } from '~/types/business/components/Mo00611Type'
import type { Mo01338OnewayType } from '~/types/business/components/Mo01338Type'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import type {
  Or00233OnewayType,
  Or00233SelectInfoType,
  Or00233Type,
} from '~/types/cmn/business/components/Or00233Type'
import { CustomClass } from '~/types/CustomClassType'
import type {
  KibohudanTorokuSelectInEntity,
  KibohudanTorokuSelectOutEntity,
} from '~/repositories/cmn/entities/KibohudanTorokuSelectEntity'
import type { KibohudanTorokuKakuteInEntity } from '~/repositories/cmn/entities/KibohudanTorokuKakuteEntity'

const { t } = useI18n()
/************************************************
 * Props
 ************************************************/
interface Props {
  modelValue: Or00233Type
  onewayModelValue: Or00233SelectInfoType
  uniqueCpId: string
}
const props = defineProps<Props>()

/**************************************************
 * 変数定義
 **************************************************/
const defaultOnewayModelValue: Or00233OnewayType = {
  inputInfo: {
    sysYmd: '',
    userId: '1',
  },
}

const localOneway = reactive({
  or00233: {
    ...defaultOnewayModelValue,
    ...props.onewayModelValue,
  },
  mo00024Oneway: {
    width: '540px',
    persistent: true,
    showCloseBtn: true,
    mo01344Oneway: {
      toolbarTitle: t('label.kibohudan-toroku-title'),
      showCardActions: true,
      toolbarTitleCenteredFlg: false,
    },
  },
  // 説明文ラベル
  mo01338Title1Oneway: {
    value: t('label.kibohudan-toroku-text'),
    valueFontWeight: 'bold',
    customClass: new CustomClass({ itemClass: 'ma-1', itemStyle: 'color: grey' }),
  } as Mo01338OnewayType,
  // 居宅介護サービス負担額ラベル
  mo01338Title2Oneway: {
    value: t('label.kibohudan-toroku-home-care-service'),
    customClass: new CustomClass({ itemClass: 'ma-1' }),
  } as Mo01338OnewayType,
  // 訪問通所負担額ラベル
  mo01338Title3Oneway: {
    value: t('label.kibohudan-toroku-home-visit-dayservice'),
    customClass: new CustomClass({ itemClass: 'ma-1' }),
  } as Mo01338OnewayType,
  // 短期入所負担額ラベル
  mo01338Title4Oneway: {
    value: t('label.kibohudan-toroku-short-term-admission'),
    customClass: new CustomClass({ itemClass: 'ma-1' }),
  } as Mo01338OnewayType,
  mo00038Oneway1: {
    mo00045Oneway: {
      showItemLabel: Or00233Const.DEFAULT.SHOW_ITEM_LABEL,
      maxLength: Or00233Const.DEFAULT.MAX_LENGTH_7,
      width: '120px',
      customClass: new CustomClass({ labelClass: '' }),
    } as Mo00045OnewayType,
    min: -999999,
    max: 9999999,
  } as Mo00038OnewayType,
  mo00038Oneway2: {
    mo00045Oneway: {
      showItemLabel: Or00233Const.DEFAULT.SHOW_ITEM_LABEL,
      maxLength: Or00233Const.DEFAULT.MAX_LENGTH_9,
      width: '150px',
      customClass: new CustomClass({ outerClass: 'ml-n2', labelClass: '' }),
    } as Mo00045OnewayType,
    min: -99999999,
    max: 999999999,
  } as Mo00038OnewayType,
  // 閉じるボタン設置
  mo00611Oneway: {
    btnLabel: t('btn.close'),
    tooltipLocation: 'bottom',
    tooltipText: t('tooltip.screen-close'),
  } as Mo00611OnewayType,
  // 確定ボタン設置
  mo00609Oneway: {
    btnLabel: t('btn.confirm'),
    tooltipLocation: 'bottom',
    tooltipText: t('tooltip.confirm-btn'),
  } as Mo00609OnewayType,
})

const local = reactive({
  // 更新区分
  updKbn: Or00233Const.DEFAULT.UPD_KBN_C,
  showFlg: compareDates(localOneway.or00233.sysYmd, Or00233Const.DEFAULT.BASE_DATE) > 0,
  // 初期訪問通所負担額
  homeVisitDayserviceNum: 0,
  // 初期短期入所負担額
  shortTermAdmissionNum: 0,
  // 初期居宅介護サービス負担額
  homeCareServiceNum: 0,
  // 更新回数
  modifiedCnt: Or00233Const.DEFAULT.ZERO,
})

const or21814_1 = ref({ uniqueCpId: '' })
const or21814_2 = ref({ uniqueCpId: '' })

// ダイアログ設置
const mo00024 = ref<Mo00024Type>({
  isOpen: Or00233Const.DEFAULT.IS_OPEN,
})

// 訪問通所負担額入力値
const mo00038HomeVisitDayservice = ref<Mo00038Type>({
  mo00045: {
    value: '',
  } as Mo00045Type,
})
// 短期入所負担額
const mo00038ShortTermAdmission = ref<Mo00038Type>({
  mo00045: {
    value: '',
  } as Mo00045Type,
})
// 居宅介護サービス負担額
const mo00038HomeCareService = ref<Mo00038Type>({
  mo00045: {
    value: '',
  } as Mo00045Type,
})

/**************************************************
 * Pinia
 **************************************************/
const { setState } = useScreenOneWayBind<Or00233StateType>({
  cpId: Or00233Const.CP_ID(0),
  uniqueCpId: props.uniqueCpId,
  onUpdate: {
    isOpen: (value) => {
      mo00024.value.isOpen = value ?? Or00233Const.DEFAULT.IS_OPEN
    },
  },
})

/**************************************************
 * Emit
 **************************************************/
const emit = defineEmits(['update:modelValue'])

/**************************************************
 * コンポーネント固有処理
 **************************************************/
// 子コンポーネントのユニークIDを設定する
useSetupChildProps(props.uniqueCpId, {
  [Or21814Const.CP_ID(1)]: or21814_1.value,
  [Or21814Const.CP_ID(2)]: or21814_2.value,
})

onMounted(() => {
  // 初期情報取得
  void getInitDataInfo()
})

/** 初期情報取得 */
async function getInitDataInfo() {
  const inputData: KibohudanTorokuSelectInEntity = {
    /** 利用者ID */
    userid: localOneway.or00233.userId,
  }

  // 希望負担額情報TBLより年月、利用者をキーに居宅介護サービス負担額を取得して表示する。
  const resData: KibohudanTorokuSelectOutEntity = await ScreenRepository.select(
    'kibohudanTorokuSelect',
    inputData
  )
  if (resData?.data !== null) {
    // 訪問通所負担額
    const homeVisitDayserviceNum = resData.data.hKibougaku
      ? Number(resData.data.hKibougaku.replace(/,/g, ''))
      : 0
    mo00038HomeVisitDayservice.value.mo00045.value = homeVisitDayserviceNum.toLocaleString()
    // 短期入所負担額
    const shortTermAdmissionNum = resData.data.tKibougaku
      ? Number(resData.data.tKibougaku.replace(/,/g, ''))
      : 0
    mo00038ShortTermAdmission.value.mo00045.value = shortTermAdmissionNum.toLocaleString()
    // 居宅介護サービス負担額
    const homeCareServiceNum = resData.data.kibougaku1pon
      ? Number(resData.data.kibougaku1pon.replace(/,/g, ''))
      : 0
    mo00038HomeCareService.value.mo00045.value = homeCareServiceNum.toLocaleString()
    local.homeVisitDayserviceNum = homeVisitDayserviceNum
    local.shortTermAdmissionNum = shortTermAdmissionNum
    local.homeCareServiceNum = homeCareServiceNum
    local.modifiedCnt = resData.data.modifiedCnt
    local.updKbn = Or00233Const.DEFAULT.UPD_KBN_U
  } else {
    mo00038HomeVisitDayservice.value.mo00045.value = Or00233Const.DEFAULT.ZERO
    mo00038ShortTermAdmission.value.mo00045.value = Or00233Const.DEFAULT.ZERO
    mo00038HomeCareService.value.mo00045.value = Or00233Const.DEFAULT.ZERO
    local.updKbn = Or00233Const.DEFAULT.UPD_KBN_C
  }
}

function compareDates(dateStr1: string, dateStr2: string): number {
  const date1 = new Date(dateStr1)
  const date2 = new Date(dateStr2)
  return date1.getTime() - date2.getTime()
}

function hasChange(): boolean {
  // 訪問通所負担額
  const homeVisitDayserviceNum = mo00038HomeVisitDayservice.value.mo00045.value
    ? Number(mo00038HomeVisitDayservice.value.mo00045.value.replace(/,/g, ''))
    : 0
  mo00038HomeVisitDayservice.value.mo00045.value = homeVisitDayserviceNum.toLocaleString()
  // 短期入所負担額
  const shortTermAdmissionNum = mo00038ShortTermAdmission.value.mo00045.value
    ? Number(mo00038ShortTermAdmission.value.mo00045.value.replace(/,/g, ''))
    : 0
  mo00038ShortTermAdmission.value.mo00045.value = shortTermAdmissionNum.toLocaleString()
  // 短期入所負担額
  const homeCareServiceNum = mo00038HomeCareService.value.mo00045.value
    ? Number(mo00038HomeCareService.value.mo00045.value.replace(/,/g, ''))
    : 0

  if (
    local.homeCareServiceNum !== homeCareServiceNum ||
    local.homeVisitDayserviceNum !== homeVisitDayserviceNum ||
    local.shortTermAdmissionNum !== shortTermAdmissionNum
  ) {
    return true
  } else {
    return false
  }
}

/**
 * 確認ダイアログの開閉
 *
 */
async function showConfirmMsg() {
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト0
      dialogText: t('message.i-cmn-21800'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.ok'),
      secondBtnType: 'blank',
      thirdBtnType: 'blank',
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814_1.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_1.value.uniqueCpId)?.isOpen,
      () => {
        const result = 'yes' as 'yes' | 'no'
        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 「確定」処理
 */
async function doConfirm() {
  const inputData: KibohudanTorokuKakuteInEntity = {
    /** 利用者ID */
    userid: localOneway.or00233.userId,
    /** 訪問通所負担額 */
    hKibougaku: mo00038HomeVisitDayservice.value.mo00045.value?.replace(/,/g, ''),
    /** 短期入所負担額 */
    tKibougaku: mo00038ShortTermAdmission.value.mo00045.value?.replace(/,/g, ''),
    /** 居宅介護サービス負担額 */
    kibougaku1pon: mo00038HomeCareService.value.mo00045.value?.replace(/,/g, ''),
    /** 更新区分 */
    updateKbn: local.updKbn,
    /** 更新回数 */
    modifiedCnt: local.modifiedCnt,
  }

  // 希望負担額登録確定処理を行う。
  await ScreenRepository.update('kibohudanTorokuKakute', inputData)

  // 選択情報値戻り
  emit('update:modelValue', {
    reSearchFlg: Or00233Const.DEFAULT.RE_SEARCH_FLG_1,
  } as Or00233Type)
  doClose()
}

/**
 * 「確定」ボタン押下
 */
const onConfirmBtn = () => {
  if (!hasChange()) {
    // 該当画面にデータが変更なしの場合
    void showConfirmMsg()
  } else {
    void doConfirm()
  }
}

/**
 * 確認ダイアログの開閉
 *
 */
async function showCloseMsg() {
  Or21814Logic.state.set({
    uniqueCpId: or21814_2.value.uniqueCpId,
    state: {
      // ダイアログタイトル
      dialogTitle: t('label.confirm'),
      // ダイアログテキスト0
      dialogText: t('message.i-com-0006'),
      firstBtnType: 'normal1',
      firstBtnLabel: t('btn.yes'),
      secondBtnType: 'destroy1',
      secondBtnLabel: t('btn.no'),
      thirdBtnType: 'normal3',
      thirdBtnLabel: t('btn.cancel'),
    },
  })
  // 確認ダイアログをオープン
  Or21814Logic.state.set({
    uniqueCpId: or21814_2.value.uniqueCpId,
    state: {
      isOpen: true,
    },
  })
  return new Promise((resolve) => {
    watch(
      () => Or21814Logic.state.get(or21814_2.value.uniqueCpId)?.isOpen,
      () => {
        const event = Or21814Logic.event.get(or21814_2.value.uniqueCpId)

        let result = 'cancel' as 'yes' | 'no' | 'cancel'

        if (event?.firstBtnClickFlg) {
          result = 'yes'
        }
        if (event?.secondBtnClickFlg) {
          result = 'no'
        }
        if (event?.thirdBtnClickFlg) {
          result = 'cancel'
        }

        // 確認ダイアログのフラグをOFF
        Or21814Logic.event.set({
          uniqueCpId: or21814_2.value.uniqueCpId,
          events: {
            firstBtnClickFlg: false,
            secondBtnClickFlg: false,
            thirdBtnClickFlg: false,
          },
        })

        resolve(result)
      },
      { once: true }
    )
  })
}

/**
 * 「閉じる」処理
 */
function doClose() {
  setState({ isOpen: false })
}

/**
 * 閉じるボタン押下時
 */
const onClickCloseBtn = async (): Promise<void> => {
  if (hasChange()) {
    // 該当画面にデータが変更ありの場合
    mo00024.value.isOpen = true
    const dialogResult = await showCloseMsg()
    switch (dialogResult) {
      case 'yes': {
        await doConfirm()
        break
      }
      case 'no': {
        doClose()
        break
      }
      case 'cancel': {
        break
      }
    }
  } else {
    doClose()
  }
}

/**
 * Mo00024（ダイアログ）の開閉状態を監視
 *
 * ダイアログの×ボタンなどで閉じられた場合に発火する
 */
watch(
  () => mo00024.value.isOpen,
  (newValue) => {
    if (!newValue) {
      void onClickCloseBtn()
    }
  }
)
</script>

<template>
  <base-mo00024
    v-model="mo00024"
    :oneway-model-value="localOneway.mo00024Oneway"
  >
    <template #cardItem>
      <c-v-sheet>
        <c-v-row
          no-gutters
          style="padding: 8px; justify-content: center; height: 240px"
        >
          <c-v-col cols="12" class="pa-0">
            <c-v-row no-gutters>
              <c-v-col cols="auto" class="pa-0" style="padding-left: 8px !important;">
                <base-mo01338 :oneway-model-value="localOneway.mo01338Title1Oneway" />
              </c-v-col>
            </c-v-row>
            <c-v-row
              v-if="local.showFlg"
              no-gutters
            >
              <c-v-col cols="auto">
                <base-mo01338 :oneway-model-value="localOneway.mo01338Title2Oneway" />
              </c-v-col>
              <c-v-col cols="7" style="padding-left: 0px !important;margin-left: -25px;">
                <base-mo00038
                  v-model="mo00038HomeCareService"
                  :oneway-model-value="localOneway.mo00038Oneway1"
                ></base-mo00038>
              </c-v-col>
            </c-v-row>
            <c-v-row
              v-if="!local.showFlg"
              no-gutters
            >
              <c-v-col cols="4">
                <base-mo01338 :oneway-model-value="localOneway.mo01338Title3Oneway" />
              </c-v-col>
              <c-v-col cols="8" style="padding-left: 0px !important;margin-left: -47px;">
                <base-mo00038
                  v-model="mo00038HomeVisitDayservice"
                  :oneway-model-value="localOneway.mo00038Oneway2"
                ></base-mo00038>
              </c-v-col>
            </c-v-row>
            <c-v-row
              v-if="!local.showFlg"
              no-gutters
            >
              <c-v-col cols="4">
                <base-mo01338 :oneway-model-value="localOneway.mo01338Title4Oneway" />
              </c-v-col>
              <c-v-col cols="8" style="padding-left: 0px !important;margin-left: -47px;">
                <base-mo00038
                  v-model="mo00038ShortTermAdmission"
                  :oneway-model-value="localOneway.mo00038Oneway2"
                ></base-mo00038>
              </c-v-col>
            </c-v-row>
          </c-v-col>
        </c-v-row>
      </c-v-sheet>
    </template>

    <template #cardActionRight>
      <!-- ダイアログフッター -->
      <c-v-row>
        <c-v-spacer />
        <!-- 閉じるボタン -->
        <base-mo00611
          :oneway-model-value="localOneway.mo00611Oneway"
          @click="onClickCloseBtn"
        >
        </base-mo00611>
        <!-- 確定ボタン-->
        <base-mo00609
          class="mx-2"
          :oneway-model-value="localOneway.mo00609Oneway"
          @click="onConfirmBtn"
        >
        </base-mo00609>
      </c-v-row>
    </template>
  </base-mo00024>
  <g-base-or21814 v-bind="or21814_1" />
  <g-base-or21814 v-bind="or21814_2" />
</template>

<style scoped lang="scss">
@use '@/styles/cmn/dialog-data-table.scss';
.pa-0 {
  padding: 0 !important;
}
</style>
