import { Or27626Const } from '../Or27626/Or27626.constants'
import { Or27626Logic } from '../Or27626/Or27626.logic'
import { Or27349Const } from '../Or27349/Or27349.constants'
import { Or27349Logic } from '../Or27349/Or27349.logic'
import { Or26323Const } from '../Or26323/Or26323.constants'
import { Or26323Logic } from '../Or26323/Or26323.logic'
import { Or26416Const } from '../Or26416/Or26416.constants'
import { Or26416Logic } from '../Or26416/Or26416.logic'
import { Or55476Logic } from '../Or55476/Or55476.logic'
import { Or55476Const } from '../Or55476/Or55476.constants'
import { Or11207Const } from './Or11207.constants'
import type { Or11207StateType } from './Or11207.type'
import { useInitialize, useOneWayBindAccessor } from '~/composables/useComponentLogic'
import { Or30980Logic } from '~/components/custom-components/organisms/Or30980/Or30980.logic'
import { Or30980Const } from '~/components/custom-components/organisms/Or30980/Or30980.constants'
import { Or10412Logic } from '~/components/custom-components/organisms/Or10412/Or10412.logic'
import { Or10412Const } from '~/components/custom-components/organisms/Or10412/Or10412.constants'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21815Const } from '~/components/base-components/organisms/Or21815/Or21815.constants'
import { Or21815Logic } from '~/components/base-components/organisms/Or21815/Or21815.logic'

/**
 * Or11207：有機体：概況調査
 * 処理ロジック
 *
 * @description
 * initialize処理とpinia領域操作用の処理を提供する
 *
 * <AUTHOR>
 */
export namespace Or11207Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: Or11207Const.CP_ID(0),
      uniqueCpId,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [
        { cpId: Or30980Const.CP_ID(1) },
        { cpId: Or10412Const.CP_ID(1) },
        { cpId: Or21813Const.CP_ID(1) },
        { cpId: Or21815Const.CP_ID(1) },
        { cpId: Or21815Const.CP_ID(1) },
        { cpId: Or27626Const.CP_ID(1) },
        { cpId: Or27349Const.CP_ID(1) },
        { cpId: Or26323Const.CP_ID(1) },
        { cpId: Or26416Const.CP_ID(1) },
        { cpId: Or55476Const.CP_ID(1) },
      ],
      initTwoWayValue: {
        ninteiYmd: {},
        ninteiShinseiYmd: {},
        oldNinteiYmd: {},
        renrakuNameKnj: {},
        addressKnj: { value: '' },
        memoKnj: { value: '' },
      },
    })

    // 子コンポーネントのセットアップ
    Or30980Logic.initialize(childCpIds[Or30980Const.CP_ID(1)].uniqueCpId)
    Or10412Logic.initialize(childCpIds[Or10412Const.CP_ID(1)].uniqueCpId)
    Or21813Logic.initialize(childCpIds[Or21813Const.CP_ID(1)].uniqueCpId)
    Or21815Logic.initialize(childCpIds[Or21815Const.CP_ID(1)].uniqueCpId)
    Or27626Logic.initialize(childCpIds[Or27626Const.CP_ID(1)].uniqueCpId)
    Or27349Logic.initialize(childCpIds[Or27349Const.CP_ID(1)].uniqueCpId)
    Or26323Logic.initialize(childCpIds[Or26323Const.CP_ID(1)].uniqueCpId)
    Or26416Logic.initialize(childCpIds[Or26416Const.CP_ID(1)].uniqueCpId)
    Or55476Logic.initialize(childCpIds[Or55476Const.CP_ID(1)].uniqueCpId)

    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }

  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<Or11207StateType>(Or11207Const.CP_ID(0))
}
