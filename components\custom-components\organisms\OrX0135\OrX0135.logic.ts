/**
 * OrX0135:有機体:承認欄登録画面モーダル
 * GUI00617_承認欄登録画面
 *
 * @description
 * 処理ロジック
 *
 *  <AUTHOR>
 */
import { Or52916Const } from '../Or52916/Or52916.constants'
import { Or52916Logic } from '../Or52916/Or52916.logic'
import { OrX0135Const } from './OrX0135.constants'
import type { OrX0135StateType } from './OrX0135.type'
import { useInitialize, useOneWayBindAccessor } from '#imports'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'

export namespace OrX0135Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: OrX0135Const.CP_ID(0),
      uniqueCpId,
      // 子コンポーネントに同一コンポーネントが複数含まれる場合は"[コンポーネントID]_[連番]"
      childCps: [{ cpId: Or21814Const.CP_ID(0) }, { cpId: Or52916Const.CP_ID(0) }],
      initTwoWayValue: {
        text1Knj: { value: '' },
        day1Knj: { value: '' },
        text2Knj: { value: '' },
        day2Knj: { value: '' },
        text3Knj: { value: '' },
        day3Knj: { value: '' },
        text4Knj: { value: '' },
        day4Knj: { value: '' },
        dispKbn: '',
        text1Font: '',
        text1Width: '',
        day1Width: '',
        text2Font: '',
        text2Width: '',
        day2Width: '',
        text3Font: '',
        text3Width: '',
        day3Width: '',
        text4Font: '',
        text4Width: '',
        day4Width: '',
        modifiedCnt: '',
        chohyoCd: '',
      },
    })

    // 子コンポーネントのセットアップ
    Or21814Logic.initialize(childCpIds.Or21814.uniqueCpId)
    Or52916Logic.initialize(childCpIds.Or52916.uniqueCpId)
    // 子コンポーネントのセットアップ
    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }

  /**
   * OneWayBind領域に関する処理
   *
   * @description
   * getterとsetterを提供する
   */
  export const state = useOneWayBindAccessor<OrX0135StateType>(OrX0135Const.CP_ID(0))
}
