import { getSequencedCpId } from '#imports'

/**
 * Or07212:静的データ
 * GUI04473_日割利用期間
 *
 * @description
 * 静的データ
 *
 * <AUTHOR> 董永強
 */
export namespace Or07212Const {
  /**
   * コンポーネントID
   *
   * @param seq - 連番（自コンポーネントから使用する場合は0を指定）
   */
  export const CP_ID = (seq: number) => getSequencedCpId('Or07212', seq)
  /**
   * 初期値
   */
  export namespace DEFAULT {
    /**
     * （初期値）開閉フラグ
     */
    export const IS_OPEN = false
  }
  /**
   * （更新区分）新規
   */
  export const UPDATE_CATEGORY_NEW = 'C'
  /**
   * （更新区分）更新
   */
  export const UPDATE_CATEGORY_UPDATE = 'U'
  /**
   * （更新区分）削除
   */
  export const UPDATE_CATEGORY_DELETE = 'D'
  /**
   * 事業所未選択
   */
  export const OFFICE_NO_SELECTED = '0'
  /**
   * 事業所未選択
   */
  export const EMIT_TYPE_CLOSE_BTN_CLICK = 'closeBtnClick'
}
