import { OrX0027Const } from '../OrX0027/OrX0027.constants'
import { OrX0026Const } from '../OrX0026/OrX0026.constants'
import { OrX0026Logic } from '../OrX0026/OrX0026.logic'
import { OrX0038Const } from '../OrX0038/OrX0038.constants'
import { OrX0038Logic } from '../OrX0038/OrX0038.logic'
import { OrX0077Const } from '../../template/OrX0077/OrX0077.constants'
import { OrX0077Logic } from '../../template/OrX0077/OrX0077.logic'
import { Or57737Logic } from '../Or57737/Or57737.logic'
import { Or57737Const } from '../Or57737/Or57737.constants'
import { Gui00070Const } from '../Gui00070/Gui00070.constants'
import { Gui00070Logic } from '../Gui00070/Gui00070.logic'
import { OrX0073Const } from '../OrX0073/OrX0073.constants'
import { OrX0073Logic } from '../OrX0073/OrX0073.logic'
import { OrX0007Const } from '../OrX0007/OrX0007.constants'
import { OrX0007Logic } from '../OrX0007/OrX0007.logic'
import { OrX0009Const } from '../OrX0009/OrX0009.constants'
import { OrX0009Logic } from '../OrX0009/OrX0009.logic'
import { OrX0042Const } from './OrX0042.constants'
import { Or11871Const } from '~/components/base-components/organisms/Or11871/Or11871.constants'
import { Or11871Logic } from '~/components/base-components/organisms/Or11871/Or11871.logic'
import { useInitialize } from '~/composables/useComponentLogic'
import { Or21813Const } from '~/components/base-components/organisms/Or21813/Or21813.constants'
import { Or21813Logic } from '~/components/base-components/organisms/Or21813/Or21813.logic'
import { Or21814Const } from '~/components/base-components/organisms/Or21814/Or21814.constants'
import { Or21814Logic } from '~/components/base-components/organisms/Or21814/Or21814.logic'
import { Or10583Const } from '~/components/custom-components/organisms/Or10583/Or10583.constants'
import { Or10583Logic } from '~/components/custom-components/organisms/Or10583/Or10583.logic'
import { Or26610Const } from '~/components/custom-components/organisms/Or26610/Or26610.constants'
import { Or26610Logic } from '~/components/custom-components/organisms/Or26610/Or26610.logic'
import { Or10878Const } from '~/components/custom-components/organisms/Or10878/Or10878.constants'
import { Or10878Logic } from '~/components/custom-components/organisms/Or10878/Or10878.logic'

/**
 * OrX0042Const:有機体:計画書（１）（画面コンポーネント）
 * 処理ロジック
 *
 * @description
 * initialize処理を提供する
 *
 * <AUTHOR>
 */
export namespace OrX0042Logic {
  /**
   * initialize
   *
   * @param uniqueCpId - ユニークコンポーネントID
   */
  export function initialize(uniqueCpId?: string) {
    // 自身のPinia領域をセットアップ
    const { cpId, uniqCpId, childCpIds } = useInitialize({
      cpId: OrX0042Const.CP_ID(0),
      uniqueCpId,
      childCps: [
        { cpId: Or11871Const.CP_ID },
        { cpId: OrX0026Const.CP_ID(0) },
        { cpId: OrX0038Const.CP_ID(0) },
        { cpId: OrX0077Const.CP_ID(0) },
        { cpId: Or57737Const.CP_ID(0) },
        { cpId: OrX0027Const.CP_ID(0) },
        { cpId: Or21813Const.CP_ID(0) },
        { cpId: Or21814Const.CP_ID(0) },
        { cpId: OrX0073Const.CP_ID(0) },
        { cpId: Gui00070Const.CP_ID(0) },
        { cpId: OrX0009Const.CP_ID(0) },
        { cpId: OrX0007Const.CP_ID(0) },
        { cpId: Or10583Const.CP_ID(0) },
        { cpId: Or26610Const.CP_ID(0) },
        { cpId: Or10878Const.CP_ID(0) },
      ],
    })

    // 子コンポーネントのセットアップ
    Or11871Logic.initialize(childCpIds.Or11871.uniqueCpId)
    OrX0026Logic.initialize(childCpIds.OrX0026.uniqueCpId)
    OrX0038Logic.initialize(childCpIds.OrX0038.uniqueCpId)
    OrX0077Logic.initialize(childCpIds.OrX0077.uniqueCpId)
    Or57737Logic.initialize(childCpIds.Or57737.uniqueCpId)
    Or21813Logic.initialize(childCpIds.Or21813.uniqueCpId)
    Or21814Logic.initialize(childCpIds.Or21814.uniqueCpId)
    OrX0073Logic.initialize(childCpIds.OrX0073.uniqueCpId)
    OrX0009Logic.initialize(childCpIds.OrX0009.uniqueCpId)
    Gui00070Logic.initialize(childCpIds.Gui00070.uniqueCpId)
    OrX0007Logic.initialize(childCpIds.OrX0007.uniqueCpId)
    Or10583Logic.initialize(childCpIds.Or10583.uniqueCpId)
    Or26610Logic.initialize(childCpIds.Or26610.uniqueCpId)
    Or10878Logic.initialize(childCpIds.Or10878.uniqueCpId)
    return {
      cpId,
      uniqueCpId: uniqCpId,
      childCpIds,
    }
  }
}
